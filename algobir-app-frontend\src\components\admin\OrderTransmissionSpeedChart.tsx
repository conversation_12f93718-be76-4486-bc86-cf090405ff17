import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  Button,
  ButtonGroup,
  Badge,
  Icon,

  Switch,
  FormControl,
  FormLabel,
  Select
} from '@chakra-ui/react';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ReferenceLine
} from 'recharts';
import {
  FiWifi,
  FiWifiOff,
  FiRefreshCw
} from 'react-icons/fi';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

import useOrderTransmissionMetrics from '../../hooks/useOrderTransmissionMetrics';

interface OrderTransmissionSpeedChartProps {
  timeRangeHours?: number;
  height?: number;
  showControls?: boolean;
  autoRefresh?: boolean;
}

const OrderTransmissionSpeedChart: React.FC<OrderTransmissionSpeedChartProps> = ({
  timeRangeHours = 24,
  height = 400,
  showControls = true,
  autoRefresh = true
}) => {
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar'>('line');
  const [showBreakdown, setShowBreakdown] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRangeHours);

  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');
  const gridColor = useColorModeValue('#f0f0f0', '#2d3748');

  // Fetch metrics data
  const {
    metrics,
    stats,
    loading,
    error,
    isConnected,
    lastUpdate,
    refetch
  } = useOrderTransmissionMetrics({
    timeRangeHours: selectedTimeRange,
    enabled: true,
    autoRefresh,
    refreshInterval: 30000
  });

  // Prepare chart data
  const chartData = useMemo(() => {
    if (!metrics.length) return [];

    return metrics
      .slice()
      .reverse() // Show oldest to newest
      .map((metric) => ({
        time: format(new Date(metric.created_at), 'HH:mm:ss', { locale: tr }),
        fullTime: metric.created_at,
        totalTime: metric.total_processing_time_ms,
        jsonParsing: metric.json_parsing_time_ms,
        transformation: metric.transformation_time_ms,
        webhookDelivery: metric.webhook_delivery_time_ms || 0,
        symbol: metric.symbol,
        signalSource: metric.signal_source,
        status: metric.processing_status
      }));
  }, [metrics]);

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          bg={cardBg}
          p={3}
          borderRadius="md"
          border="1px solid"
          borderColor={borderColor}
          boxShadow="lg"
        >
          <Text fontSize="sm" fontWeight="bold" mb={2}>
            {format(new Date(data.fullTime), 'dd MMM yyyy HH:mm:ss', { locale: tr })}
          </Text>
          <VStack spacing={1} align="start">
            <HStack>
              <Badge colorScheme={data.signalSource === 'solo-robot' ? 'blue' : 'purple'}>
                {data.signalSource === 'solo-robot' ? 'Solo-Robot' : 'Bro-Robot'}
              </Badge>
              <Badge colorScheme={data.status === 'success' ? 'green' : 'red'}>
                {data.status}
              </Badge>
            </HStack>
            <Text fontSize="xs">Sembol: {data.symbol}</Text>
            <Text fontSize="xs">Toplam: {data.totalTime.toFixed(2)}ms</Text>
            {showBreakdown && (
              <>
                <Text fontSize="xs">JSON Ayrıştırma: {data.jsonParsing.toFixed(2)}ms</Text>
                <Text fontSize="xs">Dönüştürme: {data.transformation.toFixed(2)}ms</Text>
                <Text fontSize="xs">Webhook İletimi: {data.webhookDelivery.toFixed(2)}ms</Text>
              </>
            )}
          </VStack>
        </Box>
      );
    }
    return null;
  };

  // Render chart based on type
  const renderChart = () => {
    const commonProps = {
      data: chartData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 }
    };

    switch (chartType) {
      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
            <XAxis dataKey="time" stroke={textColor} fontSize={12} />
            <YAxis stroke={textColor} fontSize={12} />
            <RechartsTooltip content={<CustomTooltip />} />
            <Legend />
            {showBreakdown ? (
              <>
                <Area
                  type="monotone"
                  dataKey="jsonParsing"
                  stackId="1"
                  stroke="#3182ce"
                  fill="#3182ce"
                  fillOpacity={0.6}
                  name="JSON Ayrıştırma"
                />
                <Area
                  type="monotone"
                  dataKey="transformation"
                  stackId="1"
                  stroke="#38a169"
                  fill="#38a169"
                  fillOpacity={0.6}
                  name="Dönüştürme"
                />
                <Area
                  type="monotone"
                  dataKey="webhookDelivery"
                  stackId="1"
                  stroke="#d69e2e"
                  fill="#d69e2e"
                  fillOpacity={0.6}
                  name="Webhook İletimi"
                />
              </>
            ) : (
              <Area
                type="monotone"
                dataKey="totalTime"
                stroke="#805ad5"
                fill="#805ad5"
                fillOpacity={0.6}
                name="Toplam İşlem Süresi"
              />
            )}
          </AreaChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
            <XAxis dataKey="time" stroke={textColor} fontSize={12} />
            <YAxis stroke={textColor} fontSize={12} />
            <RechartsTooltip content={<CustomTooltip />} />
            <Legend />
            {showBreakdown ? (
              <>
                <Bar dataKey="jsonParsing" stackId="a" fill="#3182ce" name="JSON Ayrıştırma" />
                <Bar dataKey="transformation" stackId="a" fill="#38a169" name="Dönüştürme" />
                <Bar dataKey="webhookDelivery" stackId="a" fill="#d69e2e" name="Webhook İletimi" />
              </>
            ) : (
              <Bar dataKey="totalTime" fill="#805ad5" name="Toplam İşlem Süresi" />
            )}
          </BarChart>
        );

      default: // line
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
            <XAxis dataKey="time" stroke={textColor} fontSize={12} />
            <YAxis stroke={textColor} fontSize={12} />
            <RechartsTooltip content={<CustomTooltip />} />
            <Legend />
            {showBreakdown ? (
              <>
                <Line
                  type="monotone"
                  dataKey="jsonParsing"
                  stroke="#3182ce"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  name="JSON Ayrıştırma"
                />
                <Line
                  type="monotone"
                  dataKey="transformation"
                  stroke="#38a169"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  name="Dönüştürme"
                />
                <Line
                  type="monotone"
                  dataKey="webhookDelivery"
                  stroke="#d69e2e"
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  name="Webhook İletimi"
                />
              </>
            ) : (
              <Line
                type="monotone"
                dataKey="totalTime"
                stroke="#805ad5"
                strokeWidth={3}
                dot={{ r: 4 }}
                name="Toplam İşlem Süresi"
              />
            )}
            {stats && (
              <ReferenceLine
                y={stats.avg_total_processing_time_ms}
                stroke="#e53e3e"
                strokeDasharray="5 5"
                label="Ortalama"
              />
            )}
          </LineChart>
        );
    }
  };

  return (
    <Card
      bg={cardBg}
      borderRadius="xl"
      boxShadow="lg"
      border="1px solid"
      borderColor={borderColor}
      overflow="hidden"
    >
      <CardHeader pb={2}>
        <HStack justify="space-between" align="center">
          <VStack align="start" spacing={1}>
            <Heading size="md" color={textColor}>
              Emir İletim Hızı Monitörü
            </Heading>
            <HStack spacing={2}>
              <Icon
                as={isConnected ? FiWifi : FiWifiOff}
                color={isConnected ? 'green.500' : 'red.500'}
              />
              <Text fontSize="sm" color={mutedColor}>
                {isConnected ? 'Canlı Bağlantı' : 'Bağlantı Kesildi'}
              </Text>
              {lastUpdate && (
                <Text fontSize="xs" color={mutedColor}>
                  Son Güncelleme: {format(lastUpdate, 'HH:mm:ss', { locale: tr })}
                </Text>
              )}
            </HStack>
          </VStack>

          {showControls && (
            <VStack spacing={2} align="end">
              <HStack>
                <Button
                  size="sm"
                  leftIcon={<FiRefreshCw />}
                  onClick={refetch}
                  isLoading={loading}
                  variant="outline"
                >
                  Yenile
                </Button>
              </HStack>
              <HStack>
                <Select
                  size="sm"
                  value={selectedTimeRange}
                  onChange={(e) => setSelectedTimeRange(Number(e.target.value))}
                  w="120px"
                >
                  <option value={1}>Son 1 Saat</option>
                  <option value={6}>Son 6 Saat</option>
                  <option value={24}>Son 24 Saat</option>
                  <option value={168}>Son 7 Gün</option>
                </Select>
              </HStack>
            </VStack>
          )}
        </HStack>
      </CardHeader>

      <CardBody pt={0}>
        {showControls && (
          <HStack justify="space-between" mb={4}>
            <ButtonGroup size="sm" isAttached variant="outline">
              <Button
                onClick={() => setChartType('line')}
                colorScheme={chartType === 'line' ? 'blue' : 'gray'}
              >
                Çizgi
              </Button>
              <Button
                onClick={() => setChartType('area')}
                colorScheme={chartType === 'area' ? 'blue' : 'gray'}
              >
                Alan
              </Button>
              <Button
                onClick={() => setChartType('bar')}
                colorScheme={chartType === 'bar' ? 'blue' : 'gray'}
              >
                Çubuk
              </Button>
            </ButtonGroup>

            <FormControl display="flex" alignItems="center" w="auto">
              <FormLabel htmlFor="breakdown-switch" mb="0" fontSize="sm">
                Detay Göster
              </FormLabel>
              <Switch
                id="breakdown-switch"
                isChecked={showBreakdown}
                onChange={(e) => setShowBreakdown(e.target.checked)}
                colorScheme="blue"
              />
            </FormControl>
          </HStack>
        )}

        {error ? (
          <Box textAlign="center" py={8}>
            <Text color="red.500" mb={2}>
              Veri yüklenirken hata oluştu
            </Text>
            <Text fontSize="sm" color={mutedColor}>
              {error}
            </Text>
          </Box>
        ) : (
          <Box h={`${height}px`}>
            <ResponsiveContainer width="100%" height="100%">
              {renderChart()}
            </ResponsiveContainer>
          </Box>
        )}
      </CardBody>
    </Card>
  );
};

export default OrderTransmissionSpeedChart;
