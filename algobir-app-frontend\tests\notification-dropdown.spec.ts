import { test, expect } from '@playwright/test';
import { 
  loginUser, 
  openNotificationDropdown,
  waitForPageLoad,
  expectNotificationCount
} from './utils/test-helpers';

test.describe('Navbar Bildirim Dropdown', () => {
  test.beforeEach(async ({ page }) => {
    await loginUser(page);
    await waitForPageLoad(page);
  });

  test('Bildirim butonu navbar\'da görünüyor', async ({ page }) => {
    // Bildirim butonunun görünür olduğunu kontrol et
    const notificationButton = page.locator('button[aria-label="Bildirimler"]');
    await expect(notificationButton).toBeVisible();
    
    // Butonun ikonu görünür olmalı
    await expect(notificationButton.locator('img, svg')).toBeVisible();
  });

  test('Bildirim sayısı badge\'i doğru görüntüleniyor', async ({ page }) => {
    const notificationButton = page.locator('button[aria-label="Bildirimler"]');
    await expect(notificationButton).toBeVisible();
    
    // Badge varsa sayıyı kontrol et
    const badge = notificationButton.locator('.chakra-badge');
    if (await badge.isVisible()) {
      const badgeText = await badge.textContent();
      expect(badgeText).toMatch(/^\d+(\+)?$/); // Sayı veya sayı+ formatında olmalı
    }
  });

  test('Bildirim dropdown açılıyor', async ({ page }) => {
    const notificationButton = page.locator('button[aria-label="Bildirimler"]');
    await expect(notificationButton).toBeVisible();
    
    // Butona tıkla
    await notificationButton.click();
    
    // Dropdown menüsünün açılmasını bekle
    await page.waitForTimeout(500);
    
    // Menu'nun görünür olduğunu kontrol et
    const menu = page.locator('[data-testid="notification-dropdown-menu"]');
    await expect(menu).toBeVisible();
  });

  test('Dropdown içeriği doğru görüntüleniyor', async ({ page }) => {
    await openNotificationDropdown(page);
    
    // Header'ın görünür olduğunu kontrol et
    const header = page.locator('[data-testid="notification-dropdown-header"]');
    await expect(header).toBeVisible();
    
    // Bildirim listesinin görünür olduğunu kontrol et
    const notificationList = page.locator('[data-testid="notification-dropdown-item"]');
    
    // En az bir bildirim varsa kontrol et
    if (await notificationList.count() > 0) {
      await expect(notificationList.first()).toBeVisible();
    } else {
      // Boş durum mesajı görünmeli
      const emptyMessage = page.locator('text=Henüz bildiriminiz yok, text=Bildirim bulunamadı');
      await expect(emptyMessage).toBeVisible();
    }
  });

  test('Tümünü okundu işaretle butonu çalışıyor', async ({ page }) => {
    await openNotificationDropdown(page);
    
    // "Tümünü Okundu İşaretle" butonunu bul
    const markAllReadButton = page.locator('[data-testid="mark-all-read-button"]');
    
    if (await markAllReadButton.isVisible()) {
      await markAllReadButton.click();
      
      // Butonun kaybolmasını veya değişmesini bekle
      await page.waitForTimeout(1000);
      
      // Badge'in kaybolmasını kontrol et
      const badge = page.locator('button[aria-label="Bildirimler"] .chakra-badge');
      await expect(badge).not.toBeVisible();
    }
  });

  test('Dropdown\'dan bildirim sayfasına yönlendirme', async ({ page }) => {
    await openNotificationDropdown(page);
    
    // "Tümünü Görüntüle" veya benzer link'i bul
    const viewAllLink = page.locator('[data-testid="view-all-notifications-link"]');
    
    if (await viewAllLink.isVisible()) {
      await viewAllLink.click();
      
      // Bildirimler sayfasına yönlendirildiğini kontrol et
      await expect(page).toHaveURL('/notifications');
      await expect(page.locator('h1')).toContainText('Bildirimler');
    }
  });

  test('Dropdown dışına tıklayınca kapanıyor', async ({ page }) => {
    await openNotificationDropdown(page);
    
    // Menu'nun açık olduğunu kontrol et
    const menu = page.locator('[data-testid="notification-dropdown-menu"]');
    await expect(menu).toBeVisible();
    
    // Dropdown dışına tıkla
    await page.click('body', { position: { x: 100, y: 100 } });
    
    // Menu'nun kapandığını kontrol et
    await expect(menu).not.toBeVisible();
  });

  test('Bildirim öğelerine tıklama çalışıyor', async ({ page }) => {
    await openNotificationDropdown(page);
    
    // İlk bildirim öğesini bul
    const firstNotification = page.locator('[data-testid="notification-dropdown-item"]').first();
    
    if (await firstNotification.isVisible()) {
      // Bildirime tıkla
      await firstNotification.click();
      
      // Dropdown'ın kapandığını kontrol et
      const menu = page.locator('[data-testid="notification-dropdown-menu"]');
      await expect(menu).not.toBeVisible();
      
      // Uygun sayfaya yönlendirildiğini kontrol et (eğer link varsa)
      await waitForPageLoad(page);
    }
  });

  test('Bildirim dropdown responsive tasarım', async ({ page }) => {
    // Mobil viewport'a geç
    await page.setViewportSize({ width: 375, height: 667 });
    
    const notificationButton = page.locator('button[aria-label="Bildirimler"]');
    await expect(notificationButton).toBeVisible();
    
    // Butona tıkla
    await notificationButton.click();
    
    // Dropdown'ın mobilde de çalıştığını kontrol et
    const menu = page.locator('[data-testid="notification-dropdown-menu"]');
    await expect(menu).toBeVisible();
    
    // Dropdown genişliğinin ekrana uygun olduğunu kontrol et
    const menuBox = await menu.boundingBox();
    if (menuBox) {
      expect(menuBox.width).toBeLessThanOrEqual(375);
    }
  });

  test.skip('Bildirim dropdown keyboard navigasyonu', async ({ page }) => {
    // Bildirim butonunu focus'la
    const notificationButton = page.locator('button[aria-label="Bildirimler"]');
    await notificationButton.focus();

    // Enter ile dropdown'ı aç
    await page.keyboard.press('Enter');
    
    // Menu'nun açıldığını kontrol et
    const menu = page.locator('[data-testid="notification-dropdown-menu"]');
    await expect(menu).toBeVisible();

    // Escape ile kapat
    await page.keyboard.press('Escape');

    // Menu'nun kapandığını kontrol et
    await expect(menu).not.toBeVisible();
  });

  test.skip('Bildirim dropdown loading durumu', async ({ page }) => {
    // Network'ü yavaşlat - route'u sayfaya gitmeden önce ayarla
    await page.route('**/rpc/get_user_notifications', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 saniye gecikme
      await route.continue();
    });

    // Sayfayı yeniden yükle
    await page.reload();
    await waitForPageLoad(page);

    const notificationButton = page.locator('button[aria-label="Bildirimler"]');
    await notificationButton.click();

    // Loading indicator'ının görünür olduğunu kontrol et - daha kısa timeout
    const loadingIndicator = page.locator('[data-testid="notification-loading"]');
    await expect(loadingIndicator).toBeVisible({ timeout: 1000 });

    // Loading'in bitmesini bekle
    await expect(loadingIndicator).not.toBeVisible({ timeout: 10000 });
  });
});
