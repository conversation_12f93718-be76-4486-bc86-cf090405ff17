import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi } from 'vitest';
import { SidebarProvider, useSidebar } from '../../../context/SidebarContext';

// Test component to access sidebar context
const TestComponent = () => {
  const {
    sidebarState,
    isCollapsed,
    isExpanded,
    hasActiveSubmenu,
    expandSidebar,
    collapseSidebar,
    toggleSidebarState,
    openSubmenu,
    closeSubmenu,
    activeSubmenu,
    submenuItems,
    currentSidebarWidth,
    isMouseNearLeftEdge,
    mouseX
  } = useSidebar();

  return (
    <div>
      <div data-testid="sidebar-state">{sidebarState}</div>
      <div data-testid="is-collapsed">{isCollapsed.toString()}</div>
      <div data-testid="is-expanded">{isExpanded.toString()}</div>
      <div data-testid="has-active-submenu">{hasActiveSubmenu.toString()}</div>
      <div data-testid="current-width">{currentSidebarWidth}</div>
      <div data-testid="active-submenu">{activeSubmenu || 'none'}</div>
      <div data-testid="submenu-items-count">{submenuItems.length}</div>
      <div data-testid="mouse-near-edge">{isMouseNearLeftEdge.toString()}</div>
      <div data-testid="mouse-x">{mouseX}</div>
      
      <button data-testid="expand-btn" onClick={expandSidebar}>Expand</button>
      <button data-testid="collapse-btn" onClick={collapseSidebar}>Collapse</button>
      <button data-testid="toggle-btn" onClick={toggleSidebarState}>Toggle</button>
      <button 
        data-testid="open-submenu-btn" 
        onClick={() => openSubmenu('/test', [
          { id: 'test1', name: 'Test 1', path: '/test1' },
          { id: 'test2', name: 'Test 2', path: '/test2' }
        ])}
      >
        Open Submenu
      </button>
      <button data-testid="close-submenu-btn" onClick={closeSubmenu}>Close Submenu</button>
    </div>
  );
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ChakraProvider>
      <BrowserRouter>
        <SidebarProvider>
          {component}
        </SidebarProvider>
      </BrowserRouter>
    </ChakraProvider>
  );
};

describe('SidebarContext', () => {
  beforeEach(() => {
    // Mock window.innerHeight for mouse edge detection
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 1000,
    });
  });

  afterEach(() => {
    // Clean up event listeners
    document.removeEventListener('mousemove', vi.fn());
    document.removeEventListener('keydown', vi.fn());
  });

  test('should initialize with default expanded state', () => {
    renderWithProviders(<TestComponent />);
    
    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('expanded');
    expect(screen.getByTestId('is-collapsed')).toHaveTextContent('false');
    expect(screen.getByTestId('is-expanded')).toHaveTextContent('true');
    expect(screen.getByTestId('has-active-submenu')).toHaveTextContent('false');
    expect(screen.getByTestId('current-width')).toHaveTextContent('285px');
  });

  test('should collapse sidebar when collapse button is clicked', () => {
    renderWithProviders(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('collapse-btn'));
    
    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('collapsed');
    expect(screen.getByTestId('is-collapsed')).toHaveTextContent('true');
    expect(screen.getByTestId('is-expanded')).toHaveTextContent('false');
    expect(screen.getByTestId('current-width')).toHaveTextContent('70px');
  });

  test('should expand sidebar when expand button is clicked', () => {
    renderWithProviders(<TestComponent />);
    
    // First collapse
    fireEvent.click(screen.getByTestId('collapse-btn'));
    expect(screen.getByTestId('is-collapsed')).toHaveTextContent('true');
    
    // Then expand
    fireEvent.click(screen.getByTestId('expand-btn'));
    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('expanded');
    expect(screen.getByTestId('is-expanded')).toHaveTextContent('true');
  });

  test('should toggle sidebar state', () => {
    renderWithProviders(<TestComponent />);
    
    // Initially expanded
    expect(screen.getByTestId('is-expanded')).toHaveTextContent('true');
    
    // Toggle to collapsed
    fireEvent.click(screen.getByTestId('toggle-btn'));
    expect(screen.getByTestId('is-collapsed')).toHaveTextContent('true');
    
    // Toggle back to expanded
    fireEvent.click(screen.getByTestId('toggle-btn'));
    expect(screen.getByTestId('is-expanded')).toHaveTextContent('true');
  });

  test('should open submenu and change state to collapsed-with-submenu', () => {
    renderWithProviders(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('open-submenu-btn'));
    
    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('collapsed-with-submenu');
    expect(screen.getByTestId('has-active-submenu')).toHaveTextContent('true');
    expect(screen.getByTestId('active-submenu')).toHaveTextContent('/test');
    expect(screen.getByTestId('submenu-items-count')).toHaveTextContent('2');
  });

  test('should close submenu and return to collapsed state', () => {
    renderWithProviders(<TestComponent />);
    
    // Open submenu first
    fireEvent.click(screen.getByTestId('open-submenu-btn'));
    expect(screen.getByTestId('has-active-submenu')).toHaveTextContent('true');
    
    // Close submenu
    fireEvent.click(screen.getByTestId('close-submenu-btn'));
    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('collapsed');
    expect(screen.getByTestId('has-active-submenu')).toHaveTextContent('false');
    expect(screen.getByTestId('active-submenu')).toHaveTextContent('none');
  });

  test('should handle keyboard shortcuts', async () => {
    renderWithProviders(<TestComponent />);
    
    // Test Ctrl+B to toggle sidebar
    fireEvent.keyDown(document, { key: 'b', ctrlKey: true });
    await waitFor(() => {
      expect(screen.getByTestId('is-collapsed')).toHaveTextContent('true');
    });
    
    // Test Ctrl+Shift+E to expand
    fireEvent.keyDown(document, { key: 'E', ctrlKey: true, shiftKey: true });
    await waitFor(() => {
      expect(screen.getByTestId('is-expanded')).toHaveTextContent('true');
    });
    
    // Test Ctrl+Shift+C to collapse
    fireEvent.keyDown(document, { key: 'C', ctrlKey: true, shiftKey: true });
    await waitFor(() => {
      expect(screen.getByTestId('is-collapsed')).toHaveTextContent('true');
    });
  });

  test('should handle Escape key to close submenu', async () => {
    renderWithProviders(<TestComponent />);
    
    // Open submenu first
    fireEvent.click(screen.getByTestId('open-submenu-btn'));
    expect(screen.getByTestId('has-active-submenu')).toHaveTextContent('true');
    
    // Press Escape
    fireEvent.keyDown(document, { key: 'Escape' });
    await waitFor(() => {
      expect(screen.getByTestId('has-active-submenu')).toHaveTextContent('false');
    });
  });

  test('should track mouse position for edge detection', async () => {
    renderWithProviders(<TestComponent />);
    
    // Simulate mouse move near left edge
    fireEvent(document, new MouseEvent('mousemove', {
      clientX: 30,
      clientY: 500,
      bubbles: true
    }));
    
    await waitFor(() => {
      expect(screen.getByTestId('mouse-x')).toHaveTextContent('30');
      expect(screen.getByTestId('mouse-near-edge')).toHaveTextContent('true');
    });
    
    // Simulate mouse move away from edge
    fireEvent(document, new MouseEvent('mousemove', {
      clientX: 200,
      clientY: 500,
      bubbles: true
    }));
    
    await waitFor(() => {
      expect(screen.getByTestId('mouse-x')).toHaveTextContent('200');
      expect(screen.getByTestId('mouse-near-edge')).toHaveTextContent('false');
    });
  });
});
