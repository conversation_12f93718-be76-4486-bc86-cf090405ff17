import React, { useEffect, useRef } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useColorModeValue,
  Box,
  HStack,
  Icon,
  Text,
  VStack,
  Button
} from '@chakra-ui/react';
import { FiSearch, FiTrendingUp, FiHome } from 'react-icons/fi';
import { RiRobotFill } from 'react-icons/ri';
import { useNavigate } from 'react-router-dom';
import SearchBox from './SearchBox';

interface MobileSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileSearchModal: React.FC<MobileSearchModalProps> = ({
  isOpen,
  onClose
}) => {
  const initialRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  // Color mode values
  const bg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  // Quick navigation handler
  const handleQuickNavigation = (path: string) => {
    navigate(path);
    onClose();
  };

  // Auto-focus search input when modal opens
  useEffect(() => {
    if (isOpen) {
      // Small delay to ensure modal is fully rendered
      setTimeout(() => {
        const searchInput = document.querySelector('input[placeholder*="Ara"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    }
  }, [isOpen]);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      initialFocusRef={initialRef}
      size="full"
      motionPreset="slideInTop"
    >
      <ModalOverlay 
        bg="blackAlpha.600"
        backdropFilter="blur(4px)"
      />
      <ModalContent
        bg={bg}
        borderRadius="0"
        m="0"
        maxH="100vh"
        overflow="hidden"
      >
        <ModalHeader
          pb="4"
          pt="6"
          px="6"
          borderBottom="1px solid"
          borderColor={borderColor}
        >
          <HStack spacing="3" align="center">
            <Icon as={FiSearch} color={secondaryTextColor} boxSize="5" />
            <Text fontSize="lg" fontWeight="semibold" color={textColor}>
              Arama
            </Text>
          </HStack>
          <ModalCloseButton
            top="6"
            right="6"
            color={secondaryTextColor}
            _hover={{ color: textColor }}
          />
        </ModalHeader>

        <ModalBody p="0" overflow="hidden">
          <VStack spacing="0" align="stretch" h="full">
            {/* Search Box Container */}
            <Box p="6" borderBottom="1px solid" borderColor={borderColor}>
              <SearchBox
                size="lg"
                placeholder="Sayfalar, robotlar, kullanıcılar ara..."
                isMobileModal={true}
                onClose={onClose}
              />
            </Box>

            {/* Additional mobile-specific content can go here */}
            <Box flex="1" p="6">
              <VStack spacing="4" align="stretch">
                <Text fontSize="sm" color={secondaryTextColor} textAlign="center">
                  Arama yapmaya başlayın veya aşağıdaki kısayolları kullanın
                </Text>
                
                {/* Quick access shortcuts for mobile */}
                <VStack spacing="2" align="stretch">
                  <Text fontSize="xs" fontWeight="bold" color={secondaryTextColor} textTransform="uppercase">
                    Hızlı Erişim
                  </Text>

                  <Button
                    variant="ghost"
                    justifyContent="space-between"
                    h="auto"
                    py="3"
                    px="3"
                    borderRadius="lg"
                    bg={hoverBg}
                    _hover={{ bg: hoverBg, opacity: 0.8 }}
                    onClick={() => handleQuickNavigation('/')}
                  >
                    <HStack spacing="3">
                      <Icon as={FiHome} color={secondaryTextColor} />
                      <Text fontSize="sm" color={textColor}>Dashboard</Text>
                    </HStack>
                    <Text fontSize="xs" color={secondaryTextColor}>Ana sayfa</Text>
                  </Button>

                  <Button
                    variant="ghost"
                    justifyContent="space-between"
                    h="auto"
                    py="3"
                    px="3"
                    borderRadius="lg"
                    bg={hoverBg}
                    _hover={{ bg: hoverBg, opacity: 0.8 }}
                    onClick={() => handleQuickNavigation('/trades')}
                  >
                    <HStack spacing="3">
                      <Icon as={FiTrendingUp} color={secondaryTextColor} />
                      <Text fontSize="sm" color={textColor}>İşlemlerim</Text>
                    </HStack>
                    <Text fontSize="xs" color={secondaryTextColor}>Alım satım</Text>
                  </Button>

                  <Button
                    variant="ghost"
                    justifyContent="space-between"
                    h="auto"
                    py="3"
                    px="3"
                    borderRadius="lg"
                    bg={hoverBg}
                    _hover={{ bg: hoverBg, opacity: 0.8 }}
                    onClick={() => handleQuickNavigation('/marketplace')}
                  >
                    <HStack spacing="3">
                      <Icon as={RiRobotFill} color={secondaryTextColor} />
                      <Text fontSize="sm" color={textColor}>Robot Pazarı</Text>
                    </HStack>
                    <Text fontSize="xs" color={secondaryTextColor}>Robotlar</Text>
                  </Button>

                  <Button
                    variant="ghost"
                    justifyContent="space-between"
                    h="auto"
                    py="3"
                    px="3"
                    borderRadius="lg"
                    bg={hoverBg}
                    _hover={{ bg: hoverBg, opacity: 0.8 }}
                    onClick={() => handleQuickNavigation('/statistics')}
                  >
                    <HStack spacing="3">
                      <Icon as={FiTrendingUp} color={secondaryTextColor} />
                      <Text fontSize="sm" color={textColor}>İstatistikler</Text>
                    </HStack>
                    <Text fontSize="xs" color={secondaryTextColor}>Performans</Text>
                  </Button>
                </VStack>
              </VStack>
            </Box>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default MobileSearchModal;
