import React, { useState, useEffect } from 'react';
import { useParams, Link as RouterLink } from 'react-router-dom';
import {
  Box,
  VStack,
  Heading,
  Text,
  Avatar,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertDescription,
  Flex,
  SimpleGrid,
  Badge,
  Button,
  Spinner,
  Center,
  useBreakpointValue
} from '@chakra-ui/react';
import { FiCalendar, FiUser } from 'react-icons/fi';
import { RiRobotFill } from 'react-icons/ri';
import Card from '../../components/card/Card';
import { supabase } from '../../supabaseClient';

interface Robot {
  id: string;
  name: string;
  description: string;
  image_url?: string;
  strategy_type?: string;
  price: number;
  subscription_period: number;
  status: string;
  created_at: string;
}

interface PublicProfile {
  profile_id: string;
  username: string;
  avatar_url?: string;
  created_at: string;
  full_name?: string;
  bio?: string;
  url_slug: string;
  public_robots: Robot[];
}

const UserPage: React.FC = () => {
  const { username: urlSlug } = useParams<{ username: string }>();
  const [profile, setProfile] = useState<PublicProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Responsive değerler
  const cardPadding = useBreakpointValue({ base: '16px', sm: '20px', md: '24px' });
  const buttonSize = useBreakpointValue({ base: 'sm', md: 'md' });
  const fontSize = useBreakpointValue({ base: 'sm', md: 'md' });

  // Horizon UI renk tema değerleri
  const textColor = useColorModeValue('navy.700', 'white');
  const textColorSecondary = useColorModeValue('secondaryGray.600', 'white');
  const brandColor = useColorModeValue('brand.500', 'white');
  const cardShadow = useColorModeValue("0px 18px 40px rgba(112, 144, 176, 0.12)", "none");
  const cardBg = useColorModeValue('white', 'navy.800');
  const robotCardBg = useColorModeValue('secondaryGray.50', 'navy.700');

  useEffect(() => {
    const fetchPublicProfile = async () => {
      if (!urlSlug) {
        setError('Kullanıcı adı belirtilmemiş');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const { data, error } = await supabase.rpc('get_public_profile_by_slug', {
          p_url_slug: urlSlug
        });

        if (error) throw error;

        if (!data || data.length === 0) {
          setError('Kullanıcı bulunamadı');
          return;
        }

        setProfile(data[0]);
      } catch (error: any) {
        console.error('Public profil çekme hatası:', error);
        setError('Profil yüklenirken bir hata oluştu');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPublicProfile();
  }, [urlSlug]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <Box
        px={{ base: '16px', md: '24px', lg: '30px' }}
        pb={{ base: '20px', md: '30px' }}
      >
        <Center minH="400px">
          <VStack spacing={4}>
            <Spinner 
              size="xl" 
              color={brandColor}
              thickness="4px"
            />
            <Text fontSize={fontSize} color={textColorSecondary}>
              Profil yükleniyor...
            </Text>
          </VStack>
        </Center>
      </Box>
    );
  }

  if (error || !profile) {
    return (
      <Box
        px={{ base: '16px', md: '24px', lg: '30px' }}
        pb={{ base: '20px', md: '30px' }}
      >
        <VStack spacing={{ base: 6, md: 8 }} align="stretch" maxW="800px" mx="auto">
          <Card
            p={cardPadding}
            borderRadius="20px"
            boxShadow={cardShadow}
            bg={cardBg}
          >
            <Alert 
              status="error" 
              borderRadius="16px" 
              variant="subtle"
              flexDirection="column"
              alignItems="center"
              textAlign="center"
              py={8}
            >
              <AlertIcon boxSize="40px" mb={4} />
              <AlertDescription fontSize={{ base: 'md', md: 'lg' }} fontWeight="500">
                {error || 'Kullanıcı bulunamadı'}
              </AlertDescription>
              <Button
                as={RouterLink}
                to="/marketplace"
                colorScheme="brand"
                variant="outline"
                mt={4}
                size={buttonSize}
              >
                Pazar Yerine Dön
              </Button>
            </Alert>
          </Card>
        </VStack>
      </Box>
    );
  }

  return (
    <Box
      px={{ base: '16px', md: '24px', lg: '30px' }}
      pb={{ base: '20px', md: '30px' }}
    >
      <VStack spacing={{ base: 6, md: 8 }} align="stretch" maxW="1000px" mx="auto">
        {/* Başlık */}
        <Heading 
          as="h1" 
          size={{ base: 'lg', md: 'xl' }}
          color={textColor} 
          textAlign={{ base: 'center', md: 'left' }}
          fontSize={{ base: '24px', md: '32px' }}
          fontWeight="700"
        >
          Kullanıcı Profili
        </Heading>

        {/* Profil Kartı */}
        <Card
          p={cardPadding}
          borderRadius="20px"
          boxShadow={cardShadow}
          bg={cardBg}
          transition="all 0.3s"
        >
          <Flex
            direction={{ base: 'column', sm: 'row' }}
            align={{ base: 'center', sm: 'flex-start' }}
            gap={{ base: 4, sm: 6 }}
          >
            <Avatar
              size={{ base: 'xl', md: '2xl' }}
              src={profile.avatar_url}
              name={profile.full_name || profile.username}
              bg={brandColor}
              color="white"
              showBorder
              borderColor={brandColor}
              borderWidth="3px"
              shadow="lg"
            />

            <VStack 
              align={{ base: 'center', sm: 'flex-start' }} 
              spacing={{ base: 2, md: 3 }}
              flex="1"
              textAlign={{ base: 'center', sm: 'left' }}
            >
              <Heading 
                size={{ base: 'md', md: 'lg' }} 
                color={textColor}
                fontSize={{ base: '20px', md: '24px' }}
              >
                {profile.full_name || profile.username}
              </Heading>
              
              <Text 
                color={textColorSecondary} 
                fontSize={{ base: 'sm', md: 'md' }}
                fontWeight="500"
              >
                @{profile.username}
              </Text>

              <Flex 
                align="center" 
                gap={2}
                color={textColorSecondary}
                fontSize={{ base: 'xs', md: 'sm' }}
              >
                <FiCalendar />
                <Text>
                  {formatDate(profile.created_at)} tarihinde katıldı
                </Text>
              </Flex>

              {profile.bio && (
                <Text 
                  color={textColor} 
                  fontSize={{ base: 'sm', md: 'md' }}
                  mt={2}
                  maxW="500px"
                >
                  {profile.bio}
                </Text>
              )}
            </VStack>
          </Flex>
        </Card>

        {/* Robotlar Bölümü */}
        {profile.public_robots && profile.public_robots.length > 0 && (
          <Card
            p={cardPadding}
            borderRadius="20px"
            boxShadow={cardShadow}
            bg={cardBg}
          >
            <VStack spacing={6} align="stretch">
              <Flex align="center" gap={3}>
                <RiRobotFill size="24px" color={brandColor} />
                <Heading 
                  size="md" 
                  color={textColor}
                  fontSize={{ base: '18px', md: '20px' }}
                >
                  Oluşturulan Robotlar ({profile.public_robots.length})
                </Heading>
              </Flex>

              <SimpleGrid 
                columns={{ base: 1, md: 2, lg: 3 }} 
                spacing={{ base: 4, md: 6 }}
              >
                {profile.public_robots.map((robot) => (
                  <Card
                    key={robot.id}
                    p={{ base: '16px', md: '20px' }}
                    borderRadius="16px"
                    bg={robotCardBg}
                    transition="all 0.3s"
                    _hover={{ 
                      transform: 'translateY(-4px)',
                      boxShadow: '0 10px 30px rgba(112, 144, 176, 0.2)'
                    }}
                  >
                    <VStack spacing={3} align="stretch">
                      <Flex justify="space-between" align="flex-start">
                        <Box flex="1">
                          <Heading 
                            size="sm" 
                            color={textColor}
                            noOfLines={1}
                            mb={1}
                          >
                            {robot.name}
                          </Heading>
                          <Badge 
                            colorScheme={robot.status === 'active' ? 'green' : 'gray'}
                            fontSize="xs"
                          >
                            {robot.status === 'active' ? 'Aktif' : 'Pasif'}
                          </Badge>
                        </Box>
                      </Flex>

                      {robot.description && (
                        <Text 
                          fontSize="sm" 
                          color={textColorSecondary}
                          noOfLines={2}
                        >
                          {robot.description}
                        </Text>
                      )}

                      {robot.strategy_type && (
                        <Badge 
                          colorScheme="blue" 
                          variant="subtle"
                          fontSize="xs"
                          width="fit-content"
                        >
                          {robot.strategy_type}
                        </Badge>
                      )}

                      <Flex justify="space-between" align="center" pt={2}>
                        <Text 
                          fontSize="sm" 
                          fontWeight="600"
                          color={brandColor}
                        >
                          ₺{robot.price}/ay
                        </Text>
                        <Button
                          as={RouterLink}
                          to={`/marketplace/robot/${robot.id}`}
                          size="sm"
                          colorScheme="brand"
                          variant="outline"
                        >
                          Detaylar
                        </Button>
                      </Flex>
                    </VStack>
                  </Card>
                ))}
              </SimpleGrid>
            </VStack>
          </Card>
        )}

        {/* Robot yoksa bilgi kartı */}
        {(!profile.public_robots || profile.public_robots.length === 0) && (
          <Card
            p={cardPadding}
            borderRadius="20px"
            boxShadow={cardShadow}
            bg={cardBg}
          >
            <Alert 
              status="info" 
              borderRadius="16px" 
              variant="subtle"
              flexDirection="column"
              alignItems="center"
              textAlign="center"
              py={6}
            >
              <FiUser size="32px" style={{ marginBottom: '16px' }} />
              <AlertDescription fontSize={{ base: 'sm', md: 'md' }}>
                Bu kullanıcı henüz herkese açık robot oluşturmamış.
              </AlertDescription>
            </Alert>
          </Card>
        )}

        {/* Geri dön butonu */}
        <Flex justify="center">
          <Button
            as={RouterLink}
            to="/marketplace"
            colorScheme="brand"
            variant="outline"
            size={buttonSize}
            leftIcon={<FiUser />}
          >
            Pazar Yerine Dön
          </Button>
        </Flex>
      </VStack>
    </Box>
  );
};

export default UserPage; 