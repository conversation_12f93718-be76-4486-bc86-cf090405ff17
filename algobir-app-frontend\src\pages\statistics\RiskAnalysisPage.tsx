import React, { useMemo } from 'react';
import {
  VStack,
  Text,
  Box,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Badge,
  Progress,
  HStack,
  Icon,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertDescription,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel
} from '@chakra-ui/react';
import {
  FiTrendingDown,
  FiTrendingUp,
  FiShield,
  FiAlertTriangle,
  FiBarChart,
  FiTarget
} from 'react-icons/fi';
import { useEnhancedStatistics } from '../../hooks/useEnhancedStatistics';
import AdvancedLineChart from '../../components/statistics/charts/AdvancedLineChart';
import InteractivePieChart from '../../components/statistics/charts/InteractivePieChart';

const RiskAnalysisPage: React.FC = () => {
  const { stats, loading, error } = useEnhancedStatistics();

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');

  // Calculate risk metrics and prepare data - moved before early returns
  const riskMetrics = useMemo(() => {
    // Handle case when stats is null/undefined
    if (!stats) {
      return {
        volatility: 0,
        maxDrawdown: 0,
        sharpeRatio: 0,
        valueAtRisk95: 0,
        conditionalVaR: 0,
        riskScore: 0,
        riskLevel: 'Düşük' as const,
        riskColor: 'green'
      };
    }
    const volatility = stats.volatility || 0;
    const maxDrawdown = stats.maxDrawdown || 0;
    const sharpeRatio = stats.sharpeRatio || 0;
    const valueAtRisk95 = stats.valueAtRisk95 || 0;
    const conditionalVaR = stats.conditionalVaR || 0;

    // Calculate risk score (0-100, where 0 is lowest risk)
    const riskScore = Math.min(100, Math.max(0,
      (volatility * 2) +
      (maxDrawdown * 1.5) +
      (sharpeRatio < 0 ? 30 : Math.max(0, 20 - sharpeRatio * 10)) +
      (valueAtRisk95 * 0.5)
    ));

    // Risk level classification
    let riskLevel: 'Düşük' | 'Orta' | 'Yüksek' | 'Çok Yüksek';
    let riskColor: string;

    if (riskScore < 25) {
      riskLevel = 'Düşük';
      riskColor = 'green';
    } else if (riskScore < 50) {
      riskLevel = 'Orta';
      riskColor = 'yellow';
    } else if (riskScore < 75) {
      riskLevel = 'Yüksek';
      riskColor = 'orange';
    } else {
      riskLevel = 'Çok Yüksek';
      riskColor = 'red';
    }

    return {
      volatility,
      maxDrawdown,
      sharpeRatio,
      valueAtRisk95,
      conditionalVaR,
      riskScore,
      riskLevel,
      riskColor
    };
  }, [stats]);

  // Prepare risk distribution data for pie chart
  const riskDistributionData = useMemo(() => {
    if (!stats || !stats.robotComparison) {
      return [];
    }

    const robotComparison = stats.robotComparison;
    const riskLevels = { 'Düşük': 0, 'Orta': 0, 'Yüksek': 0, 'Çok Yüksek': 0 };

    robotComparison.forEach(robot => {
      const robotRisk = robot.metrics.maxDrawdown;
      if (robotRisk < 10) riskLevels['Düşük']++;
      else if (robotRisk < 20) riskLevels['Orta']++;
      else if (robotRisk < 30) riskLevels['Yüksek']++;
      else riskLevels['Çok Yüksek']++;
    });

    return Object.entries(riskLevels).map(([level, count]) => ({
      name: level,
      value: count
    })).filter(item => item.value > 0);
  }, [stats]);

  // Prepare drawdown data for line chart
  const drawdownData = useMemo(() => {
    if (!stats || !stats.cumulativePnlData || stats.cumulativePnlData.length === 0) {
      return [];
    }

    let peak = 0;
    return stats.cumulativePnlData.map(item => {
      const value = item.value;
      if (value > peak) peak = value;
      const drawdown = peak > 0 ? ((peak - value) / peak) * 100 : 0;

      return {
        date: item.date,
        value: -drawdown // Negative for drawdown visualization
      };
    });
  }, [stats]);


  const formatPercent = (value: number) => `${value.toFixed(2)}%`;

  // Early returns after all hooks
  if (loading) {
    return (
      <Box p={8} textAlign="center">
        <Text>Yükleniyor...</Text>
      </Box>
    );
  }

  if (error || !stats) {
    return (
      <Box p={8} textAlign="center">
        <Text color="red.500">Veri yüklenirken hata oluştu</Text>
      </Box>
    );
  }

  return (
    <VStack spacing={6} align="stretch">
      <Heading size="lg" color={textColor}>Risk Analizi</Heading>

      {/* Risk Overview Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Stat>
              <StatLabel>Genel Risk Skoru</StatLabel>
              <StatNumber>{riskMetrics.riskScore.toFixed(0)}/100</StatNumber>
              <StatHelpText>
                <Badge colorScheme={riskMetrics.riskColor} variant="solid">
                  {riskMetrics.riskLevel} Risk
                </Badge>
              </StatHelpText>
              <Progress
                value={riskMetrics.riskScore}
                colorScheme={riskMetrics.riskColor}
                size="sm"
                mt={2}
              />
            </Stat>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Stat>
              <HStack>
                <Icon as={FiTrendingDown} color="red.500" />
                <StatLabel>Maksimum Düşüş</StatLabel>
              </HStack>
              <StatNumber>{formatPercent(riskMetrics.maxDrawdown)}</StatNumber>
              <StatHelpText>
                <StatArrow type={riskMetrics.maxDrawdown > 20 ? 'increase' : 'decrease'} />
                {riskMetrics.maxDrawdown > 20 ? 'Yüksek Risk' : 'Kabul Edilebilir'}
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Stat>
              <HStack>
                <Icon as={FiBarChart} color="blue.500" />
                <StatLabel>Volatilite</StatLabel>
              </HStack>
              <StatNumber>{formatPercent(riskMetrics.volatility)}</StatNumber>
              <StatHelpText>
                <StatArrow type={riskMetrics.volatility > 15 ? 'increase' : 'decrease'} />
                Günlük dalgalanma
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Stat>
              <HStack>
                <Icon as={FiTarget} color="purple.500" />
                <StatLabel>Sharpe Oranı</StatLabel>
              </HStack>
              <StatNumber>{riskMetrics.sharpeRatio.toFixed(2)}</StatNumber>
              <StatHelpText>
                <StatArrow type={riskMetrics.sharpeRatio > 1 ? 'increase' : 'decrease'} />
                Risk-ayarlı getiri
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Risk Analysis Tabs */}
      <Tabs variant="enclosed" colorScheme="blue">
        <TabList>
          <Tab>Düşüş Analizi</Tab>
          <Tab>Risk Dağılımı</Tab>
          <Tab>Risk Metrikleri</Tab>
          <Tab>Risk Uyarıları</Tab>
        </TabList>

        <TabPanels>
          {/* Drawdown Analysis Tab */}
          <TabPanel px={0}>
            <VStack spacing={6} align="stretch">
              <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                <CardHeader>
                  <Heading size="md">Düşüş Trendi Analizi</Heading>
                </CardHeader>
                <CardBody>
                  {drawdownData.length > 0 ? (
                    <AdvancedLineChart
                      data={drawdownData}
                      title="Portföy Düşüş Trendi"
                      dataKey="value"
                      formatValue={(value) => `${Math.abs(value).toFixed(2)}%`}
                      height={350}
                      color="#E53E3E"
                      showBrush={true}
                      showMovingAverage={true}
                    />
                  ) : (
                    <Alert status="info" borderRadius="md">
                      <AlertIcon />
                      <AlertDescription>
                        Düşüş analizi için yeterli veri bulunmuyor.
                      </AlertDescription>
                    </Alert>
                  )}
                </CardBody>
              </Card>

              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardHeader>
                    <Heading size="sm">Düşüş İstatistikleri</Heading>
                  </CardHeader>
                  <CardBody>
                    <VStack align="stretch" spacing={4}>
                      <HStack justify="space-between">
                        <Text fontSize="sm" color={textColor}>Maksimum Düşüş:</Text>
                        <Badge colorScheme={riskMetrics.maxDrawdown > 20 ? 'red' : 'green'}>
                          {formatPercent(riskMetrics.maxDrawdown)}
                        </Badge>
                      </HStack>
                      <HStack justify="space-between">
                        <Text fontSize="sm" color={textColor}>Toparlanma Faktörü:</Text>
                        <Text fontSize="sm" fontWeight="bold">
                          {stats.recoveryFactor?.toFixed(2) || 'N/A'}
                        </Text>
                      </HStack>
                      <HStack justify="space-between">
                        <Text fontSize="sm" color={textColor}>Düşüş Süresi:</Text>
                        <Text fontSize="sm" fontWeight="bold">
                          {stats.maximumDrawdownDuration || 0} gün
                        </Text>
                      </HStack>
                    </VStack>
                  </CardBody>
                </Card>

                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardHeader>
                    <Heading size="sm">Risk Değerlendirmesi</Heading>
                  </CardHeader>
                  <CardBody>
                    <VStack align="stretch" spacing={4}>
                      <Box>
                        <Text fontSize="sm" color={textColor} mb={2}>Risk Seviyesi</Text>
                        <Progress
                          value={riskMetrics.riskScore}
                          colorScheme={riskMetrics.riskColor}
                          size="lg"
                        />
                        <Text fontSize="xs" color={textColor} mt={1}>
                          {riskMetrics.riskScore.toFixed(0)}/100 - {riskMetrics.riskLevel}
                        </Text>
                      </Box>
                      <HStack justify="space-between">
                        <Text fontSize="sm" color={textColor}>Volatilite Skoru:</Text>
                        <Badge colorScheme={riskMetrics.volatility > 15 ? 'red' : 'green'}>
                          {(riskMetrics.volatility * 2).toFixed(0)}/50
                        </Badge>
                      </HStack>
                    </VStack>
                  </CardBody>
                </Card>
              </SimpleGrid>
            </VStack>
          </TabPanel>

          {/* Risk Distribution Tab */}
          <TabPanel px={0}>
            <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
              <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                <CardHeader>
                  <Heading size="md">Robot Risk Dağılımı</Heading>
                </CardHeader>
                <CardBody>
                  {riskDistributionData.length > 0 ? (
                    <InteractivePieChart
                      data={riskDistributionData}
                      title="Risk Seviyelerine Göre Robot Dağılımı"
                      height={350}
                      formatValue={(value) => `${value} robot`}
                      showPercentages={true}
                    />
                  ) : (
                    <Alert status="info" borderRadius="md">
                      <AlertIcon />
                      <AlertDescription>
                        Risk dağılımı analizi için robot verileri bulunamadı.
                      </AlertDescription>
                    </Alert>
                  )}
                </CardBody>
              </Card>

              <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                <CardHeader>
                  <Heading size="md">Risk Kategorileri</Heading>
                </CardHeader>
                <CardBody>
                  <VStack align="stretch" spacing={4}>
                    <Box p={4} bg="green.50" borderRadius="md" borderLeft="4px solid" borderColor="green.500">
                      <HStack>
                        <Icon as={FiShield} color="green.500" />
                        <VStack align="start" spacing={1}>
                          <Text fontSize="sm" fontWeight="bold" color="green.700">Düşük Risk (0-25)</Text>
                          <Text fontSize="xs" color="green.600">
                            Düşük volatilite, kontrollü düşüş, istikrarlı performans
                          </Text>
                        </VStack>
                      </HStack>
                    </Box>

                    <Box p={4} bg="yellow.50" borderRadius="md" borderLeft="4px solid" borderColor="yellow.500">
                      <HStack>
                        <Icon as={FiBarChart} color="yellow.500" />
                        <VStack align="start" spacing={1}>
                          <Text fontSize="sm" fontWeight="bold" color="yellow.700">Orta Risk (25-50)</Text>
                          <Text fontSize="xs" color="yellow.600">
                            Orta seviye volatilite, kabul edilebilir düşüş seviyeleri
                          </Text>
                        </VStack>
                      </HStack>
                    </Box>

                    <Box p={4} bg="orange.50" borderRadius="md" borderLeft="4px solid" borderColor="orange.500">
                      <HStack>
                        <Icon as={FiTrendingUp} color="orange.500" />
                        <VStack align="start" spacing={1}>
                          <Text fontSize="sm" fontWeight="bold" color="orange.700">Yüksek Risk (50-75)</Text>
                          <Text fontSize="xs" color="orange.600">
                            Yüksek volatilite, dikkatli izleme gerektirir
                          </Text>
                        </VStack>
                      </HStack>
                    </Box>

                    <Box p={4} bg="red.50" borderRadius="md" borderLeft="4px solid" borderColor="red.500">
                      <HStack>
                        <Icon as={FiAlertTriangle} color="red.500" />
                        <VStack align="start" spacing={1}>
                          <Text fontSize="sm" fontWeight="bold" color="red.700">Çok Yüksek Risk (75+)</Text>
                          <Text fontSize="xs" color="red.600">
                            Aşırı volatilite, acil risk yönetimi gerekli
                          </Text>
                        </VStack>
                      </HStack>
                    </Box>
                  </VStack>
                </CardBody>
              </Card>
            </SimpleGrid>
          </TabPanel>

          {/* Risk Metrics Tab */}
          <TabPanel px={0}>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
              <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                <CardHeader>
                  <Heading size="md">Gelişmiş Risk Metrikleri</Heading>
                </CardHeader>
                <CardBody>
                  <VStack align="stretch" spacing={4}>
                    <HStack justify="space-between">
                      <Text fontSize="sm" color={textColor}>Value at Risk (95%):</Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {formatPercent(riskMetrics.valueAtRisk95)}
                      </Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text fontSize="sm" color={textColor}>Conditional VaR:</Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {formatPercent(riskMetrics.conditionalVaR)}
                      </Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text fontSize="sm" color={textColor}>Ulcer Index:</Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {stats.ulcerIndex?.toFixed(2) || 'N/A'}
                      </Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text fontSize="sm" color={textColor}>Calmar Oranı:</Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {stats.calmarRatio?.toFixed(2) || 'N/A'}
                      </Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text fontSize="sm" color={textColor}>Sortino Oranı:</Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {stats.sortinoRatio?.toFixed(2) || 'N/A'}
                      </Text>
                    </HStack>
                  </VStack>
                </CardBody>
              </Card>

              <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                <CardHeader>
                  <Heading size="md">Risk-Getiri Analizi</Heading>
                </CardHeader>
                <CardBody>
                  <VStack align="stretch" spacing={4}>
                    <Box>
                      <Text fontSize="sm" color={textColor} mb={2}>Sharpe Oranı</Text>
                      <Progress
                        value={Math.min(100, Math.max(0, (riskMetrics.sharpeRatio + 2) * 25))}
                        colorScheme={riskMetrics.sharpeRatio > 1 ? 'green' : riskMetrics.sharpeRatio > 0 ? 'yellow' : 'red'}
                        size="lg"
                      />
                      <Text fontSize="xs" color={textColor} mt={1}>
                        {riskMetrics.sharpeRatio.toFixed(2)} (Risk-ayarlı getiri)
                      </Text>
                    </Box>

                    <HStack justify="space-between">
                      <Text fontSize="sm" color={textColor}>Beta:</Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {stats.beta?.toFixed(2) || 'N/A'}
                      </Text>
                    </HStack>

                    <HStack justify="space-between">
                      <Text fontSize="sm" color={textColor}>Jensen Alpha:</Text>
                      <Text fontSize="sm" fontWeight="bold">
                        {formatPercent(stats.jensenAlpha || 0)}
                      </Text>
                    </HStack>
                  </VStack>
                </CardBody>
              </Card>
            </SimpleGrid>
          </TabPanel>

          {/* Risk Alerts Tab */}
          <TabPanel px={0}>
            <VStack spacing={4} align="stretch">
              {riskMetrics.riskScore > 75 && (
                <Alert status="error" borderRadius="md">
                  <AlertIcon />
                  <Box>
                    <Text fontWeight="bold">Kritik Risk Seviyesi!</Text>
                    <Text fontSize="sm">
                      Portföyünüz çok yüksek risk seviyesinde. Acil risk yönetimi önlemleri alınmalı.
                    </Text>
                  </Box>
                </Alert>
              )}

              {riskMetrics.maxDrawdown > 30 && (
                <Alert status="warning" borderRadius="md">
                  <AlertIcon />
                  <Box>
                    <Text fontWeight="bold">Yüksek Düşüş Riski</Text>
                    <Text fontSize="sm">
                      Maksimum düşüş %{riskMetrics.maxDrawdown.toFixed(1)} seviyesinde. Risk yönetimi stratejileri gözden geçirilmeli.
                    </Text>
                  </Box>
                </Alert>
              )}

              {riskMetrics.volatility > 20 && (
                <Alert status="warning" borderRadius="md">
                  <AlertIcon />
                  <Box>
                    <Text fontWeight="bold">Yüksek Volatilite</Text>
                    <Text fontSize="sm">
                      Günlük volatilite %{riskMetrics.volatility.toFixed(1)} seviyesinde. Pozisyon boyutları gözden geçirilmeli.
                    </Text>
                  </Box>
                </Alert>
              )}

              {riskMetrics.sharpeRatio < 0 && (
                <Alert status="error" borderRadius="md">
                  <AlertIcon />
                  <Box>
                    <Text fontWeight="bold">Negatif Risk-Ayarlı Getiri</Text>
                    <Text fontSize="sm">
                      Sharpe oranı negatif ({riskMetrics.sharpeRatio.toFixed(2)}). Strateji gözden geçirilmeli.
                    </Text>
                  </Box>
                </Alert>
              )}

              {riskMetrics.riskScore < 50 && riskMetrics.maxDrawdown < 15 && riskMetrics.volatility < 15 && (
                <Alert status="success" borderRadius="md">
                  <AlertIcon />
                  <Box>
                    <Text fontWeight="bold">Sağlıklı Risk Profili</Text>
                    <Text fontSize="sm">
                      Portföyünüz kabul edilebilir risk seviyelerinde. Mevcut strateji sürdürülebilir.
                    </Text>
                  </Box>
                </Alert>
              )}

              <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                <CardHeader>
                  <Heading size="md">Risk Yönetimi Önerileri</Heading>
                </CardHeader>
                <CardBody>
                  <VStack align="stretch" spacing={3}>
                    <Text fontSize="sm" color={textColor}>
                      • Pozisyon boyutlarını risk toleransınıza göre ayarlayın
                    </Text>
                    <Text fontSize="sm" color={textColor}>
                      • Stop-loss seviyelerini düzenli olarak gözden geçirin
                    </Text>
                    <Text fontSize="sm" color={textColor}>
                      • Portföy çeşitlendirmesi yaparak riski dağıtın
                    </Text>
                    <Text fontSize="sm" color={textColor}>
                      • Risk metriklerini düzenli olarak izleyin ve analiz edin
                    </Text>
                    <Text fontSize="sm" color={textColor}>
                      • Piyasa koşullarına göre risk parametrelerini güncelleyin
                    </Text>
                  </VStack>
                </CardBody>
              </Card>
            </VStack>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </VStack>
  );
};

export default RiskAnalysisPage;
