import React from 'react';
import { 
  Box, 
  Heading, 
  Text, 
  VStack, 
  Code, 
  List, 
  ListItem, 
  ListIcon, 
  Container,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Badge,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Progress,
  Button,
  HStack,
  Icon,
  Divider,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Tag
} from '@chakra-ui/react';
import { 
  MdCheckCircle, 
  MdSecurity, 
  MdDashboard, 
  MdPeople,
  MdSettings,
  MdTrendingUp,
  MdMonetizationOn,
  MdApi,
  MdAutoFixHigh,
  MdTimeline,
  MdWarning,
  MdCode,
  MdSpeed,
  MdLightbulb,
  MdTimer,

} from 'react-icons/md';
import { 
  FaChartLine, 
  FaCoins, 
  FaShieldAlt,
  FaDollarSign,
  FaInfinity
} from 'react-icons/fa';

const BroRobotGuide: React.FC = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const accentColor = useColorModeValue('purple.500', 'purple.300');
  const codeBackground = useColorModeValue('gray.100', 'gray.700');
  // Removed unused variables to fix TypeScript warnings

  const basicJsonExample = `{
  "symbol": "BTCUSDT",
  "side": "BUY",
  "quantity": 0.001,
  "price": 43500,
  "signal_source": "MyAwesomeStrategy",
  "timestamp": "2024-01-15T10:30:00Z"
}`;

  const advancedJsonExample = `{
  "symbol": "ETHUSDT",
  "side": "SELL",
  "quantity": 0.1,
  "price": 2650,
  "stopLoss": 2700,
  "takeProfit": 2600,
  "orderType": "LIMIT",
  "timeInForce": "GTC",
  "signal_source": "AI_Strategy_v2",
  "leverage": 5,
  "timestamp": "2024-01-15T10:30:00Z",
  "metadata": {
    "confidence": 85,
    "riskLevel": "medium",
    "positionSize": "standard"
  }
}`;

  const closePositionExample = `{
  "symbol": "BTCUSDT",
  "side": "SELL",
  "action": "CLOSE_POSITION",
  "quantity": "ALL",
  "reason": "PROFIT_TARGET",
  "signal_source": "MyAwesomeStrategy",
  "timestamp": "2024-01-15T11:45:00Z"
}`;

  // Removed unused clipboard hooks to fix TypeScript warnings

    return (
    <Box minH="100vh" bg={bgColor}>
      <Container maxW="1200px" pb={20}>
        <VStack spacing={12} align="stretch">
          {/* Header Section */}
          <VStack spacing={12} textAlign="center">
            <Badge 
              colorScheme="purple" 
              fontSize="2xl" 
              px={12} 
              py={5} 
              borderRadius="full"
              bgGradient="linear(to-r, purple.400, pink.400, purple.600)"
              color="white"
              fontWeight="bold"
              textTransform="none"
              boxShadow="2xl"
              border="3px solid"
              borderColor="purple.200"
            >
              🤖 Pazaryeri Satıcı Rehberi
            </Badge>
            <VStack spacing={8}>
              <Heading 
                as="h1" 
                size="4xl" 
                bgGradient="linear(to-r, purple.400, pink.400, purple.600)"
                bgClip="text"
                fontWeight="black"
                letterSpacing="tight"
                lineHeight="1.1"
                textAlign="center"
              >
                Bro-Robot
                <br />
                <Text as="span" fontSize="3xl" color={accentColor}>
                  Profesyonel Kılavuzu
                </Text>
              </Heading>
              <Text fontSize="2xl" color={textColor} maxW="1000px" lineHeight="1.8" fontWeight="medium">
                Ticaret robotlarınızı pazaryerinde listeleyerek <strong>sürdürülebilir pasif gelir</strong> 
                elde etmenin profesyonel yolu. Sıfırdan uzman seviyeye kadar tüm süreçleri, 
                kod örnekleriyle ve gerçek senaryolarla öğrenin.
              </Text>
              <HStack spacing={8} pt={6} flexWrap="wrap" justify="center">
                <Badge colorScheme="green" fontSize="lg" px={8} py={4} borderRadius="full" _hover={{ transform: 'scale(1.05)' }} transition="all 0.3s">
                  💰 Gelir Potansiyeli: ₺2,500+/ay
                </Badge>
                <Badge colorScheme="blue" fontSize="lg" px={8} py={4} borderRadius="full" _hover={{ transform: 'scale(1.05)' }} transition="all 0.3s">
                  ⏱ Kurulum: 30 dakika
                </Badge>
                <Badge colorScheme="orange" fontSize="lg" px={8} py={4} borderRadius="full" _hover={{ transform: 'scale(1.05)' }} transition="all 0.3s">
                  📈 Sınırsız Abone
                </Badge>
                <Badge colorScheme="purple" fontSize="lg" px={8} py={4} borderRadius="full" _hover={{ transform: 'scale(1.05)' }} transition="all 0.3s">
                  🏆 %0 Platform Komisyonu
                </Badge>
              </HStack>
            </VStack>
          </VStack>

          {/* Enhanced Stats */}
          <Box 
            bg={cardBg} 
            p={12} 
            borderRadius="3xl" 
            border="3px solid" 
            borderColor="purple.200"
            boxShadow="2xl"
            position="relative"
            overflow="hidden"
            _hover={{ transform: 'translateY(-5px)', boxShadow: '3xl' }}
            transition="all 0.4s"
          >
            <Box
              position="absolute"
              top="0"
              left="0"
              right="0"
              h="8px"
              bgGradient="linear(to-r, purple.400, pink.400, blue.400)"
            />
            <VStack spacing={12}>
              <VStack spacing={6}>
                <Heading size="2xl" color="purple.600" textAlign="center">
                  📊 Bro-Robot Sistem Performansı
                </Heading>
                <Text fontSize="xl" color={textColor} textAlign="center" maxW="800px" lineHeight="1.6">
                  Başarılı satıcıların elde ettiği ortalama değerler ve platform metrikleri
                </Text>
              </VStack>
              <SimpleGrid columns={{ base: 2, md: 4 }} spacing={10} w="full">
                <Stat 
                  textAlign="center" 
                  bg={useColorModeValue('green.50', 'green.900')} 
                  p={8} 
                  borderRadius="2xl" 
                  border="3px solid" 
                  borderColor="green.300"
                  _hover={{ transform: 'translateY(-8px)', borderColor: 'green.400' }}
                  transition="all 0.3s"
                  cursor="pointer"
                >
                  <Icon as={MdMonetizationOn} boxSize={8} color="green.500" mb={4} />
                  <StatLabel fontSize="lg" color="gray.600" fontWeight="bold">Ortalama Aylık Gelir</StatLabel>
                  <StatNumber color="green.500" fontSize="4xl" fontWeight="black">₺2,500+</StatNumber>
                  <StatHelpText fontSize="md" color="green.600" fontWeight="medium">Başarılı satıcılar</StatHelpText>
                  <Progress value={85} colorScheme="green" size="lg" mt={6} borderRadius="full" />
                </Stat>
                <Stat 
                  textAlign="center" 
                  bg={useColorModeValue('blue.50', 'blue.900')} 
                  p={8} 
                  borderRadius="2xl" 
                  border="3px solid" 
                  borderColor="blue.300"
                  _hover={{ transform: 'translateY(-8px)', borderColor: 'blue.400' }}
                  transition="all 0.3s"
                  cursor="pointer"
                >
                  <Icon as={MdTimer} boxSize={8} color="blue.500" mb={4} />
                  <StatLabel fontSize="lg" color="gray.600" fontWeight="bold">Kurulum Süresi</StatLabel>
                  <StatNumber color="blue.500" fontSize="4xl" fontWeight="black">30 dk</StatNumber>
                  <StatHelpText fontSize="md" color="blue.600" fontWeight="medium">İlk robot için</StatHelpText>
                  <Progress value={100} colorScheme="blue" size="lg" mt={6} borderRadius="full" />
                </Stat>
                <Stat 
                  textAlign="center" 
                  bg={useColorModeValue('purple.50', 'purple.900')} 
                  p={8} 
                  borderRadius="2xl" 
                  border="3px solid" 
                  borderColor="purple.300"
                  _hover={{ transform: 'translateY(-8px)', borderColor: 'purple.400' }}
                  transition="all 0.3s"
                  cursor="pointer"
                >
                  <Icon as={FaInfinity} boxSize={8} color="purple.500" mb={4} />
                  <StatLabel fontSize="lg" color="gray.600" fontWeight="bold">Maksimum Abone</StatLabel>
                  <StatNumber color="purple.500" fontSize="4xl" fontWeight="black">∞</StatNumber>
                  <StatHelpText fontSize="md" color="purple.600" fontWeight="medium">Sınırsız kapasite</StatHelpText>
                  <Progress value={100} colorScheme="purple" size="lg" mt={6} borderRadius="full" />
                </Stat>
                <Stat 
                  textAlign="center" 
                  bg={useColorModeValue('orange.50', 'orange.900')} 
                  p={8} 
                  borderRadius="2xl" 
                  border="3px solid" 
                  borderColor="orange.300"
                  _hover={{ transform: 'translateY(-8px)', borderColor: 'orange.400' }}
                  transition="all 0.3s"
                  cursor="pointer"
                >
                  <Icon as={FaDollarSign} boxSize={8} color="orange.500" mb={4} />
                  <StatLabel fontSize="lg" color="gray.600" fontWeight="bold">Platform Komisyonu</StatLabel>
                  <StatNumber color="orange.500" fontSize="4xl" fontWeight="black">%0</StatNumber>
                  <StatHelpText fontSize="md" color="orange.600" fontWeight="medium">Hiçbir ücret yok</StatHelpText>
                  <Progress value={100} colorScheme="orange" size="lg" mt={6} borderRadius="full" />
                </Stat>
              </SimpleGrid>
              
              <Alert status="info" borderRadius="xl" bg={useColorModeValue('blue.50', 'blue.900')} border="2px solid" borderColor="blue.200">
                <AlertIcon />
                <Box flex="1">
                  <AlertTitle fontSize="lg">🚀 Özel Lansman Fırsatı!</AlertTitle>
                  <AlertDescription fontSize="md">
                    İlk 100 robot satıcısı için özel avantajlar: Ücretsiz premium özellikler, 
                    öncelikli destek ve pazaryerinde öne çıkarma garantisi.
                  </AlertDescription>
                </Box>
              </Alert>
            </VStack>
          </Box>

          {/* Main Content Tabs */}
          <Box bg={cardBg} borderRadius="3xl" border="3px solid" borderColor="purple.200" overflow="hidden" boxShadow="xl">
            <Tabs variant="enclosed" colorScheme="purple" size="lg">
              <TabList bg={useColorModeValue('purple.50', 'purple.900')} border="none" p={4}>
                <Tab 
                  fontSize="lg" 
                  fontWeight="bold" 
                  _selected={{ 
                    bg: 'purple.500', 
                    color: 'white',
                    borderRadius: 'lg',
                    transform: 'scale(1.02)'
                  }}
                  _hover={{ bg: 'purple.200' }}
                  transition="all 0.3s"
                >
                  🚀 Hızlı Başlangıç
                </Tab>
                <Tab 
                  fontSize="lg" 
                  fontWeight="bold" 
                  _selected={{ 
                    bg: 'purple.500', 
                    color: 'white',
                    borderRadius: 'lg',
                    transform: 'scale(1.02)'
                  }}
                  _hover={{ bg: 'purple.200' }}
                  transition="all 0.3s"
                >
                  📊 Satıcı Paneli
                </Tab>
                <Tab 
                  fontSize="lg" 
                  fontWeight="bold" 
                  _selected={{ 
                    bg: 'purple.500', 
                    color: 'white',
                    borderRadius: 'lg',
                    transform: 'scale(1.02)'
                  }}
                  _hover={{ bg: 'purple.200' }}
                  transition="all 0.3s"
                >
                  ⚙️ Teknik Detaylar
                </Tab>
                <Tab 
                  fontSize="lg" 
                  fontWeight="bold" 
                  _selected={{ 
                    bg: 'purple.500', 
                    color: 'white',
                    borderRadius: 'lg',
                    transform: 'scale(1.02)'
                  }}
                  _hover={{ bg: 'purple.200' }}
                  transition="all 0.3s"
                >
                  💰 Gelir Optimizasyonu
                </Tab>
              </TabList>

            <TabPanels>
              {/* Hızlı Başlangıç Tab */}
              <TabPanel>
                <VStack spacing={8} align="stretch">
                  <Alert status="info" borderRadius="lg" variant="left-accent">
                    <AlertIcon />
                    <Box>
                      <AlertTitle fontSize="lg">🎯 Bro-Robot Nedir?</AlertTitle>
                      <AlertDescription fontSize="md">
                        Bro-Robot sistemi, ticaret robotlarınızı pazaryerinde listeleyerek diğer kullanıcılara 
                        abonelik hizmeti vermenizi sağlar. Her sinyaliniz, tüm abonelerinize otomatik olarak iletilir 
                        ve onların hesaplarında işlem gerçekleştirilir. Bu sayede hem robot geliştirme yeteneğinizi 
                        paraya çevirir hem de trader topluluğuna katkıda bulunursunuz.
                      </AlertDescription>
                    </Box>
                  </Alert>

                  <Box bg={cardBg} p={8} borderRadius="xl" border="1px solid" borderColor="purple.100">
                    <Heading size="lg" mb={6} color={accentColor}>
                      <Icon as={MdTimeline} mr={3} />
                      5 Adımda Robot Satıcısı Olun
                    </Heading>
                    
                    <VStack spacing={6} align="stretch">
                      {[
                        {
                          step: 1,
                          title: "Satıcı Paneline Giriş",
                          description: "Menüden 'Robotlarım' seçeneğine giderek robot oluşturma sürecini başlatın. İlk robotunuz için temel bilgileri girin.",
                          time: "2 dakika",
                          details: ["Hesabınıza giriş yapın", "Sol menüden 'Robotlarım' seçin", "'Yeni Robot Ekle' butonuna tıklayın"]
                        },
                        {
                          step: 2,
                          title: "Robot Bilgilerini Tanımlayın",
                          description: "Robot adı, açıklama, fiyat ve pozisyon büyüklüğü gibi temel bilgileri girin. Bu bilgiler pazaryerinde görünecek.",
                          time: "5 dakika",
                          details: ["Çekici ve açıklayıcı bir robot adı seçin", "Stratejinizi detaylı açıklayın", "Rekabetçi fiyat belirleyin"]
                        },
                        {
                          step: 3,
                          title: "JSON Formatlarını Ayarlayın",
                          description: "Alış ve satış sinyalleri için borsa-spesifik JSON formatlarını yapılandırın. Bu önemli bir adımdır.",
                          time: "8 dakika",
                          details: ["Alış sinyali JSON formatını tanımlayın", "Satış sinyali JSON formatını tanımlayın", "Test sinyalleri gönderin"]
                        },
                        {
                          step: 4,
                          title: "Robot URL'ini Alın",
                          description: "Sistem size özel bir sinyal gönderme URL'i oluşturacak. Bu URL'i güvenli bir yerde saklayın.",
                          time: "1 dakika",
                          details: ["URL'i kopyalayın", "Güvenli bir yere kaydedin", "Test amaçlı bir sinyal gönderin"]
                        },
                        {
                          step: 5,
                          title: "Robotu Yayınlayın ve Test Edin",
                          description: "Robotunuzu pazaryerinde yayınlayın ve ilk test sinyalinizi göndereren sistemi doğrulayın.",
                          time: "5 dakika",
                          details: ["'Yayınla' butonuna tıklayın", "Test sinyali gönderin", "İlk abonelerinizi bekleyin"]
                        }
                      ].map((item, index) => (
                        <Box key={index} bg={useColorModeValue('gray.50', 'gray.700')} p={6} borderRadius="lg" border="1px solid" borderColor="gray.200">
                          <HStack spacing={4} align="start" mb={4}>
                            <Box
                              bg="purple.500"
                              color="white"
                              borderRadius="full"
                              w={12}
                              h={12}
                              display="flex"
                              alignItems="center"
                              justifyContent="center"
                              fontWeight="bold"
                              flexShrink={0}
                              fontSize="lg"
                            >
                              {item.step}
                            </Box>
                            <VStack align="start" spacing={2} flex={1}>
                              <Text fontSize="xl" fontWeight="bold" color={textColor}>{item.title}</Text>
                              <Text color="gray.500">{item.description}</Text>
                              <Badge colorScheme="green" size="sm">⏱ {item.time}</Badge>
                            </VStack>
                          </HStack>
                          
                          <List spacing={2} pl={16}>
                            {item.details.map((detail, idx) => (
                              <ListItem key={idx} fontSize="sm">
                                <ListIcon as={MdCheckCircle} color="purple.500" />
                                {detail}
                    </ListItem>
                            ))}
                          </List>
                        </Box>
                      ))}
                    </VStack>

                    <Alert status="success" mt={8} borderRadius="lg">
                      <AlertIcon />
                      <Box>
                        <AlertTitle>✨ Tebrikler!</AlertTitle>
                        <AlertDescription>
                          Bu adımları tamamladığınızda robot satıcısı olmaya hazırsınız! 
                          İlk abonelerinizi beklerken diğer tablardaki gelişmiş özellikler hakkında bilgi edinebilirsiniz.
                        </AlertDescription>
                      </Box>
                    </Alert>
                  </Box>

                  <Alert status="warning" borderRadius="lg" variant="left-accent">
                    <AlertIcon />
                    <Box>
                      <AlertTitle>⚡ Hızlı İpucu</AlertTitle>
                      <AlertDescription>
                        İlk robotunuzu yayınlamadan önce mutlaka test edin! Test sinyalleri göndererek 
                        sistem üzerinde doğru çalıştığından emin olun.
                      </AlertDescription>
                    </Box>
                  </Alert>
                </VStack>
              </TabPanel>

              {/* Satıcı Paneli Tab */}
              <TabPanel>
                <VStack spacing={8} align="stretch">
                  <Box bg={cardBg} p={8} borderRadius="xl">
                    <Heading size="lg" mb={6} color={accentColor}>
                      <Icon as={MdDashboard} mr={3} />
                      Satıcı Paneli Kullanımı
                    </Heading>

                    <Accordion allowMultiple>
                      <AccordionItem border="1px solid" borderColor="purple.100" borderRadius="lg" mb={4}>
                        <AccordionButton p={6}>
                          <Icon as={MdSettings} mr={3} color="purple.500" />
                          <Box as="span" flex="1" textAlign="left" fontWeight="semibold">
                            Robot Oluşturma ve Yönetimi
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                        <AccordionPanel pb={6}>
                          <VStack spacing={4} align="stretch">
                            <Text>Satıcı panelinde robotlarınızı oluştururken dikkat etmeniz gereken ana noktalar:</Text>
                            
                            <Box bg={useColorModeValue('blue.50', 'blue.900')} p={4} borderRadius="lg">
                              <Text fontWeight="bold" mb={2} color="blue.600">🔧 Temel Ayarlar:</Text>
                              <List spacing={2}>
                                <ListItem><ListIcon as={MdCheckCircle} color="blue.500" />Robot adı: Kısa, açıklayıcı ve akılda kalıcı olmalı</ListItem>
                                <ListItem><ListIcon as={MdCheckCircle} color="blue.500" />Açıklama: Stratejinizi detaylı ama anlaşılır şekilde açıklayın</ListItem>
                                <ListItem><ListIcon as={MdCheckCircle} color="blue.500" />Fiyat: Pazardaki benzer robotları araştırarak rekabetçi fiyat belirleyin</ListItem>
                                <ListItem><ListIcon as={MdCheckCircle} color="blue.500" />Pozisyon boyutu: Aboneleriniz için optimal yatırım miktarını belirleyin</ListItem>
                              </List>
                            </Box>

                            <Box bg={useColorModeValue('green.50', 'green.900')} p={4} borderRadius="lg">
                              <Text fontWeight="bold" mb={2} color="green.600">📊 İleri Düzey Ayarlar:</Text>
                              <List spacing={2}>
                                <ListItem><ListIcon as={MdCheckCircle} color="green.500" />Risk yönetimi parametreleri</ListItem>
                                <ListItem><ListIcon as={MdCheckCircle} color="green.500" />Stop-loss ve take-profit seviyeleri</ListItem>
                                <ListItem><ListIcon as={MdCheckCircle} color="green.500" />Maksimum günlük işlem sayısı</ListItem>
                                <ListItem><ListIcon as={MdCheckCircle} color="green.500" />Abonelik süresi ve yenileme koşulları</ListItem>
                              </List>
                            </Box>
                          </VStack>
                        </AccordionPanel>
                      </AccordionItem>

                      <AccordionItem border="1px solid" borderColor="purple.100" borderRadius="lg" mb={4}>
                        <AccordionButton p={6}>
                          <Icon as={MdPeople} mr={3} color="purple.500" />
                          <Box as="span" flex="1" textAlign="left" fontWeight="semibold">
                            Abone Yönetimi
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                        <AccordionPanel pb={6}>
                          <VStack spacing={4} align="stretch">
                            <Text>Abonelerinizi etkili şekilde yönetmek için kullanabileceğiniz araçlar:</Text>
                            
                            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                              <Box bg={useColorModeValue('purple.50', 'purple.900')} p={4} borderRadius="lg">
                                <Text fontWeight="bold" mb={3} color="purple.600">👥 Abone İstatistikleri</Text>
                                <List spacing={2} fontSize="sm">
                                  <ListItem>• Toplam abone sayısı</ListItem>
                                  <ListItem>• Aktif/pasif abone dağılımı</ListItem>
                                  <ListItem>• Aylık abone değişimi</ListItem>
                                  <ListItem>• Ortalama abonelik süresi</ListItem>
                                </List>
                              </Box>
                              
                              <Box bg={useColorModeValue('orange.50', 'orange.900')} p={4} borderRadius="lg">
                                <Text fontWeight="bold" mb={3} color="orange.600">📢 İletişim Araçları</Text>
                                <List spacing={2} fontSize="sm">
                                  <ListItem>• Önemli duyurular gönderme</ListItem>
                                  <ListItem>• Strateji güncellemeleri</ListItem>
                                  <ListItem>• Performans raporları paylaşma</ListItem>
                                  <ListItem>• Geri bildirim toplama</ListItem>
                </List>
                              </Box>
                            </SimpleGrid>
                          </VStack>
                        </AccordionPanel>
                      </AccordionItem>

                      <AccordionItem border="1px solid" borderColor="purple.100" borderRadius="lg">
                        <AccordionButton p={6}>
                          <Icon as={MdTrendingUp} mr={3} color="purple.500" />
                          <Box as="span" flex="1" textAlign="left" fontWeight="semibold">
                            Performans Takibi
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                        <AccordionPanel pb={6}>
                          <VStack spacing={4} align="stretch">
                            <Text>Robotunuzun performansını takip etmek için kullanabileceğiniz metrikler:</Text>
                            
                            <TableContainer>
                              <Table variant="simple" size="sm">
                                <Thead>
                                  <Tr>
                                    <Th>Metrik</Th>
                                    <Th>Açıklama</Th>
                                    <Th>Önem Seviyesi</Th>
                                  </Tr>
                                </Thead>
                                <Tbody>
                                  <Tr>
                                    <Td fontWeight="bold">Win Rate</Td>
                                    <Td>Karlı işlem yüzdesi</Td>
                                    <Td><Badge colorScheme="red">Yüksek</Badge></Td>
                                  </Tr>
                                  <Tr>
                                    <Td fontWeight="bold">Average Return</Td>
                                    <Td>Ortalama getiri oranı</Td>
                                    <Td><Badge colorScheme="red">Yüksek</Badge></Td>
                                  </Tr>
                                  <Tr>
                                    <Td fontWeight="bold">Max Drawdown</Td>
                                    <Td>Maksimum kayıp oranı</Td>
                                    <Td><Badge colorScheme="orange">Orta</Badge></Td>
                                  </Tr>
                                  <Tr>
                                    <Td fontWeight="bold">Sharpe Ratio</Td>
                                    <Td>Risk-getiri dengesي</Td>
                                    <Td><Badge colorScheme="orange">Orta</Badge></Td>
                                  </Tr>
                                  <Tr>
                                    <Td fontWeight="bold">Signal Frequency</Td>
                                    <Td>Günlük sinyal sayısı</Td>
                                    <Td><Badge colorScheme="green">Düşük</Badge></Td>
                                  </Tr>
                                </Tbody>
                              </Table>
                            </TableContainer>
                          </VStack>
                        </AccordionPanel>
                      </AccordionItem>
                    </Accordion>
                  </Box>
                </VStack>
              </TabPanel>

              {/* Teknik Detaylar Tab */}
              <TabPanel>
                <VStack spacing={8} align="stretch">
                  <Box bg={cardBg} p={8} borderRadius="xl">
                    <Heading size="lg" mb={6} color={accentColor}>
                      <Icon as={MdApi} mr={3} />
                      API Dokümantasyonu
                    </Heading>

                    <Alert status="info" mb={6} borderRadius="lg">
                      <AlertIcon />
                      <Box>
                        <AlertTitle>🔗 Seller-Signal-Endpoint</AlertTitle>
                        <AlertDescription>
                          Robot sinyallerinizi göndermek için kullanacağınız API endpoint'i. 
                          Robot oluşturduktan sonra size özel URL verilecektir.
                        </AlertDescription>
                      </Box>
                    </Alert>

                    <Accordion allowMultiple>
                      <AccordionItem border="1px solid" borderColor="blue.100" borderRadius="lg" mb={4}>
                        <AccordionButton p={6}>
                          <Icon as={MdCode} mr={3} color="blue.500" />
                          <Box as="span" flex="1" textAlign="left" fontWeight="semibold">
                            Basit Sinyal Formatı
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                        <AccordionPanel pb={6}>
                          <VStack spacing={4} align="stretch">
                            <Text>En temel sinyal formatı. Başlangıç için idealdir:</Text>
                            <Box bg={codeBackground} p={4} borderRadius="lg" position="relative">
                              <Code display="block" whiteSpace="pre" fontSize="sm" p={0} bg="transparent">
                                {basicJsonExample}
                              </Code>
                            </Box>
                            <Text fontSize="sm" color="gray.500">
                              Bu format tüm temel bilgileri içerir ve abonelerinizin hesaplarında basit alım/satım işlemleri gerçekleştirir.
                            </Text>
                          </VStack>
                        </AccordionPanel>
                      </AccordionItem>

                      <AccordionItem border="1px solid" borderColor="green.100" borderRadius="lg" mb={4}>
                        <AccordionButton p={6}>
                          <Icon as={MdAutoFixHigh} mr={3} color="green.500" />
                          <Box as="span" flex="1" textAlign="left" fontWeight="semibold">
                            Gelişmiş Sinyal Formatı
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                        <AccordionPanel pb={6}>
                          <VStack spacing={4} align="stretch">
                            <Text>Stop-loss, take-profit ve meta veriler içeren gelişmiş format:</Text>
                            <Box bg={codeBackground} p={4} borderRadius="lg" position="relative">
                              <Code display="block" whiteSpace="pre" fontSize="sm" p={0} bg="transparent">
                                {advancedJsonExample}
                              </Code>
                            </Box>
                            <Alert status="success" size="sm">
                              <AlertIcon />
                              <Text fontSize="sm">
                                Bu format risk yönetimi araçları ve gelişmiş emir türleri içerir. 
                                Profesyonel trader'lar için önerilir.
                </Text>
                            </Alert>
                          </VStack>
                        </AccordionPanel>
                      </AccordionItem>

                      <AccordionItem border="1px solid" borderColor="red.100" borderRadius="lg" mb={4}>
                        <AccordionButton p={6}>
                          <Icon as={MdWarning} mr={3} color="red.500" />
                          <Box as="span" flex="1" textAlign="left" fontWeight="semibold">
                            Pozisyon Kapatma Sinyali
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                        <AccordionPanel pb={6}>
                          <VStack spacing={4} align="stretch">
                            <Text>Açık pozisyonları kapatmak için kullanılan özel format:</Text>
                            <Box bg={codeBackground} p={4} borderRadius="lg" position="relative">
                              <Code display="block" whiteSpace="pre" fontSize="sm" p={0} bg="transparent">
                                {closePositionExample}
                </Code>
                            </Box>
                            <Alert status="warning" size="sm">
                              <AlertIcon />
                              <Text fontSize="sm">
                                "action": "CLOSE_POSITION" parametresi önemlidir. Bu olmadan normal alım/satım işlemi olarak algılanır.
                              </Text>
                            </Alert>
                          </VStack>
                        </AccordionPanel>
                      </AccordionItem>
                    </Accordion>

                    <Divider my={8} />

                    <Box>
                      <Heading size="md" mb={4} color={accentColor}>
                        <Icon as={MdSecurity} mr={2} />
                        Güvenlik ve Sınırlar
                      </Heading>
                      
                      <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                        <Box bg={useColorModeValue('red.50', 'red.900')} p={4} borderRadius="lg">
                          <Text fontWeight="bold" mb={3} color="red.600">🚨 Hata Kodları</Text>
                          <List spacing={2} fontSize="sm">
                            <ListItem><Tag size="sm" colorScheme="green" mr={2}>200</Tag>Başarılı</ListItem>
                            <ListItem><Tag size="sm" colorScheme="orange" mr={2}>400</Tag>Geçersiz JSON formatı</ListItem>
                            <ListItem><Tag size="sm" colorScheme="red" mr={2}>401</Tag>Geçersiz Robot ID</ListItem>
                            <ListItem><Tag size="sm" colorScheme="purple" mr={2}>429</Tag>Rate limit aşıldı</ListItem>
                          </List>
                        </Box>
                        
                        <Box bg={useColorModeValue('blue.50', 'blue.900')} p={4} borderRadius="lg">
                          <Text fontWeight="bold" mb={3} color="blue.600">⚡ Rate Limiting</Text>
                          <List spacing={2} fontSize="sm">
                            <ListItem>• Dakikada maksimum 10 sinyal</ListItem>
                            <ListItem>• Saatte maksimum 50 sinyal</ListItem>
                            <ListItem>• Günlük maksimum 200 sinyal</ListItem>
                            <ListItem>• Aşım durumunda 15 dk bekleme</ListItem>
                          </List>
                        </Box>
                      </SimpleGrid>

                      <Alert status="info" mt={6} borderRadius="lg">
                        <AlertIcon />
                        <Box>
                          <AlertTitle>🔐 Güvenlik Notları</AlertTitle>
                          <AlertDescription>
                            • Robot ID'nizi asla üçüncü kişilerle paylaşmayın
                            <br />
                            • API endpoint'lerinizi güvenli bir şekilde saklayın
                            <br />
                            • Şüpheli aktivite durumunda hemen destek ekibimizle iletişime geçin
                          </AlertDescription>
                        </Box>
                      </Alert>
                    </Box>
                  </Box>
                </VStack>
              </TabPanel>

              {/* Gelir Optimizasyonu Tab */}
              <TabPanel>
                <VStack spacing={8} align="stretch">
                  <Box bg={cardBg} p={8} borderRadius="xl">
                    <Heading size="lg" mb={6} color={accentColor}>
                      <Icon as={MdMonetizationOn} mr={3} />
                      Gelir Stratejileri
                    </Heading>

                    <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6} mb={8}>
                      <Box bg={useColorModeValue('green.50', 'green.900')} p={6} borderRadius="lg" textAlign="center">
                        <Icon as={FaCoins} w={12} h={12} color="green.500" mb={4} />
                        <Text fontWeight="bold" fontSize="lg" color="green.600" mb={2}>Başlangıç Seviyesi</Text>
                        <Text fontSize="2xl" fontWeight="bold" color="green.500" mb={2}>₺49 - ₺99</Text>
                        <Text fontSize="sm" color="gray.500">Aylık abonelik ücreti</Text>
                        <List spacing={1} mt={4} fontSize="sm">
                          <ListItem>• Basit stratejiler</ListItem>
                          <ListItem>• 1-2 sinyal/gün</ListItem>
                          <ListItem>• Temel destek</ListItem>
                        </List>
                      </Box>
                      
                      <Box bg={useColorModeValue('blue.50', 'blue.900')} p={6} borderRadius="lg" textAlign="center">
                        <Icon as={FaChartLine} w={12} h={12} color="blue.500" mb={4} />
                        <Text fontWeight="bold" fontSize="lg" color="blue.600" mb={2}>Büyüme Seviyesi</Text>
                        <Text fontSize="2xl" fontWeight="bold" color="blue.500" mb={2}>₺149 - ₺299</Text>
                        <Text fontSize="sm" color="gray.500">Aylık abonelik ücreti</Text>
                        <List spacing={1} mt={4} fontSize="sm">
                          <ListItem>• Gelişmiş stratejiler</ListItem>
                          <ListItem>• 3-5 sinyal/gün</ListItem>
                          <ListItem>• Priortize destek</ListItem>
                        </List>
                      </Box>
                      
                      <Box bg={useColorModeValue('purple.50', 'purple.900')} p={6} borderRadius="lg" textAlign="center">
                        <Icon as={FaShieldAlt} w={12} h={12} color="purple.500" mb={4} />
                        <Text fontWeight="bold" fontSize="lg" color="purple.600" mb={2}>Premium Seviye</Text>
                        <Text fontSize="2xl" fontWeight="bold" color="purple.500" mb={2}>₺499+</Text>
                        <Text fontSize="sm" color="gray.500">Aylık abonelik ücreti</Text>
                        <List spacing={1} mt={4} fontSize="sm">
                          <ListItem>• AI destekli stratejiler</ListItem>
                          <ListItem>• 7+ sinyal/gün</ListItem>
                          <ListItem>• VIP destek</ListItem>
                        </List>
                      </Box>
                    </SimpleGrid>

                    <Divider my={8} />

                    <Box>
                      <Heading size="md" mb={6} color={accentColor}>
                        <Icon as={MdTrendingUp} mr={2} />
                        Performans Metrikleri
                      </Heading>
                      
                      <Text mb={4} color={textColor}>
                        Abonelerinizi artırmak ve elde tutmak için odaklanmanız gereken ana metrikler:
                      </Text>

                      <Accordion allowMultiple>
                        <AccordionItem border="1px solid" borderColor="green.100" borderRadius="lg" mb={4}>
                          <AccordionButton p={4}>
                            <Icon as={MdAutoFixHigh} mr={3} color="green.500" />
                            <Box as="span" flex="1" textAlign="left" fontWeight="semibold">
                              Sinyal Kalitesi (En Önemli)
                            </Box>
                            <AccordionIcon />
                          </AccordionButton>
                          <AccordionPanel pb={4}>
                            <VStack spacing={3} align="stretch">
                              <HStack>
                                <Text fontWeight="bold">Hedef Win Rate:</Text>
                                <Badge colorScheme="green">%65+</Badge>
                              </HStack>
                              <Progress value={65} colorScheme="green" />
                              <Text fontSize="sm" color="gray.500">
                                Sinyallerinizin %65 ve üzeri başarı oranına sahip olması, abone sayınızı önemli ölçüde artırır.
                              </Text>
                            </VStack>
                          </AccordionPanel>
                        </AccordionItem>

                        <AccordionItem border="1px solid" borderColor="blue.100" borderRadius="lg" mb={4}>
                          <AccordionButton p={4}>
                            <Icon as={MdSpeed} mr={3} color="blue.500" />
                            <Box as="span" flex="1" textAlign="left" fontWeight="semibold">
                              Zamanlama ve Tutarlılık
                            </Box>
                            <AccordionIcon />
                          </AccordionButton>
                          <AccordionPanel pb={4}>
                            <VStack spacing={3} align="stretch">
                              <HStack>
                                <Text fontWeight="bold">Günlük Sinyal:</Text>
                                <Badge colorScheme="blue">2-4 adet</Badge>
                              </HStack>
                              <Progress value={75} colorScheme="blue" />
                              <Text fontSize="sm" color="gray.500">
                                Çok fazla sinyal spam yaratırken, çok az sinyal değer kaybına neden olur. 2-4 kaliteli sinyal idealdir.
                              </Text>
                            </VStack>
                          </AccordionPanel>
                        </AccordionItem>

                        <AccordionItem border="1px solid" borderColor="purple.100" borderRadius="lg">
                          <AccordionButton p={4}>
                            <Icon as={MdPeople} mr={3} color="purple.500" />
                            <Box as="span" flex="1" textAlign="left" fontWeight="semibold">
                              İletişim ve Şeffaflık
                            </Box>
                            <AccordionIcon />
                          </AccordionButton>
                          <AccordionPanel pb={4}>
                            <VStack spacing={3} align="stretch">
                              <HStack>
                                <Text fontWeight="bold">Haftalık Rapor:</Text>
                                <Badge colorScheme="purple">Önerilen</Badge>
                              </HStack>
                              <Progress value={85} colorScheme="purple" />
                              <Text fontSize="sm" color="gray.500">
                                Abonelerinizle düzenli iletişim kurarak güven oluşturun ve performans raporları paylaşın.
                </Text>
                            </VStack>
                          </AccordionPanel>
                        </AccordionItem>
                      </Accordion>
                    </Box>

                    <Alert status="success" mt={8} borderRadius="lg">
                      <AlertIcon />
                      <Box>
                        <AlertTitle>💡 Başarı Formülü</AlertTitle>
                        <AlertDescription>
                          <Text fontWeight="bold">Kaliteli Sinyaller + Tutarlı Performans + İyi İletişim = Başarılı Robot Satıcısı</Text>
                          <br />
                          Bu üç unsuru dengede tutarak sürdürülebilir gelir elde edebilirsiniz.
                        </AlertDescription>
                      </Box>
                    </Alert>

                    <Box mt={8} bg={useColorModeValue('yellow.50', 'yellow.900')} p={6} borderRadius="lg">
                      <Heading size="sm" mb={4} color="yellow.600">
                        <Icon as={MdLightbulb} mr={2} />
                        Pro İpuçları
                      </Heading>
                      <List spacing={2} fontSize="sm">
                        <ListItem><ListIcon as={MdCheckCircle} color="yellow.500" />Pazardaki rakiplerinizi analiz edin ve farklılaşın</ListItem>
                        <ListItem><ListIcon as={MdCheckCircle} color="yellow.500" />Abonelerinizden geri bildirim alın ve stratejinizi geliştirin</ListItem>
                        <ListItem><ListIcon as={MdCheckCircle} color="yellow.500" />Sosyal medyada varlığınızı artırın ve güvenilirlik oluşturun</ListItem>
                        <ListItem><ListIcon as={MdCheckCircle} color="yellow.500" />İlk 30 gün ücretsiz deneme sunarak abone sayınızı artırın</ListItem>
                        <ListItem><ListIcon as={MdCheckCircle} color="yellow.500" />Düzenli aralıklarla fiyat stratejinizi gözden geçirin</ListItem>
                </List>
                    </Box>
                  </Box>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
          </Box>

          {/* Final CTA */}
          <Box bg={useColorModeValue('purple.50', 'purple.900')} p={8} borderRadius="xl" textAlign="center">
            <VStack spacing={4}>
              <Heading size="lg" color="purple.600">
                🚀 Robot Satıcısı Olmaya Hazır mısınız?
              </Heading>
              <Text color={textColor} maxW="600px">
                Bu rehber ile robot satıcısı olmanın tüm inceliklerini öğrendiniz. 
                Şimdi kendi pasif gelir kaynağınızı oluşturma zamanı!
              </Text>
              <Button colorScheme="purple" size="lg" as="a" href="/seller/robots">
                Hemen Başla →
              </Button>
            </VStack>
          </Box>
        </VStack>
      </Container>
        </Box>
    );
};

export default BroRobotGuide; 