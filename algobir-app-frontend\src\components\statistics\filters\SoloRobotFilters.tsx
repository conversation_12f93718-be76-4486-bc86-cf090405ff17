import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardBody,
  HStack,
  VStack,
  Text,
  Select,
  Input,
  Button,
  ButtonGroup,
  Badge,

  Icon,
  useColorModeValue,
  Collapse,
  useDisclosure,
  RangeSlider,
  RangeSliderTrack,
  RangeSliderFilledTrack,
  RangeSliderThumb,
  FormControl,
  FormLabel,
  Switch,
  Tooltip,
  IconButton,
  Wrap,
  WrapItem
} from '@chakra-ui/react';
import {
  FiFilter,
  FiCalendar,
  FiTrendingUp,
  FiTarget,
  FiDollarSign,
  FiRefreshCw,
  FiX,
  FiChevronDown,
  FiChevronUp
} from 'react-icons/fi';

export interface SoloRobotFilterState {
  dateRange: {
    preset: 'all' | '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'custom';
    startDate: string;
    endDate: string;
  };
  performance: {
    minROI: number;
    maxROI: number;
    minWinRate: number;
    maxWinRate: number;
    minTrades: number;
    maxTrades: number;
  };
  metrics: {
    showProfitable: boolean;
    showUnprofitable: boolean;
    minProfitFactor: number;
    maxDrawdown: number;
  };
  display: {
    chartType: 'area' | 'line' | 'bar' | 'candlestick';
    timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly';
    groupBy: 'time' | 'symbol' | 'performance';
  };
}

interface SoloRobotFiltersProps {
  filters: SoloRobotFilterState;
  onFiltersChange: (filters: SoloRobotFilterState) => void;
  onReset: () => void;
  isLoading?: boolean;
}

const SoloRobotFilters: React.FC<SoloRobotFiltersProps> = ({
  filters,
  onFiltersChange,
  onReset,
  isLoading = false
}) => {
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: false });
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');

  // Calculate active filters count
  useEffect(() => {
    let count = 0;
    if (filters.dateRange.preset !== 'all') count++;
    if (filters.performance.minROI > -100 || filters.performance.maxROI < 1000) count++;
    if (filters.performance.minWinRate > 0 || filters.performance.maxWinRate < 100) count++;
    if (filters.performance.minTrades > 0) count++;
    if (!filters.metrics.showProfitable || !filters.metrics.showUnprofitable) count++;
    if (filters.metrics.minProfitFactor > 0) count++;
    if (filters.metrics.maxDrawdown < 100) count++;
    
    setActiveFiltersCount(count);
  }, [filters]);

  const handleFilterChange = (section: keyof SoloRobotFilterState, key: string, value: any) => {
    onFiltersChange({
      ...filters,
      [section]: {
        ...filters[section],
        [key]: value
      }
    });
  };

  const datePresets = [
    { value: 'all', label: 'Tüm Zamanlar' },
    { value: '1D', label: 'Son 1 Gün' },
    { value: '1W', label: 'Son 1 Hafta' },
    { value: '1M', label: 'Son 1 Ay' },
    { value: '3M', label: 'Son 3 Ay' },
    { value: '6M', label: 'Son 6 Ay' },
    { value: '1Y', label: 'Son 1 Yıl' },
    { value: 'custom', label: 'Özel Tarih' }
  ];

  const chartTypes = [
    { value: 'area', label: 'Alan', icon: FiTrendingUp },
    { value: 'line', label: 'Çizgi', icon: FiTrendingUp },
    { value: 'bar', label: 'Çubuk', icon: FiTarget },
    { value: 'candlestick', label: 'Mum', icon: FiDollarSign }
  ];

  const timeframes = [
    { value: 'hourly', label: 'Saatlik' },
    { value: 'daily', label: 'Günlük' },
    { value: 'weekly', label: 'Haftalık' },
    { value: 'monthly', label: 'Aylık' }
  ];

  return (
    <Card bg={cardBg} borderRadius="xl" shadow="sm" border="1px" borderColor={borderColor}>
      <CardBody p={4}>
        <VStack spacing={4} align="stretch">
          {/* Filter Header */}
          <HStack justify="space-between" cursor="pointer" onClick={onToggle}>
            <HStack spacing={3}>
              <Icon as={FiFilter} color="blue.500" boxSize={5} />
              <Text fontWeight="semibold" color={textColor}>
                Gelişmiş Filtreler
              </Text>
              {activeFiltersCount > 0 && (
                <Badge colorScheme="blue" variant="solid" borderRadius="full">
                  {activeFiltersCount}
                </Badge>
              )}
            </HStack>
            <HStack spacing={2}>
              {activeFiltersCount > 0 && (
                <Tooltip label="Filtreleri Temizle">
                  <IconButton
                    aria-label="Filtreleri temizle"
                    icon={<FiX />}
                    size="sm"
                    variant="ghost"
                    colorScheme="red"
                    onClick={(e) => {
                      e.stopPropagation();
                      onReset();
                    }}
                  />
                </Tooltip>
              )}
              <Icon as={isOpen ? FiChevronUp : FiChevronDown} color="gray.500" />
            </HStack>
          </HStack>

          {/* Quick Filters */}
          <Wrap spacing={2}>
            <WrapItem>
              <ButtonGroup size="sm" isAttached variant="outline">
                {datePresets.slice(0, 4).map((preset) => (
                  <Button
                    key={preset.value}
                    isActive={filters.dateRange.preset === preset.value}
                    onClick={() => handleFilterChange('dateRange', 'preset', preset.value)}
                  >
                    {preset.label}
                  </Button>
                ))}
              </ButtonGroup>
            </WrapItem>
            <WrapItem>
              <ButtonGroup size="sm" isAttached variant="outline">
                {chartTypes.map((type) => (
                  <Button
                    key={type.value}
                    isActive={filters.display.chartType === type.value}
                    onClick={() => handleFilterChange('display', 'chartType', type.value)}
                    leftIcon={<Icon as={type.icon} />}
                  >
                    {type.label}
                  </Button>
                ))}
              </ButtonGroup>
            </WrapItem>
          </Wrap>

          {/* Detailed Filters */}
          <Collapse in={isOpen} animateOpacity>
            <VStack spacing={6} align="stretch" pt={4}>
              {/* Date Range Filters */}
              <Box>
                <Text fontSize="sm" fontWeight="medium" color={textColor} mb={3}>
                  <Icon as={FiCalendar} mr={2} />
                  Tarih Aralığı
                </Text>
                <VStack spacing={3} align="stretch">
                  <Select
                    value={filters.dateRange.preset}
                    onChange={(e) => handleFilterChange('dateRange', 'preset', e.target.value)}
                    size="sm"
                  >
                    {datePresets.map((preset) => (
                      <option key={preset.value} value={preset.value}>
                        {preset.label}
                      </option>
                    ))}
                  </Select>
                  
                  {filters.dateRange.preset === 'custom' && (
                    <HStack spacing={3}>
                      <FormControl>
                        <FormLabel fontSize="xs">Başlangıç</FormLabel>
                        <Input
                          type="date"
                          size="sm"
                          value={filters.dateRange.startDate}
                          onChange={(e) => handleFilterChange('dateRange', 'startDate', e.target.value)}
                        />
                      </FormControl>
                      <FormControl>
                        <FormLabel fontSize="xs">Bitiş</FormLabel>
                        <Input
                          type="date"
                          size="sm"
                          value={filters.dateRange.endDate}
                          onChange={(e) => handleFilterChange('dateRange', 'endDate', e.target.value)}
                        />
                      </FormControl>
                    </HStack>
                  )}
                </VStack>
              </Box>

              {/* Performance Filters */}
              <Box>
                <Text fontSize="sm" fontWeight="medium" color={textColor} mb={3}>
                  <Icon as={FiTrendingUp} mr={2} />
                  Performans Kriterleri
                </Text>
                <VStack spacing={4} align="stretch">
                  <FormControl>
                    <FormLabel fontSize="xs">ROI Aralığı (%)</FormLabel>
                    <RangeSlider
                      min={-100}
                      max={1000}
                      step={5}
                      value={[filters.performance.minROI, filters.performance.maxROI]}
                      onChange={(values) => {
                        handleFilterChange('performance', 'minROI', values[0]);
                        handleFilterChange('performance', 'maxROI', values[1]);
                      }}
                    >
                      <RangeSliderTrack>
                        <RangeSliderFilledTrack />
                      </RangeSliderTrack>
                      <RangeSliderThumb index={0} />
                      <RangeSliderThumb index={1} />
                    </RangeSlider>
                    <HStack justify="space-between" fontSize="xs" color="gray.500">
                      <Text>{filters.performance.minROI}%</Text>
                      <Text>{filters.performance.maxROI}%</Text>
                    </HStack>
                  </FormControl>

                  <FormControl>
                    <FormLabel fontSize="xs">Kazanma Oranı (%)</FormLabel>
                    <RangeSlider
                      min={0}
                      max={100}
                      step={1}
                      value={[filters.performance.minWinRate, filters.performance.maxWinRate]}
                      onChange={(values) => {
                        handleFilterChange('performance', 'minWinRate', values[0]);
                        handleFilterChange('performance', 'maxWinRate', values[1]);
                      }}
                    >
                      <RangeSliderTrack>
                        <RangeSliderFilledTrack />
                      </RangeSliderTrack>
                      <RangeSliderThumb index={0} />
                      <RangeSliderThumb index={1} />
                    </RangeSlider>
                    <HStack justify="space-between" fontSize="xs" color="gray.500">
                      <Text>{filters.performance.minWinRate}%</Text>
                      <Text>{filters.performance.maxWinRate}%</Text>
                    </HStack>
                  </FormControl>
                </VStack>
              </Box>

              {/* Display Options */}
              <Box>
                <Text fontSize="sm" fontWeight="medium" color={textColor} mb={3}>
                  <Icon as={FiTarget} mr={2} />
                  Görünüm Seçenekleri
                </Text>
                <VStack spacing={3} align="stretch">
                  <FormControl>
                    <FormLabel fontSize="xs">Zaman Dilimi</FormLabel>
                    <Select
                      value={filters.display.timeframe}
                      onChange={(e) => handleFilterChange('display', 'timeframe', e.target.value)}
                      size="sm"
                    >
                      {timeframes.map((timeframe) => (
                        <option key={timeframe.value} value={timeframe.value}>
                          {timeframe.label}
                        </option>
                      ))}
                    </Select>
                  </FormControl>

                  <HStack justify="space-between">
                    <FormControl display="flex" alignItems="center">
                      <FormLabel fontSize="xs" mb={0}>Karlı İşlemler</FormLabel>
                      <Switch
                        isChecked={filters.metrics.showProfitable}
                        onChange={(e) => handleFilterChange('metrics', 'showProfitable', e.target.checked)}
                        colorScheme="green"
                        size="sm"
                      />
                    </FormControl>
                    <FormControl display="flex" alignItems="center">
                      <FormLabel fontSize="xs" mb={0}>Zararlı İşlemler</FormLabel>
                      <Switch
                        isChecked={filters.metrics.showUnprofitable}
                        onChange={(e) => handleFilterChange('metrics', 'showUnprofitable', e.target.checked)}
                        colorScheme="red"
                        size="sm"
                      />
                    </FormControl>
                  </HStack>
                </VStack>
              </Box>

              {/* Action Buttons */}
              <HStack justify="flex-end" spacing={3} pt={2}>
                <Button
                  size="sm"
                  variant="outline"
                  leftIcon={<FiRefreshCw />}
                  onClick={onReset}
                  isDisabled={activeFiltersCount === 0}
                >
                  Sıfırla
                </Button>
                <Button
                  size="sm"
                  colorScheme="blue"
                  leftIcon={<FiFilter />}
                  isLoading={isLoading}
                  loadingText="Uygulanıyor"
                >
                  Filtreleri Uygula
                </Button>
              </HStack>
            </VStack>
          </Collapse>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default SoloRobotFilters;
