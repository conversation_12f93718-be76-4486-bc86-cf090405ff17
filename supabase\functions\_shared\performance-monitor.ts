// Enhanced real-time performance monitoring for order transmission speed tracking
// Provides granular metrics and real-time dashboards

export interface DetailedPerformanceMetrics {
  // Timing metrics (in milliseconds)
  signalReceivedAt: string;
  jsonParsingTime: number;
  transformationTime: number;
  databaseLookupTime: number;
  cacheHitRate: number;
  webhookDeliveryTime: number;
  totalProcessingTime: number;
  
  // System metrics
  memoryUsage: number; // MB
  cpuUsage?: number; // Percentage
  activeConnections: number;
  
  // Business metrics
  signalType: string;
  signalSource: 'solo-robot' | 'bro-robot';
  symbol: string;
  orderSide: string;
  subscriberCount?: number;
  successRate: number;
  
  // Error tracking
  errorCount: number;
  errorTypes: string[];
  retryCount: number;
  
  // Performance thresholds
  isSlowRequest: boolean;
  bottleneckStage: string | null;
}

export interface PerformanceAlert {
  id: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: 'latency' | 'error_rate' | 'memory' | 'throughput';
  message: string;
  metrics: any;
  resolved: boolean;
}

export interface PerformanceThresholds {
  // Latency thresholds (ms)
  jsonParsingWarning: number;
  jsonParsingCritical: number;
  databaseWarning: number;
  databaseCritical: number;
  webhookWarning: number;
  webhookCritical: number;
  totalWarning: number;
  totalCritical: number;
  
  // System thresholds
  memoryWarning: number; // MB
  memoryCritical: number; // MB
  errorRateWarning: number; // Percentage
  errorRateCritical: number; // Percentage
}

const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  jsonParsingWarning: 20,
  jsonParsingCritical: 50,
  databaseWarning: 50,
  databaseCritical: 100,
  webhookWarning: 100,
  webhookCritical: 200,
  totalWarning: 200,
  totalCritical: 500,
  memoryWarning: 100,
  memoryCritical: 200,
  errorRateWarning: 5,
  errorRateCritical: 10
};

/**
 * Enhanced performance monitor with real-time tracking
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: DetailedPerformanceMetrics[] = [];
  private alerts: PerformanceAlert[] = [];
  private thresholds: PerformanceThresholds;
  private maxMetricsHistory = 1000;
  private maxAlertsHistory = 100;

  constructor(thresholds: Partial<PerformanceThresholds> = {}) {
    this.thresholds = { ...DEFAULT_THRESHOLDS, ...thresholds };
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Record detailed performance metrics
   */
  recordMetrics(metrics: Partial<DetailedPerformanceMetrics>): void {
    const completeMetrics: DetailedPerformanceMetrics = {
      signalReceivedAt: new Date().toISOString(),
      jsonParsingTime: 0,
      transformationTime: 0,
      databaseLookupTime: 0,
      cacheHitRate: 0,
      webhookDeliveryTime: 0,
      totalProcessingTime: 0,
      memoryUsage: 0,
      activeConnections: 0,
      signalType: 'unknown',
      signalSource: 'solo-robot',
      symbol: 'UNKNOWN',
      orderSide: 'BUY',
      successRate: 100,
      errorCount: 0,
      errorTypes: [],
      retryCount: 0,
      isSlowRequest: false,
      bottleneckStage: null,
      ...metrics
    };

    // Determine if request is slow and identify bottleneck
    this.analyzePerformance(completeMetrics);

    // Add to metrics history
    this.metrics.push(completeMetrics);
    
    // Trim history if needed
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }

    // Check for alerts
    this.checkAlerts(completeMetrics);

    // Log performance summary
    this.logPerformanceSummary(completeMetrics);
  }

  /**
   * Analyze performance and identify bottlenecks
   */
  private analyzePerformance(metrics: DetailedPerformanceMetrics): void {
    const { thresholds } = this;
    
    // Check if request is slow
    metrics.isSlowRequest = metrics.totalProcessingTime > thresholds.totalWarning;

    // Identify bottleneck stage
    if (metrics.webhookDeliveryTime > thresholds.webhookWarning) {
      metrics.bottleneckStage = 'webhook_delivery';
    } else if (metrics.databaseLookupTime > thresholds.databaseWarning) {
      metrics.bottleneckStage = 'database_lookup';
    } else if (metrics.transformationTime > 30) {
      metrics.bottleneckStage = 'data_transformation';
    } else if (metrics.jsonParsingTime > thresholds.jsonParsingWarning) {
      metrics.bottleneckStage = 'json_parsing';
    } else {
      metrics.bottleneckStage = null;
    }
  }

  /**
   * Check for performance alerts
   */
  private checkAlerts(metrics: DetailedPerformanceMetrics): void {
    const alerts: PerformanceAlert[] = [];

    // Latency alerts
    if (metrics.totalProcessingTime > this.thresholds.totalCritical) {
      alerts.push(this.createAlert('critical', 'latency', 
        `Critical latency: ${metrics.totalProcessingTime.toFixed(2)}ms total processing time`, metrics));
    } else if (metrics.totalProcessingTime > this.thresholds.totalWarning) {
      alerts.push(this.createAlert('high', 'latency', 
        `High latency: ${metrics.totalProcessingTime.toFixed(2)}ms total processing time`, metrics));
    }

    // Webhook delivery alerts
    if (metrics.webhookDeliveryTime > this.thresholds.webhookCritical) {
      alerts.push(this.createAlert('critical', 'latency', 
        `Critical webhook latency: ${metrics.webhookDeliveryTime.toFixed(2)}ms`, metrics));
    }

    // Memory alerts
    if (metrics.memoryUsage > this.thresholds.memoryCritical) {
      alerts.push(this.createAlert('critical', 'memory', 
        `Critical memory usage: ${metrics.memoryUsage.toFixed(2)}MB`, metrics));
    } else if (metrics.memoryUsage > this.thresholds.memoryWarning) {
      alerts.push(this.createAlert('medium', 'memory', 
        `High memory usage: ${metrics.memoryUsage.toFixed(2)}MB`, metrics));
    }

    // Error rate alerts
    if (metrics.errorCount > 0) {
      alerts.push(this.createAlert('medium', 'error_rate', 
        `Errors detected: ${metrics.errorCount} errors, types: ${metrics.errorTypes.join(', ')}`, metrics));
    }

    // Add alerts to history
    this.alerts.push(...alerts);
    
    // Trim alerts history
    if (this.alerts.length > this.maxAlertsHistory) {
      this.alerts = this.alerts.slice(-this.maxAlertsHistory);
    }

    // Log critical alerts immediately
    alerts.forEach(alert => {
      if (alert.severity === 'critical') {
        console.error(`[PERF-ALERT] ${alert.message}`, alert.metrics);
      } else if (alert.severity === 'high') {
        console.warn(`[PERF-ALERT] ${alert.message}`);
      }
    });
  }

  /**
   * Create performance alert
   */
  private createAlert(severity: PerformanceAlert['severity'], type: PerformanceAlert['type'], 
                     message: string, metrics: any): PerformanceAlert {
    return {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      severity,
      type,
      message,
      metrics,
      resolved: false
    };
  }

  /**
   * Log performance summary
   */
  private logPerformanceSummary(metrics: DetailedPerformanceMetrics): void {
    const summary = {
      total: `${metrics.totalProcessingTime.toFixed(2)}ms`,
      breakdown: {
        json: `${metrics.jsonParsingTime.toFixed(2)}ms`,
        db: `${metrics.databaseLookupTime.toFixed(2)}ms`,
        webhook: `${metrics.webhookDeliveryTime.toFixed(2)}ms`
      },
      cache: `${metrics.cacheHitRate.toFixed(1)}%`,
      memory: `${metrics.memoryUsage.toFixed(1)}MB`,
      bottleneck: metrics.bottleneckStage || 'none',
      slow: metrics.isSlowRequest
    };

    if (metrics.isSlowRequest) {
      console.warn(`[PERF-MONITOR] Slow request detected:`, summary);
    } else {
      console.log(`[PERF-MONITOR] Performance:`, summary);
    }
  }

  /**
   * Get real-time performance statistics
   */
  getRealTimeStats(): any {
    if (this.metrics.length === 0) {
      return null;
    }

    const recent = this.metrics.slice(-100); // Last 100 requests
    const now = Date.now();
    const last5min = recent.filter(m => 
      now - new Date(m.signalReceivedAt).getTime() < 5 * 60 * 1000
    );

    return {
      current: {
        totalRequests: recent.length,
        last5MinRequests: last5min.length,
        avgProcessingTime: this.average(recent.map(m => m.totalProcessingTime)),
        avgWebhookTime: this.average(recent.map(m => m.webhookDeliveryTime)),
        avgCacheHitRate: this.average(recent.map(m => m.cacheHitRate)),
        errorRate: (recent.filter(m => m.errorCount > 0).length / recent.length) * 100,
        slowRequestRate: (recent.filter(m => m.isSlowRequest).length / recent.length) * 100
      },
      breakdown: {
        jsonParsing: this.average(recent.map(m => m.jsonParsingTime)),
        databaseLookup: this.average(recent.map(m => m.databaseLookupTime)),
        transformation: this.average(recent.map(m => m.transformationTime)),
        webhookDelivery: this.average(recent.map(m => m.webhookDeliveryTime))
      },
      bottlenecks: this.getBottleneckStats(recent),
      alerts: {
        active: this.alerts.filter(a => !a.resolved).length,
        critical: this.alerts.filter(a => !a.resolved && a.severity === 'critical').length,
        recent: this.alerts.slice(-10)
      }
    };
  }

  /**
   * Get bottleneck statistics
   */
  private getBottleneckStats(metrics: DetailedPerformanceMetrics[]): any {
    const bottlenecks = metrics.filter(m => m.bottleneckStage).map(m => m.bottleneckStage);
    const counts = bottlenecks.reduce((acc, stage) => {
      acc[stage!] = (acc[stage!] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(counts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([stage, count]) => ({ stage, count, percentage: (count / metrics.length) * 100 }));
  }

  /**
   * Calculate average
   */
  private average(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
  }

  /**
   * Get performance dashboard data
   */
  getDashboardData(): any {
    return {
      realTimeStats: this.getRealTimeStats(),
      recentMetrics: this.metrics.slice(-50),
      activeAlerts: this.alerts.filter(a => !a.resolved),
      thresholds: this.thresholds,
      systemHealth: this.getSystemHealth()
    };
  }

  /**
   * Get system health status
   */
  private getSystemHealth(): any {
    const stats = this.getRealTimeStats();
    if (!stats) return { status: 'unknown' };

    const { current } = stats;
    let status = 'healthy';
    let issues: string[] = [];

    if (current.avgProcessingTime > this.thresholds.totalWarning) {
      status = 'degraded';
      issues.push('High processing time');
    }

    if (current.errorRate > this.thresholds.errorRateWarning) {
      status = 'degraded';
      issues.push('High error rate');
    }

    if (current.slowRequestRate > 20) {
      status = 'degraded';
      issues.push('High slow request rate');
    }

    if (current.avgProcessingTime > this.thresholds.totalCritical || current.errorRate > this.thresholds.errorRateCritical) {
      status = 'unhealthy';
    }

    return {
      status,
      issues,
      score: Math.max(0, 100 - (current.avgProcessingTime / 10) - (current.errorRate * 5) - (current.slowRequestRate * 2))
    };
  }

  /**
   * Clear old metrics and alerts
   */
  cleanup(): void {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    
    this.metrics = this.metrics.filter(m => 
      new Date(m.signalReceivedAt).getTime() > cutoff
    );
    
    this.alerts = this.alerts.filter(a => 
      new Date(a.timestamp).getTime() > cutoff
    );
    
    console.log(`[PERF-MONITOR] Cleanup completed. Metrics: ${this.metrics.length}, Alerts: ${this.alerts.length}`);
  }
}
