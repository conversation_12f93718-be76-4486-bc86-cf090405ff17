import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  Text,
  Badge,
  Icon,
  Card,
  CardBody,
  useColorModeValue,
  Divider,
  Box,
  Flex,
  Stat,
  StatNumber,
  StatLabel
} from '@chakra-ui/react';
import { 
  FiBell, 
  FiUsers, 
  FiSend,
  FiInfo,
  FiAlertTriangle,
  FiAlertCircle,
  FiCheck,
  FiUser,
  FiClock
} from 'react-icons/fi';

interface NotificationPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  previewData: {
    title: string;
    message: string;
    target_audience: 'all' | 'solo_users' | 'bro_sellers' | 'bro_subscribers' | 'individual';
    severity: 'info' | 'warning' | 'error' | 'success';
    targetCount: number;
    recipientName?: string; // Individual user için
    expires_at?: string;
    action_url?: string;
    action_label?: string;
  };
  isLoading?: boolean;
}

const NotificationPreviewModal: React.FC<NotificationPreviewModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  previewData,
  isLoading = false
}) => {
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const previewBg = useColorModeValue('gray.50', 'gray.700');

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'info': return 'blue';
      case 'warning': return 'orange';
      case 'error': return 'red';
      case 'success': return 'green';
      default: return 'gray';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'info': return FiInfo;
      case 'warning': return FiAlertTriangle;
      case 'error': return FiAlertCircle;
      case 'success': return FiCheck;
      default: return FiBell;
    }
  };

  const getAudienceLabel = (audience: string) => {
    switch (audience) {
      case 'all': return 'Tüm Kullanıcılar';
      case 'solo_users': return 'Solo Kullanıcılar';
      case 'bro_sellers': return 'Bro Satıcılar';
      case 'bro_subscribers': return 'Bro Aboneler';
      case 'individual': return 'Bireysel Kullanıcı';
      default: return 'Bilinmeyen';
    }
  };

  const getAudienceIcon = (audience: string) => {
    switch (audience) {
      case 'individual': return FiUser;
      default: return FiUsers;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay backdropFilter="blur(10px)" />
      <ModalContent>
        <ModalHeader>
          <HStack>
            <Icon as={FiBell} color="blue.500" />
            <Text>Bildirim Önizleme</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={6} align="stretch">
            {/* Bildirim Önizleme */}
            <Card bg={previewBg} borderColor={borderColor}>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <Flex justify="space-between" align="center">
                    <HStack>
                      <Icon 
                        as={getSeverityIcon(previewData.severity)} 
                        color={`${getSeverityColor(previewData.severity)}.500`}
                        boxSize={5}
                      />
                      <Text fontWeight="bold" fontSize="lg">
                        {previewData.title}
                      </Text>
                    </HStack>
                    <Badge 
                      colorScheme={getSeverityColor(previewData.severity)}
                      variant="subtle"
                      textTransform="capitalize"
                    >
                      {previewData.severity}
                    </Badge>
                  </Flex>
                  
                  <Text color="gray.600" lineHeight="1.6">
                    {previewData.message}
                  </Text>
                  
                  {previewData.action_url && previewData.action_label && (
                    <Box pt={2}>
                      <Button 
                        size="sm" 
                        colorScheme={getSeverityColor(previewData.severity)}
                        variant="outline"
                      >
                        {previewData.action_label}
                      </Button>
                    </Box>
                  )}
                  
                  {previewData.expires_at && (
                    <HStack fontSize="sm" color="gray.500">
                      <Icon as={FiClock} />
                      <Text>
                        Son geçerlilik: {new Date(previewData.expires_at).toLocaleString('tr-TR')}
                      </Text>
                    </HStack>
                  )}
                </VStack>
              </CardBody>
            </Card>

            <Divider />

            {/* Hedef Bilgileri */}
            <Card bg={cardBg} borderColor={borderColor}>
              <CardBody>
                <VStack spacing={4}>
                  <Flex w="full" justify="space-between" align="center">
                    <Text fontWeight="semibold" fontSize="md">
                      Gönderim Detayları
                    </Text>
                    <Badge colorScheme="blue" variant="subtle">
                      Gönderilmeye Hazır
                    </Badge>
                  </Flex>
                  
                  <HStack w="full" spacing={8}>
                    <Stat>
                      <HStack>
                        <Icon 
                          as={getAudienceIcon(previewData.target_audience)} 
                          color="blue.500" 
                          boxSize={4} 
                        />
                        <StatLabel>Hedef Kitle</StatLabel>
                      </HStack>
                      <StatNumber fontSize="lg" color="blue.500">
                        {getAudienceLabel(previewData.target_audience)}
                      </StatNumber>
                      {previewData.recipientName && (
                        <Text fontSize="sm" color="gray.600">
                          {previewData.recipientName}
                        </Text>
                      )}
                    </Stat>

                    <Stat>
                      <HStack>
                        <Icon as={FiSend} color="green.500" boxSize={4} />
                        <StatLabel>Alıcı Sayısı</StatLabel>
                      </HStack>
                      <StatNumber fontSize="lg" color="green.500">
                        {previewData.targetCount}
                      </StatNumber>
                    </Stat>
                  </HStack>
                </VStack>
              </CardBody>
            </Card>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button 
              variant="ghost" 
              onClick={onClose}
              disabled={isLoading}
            >
              İptal Et
            </Button>
            <Button 
              colorScheme="blue" 
              leftIcon={<Icon as={FiSend} />}
              onClick={onConfirm}
              isLoading={isLoading}
              loadingText="Gönderiliyor..."
              bgGradient="linear(to-r, blue.400, blue.600)"
              _hover={{
                bgGradient: "linear(to-r, blue.500, blue.700)",
              }}
            >
              Bildirimi Gönder
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default NotificationPreviewModal; 