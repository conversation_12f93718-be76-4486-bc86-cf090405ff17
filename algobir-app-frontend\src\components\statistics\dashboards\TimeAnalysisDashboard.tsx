import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  Button,
  ButtonGroup,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,


  Icon,

} from '@chakra-ui/react';
import {
  ResponsiveContainer,

  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,

} from 'recharts';
import {
  FiClock,
  FiCalendar,

  FiTrendingUp,

} from 'react-icons/fi';
import { EnhancedStatsData } from '../../../hooks/useEnhancedStatistics';
import AdvancedLineChart from '../charts/AdvancedLineChart';
import HeatmapChart from '../charts/HeatmapChart';

interface TimeAnalysisDashboardProps {
  stats: EnhancedStatsData;
}

const TimeAnalysisDashboard: React.FC<TimeAnalysisDashboardProps> = ({ stats }) => {
  const [timeframe, setTimeframe] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [viewMode, setViewMode] = useState<'pnl' | 'trades'>('pnl');
  
  const cardBg = useColorModeValue('white', 'gray.700');
  const accentColor = useColorModeValue('#4299E1', '#63B3ED');
  
  // Format functions
  const formatCurrency = (value: number) => `₺${value.toFixed(2)}`;
  const formatPercent = (value: number) => `${value.toFixed(2)}%`;

  // Prepare hourly performance data for heatmap
  const hourlyHeatmapData = useMemo(() => {
    const hourlyData = stats.hourlyPerformance || [];
    return hourlyData.map(item => ({
      x: item.hour,
      y: viewMode === 'pnl' ? 'P&L' : 'İşlem',
      value: viewMode === 'pnl' ? item.pnl : item.trades,
      label: viewMode === 'pnl' 
        ? `Saat ${item.hour}: ${formatCurrency(item.pnl)}`
        : `Saat ${item.hour}: ${item.trades} işlem`
    }));
  }, [stats.hourlyPerformance, viewMode]);

  // Prepare daily performance data
  const dailyPerformanceData = useMemo(() => {
    const dailyData = stats.dailyPerformance || [];
    return dailyData.map(item => ({
      date: item.day,
      value: viewMode === 'pnl' ? item.pnl : item.trades,
      pnl: item.pnl,
      trades: item.trades
    }));
  }, [stats.dailyPerformance, viewMode]);

  // Prepare weekly performance data
  const weeklyPerformanceData = useMemo(() => {
    const weeklyData = stats.weeklyPerformance || [];
    return weeklyData.map(item => ({
      date: item.week,
      value: viewMode === 'pnl' ? item.pnl : item.trades,
      pnl: item.pnl,
      trades: item.trades
    }));
  }, [stats.weeklyPerformance, viewMode]);

  // Prepare monthly performance data
  const monthlyPerformanceData = useMemo(() => {
    const monthlyData = stats.monthlyPerformance || [];
    return monthlyData.map(item => ({
      date: item.month,
      value: viewMode === 'pnl' ? item.pnl : item.trades,
      pnl: item.pnl,
      trades: item.trades
    }));
  }, [stats.monthlyPerformance, viewMode]);

  // Get current performance data based on timeframe
  const currentPerformanceData = useMemo(() => {
    switch (timeframe) {
      case 'daily':
        return dailyPerformanceData;
      case 'weekly':
        return weeklyPerformanceData;
      case 'monthly':
        return monthlyPerformanceData;
      default:
        return dailyPerformanceData;
    }
  }, [timeframe, dailyPerformanceData, weeklyPerformanceData, monthlyPerformanceData]);

  // Calculate time-based statistics
  const timeStats = useMemo(() => {
    // Best and worst hours
    const bestHour = hourlyHeatmapData.length > 0
      ? hourlyHeatmapData.reduce((best, current) => 
          current.value > best.value ? current : best, hourlyHeatmapData[0]).x
      : 'N/A';
      
    const worstHour = hourlyHeatmapData.length > 0
      ? hourlyHeatmapData.reduce((worst, current) => 
          current.value < worst.value ? current : worst, hourlyHeatmapData[0]).x
      : 'N/A';

    // Trading days stats
    const totalTradingDays = dailyPerformanceData.length;
    const profitableDays = dailyPerformanceData.filter(day => day.pnl > 0).length;
    const profitableDaysPercent = totalTradingDays > 0 
      ? (profitableDays / totalTradingDays) * 100 
      : 0;

    // Best and worst days of week
    const dayOfWeekPerformance = [
      { day: 'Pazartesi', pnl: 0, trades: 0 },
      { day: 'Salı', pnl: 0, trades: 0 },
      { day: 'Çarşamba', pnl: 0, trades: 0 },
      { day: 'Perşembe', pnl: 0, trades: 0 },
      { day: 'Cuma', pnl: 0, trades: 0 }
    ];

    // Calculate day of week performance (simplified)
    // In a real implementation, you would parse the date and determine the day of week
    
    // Find best and worst days
    const bestDay = dayOfWeekPerformance.reduce((best, current) => 
      current.pnl > best.pnl ? current : best, dayOfWeekPerformance[0]).day;
      
    const worstDay = dayOfWeekPerformance.reduce((worst, current) => 
      current.pnl < worst.pnl ? current : worst, dayOfWeekPerformance[0]).day;

    return {
      bestHour,
      worstHour,
      totalTradingDays,
      profitableDays,
      profitableDaysPercent,
      bestDay,
      worstDay
    };
  }, [hourlyHeatmapData, dailyPerformanceData]);

  // Trading session analysis
  const tradingSessionData = useMemo(() => {
    // Define sessions
    const sessions = [
      { name: 'Sabah (09:00-12:00)', value: 0, color: '#4299E1', trades: 0 },
      { name: 'Öğle (12:00-15:00)', value: 0, color: '#48BB78', trades: 0 },
      { name: 'Akşam (15:00-18:00)', value: 0, color: '#ED8936', trades: 0 },
      { name: 'Gece (18:00-21:00)', value: 0, color: '#9F7AEA', trades: 0 }
    ];

    // Calculate session performance from hourly data
    const hourlyData = stats.hourlyPerformance || [];
    hourlyData.forEach(hour => {
      if (hour.hour >= 9 && hour.hour < 12) {
        sessions[0].value += hour.pnl;
        sessions[0].trades += hour.trades;
      } else if (hour.hour >= 12 && hour.hour < 15) {
        sessions[1].value += hour.pnl;
        sessions[1].trades += hour.trades;
      } else if (hour.hour >= 15 && hour.hour < 18) {
        sessions[2].value += hour.pnl;
        sessions[2].trades += hour.trades;
      } else if (hour.hour >= 18 && hour.hour < 21) {
        sessions[3].value += hour.pnl;
        sessions[3].trades += hour.trades;
      }
    });

    return sessions;
  }, [stats.hourlyPerformance]);

  return (
    <VStack spacing={6} align="stretch">
      <HStack justify="space-between" align="center">
        <Heading size="lg">Zaman Analizi</Heading>
        <HStack spacing={4}>
          <ButtonGroup size="sm" isAttached variant="outline">
            <Button
              isActive={viewMode === 'pnl'}
              onClick={() => setViewMode('pnl')}
            >
              P&L
            </Button>
            <Button
              isActive={viewMode === 'trades'}
              onClick={() => setViewMode('trades')}
            >
              İşlem Sayısı
            </Button>
          </ButtonGroup>
        </HStack>
      </HStack>

      {/* Time Statistics Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
        <Card bg={cardBg} borderRadius="16px" boxShadow="md">
          <CardBody>
            <Stat>
              <HStack spacing={3} mb={2}>
                <Icon as={FiClock} color="green.500" boxSize={5} />
                <StatLabel fontSize="sm" fontWeight="medium">En İyi Saat</StatLabel>
              </HStack>
              <StatNumber fontSize="2xl" color="green.500" mb={1}>
                {timeStats.bestHour !== 'N/A' ? `${timeStats.bestHour}:00` : 'N/A'}
              </StatNumber>
              <StatHelpText fontSize="xs" mb={2}>
                En karlı işlem saati
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card bg={cardBg} borderRadius="16px" boxShadow="md">
          <CardBody>
            <Stat>
              <HStack spacing={3} mb={2}>
                <Icon as={FiClock} color="red.500" boxSize={5} />
                <StatLabel fontSize="sm" fontWeight="medium">En Kötü Saat</StatLabel>
              </HStack>
              <StatNumber fontSize="2xl" color="red.500" mb={1}>
                {timeStats.worstHour !== 'N/A' ? `${timeStats.worstHour}:00` : 'N/A'}
              </StatNumber>
              <StatHelpText fontSize="xs" mb={2}>
                En zararlı işlem saati
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card bg={cardBg} borderRadius="16px" boxShadow="md">
          <CardBody>
            <Stat>
              <HStack spacing={3} mb={2}>
                <Icon as={FiCalendar} color="blue.500" boxSize={5} />
                <StatLabel fontSize="sm" fontWeight="medium">Toplam Gün</StatLabel>
              </HStack>
              <StatNumber fontSize="2xl" color="blue.500" mb={1}>
                {timeStats.totalTradingDays}
              </StatNumber>
              <StatHelpText fontSize="xs" mb={2}>
                İşlem yapılan gün sayısı
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card bg={cardBg} borderRadius="16px" boxShadow="md">
          <CardBody>
            <Stat>
              <HStack spacing={3} mb={2}>
                <Icon as={FiTrendingUp} color="green.500" boxSize={5} />
                <StatLabel fontSize="sm" fontWeight="medium">Karlı Günler</StatLabel>
              </HStack>
              <StatNumber fontSize="2xl" color="green.500" mb={1}>
                {timeStats.profitableDays}
              </StatNumber>
              <StatHelpText fontSize="xs" mb={2}>
                {formatPercent(timeStats.profitableDaysPercent)} başarı oranı
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Hourly Performance Heatmap */}
      <Card bg={cardBg} borderRadius="16px" boxShadow="md">
        <CardHeader>
          <Heading size="md">Saatlik Performans Haritası</Heading>
        </CardHeader>
        <CardBody>
          {hourlyHeatmapData.length > 0 ? (
            <HeatmapChart
              data={hourlyHeatmapData}
              title=""
              xAxisLabel="Saat"
              yAxisLabel={viewMode === 'pnl' ? 'P&L' : 'İşlem Sayısı'}
              colorScheme="blue"
              formatValue={viewMode === 'pnl' 
                ? (value) => formatCurrency(value)
                : (value) => `${value} işlem`
              }
              height={200}
            />
          ) : (
            <Box p={8} textAlign="center">
              <Text color="gray.500">Saatlik performans verisi bulunamadı</Text>
            </Box>
          )}
        </CardBody>
      </Card>

      {/* Time Period Performance */}
      <Card bg={cardBg} borderRadius="16px" boxShadow="md">
        <CardHeader>
          <HStack justify="space-between">
            <Heading size="md">Zaman Periyodu Performansı</Heading>
            <ButtonGroup size="sm" isAttached variant="outline">
              <Button
                isActive={timeframe === 'daily'}
                onClick={() => setTimeframe('daily')}
              >
                Günlük
              </Button>
              <Button
                isActive={timeframe === 'weekly'}
                onClick={() => setTimeframe('weekly')}
              >
                Haftalık
              </Button>
              <Button
                isActive={timeframe === 'monthly'}
                onClick={() => setTimeframe('monthly')}
              >
                Aylık
              </Button>
            </ButtonGroup>
          </HStack>
        </CardHeader>
        <CardBody>
          {currentPerformanceData.length > 0 ? (
            <AdvancedLineChart
              data={currentPerformanceData}
              title=""
              dataKey="value"
              formatValue={viewMode === 'pnl' ? formatCurrency : (value) => `${value} işlem`}
              height={350}
              showBrush={true}
              showMovingAverage={true}
              color={accentColor}
            />
          ) : (
            <Box p={8} textAlign="center">
              <Text color="gray.500">
                {timeframe === 'daily' ? 'Günlük' : 
                 timeframe === 'weekly' ? 'Haftalık' : 'Aylık'} performans verisi bulunamadı
              </Text>
            </Box>
          )}
        </CardBody>
      </Card>

      {/* Trading Session Analysis */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        <Card bg={cardBg} borderRadius="16px" boxShadow="md">
          <CardHeader>
            <Heading size="md">Seans Analizi (P&L)</Heading>
          </CardHeader>
          <CardBody>
            <Box h="350px">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={tradingSessionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip 
                    formatter={(value: number) => [formatCurrency(value), 'P&L']}
                  />
                  <Bar dataKey="value" fill={accentColor} />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          </CardBody>
        </Card>

        <Card bg={cardBg} borderRadius="16px" boxShadow="md">
          <CardHeader>
            <Heading size="md">Seans Analizi (İşlem Sayısı)</Heading>
          </CardHeader>
          <CardBody>
            <Box h="350px">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={tradingSessionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip 
                    formatter={(value: number) => [`${value} işlem`, 'İşlem Sayısı']}
                  />
                  <Bar dataKey="trades" fill="#48BB78" />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          </CardBody>
        </Card>
      </SimpleGrid>
    </VStack>
  );
};

export default TimeAnalysisDashboard;
