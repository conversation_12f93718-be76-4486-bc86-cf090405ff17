/**
 * Test Authentication Utilities
 * 
 * This file provides utilities for testing authenticated routes
 * in development and testing environments.
 */

export const createTestSession = () => {
  const testUser = {
    id: '69742a2d-1e70-4ac5-9d42-b21885aaa623',
    email: '<EMAIL>',
    user_metadata: {
      full_name: 'Test User'
    },
    app_metadata: {},
    aud: 'authenticated',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const testSession = {
    access_token: 'test-access-token',
    refresh_token: 'test-refresh-token',
    expires_in: 3600,
    expires_at: Math.floor(Date.now() / 1000) + 3600,
    token_type: 'bearer',
    user: testUser
  };

  return { user: testUser, session: testSession };
};

export const setTestAuthInLocalStorage = () => {
  const { user, session } = createTestSession();
  
  // Store in localStorage (similar to how Supabase stores auth data)
  localStorage.setItem('supabase.auth.token', JSON.stringify(session));
  localStorage.setItem('supabase.auth.user', JSON.stringify(user));
  
  return { user, session };
};

export const clearTestAuth = () => {
  localStorage.removeItem('supabase.auth.token');
  localStorage.removeItem('supabase.auth.user');
};

export const isTestEnvironment = () => {
  return process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';
};
