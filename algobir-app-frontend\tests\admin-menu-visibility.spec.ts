import { test, expect } from '@playwright/test';

test.describe('Admin Menu Visibility Tests', () => {
  test('should show admin menu structure when admin privileges are available', async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');

    // Wait a bit more for React components to render
    await page.waitForTimeout(2000);

    // Check if we can access the sidebar - try multiple selectors
    let sidebar = page.locator('[data-testid="sidebar"]');
    let sidebarVisible = await sidebar.isVisible();

    if (!sidebarVisible) {
      // Try alternative selectors
      sidebar = page.locator('[role="navigation"]');
      sidebarVisible = await sidebar.isVisible();
    }

    if (!sidebarVisible) {
      // Try looking for any sidebar-like element
      sidebar = page.locator('nav, [aria-label*="navigasyon"], [aria-label*="navigation"]');
      sidebarVisible = await sidebar.isVisible();
    }

    console.log('Sidebar visible:', sidebarVisible);

    if (!sidebarVisible) {
      console.log('❌ Sidebar not found - checking page content');
      const pageContent = await page.textContent('body');
      console.log('Page contains login form:', pageContent?.includes('login') || pageContent?.includes('giriş'));
      return;
    }

    // Look for admin menu - it may not be visible for non-admin users
    const adminMenu = sidebar.locator('text=Admin Paneli');
    const isAdminMenuVisible = await adminMenu.isVisible();

    if (isAdminMenuVisible) {
      console.log('✅ Admin menu is visible - testing submenu functionality');
      
      // Click on admin menu to open submenu
      await adminMenu.click();
      await page.waitForTimeout(500);

      // Check for submenu panel
      const submenuPanel = page.locator('[role="dialog"][aria-label*="Admin Paneli"]');
      await expect(submenuPanel).toBeVisible();

      // Verify all required submenu items (including new Dashboard item)
      const expectedItems = [
        { name: 'Ana Sayfa', path: '/admin' },
        { name: 'Kullanıcı Yönetimi', path: '/admin/users' },
        { name: 'İşlem Yönetimi', path: '/admin/trades' },
        { name: 'Bildirim Yönetimi', path: '/admin/notifications' },
        { name: 'Sistem İstatistikleri', path: '/admin/statistics' },
        { name: 'Sistem Durumu', path: '/admin/status' }
      ];

      for (const item of expectedItems) {
        const submenuItem = submenuPanel.locator(`text=${item.name}`);
        await expect(submenuItem).toBeVisible();
        console.log(`✅ Found submenu item: ${item.name}`);
      }

      // Test navigation to Dashboard (first submenu item)
      const dashboardLink = submenuPanel.locator('text=Ana Sayfa');
      await dashboardLink.click();

      // Wait for navigation and verify URL
      await page.waitForURL('**/admin', { timeout: 5000 });
      expect(page.url()).toMatch(/.*\/admin\/?$/); // Should end with /admin or /admin/
      console.log('✅ Navigation to admin dashboard successful');

      // Go back and test another navigation item
      await page.goBack();
      await page.waitForTimeout(500);

      // Test navigation to User Management
      await adminMenu.click();
      await page.waitForTimeout(500);

      const userManagementLink = submenuPanel.locator('text=Kullanıcı Yönetimi');
      await userManagementLink.click();

      // Wait for navigation and verify URL
      await page.waitForURL('**/admin/users', { timeout: 5000 });
      expect(page.url()).toContain('/admin/users');
      console.log('✅ Navigation to admin/users successful');

    } else {
      console.log('ℹ️ Admin menu not visible - user does not have admin privileges');
      console.log('This is expected behavior for non-admin users');
      
      // Verify that admin routes are protected
      await page.goto('http://localhost:3001/admin');
      
      // Should either redirect or show access denied
      const currentUrl = page.url();
      if (!currentUrl.includes('/admin')) {
        console.log('✅ Admin route protection working - redirected to:', currentUrl);
      } else {
        console.log('⚠️ Admin route accessible without admin privileges');
      }
    }
  });

  test('should have proper admin menu structure in routes configuration', async ({ page }) => {
    // This test verifies the admin menu structure by checking the DOM
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');

    // Check if the sidebar routes are properly configured
    const sidebar = page.locator('[data-testid="sidebar"]');
    
    // Look for any admin-related elements in the sidebar
    const adminElements = await sidebar.locator('*').allTextContents();
    
    console.log('Sidebar elements found:', adminElements.filter(text => 
      text.includes('Admin') || text.includes('Yönetim') || text.includes('admin')
    ));

    // Verify that the admin menu has the correct icon and structure
    const adminMenuWithIcon = sidebar.locator('[data-testid*="admin"], [aria-label*="admin"], [aria-label*="Admin"]');
    const adminMenuCount = await adminMenuWithIcon.count();
    
    console.log(`Found ${adminMenuCount} admin-related elements in sidebar`);
  });

  test('should handle admin menu in collapsed sidebar state', async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');

    const sidebar = page.locator('[data-testid="sidebar"]');
    
    // Test sidebar hover behavior
    await page.mouse.move(30, 300); // Move to left edge
    await page.waitForTimeout(200);
    
    // Check if admin menu is accessible in expanded state
    const adminMenu = sidebar.locator('text=Admin Paneli');
    const isVisible = await adminMenu.isVisible();
    
    if (isVisible) {
      console.log('✅ Admin menu accessible in expanded sidebar');
      
      // Move mouse away to collapse
      await page.mouse.move(500, 300);
      await page.waitForTimeout(1000);
      
      // Admin menu should still be accessible (as icon or in collapsed state)
      const sidebarStillVisible = await sidebar.isVisible();
      expect(sidebarStillVisible).toBe(true);
      console.log('✅ Sidebar remains functional in collapsed state');
    }
  });
});
