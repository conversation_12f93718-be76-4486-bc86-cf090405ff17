import { ReactNode, useEffect } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { Spinner, Center, Text, VStack, Alert, AlertIcon, Box } from '@chakra-ui/react';
import { useAdminAuth } from '../hooks/useAdminAuth';
import { useAuth } from '../context/AuthContext';
import { useToastHelper } from '../components/ToastHelper';

interface AdminRouteProps {
  children?: ReactNode;
}

const AdminRoute = ({ children }: AdminRouteProps) => {
  const { isAdmin, loading: adminLoading, error } = useAdminAuth();
  const { session, loading: sessionLoading } = useAuth();
  const toast = useToastHelper();
  const location = useLocation();
  
  // Hata durumunu izle ve kullanıcıya bildir
  useEffect(() => {
    if (error) {
      console.error('Admin yetkisi kontrol edilirken hata oluştu:', error);
      toast.showErrorToast(
        'Yetki hatası', 
        'Admin yetkiniz doğrulanamadı. Bu bir geçici RLS veya erişim hatası olabilir.'
      );
    }
  }, [error, toast]);
  
  // Yükleme sırasında loading göster
  if (sessionLoading || adminLoading) {
    return (
      <Center minH="100vh">
        <VStack spacing={4}>
          <Spinner size="xl" color="brand.500" thickness="4px" />
          <Text>Yetki kontrol ediliyor...</Text>
        </VStack>
      </Center>
    );
  }
  
  // Oturum yoksa login sayfasına yönlendir
  if (!session) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Hata durumunda ana sayfaya yönlendir ve hata göster
  if (error) {
    return (
      <Center minH="100vh">
        <VStack spacing={4} maxW="500px" p={4}>
          <Alert status="error" borderRadius="md">
            <AlertIcon />
            <Box>
              <Text fontWeight="bold">Yetki hatası</Text>
              <Text>Admin paneline erişim sağlanamadı. Bu bir geçici RLS veya erişim hatası olabilir.</Text>
            </Box>
          </Alert>
          <Box pt={2}>
            <Navigate to="/" replace />
          </Box>
        </VStack>
      </Center>
    );
  }

  // Admin değilse ana sayfaya yönlendir
  if (!isAdmin) {
    toast.showWarningToast('Yetkisiz erişim', 'Admin paneline erişim yetkiniz bulunmamaktadır.');
    return <Navigate to="/" replace />;
  }
  
  // Admin ise children'ı göster ya da Outlet'i render et
  return children ? <>{children}</> : <Outlet />;
};

export default AdminRoute; 