# Algobir Developer Onboarding Guide

## 🎯 Welcome to Algobir

Welcome to the Algobir development team! This guide will help you get up to speed with our algorithmic trading platform, understand our codebase, development practices, and become a productive team member.

## 📚 Essential Reading

### 1. Core Documentation (Read First)
- **[Technical Architecture](./TECHNICAL_ARCHITECTURE.md)**: Complete system overview
- **[API Reference](./API_REFERENCE.md)**: Comprehensive API documentation
- **[Developer Guide](./DEVELOPER_GUIDE.md)**: Development practices and patterns

### 2. Architectural Decision Records (ADRs)
- **[ADR-001: Technology Stack Selection](./adr/ADR-001-TECHNOLOGY_STACK_SELECTION.md)**
- **[ADR-002: Database Schema Design](./adr/ADR-002-DATABASE_SCHEMA_DESIGN.md)**
- **[ADR-003: Authentication & Authorization Strategy](./adr/ADR-003-AUTHENTICATION_AUTHORIZATION_STRATEGY.md)**

### 3. Project Context
- **[Project Core Rules](./.augment/rules/imported/rules/core/PROJECT_CORE.mdc)**: Business model and core features
- **[Frontend Guidelines](./.augment/rules/imported/rules/frontend/MAIN_APP.mdc)**: UI/UX patterns and components

## 🚀 Quick Start Checklist

### Day 1: Environment Setup
- [ ] **Clone Repository**: Get access to the main repository
- [ ] **Install Dependencies**: Set up Node.js, npm, and required tools
- [ ] **Environment Configuration**: Configure local development environment
- [ ] **Run Application**: Successfully start the development server
- [ ] **Access Documentation**: Bookmark all essential documentation links
- [ ] **Team Introductions**: Meet your team members and stakeholders

### Day 2-3: Codebase Exploration
- [ ] **Project Structure**: Understand the folder organization and file structure
- [ ] **Core Components**: Explore main UI components and their relationships
- [ ] **API Integration**: Understand how frontend communicates with backend
- [ ] **Database Schema**: Review database tables and relationships
- [ ] **Authentication Flow**: Understand user authentication and authorization
- [ ] **Trading Logic**: Explore Solo-Robot and Bro-Robot implementations

### Week 1: First Contribution
- [ ] **Pick Starter Task**: Choose a beginner-friendly issue or feature
- [ ] **Create Branch**: Follow our Git workflow and branching strategy
- [ ] **Write Tests**: Implement tests for your changes
- [ ] **Code Review**: Submit your first pull request
- [ ] **Deploy Changes**: See your code deployed to staging environment

## 🏗️ System Architecture Overview

### High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Supabase      │    │   External      │
│   React App     │◄──►│   Backend       │◄──►│   Services      │
│                 │    │                 │    │                 │
│ • Dashboard     │    │ • PostgreSQL    │    │ • TradingView   │
│ • Marketplace   │    │ • Auth          │    │ • Exchanges     │
│ • Statistics    │    │ • Edge Functions│    │ • Webhooks      │
│ • Admin Panel   │    │ • Real-time     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Components
1. **Frontend (React + TypeScript)**: User interface and client-side logic
2. **Backend (Supabase)**: Database, authentication, and serverless functions
3. **Real-time System**: WebSocket connections for live data updates
4. **Trading Engine**: Signal processing and order execution
5. **Monitoring System**: Performance tracking and error reporting

## 🔧 Development Environment

### Required Tools
```bash
# Core development tools
Node.js 18.0.0+
npm 9.0.0+
Git (latest)
VS Code (recommended)

# Optional but recommended
Docker Desktop
Postman or Insomnia (API testing)
PostgreSQL client (DBeaver, pgAdmin)
```

### VS Code Extensions
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "ms-playwright.playwright",
    "vitest.explorer",
    "chakra-ui.chakra-ui-snippets"
  ]
}
```

### Environment Setup
```bash
# 1. Clone the repository
git clone https://github.com/algobir/tobot-v2-app.git
cd tobot-v2-app

# 2. Install dependencies
cd algobir-app-frontend
npm install

# 3. Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# 4. Start development server
npm run dev

# 5. Run tests (in separate terminal)
npm run test
```

## 📖 Learning Path

### Week 1: Foundation
**Goal**: Understand the basics and make your first contribution

**Tasks**:
- Complete environment setup
- Read core documentation
- Explore codebase structure
- Understand Git workflow
- Make first small contribution (bug fix or documentation update)

**Key Concepts**:
- React functional components and hooks
- TypeScript basics and type definitions
- Chakra UI component library
- Supabase client integration
- Basic trading concepts (Solo-Robot vs Bro-Robot)

### Week 2: Frontend Development
**Goal**: Become proficient with our frontend stack

**Tasks**:
- Build a simple component from scratch
- Implement a new page or feature
- Write unit tests for your components
- Understand state management patterns
- Learn our styling and theming approach

**Key Concepts**:
- Advanced React patterns (Context, custom hooks)
- Form handling with React Hook Form
- Data visualization with Recharts
- Responsive design with Chakra UI
- Performance optimization techniques

### Week 3: Backend Integration
**Goal**: Understand backend systems and API integration

**Tasks**:
- Explore Supabase dashboard and database
- Understand Row Level Security (RLS) policies
- Work with Edge Functions
- Implement real-time features
- Learn authentication and authorization

**Key Concepts**:
- PostgreSQL and SQL queries
- Supabase client methods and APIs
- Real-time subscriptions
- JWT authentication
- Database security and RLS

### Week 4: Advanced Features
**Goal**: Work on complex features and understand business logic

**Tasks**:
- Implement trading-related features
- Work with performance monitoring
- Understand the marketplace system
- Contribute to admin panel features
- Optimize application performance

**Key Concepts**:
- Trading signal processing
- Subscription management
- Performance monitoring and analytics
- Error handling and reporting
- Security best practices

## 🧪 Testing Strategy

### Testing Pyramid
```
    ┌─────────────────┐
    │   E2E Tests     │  ← Playwright (Critical user journeys)
    │   (Playwright)  │
    ├─────────────────┤
    │ Integration     │  ← API integration, component integration
    │ Tests           │
    ├─────────────────┤
    │   Unit Tests    │  ← Vitest (Components, hooks, utilities)
    │   (Vitest)      │
    └─────────────────┘
```

### Writing Tests
```typescript
// Unit test example
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { ChakraProvider } from '@chakra-ui/react';
import TradingCard from '../TradingCard';

describe('TradingCard', () => {
  it('displays trade information correctly', () => {
    const trade = {
      symbol: 'BTCUSDT',
      side: 'buy',
      quantity: 0.001,
      price: 45000
    };
    
    render(
      <ChakraProvider>
        <TradingCard trade={trade} />
      </ChakraProvider>
    );
    
    expect(screen.getByText('BTCUSDT')).toBeInTheDocument();
    expect(screen.getByText('BUY')).toBeInTheDocument();
  });
});
```

### Running Tests
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:coverage

# Watch mode for development
npm run test:watch
```

## 🔄 Development Workflow

### Git Workflow
```bash
# 1. Create feature branch from develop
git checkout develop
git pull origin develop
git checkout -b feature/your-feature-name

# 2. Make your changes
# ... code, test, commit ...

# 3. Push and create PR
git push origin feature/your-feature-name
# Create PR via GitHub interface

# 4. After review and approval
# PR is merged to develop by maintainer
```

### Branch Naming Convention
- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `hotfix/description` - Critical production fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

### Commit Message Format
```
type(scope): description

Examples:
feat(auth): add two-factor authentication
fix(trading): resolve order execution bug
docs(api): update authentication documentation
refactor(components): simplify TradingCard component
```

## 🚀 Deployment Process

### Environments
- **Development**: Local development environment
- **Staging**: `https://algobir-staging.vercel.app`
- **Production**: `https://algobir.vercel.app`

### Deployment Pipeline
1. **Development**: Local testing and development
2. **PR Review**: Code review and automated testing
3. **Staging**: Automatic deployment on merge to `develop`
4. **Production**: Manual deployment from `main` branch

### Quality Gates
- [ ] All tests passing
- [ ] Code review approved
- [ ] No TypeScript errors
- [ ] Performance benchmarks met
- [ ] Security scan passed

## 📊 Monitoring and Debugging

### Development Tools
```typescript
// Performance monitoring in development
const { trackEvent, startTimer } = useMonitoring();

// Track user interactions
const handleButtonClick = () => {
  trackEvent('button_click', 1, { component: 'TradingCard' });
  // ... handle click
};

// Measure performance
const timer = startTimer('api_call');
const result = await fetchTrades();
timer.end();
```

### Debugging Tips
- **React DevTools**: Inspect component state and props
- **Network Tab**: Monitor API calls and responses
- **Console Logs**: Use structured logging for debugging
- **Supabase Dashboard**: Monitor database queries and performance
- **Error Boundaries**: Catch and handle React errors gracefully

## 🤝 Team Collaboration

### Communication Channels
- **Slack**: Daily communication and quick questions
- **GitHub**: Code reviews, issues, and project management
- **Weekly Standups**: Progress updates and blockers
- **Monthly Retrospectives**: Process improvements and feedback

### Code Review Guidelines
- **Be Constructive**: Provide helpful feedback and suggestions
- **Ask Questions**: If something is unclear, ask for clarification
- **Test Thoroughly**: Verify that changes work as expected
- **Consider Performance**: Look for potential performance issues
- **Security Mindset**: Consider security implications of changes

### Getting Help
1. **Documentation**: Check existing documentation first
2. **Code Examples**: Look for similar implementations in the codebase
3. **Team Members**: Ask team members for guidance
4. **GitHub Issues**: Search for related issues and discussions
5. **External Resources**: Consult official documentation for libraries

## 📚 Continuous Learning

### Recommended Resources
- **React**: [React Documentation](https://react.dev/)
- **TypeScript**: [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- **Supabase**: [Supabase Documentation](https://supabase.com/docs)
- **Trading**: [Algorithmic Trading Basics](https://www.investopedia.com/articles/active-trading/101014/basics-algorithmic-trading-concepts-and-examples.asp)

### Internal Learning
- **Code Reviews**: Learn from reviewing others' code
- **Pair Programming**: Work with senior developers
- **Tech Talks**: Attend internal technical presentations
- **Documentation**: Contribute to and improve documentation

## 🎯 Success Metrics

### 30-Day Goals
- [ ] Successfully set up development environment
- [ ] Understand core system architecture
- [ ] Make meaningful contributions to codebase
- [ ] Complete first feature implementation
- [ ] Demonstrate understanding of testing practices

### 90-Day Goals
- [ ] Independently implement complex features
- [ ] Mentor new team members
- [ ] Contribute to architectural decisions
- [ ] Improve system performance or reliability
- [ ] Lead a small project or initiative

## 📞 Support and Resources

### Key Contacts
- **Technical Lead**: Architecture and technical decisions
- **Product Manager**: Feature requirements and priorities
- **DevOps Engineer**: Deployment and infrastructure
- **QA Engineer**: Testing strategies and quality assurance

### Emergency Contacts
- **Production Issues**: On-call rotation schedule
- **Security Incidents**: Security team contact information
- **Infrastructure Problems**: DevOps team escalation

---

**Welcome to the team! We're excited to have you contribute to Algobir's success.** 🚀

*This guide is a living document. Please contribute improvements and updates as you learn and grow with the platform.*
