{"dependencies": {"@chakra-ui/icons": "^2.2.4", "@chakra-ui/react": "^2.10.7", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-table": "^8.21.3", "@types/d3": "^7.4.3", "@types/lodash": "^4.17.20", "@types/mermaid": "^9.1.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "apexcharts": "^4.7.0", "chart.js": "^4.5.0", "d3": "^7.9.0", "date-fns": "^4.1.0", "framer-motion": "^11.11.18", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "mermaid": "^11.6.0", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-custom-scrollbars-2": "^4.5.0", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-pdf": "^10.0.1", "react-router-dom": "^6.30.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "recharts": "^2.15.4", "simplebar-react": "^3.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/plugin-transform-react-constant-elements": "^7.27.1", "@babel/plugin-transform-react-inline-elements": "^7.27.1", "@playwright/test": "^1.53.2", "@tanstack/react-query": "^5.84.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/jest-axe": "^3.5.9", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@upstash/context7-mcp": "^1.0.14", "@vitejs/plugin-react": "^4.4.0", "@vitest/coverage-v8": "^3.2.4", "bundlesize": "^0.18.2", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "msw": "^2.10.4", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.12.0", "terser": "^5.40.0", "typescript": "^5.8.3", "vite": "^5.4.18", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^0.21.1", "vitest": "^3.2.4"}, "name": "algobir-app-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:optimized": "tsc && vite build --config vite.config.optimization.ts", "build:analyze": "npm run build:optimized && node scripts/build-optimizer.js", "build:production": "NODE_ENV=production npm run build:analyze", "build:staging": "NODE_ENV=staging npm run build:optimized", "preview": "vite preview", "preview:production": "vite preview --mode production", "type-check": "tsc --noEmit", "lint": "tsc --noEmit", "lint:fix": "eslint . --ext ts,tsx --fix", "postbuild": "echo 'Build completed successfully!'", "test": "vitest", "test:unit": "vitest run", "test:unit:watch": "vitest --watch", "test:unit:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:all": "npm run test:unit && npm run test:e2e", "test:ci": "npm run test:unit:coverage && npm run test:e2e", "deploy:staging": "node scripts/deploy.js staging", "deploy:production": "node scripts/deploy.js production", "health-check": "node scripts/health-check.js", "cache:clear": "node -e \"require('./.buildcache.config.js').CacheManager.clearAll()\"", "cache:clear:deps": "node -e \"require('./.buildcache.config.js').CacheManager.clear('dependencies')\"", "cache:clear:build": "node -e \"require('./.buildcache.config.js').CacheManager.clear('vite')\"", "cache:clear:assets": "node -e \"require('./.buildcache.config.js').CacheManager.clear('assets')\"", "cache:stats": "node -e \"console.log(JSON.stringify(require('./.buildcache.config.js').CacheManager.getStats(), null, 2))\"", "health-check:staging": "node scripts/health-check.js https://algobir-staging.vercel.app", "health-check:production": "node scripts/health-check.js https://algobir.vercel.app", "deploy:verify": "npm run health-check:staging && npm run health-check:production", "optimize": "node scripts/build-optimizer.js", "bundle-size": "npm run build:optimized && bundlesize", "clean": "rimraf dist build-analysis", "clean:all": "rimraf dist build-analysis node_modules/.vite"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["react", "typescript", "vite", "chakra-ui", "trading", "algorithms"], "author": "Algobir Team", "license": "MIT", "description": "Algobir - Algorithmic Trading Signal Automation Platform"}