import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { getCorsHeaders } from '../_shared/cors.ts';
import { CacheManager } from '../_shared/cache-manager.ts';
import { MemoryOptimizer } from '../_shared/memory-optimizer.ts';
// Supabase setup
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
const supabase = createClient(supabaseUrl, supabaseServiceKey);
// Signal name parsing function
function parseSignalName(signalName) {
  console.log(`[parseSignalName] Processing signal name: "${signalName}"`);
  const parts = signalName.trim().split(/\s+/);
  console.log(`[parseSignalName] Split parts:`, parts);
  let baseSystemName = 'Unknown';
  let signalType = 'UNKNOWN';
  let orderSide = 'BUY'; // Default
  let symbol;
  if (parts.length >= 2) {
    // İLK KELİME = SİSTEM ADI (örn: "Eray", "System1")
    baseSystemName = parts[0];
    // İKİNCİ KELİME = SİNYAL TİPİ (örn: "BUY", "SELL", "PTCTSELL")
    signalType = parts[1].toUpperCase();
    // Sinyal tipine göre order side belirleme
    if ([
      'SELL',
      'CLOSE',
      'EXIT',
      'PTCTSELL',
      'SİLME'
    ].includes(signalType)) {
      orderSide = 'SELL';
    } else {
      orderSide = 'BUY';
    }
  } else if (parts.length === 1) {
    // Tek kelime ise sistem adı olarak kullan, varsayılan BUY
    baseSystemName = parts[0];
    signalType = 'BUY';
    orderSide = 'BUY';
  }
  // Symbol arama (BTCUSDT, ETHUSDT, AAPL, vb.)
  for (const part of parts){
    // Crypto pairs (BTCUSDT, ETHUSDT etc.)
    if (part.match(/^[A-Z]{3,10}USDT?$/i) || part.match(/^[A-Z]{3,10}BTC$/i)) {
      symbol = part.toUpperCase();
      break;
    }
    // Traditional symbols (AAPL, GOOGL etc.)
    if (part.match(/^[A-Z]{2,5}$/i) && ![
      'BUY',
      'SELL',
      'OPEN',
      'CLOSE',
      'EXIT',
      'PTCTSELL',
      'SİLME'
    ].includes(part.toUpperCase())) {
      symbol = part.toUpperCase();
      break;
    }
  }
  const result = {
    baseSystemName,
    signalType,
    orderSide,
    symbol
  };
  console.log(`[parseSignalName] Parsed result:`, result);
  return result;
}
// Türk para formatı fonksiyonu
function formatTurkishCurrency(amount: number): string {
  return new Intl.NumberFormat('tr-TR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}
// SELL işlemleri için açık pozisyon kontrolü artık main function içinde yapılıyor
Deno.serve(async (req)=>{
  const corsHeaders = getCorsHeaders(req.headers);

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  // Performance monitoring - Start timing
  const performanceMetrics = {
    signalReceivedAt: new Date(),
    jsonParsingStartTime: 0,
    jsonParsingEndTime: 0,
    transformationStartTime: 0,
    transformationEndTime: 0,
    webhookDeliveryStartTime: 0,
    webhookDeliveryEndTime: 0,
    totalStartTime: performance.now()
  };

  try {
    console.log('[SOLO-ROBOT] Solo-robot webhook listener started');
    console.log('[SOLO-ROBOT] Request method:', req.method);
    console.log('[SOLO-ROBOT] Request headers:', Object.fromEntries(req.headers.entries()));
    if (req.method !== 'POST') {
      console.log('[SOLO-ROBOT] Invalid method:', req.method);
      return new Response('Method not allowed', {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const requestBody = await req.text();
    console.log('[SOLO-ROBOT] Raw request body:', requestBody);

    // Performance monitoring - JSON parsing start
    const jsonParsingStartTime = performance.now();
    let signalData;
    try {
      signalData = JSON.parse(requestBody);
      const jsonParsingEndTime = performance.now();
      performanceMetrics.jsonParsingStartTime = jsonParsingStartTime;
      performanceMetrics.jsonParsingEndTime = jsonParsingEndTime;
      console.log('[SOLO-ROBOT] Parsed signal data:', JSON.stringify(signalData, null, 2));
      console.log('[SOLO-ROBOT] JSON parsing time:', (jsonParsingEndTime - jsonParsingStartTime).toFixed(3), 'ms');
    } catch (parseError) {
      const jsonParsingEndTime = performance.now();
      performanceMetrics.jsonParsingStartTime = jsonParsingStartTime;
      performanceMetrics.jsonParsingEndTime = jsonParsingEndTime;
      console.error('[SOLO-ROBOT] JSON parse error:', parseError);
      console.log('[SOLO-ROBOT] JSON parsing time (error):', (jsonParsingEndTime - jsonParsingStartTime).toFixed(3), 'ms');
      return new Response(JSON.stringify({ error: 'Invalid JSON' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    // Gerekli alanları kontrol et
    if (!signalData.name) {
      console.error('[SOLO-ROBOT] Missing required field: name');
      return new Response(JSON.stringify({ error: 'Missing required field: name' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Performance monitoring - Transformation start
    performanceMetrics.transformationStartTime = performance.now();

    // Signal name'i parse et
    const parsedSignal = parseSignalName(signalData.name);
    console.log('[SOLO-ROBOT] Parsed signal:', parsedSignal);
    // Symbol'ü belirle
    const symbol = signalData.symbol || parsedSignal.symbol || 'UNKNOWN';
    console.log('[SOLO-ROBOT] Determined symbol:', symbol);
    // Order side ve category'yi belirle (signalData.orderSide öncelikli)
    let orderSide = parsedSignal.orderSide;
    let tradeCategory = 'ALIM';
    let positionStatus = 'Açık';
    // signalData.orderSide varsa onu kullan (prioritize)
    if (signalData?.orderSide) {
      const orderSideValue = signalData.orderSide.toUpperCase();
      if (orderSideValue === 'BUY') {
        orderSide = 'BUY';
        tradeCategory = 'ALIM';
        positionStatus = 'Açık'; // BUY = pozisyon açma
      } else if (orderSideValue === 'SELL') {
        orderSide = 'SELL';
        tradeCategory = 'SATIM';
        positionStatus = 'Kapalı'; // SELL = pozisyon kapama
      }
    } else {
      // signalData.orderSide yoksa parsed signal'dan kullan
      if (orderSide === 'SELL') {
        tradeCategory = 'SATIM';
        positionStatus = 'Kapalı'; // SELL = pozisyon kapama
      } else {
        tradeCategory = 'ALIM';
        positionStatus = 'Açık'; // BUY = pozisyon açma
      }
    }
    console.log('[SOLO-ROBOT] Final order details:', {
      orderSide,
      tradeCategory,
      positionStatus,
      signalType: parsedSignal.signalType
    });
    // Get webhook_id from URL path
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const webhookId = pathParts[pathParts.length - 1];
    console.log('[SOLO-ROBOT] Extracted webhook_id from URL:', webhookId);
    if (!webhookId || webhookId === 'algobir-webhook-listener') {
      console.error('[SOLO-ROBOT] No webhook_id provided in URL path');
      return new Response(JSON.stringify({ error: 'No webhook_id provided' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    // OPTIMIZATION: Get user settings by webhook ID with caching
    console.log('[SOLO-ROBOT] Looking up user by webhook_id:', webhookId);
    const userSettings = await CacheManager.getUserSettingsByWebhookId(supabase, webhookId);
    if (!userSettings) {
      console.error('[SOLO-ROBOT] User not found or inactive for webhook:', webhookId);
      return new Response(JSON.stringify({ error: 'User not found or inactive' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    console.log('[SOLO-ROBOT] Found user settings for user ID:', userSettings.id);
    const userId = userSettings.id;
    // SELL işlemleri için açık pozisyon kontrolü ve quantity alma
    let openPositionData = null;
    if (orderSide === 'SELL') {
      console.log('[SOLO-ROBOT] SELL order detected, checking for open position');
      
      // Açık pozisyonu bul ve quantity'sini al
      const { data: openTrades, error: openTradeError } = await supabase
        .from('trades')
        .select('id, calculated_quantity, price, investment_amount')
        .eq('user_id', userId)
        .eq('symbol', symbol)
        .eq('order_side', 'BUY')
        .eq('position_status', 'Açık')
        .order('created_at', { ascending: false })
        .limit(1);
      
      if (openTradeError || !openTrades || openTrades.length === 0) {
        console.log('[SOLO-ROBOT] No open position found for SELL order, skipping trade creation');
        return new Response(JSON.stringify({
          success: false,
          message: `No open position found for ${symbol} to sell`,
          skipped: true,
          symbol: symbol,
          orderSide: 'SELL'
        }), {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
      
      openPositionData = openTrades[0];
      console.log('[SOLO-ROBOT] Open position found:', {
        id: openPositionData.id,
        quantity: openPositionData.calculated_quantity,
        buyPrice: openPositionData.price
      });
    }
    // Get API keys from user_settings
    console.log('[SOLO-ROBOT] Getting API keys from user_settings table');
    const { data: apiKeyData, error: apiKeyError } = await supabase.from('user_settings').select('encrypted_api_key, encrypted_token').eq('id', userId).single();
    if (apiKeyError || !apiKeyData?.encrypted_api_key || !apiKeyData?.encrypted_token) {
      console.error('[SOLO-ROBOT] API keys not found:', apiKeyError);
      return new Response(JSON.stringify({ error: 'API keys not configured' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    console.log('[SOLO-ROBOT] API keys found in user_settings');
    // Create the trade record - SELL için açık pozisyonun quantity'sini kullan
    let investmentAmount, price, calculatedQuantity;
    
    if (orderSide === 'SELL' && openPositionData) {
      // SELL işlemi: Açık pozisyonun verilerini kullan
      calculatedQuantity = openPositionData.calculated_quantity;
      investmentAmount = openPositionData.investment_amount;
      price = parseFloat(signalData.price) || 1.0; // SELL fiyatı signal'dan gelir
      
      console.log('[SOLO-ROBOT] Using open position data for SELL:', {
        calculatedQuantity,
        investmentAmount,
        sellPrice: price,
        buyPrice: openPositionData.price
      });
    } else {
      // BUY işlemi: Normal hesaplama
      investmentAmount = userSettings.investment_amount || 100.00;
      price = parseFloat(signalData.price) || 1.0;
      calculatedQuantity = Math.floor(investmentAmount / price);
      
      console.log('[SOLO-ROBOT] Calculated new position for BUY:', {
        calculatedQuantity,
        investmentAmount,
        buyPrice: price
      });
    }
    const tradeData = {
      user_id: userId,
      webhook_id: webhookId,
      robot_id: null,
      symbol: symbol,
      order_side: orderSide,
      signal_type: parsedSignal.signalType,
      trade_category: tradeCategory,
      position_status: positionStatus,
      system_name: `Solo-Robot (${parsedSignal.baseSystemName})`,
      price: price,
      calculated_quantity: calculatedQuantity,
      investment_amount: investmentAmount,
      signal_name: signalData.name,
      category: tradeCategory.toLowerCase(),
      forwarded: false,
      status: 'pending',
      raw_signal_data: signalData,
      name: signalData.name
    };

    // Performance monitoring - Transformation end
    performanceMetrics.transformationEndTime = performance.now();

    console.log('[SOLO-ROBOT] Creating trade record:', tradeData);
    const { data: tradeRecord, error: tradeError } = await supabase.from('trades').insert([
      tradeData
    ]).select('*').single();
    if (tradeError) {
      console.error('[SOLO-ROBOT] Trade creation error:', tradeError);
      return new Response(JSON.stringify({ error: 'Failed to create trade record' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    console.log('[SOLO-ROBOT] Trade record created:', tradeRecord.id);

    // OPTIMIZATION: P/L calculation for SELL trades (pozisyon kapama) - Made async to not block main flow
    if (orderSide === 'SELL' || positionStatus === 'Kapalı') {
      console.log(`[SOLO-ROBOT] Position closing detected, scheduling async P&L calculation for trade ${tradeRecord.id}`);

      // Run P&L calculation asynchronously without blocking the main flow
      supabase.rpc('calculate_and_update_pnl_for_position', {
        p_closing_trade_id: tradeRecord.id
      }).then(({ error: pnlError }) => {
        if (pnlError) {
          console.error('[SOLO-ROBOT] Async P&L calculation failed:', pnlError);
        } else {
          console.log(`[SOLO-ROBOT] Async P&L calculated successfully for trade ${tradeRecord.id}`);
        }
      }).catch(pnlCalculationError => {
        console.error('[SOLO-ROBOT] Async P&L calculation exception:', pnlCalculationError);
      });
    }

    // OPTIMIZATION: Send notification to user about trade - Made async to not block main flow
    console.log(`[SOLO-ROBOT] Scheduling async notification creation for trade ${tradeRecord.id}, user ${userId}`);

    // Create notification asynchronously without blocking the main flow
    const createNotificationAsync = async () => {
      try {
        const notificationTitle = orderSide === 'BUY' ?
          `Al Bildirimi` :
          `Sat Bildirimi`;

        const totalAmount = calculatedQuantity * price;
        const formattedPrice = formatTurkishCurrency(price);
        const formattedTotalAmount = formatTurkishCurrency(totalAmount);

        const notificationMessage = orderSide === 'BUY' ?
          `Al Bildirimi: ${symbol} hissesinden ${formattedPrice} TL fiyatla ${calculatedQuantity} adet alındı. Toplam tutar ₺${formattedTotalAmount}'dır. Solo-Robot (Robot ismi) ile işlem açıldı.` :
          `Sat Bildirimi: ${symbol} hissesinden ${formattedPrice} TL fiyatla ${calculatedQuantity} adet satıldı. Toplam tutar ₺${formattedTotalAmount}'dır. Solo-Robot (Robot ismi) ile işlem kapandı.`;

        console.log(`[SOLO-ROBOT] Async notification details:`, {
          title: notificationTitle,
          message: notificationMessage,
          type: orderSide === 'BUY' ? 'trade_opened' : 'trade_closed',
          userId: userId,
          tradeId: tradeRecord.id
        });

        const { data: notificationData, error: notificationError } = await supabase.rpc('create_notification', {
        p_user_id: userId,
        p_title: notificationTitle,
        p_message: notificationMessage,
        p_type: orderSide === 'BUY' ? 'trade_opened' : 'trade_closed',
        p_severity: 'success',
        p_metadata: {
          trade_id: tradeRecord.id,
          symbol: symbol,
          order_side: orderSide,
          quantity: calculatedQuantity,
          price: price,
          total_amount: totalAmount,
          source: 'solo-robot'
        },
        p_action_url: '/trades',
        p_action_label: 'İşlemleri Görüntüle'
      });

      if (notificationError) {
        console.error('[SOLO-ROBOT] Notification creation failed:', notificationError);
        console.error('[SOLO-ROBOT] Notification error details:', JSON.stringify(notificationError, null, 2));

        // Try fallback notification creation using direct INSERT
        try {
          console.log('[SOLO-ROBOT] Attempting fallback notification creation...');
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('notifications')
            .insert([{
              user_id: userId,
              title: notificationTitle,
              message: notificationMessage,
              type: orderSide === 'BUY' ? 'trade_opened' : 'trade_closed',
              severity: 'success',
              metadata: {
                trade_id: tradeRecord.id,
                symbol: symbol,
                order_side: orderSide,
                quantity: calculatedQuantity,
                price: price,
                total_amount: totalAmount,
                source: 'solo-robot'
              },
              action_url: '/trades',
              action_label: 'İşlemleri Görüntüle',
              is_read: false
            }])
            .select('*');

          if (fallbackError) {
            console.error('[SOLO-ROBOT] Fallback notification creation also failed:', fallbackError);
          } else {
            console.log(`[SOLO-ROBOT] Fallback notification created successfully:`, fallbackData);
          }
        } catch (fallbackException) {
          console.error('[SOLO-ROBOT] Async fallback notification exception:', fallbackException);
        }
      } else {
        console.log(`[SOLO-ROBOT] Async notification sent successfully to user ${userId} for trade ${tradeRecord.id}`);
        console.log(`[SOLO-ROBOT] Async notification data:`, notificationData);
      }
    } catch (notificationException) {
      console.error('[SOLO-ROBOT] Async notification exception:', notificationException);
      console.error('[SOLO-ROBOT] Async exception details:', JSON.stringify(notificationException, null, 2));
    }
    };

    // Execute notification creation asynchronously without blocking
    createNotificationAsync().catch(error => {
      console.error('[SOLO-ROBOT] Async notification creation failed:', error);
    });

    console.log(`Trade ${tradeRecord.id} created successfully. Now invoking order submission function.`);

    const authHeader = req.headers.get('Authorization') || `Bearer ${supabaseServiceKey}`;

    // Performance monitoring - Webhook delivery start
    performanceMetrics.webhookDeliveryStartTime = performance.now();

    // Try to invoke order submission function, but don't fail the entire process if it fails
    let orderSubmissionSuccess = false;
    let orderSubmissionError = null;

    try {
      const { data: invokeData, error: invokeError } = await supabase.functions.invoke(
        'osmanli-yatirim-emir-iletim-solo-robot',
        {
          body: { trade_id: tradeRecord.id },
          headers: { Authorization: authHeader },
        }
      );

      performanceMetrics.webhookDeliveryEndTime = performance.now();

      if (invokeError) {
        console.error(`Error invoking 'osmanli-yatirim-emir-iletim-solo-robot' for trade ${tradeRecord.id}:`, invokeError.message);
        orderSubmissionError = invokeError.message;
      } else {
        console.log(`'osmanli-yatirim-emir-iletim-solo-robot' invoked successfully for trade ${tradeRecord.id}.`, invokeData);
        orderSubmissionSuccess = true;
      }
    } catch (invokeException) {
      performanceMetrics.webhookDeliveryEndTime = performance.now();
      console.error(`Exception during order submission for trade ${tradeRecord.id}:`, invokeException);
      orderSubmissionError = invokeException.message;
    }

    // Update trade record with forwarding status
    try {
      await supabase
        .from('trades')
        .update({
          forwarded: orderSubmissionSuccess,
          forwarding_status: orderSubmissionSuccess ? 200 : 500,
          forwarded_at: new Date().toISOString()
        })
        .eq('id', tradeRecord.id);
    } catch (updateError) {
      console.error(`Failed to update forwarding status for trade ${tradeRecord.id}:`, updateError);
    }

    // Performance monitoring - Record metrics
    const totalProcessingTime = performance.now() - performanceMetrics.totalStartTime;
    const jsonParsingTime = performanceMetrics.jsonParsingEndTime - performanceMetrics.jsonParsingStartTime;
    const transformationTime = performanceMetrics.transformationEndTime - performanceMetrics.transformationStartTime;
    const webhookDeliveryTime = performanceMetrics.webhookDeliveryEndTime > 0 ?
      performanceMetrics.webhookDeliveryEndTime - performanceMetrics.webhookDeliveryStartTime : null;

    // Log performance metrics for debugging
    console.log('[SOLO-ROBOT] Performance Metrics:');
    console.log('  - JSON Parsing Time:', jsonParsingTime.toFixed(3), 'ms');
    console.log('  - Transformation Time:', transformationTime.toFixed(3), 'ms');
    console.log('  - Webhook Delivery Time:', webhookDeliveryTime ? webhookDeliveryTime.toFixed(3) : 'null', 'ms');
    console.log('  - Total Processing Time:', totalProcessingTime.toFixed(3), 'ms');

    // OPTIMIZATION: Record performance metrics asynchronously (truly non-blocking)
    supabase.rpc('insert_order_transmission_metrics', {
      p_trade_id: tradeRecord.id,
      p_webhook_id: webhookId,
      p_robot_id: null,
      p_signal_type: parsedSignal.signalType,
      p_symbol: symbol,
      p_order_side: orderSide,
      p_json_parsing_time_ms: jsonParsingTime,
      p_transformation_time_ms: transformationTime,
      p_webhook_delivery_time_ms: webhookDeliveryTime,
      p_total_processing_time_ms: totalProcessingTime,
      p_signal_source: 'solo-robot',
      p_processing_status: orderSubmissionSuccess ? 'success' : (orderSubmissionError ? 'partial' : 'success'),
      p_error_details: orderSubmissionError ? { webhook_error: orderSubmissionError } : null,
      p_signal_received_at: performanceMetrics.signalReceivedAt.toISOString(),
      p_processing_completed_at: new Date().toISOString(),
      p_raw_signal_size_bytes: new TextEncoder().encode(JSON.stringify(signalData)).length,
      p_endpoint_url: req.url,
      p_user_agent: req.headers.get('User-Agent')
    }).then(() => {
      console.log('[SOLO-ROBOT] Performance metrics recorded successfully');
    }).catch(metricsError => {
      console.error('[SOLO-ROBOT] Failed to record performance metrics:', metricsError);
      // Don't fail the main process if metrics recording fails
    });

    // Always return success for trade creation, but include order submission status
    return new Response(JSON.stringify({
      success: true,
      message: 'Solo-robot signal processed successfully',
      tradeId: tradeRecord.id,
      symbol: symbol,
      orderSide: orderSide,
      signalType: parsedSignal.signalType,
      positionAction: orderSide === 'BUY' ? 'Position Opened' : 'Position Closed',
      orderSubmission: {
        success: orderSubmissionSuccess,
        error: orderSubmissionError
      },
      performanceMetrics: {
        totalProcessingTimeMs: Math.round(totalProcessingTime * 100) / 100,
        jsonParsingTimeMs: Math.round(jsonParsingTime * 100) / 100,
        transformationTimeMs: Math.round(transformationTime * 100) / 100,
        webhookDeliveryTimeMs: webhookDeliveryTime ? Math.round(webhookDeliveryTime * 100) / 100 : null
      }
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('[SOLO-ROBOT] Unexpected error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
