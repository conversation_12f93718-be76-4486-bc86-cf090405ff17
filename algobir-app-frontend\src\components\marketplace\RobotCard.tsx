import { useState, useEffect } from 'react';
import { Box, Image, Heading, Text, Button, IconButton, Flex, useColorModeValue, Stack, Link } from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import { FiHeart } from 'react-icons/fi';
import { FaHeart } from 'react-icons/fa';
import { Robot } from '../../types/robot';
import { useAuth } from '../../context/AuthContext';
import { isWithinInterval, subWeeks } from 'date-fns';

interface RobotCardProps {
  robot: Robot;
  isSubscribed: boolean;
  onSubscribe: (robotId: string) => Promise<void>;
  onUnsubscribe: (robotId: string) => Promise<void>;
}

const RobotCard: React.FC<RobotCardProps> = ({ 
  robot, 
  isSubscribed, 
  onSubscribe, 
  onUnsubscribe 
}) => {
  const { user } = useAuth();
  const userId = user?.id || null;
  
  const [isLoading, setIsLoading] = useState(false);
  const [localSubscriptionState, setLocalSubscriptionState] = useState(isSubscribed);
  const boxBg = useColorModeValue('white', 'navy.700');
  const textColor = useColorModeValue('navy.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const brandColor = useColorModeValue('brand.500', 'brand.400');
  const updatedTextColor = useColorModeValue('brand.500', 'brand.400');

  // isSubscribed prop değiştiğinde yerel state'i güncelle
  useEffect(() => {
    setLocalSubscriptionState(isSubscribed);
  }, [isSubscribed]);

  // Favori durumu için state ekle
  const [isFavorited, setIsFavorited] = useState(false);

  // Favori durumunu değiştiren işleyici
  const handleFavoriteToggle = () => {
    setIsFavorited(!isFavorited);
    console.log(`${robot.name} ${isFavorited ? 'favorilerden çıkarıldı' : 'favorilere eklendi'}`);
  };

  // Abone olma/çıkma işleyicisi
  const handleSubscriptionToggle = async () => {
    if (!userId) return;  // Kullanıcı giriş yapmamışsa işlem yapma
    
    try {
      setIsLoading(true);
      
      if (localSubscriptionState) {
        // Önce yerel durumu güncelle, sonra API çağrısı yap
        setLocalSubscriptionState(false);
        await onUnsubscribe(robot.id);
      } else {
        // Önce yerel durumu güncelle, sonra API çağrısı yap
        setLocalSubscriptionState(true);
        await onSubscribe(robot.id);
      }
    } catch (error) {
      console.error('Abonelik işlemi sırasında hata:', error);
      // Hata durumunda yerel durumu eski haline geri al
      setLocalSubscriptionState(!localSubscriptionState);
    } finally {
      setIsLoading(false);
    }
  };

  // Son 7 gün içinde güncellenmiş mi kontrol et
  const isRecentlyUpdated = robot.updated_at && 
    isWithinInterval(new Date(robot.updated_at), {
      start: subWeeks(new Date(), 1),
      end: new Date()
    });

  // Tarih formatlama fonksiyonu
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <Box
      bg={boxBg}
      borderRadius="xl"
      boxShadow="md"
      overflow="hidden"
      borderWidth="1px"
      borderColor={borderColor}
      transition="transform 0.3s, box-shadow 0.3s"
      _hover={{
        transform: 'translateY(-5px)',
        boxShadow: 'lg',
      }}
    >
      <Box position="relative">
        <Image
          src={robot.image_url || ''}
          alt={robot.name}
          height="200px"
          width="100%"
          objectFit="cover"
          fallbackSrc="https://placehold.co/400x300?text=Robot"
          onError={(e) => { e.currentTarget.src = 'https://placehold.co/400x300?text=Robot'; }}
        />
        <IconButton
          aria-label="Favorilere ekle"
          icon={isFavorited ? <FaHeart /> : <FiHeart />}
          position="absolute"
          top="3"
          right="3"
          size="sm"
          colorScheme={isFavorited ? "red" : "gray"}
          variant="ghost"
          bg="whiteAlpha.700"
          _hover={{ bg: 'whiteAlpha.900', color: 'red.500' }}
          onClick={handleFavoriteToggle}
        />
      </Box>

      <Stack p={4} spacing={3}>
        <Heading as="h3" size="md" color={textColor} noOfLines={1}>
          <Link 
            as={RouterLink} 
            to={`/marketplace/robot/${robot.id}`}
            _hover={{ color: brandColor, textDecoration: "none" }}
            transition="color 0.2s"
          >
            {robot.name}
          </Link>
        </Heading>
        
        {robot.description && (
          <Text color={textColor} fontSize="sm" noOfLines={2} mb={3}>
            {robot.description}
          </Text>
        )}
        
        <Text fontSize="xs" color={textColor}>
          Oluşturulma: {formatDate(robot.created_at)}
        </Text>
        
        {robot.updated_at && (
          <Text 
            fontSize="xs" 
            color={isRecentlyUpdated ? updatedTextColor : textColor}
            fontWeight={isRecentlyUpdated ? "bold" : "normal"}
          >
            Güncellenme: {formatDate(robot.updated_at)}
            {isRecentlyUpdated && " 🆕"}
          </Text>
        )}
        
        <Flex justifyContent="space-between" alignItems="center">
          {robot.seller_username && robot.seller_url_slug ? (
            <Link 
              as={RouterLink} 
              to={`/user/${robot.seller_url_slug}`}
              fontWeight="medium" 
              color="brand.500"
              _hover={{ color: "brand.600", textDecoration: "underline" }}
            >
              {robot.seller_full_name || robot.seller_username}
            </Link>
          ) : robot.seller_username ? (
            <Text fontWeight="medium" color={textColor}>
              {robot.seller_full_name || robot.seller_username}
            </Text>
          ) : (
            <Text fontWeight="medium" color="gray.600">
              Geliştirici Bilgisi Yok
            </Text>
          )}
          
          {robot.price !== undefined && robot.price !== null && (
            <Text fontWeight="bold" fontSize="md" color={textColor}>
              ₺{robot.price}
            </Text>
          )}
        </Flex>
        
        <Button 
          colorScheme={localSubscriptionState ? "red" : "brand"}
          size="md" 
          w="100%"
          isLoading={isLoading}
          isDisabled={!userId}
          onClick={handleSubscriptionToggle}
          mb={2}
        >
          {!userId 
            ? "Giriş Yapın" 
            : localSubscriptionState 
              ? "Abonelikten Çık" 
              : "Abone Ol"
          }
        </Button>
        
        <Button 
          as={RouterLink}
          to={`/marketplace/robot/${robot.id}`}
          variant="outline"
          colorScheme="brand"
          size="md" 
          w="100%"
        >
          Robot Detaylarını Gör
        </Button>
      </Stack>
    </Box>
  );
};

export default RobotCard; 