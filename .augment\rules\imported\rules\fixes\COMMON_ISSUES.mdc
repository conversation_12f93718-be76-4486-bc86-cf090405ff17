---
type: "manual"
priority: "medium"
scope: ["troubleshooting", "debugging", "common-issues", "solutions", "fixes"]
last_updated: "2025-01-29"
---

# Common Issues & Solutions

## 🔧 Build & Development Issues

### Vite Build Errors
```bash
# Issue: Module resolution errors
Error: Failed to resolve import "@/components/..." from "src/pages/..."

# Solution: Check tsconfig.json paths configuration
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/pages/*": ["src/pages/*"]
    }
  }
}

# Alternative: Use relative imports
import { Component } from '../components/Component';
```

### TypeScript Errors
```typescript
// Issue: Type errors with Supabase client
Property 'from' does not exist on type 'SupabaseClient'

// Solution: Ensure proper Supabase types
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from './types/database.types';

const supabase: SupabaseClient<Database> = createClient(url, key);

// Issue: Chakra UI theme type errors
// Solution: Extend theme types properly
import { extendTheme, type ThemeConfig } from '@chakra-ui/react';

const config: ThemeConfig = {
  initialColorMode: 'light',
  useSystemColorMode: false,
};

const theme = extendTheme({ config });
```

### Dependency Issues
```bash
# Issue: Package version conflicts
npm ERR! peer dep missing: react@"^18.0.0"

# Solution: Check and update package.json
npm install react@^18.3.1 react-dom@^18.3.1

# Issue: Chakra UI version compatibility
# Solution: Ensure compatible versions
npm install @chakra-ui/react@^2.10.7 @emotion/react@^11.14.0 @emotion/styled@^11.14.0

# Issue: Supabase client version mismatch
# Solution: Update to latest stable version
npm install @supabase/supabase-js@^2.49.4
```

## 🗄️ Database & Supabase Issues

### Connection Problems
```typescript
// Issue: Supabase connection timeout
// Solution: Implement connection retry logic
const createSupabaseClient = () => {
  const client = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
    global: {
      headers: {
        'x-application-name': 'algobir-app-frontend',
      },
    },
    // Add retry configuration
    db: {
      schema: 'public',
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  });
  
  return client;
};

// Issue: RLS policy blocking queries
// Solution: Check and update RLS policies
-- Ensure user can access their own data
CREATE POLICY "Users can view own data" ON table_name
  FOR SELECT USING (auth.uid() = user_id);
```

### Query Performance Issues
```sql
-- Issue: Slow queries on large tables
-- Solution: Add proper indexes
CREATE INDEX CONCURRENTLY idx_trades_user_date 
ON trades (user_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_robots_performance 
ON robots USING GIN (performance_data);

-- Issue: N+1 query problems
-- Solution: Use proper joins and select statements
SELECT 
  t.*,
  r.name as robot_name,
  p.username
FROM trades t
LEFT JOIN robots r ON t.robot_id = r.id
LEFT JOIN profiles p ON t.user_id = p.id
WHERE t.user_id = $1;
```

### Real-time Subscription Issues
```typescript
// Issue: Real-time subscriptions not working
// Solution: Check channel configuration and RLS
const subscription = supabase
  .channel('trades')
  .on('postgres_changes', 
    { 
      event: 'INSERT', 
      schema: 'public', 
      table: 'trades',
      filter: `user_id=eq.${userId}` // Add filter for RLS
    },
    (payload) => {
      console.log('New trade:', payload.new);
      updateTradesState(payload.new);
    }
  )
  .subscribe((status) => {
    console.log('Subscription status:', status);
    if (status === 'SUBSCRIPTION_ERROR') {
      // Retry subscription
      setTimeout(() => {
        subscription.unsubscribe();
        setupSubscription();
      }, 5000);
    }
  });

// Issue: Memory leaks with subscriptions
// Solution: Proper cleanup
useEffect(() => {
  const subscription = setupSubscription();
  
  return () => {
    subscription.unsubscribe();
  };
}, [userId]);
```

## 🔐 Authentication Issues

### Session Management Problems
```typescript
// Issue: User session not persisting
// Solution: Check auth configuration and storage
const { data: { session }, error } = await supabase.auth.getSession();

if (error) {
  console.error('Session error:', error);
  // Clear corrupted session
  await supabase.auth.signOut();
  return;
}

// Issue: Token refresh failures
// Solution: Handle refresh errors gracefully
supabase.auth.onAuthStateChange(async (event, session) => {
  if (event === 'TOKEN_REFRESHED') {
    console.log('Token refreshed successfully');
  } else if (event === 'SIGNED_OUT') {
    // Clear user data
    clearUserData();
    navigate('/login');
  }
});

// Issue: Admin route access problems
// Solution: Verify admin status properly
const checkAdminStatus = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_settings')
    .select('is_superuser')
    .eq('id', userId)
    .single();
  
  if (error) {
    console.error('Admin check error:', error);
    return false;
  }
  
  return data?.is_superuser || false;
};
```

### Permission Issues
```sql
-- Issue: RLS blocking admin access
-- Solution: Create proper admin policies
CREATE POLICY "Admins can access all data" ON trades
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_settings 
      WHERE id = auth.uid() AND is_superuser = TRUE
    )
  );

-- Issue: Function execution permissions
-- Solution: Set proper security definer
CREATE OR REPLACE FUNCTION get_user_statistics(p_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER -- This allows function to run with elevated privileges
AS $$
BEGIN
  -- Function implementation
END;
$$;
```

## ⚡ Performance Issues

### Slow Page Loading
```typescript
// Issue: Large bundle sizes
// Solution: Implement code splitting
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Statistics = lazy(() => import('./pages/Statistics'));

// Issue: Unnecessary re-renders
// Solution: Use React.memo and useMemo
const ExpensiveComponent = memo(({ data }: { data: any[] }) => {
  const processedData = useMemo(() => {
    return data.map(item => processItem(item));
  }, [data]);
  
  return <div>{/* Render processed data */}</div>;
});

// Issue: Slow API responses
// Solution: Implement caching
const useDataWithCache = (key: string, fetcher: () => Promise<any>) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const cached = localStorage.getItem(key);
    if (cached) {
      const { data: cachedData, timestamp } = JSON.parse(cached);
      if (Date.now() - timestamp < 5 * 60 * 1000) { // 5 minutes
        setData(cachedData);
        setLoading(false);
        return;
      }
    }
    
    fetcher().then(result => {
      setData(result);
      localStorage.setItem(key, JSON.stringify({
        data: result,
        timestamp: Date.now()
      }));
      setLoading(false);
    });
  }, [key]);
  
  return { data, loading };
};
```

### Memory Leaks
```typescript
// Issue: Event listeners not cleaned up
// Solution: Proper cleanup in useEffect
useEffect(() => {
  const handleResize = () => {
    // Handle resize
  };
  
  window.addEventListener('resize', handleResize);
  
  return () => {
    window.removeEventListener('resize', handleResize);
  };
}, []);

// Issue: Timers not cleared
// Solution: Clear timers in cleanup
useEffect(() => {
  const interval = setInterval(() => {
    fetchData();
  }, 30000);
  
  return () => {
    clearInterval(interval);
  };
}, []);

// Issue: Supabase subscriptions not unsubscribed
// Solution: Proper subscription cleanup
useEffect(() => {
  const subscription = supabase
    .channel('updates')
    .on('postgres_changes', { /* config */ }, handler)
    .subscribe();
  
  return () => {
    subscription.unsubscribe();
  };
}, []);
```

## 🔔 Trading System Issues

### Order Transmission Problems
```typescript
// Issue: Webhook timeouts
// Solution: Implement async processing
const processOrderAsync = async (trade: Trade) => {
  // Return immediately to webhook
  const response = {
    success: true,
    trade_id: trade.id
  };
  
  // Process order asynchronously
  setImmediate(async () => {
    try {
      await executeOrder(trade);
    } catch (error) {
      await logOrderError(trade.id, error);
    }
  });
  
  return response;
};

// Issue: Signal parsing failures
// Solution: Robust parsing with fallbacks
const parseSignalSafely = (signalName: string) => {
  try {
    const parsed = parseSignalName(signalName);
    return parsed;
  } catch (error) {
    console.error('Signal parsing error:', error);
    
    // Return safe defaults
    return {
      systemName: 'Unknown',
      signalType: 'UNKNOWN',
      orderSide: 'BUY',
      symbol: 'UNKNOWN'
    };
  }
};

// Issue: Duplicate trade creation
// Solution: Add unique constraints and checks
const createTradeWithDuplicateCheck = async (tradeData: TradeData) => {
  // Check for recent duplicate
  const { data: existing } = await supabase
    .from('trades')
    .select('id')
    .eq('user_id', tradeData.user_id)
    .eq('symbol', tradeData.symbol)
    .eq('order_side', tradeData.order_side)
    .gte('created_at', new Date(Date.now() - 60000).toISOString()) // Last minute
    .limit(1);
  
  if (existing && existing.length > 0) {
    throw new Error('Duplicate trade detected');
  }
  
  return await createTrade(tradeData);
};
```

### Real-time Update Issues
```typescript
// Issue: UI not updating with new trades
// Solution: Ensure proper state updates
const useTradesWithRealtime = (userId: string) => {
  const [trades, setTrades] = useState<Trade[]>([]);
  
  useEffect(() => {
    // Initial fetch
    fetchTrades();
    
    // Real-time subscription
    const subscription = supabase
      .channel('user_trades')
      .on('postgres_changes',
        { 
          event: 'INSERT', 
          schema: 'public', 
          table: 'trades',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          setTrades(prev => [payload.new as Trade, ...prev]);
        }
      )
      .subscribe();
    
    return () => subscription.unsubscribe();
  }, [userId]);
  
  const fetchTrades = async () => {
    const { data } = await supabase
      .from('trades')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    setTrades(data || []);
  };
  
  return { trades, refetch: fetchTrades };
};
```

## 🎨 UI/UX Issues

### Chakra UI Theme Problems
```typescript
// Issue: Theme not applying correctly
// Solution: Ensure proper theme provider setup
import { ChakraProvider, extendTheme } from '@chakra-ui/react';

const theme = extendTheme({
  config: {
    initialColorMode: 'light',
    useSystemColorMode: false,
  },
  colors: {
    brand: {
      50: '#E6FFFA',
      500: '#38B2AC',
      900: '#234E52',
    },
  },
});

// Wrap app with theme provider
<ChakraProvider theme={theme}>
  <App />
</ChakraProvider>

// Issue: Responsive design not working
// Solution: Use proper breakpoint syntax
<Box
  width={{ base: '100%', md: '50%', lg: '25%' }}
  padding={{ base: 4, md: 6, lg: 8 }}
>
  Content
</Box>
```

### Animation Issues
```typescript
// Issue: Framer Motion conflicts with Chakra UI
// Solution: Use MotionBox from Chakra UI
import { motion } from 'framer-motion';
import { Box } from '@chakra-ui/react';

const MotionBox = motion(Box);

// Use MotionBox instead of motion.div
<MotionBox
  initial={{ opacity: 0 }}
  animate={{ opacity: 1 }}
  transition={{ duration: 0.3 }}
>
  Content
</MotionBox>

// Issue: Layout shift during animations
// Solution: Reserve space for animated elements
<Box minHeight="200px">
  <AnimatedComponent />
</Box>
```

## 📱 Mobile & Responsive Issues

### Mobile Navigation Problems
```typescript
// Issue: Sidebar not working on mobile
// Solution: Use Chakra UI Drawer for mobile
const MobileSidebar = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => (
  <Drawer isOpen={isOpen} placement="left" onClose={onClose}>
    <DrawerOverlay />
    <DrawerContent>
      <DrawerCloseButton />
      <DrawerBody>
        <SidebarContent />
      </DrawerBody>
    </DrawerContent>
  </Drawer>
);

// Issue: Touch events not working
// Solution: Add proper touch event handlers
const handleTouchStart = (e: TouchEvent) => {
  // Handle touch start
};

const handleTouchEnd = (e: TouchEvent) => {
  // Handle touch end
};

useEffect(() => {
  const element = ref.current;
  if (element) {
    element.addEventListener('touchstart', handleTouchStart);
    element.addEventListener('touchend', handleTouchEnd);
    
    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }
}, []);
```

## Update Requirements

### When to Update This File
- New common issues discovered
- Solutions to existing problems found
- Framework or library updates causing new issues
- User-reported bugs and their fixes
- Performance issues and optimizations
- Security vulnerabilities and patches

### Related Files to Update
- Update relevant feature files when issues are specific to features
- Update `core/PROJECT_CORE.mdc` for architecture-related issues
- Update `security/GUIDELINES.mdc` for security-related fixes
- Update `performance/OPTIMIZATION.mdc` for performance issues
- Update `deployment/PRODUCTION.mdc` for deployment-related problems
