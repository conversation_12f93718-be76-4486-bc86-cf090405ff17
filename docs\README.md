# Algobir Documentation Hub

Welcome to the comprehensive documentation for the Algobir algorithmic trading platform. This documentation covers all aspects of the system from architecture to deployment.

## 📚 Documentation Structure

### Core Documentation

#### [Technical Architecture](./TECHNICAL_ARCHITECTURE.md)
Complete system architecture documentation including:
- **System Overview**: Business model and technology stack
- **Frontend Architecture**: React 18.3.1, TypeScript 5.8.3, Chakra UI 2.10.7
- **Backend Architecture**: Supabase PostgreSQL 15, Edge Functions, Real-time subscriptions
- **Data Flow Architecture**: Trading signal processing with visual diagrams
- **Security Architecture**: Authentication, authorization, data protection
- **Performance Optimization**: Frontend/backend optimization strategies

#### [API Reference](./API_REFERENCE.md)
Comprehensive API documentation including:
- **Edge Functions API**: Complete endpoint documentation with examples
- **Trading Signal Processing**: Solo-Robot and Bro-Robot signal handling
- **Database API**: Supabase REST API with CRUD operations
- **Real-time Subscriptions**: WebSocket channel documentation
- **Authentication**: JWT-based auth with role-based access control

#### [Developer Guide](./DEVELOPER_GUIDE.md)
Complete development guide covering:
- **Getting Started**: Development environment setup
- **Component Development**: React patterns and best practices
- **API Integration**: Service layer patterns and data fetching
- **Testing**: Unit testing (Vitest) and E2E testing (Playwright)
- **Performance Optimization**: Code splitting, memoization, monitoring

#### [Onboarding Guide](./ONBOARDING_GUIDE.md)
New developer onboarding documentation:
- **Quick Start**: Get up and running in 15 minutes
- **Development Environment**: Complete setup instructions
- **Project Structure**: Codebase organization and conventions
- **Development Workflow**: Git workflow, code review process

### Architectural Decision Records (ADRs)

#### [ADR-001: Technology Stack Selection](./adr/ADR-001-TECHNOLOGY_STACK_SELECTION.md)
Decision rationale for core technology choices:
- React 18.3.1 with TypeScript 5.8.3
- Chakra UI 2.10.7 for component library
- Supabase for backend infrastructure
- Vite 5.4.18 for build tooling

#### [ADR-002: Database Schema Design](./adr/ADR-002-DATABASE_SCHEMA_DESIGN.md)
Database architecture decisions:
- PostgreSQL schema design
- Row Level Security (RLS) implementation
- Performance optimization strategies
- Data integrity and validation

#### [ADR-003: Authentication & Authorization Strategy](./adr/ADR-003-AUTHENTICATION_AUTHORIZATION_STRATEGY.md)
Security architecture decisions:
- JWT-based authentication
- Role-based access control (RBAC)
- Session management
- API security patterns

## 🚀 Quick Navigation

### For New Developers
1. Start with [Onboarding Guide](./ONBOARDING_GUIDE.md)
2. Review [Technical Architecture](./TECHNICAL_ARCHITECTURE.md)
3. Follow [Developer Guide](./DEVELOPER_GUIDE.md)

### For API Integration
1. Review [API Reference](./API_REFERENCE.md)
2. Check [Authentication documentation](./API_REFERENCE.md#authentication)
3. Explore [Real-time features](./API_REFERENCE.md#real-time-subscriptions)

### For System Architecture
1. Study [Technical Architecture](./TECHNICAL_ARCHITECTURE.md)
2. Review relevant [ADRs](./adr/)
3. Understand [Data Flow Architecture](./TECHNICAL_ARCHITECTURE.md#data-flow-architecture)

## 📋 Documentation Standards

### Writing Guidelines
- Use clear, concise language
- Include code examples where applicable
- Maintain consistent formatting
- Update documentation with code changes

### Code Examples
- All examples should be tested and working
- Include both TypeScript and JavaScript where relevant
- Provide complete, runnable examples
- Include error handling patterns

### Diagrams and Visuals
- Use Mermaid for system diagrams
- Include screenshots for UI documentation
- Maintain consistent visual style
- Update diagrams with system changes

## 🔄 Maintenance

### Regular Updates
- Documentation is updated with each major release
- ADRs are created for significant architectural decisions
- API documentation is automatically generated where possible
- Examples are tested as part of CI/CD pipeline

### Contributing
- All code changes should include documentation updates
- New features require corresponding documentation
- Breaking changes must be clearly documented
- Follow the established documentation patterns

## 📞 Support

### Internal Support
- Development team: `<EMAIL>`
- Architecture questions: Review ADRs or create new ones
- API questions: Check API Reference or contact backend team

### External Resources
- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Chakra UI Documentation](https://chakra-ui.com/docs)
- [Supabase Documentation](https://supabase.com/docs)

## 📈 Metrics and Analytics

### Documentation Usage
- Track documentation page views
- Monitor search queries
- Collect feedback on documentation quality
- Regular documentation audits

### Quality Metrics
- Documentation coverage for new features
- Time to onboard new developers
- Developer satisfaction with documentation
- Documentation maintenance overhead

---

**Last Updated**: January 30, 2025  
**Version**: 1.0.0  
**Maintained by**: Algobir Development Team
