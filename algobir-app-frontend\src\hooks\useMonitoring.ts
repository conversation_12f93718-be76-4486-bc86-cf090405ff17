/**
 * Monitoring Hook
 * Phase 3.3: Monitoring & Alerting Systems
 * React hook for monitoring integration and business metrics tracking
 */

import { useEffect, useCallback, useRef } from 'react';
import { monitoring, initializeMonitoring } from '../utils/monitoring';

// Types
interface MonitoringHookOptions {
  enabled?: boolean;
  trackPageViews?: boolean;
  trackUserInteractions?: boolean;
  trackPerformance?: boolean;
  trackErrors?: boolean;
}

// Removed unused BusinessEvent interface

interface PerformanceTimer {
  name: string;
  startTime: number;
  endTimer: () => void;
}

// Hook implementation
export const useMonitoring = (options: MonitoringHookOptions = {}) => {
  const {
    enabled = true,
    trackPageViews = true,
    trackUserInteractions = true,
    trackPerformance = true,
    trackErrors = true
  } = options;

  const initialized = useRef(false);
  const pageViewTracked = useRef(false);
  const performanceTimers = useRef<Map<string, number>>(new Map());

  // Initialize monitoring on mount
  useEffect(() => {
    if (!enabled || initialized.current) return;

    try {
      initializeMonitoring();
      initialized.current = true;
      
      console.log('Monitoring initialized successfully');
    } catch (error) {
      console.error('Failed to initialize monitoring:', error);
    }
  }, [enabled]);

  // Track page views
  useEffect(() => {
    if (!enabled || !trackPageViews || pageViewTracked.current) return;

    try {
      monitoring.trackEvent('page_view', 1, {
        path: window.location.pathname,
        search: window.location.search,
        referrer: document.referrer,
        timestamp: Date.now()
      });
      
      pageViewTracked.current = true;
    } catch (error) {
      console.error('Failed to track page view:', error);
    }
  }, [enabled, trackPageViews]);

  // Track performance metrics
  useEffect(() => {
    if (!enabled || !trackPerformance) return;

    const trackNavigationTiming = () => {
      try {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          // Track key navigation metrics
          monitoring.recordMetric('navigation_dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart);
          monitoring.recordMetric('navigation_load_complete', navigation.loadEventEnd - navigation.loadEventStart);
          monitoring.recordMetric('navigation_dns_lookup', navigation.domainLookupEnd - navigation.domainLookupStart);
          monitoring.recordMetric('navigation_tcp_connect', navigation.connectEnd - navigation.connectStart);
          monitoring.recordMetric('navigation_request_response', navigation.responseEnd - navigation.requestStart);
        }
      } catch (error) {
        console.error('Failed to track navigation timing:', error);
      }
    };

    // Track navigation timing when page is fully loaded
    if (document.readyState === 'complete') {
      trackNavigationTiming();
    } else {
      window.addEventListener('load', trackNavigationTiming, { once: true });
    }

    return () => {
      window.removeEventListener('load', trackNavigationTiming);
    };
  }, [enabled, trackPerformance]);

  // Track user interactions
  useEffect(() => {
    if (!enabled || !trackUserInteractions) return;

    const trackClick = (event: MouseEvent) => {
      try {
        const target = event.target as HTMLElement;
        const tagName = target.tagName.toLowerCase();
        const className = target.className;
        const id = target.id;
        
        monitoring.trackEvent('user_click', 1, {
          tagName,
          className: className || undefined,
          id: id || undefined,
          timestamp: Date.now()
        });
      } catch (error) {
        console.error('Failed to track click:', error);
      }
    };

    const trackKeyPress = (event: KeyboardEvent) => {
      try {
        // Only track specific keys to avoid privacy issues
        const trackedKeys = ['Enter', 'Escape', 'Tab', 'Space'];
        
        if (trackedKeys.includes(event.key)) {
          monitoring.trackEvent('user_keypress', 1, {
            key: event.key,
            timestamp: Date.now()
          });
        }
      } catch (error) {
        console.error('Failed to track keypress:', error);
      }
    };

    document.addEventListener('click', trackClick);
    document.addEventListener('keydown', trackKeyPress);

    return () => {
      document.removeEventListener('click', trackClick);
      document.removeEventListener('keydown', trackKeyPress);
    };
  }, [enabled, trackUserInteractions]);

  // Track errors
  useEffect(() => {
    if (!enabled || !trackErrors) return;

    const trackError = (event: ErrorEvent) => {
      try {
        monitoring.captureException(
          new Error(event.message),
          {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            type: 'javascript_error'
          },
          'high'
        );
      } catch (error) {
        console.error('Failed to track error:', error);
      }
    };

    const trackUnhandledRejection = (event: PromiseRejectionEvent) => {
      try {
        monitoring.captureException(
          new Error(`Unhandled Promise Rejection: ${event.reason}`),
          {
            reason: event.reason,
            type: 'unhandled_promise_rejection'
          },
          'high'
        );
      } catch (error) {
        console.error('Failed to track unhandled rejection:', error);
      }
    };

    window.addEventListener('error', trackError);
    window.addEventListener('unhandledrejection', trackUnhandledRejection);

    return () => {
      window.removeEventListener('error', trackError);
      window.removeEventListener('unhandledrejection', trackUnhandledRejection);
    };
  }, [enabled, trackErrors]);

  // Business event tracking
  const trackEvent = useCallback((event: string, value?: number, properties?: Record<string, any>) => {
    if (!enabled) return;

    try {
      monitoring.trackEvent(event, value, properties);
    } catch (error) {
      console.error('Failed to track business event:', error);
    }
  }, [enabled]);

  // Performance timer utilities
  const startTimer = useCallback((name: string): PerformanceTimer => {
    const startTime = performance.now();
    performanceTimers.current.set(name, startTime);

    const endTimer = () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      try {
        monitoring.recordMetric(`timer_${name}`, duration, { unit: 'ms' });
        performanceTimers.current.delete(name);
      } catch (error) {
        console.error('Failed to record timer:', error);
      }
    };

    return {
      name,
      startTime,
      endTimer
    };
  }, []);

  // Custom metric recording
  const recordMetric = useCallback((name: string, value: number, tags?: Record<string, string>) => {
    if (!enabled) return;

    try {
      monitoring.recordMetric(name, value, tags);
    } catch (error) {
      console.error('Failed to record metric:', error);
    }
  }, [enabled]);

  // Error capturing
  const captureException = useCallback((error: Error, context?: Record<string, any>, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium') => {
    if (!enabled) return;

    try {
      monitoring.captureException(error, context, severity);
    } catch (err) {
      console.error('Failed to capture exception:', err);
    }
  }, [enabled]);

  // Message capturing
  const captureMessage = useCallback((message: string, context?: Record<string, any>, severity: 'low' | 'medium' | 'high' | 'critical' = 'low') => {
    if (!enabled) return;

    try {
      monitoring.captureMessage(message, context, severity);
    } catch (error) {
      console.error('Failed to capture message:', error);
    }
  }, [enabled]);

  // Trading-specific event tracking
  const trackTradingEvent = useCallback((eventType: string, data: Record<string, any>) => {
    if (!enabled) return;

    try {
      monitoring.trackEvent(`trading_${eventType}`, 1, {
        ...data,
        timestamp: Date.now(),
        category: 'trading'
      });
    } catch (error) {
      console.error('Failed to track trading event:', error);
    }
  }, [enabled]);

  // User interaction tracking
  const trackUserAction = useCallback((action: string, target?: string, metadata?: Record<string, any>) => {
    if (!enabled) return;

    try {
      monitoring.trackEvent('user_action', 1, {
        action,
        target,
        ...metadata,
        timestamp: Date.now(),
        category: 'user_interaction'
      });
    } catch (error) {
      console.error('Failed to track user action:', error);
    }
  }, [enabled]);

  // API call tracking
  const trackApiCall = useCallback((endpoint: string, method: string, duration: number, status: number) => {
    if (!enabled) return;

    try {
      monitoring.recordMetric('api_call_duration', duration, {
        endpoint,
        method,
        status: status.toString()
      });

      monitoring.trackEvent('api_call', 1, {
        endpoint,
        method,
        status,
        duration,
        timestamp: Date.now(),
        category: 'api'
      });
    } catch (error) {
      console.error('Failed to track API call:', error);
    }
  }, [enabled]);

  return {
    // Event tracking
    trackEvent,
    trackTradingEvent,
    trackUserAction,
    trackApiCall,
    
    // Performance monitoring
    startTimer,
    recordMetric,
    
    // Error tracking
    captureException,
    captureMessage,
    
    // Status
    isEnabled: enabled && initialized.current,
    isInitialized: initialized.current
  };
};

export default useMonitoring;
