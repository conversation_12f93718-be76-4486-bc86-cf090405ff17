/**
 * Application Performance Monitoring & Error Tracking
 * Phase 3.3: Monitoring & Alerting Systems
 * Comprehensive monitoring solution for production applications
 */

// Performance monitoring types
interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

interface ErrorReport {
  message: string;
  stack?: string;
  timestamp: number;
  url: string;
  userAgent: string;
  userId?: string;
  sessionId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
}

interface BusinessMetric {
  event: string;
  value: number;
  timestamp: number;
  userId?: string;
  properties?: Record<string, any>;
}

// Monitoring configuration
const MONITORING_CONFIG = {
  enabled: import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true',
  errorReporting: import.meta.env.VITE_ENABLE_ERROR_REPORTING === 'true',
  sampleRate: 0.1, // 10% sampling for performance metrics
  errorSampleRate: 1.0, // 100% error reporting
  batchSize: 10,
  flushInterval: 30000, // 30 seconds
  maxRetries: 3,
  retryDelay: 1000,
  endpoints: {
    collector: import.meta.env.VITE_SUPABASE_URL + '/functions/v1/monitoring-collector',
    dashboard: import.meta.env.VITE_SUPABASE_URL + '/functions/v1/monitoring-dashboard'
  }
};

// Performance monitoring class
class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observer: PerformanceObserver | null = null;
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    if (!MONITORING_CONFIG.enabled) return;
    
    this.initializePerformanceObserver();
    this.startFlushTimer();
    this.trackWebVitals();
  }

  private initializePerformanceObserver() {
    if (!('PerformanceObserver' in window)) return;

    try {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.recordMetric({
            name: entry.name,
            value: entry.duration || (entry as any).value || 0,
            timestamp: Date.now(),
            tags: {
              type: entry.entryType,
              initiatorType: (entry as any).initiatorType || 'unknown'
            },
            metadata: {
              startTime: entry.startTime,
              entryType: entry.entryType
            }
          });
        });
      });

      // Observe different types of performance entries
      this.observer.observe({ entryTypes: ['navigation', 'resource', 'measure', 'mark'] });
    } catch (error) {
      console.warn('Failed to initialize PerformanceObserver:', error);
    }
  }

  private trackWebVitals() {
    // Track Core Web Vitals
    this.trackLCP(); // Largest Contentful Paint
    this.trackFID(); // First Input Delay
    this.trackCLS(); // Cumulative Layout Shift
    this.trackFCP(); // First Contentful Paint
    this.trackTTFB(); // Time to First Byte
  }

  private trackLCP() {
    if (!('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        this.recordMetric({
          name: 'web_vitals_lcp',
          value: lastEntry.startTime,
          timestamp: Date.now(),
          tags: { vital: 'lcp', unit: 'ms' }
        });
      });

      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (error) {
      console.warn('Failed to track LCP:', error);
    }
  }

  private trackFID() {
    if (!('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.recordMetric({
            name: 'web_vitals_fid',
            value: (entry as any).processingStart - entry.startTime,
            timestamp: Date.now(),
            tags: { vital: 'fid', unit: 'ms' }
          });
        });
      });

      observer.observe({ entryTypes: ['first-input'] });
    } catch (error) {
      console.warn('Failed to track FID:', error);
    }
  }

  private trackCLS() {
    if (!('PerformanceObserver' in window)) return;

    let clsValue = 0;
    let sessionValue = 0;
    let sessionEntries: any[] = [];

    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            const firstSessionEntry = sessionEntries[0];
            const lastSessionEntry = sessionEntries[sessionEntries.length - 1];

            if (sessionValue && 
                entry.startTime - lastSessionEntry.startTime < 1000 &&
                entry.startTime - firstSessionEntry.startTime < 5000) {
              sessionValue += entry.value;
              sessionEntries.push(entry);
            } else {
              sessionValue = entry.value;
              sessionEntries = [entry];
            }

            if (sessionValue > clsValue) {
              clsValue = sessionValue;
              
              this.recordMetric({
                name: 'web_vitals_cls',
                value: clsValue,
                timestamp: Date.now(),
                tags: { vital: 'cls', unit: 'score' }
              });
            }
          }
        });
      });

      observer.observe({ entryTypes: ['layout-shift'] });
    } catch (error) {
      console.warn('Failed to track CLS:', error);
    }
  }

  private trackFCP() {
    if (!('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            this.recordMetric({
              name: 'web_vitals_fcp',
              value: entry.startTime,
              timestamp: Date.now(),
              tags: { vital: 'fcp', unit: 'ms' }
            });
          }
        });
      });

      observer.observe({ entryTypes: ['paint'] });
    } catch (error) {
      console.warn('Failed to track FCP:', error);
    }
  }

  private trackTTFB() {
    if (!('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (entry.entryType === 'navigation') {
            const ttfb = entry.responseStart - entry.requestStart;
            
            this.recordMetric({
              name: 'web_vitals_ttfb',
              value: ttfb,
              timestamp: Date.now(),
              tags: { vital: 'ttfb', unit: 'ms' }
            });
          }
        });
      });

      observer.observe({ entryTypes: ['navigation'] });
    } catch (error) {
      console.warn('Failed to track TTFB:', error);
    }
  }

  recordMetric(metric: PerformanceMetric) {
    if (!MONITORING_CONFIG.enabled) return;
    
    // Apply sampling
    if (Math.random() > MONITORING_CONFIG.sampleRate) return;

    this.metrics.push(metric);

    // Flush if batch size reached
    if (this.metrics.length >= MONITORING_CONFIG.batchSize) {
      this.flush();
    }
  }

  private startFlushTimer() {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, MONITORING_CONFIG.flushInterval);
  }

  private async flush() {
    if (this.metrics.length === 0) return;

    const metricsToSend = [...this.metrics];
    this.metrics = [];

    try {
      await this.sendMetrics(metricsToSend);
    } catch (error) {
      console.warn('Failed to send performance metrics:', error);
      // Re-add metrics for retry (with limit)
      if (this.metrics.length < MONITORING_CONFIG.batchSize * 2) {
        this.metrics.unshift(...metricsToSend);
      }
    }
  }

  private async sendMetrics(metrics: PerformanceMetric[], retryCount = 0): Promise<void> {
    try {
      const response = await fetch(MONITORING_CONFIG.endpoints.collector, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          type: 'performance',
          data: metrics,
          sessionId: this.getSessionId(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      if (retryCount < MONITORING_CONFIG.maxRetries) {
        await new Promise(resolve =>
          setTimeout(resolve, MONITORING_CONFIG.retryDelay * Math.pow(2, retryCount))
        );
        return this.sendMetrics(metrics, retryCount + 1);
      }
      throw error;
    }
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('monitoring_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('monitoring_session_id', sessionId);
    }
    return sessionId;
  }

  // Public API methods
  startTimer(name: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      this.recordMetric({
        name: `custom_${name}`,
        value: duration,
        timestamp: Date.now(),
        tags: { type: 'custom_timer', unit: 'ms' }
      });
    };
  }

  recordCustomMetric(name: string, value: number, tags?: Record<string, string>) {
    this.recordMetric({
      name: `custom_${name}`,
      value,
      timestamp: Date.now(),
      tags: { type: 'custom', ...tags }
    });
  }

  destroy() {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
    
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    
    // Final flush
    this.flush();
  }
}

// Error tracking class
class ErrorTracker {
  private errors: ErrorReport[] = [];
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    if (!MONITORING_CONFIG.errorReporting) return;
    
    this.initializeErrorHandlers();
    this.startFlushTimer();
  }

  private initializeErrorHandlers() {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.recordError({
        message: event.message,
        stack: event.error?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        sessionId: this.getSessionId(),
        severity: 'high',
        context: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          type: 'javascript_error'
        }
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.recordError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        sessionId: this.getSessionId(),
        severity: 'high',
        context: {
          type: 'unhandled_promise_rejection',
          reason: event.reason
        }
      });
    });
  }

  recordError(error: ErrorReport) {
    if (!MONITORING_CONFIG.errorReporting) return;
    
    // Apply sampling
    if (Math.random() > MONITORING_CONFIG.errorSampleRate) return;

    this.errors.push(error);

    // Flush immediately for critical errors
    if (error.severity === 'critical') {
      this.flush();
    } else if (this.errors.length >= MONITORING_CONFIG.batchSize) {
      this.flush();
    }
  }

  private startFlushTimer() {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, MONITORING_CONFIG.flushInterval);
  }

  private async flush() {
    if (this.errors.length === 0) return;

    const errorsToSend = [...this.errors];
    this.errors = [];

    try {
      await this.sendErrors(errorsToSend);
    } catch (error) {
      console.warn('Failed to send error reports:', error);
      // Re-add errors for retry (with limit)
      if (this.errors.length < MONITORING_CONFIG.batchSize * 2) {
        this.errors.unshift(...errorsToSend);
      }
    }
  }

  private async sendErrors(errors: ErrorReport[], retryCount = 0): Promise<void> {
    try {
      const response = await fetch(MONITORING_CONFIG.endpoints.collector, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          type: 'error',
          data: errors,
          sessionId: this.getSessionId(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      if (retryCount < MONITORING_CONFIG.maxRetries) {
        await new Promise(resolve =>
          setTimeout(resolve, MONITORING_CONFIG.retryDelay * Math.pow(2, retryCount))
        );
        return this.sendErrors(errors, retryCount + 1);
      }
      throw error;
    }
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('monitoring_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('monitoring_session_id', sessionId);
    }
    return sessionId;
  }

  // Public API methods
  captureException(error: Error, context?: Record<string, any>, severity: ErrorReport['severity'] = 'medium') {
    this.recordError({
      message: error.message,
      stack: error.stack,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      sessionId: this.getSessionId(),
      severity,
      context: {
        type: 'captured_exception',
        ...context
      }
    });
  }

  captureMessage(message: string, context?: Record<string, any>, severity: ErrorReport['severity'] = 'low') {
    this.recordError({
      message,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      sessionId: this.getSessionId(),
      severity,
      context: {
        type: 'captured_message',
        ...context
      }
    });
  }

  destroy() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    
    // Final flush
    this.flush();
  }
}

// Business metrics tracking class
class BusinessMetricsTracker {
  private metrics: BusinessMetric[] = [];
  private flushTimer: NodeJS.Timeout | null = null;

  constructor() {
    this.startFlushTimer();
  }

  private startFlushTimer() {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, MONITORING_CONFIG.flushInterval);
  }

  recordEvent(event: string, value: number = 1, properties?: Record<string, any>) {
    this.metrics.push({
      event,
      value,
      timestamp: Date.now(),
      userId: this.getUserId(),
      properties
    });

    if (this.metrics.length >= MONITORING_CONFIG.batchSize) {
      this.flush();
    }
  }

  private async flush() {
    if (this.metrics.length === 0) return;

    const metricsToSend = [...this.metrics];
    this.metrics = [];

    try {
      await this.sendMetrics(metricsToSend);
    } catch (error) {
      console.warn('Failed to send business metrics:', error);
      // Re-add metrics for retry (with limit)
      if (this.metrics.length < MONITORING_CONFIG.batchSize * 2) {
        this.metrics.unshift(...metricsToSend);
      }
    }
  }

  private async sendMetrics(metrics: BusinessMetric[], retryCount = 0): Promise<void> {
    try {
      const response = await fetch(MONITORING_CONFIG.endpoints.collector, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        },
        body: JSON.stringify({
          type: 'business',
          data: metrics,
          sessionId: this.getSessionId(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      if (retryCount < MONITORING_CONFIG.maxRetries) {
        await new Promise(resolve =>
          setTimeout(resolve, MONITORING_CONFIG.retryDelay * Math.pow(2, retryCount))
        );
        return this.sendMetrics(metrics, retryCount + 1);
      }
      throw error;
    }
  }

  private getUserId(): string | undefined {
    // Get user ID from auth context or localStorage
    return localStorage.getItem('user_id') || undefined;
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('monitoring_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('monitoring_session_id', sessionId);
    }
    return sessionId;
  }

  destroy() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    
    // Final flush
    this.flush();
  }
}

// Singleton instances
let performanceMonitor: PerformanceMonitor | null = null;
let errorTracker: ErrorTracker | null = null;
let businessMetricsTracker: BusinessMetricsTracker | null = null;

// Initialize monitoring
export const initializeMonitoring = () => {
  if (typeof window === 'undefined') return;

  performanceMonitor = new PerformanceMonitor();
  errorTracker = new ErrorTracker();
  businessMetricsTracker = new BusinessMetricsTracker();

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    performanceMonitor?.destroy();
    errorTracker?.destroy();
    businessMetricsTracker?.destroy();
  });
};

// Export monitoring API
export const monitoring = {
  // Performance monitoring
  startTimer: (name: string) => performanceMonitor?.startTimer(name) || (() => {}),
  recordMetric: (name: string, value: number, tags?: Record<string, string>) => 
    performanceMonitor?.recordCustomMetric(name, value, tags),
  
  // Error tracking
  captureException: (error: Error, context?: Record<string, any>, severity?: ErrorReport['severity']) =>
    errorTracker?.captureException(error, context, severity),
  captureMessage: (message: string, context?: Record<string, any>, severity?: ErrorReport['severity']) =>
    errorTracker?.captureMessage(message, context, severity),
  
  // Business metrics
  trackEvent: (event: string, value?: number, properties?: Record<string, any>) =>
    businessMetricsTracker?.recordEvent(event, value, properties),
  
  // Utility
  isEnabled: () => MONITORING_CONFIG.enabled
};

export default monitoring;
