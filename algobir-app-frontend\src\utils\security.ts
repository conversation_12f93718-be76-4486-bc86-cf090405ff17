/**
 * Security Utilities - Comprehensive security functions
 * Input validation, sanitization, XSS prevention, CSRF protection
 */

// Input sanitization functions
export const sanitizeString = (input: string): string => {
  if (typeof input !== 'string') return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .replace(/data:/gi, '') // Remove data: protocol
    .slice(0, 1000); // Limit length
};

export const sanitizeEmail = (email: string): string => {
  if (typeof email !== 'string') return '';
  
  return email
    .trim()
    .toLowerCase()
    .replace(/[^\w@.-]/g, '') // Only allow word chars, @, ., -
    .slice(0, 254); // RFC 5321 limit
};

export const sanitizeUsername = (username: string): string => {
  if (typeof username !== 'string') return '';
  
  return username
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9_-]/g, '') // Only alphanumeric, underscore, hyphen
    .slice(0, 30);
};

export const sanitizeNumeric = (value: string | number): number => {
  if (typeof value === 'number') return Math.max(0, value);
  if (typeof value !== 'string') return 0;
  
  const cleaned = value.replace(/[^\d.-]/g, '');
  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : Math.max(0, parsed);
};

// Validation functions
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254;
};

export const isValidUsername = (username: string): boolean => {
  const usernameRegex = /^[a-zA-Z0-9_-]{3,30}$/;
  return usernameRegex.test(username);
};

export const isValidPassword = (password: string): boolean => {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,128}$/;
  return passwordRegex.test(password);
};

export const isValidSymbol = (symbol: string): boolean => {
  const symbolRegex = /^[A-Z]{2,10}$/;
  return symbolRegex.test(symbol);
};

export const isValidPrice = (price: number): boolean => {
  return typeof price === 'number' && price > 0 && price < 999999 && !isNaN(price);
};

export const isValidQuantity = (quantity: number): boolean => {
  return typeof quantity === 'number' && quantity > 0 && quantity < 999999 && !isNaN(quantity);
};

// XSS Prevention
export const escapeHtml = (unsafe: string): string => {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
};

export const stripHtml = (html: string): string => {
  const tmp = document.createElement("DIV");
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || "";
};

// CSRF Protection
export const generateCSRFToken = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

export const setCSRFToken = (token: string): void => {
  sessionStorage.setItem('csrf_token', token);
};

export const getCSRFToken = (): string | null => {
  return sessionStorage.getItem('csrf_token');
};

export const validateCSRFToken = (token: string): boolean => {
  const storedToken = getCSRFToken();
  return storedToken !== null && storedToken === token;
};

// Content Security Policy helpers
export const isAllowedURL = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const allowedDomains = [
      'algobir.com',
      'panel.algobir.com',
      'algobir.vercel.app',
      'localhost',
      '127.0.0.1'
    ];
    
    return allowedDomains.some(domain => 
      urlObj.hostname === domain || urlObj.hostname.endsWith(`.${domain}`)
    );
  } catch {
    return false;
  }
};

// Rate limiting helpers (client-side)
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

export const checkRateLimit = (
  key: string, 
  maxRequests: number = 10, 
  windowMs: number = 60000
): boolean => {
  const now = Date.now();
  const entry = rateLimitStore.get(key);
  
  if (!entry || now > entry.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (entry.count >= maxRequests) {
    return false;
  }
  
  entry.count++;
  return true;
};

// Secure random string generation
export const generateSecureRandomString = (length: number = 32): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  
  return Array.from(array, byte => chars[byte % chars.length]).join('');
};

// JWT token validation (client-side basic checks)
export const isValidJWTFormat = (token: string): boolean => {
  const parts = token.split('.');
  if (parts.length !== 3) return false;
  
  try {
    // Check if parts are valid base64
    parts.forEach(part => {
      atob(part.replace(/-/g, '+').replace(/_/g, '/'));
    });
    return true;
  } catch {
    return false;
  }
};

export const getJWTPayload = (token: string): any | null => {
  if (!isValidJWTFormat(token)) return null;
  
  try {
    const payload = token.split('.')[1];
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decoded);
  } catch {
    return null;
  }
};

export const isJWTExpired = (token: string): boolean => {
  const payload = getJWTPayload(token);
  if (!payload || !payload.exp) return true;
  
  return Date.now() >= payload.exp * 1000;
};

// Form validation schemas
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean;
  message?: string;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export const validateForm = (data: Record<string, any>, schema: ValidationSchema): {
  isValid: boolean;
  errors: Record<string, string>;
} => {
  const errors: Record<string, string> = {};
  
  for (const [field, rule] of Object.entries(schema)) {
    const value = data[field];
    
    if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
      errors[field] = rule.message || `${field} is required`;
      continue;
    }
    
    if (value && typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        errors[field] = rule.message || `${field} must be at least ${rule.minLength} characters`;
        continue;
      }
      
      if (rule.maxLength && value.length > rule.maxLength) {
        errors[field] = rule.message || `${field} must be no more than ${rule.maxLength} characters`;
        continue;
      }
      
      if (rule.pattern && !rule.pattern.test(value)) {
        errors[field] = rule.message || `${field} format is invalid`;
        continue;
      }
    }
    
    if (rule.custom && !rule.custom(value)) {
      errors[field] = rule.message || `${field} is invalid`;
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Common validation schemas
export const commonSchemas = {
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    maxLength: 254,
    message: 'Please enter a valid email address'
  },
  password: {
    required: true,
    minLength: 8,
    maxLength: 128,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,128}$/,
    message: 'Password must be 8-128 characters with uppercase, lowercase, and number'
  },
  username: {
    required: true,
    minLength: 3,
    maxLength: 30,
    pattern: /^[a-zA-Z0-9_-]+$/,
    message: 'Username must be 3-30 characters, alphanumeric with _ or -'
  },
  symbol: {
    required: true,
    pattern: /^[A-Z]{2,10}$/,
    message: 'Symbol must be 2-10 uppercase letters'
  },
  price: {
    required: true,
    custom: (value: any) => isValidPrice(parseFloat(value)),
    message: 'Price must be a positive number'
  },
  quantity: {
    required: true,
    custom: (value: any) => isValidQuantity(parseFloat(value)),
    message: 'Quantity must be a positive number'
  }
};

// Security headers helper
export const getSecurityHeaders = (): Record<string, string> => {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
  };
};

export default {
  sanitizeString,
  sanitizeEmail,
  sanitizeUsername,
  sanitizeNumeric,
  isValidEmail,
  isValidUsername,
  isValidPassword,
  isValidSymbol,
  isValidPrice,
  isValidQuantity,
  escapeHtml,
  stripHtml,
  generateCSRFToken,
  setCSRFToken,
  getCSRFToken,
  validateCSRFToken,
  isAllowedURL,
  checkRateLimit,
  generateSecureRandomString,
  isValidJWTFormat,
  getJWTPayload,
  isJWTExpired,
  validateForm,
  commonSchemas,
  getSecurityHeaders
};
