/**
 * Enhanced CORS Configuration - Security optimized
 * Provides strict origin validation and security headers
 */

// Environment-based allowed origins
const getAllowedOrigins = (): string[] => {
  const isDevelopment = Deno.env.get('ENVIRONMENT') === 'development';

  const productionOrigins = [
    'https://panel.algobir.com',
    'https://algobir.com',
    'https://algobir-v2-app.vercel.app'
  ];

  const developmentOrigins = [
    'http://localhost:3000',
    'http://localhost:5173',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:5173'
  ];

  return isDevelopment
    ? [...productionOrigins, ...developmentOrigins]
    : productionOrigins;
};

// Rate limiting for CORS preflight requests
const corsRateLimitMap = new Map<string, { count: number; resetTime: number }>();
const CORS_RATE_LIMIT = 100; // requests per minute
const CORS_RATE_WINDOW = 60 * 1000; // 1 minute

const checkCorsRateLimit = (origin: string): boolean => {
  const now = Date.now();
  const entry = corsRateLimitMap.get(origin);

  if (!entry || now > entry.resetTime) {
    corsRateLimitMap.set(origin, { count: 1, resetTime: now + CORS_RATE_WINDOW });
    return true;
  }

  if (entry.count >= CORS_RATE_LIMIT) {
    return false;
  }

  entry.count++;
  return true;
};

// Enhanced CORS headers generation with security features
export function getCorsHeaders(requestHeaders: Headers): Headers {
  const origin = requestHeaders.get('Origin') || '';
  const userAgent = requestHeaders.get('User-Agent') || '';
  const headers = new Headers();

  // Get allowed origins based on environment
  const allowedOrigins = getAllowedOrigins();

  // Validate origin
  const isValidOrigin = allowedOrigins.includes(origin);

  // Check rate limit for this origin
  if (origin && !checkCorsRateLimit(origin)) {
    console.warn(`[CORS] Rate limit exceeded for origin: ${origin}`);
    // Don't set CORS headers for rate-limited origins
    return headers;
  }

  // Log suspicious requests
  if (origin && !isValidOrigin) {
    console.warn(`[CORS] Blocked request from unauthorized origin: ${origin}, User-Agent: ${userAgent}`);
  }

  // Set CORS headers only for valid origins
  if (isValidOrigin) {
    headers.set('Access-Control-Allow-Origin', origin);
    headers.set('Access-Control-Allow-Credentials', 'true');
  } else if (!origin) {
    // For requests without origin (like direct API calls), allow first production origin
    headers.set('Access-Control-Allow-Origin', allowedOrigins[0]);
  }

  // Enhanced security headers
  headers.set('Access-Control-Allow-Headers',
    'authorization, x-client-info, apikey, content-type, x-csrf-token, x-requested-with'
  );
  headers.set('Access-Control-Allow-Methods', 'POST, GET, OPTIONS, PUT, DELETE');
  headers.set('Access-Control-Max-Age', '86400'); // 24 hours
  headers.set('Access-Control-Expose-Headers', 'x-ratelimit-remaining, x-ratelimit-reset');

  // Additional security headers
  headers.set('X-Content-Type-Options', 'nosniff');
  headers.set('X-Frame-Options', 'DENY');
  headers.set('X-XSS-Protection', '1; mode=block');
  headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // Vary header for proper caching
  headers.set('Vary', 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers');

  return headers;
}

// Validate request origin and method
export function validateRequest(request: Request): {
  isValid: boolean;
  reason?: string;
} {
  const origin = request.headers.get('Origin');
  const method = request.method;
  const contentType = request.headers.get('Content-Type');

  // Check if origin is allowed
  if (origin && !getAllowedOrigins().includes(origin)) {
    return { isValid: false, reason: 'Invalid origin' };
  }

  // Validate HTTP method
  const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
  if (!allowedMethods.includes(method)) {
    return { isValid: false, reason: 'Invalid HTTP method' };
  }

  // Validate content type for POST/PUT requests
  if (['POST', 'PUT'].includes(method)) {
    if (!contentType || !contentType.includes('application/json')) {
      return { isValid: false, reason: 'Invalid content type' };
    }
  }

  return { isValid: true };
}

// Create secure response with CORS headers
export function createSecureResponse(
  body: any,
  status: number = 200,
  requestHeaders: Headers,
  additionalHeaders: Record<string, string> = {}
): Response {
  const corsHeaders = getCorsHeaders(requestHeaders);

  // Add additional headers
  Object.entries(additionalHeaders).forEach(([key, value]) => {
    corsHeaders.set(key, value);
  });

  // Ensure content type is set
  if (!corsHeaders.has('Content-Type')) {
    corsHeaders.set('Content-Type', 'application/json');
  }

  return new Response(
    typeof body === 'string' ? body : JSON.stringify(body),
    {
      status,
      headers: corsHeaders
    }
  );
}

// Legacy export for backward compatibility (deprecated)
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE',
}; 