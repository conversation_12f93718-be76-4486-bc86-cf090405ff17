import { useCallback } from 'react';
import { useToast } from '@chakra-ui/react';

/**
 * Error types for better categorization
 */
export type ErrorType = 
  | 'network'
  | 'authentication'
  | 'authorization'
  | 'validation'
  | 'server'
  | 'client'
  | 'unknown';

/**
 * Structured error information
 */
export interface ErrorInfo {
  type: ErrorType;
  message: string;
  code?: string | number;
  details?: any;
  timestamp: number;
  context?: string;
}

/**
 * Error handling configuration
 */
export interface ErrorHandlerConfig {
  showToast?: boolean;
  logToConsole?: boolean;
  reportToService?: boolean;
  fallbackMessage?: string;
  context?: string;
}

/**
 * Error handler return type
 */
export interface ErrorHandlerReturn {
  handleError: (error: any, config?: Partial<ErrorHandlerConfig>) => ErrorInfo;
  clearError: () => void;
  lastError: ErrorInfo | null;
}

/**
 * Standardized error handling hook
 * Provides consistent error processing, logging, and user feedback
 */
export function useErrorHandler(defaultConfig: ErrorHandlerConfig = {}): ErrorHandlerReturn {
  const toast = useToast();
  
  const {
    showToast = true,
    logToConsole = true,
    reportToService = false,
    fallbackMessage = 'Beklenmeyen bir hata oluştu',
    context = 'Unknown'
  } = defaultConfig;

  // Determine error type based on error properties
  const determineErrorType = useCallback((error: any): ErrorType => {
    if (!error) return 'unknown';

    // Network errors
    if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
      return 'network';
    }

    // Supabase specific errors
    if (error.code) {
      switch (error.code) {
        case 'PGRST301':
        case 'PGRST302':
          return 'authentication';
        case 'PGRST116':
          return 'authorization';
        case '23505':
        case '23503':
        case '23502':
          return 'validation';
        default:
          if (error.code.startsWith('PGRST')) {
            return 'server';
          }
      }
    }

    // HTTP status codes
    if (error.status || error.statusCode) {
      const status = error.status || error.statusCode;
      if (status >= 400 && status < 500) {
        if (status === 401) return 'authentication';
        if (status === 403) return 'authorization';
        if (status === 422) return 'validation';
        return 'client';
      }
      if (status >= 500) return 'server';
    }

    // JavaScript errors
    if (error instanceof TypeError) return 'client';
    if (error instanceof ReferenceError) return 'client';

    return 'unknown';
  }, []);

  // Get user-friendly message based on error type
  const getUserFriendlyMessage = useCallback((errorInfo: ErrorInfo): string => {
    const { type, message, code } = errorInfo;

    switch (type) {
      case 'network':
        return 'İnternet bağlantınızı kontrol edin ve tekrar deneyin.';
      case 'authentication':
        return 'Oturum süreniz dolmuş. Lütfen tekrar giriş yapın.';
      case 'authorization':
        return 'Bu işlem için yetkiniz bulunmuyor.';
      case 'validation':
        return 'Girdiğiniz bilgileri kontrol edin ve tekrar deneyin.';
      case 'server':
        return 'Sunucu hatası oluştu. Lütfen daha sonra tekrar deneyin.';
      case 'client':
        return 'Bir hata oluştu. Sayfayı yenileyip tekrar deneyin.';
      default:
        // Try to extract meaningful message from error
        if (message && message.length < 100) {
          return message;
        }
        return fallbackMessage;
    }
  }, [fallbackMessage]);

  // Main error handling function
  const handleError = useCallback((
    error: any, 
    config: Partial<ErrorHandlerConfig> = {}
  ): ErrorInfo => {
    const finalConfig = { ...defaultConfig, ...config };
    
    const errorInfo: ErrorInfo = {
      type: determineErrorType(error),
      message: error?.message || error?.toString() || 'Unknown error',
      code: error?.code || error?.status || error?.statusCode,
      details: error,
      timestamp: Date.now(),
      context: finalConfig.context
    };

    // Log to console if enabled
    if (finalConfig.logToConsole) {
      console.group(`🚨 Error in ${errorInfo.context}`);
      console.error('Error Type:', errorInfo.type);
      console.error('Message:', errorInfo.message);
      console.error('Code:', errorInfo.code);
      console.error('Details:', errorInfo.details);
      console.error('Timestamp:', new Date(errorInfo.timestamp).toISOString());
      console.groupEnd();
    }

    // Show toast notification if enabled
    if (finalConfig.showToast) {
      const userMessage = getUserFriendlyMessage(errorInfo);
      
      toast({
        title: 'Hata',
        description: userMessage,
        status: 'error',
        duration: errorInfo.type === 'network' ? 8000 : 5000,
        isClosable: true,
        position: 'top-right'
      });
    }

    // Report to error service if enabled
    if (finalConfig.reportToService) {
      // This would integrate with error reporting services like Sentry
      console.log('Would report to error service:', errorInfo);
    }

    return errorInfo;
  }, [defaultConfig, determineErrorType, getUserFriendlyMessage, toast]);

  // Clear error state
  const clearError = useCallback(() => {
    // This could be extended to clear error state if we maintain it
    console.log('Error cleared');
  }, []);

  return {
    handleError,
    clearError,
    lastError: null // This could be extended to maintain error state
  };
}

/**
 * Specialized error handlers for common scenarios
 */

// Database operation errors
export function useDatabaseErrorHandler() {
  return useErrorHandler({
    context: 'Database Operation',
    showToast: true,
    logToConsole: true,
    fallbackMessage: 'Veritabanı işlemi sırasında hata oluştu'
  });
}

// API request errors
export function useApiErrorHandler() {
  return useErrorHandler({
    context: 'API Request',
    showToast: true,
    logToConsole: true,
    fallbackMessage: 'API isteği sırasında hata oluştu'
  });
}

// Form validation errors
export function useFormErrorHandler() {
  return useErrorHandler({
    context: 'Form Validation',
    showToast: false, // Forms usually handle their own error display
    logToConsole: true,
    fallbackMessage: 'Form doğrulama hatası'
  });
}
