import { ReactNode } from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { Center, Spinner, Text, VStack } from '@chakra-ui/react';

interface ProtectedRouteProps {
  children?: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { session, loading } = useAuth();
  const location = useLocation();

  // AuthContext yüklenene kadar bekle
  if (loading) {
    return (
      <Center minH="100vh">
        <VStack spacing={4}>
          <Spinner size="xl" color="brand.500" thickness="4px" />
          <Text>Oturum kontrol ediliyor...</Text>
        </VStack>
      </Center>
    );
  }

  // Oturum yoksa login sayfasına yönlendir
  // Şu anki konumu state olarak geçir, böylece giriş sonrası geri dönülebilir
  if (!session) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Oturum süresi kontrolü - expires_at bir sayıdır (Unix timestamp)
  const sessionExpiry = session.expires_at ? new Date((session.expires_at as number) * 1000) : null;
  const now = new Date();
  
  if (sessionExpiry && sessionExpiry <= now) {
    // Oturum süresi dolmuş, login sayfasına yönlendir
    return <Navigate to="/login" state={{ from: location, expired: true }} replace />;
  }

  // Oturum varsa ya children'ı göster ya da Outlet'i render et
  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute; 