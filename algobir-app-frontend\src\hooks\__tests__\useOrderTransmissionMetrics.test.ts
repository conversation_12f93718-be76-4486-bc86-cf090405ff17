import { renderHook, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import useOrderTransmissionMetrics from '../useOrderTransmissionMetrics';

// Mock Supabase client
const mockSupabase = {
  rpc: vi.fn(),
  channel: vi.fn(),
  removeChannel: vi.fn()
};

// Mock channel
const mockChannel = {
  on: vi.fn().mockReturnThis(),
  subscribe: vi.fn().mockReturnValue(Promise.resolve({ status: 'SUBSCRIBED' }))
};

vi.mock('../../supabaseClient', () => ({
  supabase: mockSupabase
}));

describe('useOrderTransmissionMetrics', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockSupabase.channel.mockReturnValue(mockChannel);
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  it('should fetch metrics and stats on mount', async () => {
    const mockMetrics = [
      {
        id: '1',
        trade_id: 123,
        signal_type: 'BUY',
        symbol: 'BTCUSDT',
        order_side: 'BUY',
        json_parsing_time_ms: 5.2,
        transformation_time_ms: 15.8,
        webhook_delivery_time_ms: 45.3,
        total_processing_time_ms: 66.3,
        signal_source: 'solo-robot',
        processing_status: 'success',
        created_at: '2024-01-15T10:30:00Z',
        signal_received_at: '2024-01-15T10:30:00Z',
        processing_completed_at: '2024-01-15T10:30:00Z'
      }
    ];

    const mockStats = {
      total_signals: 100,
      avg_json_parsing_time_ms: 5.5,
      avg_transformation_time_ms: 18.2,
      avg_webhook_delivery_time_ms: 42.1,
      avg_total_processing_time_ms: 65.8,
      p95_total_processing_time_ms: 120.5,
      p99_total_processing_time_ms: 180.2,
      success_rate: 98.5,
      signals_per_minute: 2.3,
      fastest_processing_time_ms: 25.1,
      slowest_processing_time_ms: 250.8,
      solo_robot_count: 60,
      bro_robot_count: 40
    };

    mockSupabase.rpc
      .mockResolvedValueOnce({ data: mockMetrics, error: null })
      .mockResolvedValueOnce({ data: [mockStats], error: null });

    const { result } = renderHook(() => useOrderTransmissionMetrics({
      timeRangeHours: 24,
      enabled: true,
      autoRefresh: false
    }));

    // Initially loading
    expect(result.current.loading).toBe(true);
    expect(result.current.metrics).toEqual([]);
    expect(result.current.stats).toBe(null);

    // Wait for data to load
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.metrics).toEqual(mockMetrics);
    expect(result.current.stats).toEqual(mockStats);
    expect(result.current.error).toBe(null);
    expect(mockSupabase.rpc).toHaveBeenCalledTimes(2);
  });

  it('should handle fetch errors gracefully', async () => {
    const mockError = new Error('Database connection failed');
    mockSupabase.rpc.mockRejectedValue(mockError);

    const { result } = renderHook(() => useOrderTransmissionMetrics({
      enabled: true,
      autoRefresh: false
    }));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe('Database connection failed');
    expect(result.current.metrics).toEqual([]);
    expect(result.current.stats).toBe(null);
  });

  it('should set up real-time subscription when enabled', () => {
    renderHook(() => useOrderTransmissionMetrics({
      enabled: true,
      autoRefresh: true
    }));

    expect(mockSupabase.channel).toHaveBeenCalledWith('order_transmission_metrics_realtime');
    expect(mockChannel.on).toHaveBeenCalledWith(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'order_transmission_metrics'
      },
      expect.any(Function)
    );
    expect(mockChannel.subscribe).toHaveBeenCalled();
  });

  it('should not fetch data when disabled', () => {
    renderHook(() => useOrderTransmissionMetrics({
      enabled: false
    }));

    expect(mockSupabase.rpc).not.toHaveBeenCalled();
    expect(mockSupabase.channel).not.toHaveBeenCalled();
  });

  it('should filter by signal source when specified', async () => {
    mockSupabase.rpc.mockResolvedValue({ data: [], error: null });

    renderHook(() => useOrderTransmissionMetrics({
      signalSource: 'solo-robot',
      enabled: true,
      autoRefresh: false
    }));

    await waitFor(() => {
      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_order_transmission_metrics',
        expect.objectContaining({
          p_signal_source: 'solo-robot'
        })
      );
    });
  });

  it('should refetch data when refetch is called', async () => {
    mockSupabase.rpc.mockResolvedValue({ data: [], error: null });

    const { result } = renderHook(() => useOrderTransmissionMetrics({
      enabled: true,
      autoRefresh: false
    }));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Clear previous calls
    mockSupabase.rpc.mockClear();

    // Call refetch
    await result.current.refetch();

    expect(mockSupabase.rpc).toHaveBeenCalledTimes(2); // metrics + stats
  });

  it('should handle real-time updates', async () => {
    let realtimeCallback: Function;
    mockChannel.on.mockImplementation((_event, _config, callback) => {
      realtimeCallback = callback;
      return mockChannel;
    });

    mockSupabase.rpc.mockResolvedValue({ data: [], error: null });

    const { result } = renderHook(() => useOrderTransmissionMetrics({
      enabled: true,
      autoRefresh: true
    }));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Clear previous RPC calls
    mockSupabase.rpc.mockClear();

    // Simulate real-time update
    const mockPayload = {
      eventType: 'INSERT',
      new: {
        id: '2',
        symbol: 'ETHUSDT',
        total_processing_time_ms: 75.2
      }
    };

    realtimeCallback!(mockPayload);

    // Should trigger refetch
    await waitFor(() => {
      expect(mockSupabase.rpc).toHaveBeenCalled();
    });
  });

  it('should clean up subscriptions on unmount', () => {
    const { unmount } = renderHook(() => useOrderTransmissionMetrics({
      enabled: true
    }));

    unmount();

    expect(mockSupabase.removeChannel).toHaveBeenCalledWith(mockChannel);
  });

  it('should respect time range parameter', async () => {
    mockSupabase.rpc.mockResolvedValue({ data: [], error: null });

    renderHook(() => useOrderTransmissionMetrics({
      timeRangeHours: 6,
      enabled: true,
      autoRefresh: false
    }));

    await waitFor(() => {
      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_order_transmission_metrics',
        expect.objectContaining({
          p_time_range_hours: 6
        })
      );
    });
  });

  it('should respect limit parameter', async () => {
    mockSupabase.rpc.mockResolvedValue({ data: [], error: null });

    renderHook(() => useOrderTransmissionMetrics({
      limit: 50,
      enabled: true,
      autoRefresh: false
    }));

    await waitFor(() => {
      expect(mockSupabase.rpc).toHaveBeenCalledWith(
        'get_order_transmission_metrics',
        expect.objectContaining({
          p_limit: 50
        })
      );
    });
  });

  it('should update connection status based on subscription', async () => {
    let statusCallback: Function;
    mockChannel.subscribe.mockImplementation((callback) => {
      statusCallback = callback;
      return Promise.resolve();
    });

    const { result } = renderHook(() => useOrderTransmissionMetrics({
      enabled: true
    }));

    // Initially not connected
    expect(result.current.isConnected).toBe(false);

    // Simulate successful subscription
    statusCallback!('SUBSCRIBED');

    await waitFor(() => {
      expect(result.current.isConnected).toBe(true);
    });

    // Simulate disconnection
    statusCallback!('CLOSED');

    await waitFor(() => {
      expect(result.current.isConnected).toBe(false);
    });
  });
});
