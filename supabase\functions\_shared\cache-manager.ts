// Intelligent caching layer for frequently accessed data
// Reduces database hits for user settings, robot configurations, and API credentials

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

export interface CacheOptions {
  ttl?: number; // Default TTL in milliseconds
  maxSize?: number; // Maximum number of entries
  cleanupInterval?: number; // Cleanup interval in milliseconds
}

export interface UserSettings {
  id: string;
  is_active: boolean;
  custom_webhook_url: string;
  encrypted_api_key: string;
  encrypted_token: string;
  investment_amount: number;
}

export interface RobotConfig {
  id: string;
  name: string;
  investment_amount: number;
  is_active: boolean;
  seller_id: string;
}

const DEFAULT_OPTIONS: CacheOptions = {
  ttl: 5 * 60 * 1000, // 5 minutes default TTL
  maxSize: 1000, // Maximum 1000 entries
  cleanupInterval: 2 * 60 * 1000 // Cleanup every 2 minutes
};

/**
 * High-performance in-memory cache with TTL and automatic cleanup
 */
export class InMemoryCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private options: CacheOptions;
  private cleanupTimer: number | null = null;

  constructor(options: Partial<CacheOptions> = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
    this.startCleanup();
  }

  /**
   * Get value from cache
   */
  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Set value in cache with optional TTL override
   */
  set(key: string, value: T, ttl?: number): void {
    const entryTtl = ttl || this.options.ttl!;
    
    // If cache is at max size, remove oldest entry
    if (this.cache.size >= this.options.maxSize!) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data: value,
      timestamp: Date.now(),
      ttl: entryTtl
    });
  }

  /**
   * Delete value from cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; maxSize: number; hitRate?: number } {
    return {
      size: this.cache.size,
      maxSize: this.options.maxSize!
    };
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`[CACHE] Cleaned up ${expiredKeys.length} expired entries`);
    }
  }

  /**
   * Start automatic cleanup
   */
  private startCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.options.cleanupInterval!);
  }

  /**
   * Stop automatic cleanup
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clear();
  }
}

// Global cache instances
const userSettingsCache = new InMemoryCache<UserSettings>({
  ttl: 3 * 60 * 1000, // 3 minutes for user settings
  maxSize: 500
});

const robotConfigCache = new InMemoryCache<RobotConfig>({
  ttl: 10 * 60 * 1000, // 10 minutes for robot configs (less frequent changes)
  maxSize: 200
});

const subscribersCache = new InMemoryCache<string[]>({
  ttl: 2 * 60 * 1000, // 2 minutes for subscriber lists
  maxSize: 100
});

/**
 * Cache manager with intelligent caching strategies
 */
export class CacheManager {
  /**
   * Get user settings with caching
   */
  static async getUserSettings(supabase: any, userId: string): Promise<UserSettings | null> {
    const cacheKey = `user_settings:${userId}`;
    
    // Try cache first
    let userSettings = userSettingsCache.get(cacheKey);
    if (userSettings) {
      console.log(`[CACHE] User settings cache hit for ${userId}`);
      return userSettings;
    }

    // Cache miss - fetch from database
    console.log(`[CACHE] User settings cache miss for ${userId}, fetching from DB`);
    const startTime = performance.now();
    
    const { data, error } = await supabase
      .from('user_settings')
      .select(`
        id,
        is_active,
        custom_webhook_url,
        encrypted_api_key,
        encrypted_token,
        investment_amount
      `)
      .eq('id', userId)
      .single();

    const fetchTime = performance.now() - startTime;
    console.log(`[CACHE] User settings DB fetch took ${fetchTime.toFixed(2)}ms`);

    if (error || !data) {
      console.error(`[CACHE] Failed to fetch user settings for ${userId}:`, error);
      return null;
    }

    // Cache the result
    userSettingsCache.set(cacheKey, data);
    return data;
  }

  /**
   * Get multiple user settings with batch caching
   */
  static async getBatchUserSettings(supabase: any, userIds: string[]): Promise<Map<string, UserSettings>> {
    const result = new Map<string, UserSettings>();
    const uncachedIds: string[] = [];

    // Check cache for each user
    for (const userId of userIds) {
      const cacheKey = `user_settings:${userId}`;
      const cached = userSettingsCache.get(cacheKey);
      
      if (cached) {
        result.set(userId, cached);
      } else {
        uncachedIds.push(userId);
      }
    }

    console.log(`[CACHE] User settings batch: ${result.size} cache hits, ${uncachedIds.length} cache misses`);

    // Fetch uncached users from database
    if (uncachedIds.length > 0) {
      const startTime = performance.now();
      
      const { data, error } = await supabase
        .from('user_settings')
        .select(`
          id,
          is_active,
          custom_webhook_url,
          encrypted_api_key,
          encrypted_token,
          investment_amount
        `)
        .in('id', uncachedIds);

      const fetchTime = performance.now() - startTime;
      console.log(`[CACHE] Batch user settings DB fetch took ${fetchTime.toFixed(2)}ms for ${uncachedIds.length} users`);

      if (!error && data) {
        // Cache and add to result
        data.forEach((userSettings: UserSettings) => {
          const cacheKey = `user_settings:${userSettings.id}`;
          userSettingsCache.set(cacheKey, userSettings);
          result.set(userSettings.id, userSettings);
        });
      }
    }

    return result;
  }

  /**
   * Get robot configuration with caching
   */
  static async getRobotConfig(supabase: any, robotId: string): Promise<RobotConfig | null> {
    const cacheKey = `robot_config:${robotId}`;
    
    // Try cache first
    let robotConfig = robotConfigCache.get(cacheKey);
    if (robotConfig) {
      console.log(`[CACHE] Robot config cache hit for ${robotId}`);
      return robotConfig;
    }

    // Cache miss - fetch from database
    console.log(`[CACHE] Robot config cache miss for ${robotId}, fetching from DB`);
    const startTime = performance.now();
    
    const { data, error } = await supabase
      .from('robots')
      .select(`
        id,
        name,
        investment_amount,
        is_active,
        seller_id
      `)
      .eq('id', robotId)
      .single();

    const fetchTime = performance.now() - startTime;
    console.log(`[CACHE] Robot config DB fetch took ${fetchTime.toFixed(2)}ms`);

    if (error || !data) {
      console.error(`[CACHE] Failed to fetch robot config for ${robotId}:`, error);
      return null;
    }

    // Cache the result
    robotConfigCache.set(cacheKey, data);
    return data;
  }

  /**
   * Get robot subscribers with caching
   */
  static async getRobotSubscribers(supabase: any, robotId: string): Promise<string[]> {
    const cacheKey = `robot_subscribers:${robotId}`;
    
    // Try cache first
    let subscribers = subscribersCache.get(cacheKey);
    if (subscribers) {
      console.log(`[CACHE] Robot subscribers cache hit for ${robotId}`);
      return subscribers;
    }

    // Cache miss - fetch from database
    console.log(`[CACHE] Robot subscribers cache miss for ${robotId}, fetching from DB`);
    const startTime = performance.now();
    
    const { data, error } = await supabase
      .from('subscriptions')
      .select('user_id')
      .eq('robot_id', robotId)
      .eq('is_active', true)
      .eq('is_deleted', false);

    const fetchTime = performance.now() - startTime;
    console.log(`[CACHE] Robot subscribers DB fetch took ${fetchTime.toFixed(2)}ms`);

    if (error || !data) {
      console.error(`[CACHE] Failed to fetch robot subscribers for ${robotId}:`, error);
      return [];
    }

    const subscriberIds = data.map((sub: any) => sub.user_id);
    
    // Cache the result
    subscribersCache.set(cacheKey, subscriberIds);
    return subscriberIds;
  }

  /**
   * Invalidate user settings cache
   */
  static invalidateUserSettings(userId: string): void {
    const cacheKey = `user_settings:${userId}`;
    userSettingsCache.delete(cacheKey);
    console.log(`[CACHE] Invalidated user settings cache for ${userId}`);
  }

  /**
   * Invalidate robot config cache
   */
  static invalidateRobotConfig(robotId: string): void {
    const cacheKey = `robot_config:${robotId}`;
    robotConfigCache.delete(cacheKey);
    console.log(`[CACHE] Invalidated robot config cache for ${robotId}`);
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): any {
    return {
      userSettings: userSettingsCache.getStats(),
      robotConfig: robotConfigCache.getStats(),
      subscribers: subscribersCache.getStats()
    };
  }

  /**
   * Get user settings by webhook ID with caching
   */
  static async getUserSettingsByWebhookId(supabase: any, webhookId: string): Promise<UserSettings | null> {
    const cacheKey = `user_settings_webhook:${webhookId}`;

    // Try cache first
    let userSettings = userSettingsCache.get(cacheKey);
    if (userSettings) {
      console.log(`[CACHE] User settings by webhook cache hit for ${webhookId}`);
      return userSettings;
    }

    // Cache miss - fetch from database
    console.log(`[CACHE] User settings by webhook cache miss for ${webhookId}, fetching from DB`);
    const startTime = performance.now();

    const { data, error } = await supabase
      .from('user_settings')
      .select(`
        id,
        is_active,
        custom_webhook_url,
        encrypted_api_key,
        encrypted_token,
        investment_amount
      `)
      .eq('webhook_id', webhookId)
      .eq('is_active', true)
      .single();

    const fetchTime = performance.now() - startTime;
    console.log(`[CACHE] User settings by webhook DB fetch took ${fetchTime.toFixed(2)}ms`);

    if (error || !data) {
      console.error(`[CACHE] Failed to fetch user settings by webhook ${webhookId}:`, error);
      return null;
    }

    // Cache the result with both webhook and user ID keys
    userSettingsCache.set(cacheKey, data);
    userSettingsCache.set(`user_settings:${data.id}`, data);
    return data;
  }

  /**
   * Clear all caches
   */
  static clearAllCaches(): void {
    userSettingsCache.clear();
    robotConfigCache.clear();
    subscribersCache.clear();
    console.log('[CACHE] All caches cleared');
  }
}
