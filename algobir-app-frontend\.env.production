# Algobir Trading Platform - Production Environment Configuration
# Phase 3.2: Deployment Automation & CI/CD Enhancement

# Application Environment
NODE_ENV=production
VITE_APP_ENV=production
VITE_APP_VERSION=1.0.0
VITE_APP_BUILD_TIME=__BUILD_TIME__

# Supabase Configuration (Production)
VITE_SUPABASE_URL=https://fllklckmycxcgwhboiji.supabase.co
VITE_SUPABASE_ANON_KEY=__PRODUCTION_SUPABASE_ANON_KEY__
VITE_SUPABASE_SERVICE_ROLE_KEY=__PRODUCTION_SUPABASE_SERVICE_ROLE_KEY__

# API Configuration
VITE_API_BASE_URL=https://fllklckmycxcgwhboiji.supabase.co
VITE_API_TIMEOUT=15000
VITE_API_RETRY_ATTEMPTS=2

# Feature Flags (Production)
VITE_ENABLE_DEBUG_MODE=false
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_A_B_TESTING=false
VITE_PERFORMANCE_SAMPLE_RATE=0.05
VITE_ERROR_SAMPLE_RATE=1.0

# Security Configuration
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true
VITE_ENABLE_SECURE_COOKIES=true

# Performance Configuration
VITE_ENABLE_SERVICE_WORKER=true
VITE_ENABLE_CODE_SPLITTING=true
VITE_ENABLE_LAZY_LOADING=true
VITE_BUNDLE_ANALYZER=false

# Monitoring & Logging
VITE_LOG_LEVEL=error
VITE_ENABLE_CONSOLE_LOGS=false
VITE_ENABLE_NETWORK_LOGGING=false
VITE_ENABLE_PERFORMANCE_LOGS=true

# Trading Configuration (Production)
VITE_TRADING_MODE=live
VITE_MAX_CONCURRENT_TRADES=50
VITE_ORDER_TIMEOUT=15000
VITE_ENABLE_REAL_TIME_UPDATES=true

# WebSocket Configuration
VITE_WS_RECONNECT_ATTEMPTS=3
VITE_WS_RECONNECT_DELAY=2000
VITE_WS_HEARTBEAT_INTERVAL=30000

# Cache Configuration
VITE_CACHE_TTL=600000
VITE_ENABLE_OFFLINE_MODE=false
VITE_CACHE_STRATEGY=cache-first

# Development Tools (Production)
VITE_ENABLE_REACT_DEVTOOLS=false
VITE_ENABLE_REDUX_DEVTOOLS=false
VITE_ENABLE_WHYRENDER=false

# Error Handling
VITE_ERROR_BOUNDARY_FALLBACK=true
VITE_ENABLE_ERROR_OVERLAY=false
VITE_SENTRY_DSN=__PRODUCTION_SENTRY_DSN__

# Deployment Configuration
VITE_DEPLOYMENT_URL=https://algobir.vercel.app
VITE_DEPLOYMENT_BRANCH=main
VITE_DEPLOYMENT_COMMIT=__GIT_COMMIT_SHA__

# Third-party Integrations (Production)
VITE_GOOGLE_ANALYTICS_ID=__PRODUCTION_GA_ID__
VITE_HOTJAR_ID=__PRODUCTION_HOTJAR_ID__
VITE_INTERCOM_APP_ID=__PRODUCTION_INTERCOM_ID__

# Rate Limiting
VITE_API_RATE_LIMIT=500
VITE_API_RATE_WINDOW=3600000

# File Upload Configuration
VITE_MAX_FILE_SIZE=2097152
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Notification Configuration
VITE_ENABLE_PUSH_NOTIFICATIONS=true
VITE_ENABLE_EMAIL_NOTIFICATIONS=true
VITE_NOTIFICATION_SOUND=false

# Accessibility Configuration
VITE_ENABLE_HIGH_CONTRAST=true
VITE_ENABLE_REDUCED_MOTION=true
VITE_ENABLE_SCREEN_READER=true

# Internationalization
VITE_DEFAULT_LOCALE=en
VITE_SUPPORTED_LOCALES=en,tr,de,fr,es
VITE_ENABLE_RTL=false

# Build Configuration
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=true
VITE_BUILD_ANALYZE=false
VITE_BUILD_REPORT=false
