import React from 'react';
import {
  Box,
  Container,
  VStack,
  Heading,
  Text,
  useColorModeValue
} from '@chakra-ui/react';
import OrderTransmissionMonitoringWrapper from '../../components/admin/OrderTransmissionMonitoringWrapper';
import OrderTransmissionTestComponent from '../../components/admin/OrderTransmissionTestComponent';
import SystemMonitoringTest from '../../components/admin/SystemMonitoringTest';
import RealTimeServiceStatus from '../../components/admin/RealTimeServiceStatus';
import RealTimeSystemMetrics from '../../components/admin/RealTimeSystemMetrics';
import RealTimeUptimeMonitor from '../../components/admin/RealTimeUptimeMonitor';
import { useSystemMonitoring } from '../../hooks/useSystemMonitoring';

const OrderTransmissionTest: React.FC = () => {
  const bgGradient = useColorModeValue(
    'linear(to-br, gray.50, blue.50)',
    'linear(to-br, gray.900, blue.900)'
  );

  // Test the system monitoring hook
  const { data: systemData, loading: systemLoading } = useSystemMonitoring({
    refreshInterval: 15000,
    enabled: true
  });

  return (
    <Box
      minH="100vh"
      bgGradient={bgGradient}
      py={8}
    >
      <Container maxW="container.xl">
        <VStack spacing={8} align="stretch">
          <Box textAlign="center">
            <Heading size="lg" mb={2}>
              Real-Time System Monitoring Test
            </Heading>
            <Text color="gray.600">
              Testing the complete real-time monitoring system with live data integration
            </Text>
          </Box>

          {/* System Monitoring Test */}
          <SystemMonitoringTest />

          {/* Order Transmission Test */}
          <OrderTransmissionTestComponent />

          {/* Real-time Components */}
          {systemData && (
            <>
              <RealTimeServiceStatus
                services={systemData.services}
                loading={systemLoading}
                lastUpdate={new Date()}
                isConnected={true}
              />

              <RealTimeSystemMetrics
                metrics={systemData.metrics}
                loading={systemLoading}
                lastUpdate={new Date()}
              />

              <RealTimeUptimeMonitor
                uptime={systemData.uptime}
                incidents={systemData.incidents}
                loading={systemLoading}
                lastUpdate={new Date()}
              />
            </>
          )}

          {/* Order Transmission Monitoring */}
          <OrderTransmissionMonitoringWrapper
            timeRangeHours={24}
            autoRefresh={true}
          />
        </VStack>
      </Container>
    </Box>
  );
};

export default OrderTransmissionTest;
