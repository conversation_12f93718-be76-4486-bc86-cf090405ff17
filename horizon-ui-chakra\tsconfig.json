{ 
  "compilerOptions": {
    "typeRoots": ["node_modules/@types", "src/types"],
    "target": "es2016",
    "lib": ["dom", "dom.iterable", "esnext", "es2015.iterable"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "noImplicitAny": true,
    "noImplicitThis": true,
    "strictNullChecks": false,
    "downlevelIteration": true,
    "baseUrl": "src",
    "paths": {
      "@assets/*": ["./src/assetsassets/*"]
    },
  },
  "include": [
    "./src"
  ], 
}
