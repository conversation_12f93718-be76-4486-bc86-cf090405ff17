# ADR-003: Authentication and Authorization Strategy

## Status
**ACCEPTED** - 2025-01-30

## Context

The Algobir trading platform requires a robust authentication and authorization system that can handle:
- Secure user authentication with multiple providers
- Role-based access control for different user types
- API key management for trading integrations
- Session management and security
- Compliance with financial industry security standards
- Scalable permission system for future features

## Decision

We have implemented a comprehensive authentication and authorization strategy using Supabase Auth with custom extensions:

### Authentication Architecture

#### Primary Authentication: Supabase Auth
- **JWT-based Authentication**: Stateless token-based authentication
- **Multiple Providers**: Email/password, Google, GitHub, Discord
- **Secure Token Management**: Automatic token refresh and validation
- **Session Management**: Secure session handling with configurable expiration
- **Password Security**: Bcrypt hashing with configurable rounds

#### Authentication Flow
```typescript
// Authentication Context
interface AuthContext {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<AuthResponse>;
  signUp: (email: string, password: string, metadata?: UserMetadata) => Promise<AuthResponse>;
  signInWithProvider: (provider: Provider) => Promise<AuthResponse>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (password: string) => Promise<void>;
  updateProfile: (updates: ProfileUpdates) => Promise<void>;
}
```

### Authorization System

#### Role-Based Access Control (RBAC)
```typescript
enum UserRole {
  USER = 'user',           // Standard trading users
  PREMIUM = 'premium',     // Premium subscription users
  SELLER = 'seller',       // Robot signal providers
  ADMIN = 'admin',         // Platform administrators
  SUPERUSER = 'superuser'  // System administrators
}

interface UserPermissions {
  // Trading permissions
  canCreateSoloRobots: boolean;
  canCreateBroRobots: boolean;
  canSubscribeToRobots: boolean;
  canExecuteTrades: boolean;
  
  // Platform permissions
  canAccessMarketplace: boolean;
  canViewAnalytics: boolean;
  canManageSubscriptions: boolean;
  
  // Administrative permissions
  canAccessAdminPanel: boolean;
  canManageUsers: boolean;
  canViewSystemMetrics: boolean;
  canManageRobots: boolean;
  
  // API permissions
  canUsePublicAPI: boolean;
  canUsePrivateAPI: boolean;
  canManageAPIKeys: boolean;
}
```

#### Permission Matrix
```typescript
const ROLE_PERMISSIONS: Record<UserRole, UserPermissions> = {
  [UserRole.USER]: {
    canCreateSoloRobots: true,
    canCreateBroRobots: false,
    canSubscribeToRobots: true,
    canExecuteTrades: true,
    canAccessMarketplace: true,
    canViewAnalytics: true,
    canManageSubscriptions: true,
    canAccessAdminPanel: false,
    canManageUsers: false,
    canViewSystemMetrics: false,
    canManageRobots: false,
    canUsePublicAPI: true,
    canUsePrivateAPI: false,
    canManageAPIKeys: true
  },
  
  [UserRole.PREMIUM]: {
    // Inherits USER permissions plus:
    canCreateBroRobots: true,
    canUsePrivateAPI: true,
    // ... additional premium features
  },
  
  [UserRole.SELLER]: {
    // Inherits PREMIUM permissions plus:
    canManageRobots: true,
    // ... seller-specific features
  },
  
  [UserRole.ADMIN]: {
    // All permissions except superuser functions
    canAccessAdminPanel: true,
    canManageUsers: true,
    canViewSystemMetrics: true,
    // ... admin features
  },
  
  [UserRole.SUPERUSER]: {
    // All permissions
    // ... complete system access
  }
};
```

### Database-Level Security

#### Row Level Security (RLS) Policies
```sql
-- User profile access control
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Admin users can view all profiles
CREATE POLICY "Admins can view all profiles" ON profiles
  FOR SELECT USING (
    auth.jwt() ->> 'role' IN ('admin', 'superuser')
  );

-- Trading data access control
CREATE POLICY "Users can view own trades" ON trades
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view subscribed robot trades" ON trades
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM subscriptions 
      WHERE user_id = auth.uid() 
      AND robot_id = trades.robot_id 
      AND is_active = true
    )
  );

-- Robot management policies
CREATE POLICY "Users can manage own robots" ON robots
  FOR ALL USING (auth.uid() = seller_id);

CREATE POLICY "Users can view active robots" ON robots
  FOR SELECT USING (
    is_active = true OR 
    seller_id = auth.uid() OR
    auth.jwt() ->> 'role' IN ('admin', 'superuser')
  );
```

#### JWT Claims Enhancement
```sql
-- Function to add custom claims to JWT
CREATE OR REPLACE FUNCTION auth.get_user_claims(user_id UUID)
RETURNS JSONB AS $$
DECLARE
  user_role TEXT;
  user_permissions JSONB;
BEGIN
  -- Get user role from profiles table
  SELECT 
    COALESCE(metadata->>'role', 'user') INTO user_role
  FROM auth.users 
  WHERE id = user_id;
  
  -- Get user permissions based on role
  SELECT 
    CASE user_role
      WHEN 'admin' THEN '{"admin": true, "manage_users": true}'::JSONB
      WHEN 'superuser' THEN '{"admin": true, "superuser": true}'::JSONB
      ELSE '{}'::JSONB
    END INTO user_permissions;
  
  RETURN jsonb_build_object(
    'role', user_role,
    'permissions', user_permissions
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### API Key Management

#### Secure API Key Storage
```typescript
// API Key encryption and storage
interface APIKeyData {
  id: string;
  user_id: string;
  exchange: string;
  api_key_encrypted: string;
  api_secret_encrypted: string;
  permissions: string[];
  is_active: boolean;
  created_at: string;
  last_used_at?: string;
}

// Encryption service for API keys
class APIKeyService {
  static async storeAPIKey(
    userId: string,
    exchange: string,
    apiKey: string,
    apiSecret: string,
    permissions: string[]
  ): Promise<string> {
    const { data, error } = await supabase.rpc('encrypt_and_store_api_key', {
      user_id: userId,
      exchange: exchange,
      api_key: apiKey,
      api_secret: apiSecret,
      permissions: permissions
    });
    
    if (error) throw error;
    return data.key_id;
  }
  
  static async getDecryptedAPIKey(
    userId: string,
    keyId: string
  ): Promise<{ apiKey: string; apiSecret: string }> {
    const { data, error } = await supabase.rpc('decrypt_api_key', {
      user_id: userId,
      key_id: keyId
    });
    
    if (error) throw error;
    return {
      apiKey: data.api_key,
      apiSecret: data.api_secret
    };
  }
}
```

#### API Key Security Policies
```sql
-- API key access control
CREATE POLICY "Users can manage own API keys" ON api_keys
  FOR ALL USING (auth.uid() = user_id);

-- Audit trail for API key usage
CREATE TABLE api_key_audit (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  key_id UUID REFERENCES api_keys(id),
  action TEXT NOT NULL,
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMPTZ DEFAULT NOW()
);
```

### Session Management

#### Session Configuration
```typescript
// Session management configuration
const sessionConfig = {
  // JWT token expiration
  accessTokenExpiry: '1 hour',
  refreshTokenExpiry: '30 days',
  
  // Session security
  requireReauthentication: true,
  maxConcurrentSessions: 5,
  sessionTimeout: '24 hours',
  
  // Security features
  enableDeviceTracking: true,
  requireEmailVerification: true,
  enableTwoFactorAuth: false, // Future feature
  
  // Password policy
  passwordMinLength: 8,
  passwordRequireUppercase: true,
  passwordRequireLowercase: true,
  passwordRequireNumbers: true,
  passwordRequireSymbols: true
};
```

#### Session Monitoring
```typescript
// Session monitoring and security
interface SessionMonitoring {
  trackLoginAttempts: boolean;
  maxFailedAttempts: number;
  lockoutDuration: number;
  enableGeolocationTracking: boolean;
  alertOnSuspiciousActivity: boolean;
}

// Suspicious activity detection
const detectSuspiciousActivity = (session: Session, request: Request) => {
  const checks = [
    checkGeolocation(session, request),
    checkDeviceFingerprint(session, request),
    checkLoginFrequency(session),
    checkIPReputation(request.ip)
  ];
  
  return checks.some(check => check.isSuspicious);
};
```

### Authorization Middleware

#### Route Protection
```typescript
// Higher-order component for route protection
const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  requiredPermissions?: string[]
) => {
  return (props: P) => {
    const { user, loading } = useAuth();
    const permissions = usePermissions();
    
    if (loading) return <LoadingSpinner />;
    if (!user) return <Navigate to="/login" />;
    
    if (requiredPermissions) {
      const hasPermission = requiredPermissions.every(permission =>
        permissions[permission as keyof UserPermissions]
      );
      
      if (!hasPermission) {
        return <UnauthorizedPage />;
      }
    }
    
    return <Component {...props} />;
  };
};

// Usage example
const AdminPanel = withAuth(AdminPanelComponent, ['canAccessAdminPanel']);
const UserManagement = withAuth(UserManagementComponent, ['canManageUsers']);
```

#### API Route Protection
```typescript
// Edge Function authentication middleware
const withAuthMiddleware = (handler: EdgeFunctionHandler) => {
  return async (req: Request) => {
    try {
      // Extract JWT token
      const token = req.headers.get('Authorization')?.replace('Bearer ', '');
      if (!token) {
        return new Response('Unauthorized', { status: 401 });
      }
      
      // Verify JWT token
      const { data: user, error } = await supabase.auth.getUser(token);
      if (error || !user) {
        return new Response('Invalid token', { status: 401 });
      }
      
      // Add user context to request
      req.user = user;
      
      return handler(req);
    } catch (error) {
      return new Response('Authentication error', { status: 500 });
    }
  };
};
```

## Rationale

### Design Decisions

#### Supabase Auth Choice
- **Proven Security**: Battle-tested authentication system
- **JWT Standards**: Industry-standard token-based authentication
- **Multiple Providers**: Support for social login providers
- **Scalability**: Handles authentication at scale
- **Integration**: Seamless integration with PostgreSQL RLS
- **Developer Experience**: Excellent documentation and tooling

#### Role-Based Access Control
- **Flexibility**: Easy to add new roles and permissions
- **Granularity**: Fine-grained permission control
- **Maintainability**: Clear separation of concerns
- **Scalability**: Supports complex permission hierarchies
- **Security**: Principle of least privilege

#### Database-Level Security
- **Defense in Depth**: Multiple layers of security
- **Performance**: Efficient policy evaluation
- **Consistency**: Uniform security across all data access
- **Compliance**: Meets regulatory requirements
- **Auditability**: Complete access audit trails

### Security Considerations

#### Token Security
- **Short-lived Access Tokens**: Reduced exposure window
- **Secure Refresh Tokens**: Long-lived but revocable
- **Token Rotation**: Automatic token refresh
- **Secure Storage**: HttpOnly cookies for web clients
- **Transport Security**: HTTPS-only token transmission

#### API Key Security
- **Encryption at Rest**: AES-256 encryption for stored keys
- **Access Control**: User-specific key access
- **Audit Logging**: Complete usage audit trails
- **Permission Scoping**: Limited API key permissions
- **Rotation Support**: Easy key rotation and revocation

## Consequences

### Positive Consequences
- **Security**: Comprehensive security at multiple layers
- **Scalability**: Supports growth in users and features
- **Compliance**: Meets financial industry security standards
- **Developer Experience**: Clear authentication patterns
- **Maintainability**: Well-structured permission system
- **Auditability**: Complete access and usage tracking

### Negative Consequences
- **Complexity**: Multiple authentication layers add complexity
- **Performance**: RLS policies can impact query performance
- **Vendor Lock-in**: Dependency on Supabase Auth
- **Learning Curve**: Team needs to understand RLS concepts

### Mitigation Strategies
- **Documentation**: Comprehensive security documentation
- **Testing**: Extensive security testing and penetration testing
- **Monitoring**: Real-time security monitoring and alerting
- **Training**: Team training on security best practices

## Implementation Notes

### Migration Strategy
1. **Basic Authentication**: Implement core Supabase Auth integration
2. **Role System**: Add role-based access control
3. **RLS Policies**: Implement database-level security policies
4. **API Key Management**: Add secure API key storage and management
5. **Advanced Features**: Add session monitoring and security features

### Security Testing
- **Penetration Testing**: Regular security assessments
- **Automated Testing**: Security test automation
- **Code Review**: Security-focused code reviews
- **Compliance Audits**: Regular compliance assessments

### Monitoring and Alerting
- **Failed Login Attempts**: Monitor and alert on suspicious activity
- **Permission Changes**: Audit and alert on permission modifications
- **API Key Usage**: Monitor API key usage patterns
- **Session Anomalies**: Detect and alert on unusual session activity

## References
- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [JWT Best Practices](https://auth0.com/blog/a-look-at-the-latest-draft-for-jwt-bcp/)
- [OWASP Authentication Guide](https://owasp.org/www-project-top-ten/2017/A2_2017-Broken_Authentication)
- [PostgreSQL RLS Documentation](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)

## Related ADRs
- ADR-001: Technology Stack Selection
- ADR-002: Database Schema Design
- ADR-006: Data Privacy and Compliance Strategy
- ADR-007: API Security and Rate Limiting

---

**Author**: Algobir Development Team  
**Date**: 2025-01-30  
**Status**: Accepted  
**Reviewers**: Security Engineer, Technical Lead, Senior Developers
