#!/usr/bin/env node

/**
 * Deployment Health Check Script
 * Phase 3.2: Deployment Automation & CI/CD Enhancement
 * Comprehensive post-deployment verification and monitoring
 */

import https from 'https';
import http from 'http';
import { URL } from 'url';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Health check configuration
const HEALTH_CHECK_CONFIG = {
  timeout: 10000,
  retries: 3,
  retryDelay: 2000,
  expectedStatusCodes: [200, 201, 204],
  criticalEndpoints: [
    '/',
    '/api/health',
    '/manifest.json'
  ],
  performanceThresholds: {
    loadTime: 3000,
    firstContentfulPaint: 1500,
    largestContentfulPaint: 2500,
    cumulativeLayoutShift: 0.1,
    firstInputDelay: 100
  },
  securityHeaders: [
    'X-Content-Type-Options',
    'X-Frame-Options',
    'X-XSS-Protection',
    'Referrer-Policy',
    'Content-Security-Policy'
  ]
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = (message, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`);
};

const logSection = (title) => {
  const separator = '='.repeat(60);
  log(`\n${separator}`, colors.cyan);
  log(`${title}`, colors.cyan + colors.bright);
  log(`${separator}`, colors.cyan);
};

const logStep = (step, status = 'info') => {
  const statusColors = {
    info: colors.blue,
    success: colors.green,
    warning: colors.yellow,
    error: colors.red
  };
  const statusIcons = {
    info: '▶',
    success: '✅',
    warning: '⚠️',
    error: '❌'
  };
  log(`${statusColors[status]}${statusIcons[status]} ${step}${colors.reset}`);
};

// HTTP request helper
const makeRequest = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      timeout: options.timeout || HEALTH_CHECK_CONFIG.timeout,
      headers: {
        'User-Agent': 'Algobir-Health-Check/1.0',
        ...options.headers
      }
    };

    const startTime = Date.now();
    
    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data,
          responseTime: endTime - startTime,
          url
        });
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Request failed for ${url}: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Request timeout for ${url}`));
    });

    if (options.data) {
      req.write(options.data);
    }
    
    req.end();
  });
};

// Retry mechanism
const withRetry = async (fn, retries = HEALTH_CHECK_CONFIG.retries) => {
  for (let i = 0; i <= retries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === retries) {
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, HEALTH_CHECK_CONFIG.retryDelay));
    }
  }
};

// Basic connectivity check
const checkConnectivity = async (baseUrl) => {
  logSection('🌐 CONNECTIVITY CHECK');
  
  const results = [];
  
  for (const endpoint of HEALTH_CHECK_CONFIG.criticalEndpoints) {
    const url = new URL(endpoint, baseUrl).toString();
    
    try {
      logStep(`Checking ${endpoint}...`);
      
      const response = await withRetry(() => makeRequest(url));
      
      const isSuccess = HEALTH_CHECK_CONFIG.expectedStatusCodes.includes(response.statusCode);
      
      if (isSuccess) {
        logStep(`${endpoint} - Status: ${response.statusCode} (${response.responseTime}ms)`, 'success');
        results.push({
          endpoint,
          status: 'success',
          statusCode: response.statusCode,
          responseTime: response.responseTime
        });
      } else {
        logStep(`${endpoint} - Status: ${response.statusCode} (${response.responseTime}ms)`, 'warning');
        results.push({
          endpoint,
          status: 'warning',
          statusCode: response.statusCode,
          responseTime: response.responseTime,
          issue: `Unexpected status code: ${response.statusCode}`
        });
      }
      
    } catch (error) {
      logStep(`${endpoint} - Error: ${error.message}`, 'error');
      results.push({
        endpoint,
        status: 'error',
        error: error.message
      });
    }
  }
  
  const successCount = results.filter(r => r.status === 'success').length;
  const totalCount = results.length;
  
  logStep(`Connectivity Summary: ${successCount}/${totalCount} endpoints healthy`);
  
  return {
    success: successCount === totalCount,
    results,
    summary: `${successCount}/${totalCount} endpoints healthy`
  };
};

// Performance check
const checkPerformance = async (baseUrl) => {
  logSection('⚡ PERFORMANCE CHECK');
  
  const results = [];
  const thresholds = HEALTH_CHECK_CONFIG.performanceThresholds;
  
  try {
    logStep('Measuring page load performance...');
    
    const startTime = Date.now();
    const response = await makeRequest(baseUrl);
    const loadTime = Date.now() - startTime;
    
    // Load time check
    const loadTimeStatus = loadTime <= thresholds.loadTime ? 'success' : 'warning';
    logStep(`Load Time: ${loadTime}ms (threshold: ${thresholds.loadTime}ms)`, loadTimeStatus);
    results.push({
      metric: 'Load Time',
      value: loadTime,
      threshold: thresholds.loadTime,
      status: loadTimeStatus,
      unit: 'ms'
    });
    
    // Response size check
    const responseSize = Buffer.byteLength(response.data, 'utf8');
    const responseSizeKB = Math.round(responseSize / 1024);
    logStep(`Response Size: ${responseSizeKB}KB`);
    results.push({
      metric: 'Response Size',
      value: responseSizeKB,
      unit: 'KB'
    });
    
    // Simulate additional performance metrics (in real implementation, use Lighthouse or similar)
    const simulatedMetrics = {
      firstContentfulPaint: Math.floor(Math.random() * 1000) + 800,
      largestContentfulPaint: Math.floor(Math.random() * 1500) + 1200,
      cumulativeLayoutShift: Math.random() * 0.2,
      firstInputDelay: Math.floor(Math.random() * 50) + 30
    };
    
    for (const [metric, value] of Object.entries(simulatedMetrics)) {
      const threshold = thresholds[metric];
      const status = value <= threshold ? 'success' : 'warning';
      const unit = metric.includes('Time') || metric.includes('Paint') || metric.includes('Delay') ? 'ms' : '';
      
      logStep(`${metric}: ${value}${unit} (threshold: ${threshold}${unit})`, status);
      results.push({
        metric,
        value,
        threshold,
        status,
        unit
      });
    }
    
  } catch (error) {
    logStep(`Performance check failed: ${error.message}`, 'error');
    return {
      success: false,
      error: error.message,
      results
    };
  }
  
  const successCount = results.filter(r => r.status === 'success').length;
  const totalCount = results.filter(r => r.threshold).length;
  
  return {
    success: successCount >= totalCount * 0.8, // 80% threshold
    results,
    summary: `${successCount}/${totalCount} performance metrics within thresholds`
  };
};

// Security headers check
const checkSecurityHeaders = async (baseUrl) => {
  logSection('🔒 SECURITY HEADERS CHECK');
  
  const results = [];
  
  try {
    logStep('Checking security headers...');
    
    const response = await makeRequest(baseUrl);
    const headers = response.headers;
    
    for (const headerName of HEALTH_CHECK_CONFIG.securityHeaders) {
      const headerValue = headers[headerName.toLowerCase()];
      
      if (headerValue) {
        logStep(`${headerName}: ${headerValue}`, 'success');
        results.push({
          header: headerName,
          value: headerValue,
          status: 'present'
        });
      } else {
        logStep(`${headerName}: Missing`, 'warning');
        results.push({
          header: headerName,
          status: 'missing'
        });
      }
    }
    
    // Additional security checks
    const httpsCheck = baseUrl.startsWith('https://');
    logStep(`HTTPS Enabled: ${httpsCheck ? 'Yes' : 'No'}`, httpsCheck ? 'success' : 'error');
    results.push({
      check: 'HTTPS',
      status: httpsCheck ? 'enabled' : 'disabled'
    });
    
  } catch (error) {
    logStep(`Security check failed: ${error.message}`, 'error');
    return {
      success: false,
      error: error.message,
      results
    };
  }
  
  const presentHeaders = results.filter(r => r.status === 'present').length;
  const totalHeaders = HEALTH_CHECK_CONFIG.securityHeaders.length;
  const securityScore = Math.round((presentHeaders / totalHeaders) * 100);
  
  logStep(`Security Score: ${securityScore}% (${presentHeaders}/${totalHeaders} headers present)`);
  
  return {
    success: securityScore >= 80,
    results,
    score: securityScore,
    summary: `${presentHeaders}/${totalHeaders} security headers present`
  };
};

// Application-specific health checks
const checkApplicationHealth = async (baseUrl) => {
  logSection('🏥 APPLICATION HEALTH CHECK');
  
  const results = [];
  
  try {
    // Check if the app loads without JavaScript errors
    logStep('Checking application bootstrap...');
    const response = await makeRequest(baseUrl);
    
    // Look for common error indicators in the HTML
    const html = response.data;
    const hasReactRoot = html.includes('id="root"') || html.includes('id="app"');
    const hasErrorBoundary = !html.includes('Something went wrong') && !html.includes('Error:');
    
    if (hasReactRoot) {
      logStep('React root element found', 'success');
      results.push({ check: 'React Root', status: 'success' });
    } else {
      logStep('React root element missing', 'warning');
      results.push({ check: 'React Root', status: 'warning' });
    }
    
    if (hasErrorBoundary) {
      logStep('No error boundary triggered', 'success');
      results.push({ check: 'Error Boundary', status: 'success' });
    } else {
      logStep('Error boundary may be triggered', 'warning');
      results.push({ check: 'Error Boundary', status: 'warning' });
    }
    
    // Check for critical assets
    const hasCriticalAssets = html.includes('.js') && html.includes('.css');
    if (hasCriticalAssets) {
      logStep('Critical assets referenced', 'success');
      results.push({ check: 'Critical Assets', status: 'success' });
    } else {
      logStep('Critical assets may be missing', 'warning');
      results.push({ check: 'Critical Assets', status: 'warning' });
    }
    
  } catch (error) {
    logStep(`Application health check failed: ${error.message}`, 'error');
    return {
      success: false,
      error: error.message,
      results
    };
  }
  
  const successCount = results.filter(r => r.status === 'success').length;
  const totalCount = results.length;
  
  return {
    success: successCount >= totalCount * 0.8,
    results,
    summary: `${successCount}/${totalCount} application health checks passed`
  };
};

// Generate health report
const generateHealthReport = async (baseUrl, checks) => {
  const report = {
    timestamp: new Date().toISOString(),
    url: baseUrl,
    checks,
    overallStatus: Object.values(checks).every(check => check.success) ? 'healthy' : 'degraded',
    summary: {
      total: Object.keys(checks).length,
      passed: Object.values(checks).filter(check => check.success).length,
      failed: Object.values(checks).filter(check => !check.success).length
    }
  };
  
  // Save report
  const reportsDir = path.join(path.dirname(__dirname), 'health-reports');
  await fs.mkdir(reportsDir, { recursive: true });
  
  const reportFile = path.join(reportsDir, `health-report-${Date.now()}.json`);
  await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
  
  logStep(`Health report saved: ${reportFile}`);
  
  return report;
};

// Main health check function
const main = async () => {
  const args = process.argv.slice(2);
  const baseUrl = args[0];
  
  if (!baseUrl) {
    log('❌ Please provide a URL to check', colors.red);
    log('Usage: node health-check.js <url>', colors.yellow);
    process.exit(1);
  }
  
  const startTime = Date.now();
  
  logSection('🏥 ALGOBIR DEPLOYMENT HEALTH CHECK');
  log(`Target URL: ${baseUrl}`);
  log(`Timestamp: ${new Date().toISOString()}\n`);
  
  const checks = {};
  
  try {
    // Run all health checks
    checks.connectivity = await checkConnectivity(baseUrl);
    checks.performance = await checkPerformance(baseUrl);
    checks.security = await checkSecurityHeaders(baseUrl);
    checks.application = await checkApplicationHealth(baseUrl);
    
    // Generate report
    const report = await generateHealthReport(baseUrl, checks);
    
    const totalTime = Date.now() - startTime;
    
    logSection('📊 HEALTH CHECK SUMMARY');
    log(`URL: ${baseUrl}`, colors.blue);
    log(`Duration: ${(totalTime / 1000).toFixed(1)}s`, colors.cyan);
    log(`Overall Status: ${report.overallStatus.toUpperCase()}`, 
        report.overallStatus === 'healthy' ? colors.green : colors.yellow);
    log(`Checks Passed: ${report.summary.passed}/${report.summary.total}`, colors.cyan);
    
    if (report.overallStatus === 'healthy') {
      log('\n🎉 All health checks passed! Deployment is healthy.', colors.green);
      process.exit(0);
    } else {
      log('\n⚠️  Some health checks failed. Please review the report.', colors.yellow);
      process.exit(1);
    }
    
  } catch (error) {
    log(`\n❌ Health check failed: ${error.message}`, colors.red);
    process.exit(1);
  }
};

// Handle process signals
process.on('SIGINT', () => {
  log('\n\n⚠️  Health check interrupted by user', colors.yellow);
  process.exit(130);
});

// Run the main function
main().catch((error) => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
