import React from 'react';
import {
  Box,
  Container,
  Flex,
  Heading,
  Text,
  VStack,
  HStack,
  useColorModeValue,
  useBreakpointValue,
  IconButton,
  Drawer,
  Drawer<PERSON><PERSON>,
  Drawer<PERSON>eader,
  <PERSON>er<PERSON><PERSON>lay,
  Drawer<PERSON>ontent,
  DrawerCloseButton,
  useDisclosure,
  Badge,
  Spinner,
  Alert,
  AlertIcon,
  Button,
  Tooltip
} from '@chakra-ui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { HamburgerIcon, DownloadIcon, RepeatIcon } from '@chakra-ui/icons';
import { FiTrendingUp, FiBarChart, FiPieChart, FiActivity } from 'react-icons/fi';
import { useEnhancedStatistics } from '../../hooks/useEnhancedStatistics';
import StatisticsHeader from './StatisticsHeader';
import StatisticsExport from './StatisticsExport';

const MotionBox = motion(Box);

interface StatisticsLayoutProps {
  children: React.ReactNode;
}

const StatisticsLayout: React.FC<StatisticsLayoutProps> = ({
  children
}) => {
  const { stats, loading, error, filters, updateFilters, refetch } = useEnhancedStatistics();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  // Theme colors
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const accentColor = useColorModeValue('blue.500', 'blue.300');
  
  // Responsive values
  const isMobile = useBreakpointValue({ base: true, md: false });
  const containerMaxW = useBreakpointValue({ base: 'full', lg: '7xl' });

  // Quick stats for header
  const quickStats = stats ? [
    {
      label: 'Toplam ROI',
      value: `${stats.roiAnalysis.totalROI.toFixed(2)}%`,
      icon: FiTrendingUp,
      color: stats.roiAnalysis.totalROI > 0 ? 'green' : 'red'
    },
    {
      label: 'Aktif Robot',
      value: stats.realtimeMetrics.activeRobots.toString(),
      icon: FiActivity,
      color: 'blue'
    },
    {
      label: 'Toplam İşlem',
      value: stats.totalTrades.toString(),
      icon: FiBarChart,
      color: 'purple'
    },
    {
      label: 'Kazanma Oranı',
      value: `${stats.winRate.toFixed(1)}%`,
      icon: FiPieChart,
      color: stats.winRate > 50 ? 'green' : 'orange'
    }
  ] : [];

  if (loading) {
    return (
      <Box minH="100vh" bg={bgColor} display="flex" alignItems="center" justifyContent="center">
        <VStack spacing={4}>
          <Spinner size="xl" color={accentColor} thickness="4px" />
          <Text color={textColor} fontSize="lg">İstatistikler yükleniyor...</Text>
        </VStack>
      </Box>
    );
  }

  if (error) {
    return (
      <Box minH="100vh" bg={bgColor} p={8}>
        <Container maxW={containerMaxW}>
          <Alert status="error" borderRadius="lg">
            <AlertIcon />
            <VStack align="start" spacing={2}>
              <Text fontWeight="bold">İstatistikler yüklenirken hata oluştu</Text>
              <Text>{error}</Text>
              <Button size="sm" onClick={refetch} leftIcon={<RepeatIcon />}>
                Tekrar Dene
              </Button>
            </VStack>
          </Alert>
        </Container>
      </Box>
    );
  }

  if (!stats || stats.totalTrades === 0) {
    return (
      <Box minH="100vh" bg={bgColor} display="flex" alignItems="center" justifyContent="center">
        <VStack spacing={6} textAlign="center">
          <Box fontSize="6xl">📊</Box>
          <Heading size="lg" color={textColor}>İstatistikler</Heading>
          <Text color={textColor} maxW="md">
            Henüz kapalı işleminiz bulunmuyor. İlk işleminizi tamamladıktan sonra 
            detaylı analiz ve performans raporlarınızı burada görebileceksiniz.
          </Text>
        </VStack>
      </Box>
    );
  }

  return (
    <Box minH="100vh" bg={bgColor}>
      {/* Header */}
      <Box bg={cardBg} borderBottom="1px" borderColor={borderColor} position="sticky" top={0} zIndex={10}>
        <Container maxW={containerMaxW}>
          <Flex h="80px" align="center" justify="space-between">
            <HStack spacing={4}>
              {isMobile && (
                <IconButton
                  aria-label="Menüyü aç"
                  icon={<HamburgerIcon />}
                  variant="ghost"
                  onClick={onOpen}
                />
              )}
              <VStack align="start" spacing={0}>
                <Heading size="lg" color={textColor}>
                  📊 Trading Analizi
                </Heading>
                <Text fontSize="sm" color="gray.500">
                  Son güncelleme: {new Date(stats.lastUpdateTime).toLocaleString('tr-TR')}
                </Text>
              </VStack>
            </HStack>

            <HStack spacing={2}>
              {/* Quick Stats */}
              {!isMobile && quickStats.slice(0, 2).map((stat, index) => (
                <Tooltip key={index} label={stat.label}>
                  <HStack spacing={2} px={3} py={2} bg={bgColor} borderRadius="md">
                    <Box as={stat.icon} color={`${stat.color}.500`} />
                    <Text fontSize="sm" fontWeight="bold" color={textColor}>
                      {stat.value}
                    </Text>
                  </HStack>
                </Tooltip>
              ))}

              {/* Action Buttons */}
              <Tooltip label="Verileri yenile">
                <IconButton
                  aria-label="Yenile"
                  icon={<RepeatIcon />}
                  variant="ghost"
                  onClick={refetch}
                  isLoading={loading}
                />
              </Tooltip>
              
              <StatisticsExport
                stats={stats}
                trigger={
                  <Tooltip label="Rapor indir">
                    <IconButton
                      aria-label="İndir"
                      icon={<DownloadIcon />}
                      variant="ghost"
                      colorScheme="blue"
                    />
                  </Tooltip>
                }
              />
            </HStack>
          </Flex>
        </Container>
      </Box>

      {/* Filters Section - Desktop */}
      {!isMobile && (
        <Container maxW={containerMaxW} py={4}>
          <StatisticsHeader
            stats={stats}
            filters={filters}
            updateFilters={updateFilters}
          />
        </Container>
      )}

      {/* Main Content */}
      <Container maxW={containerMaxW} py={6}>
        <AnimatePresence mode="wait">
          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {children}
          </MotionBox>
        </AnimatePresence>
      </Container>

      {/* Mobile Drawer */}
      <Drawer isOpen={isOpen} placement="left" onClose={onClose} size="sm">
        <DrawerOverlay />
        <DrawerContent>
          <DrawerCloseButton />
          <DrawerHeader borderBottomWidth="1px">
            <VStack align="start" spacing={2}>
              <Text>Trading Analizi</Text>
              <HStack spacing={2}>
                {quickStats.map((stat, index) => (
                  <Badge key={index} colorScheme={stat.color} variant="subtle">
                    {stat.value}
                  </Badge>
                ))}
              </HStack>
            </VStack>
          </DrawerHeader>
          <DrawerBody p={0}>
            <VStack spacing={4} p={4}>
              <StatisticsHeader
                stats={stats}
                filters={filters}
                updateFilters={updateFilters}
              />
            </VStack>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    </Box>
  );
};

export default StatisticsLayout;
