import React, { useState } from 'react';
import {
  Box,
  Button,
  VStack,
  HStack,
  Text,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  FormControl,
  FormLabel,

  Checkbox,
  CheckboxGroup,
  Stack,
  Progress,
  Alert,
  AlertIcon,
  useColorModeValue,
  Icon,
  Tooltip,
  Badge,
  Divider
} from '@chakra-ui/react';
import { DownloadIcon, CalendarIcon } from '@chakra-ui/icons';
import { FiTable, FiCode, FiPrinter } from 'react-icons/fi';
import { EnhancedStatsData } from '../../hooks/useEnhancedStatistics';
import { useStatisticsExport, ExportOptions } from '../../hooks/useStatisticsExport';

interface StatisticsExportProps {
  stats: EnhancedStatsData;
  trigger?: React.ReactElement;
}

const StatisticsExport: React.FC<StatisticsExportProps> = ({ stats, trigger }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { exportStatistics, isExporting, exportProgress } = useStatisticsExport();
  
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'excel',
    includeCharts: false,
    includeSummary: true,
    includeRobotDetails: true,
    includeTradeHistory: false
  });

  const [error, setError] = useState<string | null>(null);

  const cardBg = useColorModeValue('white', 'gray.700');


  const formatOptions = [
    { value: 'excel', label: 'Excel (.xlsx)', icon: FiTable, description: 'Detaylı tablolar ve analiz için ideal' },
    { value: 'csv', label: 'CSV (.csv)', icon: FiTable, description: 'Basit veri analizi için' },
    { value: 'json', label: 'JSON (.json)', icon: FiCode, description: 'Programatik kullanım için' },
    { value: 'pdf', label: 'PDF (.pdf)', icon: FiPrinter, description: 'Yazdırma ve sunum için' }
  ];

  const handleExport = async () => {
    setError(null);
    
    try {
      await exportStatistics(stats, exportOptions);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Dışa aktarma sırasında hata oluştu');
    }
  };

  const getEstimatedFileSize = () => {
    let size = 'Küçük';
    let sizeKB = 10;

    if (exportOptions.includeSummary) sizeKB += 5;
    if (exportOptions.includeRobotDetails) sizeKB += stats.broRobotStats.length * 2;
    if (exportOptions.includeTradeHistory) sizeKB += stats.totalTrades * 0.5;
    if (exportOptions.includeCharts) sizeKB += 50;

    if (sizeKB > 100) size = 'Büyük';
    else if (sizeKB > 50) size = 'Orta';

    return { size, sizeKB: Math.round(sizeKB) };
  };

  const estimatedSize = getEstimatedFileSize();

  return (
    <>
      {trigger ? (
        React.cloneElement(trigger as React.ReactElement<any>, { onClick: onOpen })
      ) : (
        <Tooltip label="İstatistikleri dışa aktar">
          <Button
            leftIcon={<DownloadIcon />}
            onClick={onOpen}
            variant="outline"
            size="sm"
          >
            Dışa Aktar
          </Button>
        </Tooltip>
      )}

      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent bg={cardBg}>
          <ModalHeader>
            <HStack spacing={3}>
              <Icon as={DownloadIcon} color="blue.500" />
              <Text>İstatistikleri Dışa Aktar</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          
          <ModalBody>
            <VStack spacing={6} align="stretch">
              {/* Format Selection */}
              <FormControl>
                <FormLabel fontSize="sm" fontWeight="semibold">
                  Dosya Formatı
                </FormLabel>
                <VStack spacing={2} align="stretch">
                  {formatOptions.map((format) => (
                    <Box
                      key={format.value}
                      p={3}
                      borderRadius="md"
                      border="2px"
                      borderColor={exportOptions.format === format.value ? 'blue.500' : 'gray.200'}
                      bg={exportOptions.format === format.value ? 'blue.50' : 'transparent'}
                      cursor="pointer"
                      onClick={() => setExportOptions(prev => ({ ...prev, format: format.value as any }))}
                      _hover={{ borderColor: 'blue.300' }}
                    >
                      <HStack spacing={3}>
                        <Icon as={format.icon} color="blue.500" boxSize={5} />
                        <VStack align="start" spacing={0} flex={1}>
                          <Text fontSize="sm" fontWeight="medium">
                            {format.label}
                          </Text>
                          <Text fontSize="xs" color="gray.500">
                            {format.description}
                          </Text>
                        </VStack>
                        {exportOptions.format === format.value && (
                          <Badge colorScheme="blue" variant="solid">
                            Seçili
                          </Badge>
                        )}
                      </HStack>
                    </Box>
                  ))}
                </VStack>
              </FormControl>

              <Divider />

              {/* Content Options */}
              <FormControl>
                <FormLabel fontSize="sm" fontWeight="semibold">
                  İçerik Seçenekleri
                </FormLabel>
                <CheckboxGroup>
                  <Stack spacing={3}>
                    <Checkbox
                      isChecked={exportOptions.includeSummary}
                      onChange={(e) => setExportOptions(prev => ({
                        ...prev,
                        includeSummary: e.target.checked
                      }))}
                    >
                      <VStack align="start" spacing={0}>
                        <Text fontSize="sm">Genel Özet</Text>
                        <Text fontSize="xs" color="gray.500">
                          Temel performans metrikleri ve ROI analizi
                        </Text>
                      </VStack>
                    </Checkbox>

                    <Checkbox
                      isChecked={exportOptions.includeRobotDetails}
                      onChange={(e) => setExportOptions(prev => ({
                        ...prev,
                        includeRobotDetails: e.target.checked
                      }))}
                    >
                      <VStack align="start" spacing={0}>
                        <Text fontSize="sm">Robot Detayları</Text>
                        <Text fontSize="xs" color="gray.500">
                          Solo ve Bro-Robot performans karşılaştırması
                        </Text>
                      </VStack>
                    </Checkbox>

                    <Checkbox
                      isChecked={exportOptions.includeTradeHistory}
                      onChange={(e) => setExportOptions(prev => ({
                        ...prev,
                        includeTradeHistory: e.target.checked
                      }))}
                    >
                      <VStack align="start" spacing={0}>
                        <Text fontSize="sm">İşlem Geçmişi</Text>
                        <Text fontSize="xs" color="gray.500">
                          Detaylı işlem listesi (dosya boyutunu artırır)
                        </Text>
                      </VStack>
                    </Checkbox>

                    {(exportOptions.format === 'excel' || exportOptions.format === 'pdf') && (
                      <Checkbox
                        isChecked={exportOptions.includeCharts}
                        onChange={(e) => setExportOptions(prev => ({
                          ...prev,
                          includeCharts: e.target.checked
                        }))}
                      >
                        <VStack align="start" spacing={0}>
                          <Text fontSize="sm">Grafikler</Text>
                          <Text fontSize="xs" color="gray.500">
                            Performans grafikleri ve görselleştirmeler
                          </Text>
                        </VStack>
                      </Checkbox>
                    )}
                  </Stack>
                </CheckboxGroup>
              </FormControl>

              <Divider />

              {/* File Info */}
              <Box p={3} bg="gray.50" borderRadius="md">
                <VStack spacing={2} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="sm" fontWeight="medium">
                      Tahmini Dosya Boyutu:
                    </Text>
                    <Badge colorScheme="blue" variant="subtle">
                      {estimatedSize.size} (~{estimatedSize.sizeKB} KB)
                    </Badge>
                  </HStack>
                  
                  <HStack justify="space-between">
                    <Text fontSize="sm" fontWeight="medium">
                      İçerik:
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      {[
                        exportOptions.includeSummary && 'Özet',
                        exportOptions.includeRobotDetails && 'Robotlar',
                        exportOptions.includeTradeHistory && 'İşlemler',
                        exportOptions.includeCharts && 'Grafikler'
                      ].filter(Boolean).join(', ')}
                    </Text>
                  </HStack>

                  <HStack justify="space-between">
                    <Text fontSize="sm" fontWeight="medium">
                      Veri Aralığı:
                    </Text>
                    <HStack spacing={1}>
                      <Icon as={CalendarIcon} boxSize={3} />
                      <Text fontSize="sm" color="gray.600">
                        Tüm veriler
                      </Text>
                    </HStack>
                  </HStack>
                </VStack>
              </Box>

              {/* Export Progress */}
              {isExporting && (
                <Box>
                  <Text fontSize="sm" mb={2}>
                    Dışa aktarılıyor... {exportProgress}%
                  </Text>
                  <Progress value={exportProgress} colorScheme="blue" />
                </Box>
              )}

              {/* Error Display */}
              {error && (
                <Alert status="error" borderRadius="md">
                  <AlertIcon />
                  <Text fontSize="sm">{error}</Text>
                </Alert>
              )}
            </VStack>
          </ModalBody>

          <ModalFooter>
            <HStack spacing={3}>
              <Button variant="ghost" onClick={onClose} isDisabled={isExporting}>
                İptal
              </Button>
              <Button
                colorScheme="blue"
                onClick={handleExport}
                isLoading={isExporting}
                loadingText="Dışa aktarılıyor..."
                leftIcon={<DownloadIcon />}
              >
                Dışa Aktar
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default StatisticsExport;
