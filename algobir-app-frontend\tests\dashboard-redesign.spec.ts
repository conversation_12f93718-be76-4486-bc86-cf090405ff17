import { test, expect } from '@playwright/test';

test.describe('Dashboard Redesign Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('Dashboard loads with 4-4-4 card layout', async ({ page }) => {
    // Check if dashboard cards are present
    const cardContainer = page.locator('[data-testid="dashboard-cards"]').first();
    await expect(cardContainer).toBeVisible();

    // Check for the presence of key dashboard cards
    await expect(page.locator('text=Toplam Yatırım')).toBeVisible();
    await expect(page.locator('text=Toplam Kâr/Zarar')).toBeVisible();
    await expect(page.locator('text=Bugünkü İşlemler')).toBeVisible();
    await expect(page.locator('text=<PERSON><PERSON><PERSON><PERSON>')).toBeVisible();
    await expect(page.locator('text=Kazanma Oranı')).toBeVisible();
    await expect(page.locator('text=Son İşlem Zamanı')).toBeVisible();
    await expect(page.locator('text=API Kimlik Bilgileri Durumu')).toBeVisible();
    await expect(page.locator('text=Portföy Performansı')).toBeVisible();
  });

  test('Dashboard cards display readable text without overflow', async ({ page }) => {
    // Check that card text is not truncated or overflowing
    const cards = page.locator('[data-testid="trading-stat-card"]');
    const cardCount = await cards.count();
    
    for (let i = 0; i < cardCount; i++) {
      const card = cards.nth(i);
      await expect(card).toBeVisible();
      
      // Check that card content is readable
      const cardText = await card.textContent();
      expect(cardText).toBeTruthy();
      expect(cardText!.length).toBeGreaterThan(0);
    }
  });

  test('Dashboard layout is responsive on different screen sizes', async ({ page }) => {
    // Test desktop layout
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('[data-testid="dashboard-cards"]').first()).toBeVisible();

    // Test tablet layout
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('[data-testid="dashboard-cards"]').first()).toBeVisible();

    // Test mobile layout
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('[data-testid="dashboard-cards"]').first()).toBeVisible();
  });

  test('No tooltip overlapping issues exist', async ({ page }) => {
    // Hover over dashboard cards to ensure no tooltip issues
    const cards = page.locator('[data-testid="trading-stat-card"]');
    const cardCount = await cards.count();
    
    for (let i = 0; i < cardCount; i++) {
      const card = cards.nth(i);
      await card.hover();
      
      // Wait a moment for any potential tooltips
      await page.waitForTimeout(500);
      
      // Check that no tooltip elements are present
      const tooltips = page.locator('[role="tooltip"]');
      const tooltipCount = await tooltips.count();
      expect(tooltipCount).toBe(0);
    }
  });

  test('Last Closed Trades table is not modified', async ({ page }) => {
    // Scroll to the trades table
    await page.locator('text=Son Kapatılan İşlemler').scrollIntoViewIfNeeded();
    
    // Check that the trades table exists and is functional
    const tradesTable = page.locator('[data-testid="recent-trades-table"]');
    await expect(tradesTable).toBeVisible();
    
    // Check for table headers
    await expect(page.locator('text=Sinyal')).toBeVisible();
    await expect(page.locator('text=Sembol')).toBeVisible();
    await expect(page.locator('text=Yön')).toBeVisible();
    await expect(page.locator('text=Kar/Zarar')).toBeVisible();
  });
});

test.describe('NotificationBar Positioning Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('NotificationBar appears below navbar when API credentials expire soon', async ({ page }) => {
    // Mock API credentials expiry to trigger notification
    await page.route('**/rest/v1/user_settings*', async route => {
      const response = await route.fetch();
      const json = await response.json();
      
      // Set expiry date to 24 hours from now to trigger notification
      const tomorrow = new Date();
      tomorrow.setHours(tomorrow.getHours() + 24);
      
      if (json.data && json.data.length > 0) {
        json.data[0].api_credentials_expiry_date = tomorrow.toISOString();
      }
      
      await route.fulfill({ response, json });
    });

    await page.reload();
    await page.waitForLoadState('networkidle');

    // Check if notification bar is visible
    const notificationBar = page.locator('[data-testid="notification-bar"]');
    if (await notificationBar.isVisible()) {
      // Get navbar and notification bar positions
      const navbar = page.locator('[data-testid="navbar"]');
      const navbarBox = await navbar.boundingBox();
      const notificationBox = await notificationBar.boundingBox();

      // Ensure notification bar is below navbar
      if (navbarBox && notificationBox) {
        expect(notificationBox.y).toBeGreaterThanOrEqual(navbarBox.y + navbarBox.height);
      }
    }
  });

  test('NotificationBar positioning works on different screen sizes', async ({ page }) => {
    // Test on different viewport sizes
    const viewports = [
      { width: 1920, height: 1080 }, // Desktop
      { width: 1024, height: 768 },  // Tablet
      { width: 375, height: 667 }    // Mobile
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(500);

      const notificationBar = page.locator('[data-testid="notification-bar"]');
      if (await notificationBar.isVisible()) {
        // Check that notification bar doesn't overlap with navbar
        const navbar = page.locator('[data-testid="navbar"]');
        const navbarBox = await navbar.boundingBox();
        const notificationBox = await notificationBar.boundingBox();

        if (navbarBox && notificationBox) {
          expect(notificationBox.y).toBeGreaterThanOrEqual(navbarBox.y + navbarBox.height);
        }
      }
    }
  });
});

test.describe('API Expiry Management Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/management');
    await page.waitForLoadState('networkidle');
  });

  test('Management page shows single unified expiry date input', async ({ page }) => {
    // Check for the unified API credentials expiry section
    await expect(page.locator('text=API Kimlik Bilgileri Süresi Yönetimi')).toBeVisible();
    
    // Check for single expiry date input
    const expiryInput = page.locator('input[name="api-credentials-expiry"]');
    await expect(expiryInput).toBeVisible();
    
    // Ensure old separate inputs don't exist
    const apiKeyInput = page.locator('input[name="api-key-expiry"]');
    const tokenInput = page.locator('input[name="token-expiry"]');
    
    await expect(apiKeyInput).not.toBeVisible();
    await expect(tokenInput).not.toBeVisible();
  });

  test('Unified expiry date can be set and saved', async ({ page }) => {
    const expiryInput = page.locator('input[name="api-credentials-expiry"]');
    
    // Set a future date
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 30);
    const dateString = futureDate.toISOString().slice(0, 16); // Format for datetime-local
    
    await expiryInput.fill(dateString);
    
    // Click save button
    await page.locator('button:has-text("Süre Ayarlarını Kaydet")').click();
    
    // Wait for success message
    await expect(page.locator('text=başarıyla kaydedildi')).toBeVisible({ timeout: 5000 });
  });
});

test.describe('Console Error Tests', () => {
  test('No React console errors during normal dashboard usage', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    // Capture console errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Navigate and interact with dashboard
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Interact with various elements
    const cards = page.locator('[data-testid="trading-stat-card"]');
    const cardCount = await cards.count();
    
    for (let i = 0; i < Math.min(cardCount, 3); i++) {
      await cards.nth(i).hover();
      await page.waitForTimeout(200);
    }

    // Check for React-specific errors
    const reactErrors = consoleErrors.filter(error => 
      error.includes('React') || 
      error.includes('forwardRef') || 
      error.includes('Warning:')
    );
    
    expect(reactErrors).toHaveLength(0);
  });
});
