import { useState, useEffect } from 'react';
import {
  Box, VStack, Heading, Alert, AlertIcon, Text,
  FormControl, FormLabel, FormHelperText, Switch, Input, Button,
  NumberInput, NumberInputField,
  IconButton, InputGroup, InputRightElement,
  useColorModeValue, Badge, Flex, Image, HStack,
  useBreakpointValue, Stack
} from '@chakra-ui/react';
import { FaCopy, FaSave, FaPowerOff } from 'react-icons/fa';
import { CheckCircleIcon, WarningIcon } from '@chakra-ui/icons';
import { useAuth } from '../context/AuthContext';
import { useToastHelper } from '../components/ToastHelper';
import LoadingState from '../components/LoadingState';
import ErrorFallback from '../components/ErrorFallback';
import Card from '../components/card/Card';
import { useManagementData } from '../hooks/useManagementData';
import type { UserSettings } from '../hooks/useManagementData';

import { supabase } from '../supabaseClient';

const Management = () => {
  // Tüm context hook'larını en başta çağır
  const { user } = useAuth();
  const toast = useToastHelper();
  
  // useManagementData hook'unu kullan
  const {
    settings,
    subscriptions,
    isLoading,
    isUpdating,
    isDeleting,
    error,
    updateSettings,
    deleteSubscription,
    toggleSubscriptionStatus,
    saveCredentials
  } = useManagementData();
  

  
  // Color mode değerlerini Horizon UI'a uyumlu hale getir
  const textColor = useColorModeValue('navy.700', 'white');
  const textColorSecondary = useColorModeValue('secondaryGray.600', 'white');
  const brandColor = useColorModeValue('brand.500', 'white');
  const borderColor = useColorModeValue('secondaryGray.200', 'whiteAlpha.100');
  const inputBg = useColorModeValue('secondaryGray.300', 'navy.900');
  const codeBgColor = useColorModeValue("secondaryGray.300", "navy.900");
  const codeTextColor = useColorModeValue("navy.700", "white");
  const cardShadow = useColorModeValue("0px 18px 40px rgba(112, 144, 176, 0.12)", "none");
  
  // Responsive değerler - Tüm hook'ları en başta çağır
  const cardPadding = useBreakpointValue({ base: '16px', sm: '20px', md: '24px' });
  const inputHeight = useBreakpointValue({ base: '44px', md: '48px' });
  const fontSize = useBreakpointValue({ base: 'sm', md: 'md' });
  const containerPadding = useBreakpointValue({ base: '16px', md: '24px', lg: '30px' });
  
  // UI durumları
  const [newApiKey, setNewApiKey] = useState<string>('');
  const [newToken, setNewToken] = useState<string>('');
  
  // Form durumları - Ayarları Kaydet butonu için
  const [formSettings, setFormSettings] = useState<Partial<UserSettings>>({});
  const [formChanged, setFormChanged] = useState<boolean>(false);
  
  // Webhook URL'ini oluştur
  const webhookBaseUrl = import.meta.env.VITE_WEBHOOK_BASE_URL || 'https://hook.algobir.com';
  const webhookUrl = settings?.webhook_id
    ? `${webhookBaseUrl}/algobir-webhook-listener/${settings.webhook_id}`
    : '';
  
  // Kopyalama işlevi
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.showSuccessToast('Kopyalandı', 'Webhook URL\'i panoya kopyalandı.');
  };



  // Settings değiştiğinde form değerlerini güncelle
  useEffect(() => {
    if (settings) {
      setFormSettings({
        is_active: settings.is_active,
        investment_amount: settings.investment_amount,
        total_investment_amount: settings.total_investment_amount,
        custom_webhook_url: settings.custom_webhook_url,
        api_credentials_expiry_date: settings.api_credentials_expiry_date || ''
      });
      setFormChanged(false);
    }
  }, [settings]);
  
  // Form değerlerini güncelle
  const handleInputChange = (field: string, value: any) => {
    setFormSettings((prev: Partial<UserSettings>) => ({
      ...prev,
      [field]: value
    }));
    setFormChanged(true);
  };
  
  // Aktif/Pasif değişimini kaydet
  const handleToggleActive = async (isActive: boolean) => {
    if (!user || !settings) return;
    
    try {
      // Supabase RPC fonksiyonunu çağır
      const { error: rpcError } = await supabase.rpc('set_robot_emergency_status', {
        p_requesting_user_id: user.id,
        p_is_stopped: !isActive
      });
      
      if (rpcError) {
        console.error('[ManagementPage] Error toggling emergency status:', rpcError);
        toast.showErrorToast('Hata', `Acil durdurma durumu değiştirilemedi: ${rpcError.message || 'Bilinmeyen hata'}`);
        return;
      }
      
      // Form durumunu ve UI'ı güncelle
      setFormSettings((prev: Partial<UserSettings>) => ({
        ...prev,
        is_active: isActive
      }));
      
      setFormChanged(true);
      
      // Başarı mesajı göster
      toast.showSuccessToast(
        'Başarılı', 
        isActive ? 'Robot başarıyla başlatıldı.' : 'Robot başarıyla durduruldu.'
      );
      
    } catch (err: any) {
      console.error('[ManagementPage] Error in handleToggleActive:', err);
      toast.showErrorToast('Hata', `İşlem sırasında bir hata oluştu: ${err.message || 'Bilinmeyen hata'}`);
    }
  };
  
  // API Anahtarı ve Token kaydetme işlevi (tek bir fonksiyonda birleştirildi)
  const handleSaveCredentials = async () => {
    if (!user) {
      console.error('[ManagementPage] handleSaveCredentials called but user is null');
      toast.showErrorToast('Hata', 'Kullanıcı bilgileriniz yüklenemedi. Lütfen sayfayı yenileyip tekrar deneyin.');
      return;
    }

    // Her iki alanın değerlerini kontrol et
    const trimmedApiKey = newApiKey.trim();
    const trimmedToken = newToken.trim();
    
    // En az biri dolu olmalı
    if (!trimmedApiKey && !trimmedToken) {
      toast.showWarningToast('Uyarı', 'Kaydedilecek değer bulunamadı. API Anahtarı veya Token girilmelidir.');
      return;
    }
    
    // Kaydetme işlemi için payload oluştur - sadece dolu olanları gönder
    const credentialsPayload: { apiKey?: string; token?: string } = {};
    
    if (trimmedApiKey) {
      credentialsPayload.apiKey = trimmedApiKey;
    }
    
    if (trimmedToken) {
      credentialsPayload.token = trimmedToken;
    }
    
    const success = await saveCredentials(credentialsPayload);
    
    if (success) {
      // Başarılı kaydedildiğinde Input'ları temizle
      setNewApiKey('');
      setNewToken('');
    }
  };

  // API Credentials Expiry Date kaydetme işlevi
  const handleSaveExpiryDates = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast.showErrorToast('Hata', 'Kullanıcı bilgileriniz yüklenemedi. Lütfen sayfayı yenileyip tekrar deneyin.');
      return;
    }

    const expirySettings = {
      api_credentials_expiry_date: formSettings.api_credentials_expiry_date || null
    };

    const success = await updateSettings(expirySettings);

    if (success) {
      toast.showSuccessToast('Başarılı', 'API kimlik bilgileri süre ayarları başarıyla kaydedildi.');
    }
  };

  // Form submit handler for API credentials
  const handleCredentialsFormSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    await handleSaveCredentials();
  };
  
  // Ayarları kaydet
  const handleSaveSettings = async () => {
    // Kullanıcı ve settings kontrolü
    if (!user) {
      console.error('[ManagementPage] handleSaveSettings called but user is null');
      toast.showErrorToast('Hata', 'Kullanıcı bilgileriniz yüklenemedi. Lütfen sayfayı yenileyip tekrar deneyin.');
      return;
    }
    
    if (!settings) {
      console.error('[ManagementPage] handleSaveSettings called but settings is null');
      toast.showErrorToast('Hata', 'Kullanıcı ayarlarınız yüklenemedi. Lütfen sayfayı yenileyip tekrar deneyin.');
      return;
    }
    
    if (!formChanged) {
      toast.showInfoToast('Bilgi', 'Değişiklik yapılmadı.');
      return;
    }
    
    // URL değerini işle ve doğrula
    let webhookUrl = '';
    try {
      // custom_webhook_url değerini kontrol et
      if (formSettings.custom_webhook_url) {
        // Boşlukları temizle
        const trimmedUrl = formSettings.custom_webhook_url.trim();
        
        if (trimmedUrl) {
          // URL protokolünü kontrol et
          if (!trimmedUrl.startsWith('http://') && !trimmedUrl.startsWith('https://')) {
            // Protokol yoksa, https:// ekle
            webhookUrl = 'https://' + trimmedUrl;
            console.log(`[ManagementPage] Added https:// protocol to URL: ${webhookUrl}`);
          } else {
            webhookUrl = trimmedUrl;
          }
          
          // URL geçerli mi kontrol et (basit kontrol)
          try {
            new URL(webhookUrl);
            console.log(`[ManagementPage] Valid URL format: ${webhookUrl}`);
          } catch (urlError) {
            console.warn(`[ManagementPage] Invalid URL format: ${webhookUrl}`, urlError);
            toast.showWarningToast('Uyarı', 'Girilen URL formatı geçerli değil. URL formatını kontrol edin.');
            // Yine de devam et, kullanıcının girdiği değeri koruyarak
          }
        }
      }
      
      console.log(`[ManagementPage] Final webhook URL: "${webhookUrl}"`);
    } catch (urlError) {
      console.error('[ManagementPage] Error processing webhook URL:', urlError);
      // Hata durumunda boş string kullan
      webhookUrl = '';
    }
    
    try {
      // Diğer ayarları güncelle
      const updateValues = {
        is_active: formSettings.is_active !== undefined ? formSettings.is_active : settings.is_active,
        investment_amount: formSettings.investment_amount !== undefined ? formSettings.investment_amount : settings.investment_amount,
        total_investment_amount: formSettings.total_investment_amount !== undefined ? formSettings.total_investment_amount : settings.total_investment_amount,
        custom_webhook_url: webhookUrl // İşlenmiş URL'i kullan
      };
      
      // Settings güncelleme
      await updateSettings(updateValues);
      setFormChanged(false);
      
    } catch (err: any) {
      console.error('[ManagementPage] Failed to save settings:', err);
      toast.showErrorToast('Hata', `Ayarlar kaydedilemedi: ${err.message || 'Bilinmeyen hata'}`);
    }
  };
  
  // Abonelik aktif/pasif değiştirme işlevi
  const handleToggleSubscriptionStatus = async (subscriptionId: string, newStatus: boolean) => {
    try {
      await toggleSubscriptionStatus(subscriptionId, newStatus);
    } catch (err: any) {
      console.error('[ManagementPage] Error toggling subscription status:', err);
      toast.showErrorToast('Hata', `Abonelik durumu değiştirilemedi: ${err.message || 'Bilinmeyen hata'}`);
    }
  };
  
  // Yükleniyor durumu
  if (isLoading) {
    return <LoadingState isFullPage={true} text="Ayarlar yükleniyor..." />;
  }
  
  // Hata durumu
  if (error) {
    return <ErrorFallback error={new Error(error)} />;
  }

  return (
    <Box
      px={containerPadding}
      pb={{ base: '20px', md: '30px' }}
    >
      <VStack spacing={{ base: 6, md: 8 }} align="stretch" maxW="1200px" mx="auto">
        {/* Başlık */}
        <Heading 
          as="h1" 
          size={{ base: 'lg', md: 'xl' }}
          color={textColor} 
          textAlign={{ base: 'center', md: 'left' }}
          fontSize={{ base: '24px', md: '32px' }}
          fontWeight="700"
          mb={{ base: 4, md: 6 }}
        >
          Yönetim
        </Heading>
        
        {/* Acil Durdurma Anahtarı */}
        <Card
          p={cardPadding}
          borderRadius="20px"
          boxShadow={cardShadow}
          transition="all 0.3s"
          _hover={{ 
            transform: 'translateY(-2px)',
            boxShadow: '0 20px 27px 0 rgba(0, 0, 0, 0.05)'
          }}
        >
          <VStack spacing={{ base: 4, md: 6 }} align="stretch">
            <Flex 
              direction={{ base: 'column', sm: 'row' }}
              align={{ base: 'flex-start', sm: 'center' }}
              gap={{ base: 2, sm: 3 }}
            >
              <Flex align="center" gap={3}>
                <Box 
                  as={FaPowerOff} 
                  color={settings?.is_active ? "green.500" : "red.500"} 
                  fontSize={{ base: 'lg', md: 'xl' }}
                />
                <Text 
                  fontSize={{ base: 'lg', md: 'xl' }} 
                  fontWeight="700" 
                  color={textColor}
                >
                  Acil Durdurma Anahtarı
                </Text>
              </Flex>
            </Flex>
            
            <Stack 
              direction={{ base: 'column', sm: 'row' }}
              spacing={{ base: 3, sm: 4 }}
              align={{ base: 'stretch', sm: 'center' }}
            >
              <FormControl display="flex" alignItems="center" flex="1">
                <FormLabel 
                  htmlFor="is-active" 
                  mb="0" 
                  fontWeight="500" 
                  color={textColor} 
                  fontSize={fontSize}
                  mr={{ base: 2, sm: 3 }}
                >
                  Sinyal İletimi
                </FormLabel>
                <Switch 
                  id="is-active" 
                  colorScheme="brandScheme"
                  variant="main"
                  isChecked={formSettings.is_active !== undefined ? formSettings.is_active : settings?.is_active}
                  onChange={(e) => handleToggleActive(e.target.checked)}
                  size={{ base: 'md', md: 'lg' }}
                />
              </FormControl>
              
              <Badge 
                colorScheme={(() => {
                  const isActive = formSettings.is_active !== undefined ? formSettings.is_active : settings?.is_active;
                  return isActive ? 'green' : 'red';
                })()} 
                fontSize={{ base: 'xs', md: 'sm' }}
                borderRadius="10px" 
                px={{ base: 2, md: 3 }}
                py={1}
                minW="fit-content"
              >
                {(formSettings.is_active !== undefined ? formSettings.is_active : settings?.is_active) ? 'Aktif' : 'Pasif'}
              </Badge>
            </Stack>
            
            <Text 
              fontSize={{ base: 'xs', md: 'sm' }}
              color={textColorSecondary}
              textAlign={{ base: 'center', sm: 'left' }}
            >
              Bu anahtarı kapatarak sinyal iletimini anında durdurabilirsiniz.
            </Text>
          </VStack>
        </Card>
        
        {/* Webhook URL Bilgileri Kartı */}
        <Card mb="20px" p={{ base: "30px", sm: "30px" }}>
          <Heading size="md" mb={6} color={textColor}>
            Webhook URL Bilgileri
          </Heading>
          
          <VStack spacing={6} align="stretch">
            {/* Algobir Webhook URL (Gelen Sinyal - Solo-Robot) */}
            <Box>
              <Heading size="sm" mb={2} color={textColorSecondary}>
                Solo-Robot Webhook URL (Gelen Sinyal URL) 
                <Badge ml={2} colorScheme="blue">TradingView</Badge>
              </Heading>
              <Text fontSize="sm" mb={2} color={textColorSecondary}>
                Bu URL'i TradingView'de veya diğer sistemlerde kullanın. Kendi sinyallerinizi Algobir'a göndermek için bu webhook URL'ini kullanmalısınız.
              </Text>
              
              {settings?.webhook_id ? (
                <InputGroup>
                  <Input
                    value={webhookUrl}
                    isReadOnly
                    bg={codeBgColor}
                    color={codeTextColor}
                    fontFamily="mono"
                  />
                  <InputRightElement>
                    <IconButton 
                      aria-label='Copy webhook URL'
                      icon={<FaCopy />}
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(webhookUrl)}
                    />
                  </InputRightElement>
                </InputGroup>
              ) : (
                <Alert status="warning">
                  <AlertIcon />
                  <Text fontSize="sm">
                    Webhook URL oluşturulmadı. Lütfen site yöneticisine bildirin.
                  </Text>
                </Alert>
              )}
            </Box>
            
            {/* Özel Webhook URL (Giden Sinyal - Solo-Robot ve Bro-Robot için ortak) */}
            <Box>
              <Heading size="sm" mb={2} color={textColorSecondary}>
                Özel Webhook URL (Giden Sinyal URL) 
                <Badge ml={2} colorScheme="green">Tüm Sinyaller</Badge>
              </Heading>
              <Text fontSize="sm" mb={3} color={textColorSecondary}>
                Algobir'un işlem sinyallerini göndereceği URL'i buraya yazın. Bu URL hem Solo-Robot (kendi webhook'unuzdan gelen) hem de Bro-Robot (pazar yerinden abone olduğunuz) sinyallerinin gönderileceği hedef adrestir.
              </Text>
              
              <FormControl mb={4}>
                <Input
                  value={formSettings.custom_webhook_url || ''}
                  placeholder="https://your-broker-api.com/webhook"
                  onChange={(e) => handleInputChange('custom_webhook_url', e.target.value)}
                  bg={inputBg}
                  h={inputHeight}
                  fontSize={fontSize}
                  borderRadius="16px"
                  border="1px solid"
                  borderColor={borderColor}
                  _focus={{
                    borderColor: brandColor,
                    boxShadow: `0 0 0 1px ${brandColor}`,
                  }}
                />
                <FormHelperText>
                  Borsa veya aracı kurumunuzun webhook URL'ini girin. Sinyaller borsa-custom-json formatında gönderilecektir.
                </FormHelperText>
              </FormControl>
            </Box>
          </VStack>
        </Card>
        
        {/* Borsa API Bilgileri Kartı */}
        <Card mb="20px" p={{ base: "30px", sm: "30px" }}>
          <Heading size="md" mb={4} color={textColor}>
            Borsa API Bilgileri
            <Badge ml={2} colorScheme="green">Solo-Robot & Bro-Robot</Badge>
          </Heading>
          
          <Text fontSize="sm" mb={4} color={textColorSecondary}>
            Bu bilgiler "Özel Webhook URL"e gönderilen tüm sinyallerde (Solo-Robot ve Bro-Robot) borsa-custom-json formatı için kullanılacaktır. Bilgileriniz güvenli bir şekilde saklanır ve sadece sinyal iletiminde kullanılır.
          </Text>
          
          <Box as="form" onSubmit={handleCredentialsFormSubmit}>
            <VStack align="stretch" spacing={6}>

              {/* Hidden username field for password manager accessibility */}
              <FormControl style={{ display: 'none' }}>
                <FormLabel>Username</FormLabel>
                <Input
                  type="text"
                  name="username"
                  value={settings?.webhook_id || 'algobir-user'}
                  readOnly
                  autoComplete="username"
                />
              </FormControl>

              {/* API Key */}
              <FormControl>
                <FormLabel 
                  fontSize="sm" 
                  fontWeight="600" 
                  color={textColor}
                  ms="4px"
                  mb="8px"
                >
                  <HStack spacing={2} align="center">
                    <Text>API Key</Text>
                    {settings?.api_key_set === true && <CheckCircleIcon color="green.500" />}
                    {settings?.api_key_set === false && <WarningIcon color="orange.400" />}
                  </HStack>
                </FormLabel>
                <InputGroup>
                  <Input
                    type="password"
                    name="api-key"
                    value={newApiKey}
                    onChange={(e) => setNewApiKey(e.target.value)}
                    placeholder={settings?.api_key_set ? 'API Anahtarı ayarlandı (Güncellemek için yenisini girin)' : 'API Anahtarınızı girin'}
                    focusBorderColor={brandColor}
                    fontSize="sm"
                    fontWeight="500"
                    h="44px"
                    borderRadius="16px"
                    variant="main"
                    bg={inputBg}
                    border="1px solid"
                    borderColor={borderColor}
                    autoComplete="current-password"
                    _focus={{
                      borderColor: brandColor,
                      boxShadow: `0 0 0 1px ${brandColor}`,
                    }}
                    _placeholder={{ color: "secondaryGray.600", fontWeight: "400" }}
                  />
                </InputGroup>
                {settings?.api_key_set && (
                  <FormHelperText color="green.500" fontSize="xs" mt={1} ms="4px">
                    <HStack spacing={1}>
                      <CheckCircleIcon />
                      <Text>Mevcut API Anahtarı güvenli bir şekilde saklanıyor.</Text>
                    </HStack>
                  </FormHelperText>
                )}
                {!settings?.api_key_set && (
                  <FormHelperText fontSize="xs" color={textColorSecondary} mt={1} ms="4px">
                    Borsa API anahtarınızı girin. Bu anahtar güvenli bir şekilde saklanacaktır.
                  </FormHelperText>
                )}
              </FormControl>
              
              {/* Token */}
              <FormControl>
                <FormLabel 
                  fontSize="sm" 
                  fontWeight="600" 
                  color={textColor}
                  ms="4px"
                  mb="8px"
                >
                  <HStack spacing={2} align="center">
                    <Text>Token</Text>
                    {settings?.token_set === true && <CheckCircleIcon color="green.500" />}
                    {settings?.token_set === false && <WarningIcon color="orange.400" />}
                  </HStack>
                </FormLabel>
                <InputGroup>
                  <Input
                    type="password"
                    name="token"
                    value={newToken}
                    onChange={(e) => setNewToken(e.target.value)}
                    placeholder={settings?.token_set ? 'Token ayarlandı (Güncellemek için yenisini girin)' : 'Token bilginizi girin'}
                    fontSize="sm"
                    fontWeight="500"
                    h="44px"
                    borderRadius="16px"
                    variant="main"
                    border="1px solid"
                    borderColor={borderColor}
                    bg={inputBg}
                    autoComplete="current-password"
                    _focus={{
                      borderColor: brandColor,
                      boxShadow: `0 0 0 1px ${brandColor}`,
                    }}
                    _placeholder={{ color: "secondaryGray.600", fontWeight: "400" }}
                  />
                </InputGroup>
                {settings?.token_set && (
                  <FormHelperText color="green.500" fontSize="xs" mt={1} ms="4px">
                    <HStack spacing={1}>
                      <CheckCircleIcon />
                      <Text>Mevcut Token güvenli bir şekilde saklanıyor.</Text>
                    </HStack>
                  </FormHelperText>
                )}
                {!settings?.token_set && (
                  <FormHelperText fontSize="xs" color={textColorSecondary} mt={1} ms="4px">
                    Borsa token bilginizi girin. Bu token güvenli bir şekilde saklanacaktır.
                  </FormHelperText>
                )}
              </FormControl>
              
              {/* API Key ve Token kaydetme butonu */}
              <Button 
                leftIcon={<FaSave />}
                colorScheme="brand"
                variant="outline"
                size="sm"
                type="submit"
                isDisabled={(!newApiKey.trim() && !newToken.trim()) || isUpdating}
                isLoading={isUpdating}
                loadingText="Kaydediliyor..."
              >
                API Bilgilerini Kaydet
              </Button>
            </VStack>
          </Box>
        </Card>

        {/* API Kimlik Bilgileri Süresi Yönetimi Kartı */}
        <Card mb="20px" p={{ base: "30px", sm: "30px" }}>
          <Heading size="md" mb={4} color={textColor}>
            ⏰ API Kimlik Bilgileri Süresi Yönetimi
            <Badge ml={2} colorScheme="orange">Güvenlik</Badge>
          </Heading>

          <Text fontSize="sm" mb={4} color={textColorSecondary}>
            API kimlik bilgilerinizin (anahtar ve token) son kullanma tarihini ayarlayın. Süre dolmadan 48 saat önce bildirim alacaksınız.
          </Text>

          <Box as="form" onSubmit={handleSaveExpiryDates}>
            <VStack spacing={6} align="stretch">
              {/* Unified API Credentials Expiry Date */}
              <FormControl>
                <FormLabel
                  fontSize="sm"
                  fontWeight="600"
                  color={textColor}
                  ms="4px"
                  mb="8px"
                >
                  <HStack spacing={2} align="center">
                    <Text>API Kimlik Bilgileri Son Kullanma Tarihi</Text>
                  </HStack>
                </FormLabel>
                <Input
                  type="datetime-local"
                  name="api-credentials-expiry"
                  value={formSettings.api_credentials_expiry_date || ''}
                  onChange={(e) => setFormSettings(prev => ({
                    ...prev,
                    api_credentials_expiry_date: e.target.value
                  }))}
                  focusBorderColor={brandColor}
                  fontSize="sm"
                  fontWeight="500"
                  h="44px"
                  borderRadius="16px"
                  variant="main"
                  bg={inputBg}
                  border="1px solid"
                  borderColor={borderColor}
                  _focus={{
                    borderColor: brandColor,
                    boxShadow: `0 0 0 1px ${brandColor}`,
                  }}
                />
                <FormHelperText fontSize="xs" color={textColorSecondary} mt={1} ms="4px">
                  API anahtarı ve token'ınızın geçerlilik süresinin son tarihini belirtin.
                </FormHelperText>
              </FormControl>

              {/* Save Expiry Date Button */}
              <Button
                leftIcon={<FaSave />}
                colorScheme="orange"
                variant="outline"
                size="sm"
                type="submit"
                isLoading={isUpdating}
                loadingText="Kaydediliyor..."
              >
                Süre Ayarlarını Kaydet
              </Button>
            </VStack>
          </Box>
        </Card>


        
        {/* Sinyal Ayarları Kartı */}
        <Card mb="20px" p={{ base: "30px", sm: "30px" }}>
          <Heading size="md" mb={4} color={textColor}>
            Sinyal Ayarları
          </Heading>
          
          <VStack align="stretch" spacing={6}>
            {/* Yatırım Tutarı */}
            <FormControl>
              <FormLabel 
                fontSize="sm" 
                fontWeight="600" 
                color={textColor}
                ms="4px"
                mb="8px"
              >
                Pozisyon Başına Yatırım Tutarı
              </FormLabel>
              <NumberInput 
                value={formSettings.investment_amount !== undefined ? formSettings.investment_amount : settings?.investment_amount || 0} 
                onChange={(_, val) => handleInputChange('investment_amount', val)}
                min={0}
                focusBorderColor={brandColor}
              >
                <NumberInputField 
                  placeholder="Örn: 1000"
                  borderRadius="16px"
                  fontSize="sm"
                  fontWeight="500"
                  h="44px"
                  border="1px solid"
                  borderColor={borderColor}
                  bg={inputBg}
                  _focus={{
                    borderColor: brandColor,
                    boxShadow: `0 0 0 1px ${brandColor}`,
                  }}
                  _placeholder={{ color: "secondaryGray.600", fontWeight: "400" }}
                />
              </NumberInput>
              <FormHelperText fontSize="xs" color={textColorSecondary} mt={1} ms="4px">
                Her işlemde kullanılacak yatırım tutarını girin (TL cinsinden). 
                Bu değer, işlem sinyalinde fiyata bölünerek tam sayı lot miktarı hesaplanmasında kullanılır (quantity = Math.floor(yatırım tutarı / fiyat)).
                Hesaplanan miktar sıfır veya daha küçükse işlem gerçekleştirilmez.
              </FormHelperText>
            </FormControl>
            
            {/* Toplam Yatırım Limiti */}
            <FormControl>
              <FormLabel 
                fontSize="sm" 
                fontWeight="600" 
                color={textColor}
                ms="4px"
                mb="8px"
              >
                Toplam Yatırım Limiti
              </FormLabel>
              <NumberInput 
                value={formSettings.total_investment_amount || undefined} 
                onChange={(_, val) => handleInputChange('total_investment_amount', val)}
                min={0}
                focusBorderColor={brandColor}
              >
                <NumberInputField 
                  placeholder="Örn: 10000"
                  borderRadius="16px"
                  fontSize="sm"
                  fontWeight="500"
                  h="44px"
                  border="1px solid"
                  borderColor={borderColor}
                  bg={inputBg}
                  _focus={{
                    borderColor: brandColor,
                    boxShadow: `0 0 0 1px ${brandColor}`,
                  }}
                  _placeholder={{ color: "secondaryGray.600", fontWeight: "400" }}
                />
              </NumberInput>
              <FormHelperText fontSize="xs" color={textColorSecondary} mt={1} ms="4px">
                Aynı anda açık pozisyonlarınız için toplam yatırım limitinizi belirleyin. Bu limit aşıldığında yeni alım sinyalleri işleme alınmayacaktır. (TL cinsinden)
              </FormHelperText>
            </FormControl>
            
            {/* Ayarları Kaydet Butonu */}
            <Button 
              leftIcon={<FaSave />}
              colorScheme="brand"
              variant="brand"
              isLoading={isUpdating}
              loadingText="Kaydediliyor"
              onClick={handleSaveSettings}
              mt={4}
              fontSize="sm"
              fontWeight="600"
              borderRadius="16px"
              h="44px"
              w={{ base: "100%", md: "fit-content" }}
              boxShadow="0px 4px 10px rgba(66, 42, 251, 0.3)"
              _hover={{
                transform: 'translateY(-2px)',
                boxShadow: '0px 6px 15px rgba(66, 42, 251, 0.4)',
                bg: 'brand.600'
              }}
              transition="all 0.3s"
            >
              Ayarları Kaydet
            </Button>
          </VStack>
        </Card>
        
        {/* Aktif Robot Abonelikleri Bölümü */}
        <Card
          p="20px"
          borderRadius="20px"
          boxShadow={cardShadow}
          transition="all 0.3s"
          _hover={{ 
            transform: 'translateY(-2px)',
            boxShadow: '0 20px 27px 0 rgba(0, 0, 0, 0.05)'
          }}
        >
          <Flex direction="column">
            <Text fontSize="lg" fontWeight="700" color={textColor} mb={6}>Aktif Robot Aboneliklerim</Text>
            
            {subscriptions === null ? (
              <Flex justify="center" align="center" py={8}>
                <LoadingState text="Abonelikler yükleniyor..." />
              </Flex>
            ) : subscriptions.length === 0 ? (
              <Alert status="info" borderRadius="16px" variant="subtle">
                <AlertIcon />
                <Text fontSize="sm" color={textColorSecondary}>
                  Aktif robot aboneliğiniz bulunmamaktadır. Marketplace sayfasından robotlara göz atabilirsiniz.
                </Text>
              </Alert>
            ) : (
              <VStack spacing={4} align="stretch">
                {subscriptions.map((sub) => (
                  <Box key={sub.subscription_id} p={4} borderWidth={1} borderRadius="md" display="flex" justifyContent="space-between" alignItems="center" flexWrap="wrap">
                    <Flex align="center" mb={{ base: 2, md: 0 }}>
                      {/* Robot Görseli */}
                      <Image
                        src={sub.robot_image_url || 'https://placehold.co/40'}
                        alt={`${sub.robot_name} logo`}
                        boxSize="40px"
                        borderRadius="md"
                        mr={4}
                        objectFit="cover"
                        fallbackSrc='https://placehold.co/40'
                      />
                      <Box>
                        {/* Robot Adı */}
                        <Text fontWeight="bold">{sub.robot_name}</Text>
                        {/* Robot Strateji Tipi */}
                        {sub.robot_strategy_type && (
                          <Badge colorScheme="purple" size="sm" mt={1} textTransform="capitalize">
                            {sub.robot_strategy_type.replace('_', ' ')}
                          </Badge>
                        )}
                      </Box>
                    </Flex>
                    
                    <Flex align="center">
                      {/* Aktif/Pasif Durumu için Toggle Switch */}
                      <FormControl display="flex" alignItems="center" mr={4}>
                        <FormLabel htmlFor={`robot-active-${sub.subscription_id}`} mb="0" fontSize="sm">
                          {sub.is_active ? 'Aktif' : 'Pasif'}
                        </FormLabel>
                        <Switch
                          id={`robot-active-${sub.subscription_id}`}
                          colorScheme="brandScheme"
                          size="md"
                          isChecked={sub.is_active}
                          onChange={(e) => handleToggleSubscriptionStatus(sub.subscription_id, e.target.checked)}
                          isDisabled={isDeleting[sub.subscription_id]}
                        />
                      </FormControl>
                      {/* Abonelikten Çık Butonu */}
                      <Button
                        colorScheme="red"
                        variant="outline"
                        size="sm"
                        isLoading={isDeleting[sub.subscription_id]}
                        onClick={() => deleteSubscription(sub.subscription_id)}
                        minW="120px"
                        whiteSpace="normal"
                        wordBreak="break-word"
                        lineHeight="1.2"
                        px={3}
                        py={2}
                      >
                        Abonelikten Çık
                      </Button>
                    </Flex>
                  </Box>
                ))}
              </VStack>
            )}
          </Flex>
        </Card>
      </VStack>
    </Box>
  );
};

export default Management; 