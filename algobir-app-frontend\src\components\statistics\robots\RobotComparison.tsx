import React, { useState } from 'react';
import {
  Box,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Button,
  ButtonGroup,
  useColorModeValue,
  Icon,
  Alert,
  AlertIcon,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,

} from '@chakra-ui/react';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Legend,
  ScatterChart,
  Scatter,

} from 'recharts';
import { FiTarget, FiActivity, FiAward } from 'react-icons/fi';
import { EnhancedStatsData } from '../../../hooks/useEnhancedStatistics';

interface RobotComparisonProps {
  stats: EnhancedStatsData;
}

const RobotComparison: React.FC<RobotComparisonProps> = ({ stats }) => {
  const [comparisonMode, setComparisonMode] = useState<'performance' | 'risk' | 'efficiency'>('performance');
  
  const cardBg = useColorModeValue('white', 'gray.700');
  const textColor = useColorModeValue('gray.700', 'white');
  const accentColor = useColorModeValue('blue.500', 'blue.300');

  const formatCurrency = (value: number) => `₺${value.toFixed(2)}`;
  const formatPercent = (value: number) => `${value.toFixed(2)}%`;

  // Helper function to calculate volatility for individual robots
  const calculateRobotVolatility = (robot: any): number => {
    // Use the overall volatility from stats as a base, then adjust based on robot performance
    const baseVolatility = stats.volatility || 0;

    // If we have enough data, calculate robot-specific volatility
    if (robot.totalTrades > 10) {
      // Estimate volatility based on win rate and profit factor variability
      const winRateVolatility = Math.abs(robot.winRate - 50) / 50; // Normalize around 50%
      const profitFactorVolatility = robot.profitFactor > 1 ?
        Math.abs(robot.profitFactor - 1) / robot.profitFactor :
        1 - robot.profitFactor;

      return Math.max(baseVolatility, (winRateVolatility + profitFactorVolatility) * 10);
    }

    return baseVolatility;
  };

  // Combine solo and bro robot data
  const allRobots = [
    ...(stats.soloRobotStats ? [{
      ...stats.soloRobotStats,
      robotName: 'Solo-Robot',
      type: 'solo' as const,
      volatility: calculateRobotVolatility(stats.soloRobotStats)
    }] : []),
    ...stats.broRobotStats.map(robot => ({
      ...robot,
      type: 'bro' as const,
      volatility: calculateRobotVolatility(robot)
    }))
  ];

  if (allRobots.length === 0) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <VStack align="start" spacing={1}>
          <Text fontWeight="bold">Robot Karşılaştırması Yapılamıyor</Text>
          <Text fontSize="sm">
            Karşılaştırma yapabilmek için en az bir robot verisi gereklidir.
          </Text>
        </VStack>
      </Alert>
    );
  }

  // Performance comparison data
  const performanceData = allRobots.map(robot => ({
    name: robot.robotName.length > 12 ? robot.robotName.slice(0, 12) + '...' : robot.robotName,
    fullName: robot.robotName,
    roi: robot.roi,
    winRate: robot.winRate,
    profitFactor: robot.profitFactor,
    totalPnl: robot.totalPnl,
    totalTrades: robot.totalTrades,
    type: robot.type,
    sharpeRatio: robot.sharpeRatio,
    maxDrawdown: robot.maxDrawdown
  }));

  // Risk vs Return scatter data
  const riskReturnData = allRobots.map(robot => ({
    x: robot.maxDrawdown, // Risk (x-axis)
    y: robot.roi, // Return (y-axis)
    name: robot.robotName,
    type: robot.type,
    trades: robot.totalTrades
  }));

  // Radar chart data for multi-dimensional comparison
  // Each data point represents a metric, with values for each robot
  const topRobots = allRobots.slice(0, 3); // Limit to top 3 robots for clarity
  const radarData = [
    {
      metric: 'ROI',
      ...topRobots.reduce((acc, robot, index) => {
        acc[`robot${index + 1}`] = Math.max(0, Math.min(100, robot.roi + 50)); // Normalize to 0-100
        return acc;
      }, {} as Record<string, number>)
    },
    {
      metric: 'Kazanma Oranı',
      ...topRobots.reduce((acc, robot, index) => {
        acc[`robot${index + 1}`] = robot.winRate;
        return acc;
      }, {} as Record<string, number>)
    },
    {
      metric: 'Kar Faktörü',
      ...topRobots.reduce((acc, robot, index) => {
        acc[`robot${index + 1}`] = Math.min(100, robot.profitFactor * 20);
        return acc;
      }, {} as Record<string, number>)
    },
    {
      metric: 'İşlem Sayısı',
      ...topRobots.reduce((acc, robot, index) => {
        acc[`robot${index + 1}`] = Math.min(100, (robot.totalTrades / 100) * 100);
        return acc;
      }, {} as Record<string, number>)
    },
    {
      metric: 'Sharpe Oranı',
      ...topRobots.reduce((acc, robot, index) => {
        acc[`robot${index + 1}`] = Math.max(0, Math.min(100, (robot.sharpeRatio + 2) * 25));
        return acc;
      }, {} as Record<string, number>)
    }
  ];

  // Efficiency metrics
  const efficiencyData = allRobots.map(robot => ({
    name: robot.robotName.slice(0, 10),
    fullName: robot.robotName,
    tradesPerMonth: robot.totalTrades / Math.max(1, robot.monthlyPerformance.length),
    avgTradeSize: robot.averageTradeSize,
    profitPerTrade: robot.totalPnl / Math.max(1, robot.totalTrades),
    type: robot.type
  }));

  // Rankings
  const rankings = {
    byROI: [...allRobots].sort((a, b) => b.roi - a.roi),
    byWinRate: [...allRobots].sort((a, b) => b.winRate - a.winRate),
    byProfitFactor: [...allRobots].sort((a, b) => b.profitFactor - a.profitFactor),
    byTotalPnl: [...allRobots].sort((a, b) => b.totalPnl - a.totalPnl)
  };

  const getRankColor = (rank: number) => {
    if (rank === 1) return 'gold';
    if (rank === 2) return 'silver';
    if (rank === 3) return 'bronze';
    return 'gray';
  };

  const getRankIcon = (rank: number) => {
    if (rank <= 3) return FiAward;
    return FiActivity;
  };

  return (
    <VStack spacing={6} align="stretch">
      {/* Header */}
      <Box>
        <HStack spacing={4} mb={4}>
          <Icon as={FiTarget} boxSize={8} color={accentColor} />
          <VStack align="start" spacing={0}>
            <Heading size="lg" color={textColor}>Robot Performans Karşılaştırması</Heading>
            <Text color="gray.500">Tüm robotların detaylı karşılaştırmalı analizi</Text>
          </VStack>
        </HStack>
        
        <HStack spacing={4} flexWrap="wrap">
          <Badge colorScheme="blue" variant="subtle" px={3} py={1}>
            {allRobots.length} Robot
          </Badge>
          <Badge colorScheme="green" variant="subtle" px={3} py={1}>
            {stats.soloRobotStats ? 1 : 0} Solo
          </Badge>
          <Badge colorScheme="purple" variant="subtle" px={3} py={1}>
            {stats.broRobotStats.length} Bro
          </Badge>
        </HStack>
      </Box>

      {/* Comparison Mode Selector */}
      <Card bg={cardBg}>
        <CardBody>
          <HStack spacing={4}>
            <Text fontWeight="semibold">Karşılaştırma Türü:</Text>
            <ButtonGroup size="sm" isAttached variant="outline">
              <Button
                isActive={comparisonMode === 'performance'}
                onClick={() => setComparisonMode('performance')}
              >
                Performans
              </Button>
              <Button
                isActive={comparisonMode === 'risk'}
                onClick={() => setComparisonMode('risk')}
              >
                Risk Analizi
              </Button>
              <Button
                isActive={comparisonMode === 'efficiency'}
                onClick={() => setComparisonMode('efficiency')}
              >
                Verimlilik
              </Button>
            </ButtonGroup>
          </HStack>
        </CardBody>
      </Card>

      {/* Performance Comparison */}
      {comparisonMode === 'performance' && (
        <VStack spacing={6} align="stretch">
          {/* Performance Charts */}
          <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
            <Card bg={cardBg}>
              <CardHeader>
                <Heading size="md">ROI Karşılaştırması</Heading>
              </CardHeader>
              <CardBody>
                <Box h="300px">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={performanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <RechartsTooltip 
                        formatter={(value: number) => [formatPercent(value), 'ROI']}
                      />
                      <Bar 
                        dataKey="roi" 
                        fill={accentColor}
                        name="ROI %"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardBody>
            </Card>

            <Card bg={cardBg}>
              <CardHeader>
                <Heading size="md">Çok Boyutlu Karşılaştırma</Heading>
              </CardHeader>
              <CardBody>
                <Box h="300px">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart data={radarData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="metric" />
                      <PolarRadiusAxis angle={90} domain={[0, 100]} />
                      {topRobots.map((robot, index) => (
                        <Radar
                          key={robot.robotId}
                          name={robot.robotName.slice(0, 10)}
                          dataKey={`robot${index + 1}`}
                          stroke={['#3182CE', '#38A169', '#E53E3E'][index]}
                          fill={['#3182CE', '#38A169', '#E53E3E'][index]}
                          fillOpacity={0.1}
                        />
                      ))}
                      <Legend />
                    </RadarChart>
                  </ResponsiveContainer>
                </Box>
              </CardBody>
            </Card>
          </SimpleGrid>

          {/* Performance Rankings */}
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
            <Card bg={cardBg}>
              <CardHeader>
                <Heading size="md">🏆 ROI Sıralaması</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={3} align="stretch">
                  {rankings.byROI.slice(0, 5).map((robot, index) => (
                    <HStack key={robot.robotId} spacing={3}>
                      <Icon 
                        as={getRankIcon(index + 1)} 
                        color={`${getRankColor(index + 1)}.500`}
                        boxSize={5}
                      />
                      <Text flex={1} fontSize="sm">{robot.robotName}</Text>
                      <Badge 
                        colorScheme={robot.roi > 0 ? 'green' : 'red'}
                        variant="subtle"
                      >
                        {formatPercent(robot.roi)}
                      </Badge>
                    </HStack>
                  ))}
                </VStack>
              </CardBody>
            </Card>

            <Card bg={cardBg}>
              <CardHeader>
                <Heading size="md">🎯 Kazanma Oranı Sıralaması</Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={3} align="stretch">
                  {rankings.byWinRate.slice(0, 5).map((robot, index) => (
                    <HStack key={robot.robotId} spacing={3}>
                      <Icon 
                        as={getRankIcon(index + 1)} 
                        color={`${getRankColor(index + 1)}.500`}
                        boxSize={5}
                      />
                      <Text flex={1} fontSize="sm">{robot.robotName}</Text>
                      <Badge 
                        colorScheme={robot.winRate > 50 ? 'green' : 'red'}
                        variant="subtle"
                      >
                        {formatPercent(robot.winRate)}
                      </Badge>
                    </HStack>
                  ))}
                </VStack>
              </CardBody>
            </Card>
          </SimpleGrid>
        </VStack>
      )}

      {/* Risk Analysis */}
      {comparisonMode === 'risk' && (
        <VStack spacing={6} align="stretch">
          <Card bg={cardBg}>
            <CardHeader>
              <Heading size="md">Risk vs Getiri Analizi</Heading>
            </CardHeader>
            <CardBody>
              <Box h={{ base: "300px", md: "400px" }} w="100%" overflow="hidden">
                <ResponsiveContainer width="100%" height="100%">
                  <ScatterChart
                    data={riskReturnData}
                    margin={{ top: 20, right: 30, bottom: 60, left: 60 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="x"
                      type="number"
                      name="Risk (Max Drawdown %)"
                      label={{
                        value: 'Risk (Max Drawdown %)',
                        position: 'insideBottom',
                        offset: -5,
                        textAnchor: 'middle'
                      }}
                      domain={['dataMin - 1', 'dataMax + 1']}
                    />
                    <YAxis
                      dataKey="y"
                      type="number"
                      name="Getiri (ROI %)"
                      label={{
                        value: 'Getiri (ROI %)',
                        angle: -90,
                        position: 'insideLeft',
                        textAnchor: 'middle'
                      }}
                      domain={['dataMin - 2', 'dataMax + 2']}
                    />
                    <RechartsTooltip
                      formatter={(value: number, name: string) => {
                        if (name === 'Risk (Max Drawdown %)') return [formatPercent(value), 'Risk'];
                        if (name === 'Getiri (ROI %)') return [formatPercent(value), 'Getiri'];
                        return [value, name];
                      }}
                      labelFormatter={(_label, payload) => {
                        if (payload && payload.length > 0) {
                          return payload[0]?.payload?.name || '';
                        }
                        return '';
                      }}
                    />
                    <Scatter
                      dataKey="y"
                      fill={accentColor}
                      r={6}
                    />
                  </ScatterChart>
                </ResponsiveContainer>
              </Box>
            </CardBody>
          </Card>

          {/* Risk Metrics Table */}
          <Card bg={cardBg}>
            <CardHeader>
              <Heading size="md">Risk Metrikleri Karşılaştırması</Heading>
            </CardHeader>
            <CardBody>
              <TableContainer>
                <Table size="sm">
                  <Thead>
                    <Tr>
                      <Th>Robot</Th>
                      <Th isNumeric>Max Drawdown</Th>
                      <Th isNumeric>Sharpe Ratio</Th>
                      <Th isNumeric>Volatilite</Th>
                      <Th>Risk Seviyesi</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {allRobots.map((robot) => {
                      const riskLevel = robot.maxDrawdown > 20 ? 'Yüksek' : 
                                       robot.maxDrawdown > 10 ? 'Orta' : 'Düşük';
                      const riskColor = robot.maxDrawdown > 20 ? 'red' : 
                                       robot.maxDrawdown > 10 ? 'orange' : 'green';
                      
                      return (
                        <Tr key={robot.robotId}>
                          <Td>
                            <HStack spacing={2}>
                              <Text fontSize="sm">{robot.robotName}</Text>
                              <Badge 
                                size="sm" 
                                colorScheme={robot.type === 'solo' ? 'blue' : 'purple'}
                              >
                                {robot.type}
                              </Badge>
                            </HStack>
                          </Td>
                          <Td isNumeric>
                            <Text color="red.500">{formatPercent(robot.maxDrawdown)}</Text>
                          </Td>
                          <Td isNumeric>
                            <Text color={robot.sharpeRatio > 1 ? 'green.500' : 'orange.500'}>
                              {robot.sharpeRatio.toFixed(2)}
                            </Text>
                          </Td>
                          <Td isNumeric>
                            <Text color={robot.volatility > 20 ? 'red.500' : robot.volatility > 10 ? 'orange.500' : 'green.500'}>
                              {robot.volatility ? `${robot.volatility.toFixed(2)}%` : 'N/A'}
                            </Text>
                          </Td>
                          <Td>
                            <Badge colorScheme={riskColor} variant="subtle">
                              {riskLevel}
                            </Badge>
                          </Td>
                        </Tr>
                      );
                    })}
                  </Tbody>
                </Table>
              </TableContainer>
            </CardBody>
          </Card>
        </VStack>
      )}

      {/* Efficiency Analysis */}
      {comparisonMode === 'efficiency' && (
        <VStack spacing={6} align="stretch">
          <Card bg={cardBg}>
            <CardHeader>
              <Heading size="md">İşlem Verimliliği</Heading>
            </CardHeader>
            <CardBody>
              <Box h="300px">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={efficiencyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip 
                      formatter={(value: number) => [formatCurrency(value), 'İşlem Başına Kar']}
                    />
                    <Bar dataKey="profitPerTrade" fill={accentColor} name="İşlem Başına Kar" />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardBody>
          </Card>

          {/* Efficiency Metrics */}
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
            {allRobots.map((robot) => (
              <Card key={robot.robotId} bg={cardBg}>
                <CardBody>
                  <VStack spacing={3} align="stretch">
                    <HStack spacing={2}>
                      <Text fontWeight="bold" fontSize="sm">{robot.robotName}</Text>
                      <Badge 
                        size="sm" 
                        colorScheme={robot.type === 'solo' ? 'blue' : 'purple'}
                      >
                        {robot.type}
                      </Badge>
                    </HStack>
                    
                    <VStack spacing={2} align="stretch">
                      <HStack justify="space-between">
                        <Text fontSize="xs">İşlem/Ay:</Text>
                        <Text fontSize="xs" fontWeight="bold">
                          {(robot.totalTrades / Math.max(1, robot.monthlyPerformance.length)).toFixed(1)}
                        </Text>
                      </HStack>
                      
                      <HStack justify="space-between">
                        <Text fontSize="xs">İşlem Başına Kar:</Text>
                        <Text 
                          fontSize="xs" 
                          fontWeight="bold"
                          color={robot.totalPnl > 0 ? 'green.500' : 'red.500'}
                        >
                          {formatCurrency(robot.totalPnl / Math.max(1, robot.totalTrades))}
                        </Text>
                      </HStack>
                      
                      <HStack justify="space-between">
                        <Text fontSize="xs">Ortalama İşlem:</Text>
                        <Text fontSize="xs" fontWeight="bold">
                          {formatCurrency(robot.averageTradeSize)}
                        </Text>
                      </HStack>
                    </VStack>
                  </VStack>
                </CardBody>
              </Card>
            ))}
          </SimpleGrid>
        </VStack>
      )}
    </VStack>
  );
};

export default RobotComparison;
