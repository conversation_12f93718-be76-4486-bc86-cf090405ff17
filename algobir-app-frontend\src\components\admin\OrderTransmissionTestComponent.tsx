import React from 'react';
import {
  Box,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  VStack,
  H<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ner,
  <PERSON><PERSON>,
  <PERSON>ertIcon
} from '@chakra-ui/react';
import useOrderTransmissionMetrics from '../../hooks/useOrderTransmissionMetrics';

/**
 * Test component to verify that the useOrderTransmissionMetrics hook
 * works correctly without causing hook order violations
 */
const OrderTransmissionTestComponent: React.FC = () => {
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');

  // Test the hook with different configurations
  const {
    metrics,
    stats,
    loading,
    error,
    isConnected,
    lastUpdate
  } = useOrderTransmissionMetrics({
    timeRangeHours: 24,
    enabled: true,
    autoRefresh: true,
    refreshInterval: 30000
  });

  return (
    <Card
      bg={cardBg}
      borderRadius="xl"
      boxShadow="md"
      border="1px solid"
      borderColor={borderColor}
    >
      <CardHeader>
        <Heading size="md" color={textColor}>
          Order Transmission Metrics Test
        </Heading>
      </CardHeader>
      <CardBody>
        <VStack spacing={4} align="stretch">
          {/* Connection Status */}
          <HStack justify="space-between">
            <Text fontSize="sm" fontWeight="medium">Connection Status:</Text>
            <Badge colorScheme={isConnected ? 'green' : 'red'}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </HStack>

          {/* Loading State */}
          <HStack justify="space-between">
            <Text fontSize="sm" fontWeight="medium">Loading:</Text>
            {loading ? <Spinner size="sm" /> : <Badge colorScheme="green">Ready</Badge>}
          </HStack>

          {/* Error State */}
          {error && (
            <Alert status="error" size="sm">
              <AlertIcon />
              <Text fontSize="sm">{error}</Text>
            </Alert>
          )}

          {/* Data Status */}
          <HStack justify="space-between">
            <Text fontSize="sm" fontWeight="medium">Metrics Count:</Text>
            <Badge>{metrics.length}</Badge>
          </HStack>

          <HStack justify="space-between">
            <Text fontSize="sm" fontWeight="medium">Stats Available:</Text>
            <Badge colorScheme={stats ? 'green' : 'gray'}>
              {stats ? 'Yes' : 'No'}
            </Badge>
          </HStack>

          {/* Last Update */}
          {lastUpdate && (
            <HStack justify="space-between">
              <Text fontSize="sm" fontWeight="medium">Last Update:</Text>
              <Text fontSize="sm">{lastUpdate.toLocaleTimeString()}</Text>
            </HStack>
          )}

          {/* Stats Summary */}
          {stats && (
            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2}>Stats Summary:</Text>
              <VStack spacing={1} align="stretch" fontSize="xs">
                <HStack justify="space-between">
                  <Text>Total Signals:</Text>
                  <Text>{stats.total_signals}</Text>
                </HStack>
                <HStack justify="space-between">
                  <Text>Success Rate:</Text>
                  <Text>{stats.success_rate?.toFixed(1)}%</Text>
                </HStack>
                <HStack justify="space-between">
                  <Text>Avg Processing Time:</Text>
                  <Text>{stats.avg_total_processing_time_ms?.toFixed(1)}ms</Text>
                </HStack>
              </VStack>
            </Box>
          )}
        </VStack>
      </CardBody>
    </Card>
  );
};

export default OrderTransmissionTestComponent;
