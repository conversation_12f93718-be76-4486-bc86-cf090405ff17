import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>lay,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  ModalCloseButton,
  ModalBody,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepIndicator,
  StepStatus,
  StepIcon,
  StepNumber,
  StepTitle,
  StepDescription,
  Box,
  VStack,
  FormControl,
  FormLabel,
  Input,
  FormErrorMessage,
  Text,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  HStack,
  useSteps,
  useToast,
} from '@chakra-ui/react';
import { useForm } from 'react-hook-form';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../supabaseClient';

const steps = [
  { title: 'Webhook URL', description: 'Borsanıza iletim adresi' },
  { title: 'API Anahtarı', description: 'Güvenli borsa bağlantısı' },
];

interface InitialSetupFormData {
  webhookUrl: string;
  apiKey: string;
  apiSecret: string;
}

interface InitialSetupWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
}

export const InitialSetupWizard: React.FC<InitialSetupWizardProps> = ({ isOpen, onClose, onComplete }) => {
  const { user } = useAuth();
  const toast = useToast();
  const { activeStep, goToNext, goToPrevious } = useSteps({
    index: 0,
    count: steps.length,
  });

  const [isLoading, setIsLoading] = React.useState(false);

  const methods = useForm<InitialSetupFormData>({
    defaultValues: {
      webhookUrl: 'https://panel.algobir.com',
      apiKey: '12345678901',
      apiSecret: '12345678901',
    },
  });

  const { register, handleSubmit, formState: { errors }, trigger } = methods;

  const validateCurrentStep = async (): Promise<boolean> => {
    switch (activeStep) {
      case 0: // Webhook URL step
        return await trigger('webhookUrl');
      case 1: // API Key step
        return await trigger(['apiKey', 'apiSecret']);
      default:
        return true;
    }
  };

  const handleNext = async () => {
    const isValid = await validateCurrentStep();
    if (isValid) {
      goToNext();
    } else {
      toast({
        title: "Lütfen tüm gerekli alanları doldurun",
        status: 'warning',
        isClosable: true,
      });
    }
  };

  const onSubmit = async (data: InitialSetupFormData) => {
    if (!user) {
      toast({ title: 'Giriş yapmanız gerekiyor.', status: 'error' });
      return;
    }

    setIsLoading(true);
    
    try {
      // 1. Save webhook URL and mark setup as complete
      const { error: rpcError } = await supabase.rpc('complete_initial_setup', {
        p_webhook_url: data.webhookUrl.trim(),
      });

      if (rpcError) {
        throw new Error(`Webhook URL kaydedilemedi: ${rpcError.message}`);
      }

      // 2. Save API keys securely
      const { error: functionError } = await supabase.functions.invoke('secure-save-api-key', {
        body: {
          apiKey: data.apiKey.trim(),
          apiSecret: data.apiSecret.trim(),
        },
      });

      if (functionError) {
        throw new Error(`API anahtarları kaydedilemedi: ${functionError.message}`);
      }

      toast({
        title: 'Kurulum tamamlandı!',
        description: 'Artık Algobir\'i kullanmaya başlayabilirsiniz.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      onComplete(); // Refresh auth context and close modal
    } catch (error: any) {
      console.error('Initial setup error:', error);
      toast({
        title: 'Kurulum hatası',
        description: error.message,
        status: 'error',
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <VStack spacing={4} align="stretch">
            <Alert status="info" borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertTitle>Webhook URL Nedir?</AlertTitle>
                <AlertDescription>
                  Bu adres, Algobir tarafından oluşturulan ticaret emirlerinin borsanıza iletileceği URL'dir. 
                  Genellikle aracı kurumunuzun API webhook endpoint'idir. Aracı kurum entegrasyonları devam ettiğinden bu alan demo değerleri ile doldurulmuştur.
                </AlertDescription>
              </Box>
            </Alert>
            
            <FormControl isInvalid={!!errors.webhookUrl}>
              <FormLabel>Webhook URL</FormLabel>
              <Input
                {...register('webhookUrl', {
                  required: 'Webhook URL gereklidir',
                  pattern: {
                    value: /^https?:\/\/.+/,
                    message: 'Geçerli bir URL giriniz (http:// veya https:// ile başlamalı)'
                  }
                })}
                placeholder="https://api.yourbroker.com/webhook"
                size="lg"
                autoComplete="url"
              />
              <FormErrorMessage>{errors.webhookUrl?.message}</FormErrorMessage>
            </FormControl>
          </VStack>
        );

      case 1:
        return (
          <VStack spacing={4} align="stretch">
            <Alert status="info" borderRadius="md" mb={4}>
              <AlertIcon />
              <Box>
                <AlertTitle>Aracı Kurum Entegrasyonu Devam Ediyor!</AlertTitle>
                <AlertDescription>
                  Bu alanlar demo değerleridir. Aracı kurum entegrasyonları devam ettiğinden bu alanları geçici olarakdoldurmanız gerekmektedir.
                </AlertDescription>
              </Box>
            </Alert>

            <Alert status="warning" borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertTitle>Güvenlik Uyarısı</AlertTitle>
                <AlertDescription>
                  API anahtarlarınız şifrelenmiş olarak güvenli bir şekilde saklanacaktir. 
                  Bu bilgiler sadece ticaret emirlerinizi borsanıza göndermek için kullanılır.
                </AlertDescription>
              </Box>
            </Alert>

            <FormControl isInvalid={!!errors.apiKey}>
              <FormLabel>API Anahtarı</FormLabel>
              <Input
                {...register('apiKey', {
                  required: 'API anahtarı gereklidir',
                  minLength: { value: 10, message: 'API anahtarı en az 10 karakter olmalıdır' }
                })}
                placeholder="Borsa API anahtarınız"
                size="lg"
                type="password"
                autoComplete="new-password"
              />
              <FormErrorMessage>{errors.apiKey?.message}</FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.apiSecret}>
              <FormLabel>API Secret</FormLabel>
              <Input
                {...register('apiSecret', {
                  required: 'API secret gereklidir',
                  minLength: { value: 10, message: 'API secret en az 10 karakter olmalıdır' }
                })}
                placeholder="Borsa API secret anahtarınız"
                size="lg"
                type="password"
                autoComplete="new-password"
              />
              <FormErrorMessage>{errors.apiSecret?.message}</FormErrorMessage>
            </FormControl>
          </VStack>
        );

      default:
        return null;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" closeOnOverlayClick={false}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <Text fontSize="xl" fontWeight="bold">İlk Kurulum</Text>
          <Text fontSize="sm" color="gray.600" mt={1}>
            Algobir'i kullanmaya başlamak için aşağıdaki adımları tamamlayın
          </Text>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <Box as="form" onSubmit={handleSubmit(onSubmit)}>
            <Stepper index={activeStep} mb={6} colorScheme="brand">
              {steps.map((step, index) => (
                <Step key={index}>
                  <StepIndicator>
                    <StepStatus 
                      complete={<StepIcon />} 
                      incomplete={<StepNumber />} 
                      active={<StepNumber />} 
                    />
                  </StepIndicator>
                  <Box flexShrink="0">
                    <StepTitle>{step.title}</StepTitle>
                    <StepDescription>{step.description}</StepDescription>
                  </Box>
                </Step>
              ))}
            </Stepper>

            <Box minH="300px" p={4} borderWidth={1} borderRadius="md" bg="gray.50">
              {renderStepContent()}
            </Box>
          </Box>
        </ModalBody>

        <ModalFooter>
          <HStack w="100%" justify="space-between">
            <Button variant="ghost" onClick={onClose}>
              Şimdi Değil
            </Button>
            
            <HStack>
              <Button onClick={goToPrevious} isDisabled={activeStep === 0}>
                Geri
              </Button>

              {activeStep < steps.length - 1 ? (
                <Button colorScheme="brand" onClick={handleNext}>
                  İleri
                </Button>
              ) : (
                <Button 
                  colorScheme="brand" 
                  onClick={handleSubmit(onSubmit)}
                  isLoading={isLoading}
                  loadingText="Kurulum Tamamlanıyor..."
                >
                  Kurulumu Tamamla
                </Button>
              )}
            </HStack>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}; 