name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}
  SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: algobir-app-frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: ./algobir-app-frontend
      run: npm ci
      
    - name: Run TypeScript check
      working-directory: ./algobir-app-frontend
      run: npm run type-check
      
    - name: Run unit tests
      working-directory: ./algobir-app-frontend
      run: npm run test:unit
      
    - name: Run build
      working-directory: ./algobir-app-frontend
      run: npm run build
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files
        path: algobir-app-frontend/dist/
        retention-days: 1

  e2e-test:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: algobir-app-frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: ./algobir-app-frontend
      run: npm ci
      
    - name: Install Playwright browsers
      working-directory: ./algobir-app-frontend
      run: npx playwright install --with-deps
      
    - name: Run E2E tests
      working-directory: ./algobir-app-frontend
      run: npm run test:e2e
      
    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-test-results
        path: |
          algobir-app-frontend/test-results/
          algobir-app-frontend/playwright-report/
        retention-days: 7

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-files
        path: algobir-app-frontend/dist/
        
    - name: Deploy to Vercel (Staging)
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./algobir-app-frontend
        scope: ${{ secrets.VERCEL_ORG_ID }}
        alias-domains: algobir-staging.vercel.app

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, e2e-test]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-files
        path: algobir-app-frontend/dist/
        
    - name: Deploy to Vercel (Production)
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
        vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
        working-directory: ./algobir-app-frontend
        scope: ${{ secrets.VERCEL_ORG_ID }}
        vercel-args: '--prod'
        alias-domains: algobir.vercel.app
        
    - name: Notify deployment success
      if: success()
      run: |
        echo "🚀 Production deployment successful!"
        echo "URL: https://algobir.vercel.app"
