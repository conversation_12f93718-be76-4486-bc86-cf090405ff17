import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  SimpleGrid,
  Progress,
  Badge,
  Icon,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  CircularProgress,
  CircularProgressLabel,

} from '@chakra-ui/react';
import {
  FaMicrochip,
  FaHdd,
  FaNetworkWired,
  FaServer,
  FaChartLine,
  FaExclamationTriangle,
  FaCheckCircle,
  FaClock
} from 'react-icons/fa';
import { motion } from 'framer-motion';
import { SystemMetrics } from '../../hooks/useSystemMonitoring';

const MotionBox = motion(Box);

interface RealTimeSystemMetricsProps {
  metrics: SystemMetrics;
  loading?: boolean;
  lastUpdate?: Date;
}

const RealTimeSystemMetrics: React.FC<RealTimeSystemMetricsProps> = ({
  metrics,
  loading = false,
  lastUpdate
}) => {
  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  // Helper function to get performance color
  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'green';
    if (value <= thresholds.warning) return 'yellow';
    return 'red';
  };

  // Helper function to get metric status
  const getMetricStatus = (value: number, type: 'usage' | 'performance') => {
    if (type === 'usage') {
      if (value <= 50) return { color: 'green', status: 'İyi' };
      if (value <= 80) return { color: 'yellow', status: 'Orta' };
      return { color: 'red', status: 'Yüksek' };
    } else {
      if (value <= 100) return { color: 'green', status: 'Hızlı' };
      if (value <= 500) return { color: 'yellow', status: 'Orta' };
      return { color: 'red', status: 'Yavaş' };
    }
  };

  const cpuStatus = getMetricStatus(metrics.cpu, 'usage');
  const memoryStatus = getMetricStatus(metrics.memory, 'usage');
  const responseTimeStatus = getMetricStatus(metrics.responseTime, 'performance');

  return (
    <Card
      bg={cardBg}
      borderRadius="xl"
      boxShadow="lg"
      border="1px solid"
      borderColor={borderColor}
      overflow="hidden"
    >
      <CardHeader pb={2}>
        <HStack justify="space-between" align="center">
          <VStack align="start" spacing={1}>
            <Heading size="md" color={textColor}>
              Sistem Metrikleri
            </Heading>
            <HStack spacing={2}>
              <Icon
                as={loading ? FaClock : FaCheckCircle}
                color={loading ? 'yellow.500' : 'green.500'}
                boxSize={3}
              />
              <Text fontSize="xs" color={mutedColor}>
                {loading ? 'Güncelleniyor...' : 
                 lastUpdate ? `Son güncelleme: ${lastUpdate.toLocaleTimeString('tr-TR')}` : 
                 'Veri yok'}
              </Text>
            </HStack>
          </VStack>
        </HStack>
      </CardHeader>

      <CardBody pt={0}>
        <VStack spacing={6} align="stretch">
          {/* Resource Usage Metrics */}
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
            <MotionBox
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <VStack spacing={2}>
                <CircularProgress
                  value={metrics.cpu}
                  color={`${cpuStatus.color}.400`}
                  size="60px"
                  thickness="8px"
                >
                  <CircularProgressLabel fontSize="xs" fontWeight="bold">
                    {Math.round(metrics.cpu)}%
                  </CircularProgressLabel>
                </CircularProgress>
                <VStack spacing={0}>
                  <HStack spacing={1}>
                    <Icon as={FaMicrochip} color={`${cpuStatus.color}.500`} boxSize={3} />
                    <Text fontSize="sm" fontWeight="medium">CPU</Text>
                  </HStack>
                  <Badge colorScheme={cpuStatus.color} variant="subtle" fontSize="xs">
                    {cpuStatus.status}
                  </Badge>
                </VStack>
              </VStack>
            </MotionBox>

            <MotionBox
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <VStack spacing={2}>
                <CircularProgress
                  value={metrics.memory}
                  color={`${memoryStatus.color}.400`}
                  size="60px"
                  thickness="8px"
                >
                  <CircularProgressLabel fontSize="xs" fontWeight="bold">
                    {Math.round(metrics.memory)}%
                  </CircularProgressLabel>
                </CircularProgress>
                <VStack spacing={0}>
                  <HStack spacing={1}>
                    <Icon as={FaServer} color={`${memoryStatus.color}.500`} boxSize={3} />
                    <Text fontSize="sm" fontWeight="medium">Bellek</Text>
                  </HStack>
                  <Badge colorScheme={memoryStatus.color} variant="subtle" fontSize="xs">
                    {memoryStatus.status}
                  </Badge>
                </VStack>
              </VStack>
            </MotionBox>

            <MotionBox
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <VStack spacing={2}>
                <CircularProgress
                  value={metrics.disk}
                  color={getPerformanceColor(metrics.disk, { good: 50, warning: 80}) + '.400'}
                  size="60px"
                  thickness="8px"
                >
                  <CircularProgressLabel fontSize="xs" fontWeight="bold">
                    {Math.round(metrics.disk)}%
                  </CircularProgressLabel>
                </CircularProgress>
                <VStack spacing={0}>
                  <HStack spacing={1}>
                    <Icon as={FaHdd} color={getPerformanceColor(metrics.disk, { good: 50, warning: 80}) + '.500'} boxSize={3} />
                    <Text fontSize="sm" fontWeight="medium">Disk</Text>
                  </HStack>
                  <Badge 
                    colorScheme={getPerformanceColor(metrics.disk, { good: 50, warning: 80})} 
                    variant="subtle" 
                    fontSize="xs"
                  >
                    {getMetricStatus(metrics.disk, 'usage').status}
                  </Badge>
                </VStack>
              </VStack>
            </MotionBox>

            <MotionBox
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <VStack spacing={2}>
                <CircularProgress
                  value={Math.min(100, metrics.network)}
                  color={getPerformanceColor(metrics.network, { good: 30, warning: 60}) + '.400'}
                  size="60px"
                  thickness="8px"
                >
                  <CircularProgressLabel fontSize="xs" fontWeight="bold">
                    {Math.round(metrics.network)}%
                  </CircularProgressLabel>
                </CircularProgress>
                <VStack spacing={0}>
                  <HStack spacing={1}>
                    <Icon as={FaNetworkWired} color={getPerformanceColor(metrics.network, { good: 30, warning: 60}) + '.500'} boxSize={3} />
                    <Text fontSize="sm" fontWeight="medium">Ağ</Text>
                  </HStack>
                  <Badge 
                    colorScheme={getPerformanceColor(metrics.network, { good: 30, warning: 60})} 
                    variant="subtle" 
                    fontSize="xs"
                  >
                    {getMetricStatus(metrics.network, 'usage').status}
                  </Badge>
                </VStack>
              </VStack>
            </MotionBox>
          </SimpleGrid>

          {/* Performance Metrics */}
          <SimpleGrid columns={{ base: 2, md: 3 }} spacing={4}>
            <Stat>
              <StatLabel fontSize="sm" color={mutedColor}>
                <HStack spacing={1}>
                  <Icon as={FaChartLine} boxSize={3} />
                  <Text>İstek Sayısı</Text>
                </HStack>
              </StatLabel>
              <StatNumber fontSize="lg" color={textColor}>
                {metrics.requests.toLocaleString('tr-TR')}
              </StatNumber>
              <StatHelpText fontSize="xs">
                Toplam API istekleri
              </StatHelpText>
            </Stat>

            <Stat>
              <StatLabel fontSize="sm" color={mutedColor}>
                <HStack spacing={1}>
                  <Icon as={FaClock} boxSize={3} />
                  <Text>Yanıt Süresi</Text>
                </HStack>
              </StatLabel>
              <StatNumber fontSize="lg" color={`${responseTimeStatus.color}.500`}>
                {metrics.responseTime}ms
              </StatNumber>
              <StatHelpText fontSize="xs">
                Ortalama yanıt süresi
              </StatHelpText>
            </Stat>

            <Stat>
              <StatLabel fontSize="sm" color={mutedColor}>
                <HStack spacing={1}>
                  <Icon 
                    as={metrics.errors > 0 ? FaExclamationTriangle : FaCheckCircle} 
                    boxSize={3}
                    color={metrics.errors > 0 ? 'red.500' : 'green.500'}
                  />
                  <Text>Hata Sayısı</Text>
                </HStack>
              </StatLabel>
              <StatNumber fontSize="lg" color={metrics.errors > 0 ? 'red.500' : 'green.500'}>
                {metrics.errors}
              </StatNumber>
              <StatHelpText fontSize="xs">
                Toplam hata sayısı
              </StatHelpText>
            </Stat>
          </SimpleGrid>

          {/* Additional Metrics */}
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2} color={textColor}>
                İşlem Hızı
              </Text>
              <HStack justify="space-between" mb={1}>
                <Text fontSize="xs" color={mutedColor}>
                  {metrics.throughput.toFixed(2)} req/s
                </Text>
                <Badge colorScheme="blue" variant="subtle" fontSize="xs">
                  Aktif
                </Badge>
              </HStack>
              <Progress
                value={Math.min(100, metrics.throughput * 10)}
                colorScheme="blue"
                size="sm"
                borderRadius="full"
              />
            </Box>

            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2} color={textColor}>
                Aktif Bağlantılar
              </Text>
              <HStack justify="space-between" mb={1}>
                <Text fontSize="xs" color={mutedColor}>
                  {metrics.activeConnections} bağlantı
                </Text>
                <Badge colorScheme="green" variant="subtle" fontSize="xs">
                  Stabil
                </Badge>
              </HStack>
              <Progress
                value={(metrics.activeConnections / 10) * 100}
                colorScheme="green"
                size="sm"
                borderRadius="full"
              />
            </Box>

            <Box>
              <Text fontSize="sm" fontWeight="medium" mb={2} color={textColor}>
                Önbellek İsabet Oranı
              </Text>
              <HStack justify="space-between" mb={1}>
                <Text fontSize="xs" color={mutedColor}>
                  %{metrics.cacheHitRate.toFixed(1)}
                </Text>
                <Badge 
                  colorScheme={metrics.cacheHitRate >= 80 ? 'green' : metrics.cacheHitRate >= 60 ? 'yellow' : 'red'} 
                  variant="subtle" 
                  fontSize="xs"
                >
                  {metrics.cacheHitRate >= 80 ? 'İyi' : metrics.cacheHitRate >= 60 ? 'Orta' : 'Düşük'}
                </Badge>
              </HStack>
              <Progress
                value={metrics.cacheHitRate}
                colorScheme={metrics.cacheHitRate >= 80 ? 'green' : metrics.cacheHitRate >= 60 ? 'yellow' : 'red'}
                size="sm"
                borderRadius="full"
              />
            </Box>
          </SimpleGrid>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default RealTimeSystemMetrics;
