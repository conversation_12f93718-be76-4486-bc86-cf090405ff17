import { extendTheme, ThemingProps, HTMLChakraProps } from '@chakra-ui/react';
import { CardComponent } from './additions/card/card';
import { mode } from '@chakra-ui/theme-tools';

// Horizon UI'dan gü<PERSON> stil tanımları
const globalStyles = {
  colors: {
    brand: {
      100: '#E9E3FF',
      200: '#422AFB',
      300: '#422AFB',
      400: '#7551FF',
      500: '#422AFB',
      600: '#3311DB',
      700: '#02044A',
      800: '#190793',
      900: '#11047A'
    },
    brandScheme: {
      100: '#E9E3FF',
      200: '#7551FF',
      300: '#7551FF',
      400: '#7551FF',
      500: '#422AFB',
      600: '#3311DB',
      700: '#02044A',
      800: '#190793',
      900: '#02044A'
    },
    brandTabs: {
      100: '#E9E3FF',
      200: '#422AFB',
      300: '#422AFB',
      400: '#422AFB',
      500: '#422AFB',
      600: '#3311DB',
      700: '#02044A',
      800: '#190793',
      900: '#02044A'
    },
    secondaryGray: {
      100: '#E0E5F2',
      200: '#E1E9F8',
      300: '#F4F7FE',
      400: '#E9EDF7',
      500: '#8F9BBA',
      600: '#A3AED0',
      700: '#707EAE',
      800: '#707EAE',
      900: '#1B2559'
    },
    red: {
      100: '#FEEFEE',
      500: '#EE5D50',
      600: '#E31A1A'
    },
    blue: {
      50: '#EFF4FB',
      500: '#3965FF'
    },
    orange: {
      100: '#FFF6DA',
      500: '#FFB547'
    },
    green: {
      100: '#E6FAF5',
      500: '#01B574'
    },
    navy: {
      50: '#d0dcfb',
      100: '#aac0fe',
      200: '#a3b9f8',
      300: '#728fea',
      400: '#3652ba',
      500: '#1b3bbb',
      600: '#24388a',
      700: '#1B254B',
      800: '#111c44',
      900: '#0b1437'
    },
    gray: {
      100: '#FAFCFE'
    }
  },
  styles: {
    global: (props: any) => ({
      body: {
        overflowX: 'hidden',
        bg: mode('secondaryGray.300', 'navy.900')(props),
        fontFamily: 'DM Sans',
        letterSpacing: '-0.5px'
      },
      input: {
        color: 'gray.700'
      },
      html: {
        fontFamily: 'DM Sans'
      },
      // Chakra UI reset
      'html, body': {
        WebkitFontSmoothing: 'antialiased',
        MozOsxFontSmoothing: 'grayscale',
      },
      // Ek olarak görünüm iyileştirmeleri
      '::-webkit-scrollbar': {
        width: '8px',
        height: '8px',
      },
      '::-webkit-scrollbar-thumb': {
        background: props.colorMode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
        borderRadius: '8px',
      },
      '::-webkit-scrollbar-track': {
        background: props.colorMode === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.1)',
      },
    })
  }
};

// Bileşen stilleri
const components = {
  Button: {
    baseStyle: {
      borderRadius: '16px',
      boxShadow: '45px 76px 113px 7px rgba(112, 144, 176, 0.08)',
      transition: '.25s all ease',
      boxSizing: 'border-box',
      _focus: {
        boxShadow: 'none'
      },
      _active: {
        boxShadow: 'none'
      },
      fontSize: { base: 'sm', md: 'md' },
      fontWeight: '500'
    },
    sizes: {
      xs: {
        fontSize: '10px',
        px: '14px',
        py: '8px',
        h: { base: '28px', md: '32px' }
      },
      sm: {
        fontSize: '12px',
        px: '16px',
        py: '8px',
        h: { base: '32px', md: '36px' }
      },
      md: {
        fontSize: '14px',
        px: '20px',
        py: '10px',
        h: { base: '36px', md: '40px' }
      },
      lg: {
        fontSize: '16px',
        px: '24px',
        py: '12px',
        h: { base: '40px', md: '48px' }
      }
    },
    variants: {
      brand: (props: any) => ({
        bg: mode('brand.500', 'brand.400')(props),
        color: 'white',
        _focus: {
          bg: mode('brand.500', 'brand.400')(props)
        },
        _active: {
          bg: mode('brand.500', 'brand.400')(props)
        },
        _hover: {
          bg: mode('brand.600', 'brand.400')(props)
        }
      }),
      outline: (props: any) => ({
        borderRadius: '16px',
        border: '1px solid',
        borderColor: mode('secondaryGray.200', 'whiteAlpha.200')(props),
        color: mode('secondaryGray.900', 'white')(props),
        bg: 'transparent',
        _hover: {
          bg: mode('secondaryGray.100', 'whiteAlpha.100')(props)
        },
        _active: {
          bg: mode('secondaryGray.200', 'whiteAlpha.200')(props)
        }
      }),
      darkBrand: (props: any) => ({
        bg: mode('brand.900', 'brand.400')(props),
        color: 'white',
        _focus: {
          bg: mode('brand.900', 'brand.400')(props)
        },
        _active: {
          bg: mode('brand.900', 'brand.400')(props)
        },
        _hover: {
          bg: mode('brand.800', 'brand.400')(props)
        }
      }),
      lightBrand: (props: any) => ({
        bg: mode('#F2EFFF', 'whiteAlpha.100')(props),
        color: mode('brand.500', 'white')(props),
        _focus: {
          bg: mode('#F2EFFF', 'whiteAlpha.100')(props)
        },
        _active: {
          bg: mode('secondaryGray.300', 'whiteAlpha.100')(props)
        },
        _hover: {
          bg: mode('secondaryGray.400', 'whiteAlpha.200')(props)
        }
      }),
      light: (props: any) => ({
        bg: mode('secondaryGray.300', 'whiteAlpha.100')(props),
        color: mode('secondaryGray.900', 'white')(props),
        _focus: {
          bg: mode('secondaryGray.300', 'whiteAlpha.100')(props)
        },
        _active: {
          bg: mode('secondaryGray.300', 'whiteAlpha.100')(props)
        },
        _hover: {
          bg: mode('secondaryGray.400', 'whiteAlpha.200')(props)
        }
      }),
      action: (props: any) => ({
        fontWeight: '500',
        borderRadius: '50px',
        bg: mode('secondaryGray.300', 'brand.400')(props),
        color: mode('brand.500', 'white')(props),
        _focus: {
          bg: mode('secondaryGray.300', 'brand.400')(props)
        },
        _active: { bg: mode('secondaryGray.300', 'brand.400')(props) },
        _hover: {
          bg: mode('secondaryGray.200', 'brand.400')(props)
        }
      }),
      link: (props: any) => ({
        padding: 0,
        height: 'auto',
        lineHeight: 'normal',
        color: mode('brand.500', 'white')(props),
        _hover: {
          textDecoration: 'underline',
          color: mode('brand.600', 'brand.300')(props)
        }
      }),
      ghost: (props: any) => ({
        bg: 'transparent',
        color: mode('secondaryGray.900', 'white')(props),
        _hover: {
          bg: mode('secondaryGray.100', 'whiteAlpha.100')(props)
        },
        _active: {
          bg: mode('secondaryGray.200', 'whiteAlpha.200')(props)
        }
      })
    },
    defaultProps: {
      variant: 'brand',
      size: 'md'
    },
  },
  Link: {
    baseStyle: {
      transition: 'all 0.2s',
      _hover: {
        textDecoration: 'none',
      },
    },
  },
  Heading: {
    baseStyle: {
      fontWeight: '700',
      letterSpacing: '-0.5px',
    },
  },
  Input: {
    baseStyle: {
      field: {
        fontWeight: 400,
        borderRadius: '8px',
        _focus: {
          borderColor: 'brand.500',
          boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
        },
        transition: 'all 0.2s',
      },
    },
    variants: {
      main: (props: any) => ({
        field: {
          bg: mode('transparent', 'navy.800')(props),
          border: '1px solid',
          color: mode('secondaryGray.900', 'white')(props),
          borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          borderRadius: '16px',
          fontSize: { base: 'xs', md: 'sm' },
          p: { base: '10px', md: '12px' },
          h: { base: '40px', md: 'auto' },
          _placeholder: { color: 'secondaryGray.400' }
        }
      }),
      search: () => ({
        field: {
          border: 'none',
          py: { base: '8px', md: '11px' },
          borderRadius: 'inherit',
          _placeholder: { color: 'secondaryGray.600' }
        }
      })
    },
    defaultProps: {
      variant: 'main'
    }
  },
  Table: {
    baseStyle: {
      thead: {
        th: {
          fontWeight: 'bold',
          textTransform: 'none',
          letterSpacing: '0',
          color: 'secondaryGray.900',
          borderBottom: '1px solid',
          borderColor: 'secondaryGray.100',
          fontSize: { base: 'xs', md: 'sm' },
          py: { base: '12px', md: '16px' },
          px: { base: '8px', md: '16px' }
        }
      },
      tbody: {
        tr: {
          _hover: {
            bg: 'secondaryGray.100'
          }
        },
        td: {
          borderBottom: '1px solid',
          borderColor: 'secondaryGray.100',
          py: { base: '12px', md: '16px' },
          px: { base: '8px', md: '16px' },
          fontSize: { base: 'xs', md: 'sm' }
        }
      },
      container: {
        overflowX: 'auto',
        display: 'block',
        width: '100%',
        borderRadius: '16px'
      }
    },
    variants: {
      simple: {
        th: {
          borderBottom: '1px solid',
          borderColor: 'secondaryGray.100',
          padding: { base: '8px', md: '16px' },
          textTransform: 'none',
          letterSpacing: '0',
          fontWeight: 'bold'
        },
        td: {
          padding: { base: '8px', md: '16px' }
        }
      },
      striped: {
        th: {
          borderBottom: '1px solid',
          borderColor: 'secondaryGray.100',
          padding: { base: '8px', md: '16px' },
          textTransform: 'none',
          letterSpacing: '0',
          fontWeight: 'bold'
        },
        td: {
          padding: { base: '8px', md: '16px' }
        },
        tbody: {
          tr: {
            '&:nth-of-type(odd)': {
              bg: 'secondaryGray.100'
            }
          }
        }
      }
    },
    defaultProps: {
      variant: 'simple'
    }
  },
  Modal: {
    baseStyle: {
      dialog: {
        borderRadius: { base: '16px', md: '20px' },
        bg: 'white',
        mx: { base: '4', md: 'auto' },
        my: { base: '60px', md: 'auto' },
        maxH: { base: 'calc(100% - 120px)', md: 'calc(100% - 80px)' }
      },
      header: {
        padding: { base: '16px', md: '20px' },
        borderBottom: '1px solid',
        borderColor: 'secondaryGray.100',
        fontSize: { base: 'md', md: 'lg' },
        fontWeight: 'bold'
      },
      body: {
        padding: { base: '16px', md: '20px' },
        overflow: 'auto'
      },
      footer: {
        padding: { base: '16px', md: '20px' },
        borderTop: '1px solid',
        borderColor: 'secondaryGray.100'
      },
      closeButton: {
        top: { base: '10px', md: '12px' },
        right: { base: '10px', md: '12px' },
        _hover: {
          bg: 'secondaryGray.100'
        }
      },
      overlay: {
        bg: 'blackAlpha.700',
        backdropFilter: 'blur(5px)'
      }
    }
  }
};

// Tema özellikleri
const fontSizes = {
  xs: '0.75rem',
  sm: '0.875rem',
  md: '1rem',
  lg: '1.125rem',
  xl: '1.25rem',
  '2xl': '1.5rem',
  '3xl': '1.875rem',
  '4xl': '2.25rem',
  '5xl': '3rem',
};

const breakpoints = {
  sm: '320px',
  '2sm': '380px',
  md: '768px',
  lg: '960px',
  xl: '1200px',
  '2xl': '1600px',
  '3xl': '1920px',
};

const fonts = {
  heading: 'DM Sans, sans-serif',
  body: 'DM Sans, sans-serif',
};

const shadows = {
  xs: '0 0 0 1px rgba(0, 0, 0, 0.05)',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  outline: '0 0 0 3px rgba(66, 153, 225, 0.6)',
  inner: 'inset 0 2px 4px 0 rgba(0,0,0,0.06)',
  none: 'none',
  'dark-lg': 'rgba(0, 0, 0, 0.1) 0px 0px 0px 1px, rgba(0, 0, 0, 0.2) 0px 5px 10px, rgba(0, 0, 0, 0.4) 0px 15px 40px'
};

// Tema yapılandırması
export default extendTheme({
  colors: globalStyles.colors,
  styles: globalStyles.styles,
  components,
  fontSizes,
  breakpoints,
  fonts,
  shadows,
  CardComponent,
  config: {
    initialColorMode: 'light',
    useSystemColorMode: false,
  },
});

export interface CustomCardProps extends HTMLChakraProps<'div'>, ThemingProps {}

// Responsive stil fonksiyonları
export const getResponsiveValue = (...args: any[]) => ({ base: args[0], md: args[1], lg: args[2], xl: args[3] }); 