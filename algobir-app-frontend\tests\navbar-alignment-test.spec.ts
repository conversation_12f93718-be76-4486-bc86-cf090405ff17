import { test, expect } from '@playwright/test';
import { loginUser } from './utils/test-helpers';

test.describe('Navbar-Sidebar Alignment', () => {
  test.beforeEach(async ({ page }) => {
    await loginUser(page);
    await page.waitForLoadState('networkidle');
  });

  test('should have perfect navbar-sidebar alignment in collapsed state', async ({ page }) => {
    // Ensure we're on desktop viewport
    await page.setViewportSize({ width: 1440, height: 900 });

    // Wait for elements to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
    await expect(page.locator('[data-testid="navbar"]')).toBeVisible();

    // Get sidebar and navbar elements
    const sidebar = page.locator('[data-testid="sidebar"]');
    const navbar = page.locator('[data-testid="navbar"]');

    // Get bounding boxes
    const sidebarBox = await sidebar.boundingBox();
    const navbarBox = await navbar.boundingBox();

    expect(sidebarBox).toBeTruthy();
    expect(navbarBox).toBeTruthy();

    if (sidebarBox && navbarBox) {
      // Navbar should start exactly where sidebar ends
      const expectedNavbarLeft = sidebarBox.width;
      expect(navbarBox.x).toBeCloseTo(expectedNavbarLeft, 2);

      // Navbar width should be approximately viewport width minus sidebar width
      // Allow tolerance for padding/border calculations (8px difference is acceptable)
      const expectedNavbarWidth = 1440 - sidebarBox.width;
      expect(Math.abs(navbarBox.width - expectedNavbarWidth)).toBeLessThan(10); // 10px tolerance

      // No gap between sidebar and navbar
      const gap = navbarBox.x - (sidebarBox.x + sidebarBox.width);
      expect(Math.abs(gap)).toBeLessThan(2); // Allow 2px tolerance

      console.log(`COLLAPSED STATE - Sidebar width: ${sidebarBox.width}px`);
      console.log(`COLLAPSED STATE - Navbar left: ${navbarBox.x}px`);
      console.log(`COLLAPSED STATE - Navbar width: ${navbarBox.width}px`);
      console.log(`COLLAPSED STATE - Gap: ${gap}px`);
    }
  });

  test('should have perfect navbar-sidebar alignment in expanded state', async ({ page }) => {
    // Ensure we're on desktop viewport
    await page.setViewportSize({ width: 1440, height: 900 });

    // Wait for elements to be visible
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
    await expect(page.locator('[data-testid="navbar"]')).toBeVisible();

    // Hover on left edge to expand sidebar
    await page.mouse.move(30, 400);
    await page.waitForTimeout(200); // Wait for hover expansion

    // Get sidebar and navbar elements after expansion
    const sidebar = page.locator('[data-testid="sidebar"]');
    const navbar = page.locator('[data-testid="navbar"]');

    // Get bounding boxes
    const sidebarBox = await sidebar.boundingBox();
    const navbarBox = await navbar.boundingBox();

    expect(sidebarBox).toBeTruthy();
    expect(navbarBox).toBeTruthy();

    if (sidebarBox && navbarBox) {
      // Navbar should start exactly where sidebar ends (285px for expanded)
      const expectedNavbarLeft = sidebarBox.width;
      expect(navbarBox.x).toBeCloseTo(expectedNavbarLeft, 2);

      // Navbar width should be approximately viewport width minus sidebar width
      const expectedNavbarWidth = 1440 - sidebarBox.width;
      expect(Math.abs(navbarBox.width - expectedNavbarWidth)).toBeLessThan(10); // 10px tolerance

      // No gap between sidebar and navbar
      const gap = navbarBox.x - (sidebarBox.x + sidebarBox.width);
      expect(Math.abs(gap)).toBeLessThan(2); // Allow 2px tolerance

      console.log(`EXPANDED STATE - Sidebar width: ${sidebarBox.width}px`);
      console.log(`EXPANDED STATE - Navbar left: ${navbarBox.x}px`);
      console.log(`EXPANDED STATE - Navbar width: ${navbarBox.width}px`);
      console.log(`EXPANDED STATE - Gap: ${gap}px`);
    }
  });
});
