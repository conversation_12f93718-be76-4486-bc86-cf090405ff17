const LOG_PREFIX_ERROR = "[CLIENT_ERROR]";
const LOG_PREFIX_INFO = "[CLIENT_INFO]";
const LOG_PREFIX_WARN = "[CLIENT_WARN]";
const LOG_PREFIX_DEBUG = "[CLIENT_DEBUG]";

interface LoggerContext {
  [key: string]: any;
}

class Logger {
  private stringifyContext(context?: LoggerContext): string {
    return context ? `| Context: ${JSON.stringify(context)}` : "";
  }

  public logError(error: any, context?: LoggerContext): void {
    if (error instanceof Error) {
      console.error(
        `${LOG_PREFIX_ERROR} ${error.message} ${this.stringifyContext(context)}`,
        "\nStack:",
        error.stack
      );
    } else {
      console.error(
        `${LOG_PREFIX_ERROR} ${String(error)} ${this.stringifyContext(context)}`
      );
    }
  }

  public logInfo(message: string, context?: LoggerContext): void {
    console.info(`${LOG_PREFIX_INFO} ${message} ${this.stringifyContext(context)}`);
  }

  public logWarn(message: string, context?: LoggerContext): void {
    console.warn(`${LOG_PREFIX_WARN} ${message} ${this.stringifyContext(context)}`);
  }

  public logDebug(message: string, context?: LoggerContext): void {
    const isDevelopment = import.meta.env.MODE === "development";
    const viteDebugLogs = import.meta.env.VITE_DEBUG_LOGS;
    
    if (isDevelopment || viteDebugLogs === "true") {
      console.debug(`${LOG_PREFIX_DEBUG} ${message} ${this.stringifyContext(context)}`);
    }
  }
}

const logger = new Logger();

export default logger; 