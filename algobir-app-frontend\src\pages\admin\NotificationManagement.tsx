import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Heading,
  Text,
  VStack,
  HStack,
  Flex,
  Card,
  CardBody,
  CardHeader,
  Button,

  useToast,
  SimpleGrid,
  Stat,
  StatNumber,
  StatLabel,

  Badge,
  Icon,
  Spinner,
  useColorModeValue,
  Avatar,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer
} from '@chakra-ui/react';
import { 
  FiBell, 
  FiUsers, 
  FiSend,
  FiInfo,
  FiAlertTriangle,
  FiAlertCircle,
  FiCheck,
  FiTrendingUp,
  FiActivity,

  FiRefreshCw
} from 'react-icons/fi';
import { supabase } from '../../supabaseClient';
import { Link } from 'react-router-dom';

interface NotificationStats {
  totalSent: number;
  unreadCount: number;
  soloUsers: number;
  broSellers: number;
  broSubscribers: number;
}



interface RecentNotification {
  id: string;
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'error' | 'success';
  target_audience: string;
  recipient_count: number;
  created_at: string;
  created_by_username?: string;
  notification_type?: 'bulk' | 'individual';
}

const NotificationManagement: React.FC = () => {
  const toast = useToast();
  
  const [stats, setStats] = useState<NotificationStats>({
    totalSent: 0,
    unreadCount: 0,
    soloUsers: 0,
    broSellers: 0,
    broSubscribers: 0
  });
  const [recentNotifications, setRecentNotifications] = useState<RecentNotification[]>([]);
  const [statsLoading, setStatsLoading] = useState(true);
  const [notificationsLoading, setNotificationsLoading] = useState(true);

  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');

  // İstatistikleri al
  const fetchStats = async () => {
    try {
      setStatsLoading(true);
      
      // Toplam bildirim sayısı
      const { count: totalNotifications } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true });

      // Okunmamış bildirim sayısı
      const { count: unreadNotifications } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('is_read', false);

      // Kullanıcı tiplerini hesapla
      const { data: users } = await supabase
        .from('user_settings')
        .select('id, is_superuser');

      const { data: robots } = await supabase
        .from('robots')
        .select('seller_id')
        .is('deleted_at', null);

      const { data: subscriptions } = await supabase
        .from('subscriptions')
        .select('user_id')
        .eq('is_active', true)
        .eq('is_deleted', false);

      let soloUsers = 0;
      let broSellers = 0;
      const subscriberIds = new Set();

      if (users) {
        soloUsers = users.filter(u => !u.is_superuser).length;
      }

      if (robots) {
        const sellerIds = new Set(robots.map(r => r.seller_id));
        broSellers = sellerIds.size;
      }

      if (subscriptions) {
        subscriptions.forEach(sub => subscriberIds.add(sub.user_id));
      }

      setStats({
        totalSent: totalNotifications || 0,
        unreadCount: unreadNotifications || 0,
        soloUsers,
        broSellers,
        broSubscribers: subscriberIds.size
      });
    } catch (error) {
      console.error('Stats yüklenirken hata:', error);
      toast({
        title: 'Hata',
        description: 'İstatistikler yüklenirken bir hata oluştu',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setStatsLoading(false);
    }
  };

  // Son bildirimleri al - Comprehensive view of all notifications
  const fetchRecentNotifications = async () => {
    try {
      setNotificationsLoading(true);

      console.log('🔍 Tüm sistem bildirimlerini çekiliyor...');

      let allNotifications: RecentNotification[] = [];

      // 1. Admin announcements tablosundan duyuruları çek
      console.log('📋 Admin announcements tablosundan bildirimler çekiliyor...');
      try {
        const { data: adminData, error: adminError } = await supabase
          .from('admin_announcements')
          .select(`
            id,
            title,
            message,
            severity,
            target_audience,
            created_at,
            admin_user_id
          `)
          .order('created_at', { ascending: false })
          .limit(50);

        if (!adminError && adminData && adminData.length > 0) {
          console.log(`✅ ${adminData.length} adet admin duyurusu bulundu`);
          const adminNotifications = adminData.map(notification => ({
            id: notification.id,
            title: notification.title,
            message: notification.message,
            severity: notification.severity as 'info' | 'warning' | 'error' | 'success',
            target_audience: notification.target_audience,
            recipient_count: 0, // Admin announcements için recipient count hesaplanabilir
            created_at: notification.created_at,
            created_by_username: 'Admin',
            notification_type: 'bulk' as const
          }));
          allNotifications.push(...adminNotifications);
        } else if (adminError) {
          console.warn('⚠️ Admin announcements hata:', adminError.message);
        }
      } catch (adminTableError) {
        console.error('❌ Admin announcements tablo erişim hatası:', adminTableError);
      }

      // 2. Notifications tablosundan tüm bildirimleri çek (trade, admin, system notifications)
      console.log('📋 Notifications tablosundan tüm bildirimleri çekiliyor...');
      try {
        const { data: allNotificationData, error: allNotificationError } = await supabase
          .from('notifications')
          .select(`
            id,
            title,
            message,
            severity,
            type,
            created_at,
            user_id,
            metadata
          `)
          .eq('is_deleted', false)
          .order('created_at', { ascending: false })
          .limit(100);

        if (!allNotificationError && allNotificationData && allNotificationData.length > 0) {
          console.log(`✅ ${allNotificationData.length} adet sistem bildirimi bulundu`);

          const systemNotifications = allNotificationData.map((notification: any) => {
            // Determine notification category and target audience based on type
            let targetAudience = 'Bilinmeyen';
            let notificationType: 'bulk' | 'individual' = 'individual';
            let createdBy = 'Sistem';

            switch (notification.type) {
              case 'trade_opened':
                targetAudience = 'Al İşlemi';
                createdBy = notification.metadata?.source === 'solo-robot' ? 'Solo-Robot' : 'Bro-Robot';
                break;
              case 'trade_closed':
                targetAudience = 'Sat İşlemi';
                createdBy = notification.metadata?.source === 'solo-robot' ? 'Solo-Robot' : 'Bro-Robot';
                break;
              case 'admin_announcement':
                targetAudience = 'Admin Duyurusu';
                notificationType = 'bulk';
                createdBy = 'Admin';
                break;
              case 'robot_status':
                targetAudience = 'Robot Durumu';
                createdBy = 'Robot Sistemi';
                break;
              case 'system_alert':
                targetAudience = 'Sistem Uyarısı';
                createdBy = 'Sistem';
                break;
              case 'subscription_update':
                targetAudience = 'Abonelik Güncellemesi';
                createdBy = 'Abonelik Sistemi';
                break;
              default:
                targetAudience = notification.type || 'Genel';
                break;
            }

            return {
              id: notification.id,
              title: notification.title,
              message: notification.message,
              severity: notification.severity as 'info' | 'warning' | 'error' | 'success',
              target_audience: targetAudience,
              recipient_count: 1,
              created_at: notification.created_at,
              created_by_username: createdBy,
              notification_type: notificationType
            };
          });

          allNotifications.push(...systemNotifications);
        } else if (allNotificationError) {
          console.error('❌ Notifications sorgu hatası:', allNotificationError.message);
        }
      } catch (notificationTableError) {
        console.error('❌ Notifications tablo erişim hatası:', notificationTableError);
      }

      // Tarihe göre sırala ve sonuçları göster
      if (allNotifications.length > 0) {
        allNotifications.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        console.log(`📊 Toplam yüklenen bildirim sayısı: ${allNotifications.length}`);
        setRecentNotifications(allNotifications);

        // Bildirim türlerini say
        const typeCount = allNotifications.reduce((acc, notif) => {
          acc[notif.target_audience] = (acc[notif.target_audience] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);

        console.log('📊 Bildirim türü dağılımı:', typeCount);

        toast({
          title: '✅ Tüm Bildirimler Yüklendi',
          description: `${allNotifications.length} adet bildirim başarıyla yüklendi`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else {
        console.warn('⚠️ Hiçbir kaynaktan bildirim verisi alınamadı');
        setRecentNotifications([]);
        toast({
          title: '📭 Bildirim Bulunamadı',
          description: 'Sistemde henüz bildirim bulunmamaktadır',
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
      }

    } catch (error) {
      console.error('💥 Son bildirimler yüklenirken beklenmeyen hata:', error);
      setRecentNotifications([]);
      toast({
        title: '❌ Beklenmeyen Hata',
        description: 'Bildirimler yüklenirken sistem hatası oluştu. Lütfen sayfayı yenileyin.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setNotificationsLoading(false);
    }
  };

  const refreshData = async () => {
    await Promise.all([fetchStats(), fetchRecentNotifications()]);
  };

  useEffect(() => {
    fetchStats();
    fetchRecentNotifications();
  }, []);



  // Şiddet seviyesine göre renk
  const getSeverityColor = (sev: string) => {
    switch (sev) {
      case 'success': return 'green';
      case 'warning': return 'orange';
      case 'error': return 'red';
      default: return 'blue';
    }
  };

  // Şiddet seviyesine göre ikon
  const getSeverityIcon = (sev: string) => {
    switch (sev) {
      case 'success': return FiCheck;
      case 'warning': return FiAlertTriangle;
      case 'error': return FiAlertCircle;
      default: return FiInfo;
    }
  };

  const getAudienceLabel = (audience: string) => {
    if (audience.startsWith('Bireysel:')) {
      return audience; // Bireysel bildirimler için doğrudan kullanıcı adını göster
    }
    
    switch (audience) {
      case 'all': return 'Tüm Kullanıcılar';
      case 'solo_users': return 'Solo Kullanıcılar';
      case 'bro_sellers': return 'Bro Satıcılar';
      case 'bro_subscribers': return 'Bro Aboneler';
      default: return audience;
    }
  };

  return (
    <Box p={6}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Flex justify="space-between" align="center">
          <Box>
            <Heading as="h1" size="xl" mb={2}>
              Bildirim Yönetimi
            </Heading>
            <Text color={secondaryTextColor}>
              Kullanıcılara duyuru ve bildirim gönderin
            </Text>
          </Box>
          <HStack spacing={3}>
            <Button
              leftIcon={<Icon as={FiRefreshCw} />}
              variant="outline"
              onClick={refreshData}
              isLoading={statsLoading || notificationsLoading}
            >
              Yenile
            </Button>
            <Button
              as={Link}
              to="/admin/notifications/create"
              colorScheme="blue"
              size="lg"
              leftIcon={<Icon as={FiSend} />}
              bgGradient="linear(to-r, blue.400, blue.600)"
              _hover={{
                bgGradient: "linear(to-r, blue.500, blue.700)",
                transform: "translateY(-2px)",
                boxShadow: "lg"
              }}
              borderRadius="xl"
              px={8}
            >
              Duyuru Oluştur
            </Button>
          </HStack>
        </Flex>

        {/* İstatistikler */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
          <Card bg={cardBg} borderColor={borderColor}>
            <CardBody>
              <Stat>
                <HStack>
                  <Icon as={FiBell} color="blue.500" boxSize={5} />
                  <StatLabel>Toplam Gönderilen</StatLabel>
                </HStack>
                {statsLoading ? (
                  <Spinner size="sm" />
                ) : (
                  <StatNumber color="blue.500">
                    {stats.totalSent}
                  </StatNumber>
                )}
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg} borderColor={borderColor}>
            <CardBody>
              <Stat>
                <HStack>
                  <Icon as={FiTrendingUp} color="green.500" boxSize={5} />
                  <StatLabel>Solo Kullanıcılar</StatLabel>
                </HStack>
                {statsLoading ? (
                  <Spinner size="sm" />
                ) : (
                  <StatNumber color="green.500">
                    {stats.soloUsers}
                  </StatNumber>
                )}
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg} borderColor={borderColor}>
            <CardBody>
              <Stat>
                <HStack>
                  <Icon as={FiActivity} color="purple.500" boxSize={5} />
                  <StatLabel>Bro Satıcılar</StatLabel>
                </HStack>
                {statsLoading ? (
                  <Spinner size="sm" />
                ) : (
                  <StatNumber color="purple.500">
                    {stats.broSellers}
                  </StatNumber>
                )}
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg} borderColor={borderColor}>
            <CardBody>
              <Stat>
                <HStack>
                  <Icon as={FiUsers} color="orange.500" boxSize={5} />
                  <StatLabel>Bro Aboneler</StatLabel>
                </HStack>
                {statsLoading ? (
                  <Spinner size="sm" />
                ) : (
                  <StatNumber color="orange.500">
                    {stats.broSubscribers}
                  </StatNumber>
                )}
              </Stat>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Hızlı Gönderim Butonları */}
        <Card bg={cardBg} borderColor={borderColor}>
          <CardHeader>
            <Heading size="md">Hızlı Duyuru Gönder</Heading>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
              <Button
                as={Link}
                to="/admin/notifications/create?target=all"
                leftIcon={<Icon as={FiUsers} />}
                colorScheme="blue"
                variant="outline"
                size="lg"
              >
                Tüm Kullanıcılara
              </Button>
              
              <Button
                as={Link}
                to="/admin/notifications/create?target=solo_users"
                leftIcon={<Icon as={FiTrendingUp} />}
                colorScheme="green"
                variant="outline"
                size="lg"
              >
                Solo Kullanıcılara
              </Button>
              
              <Button
                as={Link}
                to="/admin/notifications/create?target=bro_sellers"
                leftIcon={<Icon as={FiActivity} />}
                colorScheme="purple"
                variant="outline"
                size="lg"
              >
                Bro Satıcılara
              </Button>
              
              <Button
                as={Link}
                to="/admin/notifications/create?target=bro_subscribers"
                leftIcon={<Icon as={FiUsers} />}
                colorScheme="orange"
                variant="outline"
                size="lg"
              >
                Bro Abonelere
              </Button>
            </SimpleGrid>
          </CardBody>
        </Card>

        {/* Tüm Sistem Bildirimleri */}
        <Card bg={cardBg} borderColor={borderColor}>
          <CardHeader>
            <Flex justify="space-between" align="center">
              <Heading size="md">Tüm Sistem Bildirimleri</Heading>
              <Badge colorScheme="blue" variant="subtle">
                Admin, Trade, Robot ve Sistem Bildirimleri
              </Badge>
            </Flex>
          </CardHeader>
          <CardBody>
            {notificationsLoading ? (
              <Flex justify="center" p={8}>
                <VStack>
                  <Spinner size="lg" />
                  <Text>Bildirimler yükleniyor...</Text>
                </VStack>
              </Flex>
            ) : recentNotifications.length === 0 ? (
              <Flex justify="center" p={8}>
                <VStack spacing={3}>
                  <Icon as={FiBell} boxSize={12} color="gray.400" />
                  <Text color="gray.500" textAlign="center">
                    Henüz bildirim gönderilmemiş
                  </Text>
                  <Button
                    as={Link}
                    to="/admin/notifications/create"
                    colorScheme="blue"
                    variant="outline"
                    size="sm"
                  >
                    İlk Bildirimini Gönder
                  </Button>
                </VStack>
              </Flex>
            ) : (
              <TableContainer>
                <Table variant="simple" size="sm">
                  <Thead>
                    <Tr>
                      <Th>Bildirim</Th>
                      <Th>Hedef Kitle</Th>
                      <Th>Alıcı Sayısı</Th>
                      <Th>Tarih</Th>
                      <Th>Gönderen</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {recentNotifications.map((notification) => (
                      <Tr key={notification.id}>
                        <Td>
                          <VStack align="start" spacing={1}>
                            <HStack>
                              <Icon 
                                as={getSeverityIcon(notification.severity)} 
                                color={`${getSeverityColor(notification.severity)}.500`}
                                boxSize={4}
                              />
                              <Text fontWeight="medium" fontSize="sm">
                                {notification.title}
                              </Text>
                            </HStack>
                            <Text fontSize="xs" color="gray.500" noOfLines={2}>
                              {notification.message}
                            </Text>
                          </VStack>
                        </Td>
                        <Td>
                          <HStack spacing={2}>
                            <Badge 
                              colorScheme={notification.notification_type === 'individual' ? 'green' : getSeverityColor(notification.severity)}
                              variant="subtle"
                            >
                              {getAudienceLabel(notification.target_audience)}
                            </Badge>
                            {notification.notification_type === 'individual' && (
                              <Badge colorScheme="green" variant="solid" size="sm">
                                Bireysel
                              </Badge>
                            )}
                          </HStack>
                        </Td>
                        <Td>
                          <Text fontWeight="medium">
                            {notification.recipient_count}
                          </Text>
                        </Td>
                        <Td>
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm">
                              {new Date(notification.created_at).toLocaleDateString('tr-TR')}
                            </Text>
                            <Text fontSize="xs" color="gray.500">
                              {new Date(notification.created_at).toLocaleTimeString('tr-TR')}
                            </Text>
                          </VStack>
                        </Td>
                        <Td>
                          <HStack>
                            <Avatar size="xs" name={notification.created_by_username} />
                            <Text fontSize="sm">
                              {notification.created_by_username}
                            </Text>
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </TableContainer>
            )}
          </CardBody>
        </Card>
      </VStack>
    </Box>
  );
};

export default NotificationManagement;
