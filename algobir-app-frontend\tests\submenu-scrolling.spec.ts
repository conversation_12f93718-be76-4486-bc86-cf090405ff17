import { test, expect } from '@playwright/test';

test.describe('Submenu Scrolling Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display scrollable submenu when many items are present', async ({ page }) => {
    // Look for a sidebar menu item that has a submenu
    // This test assumes there's a menu item with submenu functionality
    const sidebarItem = page.locator('[data-testid="sidebar"] [role="button"]').first();
    
    if (await sidebarItem.isVisible()) {
      // Hover over the sidebar item to trigger submenu
      await sidebarItem.hover();
      
      // Wait for submenu to appear
      await page.waitForSelector('[role="dialog"][aria-modal="true"]', { timeout: 5000 });
      
      const submenu = page.locator('[role="dialog"][aria-modal="true"]');
      await expect(submenu).toBeVisible();
      
      // Check if scrollable container exists
      const scrollableContainer = submenu.locator('[data-testid="scrollbars"]');
      if (await scrollableContainer.isVisible()) {
        await expect(scrollableContainer).toBeVisible();
        
        // Check if scroll functionality works
        const menuItems = submenu.locator('[role="menuitem"]');
        const itemCount = await menuItems.count();
        
        if (itemCount > 5) {
          // Test scrolling if there are enough items
          await scrollableContainer.evaluate((element) => {
            element.scrollTop = 100;
          });
          
          // Verify scroll position changed
          const scrollTop = await scrollableContainer.evaluate((element) => element.scrollTop);
          expect(scrollTop).toBeGreaterThan(0);
        }
      }
    }
  });

  test('should maintain header position while scrolling submenu content', async ({ page }) => {
    // Look for submenu trigger
    const sidebarItem = page.locator('[data-testid="sidebar"] [role="button"]').first();
    
    if (await sidebarItem.isVisible()) {
      await sidebarItem.hover();
      
      // Wait for submenu
      await page.waitForSelector('[role="dialog"][aria-modal="true"]', { timeout: 5000 });
      
      const submenu = page.locator('[role="dialog"][aria-modal="true"]');
      
      // Check that header is outside scrollable area
      const header = submenu.locator('h1, h2, h3').first();
      const scrollableArea = submenu.locator('[data-testid="scrollbars"]');
      
      if (await header.isVisible() && await scrollableArea.isVisible()) {
        // Header should not be inside scrollable container
        const headerInScrollable = await scrollableArea.locator('h1, h2, h3').count();
        expect(headerInScrollable).toBe(0);
      }
    }
  });

  test('should handle keyboard navigation with scrolling', async ({ page }) => {
    const sidebarItem = page.locator('[data-testid="sidebar"] [role="button"]').first();
    
    if (await sidebarItem.isVisible()) {
      await sidebarItem.hover();
      
      await page.waitForSelector('[role="dialog"][aria-modal="true"]', { timeout: 5000 });
      
      const submenu = page.locator('[role="dialog"][aria-modal="true"]');
      
      // Test Escape key closes submenu
      await page.keyboard.press('Escape');
      await expect(submenu).not.toBeVisible();
      
      // Reopen submenu
      await sidebarItem.hover();
      await page.waitForSelector('[role="dialog"][aria-modal="true"]', { timeout: 5000 });
      
      // Test arrow key navigation
      const menuItems = submenu.locator('[role="menuitem"]');
      const itemCount = await menuItems.count();
      
      if (itemCount > 0) {
        // Focus first item
        await menuItems.first().focus();
        
        // Navigate with arrow keys
        await page.keyboard.press('ArrowDown');
        await page.keyboard.press('ArrowUp');
        
        // Should not throw errors
        expect(true).toBe(true);
      }
    }
  });

  test('should display custom scrollbar styling', async ({ page }) => {
    const sidebarItem = page.locator('[data-testid="sidebar"] [role="button"]').first();
    
    if (await sidebarItem.isVisible()) {
      await sidebarItem.hover();
      
      await page.waitForSelector('[role="dialog"][aria-modal="true"]', { timeout: 5000 });
      
      const submenu = page.locator('[role="dialog"][aria-modal="true"]');
      const scrollableArea = submenu.locator('[data-testid="scrollbars"]');
      
      if (await scrollableArea.isVisible()) {
        // Check if custom scrollbar elements are present
        const scrollTrack = submenu.locator('[data-testid="scroll-track"]');
        const scrollThumb = submenu.locator('[data-testid="scroll-thumb"]');
        
        if (await scrollTrack.isVisible()) {
          await expect(scrollTrack).toBeVisible();
        }
        
        if (await scrollThumb.isVisible()) {
          await expect(scrollThumb).toBeVisible();
        }
      }
    }
  });

  test('should handle mouse wheel scrolling', async ({ page }) => {
    const sidebarItem = page.locator('[data-testid="sidebar"] [role="button"]').first();
    
    if (await sidebarItem.isVisible()) {
      await sidebarItem.hover();
      
      await page.waitForSelector('[role="dialog"][aria-modal="true"]', { timeout: 5000 });
      
      const submenu = page.locator('[role="dialog"][aria-modal="true"]');
      const scrollableArea = submenu.locator('[data-testid="scrollbars"]');
      
      if (await scrollableArea.isVisible()) {
        // Get initial scroll position
        const initialScrollTop = await scrollableArea.evaluate((element) => element.scrollTop);
        
        // Simulate mouse wheel scroll
        await scrollableArea.hover();
        await page.mouse.wheel(0, 100);
        
        // Wait a bit for scroll to complete
        await page.waitForTimeout(100);
        
        // Check if scroll position changed
        const newScrollTop = await scrollableArea.evaluate((element) => element.scrollTop);
        
        // If there's scrollable content, position should change
        const hasScrollableContent = await scrollableArea.evaluate((element) => 
          element.scrollHeight > element.clientHeight
        );
        
        if (hasScrollableContent) {
          expect(newScrollTop).toBeGreaterThanOrEqual(initialScrollTop);
        }
      }
    }
  });

  test('should preserve accessibility features with scrolling', async ({ page }) => {
    const sidebarItem = page.locator('[data-testid="sidebar"] [role="button"]').first();
    
    if (await sidebarItem.isVisible()) {
      await sidebarItem.hover();
      
      await page.waitForSelector('[role="dialog"][aria-modal="true"]', { timeout: 5000 });
      
      const submenu = page.locator('[role="dialog"][aria-modal="true"]');
      
      // Check ARIA attributes are preserved
      await expect(submenu).toHaveAttribute('aria-modal', 'true');
      await expect(submenu).toHaveAttribute('role', 'dialog');
      
      // Check menu items have proper roles
      const menuItems = submenu.locator('[role="menuitem"]');
      const itemCount = await menuItems.count();
      
      if (itemCount > 0) {
        for (let i = 0; i < Math.min(itemCount, 3); i++) {
          const item = menuItems.nth(i);
          await expect(item).toHaveAttribute('role', 'menuitem');
          await expect(item).toHaveAttribute('tabIndex', '0');
        }
      }
    }
  });
});
