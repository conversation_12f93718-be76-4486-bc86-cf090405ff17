import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...');
  
  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto(config.projects[0].use.baseURL || 'http://localhost:3000');
    await page.waitForLoadState('networkidle', { timeout: 30000 });
    
    // Check if the application is running properly
    await page.waitForSelector('body', { timeout: 10000 });
    console.log('✅ Application is ready');
    
    // Setup test data if needed
    await setupTestData(page);
    
    // Setup authentication if needed
    await setupAuthentication(page);
    
    console.log('✅ Global setup completed successfully');
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

async function setupTestData(page: any) {
  console.log('📊 Setting up test data...');
  
  // Mock API responses for consistent testing
  await page.route('**/api/statistics/**', async (route: any) => {
    const url = route.request().url();
    
    if (url.includes('/roi-analysis')) {
      // Mock ROI analysis data
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          roiAnalysis: {
            totalInvestment: 100000,
            totalReturns: 15000,
            totalROI: 15,
            annualizedROI: 18.5,
            compoundGrowthRate: 17.2,
            riskAdjustedReturn: 12.3,
            volatility: 18.5,
            sharpeRatio: 1.2,
            maxDrawdown: 8.5,
            monthlyROI: [
              { month: '2024-01', roi: 12, investment: 25000, returns: 3000 },
              { month: '2024-02', roi: 18, investment: 25000, returns: 4500 },
              { month: '2024-03', roi: 15, investment: 25000, returns: 3750 },
              { month: '2024-04', roi: 14, investment: 25000, returns: 3750 }
            ],
            quarterlyROI: [
              { quarter: '2024-Q1', roi: 15, investment: 75000, returns: 11250 },
              { quarter: '2024-Q2', roi: 14, investment: 25000, returns: 3750 }
            ],
            yearlyROI: [
              { year: '2024', roi: 15, investment: 100000, returns: 15000 }
            ],
            roiByRobot: [
              { robotId: 'robot1', robotName: 'Alpha Robot', roi: 22, investment: 40000 },
              { robotId: 'robot2', robotName: 'Beta Robot', roi: 18, investment: 35000 },
              { robotId: 'robot3', robotName: 'Gamma Robot', roi: 8, investment: 25000 },
              { robotId: 'solo', robotName: 'Solo-Robot', roi: 12, investment: 15000 }
            ],
            roiBySymbol: [
              { symbol: 'AAPL', roi: 25, investment: 30000, trades: 15 },
              { symbol: 'GOOGL', roi: 18, investment: 25000, trades: 12 },
              { symbol: 'MSFT', roi: 15, investment: 20000, trades: 10 },
              { symbol: 'TSLA', roi: -5, investment: 15000, trades: 8 },
              { symbol: 'AMZN', roi: 12, investment: 10000, trades: 6 }
            ]
          },
          realtimeMetrics: {
            openPositions: 3,
            todaysPnl: 1250,
            currentROI: 15.2,
            lastTradeTime: new Date().toISOString()
          },
          liveUpdates: true,
          lastUpdateTime: new Date().toISOString()
        })
      });
    } else {
      // Continue with original request for other endpoints
      await route.continue();
    }
  });
  
  console.log('✅ Test data setup completed');
}

async function setupAuthentication(page: any) {
  console.log('🔐 Setting up authentication...');
  
  // Check if authentication is required
  const currentUrl = page.url();
  if (currentUrl.includes('/login') || currentUrl.includes('/auth')) {
    console.log('🔑 Authentication required, setting up test user...');
    
    // Mock authentication or login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword');
    await page.click('button[type="submit"]');
    
    // Wait for successful login
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    console.log('✅ Authentication completed');
  } else {
    console.log('ℹ️ No authentication required');
  }
}

export default globalSetup;
