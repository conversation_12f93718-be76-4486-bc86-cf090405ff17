/**
 * Monitoring & Alerting Configuration
 * Phase 3.3: Monitoring & Alerting Systems
 * Comprehensive monitoring setup for production systems
 */

export const MONITORING_CONFIG = {
  // Performance monitoring thresholds
  performance: {
    // Core Web Vitals thresholds
    lcp: {
      good: 2500,      // Large Contentful Paint < 2.5s
      needsImprovement: 4000,
      poor: Infinity
    },
    fid: {
      good: 100,       // First Input Delay < 100ms
      needsImprovement: 300,
      poor: Infinity
    },
    cls: {
      good: 0.1,       // Cumulative Layout Shift < 0.1
      needsImprovement: 0.25,
      poor: Infinity
    },
    fcp: {
      good: 1800,      // First Contentful Paint < 1.8s
      needsImprovement: 3000,
      poor: Infinity
    },
    ttfb: {
      good: 800,       // Time to First Byte < 800ms
      needsImprovement: 1800,
      poor: Infinity
    }
  },

  // Error monitoring configuration
  errorMonitoring: {
    // Error rate thresholds (percentage)
    errorRate: {
      warning: 1,      // 1% error rate triggers warning
      critical: 5      // 5% error rate triggers critical alert
    },
    
    // JavaScript error categories to track
    errorCategories: [
      'TypeError',
      'ReferenceError',
      'SyntaxError',
      'NetworkError',
      'ChunkLoadError',
      'AuthenticationError',
      'ValidationError'
    ],
    
    // Ignore patterns for known non-critical errors
    ignorePatterns: [
      /Script error/,
      /Non-Error promise rejection captured/,
      /ResizeObserver loop limit exceeded/,
      /Loading chunk \d+ failed/
    ]
  },

  // Business metrics monitoring
  businessMetrics: {
    // Trading performance metrics
    trading: {
      signalLatency: {
        target: 100,     // Target signal processing time < 100ms
        warning: 500,    // Warning threshold
        critical: 1000   // Critical threshold
      },
      
      orderTransmission: {
        target: 50,      // Target order transmission time < 50ms
        warning: 200,    // Warning threshold
        critical: 500    // Critical threshold
      },
      
      successRate: {
        target: 99.5,    // Target success rate > 99.5%
        warning: 98,     // Warning threshold
        critical: 95     // Critical threshold
      }
    },
    
    // User engagement metrics
    engagement: {
      sessionDuration: {
        target: 300,     // Target session duration > 5 minutes
        warning: 120     // Warning if < 2 minutes
      },
      
      bounceRate: {
        target: 30,      // Target bounce rate < 30%
        warning: 50,     // Warning threshold
        critical: 70     // Critical threshold
      }
    }
  },

  // Infrastructure monitoring
  infrastructure: {
    // Database performance
    database: {
      queryTime: {
        warning: 1000,   // Query time > 1s triggers warning
        critical: 5000   // Query time > 5s triggers critical alert
      },
      
      connectionPool: {
        warning: 80,     // Connection pool usage > 80%
        critical: 95     // Connection pool usage > 95%
      }
    },
    
    // API performance
    api: {
      responseTime: {
        warning: 2000,   // API response time > 2s
        critical: 5000   // API response time > 5s
      },
      
      availability: {
        target: 99.9,    // Target uptime > 99.9%
        warning: 99.5,   // Warning threshold
        critical: 99     // Critical threshold
      }
    }
  },

  // Alert configuration
  alerts: {
    // Alert channels
    channels: {
      email: {
        enabled: true,
        recipients: ['<EMAIL>', '<EMAIL>']
      },
      
      slack: {
        enabled: true,
        webhook: process.env.SLACK_WEBHOOK_URL,
        channel: '#alerts'
      },
      
      sms: {
        enabled: false,  // Enable for critical production alerts
        numbers: []
      }
    },
    
    // Alert rules
    rules: {
      // Performance alerts
      performance: {
        enabled: true,
        severity: 'warning',
        cooldown: 300    // 5 minutes cooldown between alerts
      },
      
      // Error alerts
      errors: {
        enabled: true,
        severity: 'critical',
        cooldown: 60     // 1 minute cooldown
      },
      
      // Business metric alerts
      business: {
        enabled: true,
        severity: 'warning',
        cooldown: 600    // 10 minutes cooldown
      },
      
      // Infrastructure alerts
      infrastructure: {
        enabled: true,
        severity: 'critical',
        cooldown: 180    // 3 minutes cooldown
      }
    }
  },

  // Data retention policies
  retention: {
    metrics: {
      realtime: '1h',      // Real-time data for 1 hour
      hourly: '7d',        // Hourly aggregates for 7 days
      daily: '30d',        // Daily aggregates for 30 days
      monthly: '1y'        // Monthly aggregates for 1 year
    },
    
    logs: {
      debug: '1d',         // Debug logs for 1 day
      info: '7d',          // Info logs for 7 days
      warning: '30d',      // Warning logs for 30 days
      error: '90d'         // Error logs for 90 days
    }
  },

  // Monitoring endpoints
  endpoints: {
    healthCheck: '/api/health',
    metrics: '/api/metrics',
    alerts: '/api/alerts',
    dashboard: '/monitoring/dashboard'
  }
};

// Environment-specific overrides
if (process.env.NODE_ENV === 'development') {
  MONITORING_CONFIG.alerts.channels.email.enabled = false;
  MONITORING_CONFIG.alerts.channels.slack.enabled = false;
  MONITORING_CONFIG.retention.logs.debug = '1h';
}

if (process.env.NODE_ENV === 'staging') {
  MONITORING_CONFIG.alerts.channels.email.recipients = ['<EMAIL>'];
  MONITORING_CONFIG.performance.lcp.good = 3000; // Relaxed thresholds for staging
}

export default MONITORING_CONFIG;
