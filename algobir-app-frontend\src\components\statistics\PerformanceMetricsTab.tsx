import React from 'react';
import { SimpleGrid, Stat, StatLabel, StatNumber } from '@chakra-ui/react';
import { StatsData } from '../../hooks/useAdvancedStatisticsData';
import Card from '../card/Card';

interface Props { stats: StatsData }

const PerformanceMetricsTab: React.FC<Props> = ({ stats }) => {
  const formatCurrency = (val: number) => `₺${val.toFixed(2)}`;
  
  const metrics = [
    { label: 'Toplam İşlem', value: stats.totalTrades },
    { label: '<PERSON>an <PERSON>', value: stats.winningTrades },
    { label: '<PERSON><PERSON>en İşlem', value: stats.losingTrades },
    { label: 'Ort. İşlem K/Z', value: formatCurrency(stats.avgTradePnl) },
    { label: 'Ort. Kazanan <PERSON>', value: formatCurrency(stats.avgWinningTrade) },
    { label: 'Ort. <PERSON><PERSON>', value: formatCurrency(stats.avgLosingTrade) },
  ];

  return (
    <SimpleGrid columns={{ base: 2, md: 3 }} spacing={6}>
      {metrics.map(metric => (
        <Card key={metric.label} p={5}>
          <Stat>
            <StatLabel>{metric.label}</StatLabel>
            <StatNumber>{metric.value}</StatNumber>
          </Stat>
        </Card>
      ))}
    </SimpleGrid>
  );
};

export default PerformanceMetricsTab; 