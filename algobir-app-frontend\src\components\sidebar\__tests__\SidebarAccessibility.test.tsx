import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { axe, toHaveNoViolations } from 'jest-axe';
import { vi } from 'vitest';
import { Icon } from '@chakra-ui/react';
import { FiHome, FiSettings } from 'react-icons/fi';
import Sidebar from '../Sidebar';
import SubmenuPanel from '../components/SubmenuPanel';
import SidebarContentCollapsed from '../components/ContentCollapsed';
import { SidebarProvider, SubmenuItem } from '../../../context/SidebarContext';
import { RouteType } from '../../../routes';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock routes for accessibility testing
const mockAccessibilityRoutes: RouteType[] = [
  {
    name: 'Dashboard',
    layout: '',
    path: '/',
    icon: <Icon as={FiHome} width="20px" height="20px" color="inherit" />,
  },
  {
    name: 'Settings',
    layout: '',
    path: '/settings',
    icon: <Icon as={FiSettings} width="20px" height="20px" color="inherit" />,
    hasSubmenu: true,
    children: [
      {
        id: 'profile',
        name: 'Profile Settings',
        path: '/settings/profile',
        description: 'Manage your profile information'
      },
      {
        id: 'account',
        name: 'Account Settings',
        path: '/settings/account',
        description: 'Manage your account preferences'
      }
    ]
  }
];

const mockSubmenuItems: SubmenuItem[] = [
  {
    id: 'profile',
    name: 'Profile Settings',
    path: '/settings/profile',
    icon: <Icon as={FiSettings} width="16px" height="16px" color="inherit" />,
    description: 'Manage your profile information'
  }
];

// Mock dependencies
vi.mock('../../../hooks/useAdminAuth', () => ({
  useAdminAuth: () => ({ isAdmin: true })
}));

vi.mock('react-custom-scrollbars-2', () => ({
  Scrollbars: ({ children, ...props }: any) => (
    <div data-testid="scrollbars" {...props}>
      {children}
    </div>
  )
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ChakraProvider>
      <BrowserRouter>
        <SidebarProvider>
          {component}
        </SidebarProvider>
      </BrowserRouter>
    </ChakraProvider>
  );
};

describe('Sidebar Accessibility Tests', () => {
  beforeEach(() => {
    // Mock desktop breakpoint
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query.includes('(min-width: 992px)'),
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });
  });

  test('should have no accessibility violations in expanded state', async () => {
    const { container } = renderWithProviders(
      <Sidebar routes={mockAccessibilityRoutes} />
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('should have proper ARIA attributes for navigation', () => {
    renderWithProviders(<Sidebar routes={mockAccessibilityRoutes} />);
    
    const navigation = screen.getByRole('navigation');
    expect(navigation).toHaveAttribute('aria-label', 'Ana navigasyon');
    expect(navigation).toHaveAttribute('aria-expanded', 'true');
  });

  test('should have proper ARIA attributes for menu items', () => {
    renderWithProviders(<Sidebar routes={mockAccessibilityRoutes} />);
    
    // Check regular menu items
    const dashboardLink = screen.getByRole('link', { name: /dashboard/i });
    expect(dashboardLink).toHaveAttribute('aria-label', 'Dashboard sayfasına git');
    
    // Check submenu items
    const settingsButton = screen.getByRole('button', { name: /settings/i });
    expect(settingsButton).toHaveAttribute('aria-label', 'Settings alt menüsünü aç');
    expect(settingsButton).toHaveAttribute('aria-expanded', 'false');
    expect(settingsButton).toHaveAttribute('aria-haspopup', 'menu');
  });

  test('should support keyboard navigation', () => {
    renderWithProviders(<Sidebar routes={mockAccessibilityRoutes} />);
    
    // Test Tab navigation
    const firstLink = screen.getByRole('link', { name: /dashboard/i });
    firstLink.focus();
    expect(firstLink).toHaveFocus();
    
    // Test keyboard interaction
    fireEvent.keyDown(firstLink, { key: 'Enter' });
    // Navigation should work (handled by React Router)
  });

  test('should have no accessibility violations in collapsed state', async () => {
    const { container } = renderWithProviders(
      <SidebarContentCollapsed routes={mockAccessibilityRoutes} />
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('should have proper tooltips in collapsed state', () => {
    renderWithProviders(<SidebarContentCollapsed routes={mockAccessibilityRoutes} />);
    
    // Tooltips should be present for icon-only buttons
    const iconButtons = screen.getAllByRole('button');
    iconButtons.forEach(button => {
      expect(button).toHaveAttribute('aria-label');
    });
  });

  test('should have no accessibility violations for submenu panel', async () => {
    const { container } = renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('should have proper ARIA attributes for submenu panel', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    const dialog = screen.getByRole('dialog');
    expect(dialog).toHaveAttribute('aria-modal', 'true');
    expect(dialog).toHaveAttribute('aria-label', 'Settings alt menüsü');
    expect(dialog).toHaveAttribute('aria-describedby', 'submenu-description');
    
    // Check close button
    const closeButton = screen.getByRole('button', { name: /kapat/i });
    expect(closeButton).toHaveAttribute('aria-label', 'Alt menüyü kapat (Escape tuşu)');
  });

  test('should have proper menu item roles in submenu', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    const menuItems = screen.getAllByRole('menuitem');
    expect(menuItems).toHaveLength(1);
    
    menuItems.forEach(item => {
      expect(item).toHaveAttribute('tabIndex', '0');
    });
  });

  test('should support focus management in submenu', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    // First menu item should be focusable
    const firstMenuItem = screen.getByRole('menuitem');
    firstMenuItem.focus();
    expect(firstMenuItem).toHaveFocus();
  });

  test('should handle reduced motion preferences', () => {
    // Mock prefers-reduced-motion
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => {
        if (query.includes('prefers-reduced-motion')) {
          return {
            matches: true,
            media: query,
            onchange: null,
            addListener: vi.fn(),
            removeListener: vi.fn(),
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            dispatchEvent: vi.fn(),
          };
        }
        return {
          matches: query.includes('(min-width: 992px)'),
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        };
      }),
    });
    
    renderWithProviders(<Sidebar routes={mockAccessibilityRoutes} />);
    
    // Component should render without animations when reduced motion is preferred
    const navigation = screen.getByRole('navigation');
    expect(navigation).toBeInTheDocument();
  });

  test('should have proper color contrast', () => {
    renderWithProviders(<Sidebar routes={mockAccessibilityRoutes} />);
    
    // This is a basic test - in a real scenario, you'd use tools like
    // @testing-library/jest-dom with custom matchers for color contrast
    const menuItems = screen.getAllByRole('link');
    menuItems.forEach(item => {
      expect(item).toBeVisible();
    });
  });

  test('should support screen reader announcements', () => {
    renderWithProviders(<Sidebar routes={mockAccessibilityRoutes} />);
    
    // Check for proper labeling
    const navigation = screen.getByRole('navigation');
    expect(navigation).toHaveAttribute('aria-label');
    
    // Check for descriptive text
    const menuItems = screen.getAllByRole('link');
    menuItems.forEach(item => {
      expect(item).toHaveAttribute('aria-label');
    });
  });

  test('should handle focus trapping in submenu', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    // Focus should be managed within the submenu
    const dialog = screen.getByRole('dialog');
    const focusableElements = dialog.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    expect(focusableElements.length).toBeGreaterThan(0);
  });

  test('should provide clear visual focus indicators', () => {
    renderWithProviders(<Sidebar routes={mockAccessibilityRoutes} />);
    
    const firstLink = screen.getByRole('link', { name: /dashboard/i });
    firstLink.focus();
    
    // Focus should be visible (this would be tested with visual regression in a real scenario)
    expect(firstLink).toHaveFocus();
  });
});
