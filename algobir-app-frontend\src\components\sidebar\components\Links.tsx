import { NavLink, useLocation } from 'react-router-dom';
import { Box, Flex, HStack, Text, useColorModeValue, Icon } from '@chakra-ui/react';
import { FiChevronRight } from 'react-icons/fi';
import { RouteType } from '../../../routes';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { useSidebar } from '../../../context/SidebarContext';
import { preloadRoute } from '../../../utils/routePreloader';
import { TRANSITIONS } from '../animations';

interface SidebarLinksProps {
  routes: RouteType[];
}

export function SidebarLinks(props: SidebarLinksProps) {
  // Chakra color mode
  let location = useLocation();
  let activeColor = useColorModeValue('gray.700', 'white');
  let textColor = useColorModeValue('secondaryGray.500', 'white');
  let activeIcon = useColorModeValue('brand.500', 'brand.400');
  let brandBg = useColorModeValue('brand.500', 'brand.400');
  let hoverBg = useColorModeValue('secondaryGray.100', 'whiteAlpha.100');
  let chevronColor = useColorModeValue('gray.400', 'gray.500');

  const { isAdmin } = useAdminAuth();
  const { openSubmenu, collapseOnNavigation } = useSidebar();
  const { routes } = props;

  // Verifies if routeName is the one active (in browser input)
  const activeRoute = (routeName: string) => {
    return location.pathname === routeName || 
      (routeName !== '/' && location.pathname.startsWith(routeName));
  };

  // Performance: Map route paths to preloader keys
  const getPreloaderKey = (path: string): string | null => {
    const preloaderMap: { [key: string]: string } = {
      '/': 'dashboard',
      '/trades': 'trades',
      '/marketplace': 'marketplace',
      '/statistics': 'statistics',
      '/management': 'management',
      '/profile': 'profile',
      '/open-positions': 'openPositions',
      '/seller/robots': 'robotManagement',
      '/notifications': 'notifications'
    };
    return preloaderMap[path] || null;
  };

  // Handle submenu click
  const handleSubmenuClick = (route: RouteType, e: React.MouseEvent) => {
    if (route.hasSubmenu && route.children) {
      e.preventDefault();
      const submenuItems = route.children.map(child => ({
        id: child.id,
        name: child.name,
        path: child.path,
        icon: child.icon,
        description: child.description
      }));
      openSubmenu(route.path, submenuItems);
    }
  };

  // Handle navigation link click - auto-collapse sidebar
  const handleNavigationClick = () => {
    collapseOnNavigation();
  };

  // This function creates the links from the routes and checks if it's admin route
  const createLinks = (routes: RouteType[]) => {
    return routes.map((route: RouteType, index: number) => {
      // Admin paneli kontrolü
      if (route.path === '/admin' && !isAdmin) {
        return null;
      }

      const preloaderKey = getPreloaderKey(route.path);
      const hasSubmenu = route.hasSubmenu && route.children && route.children.length > 0;

      // For submenu items, use a clickable div instead of NavLink
      if (hasSubmenu) {
        return (
          <Box key={index}>
            <HStack
              spacing={activeRoute(route.path) ? '22px' : '26px'}
              py={{ base: '12px', md: '10px' }}
              px='12px'
              my='2px'
              minH="44px"
              borderRadius="16px"
              _hover={{
                bg: hoverBg,
                transform: 'translateX(4px)',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}
              bg={activeRoute(route.path) ? hoverBg : 'transparent'}
              cursor="pointer"
              transition={TRANSITIONS.hover}
              sx={{
                '@media (prefers-reduced-motion: reduce)': {
                  transition: 'none'
                }
              }}
              role="button"
              aria-label={`${route.name} menüsünü aç`}
              onClick={(e) => handleSubmenuClick(route, e)}
              onMouseEnter={() => {
                if (preloaderKey) {
                  preloadRoute(preloaderKey as any);
                }
              }}
            >
              <Flex w='100%' alignItems='center' justifyContent='flex-start'>
                <Box
                  color={activeRoute(route.path) ? activeIcon : textColor}
                  me='18px'
                  mb="3px"
                >
                  {route.icon}
                </Box>
                <Text
                  me='auto'
                  color={activeRoute(route.path) ? activeColor : textColor}
                  fontWeight={activeRoute(route.path) ? 'bold' : 'normal'}
                  fontSize="md"
                >
                  {route.name}
                </Text>
                <Icon
                  as={FiChevronRight}
                  color={chevronColor}
                  w="16px"
                  h="16px"
                  transition="transform 0.2s ease"
                  _groupHover={{ transform: 'translateX(2px)' }}
                />
              </Flex>
              <Box
                h='36px'
                w='4px'
                bg={activeRoute(route.path) ? brandBg : 'transparent'}
                borderRadius='5px'
              />
            </HStack>
          </Box>
        );
      }

      // Regular navigation link
      return (
        <NavLink key={index} to={route.path} onClick={handleNavigationClick}>
          <Box>
            <HStack
              spacing={activeRoute(route.path) ? '22px' : '26px'}
              py={{ base: '12px', md: '10px' }}
              px='12px'
              my='2px'
              minH="44px"
              borderRadius="16px"
              _hover={{
                bg: hoverBg,
                transform: 'translateX(4px)',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
              }}
              bg={activeRoute(route.path) ? hoverBg : 'transparent'}
              cursor="pointer"
              transition={TRANSITIONS.hover}
              sx={{
                '@media (prefers-reduced-motion: reduce)': {
                  transition: 'none'
                }
              }}
              role="button"
              aria-label={`${route.name} sayfasına git`}
              onMouseEnter={() => {
                if (preloaderKey) {
                  preloadRoute(preloaderKey as any);
                }
              }}
            >
              <Flex w='100%' alignItems='center' justifyContent='flex-start'>
                <Box
                  color={activeRoute(route.path) ? activeIcon : textColor}
                  me='18px'
                  mb="3px"
                >
                  {route.icon}
                </Box>
                <Text
                  me='auto'
                  color={activeRoute(route.path) ? activeColor : textColor}
                  fontWeight={activeRoute(route.path) ? 'bold' : 'normal'}
                  fontSize="md"
                >
                  {route.name}
                </Text>
              </Flex>
              <Box
                h='36px'
                w='4px'
                bg={activeRoute(route.path) ? brandBg : 'transparent'}
                borderRadius='5px'
              />
            </HStack>
          </Box>
        </NavLink>
      );
    });
  };

  return <>{createLinks(routes)}</>;
}

export default SidebarLinks; 