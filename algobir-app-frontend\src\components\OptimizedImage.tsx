import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Box, Skeleton, Image as ChakraImage, ImageProps as ChakraImageProps } from '@chakra-ui/react';
import { ImageOptimizer } from '../utils/performanceOptimizer';

/**
 * Enhanced image component props
 */
interface OptimizedImageProps extends Omit<ChakraImageProps, 'src' | 'srcSet'> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: number;
  lazy?: boolean;
  responsive?: boolean;
  sizes?: string;
  breakpoints?: number[];
  fallbackSrc?: string;
  placeholder?: 'skeleton' | 'blur' | 'none';
  onLoad?: () => void;
  onError?: React.ReactEventHandler<HTMLImageElement>;
  priority?: boolean; // For above-the-fold images
}

/**
 * Optimized Image Component
 * 
 * Features:
 * - Lazy loading with Intersection Observer
 * - WebP format support with fallback
 * - Responsive images with srcSet
 * - Loading states and error handling
 * - Performance optimizations for Core Web Vitals
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  quality = 75,
  lazy = true,
  responsive = true,
  sizes,
  breakpoints = [320, 640, 768, 1024, 1280, 1536],
  fallbackSrc,
  placeholder = 'skeleton',
  onLoad,
  onError,
  priority = false,
  ...chakraProps
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [optimizedSrc, setOptimizedSrc] = useState<string>('');
  const [optimizedSrcSet, setOptimizedSrcSet] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);
  const [isInView, setIsInView] = useState(!lazy || priority);

  // Generate optimized image URLs
  useEffect(() => {
    const generateOptimizedUrls = async () => {
      try {
        // Get optimized main source
        const mainSrc = await ImageOptimizer.getOptimizedImageUrl(src, width, quality);
        setOptimizedSrc(mainSrc);

        // Generate responsive srcSet if needed
        if (responsive && breakpoints.length > 0) {
          const supportsWebP = await ImageOptimizer.supportsWebP();
          const format = supportsWebP ? 'webp' : 'jpg';
          
          const srcSet = breakpoints
            .map(bp => `${src}?w=${bp}&q=${quality}&f=${format} ${bp}w`)
            .join(', ');
          
          setOptimizedSrcSet(srcSet);
        }
      } catch (error) {
        console.warn('Failed to generate optimized image URLs:', error);
        setOptimizedSrc(src); // Fallback to original
      }
    };

    generateOptimizedUrls();
  }, [src, width, quality, responsive, breakpoints]);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || !imgRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before entering viewport
        threshold: 0.1
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [lazy, priority]);

  // Handle image load
  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    setIsError(false);
    onLoad?.();
  }, [onLoad]);

  // Handle image error
  const handleError = useCallback((event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    setIsError(true);
    setIsLoaded(false);
    
    // Try fallback source if available
    if (fallbackSrc && imgRef.current && imgRef.current.src !== fallbackSrc) {
      imgRef.current.src = fallbackSrc;
      return;
    }
    
    onError?.(event.nativeEvent);
  }, [fallbackSrc, onError]);

  // Generate sizes attribute for responsive images
  const responsiveSizes = sizes || (responsive ? 
    '(max-width: 320px) 280px, (max-width: 640px) 600px, (max-width: 768px) 720px, (max-width: 1024px) 980px, (max-width: 1280px) 1200px, 1500px'
    : undefined
  );

  // Render placeholder while loading
  const renderPlaceholder = () => {
    if (placeholder === 'none') return null;
    
    if (placeholder === 'skeleton') {
      return (
        <Skeleton
          width={width || '100%'}
          height={height || '200px'}
          borderRadius={chakraProps.borderRadius || 'md'}
          startColor="gray.100"
          endColor="gray.300"
          speed={1.2}
        />
      );
    }

    if (placeholder === 'blur') {
      return (
        <Box
          width={width || '100%'}
          height={height || '200px'}
          bg="gray.100"
          borderRadius={chakraProps.borderRadius || 'md'}
          display="flex"
          alignItems="center"
          justifyContent="center"
          position="relative"
          overflow="hidden"
          _before={{
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
            animation: 'shimmer 1.5s infinite',
          }}
          sx={{
            '@keyframes shimmer': {
              '0%': { transform: 'translateX(-100%)' },
              '100%': { transform: 'translateX(100%)' }
            }
          }}
        />
      );
    }

    return null;
  };

  // Don't render image until it's in view (for lazy loading)
  if (!isInView) {
    return (
      <Box
        ref={imgRef}
        width={width || '100%'}
        height={height || '200px'}
        {...chakraProps}
      >
        {renderPlaceholder()}
      </Box>
    );
  }

  return (
    <Box position="relative" width={width || '100%'} height={height} {...chakraProps}>
      {/* Show placeholder while loading */}
      {!isLoaded && !isError && renderPlaceholder()}
      
      {/* Main image */}
      <ChakraImage
        ref={imgRef}
        src={optimizedSrc}
        srcSet={responsive ? optimizedSrcSet : undefined}
        sizes={responsiveSizes}
        alt={alt}
        width={width}
        height={height}
        onLoad={handleLoad}
        onError={handleError}
        loading={lazy && !priority ? 'lazy' : 'eager'}
        decoding="async"
        style={{
          opacity: isLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out',
          display: isError ? 'none' : 'block'
        }}
        {...chakraProps}
      />
      
      {/* Error fallback */}
      {isError && (
        <Box
          width="100%"
          height="100%"
          bg="gray.100"
          display="flex"
          alignItems="center"
          justifyContent="center"
          borderRadius={chakraProps.borderRadius || 'md'}
          color="gray.500"
          fontSize="sm"
        >
          Resim yüklenemedi
        </Box>
      )}
    </Box>
  );
};

export default OptimizedImage;

/**
 * Prebuilt optimized image variants for common use cases
 */

// Avatar image with circular crop and optimized loading
export const OptimizedAvatar: React.FC<Omit<OptimizedImageProps, 'responsive' | 'breakpoints'> & { size?: number }> = ({
  size = 40,
  ...props
}) => (
  <OptimizedImage
    {...props}
    width={size}
    height={size}
    borderRadius="full"
    responsive={false}
    lazy={false} // Avatars are usually above the fold
    priority
  />
);

// Hero image with priority loading
export const OptimizedHeroImage: React.FC<OptimizedImageProps> = (props) => (
  <OptimizedImage
    {...props}
    priority
    lazy={false}
    placeholder="blur"
    quality={85} // Higher quality for hero images
  />
);

// Thumbnail image with aggressive lazy loading
export const OptimizedThumbnail: React.FC<OptimizedImageProps> = (props) => (
  <OptimizedImage
    {...props}
    lazy
    quality={60} // Lower quality for thumbnails
    placeholder="skeleton"
    responsive
  />
);
