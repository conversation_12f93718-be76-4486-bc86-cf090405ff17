import { extendTheme, ThemeConfig } from '@chakra-ui/react';
import { CardComponent } from './components/card';
import { ButtonComponent } from './components/button';
import { InputComponent } from './components/input';
import { LinkComponent } from './components/link';
import { BadgeComponent } from './components/badge';
import { SwitchComponent } from './components/switch';
import { ProgressComponent } from './components/progress';
import { TableComponent } from './components/table';
import { DrawerComponent } from './components/drawer';
import { ModalComponent } from './components/modal';
import { globalStyles } from './styles';
import { breakpoints } from './foundations/breakpoints';

const config: ThemeConfig = {
  initialColorMode: 'light',
  useSystemColorMode: false,
};

export const horizonTheme = extendTheme(
  { config },
  { breakpoints },
  globalStyles,
  CardComponent,
  ButtonComponent,
  InputComponent,
  LinkComponent,
  BadgeComponent,
  SwitchComponent,
  ProgressComponent,
  TableComponent,
  DrawerComponent,
  ModalComponent
);

export default horizonTheme; 