---
type: "manual"
priority: "high"
scope: ["admin", "management", "monitoring", "user-control", "system-status"]
last_updated: "2025-01-29"
---

# Admin Panel Features - Platform Management

## 🛡️ Admin Architecture

### Admin Access Control
```typescript
// Role-based admin access system
interface AdminRoles {
  superuser: boolean;     // Full platform access
  admin: boolean;         // Limited admin access
  moderator: boolean;     // Content moderation only
}

// Admin Route Protection
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isAdmin, loading } = useAuth();
  
  if (loading) return <PageLoader />;
  
  if (!user || !isAdmin) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

// Admin Navigation Structure
const adminRoutes = [
  { path: '/admin', component: AdminDashboard, name: 'Dashboard' },
  { path: '/admin/users', component: UserManagement, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>önetimi' },
  { path: '/admin/trades', component: AllTradesView, name: '<PERSON><PERSON><PERSON>' },
  { path: '/admin/notifications', component: NotificationManagement, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { path: '/admin/statistics', component: AdminStatistics, name: 'Platform İstatistikleri' },
  { path: '/admin/status', component: AdminStatus, name: 'Sistem Durumu' }
];
```

### Admin Sidebar Integration
```typescript
// Admin menu in sidebar navigation
const adminSidebarItems = {
  name: 'Admin',
  path: '/admin',
  icon: ShieldIcon,
  submenu: [
    { name: 'Dashboard', path: '/admin', icon: DashboardIcon },
    { name: 'Kullanıcılar', path: '/admin/users', icon: UsersIcon },
    { name: 'İşlemler', path: '/admin/trades', icon: TradingIcon },
    { name: 'Bildirimler', path: '/admin/notifications', icon: BellIcon },
    { name: 'İstatistikler', path: '/admin/statistics', icon: ChartIcon },
    { name: 'Sistem Durumu', path: '/admin/status', icon: StatusIcon }
  ],
  requiresAdmin: true
};

// Conditional rendering based on admin status
const SidebarContent = ({ routes }: { routes: RouteType[] }) => {
  const { isAdmin } = useAuth();
  
  const filteredRoutes = routes.filter(route => {
    if (route.requiresAdmin) {
      return isAdmin;
    }
    return true;
  });
  
  return <SidebarLinks routes={filteredRoutes} />;
};
```

## 👥 User Management

### User Administration
```typescript
// User management interface
interface AdminUser {
  id: string;
  username: string;
  full_name: string;
  email: string;
  avatar_url: string;
  created_at: string;
  last_sign_in_at: string;
  is_active: boolean;
  is_superuser: boolean;
  total_trades: number;
  total_investment: number;
  subscription_count: number;
}

// User Management Component
const UserManagement = () => {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<UserFilters>({});
  
  const fetchUsers = useCallback(async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.rpc('get_admin_users_overview', {
        p_search: filters.search,
        p_is_active: filters.isActive,
        p_is_superuser: filters.isSuperuser,
        p_limit: filters.limit || 50,
        p_offset: filters.offset || 0
      });
      
      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  }, [filters]);
  
  const toggleUserStatus = async (userId: string, isActive: boolean) => {
    try {
      const { error } = await supabase
        .from('user_settings')
        .update({ is_active: !isActive })
        .eq('id', userId);
      
      if (error) throw error;
      
      toast({
        title: 'Başarılı',
        description: `Kullanıcı durumu ${!isActive ? 'aktif' : 'pasif'} olarak güncellendi.`,
        status: 'success'
      });
      
      fetchUsers(); // Refresh list
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Kullanıcı durumu güncellenirken hata oluştu.',
        status: 'error'
      });
    }
  };
  
  const promoteToAdmin = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('user_settings')
        .update({ is_superuser: true })
        .eq('id', userId);
      
      if (error) throw error;
      
      toast({
        title: 'Başarılı',
        description: 'Kullanıcı admin olarak yetkilendirildi.',
        status: 'success'
      });
      
      fetchUsers();
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Admin yetkisi verilirken hata oluştu.',
        status: 'error'
      });
    }
  };
  
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);
  
  return (
    <VStack spacing={6} align="stretch">
      {/* User Filters */}
      <UserFilters filters={filters} onFiltersChange={setFilters} />
      
      {/* User Table */}
      <UserTable 
        users={users} 
        loading={loading}
        onToggleStatus={toggleUserStatus}
        onPromoteToAdmin={promoteToAdmin}
      />
    </VStack>
  );
};
```

### User Activity Monitoring
```typescript
// Real-time user activity tracking
const UserActivityMonitor = () => {
  const [activeUsers, setActiveUsers] = useState<ActiveUser[]>([]);
  const [recentActions, setRecentActions] = useState<UserAction[]>([]);
  
  useEffect(() => {
    // Real-time subscription for user activities
    const subscription = supabase
      .channel('user_activities')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'user_activities' },
        (payload) => {
          setRecentActions(prev => [payload.new, ...prev.slice(0, 49)]);
        }
      )
      .subscribe();
    
    return () => subscription.unsubscribe();
  }, []);
  
  return (
    <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
      {/* Active Users */}
      <Card>
        <CardHeader>
          <Heading size="md">Aktif Kullanıcılar</Heading>
        </CardHeader>
        <CardBody>
          <VStack spacing={3} align="stretch">
            {activeUsers.map(user => (
              <HStack key={user.id} justify="space-between">
                <HStack>
                  <Avatar size="sm" src={user.avatar_url} />
                  <VStack align="start" spacing={0}>
                    <Text fontWeight="bold">{user.username}</Text>
                    <Text fontSize="sm" color="gray.500">
                      {formatDistanceToNow(new Date(user.last_activity))} önce
                    </Text>
                  </VStack>
                </HStack>
                <Badge colorScheme="green">Çevrimiçi</Badge>
              </HStack>
            ))}
          </VStack>
        </CardBody>
      </Card>
      
      {/* Recent Actions */}
      <Card>
        <CardHeader>
          <Heading size="md">Son Aktiviteler</Heading>
        </CardHeader>
        <CardBody>
          <VStack spacing={3} align="stretch">
            {recentActions.map(action => (
              <HStack key={action.id} justify="space-between">
                <VStack align="start" spacing={0}>
                  <Text fontWeight="bold">{action.action_type}</Text>
                  <Text fontSize="sm">{action.username}</Text>
                </VStack>
                <Text fontSize="sm" color="gray.500">
                  {format(new Date(action.created_at), 'HH:mm')}
                </Text>
              </HStack>
            ))}
          </VStack>
        </CardBody>
      </Card>
    </SimpleGrid>
  );
};
```

## 📊 Platform Statistics

### Admin Dashboard Overview
```typescript
// Platform-wide statistics for admin dashboard
interface PlatformStats {
  totalUsers: number;
  activeUsers: number;
  totalTrades: number;
  totalVolume: number;
  totalRobots: number;
  activeSubscriptions: number;
  systemHealth: SystemHealth;
  recentGrowth: GrowthMetrics;
}

const AdminDashboard = () => {
  const [stats, setStats] = useState<PlatformStats>({});
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchPlatformStats = async () => {
      try {
        const { data, error } = await supabase.rpc('get_platform_statistics');
        if (error) throw error;
        setStats(data);
      } catch (error) {
        console.error('Error fetching platform stats:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchPlatformStats();
    
    // Refresh every 5 minutes
    const interval = setInterval(fetchPlatformStats, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);
  
  if (loading) return <PageLoader />;
  
  return (
    <VStack spacing={6} align="stretch">
      {/* Key Metrics */}
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={6}>
        <StatCard
          label="Toplam Kullanıcı"
          value={stats.totalUsers?.toLocaleString()}
          color="blue"
          icon={UsersIcon}
        />
        <StatCard
          label="Aktif Kullanıcı"
          value={stats.activeUsers?.toLocaleString()}
          color="green"
          icon={UserCheckIcon}
        />
        <StatCard
          label="Toplam İşlem"
          value={stats.totalTrades?.toLocaleString()}
          color="purple"
          icon={TradingIcon}
        />
        <StatCard
          label="İşlem Hacmi"
          value={formatCurrency(stats.totalVolume)}
          color="orange"
          icon={VolumeIcon}
        />
      </SimpleGrid>
      
      {/* System Health */}
      <SystemHealthPanel health={stats.systemHealth} />
      
      {/* Growth Charts */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        <Card>
          <CardHeader>
            <Heading size="md">Kullanıcı Büyümesi</Heading>
          </CardHeader>
          <CardBody>
            <UserGrowthChart data={stats.recentGrowth?.userGrowth} />
          </CardBody>
        </Card>
        
        <Card>
          <CardHeader>
            <Heading size="md">İşlem Hacmi Trendi</Heading>
          </CardHeader>
          <CardBody>
            <VolumeGrowthChart data={stats.recentGrowth?.volumeGrowth} />
          </CardBody>
        </Card>
      </SimpleGrid>
      
      {/* Recent Activity */}
      <UserActivityMonitor />
    </VStack>
  );
};
```

## 🔔 Notification Management

### Platform Announcements
```typescript
// Admin notification and announcement system
interface PlatformAnnouncement {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  target_audience: 'all' | 'active_users' | 'premium_users';
  is_active: boolean;
  scheduled_at?: string;
  expires_at?: string;
  created_by: string;
  created_at: string;
}

const NotificationManagement = () => {
  const [announcements, setAnnouncements] = useState<PlatformAnnouncement[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  
  const createAnnouncement = async (announcement: Omit<PlatformAnnouncement, 'id' | 'created_at' | 'created_by'>) => {
    try {
      const { data, error } = await supabase
        .from('platform_announcements')
        .insert([{
          ...announcement,
          created_by: user?.id
        }])
        .select()
        .single();
      
      if (error) throw error;
      
      // Send to all targeted users
      await supabase.rpc('broadcast_announcement', {
        p_announcement_id: data.id,
        p_target_audience: announcement.target_audience
      });
      
      toast({
        title: 'Başarılı',
        description: 'Duyuru başarıyla oluşturuldu ve gönderildi.',
        status: 'success'
      });
      
      setAnnouncements(prev => [data, ...prev]);
      setIsCreateModalOpen(false);
    } catch (error) {
      toast({
        title: 'Hata',
        description: 'Duyuru oluşturulurken hata oluştu.',
        status: 'error'
      });
    }
  };
  
  return (
    <VStack spacing={6} align="stretch">
      {/* Create Announcement Button */}
      <HStack justify="space-between">
        <Heading size="lg">Bildirim Yönetimi</Heading>
        <Button
          colorScheme="blue"
          leftIcon={<AddIcon />}
          onClick={() => setIsCreateModalOpen(true)}
        >
          Yeni Duyuru
        </Button>
      </HStack>
      
      {/* Announcements List */}
      <AnnouncementsList 
        announcements={announcements}
        onEdit={handleEditAnnouncement}
        onDelete={handleDeleteAnnouncement}
      />
      
      {/* Create Modal */}
      <CreateAnnouncementModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={createAnnouncement}
      />
    </VStack>
  );
};
```

## 🖥️ System Status Monitoring

### Real-time System Health
```typescript
// System monitoring and health checks
interface SystemHealth {
  database: {
    status: 'healthy' | 'warning' | 'critical';
    responseTime: number;
    activeConnections: number;
    maxConnections: number;
  };
  edgeFunctions: {
    status: 'healthy' | 'warning' | 'critical';
    avgResponseTime: number;
    errorRate: number;
    requestsPerMinute: number;
  };
  orderTransmission: {
    status: 'healthy' | 'warning' | 'critical';
    avgTransmissionTime: number;
    successRate: number;
    failureRate: number;
  };
  realtime: {
    status: 'healthy' | 'warning' | 'critical';
    activeConnections: number;
    messageLatency: number;
  };
}

const AdminStatus = () => {
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({});
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchSystemHealth = async () => {
      try {
        const { data, error } = await supabase.rpc('get_system_health_metrics');
        if (error) throw error;
        setSystemHealth(data);
      } catch (error) {
        console.error('Error fetching system health:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchSystemHealth();
    
    // Real-time updates every 30 seconds
    const interval = setInterval(fetchSystemHealth, 30 * 1000);
    return () => clearInterval(interval);
  }, []);
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'green';
      case 'warning': return 'yellow';
      case 'critical': return 'red';
      default: return 'gray';
    }
  };
  
  return (
    <VStack spacing={6} align="stretch">
      <Heading size="lg">Sistem Durumu</Heading>
      
      {/* System Overview */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
        <StatusCard
          title="Veritabanı"
          status={systemHealth.database?.status}
          metrics={[
            { label: 'Yanıt Süresi', value: `${systemHealth.database?.responseTime}ms` },
            { label: 'Aktif Bağlantı', value: `${systemHealth.database?.activeConnections}/${systemHealth.database?.maxConnections}` }
          ]}
          color={getStatusColor(systemHealth.database?.status)}
        />
        
        <StatusCard
          title="Edge Functions"
          status={systemHealth.edgeFunctions?.status}
          metrics={[
            { label: 'Ortalama Yanıt', value: `${systemHealth.edgeFunctions?.avgResponseTime}ms` },
            { label: 'Hata Oranı', value: `${systemHealth.edgeFunctions?.errorRate}%` }
          ]}
          color={getStatusColor(systemHealth.edgeFunctions?.status)}
        />
        
        <StatusCard
          title="Emir İletimi"
          status={systemHealth.orderTransmission?.status}
          metrics={[
            { label: 'İletim Hızı', value: `${systemHealth.orderTransmission?.avgTransmissionTime}ms` },
            { label: 'Başarı Oranı', value: `${systemHealth.orderTransmission?.successRate}%` }
          ]}
          color={getStatusColor(systemHealth.orderTransmission?.status)}
        />
        
        <StatusCard
          title="Real-time"
          status={systemHealth.realtime?.status}
          metrics={[
            { label: 'Aktif Bağlantı', value: systemHealth.realtime?.activeConnections?.toString() },
            { label: 'Mesaj Gecikmesi', value: `${systemHealth.realtime?.messageLatency}ms` }
          ]}
          color={getStatusColor(systemHealth.realtime?.status)}
        />
      </SimpleGrid>
      
      {/* Performance Charts */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        <Card>
          <CardHeader>
            <Heading size="md">Emir İletim Performansı</Heading>
          </CardHeader>
          <CardBody>
            <OrderTransmissionChart />
          </CardBody>
        </Card>
        
        <Card>
          <CardHeader>
            <Heading size="md">Sistem Kaynak Kullanımı</Heading>
          </CardHeader>
          <CardBody>
            <ResourceUsageChart />
          </CardBody>
        </Card>
      </SimpleGrid>
    </VStack>
  );
};
```

## Update Requirements

### When to Update This File
- New admin features or capabilities added
- User management functionality changes
- System monitoring improvements
- Notification system updates
- Security policy modifications
- Platform statistics enhancements

### Related Files to Update
- Update `security/GUIDELINES.mdc` for admin security changes
- Update `backend/SUPABASE_EDGE.mdc` for admin database functions
- Update `frontend/MAIN_APP.mdc` for admin UI components
- Update `core/PROJECT_CORE.mdc` for admin architecture changes
- Update `performance/OPTIMIZATION.mdc` for monitoring optimizations
