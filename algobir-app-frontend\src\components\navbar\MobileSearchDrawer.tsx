/**
 * Mobile Search Drawer - Enhanced UX for mobile search
 * Optimized for touch interactions and mobile-first design
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Drawer,
  DrawerOverlay,
  DrawerContent,

  DrawerHeader,
  DrawerBody,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  VStack,
  HStack,
  Text,
  Box,
  Icon,
  Button,

  Badge,
  useColorModeValue,
  IconButton,
  Kbd,
  Flex,
  Spinner,
  useToast
} from '@chakra-ui/react';
import {
  FiSearch,
  FiX,
  FiClock,
  FiTrendingUp,
  FiUser,
  FiFileText,
  FiSettings,
  FiArrowRight
} from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { useTouchInteractions } from '../../hooks/useTouchInteractions';
import { TOUCH_TARGETS } from '../../utils/responsiveUtils';

interface SearchResult {
  id: string;
  title: string;
  description?: string;
  type: 'user' | 'robot' | 'trade' | 'page' | 'setting';
  url: string;
  icon: React.ElementType;
  badge?: string;
}

interface MobileSearchDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  placeholder?: string;
}

const MobileSearchDrawer: React.FC<MobileSearchDrawerProps> = ({
  isOpen,
  onClose,
  placeholder = "Ara..."
}) => {
  const navigate = useNavigate();
  const toast = useToast();
  const inputRef = useRef<HTMLInputElement>(null);
  const drawerRef = useRef<HTMLElement>(null);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  // Color mode values
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  const selectedBg = useColorModeValue('blue.50', 'blue.900');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const mutedColor = useColorModeValue('gray.500', 'gray.400');

  // Touch interactions for swipe to close
  const touchCallbacks = {
    onSwipe: (gesture: any) => {
      if (gesture.direction === 'right' && gesture.distance > 100) {
        onClose();
      }
    }
  };

  useTouchInteractions(drawerRef as React.RefObject<HTMLElement>, { enableSwipe: true }, touchCallbacks);

  // Mock search data - replace with actual search API
  const mockResults: SearchResult[] = [
    {
      id: '1',
      title: 'Dashboard',
      description: 'Ana kontrol paneli',
      type: 'page',
      url: '/dashboard',
      icon: FiTrendingUp
    },
    {
      id: '2',
      title: 'Kullanıcı Ayarları',
      description: 'Profil ve hesap ayarları',
      type: 'setting',
      url: '/settings',
      icon: FiSettings
    },
    {
      id: '3',
      title: 'Trading Robotları',
      description: 'Aktif robotlarınız',
      type: 'robot',
      url: '/robots',
      icon: FiUser,
      badge: '3 aktif'
    }
  ];

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recent_searches');
    if (saved) {
      try {
        setRecentSearches(JSON.parse(saved));
      } catch {
        setRecentSearches([]);
      }
    }
  }, []);

  // Focus input when drawer opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Search function with debouncing
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setSelectedIndex(-1);
      return;
    }

    setIsSearching(true);
    const timeoutId = setTimeout(() => {
      // Mock search - replace with actual API call
      const filtered = mockResults.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setSearchResults(filtered);
      setSelectedIndex(-1);
      setIsSearching(false);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setSelectedIndex(prev => 
            prev < searchResults.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
          break;
        case 'Enter':
          event.preventDefault();
          if (selectedIndex >= 0 && searchResults[selectedIndex]) {
            handleResultClick(searchResults[selectedIndex]);
          } else if (searchQuery.trim()) {
            handleSearch();
          }
          break;
        case 'Escape':
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, searchResults, searchQuery]);

  const handleSearch = () => {
    if (!searchQuery.trim()) return;

    // Add to recent searches
    const newRecent = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5);
    setRecentSearches(newRecent);
    localStorage.setItem('recent_searches', JSON.stringify(newRecent));

    // Perform search
    toast({
      title: 'Arama yapılıyor...',
      description: `"${searchQuery}" için sonuçlar getiriliyor`,
      status: 'info',
      duration: 2000,
    });

    onClose();
  };

  const handleResultClick = (result: SearchResult) => {
    // Add to recent searches
    const searchTerm = result.title;
    const newRecent = [searchTerm, ...recentSearches.filter(s => s !== searchTerm)].slice(0, 5);
    setRecentSearches(newRecent);
    localStorage.setItem('recent_searches', JSON.stringify(newRecent));

    // Navigate
    navigate(result.url);
    onClose();
  };

  const handleRecentClick = (recent: string) => {
    setSearchQuery(recent);
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('recent_searches');
  };

  const getResultIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'user': return FiUser;
      case 'robot': return FiTrendingUp;
      case 'trade': return FiFileText;
      case 'setting': return FiSettings;
      default: return FiFileText;
    }
  };

  return (
    <Drawer
      isOpen={isOpen}
      placement="top"
      onClose={onClose}
      size="full"
      autoFocus={false}
      returnFocusOnClose={false}
    >
      <DrawerOverlay bg="blackAlpha.600" backdropFilter="blur(4px)" />
      <DrawerContent
        ref={drawerRef}
        bg={bgColor}
        maxH="100vh"
        overflowY="auto"
        borderBottomRadius="xl"
        boxShadow="xl"
      >
        <DrawerHeader
          pb={2}
          borderBottomWidth="1px"
          borderColor={borderColor}
        >
          <HStack spacing={3}>
            <InputGroup size="lg">
              <InputLeftElement pointerEvents="none">
                <Icon as={FiSearch} color={mutedColor} />
              </InputLeftElement>
              <Input
                ref={inputRef}
                placeholder={placeholder}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleSearch();
                  }
                }}
                bg={useColorModeValue('gray.50', 'gray.700')}
                border="none"
                borderRadius="xl"
                fontSize="md"
                _focus={{
                  bg: useColorModeValue('white', 'gray.600'),
                  boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
                }}
                _placeholder={{ color: mutedColor }}
              />
              <InputRightElement>
                {isSearching ? (
                  <Spinner size="sm" color={mutedColor} />
                ) : searchQuery ? (
                  <IconButton
                    aria-label="Temizle"
                    icon={<Icon as={FiX} />}
                    size="sm"
                    variant="ghost"
                    borderRadius="full"
                    onClick={() => setSearchQuery('')}
                  />
                ) : null}
              </InputRightElement>
            </InputGroup>
            
            <IconButton
              aria-label="Kapat"
              icon={<Icon as={FiX} />}
              size="lg"
              variant="ghost"
              borderRadius="full"
              onClick={onClose}
              minW={TOUCH_TARGETS.comfortable}
              minH={TOUCH_TARGETS.comfortable}
            />
          </HStack>
        </DrawerHeader>

        <DrawerBody p={4}>
          <VStack spacing={4} align="stretch">
            {/* Search Results */}
            {searchResults.length > 0 && (
              <Box>
                <Text fontSize="sm" fontWeight="semibold" color={mutedColor} mb={3}>
                  Arama Sonuçları
                </Text>
                <VStack spacing={1} align="stretch">
                  {searchResults.map((result, index) => (
                    <Button
                      key={result.id}
                      variant="ghost"
                      justifyContent="flex-start"
                      h="auto"
                      p={3}
                      bg={selectedIndex === index ? selectedBg : 'transparent'}
                      _hover={{ bg: hoverBg }}
                      onClick={() => handleResultClick(result)}
                      borderRadius="lg"
                      minH={TOUCH_TARGETS.comfortable}
                    >
                      <HStack spacing={3} w="full">
                        <Icon as={getResultIcon(result.type)} color={mutedColor} />
                        <VStack align="start" spacing={0} flex={1}>
                          <HStack w="full" justify="space-between">
                            <Text fontWeight="medium" color={textColor}>
                              {result.title}
                            </Text>
                            {result.badge && (
                              <Badge colorScheme="blue" size="sm">
                                {result.badge}
                              </Badge>
                            )}
                          </HStack>
                          {result.description && (
                            <Text fontSize="sm" color={mutedColor}>
                              {result.description}
                            </Text>
                          )}
                        </VStack>
                        <Icon as={FiArrowRight} color={mutedColor} />
                      </HStack>
                    </Button>
                  ))}
                </VStack>
              </Box>
            )}

            {/* Recent Searches */}
            {recentSearches.length > 0 && !searchQuery && (
              <Box>
                <Flex justify="space-between" align="center" mb={3}>
                  <Text fontSize="sm" fontWeight="semibold" color={mutedColor}>
                    Son Aramalar
                  </Text>
                  <Button
                    size="xs"
                    variant="ghost"
                    onClick={clearRecentSearches}
                    color={mutedColor}
                  >
                    Temizle
                  </Button>
                </Flex>
                <VStack spacing={1} align="stretch">
                  {recentSearches.map((recent, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      justifyContent="flex-start"
                      h="auto"
                      p={3}
                      _hover={{ bg: hoverBg }}
                      onClick={() => handleRecentClick(recent)}
                      borderRadius="lg"
                      minH={TOUCH_TARGETS.comfortable}
                    >
                      <HStack spacing={3}>
                        <Icon as={FiClock} color={mutedColor} />
                        <Text color={textColor}>{recent}</Text>
                      </HStack>
                    </Button>
                  ))}
                </VStack>
              </Box>
            )}

            {/* Empty State */}
            {searchQuery && searchResults.length === 0 && !isSearching && (
              <Box textAlign="center" py={8}>
                <Icon as={FiSearch} boxSize={12} color={mutedColor} mb={4} />
                <Text color={textColor} fontWeight="medium" mb={2}>
                  Sonuç bulunamadı
                </Text>
                <Text color={mutedColor} fontSize="sm">
                  "{searchQuery}" için herhangi bir sonuç bulunamadı
                </Text>
              </Box>
            )}

            {/* Keyboard Shortcuts */}
            <Box pt={4} borderTopWidth="1px" borderColor={borderColor}>
              <Text fontSize="xs" color={mutedColor} mb={2}>
                Klavye Kısayolları:
              </Text>
              <HStack spacing={4} fontSize="xs" color={mutedColor}>
                <HStack>
                  <Kbd>↑↓</Kbd>
                  <Text>Gezin</Text>
                </HStack>
                <HStack>
                  <Kbd>Enter</Kbd>
                  <Text>Seç</Text>
                </HStack>
                <HStack>
                  <Kbd>Esc</Kbd>
                  <Text>Kapat</Text>
                </HStack>
              </HStack>
            </Box>
          </VStack>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );
};

export default MobileSearchDrawer;
