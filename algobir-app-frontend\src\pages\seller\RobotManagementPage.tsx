import { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  Flex,
  Heading,
  useDisclosure,
  Text,
  SimpleGrid,
  useToast,
  HStack,
  Input,
  IconButton,
  Card,
  CardBody,
  Divider,
  VStack,
  Badge,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  FormControl,
  Code,
  Image
} from '@chakra-ui/react';
import { AddIcon, CopyIcon } from '@chakra-ui/icons';
import { FaUsers } from 'react-icons/fa';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../supabaseClient';

import { RobotSetupWizard } from '../../components/seller/RobotSetupWizard';
import EmptyState from '../../components/EmptyState';
import LoadingState from '../../components/LoadingState';
import { useClipboard } from '@chakra-ui/react';
import { Robot } from '../../types/robot';
import RobotSubscribersModal from '../../components/seller/RobotSubscribersModal';

// Seller robotlarını yöneten sade custom hook
function useSellerRobotsData(userId: string | undefined) {
  const [robots, setRobots] = useState<Robot[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchRobots = useCallback(async () => {
    if (!userId) {
      console.log('Kullanıcı ID\'si olmadan robotlar yüklenemez');
      return;
    }
    
    console.log(`Kullanıcı ${userId} için robotlar yükleniyor...`);
    setIsLoading(true);
    
    try {
      // Silinmiş robotları filtrelemek için deleted_at alanının NULL olduğu robotları getiriyoruz
      const { data, error } = await supabase
        .from('robots')
        .select('*')
        .eq('seller_id', userId)
        .is('deleted_at', null) // deleted_at IS NULL olan robotları getir
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Robotları getirirken hata:', error);
        throw error;
      }
      
      console.log(`${data?.length || 0} robot başarıyla yüklendi`);
      setRobots(data || []);
    } catch (err: any) {
      console.error('Robotlar yüklenirken bir hata oluştu:', err.message || 'Robotlar yüklenirken bir hata oluştu.');
      setRobots([]);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchRobots();
  }, [fetchRobots]);

  return { robots, isLoading, refetch: fetchRobots };
}

const RobotManagementPage = () => {
  const { user } = useAuth();
  const toast = useToast();
  // Modal artık kullanılmıyor - wizard full page oldu
  const [isWizardMode, setIsWizardMode] = useState(false);
  const [editingRobot, setEditingRobot] = useState<Robot | null>(null);
  const { robots: myRobots, isLoading, refetch: refetchRobots } = useSellerRobotsData(user?.id);
  
  // Clipboard hook'larını en üste taşıyalım
  const [clipboardValue, setClipboardValue] = useState('');
  const { onCopy } = useClipboard(clipboardValue);
  
  // Aboneler modalı için state'ler
  const [selectedRobotId, setSelectedRobotId] = useState<string>('');
  const [selectedRobotName, setSelectedRobotName] = useState<string>('');
  const { isOpen: isSubscribersOpen, onOpen: onSubscribersOpen, onClose: onSubscribersClose } = useDisclosure();
  
  // Edit modal kodları kaldırıldı - artık wizard kullanıyoruz
  
  // Silme modalı için state'ler
  const [selectedRobotForDelete, setSelectedRobotForDelete] = useState<Robot | null>(null);
  const [deleteConfirmationText, setDeleteConfirmationText] = useState<string>('');
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  
  // Webhook Base URL
  const webhookBaseUrl = import.meta.env.VITE_WEBHOOK_BASE_URL || 'https://hook.algobir.com';

  // Webhook URL oluşturma fonksiyonu
  const getWebhookUrl = (robotId: string) => {
    return `${webhookBaseUrl}/seller-signal-endpoint/${robotId}`;
  };
  
  // Kopyalama işlemi için yardımcı fonksiyon
  const handleCopy = (text: string) => {
    setClipboardValue(text);
    onCopy();
    toast({
      title: "Kopyalandı",
      status: "success",
      duration: 2000,
      isClosable: true,
    });
  };

  // Yeni robot oluşturulduğunda listeyi yenile
  const handleRobotCreated = async () => {
    await refetchRobots();
    setEditingRobot(null);
    setIsWizardMode(false);
  };

  // Yeni robot ekleme handler'ı
  const handleAddNew = () => {
    setEditingRobot(null);
    setIsWizardMode(true);
  };

  // Robot düzenleme handler'ı
  const handleEditRobot = (robot: Robot) => {
    setEditingRobot(robot);
    setIsWizardMode(true);
  };
  
  // Abone yönetimi modalını açma fonksiyonu
  const openSubscribersModal = (robotId: string, robotName: string) => {
    // ID ve isim kontrolü
    if (!robotId) {
      console.error('[RobotManagementPage] Robot ID bulunamadı, abone modalı açılamıyor.');
      toast({
        title: "Hata",
        description: "Robot bilgisi eksik, aboneler listelenemedi.",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    console.log(`[RobotManagementPage] "${robotName || 'İsimsiz Robot'}" (ID: ${robotId}) için aboneler modalı açılıyor`);
    
    // State güncelleme
    setSelectedRobotId(robotId);
    setSelectedRobotName(robotName || 'İsimsiz Robot');
    
    // Modalı aç
    onSubscribersOpen();
  };

  // Enhanced delete function with improved error handling
  const handleDeleteRobot = async () => {
    if (!selectedRobotForDelete || !user) {
      console.error('No robot selected for deletion or no user authenticated');
      return;
    }
    
    setIsDeleting(true);
    
    try {
      console.log(`Attempting to delete robot: ${selectedRobotForDelete.id} (${selectedRobotForDelete.name})`);
      
      // Call the new enhanced RPC function (single parameter, exception-based)
      const { error: rpcError } = await supabase.rpc('soft_delete_robot', {
        p_robot_id: selectedRobotForDelete.id
      });
      
      // The new function throws exceptions, so if we get here without error, it succeeded
      if (rpcError) {
        console.error('RPC error from soft_delete_robot:', rpcError);
        
        // Parse specific error types for better user messages
        let userMessage = 'Robot silinirken bir hata oluştu.';
        
        if (rpcError.message?.includes('PERMISSION_DENIED')) {
          userMessage = 'Bu robotu silme yetkiniz yok. Sadece kendi robotlarınızı silebilirsiniz.';
        } else if (rpcError.message?.includes('ROBOT_NOT_FOUND')) {
          userMessage = 'Robot bulunamadı. Belki zaten silinmiş olabilir.';
        } else if (rpcError.message?.includes('ALREADY_DELETED')) {
          userMessage = 'Bu robot zaten silinmiş durumda.';
        } else if (rpcError.message?.includes('UPDATE_FAILED')) {
          userMessage = 'Robot güncellenirken bir sorun oluştu. Lütfen tekrar deneyin.';
        } else if (rpcError.message) {
          userMessage = `Hata: ${rpcError.message}`;
        }
        
        throw new Error(userMessage);
      }
      
      console.log(`Robot ${selectedRobotForDelete.id} successfully deleted`);
      
      toast({
        title: "Robot Silindi",
        description: `"${selectedRobotForDelete.name}" başarıyla silindi.`,
        status: "success",
        duration: 4000,
        isClosable: true,
      });
      
      // Refresh the robot list - critical step
      await refetchRobots();
      
      // Close modal and reset states
      onDeleteClose();
      setSelectedRobotForDelete(null);
      setDeleteConfirmationText('');
      
    } catch (error: any) {
      console.error('Failed to delete robot:', error);
      
      // Enhanced error handling with fallback message
      const errorMessage = error?.message || 'Robot silinirken beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.';
      
      toast({
        title: "Silme İşlemi Başarısız",
        description: errorMessage,
        status: "error",
        duration: 8000,
        isClosable: true,
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Silme modalını açma fonksiyonu
  const openDeleteModal = (robot: Robot) => {
    setSelectedRobotForDelete(robot);
    setDeleteConfirmationText('');
    onDeleteOpen();
  };

  // Wizard modunda ise wizard'ı göster
  if (isWizardMode) {
    return (
      <Box w="100%">
        <RobotSetupWizard
          editingRobot={editingRobot}
          onCancel={() => {
            setIsWizardMode(false);
            setEditingRobot(null);
          }}
          onSuccess={handleRobotCreated}
        />
      </Box>
    );
  }

  return (
    <Box w="100%">
      <Flex
        mb={5}
        direction={{ base: 'column', md: 'row' }}
        justifyContent="space-between"
        alignItems={{ base: 'flex-start', md: 'center' }}
      >
        <Heading size="lg" mb={{ base: 3, md: 0 }}>
          Robotlarım
        </Heading>
        <Button
          leftIcon={<AddIcon />}
          colorScheme="brand"
          onClick={handleAddNew}
          isDisabled={isLoading}
        >
          Yeni Robot Ekle
        </Button>
      </Flex>

      {isLoading ? (
        <LoadingState />
      ) : myRobots.length === 0 ? (
        <EmptyState
          title="Henüz robot eklemediniz"
          message="Yeni robot eklemek için 'Yeni Robot Ekle' butonuna tıklayın."
          actionLabel="Yeni Robot Ekle"
          onAction={handleAddNew}
        />
      ) : (
        <SimpleGrid
          columns={{ base: 1, lg: 1 }}
          spacing={6}
          width="100%"
          py={4}
        >
          {myRobots.map((robot) => {
            // Robot için Webhook URL oluştur
            const webhookUrl = getWebhookUrl(robot.id);
            
            // Strateji türü için görünen isim
            const strategyTypeDisplay = robot.strategy_type ? 
              robot.strategy_type
                .replace('trend_following', 'Trend Takibi')
                .replace('mean_reversion', 'Ortalamaya Dönüş')
                .replace('breakout', 'Kırılım')
                .replace('momentum', 'Momentum')
                .replace('swing_trading', 'Salınım Ticareti')
                .replace('scalping', 'Scalping')
                .replace('other', 'Diğer') : '';
            
            return (
              <Card key={robot.id} variant="outline" size="md" width="100%">
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <Image
                      src={robot.image_url || '/default-robot.png'}
                      alt={robot.name}
                      fallbackSrc="/default-robot.png"
                      onError={(e) => { e.currentTarget.src = '/default-robot.png'; }}
                      borderRadius="md"
                      boxSize="120px"
                      objectFit="cover"
                      alignSelf="center"
                    />
                    <Flex justifyContent="space-between" alignItems="center">
                      <Heading size="md">{robot.name}</Heading>
                      <HStack>
                        <Badge colorScheme="blue">v{robot.version || '1.0'}</Badge>
                        <Badge colorScheme={robot.is_public ? "green" : "purple"}>
                          {robot.is_public ? "Herkese Açık" : "Özel"}
                        </Badge>
                      </HStack>
                    </Flex>
                    
                    {robot.description && (
                      <Text color="gray.600" fontSize="sm">
                        {robot.description}
                      </Text>
                    )}

                    <Divider />
                    
                    <Box>
                      <Heading size="sm" mb={2}>Webhook Bilgileri</Heading>
                      
                      <VStack spacing={3} align="stretch">
                        <Box borderWidth="1px" borderRadius="md" p={4} bg="gray.50">
                          <Text fontSize="sm" mb={1} fontWeight="bold">Robot Webhook URL (Sinyal Gönderimi):</Text>
                          <HStack>
                            <Input
                              value={webhookUrl}
                              isReadOnly
                              size="sm"
                              fontFamily="mono"
                              bg="white"
                            />
                            <IconButton
                              aria-label="Webhook URL'yi kopyala"
                              icon={<CopyIcon />}
                              size="sm"
                              onClick={() => handleCopy(webhookUrl)}
                            />
                          </HStack>
                          <Text fontSize="xs" color="gray.600" mt={2}>
                            Robotunuzun sinyallerini bu URL'ye POST isteği ile iletebilirsiniz.
                          </Text>
                        </Box>

                        <Box borderWidth="1px" borderRadius="md" p={4} bg="gray.50">
                          <Text fontSize="sm" mb={1} fontWeight="bold">İstek Formatı (JSON):</Text>
                          <VStack spacing={3} align="stretch">
                            <Text>Aşağıdaki JSON formatları, TradingView alarmlarınızın 'Mesaj' kısmına yapıştırabileceğiniz örneklerdir:</Text>
                            {/* Alım kodu örneği */}
                            <Box>
                              <Heading size="sm" mb={2}>Örnek Alım Kodu (TradingView için):</Heading>
                              <Box p={4} borderWidth="1px" borderRadius="md" position="relative" bg="gray.100">
                                <Code display="block" whiteSpace="pre" p={2} overflowX="auto">
{`{
  "name": "Algobir Buy",
  "symbol": "{{ticker}}",
  "orderSide": "buy",
  "orderType": "mktbest",
  "price": "{{close}}",
  "quantity": "1",
  "timeInForce": "day"
}`}
                                </Code>
                                <Button size="sm" position="absolute" top="0.5rem" right="0.5rem" onClick={() => handleCopy(`{\n  \"name\": \"Algobir Buy\",\n  \"symbol\": \"{{ticker}}\",\n  \"orderSide\": \"buy\",\n  \"orderType\": \"mktbest\",\n  \"price\": \"{{close}}\",\n  \"quantity\": \"1\",\n  \"timeInForce\": \"day\"\n}`)}>
                                  {clipboardValue.includes('Algobir Buy') ? 'Kopyalandı!' : 'Kopyala'}
                                </Button>
                              </Box>
                            </Box>
                            {/* Satım kodu örneği */}
                            <Box>
                              <Heading size="sm" mb={2}>Örnek Satım Kodu (TradingView için):</Heading>
                              <Box p={4} borderWidth="1px" borderRadius="md" position="relative" bg="gray.100">
                                <Code display="block" whiteSpace="pre" p={2} overflowX="auto">
{`{
  "name": "Algobir TP",
  "symbol": "{{ticker}}",
  "orderSide": "sell",
  "orderType": "mktbest",
  "price": "{{close}}",
  "quantity": "1",
  "timeInForce": "day"
}`}
                                </Code>
                                <Button size="sm" position="absolute" top="0.5rem" right="0.5rem" onClick={() => handleCopy(`{\n  \"name\": \"Algobir TP\",\n  \"symbol\": \"{{ticker}}\",\n  \"orderSide\": \"sell\",\n  \"orderType\": \"mktbest\",\n  \"price\": \"{{close}}\",\n  \"quantity\": \"1\",\n  \"timeInForce\": \"day\"\n}`)}>
                                  {clipboardValue.includes('Algobir TP') ? 'Kopyalandı!' : 'Kopyala'}
                                </Button>
                              </Box>
                            </Box>
                          </VStack>
                          <Text fontSize="xs" mt={2} color="gray.600">
                            Gönderdiğiniz sinyaller, robotunuza abone olan kullanıcılara otomatik olarak iletilecektir.
                          </Text>
                        </Box>
                      </VStack>
                    </Box>
                    
                    <Divider />
                    
                    <Flex justifyContent="space-between" alignItems="center">
                      <HStack>
                        {robot.strategy_type && (
                          <Badge colorScheme="teal" variant="subtle">
                            {strategyTypeDisplay}
                          </Badge>
                        )}
                      </HStack>
                      
                      <HStack spacing={2}>
                        <Button
                          size="sm"
                          leftIcon={<FaUsers />}
                          colorScheme="blue"
                          variant="outline"
                          onClick={() => openSubscribersModal(robot.id, robot.name)}
                        >
                          Aboneleri Yönet
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          colorScheme="brand"
                          onClick={() => handleEditRobot(robot)}
                        >
                          Düzenle
                        </Button>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          colorScheme="red"
                          onClick={() => openDeleteModal(robot)}
                        >
                          Sil
                        </Button>
                      </HStack>
                    </Flex>
                  </VStack>
                </CardBody>
              </Card>
            );
          })}
        </SimpleGrid>
      )}

      {/* Modal artık kullanılmıyor - wizard full-page oldu */}
      
      {/* Robot Aboneleri Yönetim Modalı */}
      {isSubscribersOpen && (
        <RobotSubscribersModal
          isOpen={isSubscribersOpen}
          onClose={onSubscribersClose}
          robotId={selectedRobotId}
          robotName={selectedRobotName}
        />
      )}

      {/* Edit Robot Modal - artık wizard kullanılıyor */}

      {/* Delete Robot Modal */}
      <Modal isOpen={isDeleteOpen} onClose={onDeleteClose} size="md">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader color="red.500">Robot Silme Onayı</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            {selectedRobotForDelete && (
              <>
                <Text mb={4}>
                  <strong>"{selectedRobotForDelete.name}"</strong> isimli robotu silmek istediğinizden emin misiniz?
                </Text>
                <Text mb={4}>
                  Bu işlemi geri alamazsınız. Devam etmek için lütfen robotun adını aşağıdaki alana yazın:
                </Text>
                <FormControl>
                  <Input
                    placeholder="Robot adını yazın"
                    value={deleteConfirmationText}
                    onChange={(e) => setDeleteConfirmationText(e.target.value)}
                    onPaste={(e) => e.preventDefault()}
                    onDrop={(e) => e.preventDefault()}
                  />
                </FormControl>
              </>
            )}
          </ModalBody>
          <ModalFooter>
            <Button 
              colorScheme="red" 
              mr={3} 
              isDisabled={deleteConfirmationText !== (selectedRobotForDelete?.name || '')}
              onClick={handleDeleteRobot}
              isLoading={isDeleting}
              loadingText="Siliniyor"
            >
              Silmeyi Onayla
            </Button>
            <Button onClick={onDeleteClose}>İptal</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default RobotManagementPage; 