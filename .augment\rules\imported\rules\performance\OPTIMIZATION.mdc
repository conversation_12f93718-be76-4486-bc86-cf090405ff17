---
type: "manual"
priority: "critical"
scope: ["performance", "optimization", "speed", "memory", "caching", "monitoring", "query-optimization", "bundle-optimization"]
last_updated: "2025-01-30"
performance_enhancements: ["advanced-caching", "vite-optimization", "performance-monitoring", "query-optimization", "bundle-splitting"]
---

# Performance Optimization Rules (Enhanced)

## ⚡ Sub-second Order Transmission

### Critical Performance Requirements
```typescript
// Performance targets for trading application
interface PerformanceTargets {
  orderTransmissionTime: '<1000ms';     // Sub-second requirement
  signalProcessingTime: '<500ms';       // Signal to database
  webhookDeliveryTime: '<300ms';        // Database to broker
  uiResponseTime: '<200ms';             // User interface updates
  realTimeLatency: '<100ms';            // Real-time notifications
}

// Performance monitoring implementation
const performanceMetrics = {
  signalReceivedAt: new Date(),
  jsonParsingStartTime: performance.now(),
  jsonParsingEndTime: performance.now(),
  transformationStartTime: performance.now(),
  transformationEndTime: performance.now(),
  webhookDeliveryStartTime: performance.now(),
  webhookDeliveryEndTime: performance.now(),
  totalStartTime: performance.now()
};
```

### Asynchronous Processing Strategy
```typescript
// Non-blocking order processing
const processOrderAsync = async (trade: Trade) => {
  // 1. Immediately return success to TradingView
  const response = {
    success: true,
    trade_id: trade.id,
    processing_time_ms: Date.now() - startTime
  };
  
  // 2. Process order execution asynchronously
  setImmediate(async () => {
    try {
      await executeOrderWithBroker(trade);
      await updateTradeStatus(trade.id, 'executed');
      await sendSuccessNotification(trade);
    } catch (error) {
      await updateTradeStatus(trade.id, 'failed');
      await sendErrorNotification(trade, error);
    }
  });
  
  return response;
};

// Parallel processing for Bro-Robot subscribers
const processBroRobotSignalParallel = async (signal: Signal, subscribers: Subscriber[]) => {
  const processingPromises = subscribers.map(async (subscriber) => {
    try {
      const trade = await createTradeRecord(subscriber, signal);
      // Don't wait for broker execution
      executeOrderAsync(trade);
      return { success: true, subscriber_id: subscriber.id, trade_id: trade.id };
    } catch (error) {
      return { success: false, subscriber_id: subscriber.id, error: error.message };
    }
  });
  
  // Process all subscribers in parallel
  const results = await Promise.allSettled(processingPromises);
  return results;
};
```

## 🗄️ Database Optimization

### Connection Pooling
```typescript
// Supabase connection pool configuration
const supabaseConfig = {
  db: {
    pooler: {
      enabled: true,
      pool_mode: 'transaction',
      default_pool_size: 25,
      max_client_conn: 150
    }
  }
};

// Connection management best practices
const optimizedQuery = async (query: string, params: any[]) => {
  const startTime = performance.now();
  
  try {
    // Use prepared statements for better performance
    const { data, error } = await supabase.rpc(query, params);
    
    const queryTime = performance.now() - startTime;
    
    // Log slow queries for optimization
    if (queryTime > 100) {
      console.warn(`Slow query detected: ${query} took ${queryTime}ms`);
    }
    
    return { data, error };
  } catch (error) {
    console.error(`Query error: ${query}`, error);
    throw error;
  }
};
```

### Caching Strategy
```typescript
// Multi-level caching implementation
export class CacheManager {
  private static userSettingsCache = new Map<string, CacheEntry>();
  private static robotDataCache = new Map<string, CacheEntry>();
  private static statisticsCache = new Map<string, CacheEntry>();
  
  private static readonly CACHE_TTL = {
    userSettings: 5 * 60 * 1000,      // 5 minutes
    robotData: 10 * 60 * 1000,        // 10 minutes
    statistics: 2 * 60 * 1000,        // 2 minutes
    performanceMetrics: 30 * 1000     // 30 seconds
  };
  
  static async getUserSettings(userId: string): Promise<UserSettings | null> {
    const cacheKey = `user_settings_${userId}`;
    const cached = this.userSettingsCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL.userSettings) {
      return cached.data;
    }
    
    const { data } = await supabase
      .from('user_settings')
      .select('*')
      .eq('id', userId)
      .single();
    
    this.userSettingsCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    return data;
  }
  
  static invalidateUserCache(userId: string) {
    const cacheKey = `user_settings_${userId}`;
    this.userSettingsCache.delete(cacheKey);
  }
  
  // Periodic cache cleanup
  static startCacheCleanup() {
    setInterval(() => {
      const now = Date.now();
      
      // Clean expired user settings
      for (const [key, entry] of this.userSettingsCache.entries()) {
        if (now - entry.timestamp > this.CACHE_TTL.userSettings) {
          this.userSettingsCache.delete(key);
        }
      }
      
      // Clean expired robot data
      for (const [key, entry] of this.robotDataCache.entries()) {
        if (now - entry.timestamp > this.CACHE_TTL.robotData) {
          this.robotDataCache.delete(key);
        }
      }
    }, 60 * 1000); // Run every minute
  }
}
```

### Database Query Optimization
```sql
-- Optimized queries with proper indexing
-- Index for fast user lookup by webhook_id
CREATE INDEX CONCURRENTLY idx_user_settings_webhook_id 
ON user_settings (webhook_id) 
WHERE webhook_id IS NOT NULL;

-- Index for trade queries by user and date
CREATE INDEX CONCURRENTLY idx_trades_user_date 
ON trades (user_id, created_at DESC);

-- Index for robot performance queries
CREATE INDEX CONCURRENTLY idx_robots_performance 
ON robots USING GIN (performance_data);

-- Optimized RPC function for statistics
CREATE OR REPLACE FUNCTION get_user_statistics_optimized(
  p_user_id UUID,
  p_date_from TIMESTAMPTZ DEFAULT NULL,
  p_date_to TIMESTAMPTZ DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  -- Use materialized view for better performance
  SELECT json_build_object(
    'total_trades', COUNT(*),
    'total_profit', SUM(CASE WHEN order_side = 'SELL' THEN total_amount ELSE -total_amount END),
    'win_rate', (COUNT(*) FILTER (WHERE order_side = 'SELL' AND total_amount > 0) * 100.0 / NULLIF(COUNT(*), 0)),
    'avg_trade_size', AVG(total_amount)
  ) INTO result
  FROM trades 
  WHERE user_id = p_user_id
    AND (p_date_from IS NULL OR created_at >= p_date_from)
    AND (p_date_to IS NULL OR created_at <= p_date_to);
  
  RETURN result;
END;
$$ LANGUAGE plpgsql STABLE;
```

## 🧠 Memory Management

### Edge Functions Memory Optimization
```typescript
// Memory optimization for Deno Edge Functions
export class MemoryOptimizer {
  private static readonly MAX_MEMORY_USAGE = 128 * 1024 * 1024; // 128MB
  private static memoryCheckInterval: number | null = null;
  
  static startMemoryMonitoring() {
    this.memoryCheckInterval = setInterval(() => {
      const memoryUsage = Deno.memoryUsage();
      
      if (memoryUsage.heapUsed > this.MAX_MEMORY_USAGE * 0.8) {
        console.warn('High memory usage detected:', memoryUsage);
        this.forceGarbageCollection();
      }
    }, 10000); // Check every 10 seconds
  }
  
  static forceGarbageCollection() {
    if (typeof Deno !== 'undefined' && Deno.core?.ops?.op_gc) {
      Deno.core.ops.op_gc();
    }
  }
  
  static releasePerformanceMetrics(metrics: any) {
    // Clear all properties to help GC
    Object.keys(metrics).forEach(key => {
      delete metrics[key];
    });
  }
  
  static optimizeJsonStringify(obj: any): string {
    // Use faster JSON stringification without indentation
    return JSON.stringify(obj, null, 0);
  }
  
  static createOptimizedResponse(data: any, headers: Headers): Response {
    const jsonString = this.optimizeJsonStringify(data);
    
    // Set content length for better performance
    headers.set('Content-Length', new TextEncoder().encode(jsonString).length.toString());
    
    return new Response(jsonString, { headers });
  }
}
```

### Frontend Memory Management
```typescript
// React component memory optimization
const useOptimizedData = <T>(
  fetchFunction: () => Promise<T>,
  dependencies: any[],
  cacheKey: string
) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const cacheRef = useRef(new Map<string, { data: T; timestamp: number }>());
  
  const fetchData = useCallback(async () => {
    const cache = cacheRef.current;
    const cached = cache.get(cacheKey);
    
    // Use cached data if less than 5 minutes old
    if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
      setData(cached.data);
      setLoading(false);
      return;
    }
    
    setLoading(true);
    try {
      const result = await fetchFunction();
      setData(result);
      
      // Cache the result
      cache.set(cacheKey, { data: result, timestamp: Date.now() });
      
      // Limit cache size
      if (cache.size > 50) {
        const oldestKey = cache.keys().next().value;
        cache.delete(oldestKey);
      }
    } catch (error) {
      console.error('Data fetch error:', error);
    } finally {
      setLoading(false);
    }
  }, dependencies);
  
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cacheRef.current.clear();
    };
  }, []);
  
  return { data, loading, refetch: fetchData };
};

// Virtualized list for large datasets
const VirtualizedTradesList = ({ trades }: { trades: Trade[] }) => {
  const listRef = useRef<FixedSizeList>(null);
  
  const Row = memo(({ index, style }: { index: number; style: CSSProperties }) => {
    const trade = trades[index];
    
    return (
      <div style={style}>
        <TradeRow trade={trade} />
      </div>
    );
  });
  
  return (
    <FixedSizeList
      ref={listRef}
      height={600}
      itemCount={trades.length}
      itemSize={80}
      overscanCount={5}
    >
      {Row}
    </FixedSizeList>
  );
};
```

## 🚀 Frontend Build Optimization

### Vite Configuration
```typescript
// vite.config.ts - Production optimizations
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor splitting for better caching
          'vendor-charts': ['recharts', 'chart.js', 'react-chartjs-2'],
          'vendor-xlsx': ['xlsx'],
          'vendor-data': ['@tanstack/react-table', '@supabase/supabase-js'],
          'vendor-utils': ['date-fns', 'react-helmet-async', 'lodash'],
          'vendor-ui': ['@chakra-ui/react', '@emotion/react', '@emotion/styled'],
          'vendor-vercel': ['@vercel/analytics', '@vercel/speed-insights'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info'],
      },
    },
    sourcemap: false,
    assetsInlineLimit: 4096,
  },
  optimizeDeps: {
    include: [
      'react', 'react-dom', 'react/jsx-runtime',
      '@chakra-ui/react', '@chakra-ui/icons',
      '@emotion/react', '@emotion/styled',
      'framer-motion'
    ],
    exclude: ['@vercel/analytics', '@vercel/speed-insights']
  },
});
```

### Code Splitting & Lazy Loading
```typescript
// Route-based code splitting
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Statistics = lazy(() => import('./pages/Statistics'));
const Marketplace = lazy(() => import('./pages/Marketplace'));

// Component-level lazy loading
const LazyChart = lazy(() => import('./components/charts/PerformanceChart'));

// Preload critical routes
const preloadCommonRoutes = () => {
  import('./pages/Dashboard');
  import('./pages/Trades');
};

// Preload on user interaction
const handleMouseEnter = () => {
  import('./pages/Statistics');
};
```

## 📊 Real-time Performance Monitoring

### Performance Metrics Collection
```typescript
// Real-time performance monitoring
class PerformanceMonitor {
  private static metrics: PerformanceMetric[] = [];
  private static readonly MAX_METRICS = 1000;
  
  static recordMetric(type: string, duration: number, metadata?: any) {
    const metric: PerformanceMetric = {
      type,
      duration,
      timestamp: Date.now(),
      metadata
    };
    
    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }
    
    // Alert on performance issues
    if (duration > this.getThreshold(type)) {
      this.alertSlowOperation(metric);
    }
  }
  
  static getAverageTime(type: string, timeWindow: number = 5 * 60 * 1000): number {
    const cutoff = Date.now() - timeWindow;
    const recentMetrics = this.metrics.filter(m => 
      m.type === type && m.timestamp > cutoff
    );
    
    if (recentMetrics.length === 0) return 0;
    
    const total = recentMetrics.reduce((sum, m) => sum + m.duration, 0);
    return total / recentMetrics.length;
  }
  
  private static getThreshold(type: string): number {
    const thresholds = {
      'order_transmission': 1000,
      'signal_processing': 500,
      'database_query': 100,
      'ui_render': 16
    };
    
    return thresholds[type] || 1000;
  }
  
  private static alertSlowOperation(metric: PerformanceMetric) {
    console.warn(`Slow operation detected: ${metric.type} took ${metric.duration}ms`);
    
    // Send to monitoring service
    if (typeof window !== 'undefined') {
      // Client-side monitoring
      window.gtag?.('event', 'slow_operation', {
        operation_type: metric.type,
        duration: metric.duration
      });
    }
  }
}

// Usage in components and functions
const measurePerformance = async <T>(
  operation: () => Promise<T>,
  operationType: string
): Promise<T> => {
  const startTime = performance.now();
  
  try {
    const result = await operation();
    const duration = performance.now() - startTime;
    
    PerformanceMonitor.recordMetric(operationType, duration);
    
    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    PerformanceMonitor.recordMetric(operationType, duration, { error: true });
    throw error;
  }
};
```

## Update Requirements

### When to Update This File
- Performance bottlenecks identified
- New optimization techniques implemented
- Caching strategies modified
- Database query optimizations
- Memory management improvements
- Build configuration changes
- Monitoring system updates

## 🚀 Advanced Performance Enhancements (2025-01-30)

### Multi-Level Caching System
```typescript
// performanceCache.ts - Advanced caching with compression
class PerformanceCache {
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig = {
    maxSize: 50 * 1024 * 1024, // 50MB
    maxEntries: 1000,
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    compressionThreshold: 10 * 1024, // 10KB
    enableCompression: true
  };

  // LRU eviction with compression
  set<T>(key: string, data: T, ttl?: number): void {
    const size = this.calculateSize(data);

    // Evict if needed
    while (this.cache.size >= this.config.maxEntries ||
           this.stats.totalSize + size > this.config.maxSize) {
      this.evictLRU();
    }

    // Compress large entries
    let serializedData = JSON.stringify(data);
    let compressed = false;

    if (size > this.config.compressionThreshold) {
      const compressedData = this.compress(serializedData);
      if (compressedData.length < serializedData.length) {
        serializedData = compressedData;
        compressed = true;
      }
    }

    this.cache.set(key, {
      data: serializedData,
      timestamp: Date.now(),
      ttl: ttl || this.config.defaultTTL,
      compressed,
      size
    });
  }
}

// Global cache instances
export const globalCache = new PerformanceCache();
export const userCache = new PerformanceCache({ maxSize: 10 * 1024 * 1024 });
export const apiCache = new PerformanceCache({ defaultTTL: 2 * 60 * 1000 });
```

### Enhanced Vite Configuration
```typescript
// vite.config.ts - Advanced build optimization
export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production';

  return {
    build: {
      rollupOptions: {
        output: {
          // Advanced chunk splitting strategy
          manualChunks: (id) => {
            if (id.includes('react') || id.includes('react-dom')) return 'vendor-react';
            if (id.includes('@chakra-ui') || id.includes('@emotion')) return 'vendor-ui';
            if (id.includes('recharts')) return 'vendor-charts';
            if (id.includes('@supabase')) return 'vendor-supabase';
            if (id.includes('framer-motion')) return 'vendor-animation';
            if (id.includes('node_modules')) return 'vendor-misc';
          },

          // Optimized file naming
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const ext = assetInfo.name.split('.').pop();
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return 'images/[name]-[hash][extname]';
            }
            return 'assets/[name]-[hash][extname]';
          }
        },

        // Tree shaking optimization
        treeshake: {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          unknownGlobalSideEffects: false
        }
      },

      // Advanced minification
      minify: isProduction ? 'terser' : false,
      terserOptions: isProduction ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info'],
          passes: 2
        },
        mangle: { safari10: true }
      } : {},

      // Performance optimizations
      chunkSizeWarningLimit: 1000,
      cssCodeSplit: true,
      reportCompressedSize: true
    }
  };
});
```

### Performance Monitoring System
```typescript
// usePerformanceMonitor.ts - Real-time performance tracking
export const usePerformanceMonitor = (config = {}) => {
  const [stats, setStats] = useState<PerformanceStats>({
    pageLoadTime: 0,
    domContentLoaded: 0,
    resourceLoadTime: 0,
    webVitals: {},
    customMetrics: []
  });

  // Web Vitals measurement
  const measureWebVitals = useCallback(() => {
    // First Contentful Paint
    const fcpEntries = performance.getEntriesByName('first-contentful-paint');
    if (fcpEntries.length > 0) {
      setStats(prev => ({
        ...prev,
        webVitals: { ...prev.webVitals, FCP: fcpEntries[0].startTime }
      }));
    }

    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      setStats(prev => ({
        ...prev,
        webVitals: { ...prev.webVitals, LCP: lastEntry.startTime }
      }));
    });
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
  }, []);

  // Component render time measurement
  const measureRenderTime = useCallback((componentName: string) => {
    const startTime = performance.now();
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      recordMetric(`render_${componentName}`, renderTime);
    };
  }, []);

  return { stats, measureRenderTime, getPerformanceScore };
};
```

### Database Query Optimization
```typescript
// queryOptimizer.ts - Advanced query optimization
class QueryOptimizer {
  // Optimized select with caching and profiling
  async select<T>(table: string, columns = '*', filters?, config = {}) {
    const { cache = true, cacheTTL = 5 * 60 * 1000 } = config;
    const startTime = performance.now();

    // Generate cache key
    const cacheKey = cache ? `select_${table}_${JSON.stringify({ columns, filters })}` : null;

    // Check cache first
    if (cache && cacheKey) {
      const cached = globalCache.get<T[]>(cacheKey);
      if (cached) {
        return {
          data: cached,
          executionTime: performance.now() - startTime,
          fromCache: true
        };
      }
    }

    // Build optimized query
    let query = supabase.from(table).select(columns, { count: 'exact' });

    // Apply filters efficiently
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          query = query.in(key, value);
        } else if (typeof value === 'object' && value.operator) {
          switch (value.operator) {
            case 'gte': query = query.gte(key, value.value); break;
            case 'lte': query = query.lte(key, value.value); break;
            case 'like': query = query.like(key, value.value); break;
            default: query = query.eq(key, value.value);
          }
        } else {
          query = query.eq(key, value);
        }
      });
    }

    const { data, error } = await query;
    const executionTime = performance.now() - startTime;

    // Cache successful results
    if (!error && data && cache && cacheKey) {
      globalCache.set(cacheKey, data, cacheTTL);
    }

    // Log slow queries
    if (executionTime > 1000) {
      console.warn(`[QueryOptimizer] Slow query: ${table} (${executionTime}ms)`);
    }

    return { data, error, executionTime, fromCache: false };
  }
}
```

### Memory Management
```typescript
// Memory optimization utilities
export const useMemoryOptimization = () => {
  const [memoryUsage, setMemoryUsage] = useState(0);

  useEffect(() => {
    const measureMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usage = memory.usedJSHeapSize / (1024 * 1024); // MB
        setMemoryUsage(usage);

        // Alert on high memory usage
        if (usage > 100) {
          console.warn(`[Memory] High usage detected: ${usage.toFixed(2)}MB`);
        }
      }
    };

    const interval = setInterval(measureMemory, 30000);
    return () => clearInterval(interval);
  }, []);

  return { memoryUsage };
};

// Cleanup utilities
export const cleanupResources = () => {
  // Clear caches
  globalCache.cleanup();

  // Clear intervals and timeouts
  // (handled by individual components)

  // Force garbage collection if available
  if (window.gc) {
    window.gc();
  }
};
```

### Bundle Analysis & Optimization
```typescript
// Bundle size monitoring
const bundleAnalysis = {
  // Vendor chunks optimization
  'vendor-react': '~150KB',      // React core
  'vendor-ui': '~200KB',         // Chakra UI + Emotion
  'vendor-charts': '~180KB',     // Recharts
  'vendor-supabase': '~120KB',   // Supabase client
  'vendor-animation': '~80KB',   // Framer Motion
  'vendor-utils': '~60KB',       // Utilities

  // Main application chunks
  'main': '~100KB',              // Core app logic
  'dashboard': '~80KB',          // Dashboard specific
  'marketplace': '~70KB',        // Marketplace specific

  // Asset optimization
  images: 'WebP format, lazy loading',
  fonts: 'Preload critical fonts',
  css: 'Critical CSS inlined'
};
```

## 🚨 KRİTİK PERFORMANS REGRESYONU ÇÖZÜMÜ (2025-01-30)

### Problem Tanımı ve Çözüm Süreci
```yaml
Durum Analizi:
  Problem: "Algobir Unicorn Standards Upgrade projesi sonrası ciddi performans regresyonu"
  Semptomlar:
    - Uzun loading süreleri
    - Sürekli "Loading..." durumları
    - Uygulama kullanılamaz hale geldi
  Root Cause: "TypeScript compilation errors in performance optimization utilities"

Teknik Detaylar:
  queryOptimizer.ts: 56 TypeScript hatası
  monitoring.ts: 5 TypeScript hatası
  useMonitoring.ts: 1 TypeScript hatası
  Build Process: Tamamlanamıyor (compilation errors)

Çözüm Stratejisi:
  1. queryOptimizer.ts tamamen yeniden yazıldı (simplified, type-safe)
  2. monitoring.ts TypeScript hataları düzeltildi
  3. useMonitoring.ts unused interfaces kaldırıldı
  4. Build process restore edildi
```

### Performans İyileştirme Sonuçları
```typescript
// ÖNCE (Broken State)
Build Time: ❌ Tamamlanamıyor (TypeScript errors)
Dev Server: ❌ Extended loading times
User Experience: ❌ Unusable application
TypeScript Errors: 62 total errors

// SONRA (Fixed State)
Build Time: ✅ Başarılı completion
Dev Server: ✅ 298ms startup time ⚡
User Experience: ✅ Responsive ve hızlı
TypeScript Errors: 0 errors
```

### Simplified QueryOptimizer Implementation
```typescript
// algobir-app-frontend/src/utils/queryOptimizer.ts
export class QueryOptimizer {
  private static cacheHitRate = new Map<string, { hits: number; misses: number }>();

  static async optimizedSelect<T = any>(
    table: string,
    config: {
      columns?: string;
      filters?: Record<string, any>;
      orderBy?: { column: string; ascending?: boolean }[];
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<QueryResult<T[]>> {
    const startTime = performance.now();
    const queryId = `${table}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Build basic query (type-safe implementation)
      let query = supabase.from(table).select(config.columns || '*', { count: 'exact' });

      // Apply basic filters
      Object.entries(config.filters || {}).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });

      // Apply ordering and pagination
      config.orderBy?.forEach(({ column, ascending = false }) => {
        query = query.order(column, { ascending });
      });

      if (config.limit !== undefined) {
        const from = config.offset || 0;
        const to = from + config.limit - 1;
        query = query.range(from, to);
      }

      const result = await query;
      const executionTime = performance.now() - startTime;

      return {
        data: result.data as T[] | null,
        error: result.error,
        count: result.count || undefined,
        executionTime,
        fromCache: false,
        queryId
      };
    } catch (error) {
      const executionTime = performance.now() - startTime;
      return {
        data: null,
        error,
        executionTime,
        fromCache: false,
        queryId
      };
    }
  }
}
```

### Monitoring.ts TypeScript Fixes
```typescript
// Fixed PerformanceEntry property access issues
value: entry.duration || (entry as any).value || 0,
value: (entry as any).processingStart - entry.startTime,

// Added explicit return type annotations
private async sendMetrics(metrics: PerformanceMetric[], retryCount = 0): Promise<void>
private async sendErrors(errors: ErrorReport[], retryCount = 0): Promise<void>
private async sendMetrics(metrics: BusinessMetric[], retryCount = 0): Promise<void>
```

### Öğrenilen Kritik Dersler
```yaml
Teknik Dersler:
  - "Complexity vs Stability": Aşırı karmaşık optimizasyonlar stability'yi tehlikeye atar
  - "TypeScript Compliance": Tüm utilities TypeScript strict mode'a uygun olmalı
  - "Incremental Implementation": Büyük optimizasyonlar aşamalı implement edilmeli
  - "Performance Monitoring": Regression detection için sürekli monitoring şart

Süreç Dersleri:
  - "Build Process Validation": Her optimization sonrası build test edilmeli
  - "Error Handling": TypeScript errors performance'ı tamamen bozabilir
  - "Rollback Strategy": Kritik sistemlerde rollback planı hazır olmalı
  - "Documentation": Tüm değişiklikler dokümante edilmeli
```

### Gelecek Optimizasyon Planı
```typescript
// Planlanan İyileştirmeler (Type-Safe Implementation)
interface FutureOptimizations {
  advancedQueryOptimization: {
    implementation: 'Type-safe advanced query builder';
    timeline: 'Q1 2025';
    riskLevel: 'Low (incremental)';
  };
  enhancedCaching: {
    implementation: 'Multi-level caching with compression';
    timeline: 'Q1 2025';
    riskLevel: 'Medium';
  };
  bundleOptimization: {
    implementation: 'Advanced chunk splitting';
    timeline: 'Q2 2025';
    riskLevel: 'Low';
  };
  memoryManagement: {
    implementation: 'Automatic memory cleanup';
    timeline: 'Q2 2025';
    riskLevel: 'Medium';
  };
}
```

### When to Update This File
- Performance bottlenecks identified
- New optimization techniques implemented
- Caching strategies modified
- Database query optimizations
- Memory management improvements
- Build configuration changes
- Monitoring system updates
- Web Vitals improvements
- Bundle size optimizations
- Memory leak fixes
- **Performance regression incidents and solutions**
- **TypeScript compilation performance issues**
- **Build process optimization results**

### Related Files to Update
- Update `backend/SUPABASE_EDGE.mdc` for database optimizations
- Update `frontend/MAIN_APP.mdc` for UI performance changes
- Update `features/TRADING_SYSTEM.mdc` for trading performance
- Update `core/PROJECT_CORE.mdc` for architecture performance changes
- Update `deployment/PRODUCTION.mdc` for production optimizations
- Update `security/GUIDELINES.mdc` for performance security impact
