import React from 'react';
import {
  Box,
  VStack,
  Heading,
  Text,
  HStack,
  Icon,
  Code,
  Card,
  CardBody,
  CardHeader,
  Button,
  useClipboard,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';
import { FaShoppingCart, FaCopy, FaCheck } from 'react-icons/fa';
import { RobotFormData } from '../RobotSetupWizard';

export const Step4BuyJson: React.FC = () => {
  const { getValues } = useFormContext<RobotFormData>();
  const robotName = getValues('name') || 'Robot';
  
  const buyJsonString = JSON.stringify({
    name: `${robotName} Buy`,
    symbol: "{{ticker}}",
    orderSide: "buy",
    orderType: "mktbest",
    price: "{{close}}",
    quantity: "1",
    timeInForce: "day"
  }, null, 2);

  const { onCopy, hasCopied } = useClipboard(buyJsonString);

  return (
    <Box>
      <VStack spacing={6} align="stretch">
        <Box textAlign="center" mb={4}>
          <HStack justify="center" mb={3}>
            <Icon as={FaShoppingCart} boxSize={8} color="green.500" />
          </HStack>
          <Heading size="lg" mb={2}>Alım Sinyali JSON</Heading>
          <Text color="gray.600">
            TradingView alarmlarınızda kullanacağınız alım sinyali JSON formatı hazırlandı.
          </Text>
        </Box>

        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <AlertDescription>
            Bu JSON kodunu TradingView alarm ayarlarınızın "Mesaj" bölümüne kopyalayın. 
            Robot alım sinyali gönderdiğinde bu format kullanılacaktır.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <HStack justify="space-between">
              <Heading size="md">Alım Sinyali Formatı</Heading>
              <Button
                leftIcon={hasCopied ? <FaCheck /> : <FaCopy />}
                colorScheme={hasCopied ? "green" : "brand"}
                variant="outline"
                onClick={onCopy}
                size="sm"
              >
                {hasCopied ? "Kopyalandı!" : "Kopyala"}
              </Button>
            </HStack>
          </CardHeader>
          <CardBody>
            <Box
              p={4}
              bg="gray.50"
              borderRadius="md"
              border="1px solid"
              borderColor="gray.200"
            >
              <Code 
                display="block" 
                whiteSpace="pre" 
                fontFamily="mono"
                fontSize="sm"
                bg="transparent"
                p={0}
              >
                {buyJsonString}
              </Code>
            </Box>
          </CardBody>
        </Card>

        <VStack spacing={3} align="stretch">
          <Heading size="sm">JSON Alanları Açıklaması:</Heading>
          <Box p={4} bg="blue.50" borderRadius="md">
            <VStack spacing={2} align="stretch">
              <HStack>
                <Text fontWeight="bold" color="blue.600">name:</Text>
                <Text fontSize="sm">Sinyalin adı (Robot adınız + "Buy")</Text>
              </HStack>
                             <HStack>
                 <Text fontWeight="bold" color="blue.600">symbol:</Text>
                 <Text fontSize="sm">TradingView'dan gelen sembol ({"{{"+ "ticker" + "}}"})  </Text>
               </HStack>
              <HStack>
                <Text fontWeight="bold" color="blue.600">orderSide:</Text>
                <Text fontSize="sm">İşlem yönü (alım için "buy")</Text>
              </HStack>
              <HStack>
                <Text fontWeight="bold" color="blue.600">orderType:</Text>
                <Text fontSize="sm">Emir tipi (piyasa emri için "mktbest")</Text>
              </HStack>
                             <HStack>
                 <Text fontWeight="bold" color="blue.600">price:</Text>
                 <Text fontSize="sm">TradingView'dan gelen kapanış fiyatı ({"{{"+ "close" + "}}"})  </Text>
               </HStack>
              <HStack>
                <Text fontWeight="bold" color="blue.600">quantity:</Text>
                <Text fontSize="sm">İşlem miktarı</Text>
              </HStack>
              <HStack>
                <Text fontWeight="bold" color="blue.600">timeInForce:</Text>
                <Text fontSize="sm">Emrin geçerlilik süresi ("day" = gün sonuna kadar)</Text>
              </HStack>
            </VStack>
          </Box>
        </VStack>
      </VStack>
    </Box>
  );
}; 