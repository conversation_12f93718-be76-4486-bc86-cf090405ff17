import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Text,
  Heading,
  SimpleGrid,
  Card,
  CardBody,
  Button,
  Spinner,
  Alert,
  AlertIcon,
  InputGroup,
  InputLeftElement,
  Input,
  Icon,
  useColorModeValue,
  HStack,
  VStack,
  Avatar,
} from '@chakra-ui/react';
import { Link as ReactRouterLink, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { FiSearch, FiTrendingUp, FiClock, FiStar, FiArrowRight, FiBarChart, FiBox, FiUsers, FiActivity, FiAward } from 'react-icons/fi';
import RobotCard from '../../components/marketplace/RobotCard';
import { supabase } from '../../supabaseClient';
import { useAuth } from '../../context/AuthContext';
import { useMarketplaceData } from '../../hooks/useMarketplaceData';
import { usePlatformStatistics } from '../../hooks/usePlatformStatistics';
import { useToastHelper } from '../../components/ToastHelper';

// Arayüz için top seller tipi tanımlama
interface TopSeller {
  id: string;
  username: string;
  avatar_url: string | null;
  url_slug: string;
  total_subscriber_count: number;
}

// StatCard component
interface StatCardProps {
  label: string;
  value: string | number;
  icon: React.ElementType;
  color: string;
  role?: string;
  'aria-label'?: string;
}

const StatCard: React.FC<StatCardProps> = ({ label, value, icon, color, ...props }) => {
  const textColor = useColorModeValue('gray.900', 'white');
  const secondaryText = useColorModeValue('gray.600', 'gray.400');

  return (
    <Box
      p={4}
      bg={useColorModeValue('white', 'gray.800')}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={useColorModeValue('gray.200', 'gray.700')}
      transition="all 0.2s"
      _hover={{ transform: 'translateY(-2px)', shadow: 'md' }}
      {...props}
    >
      <HStack spacing={3}>
        <Icon as={icon} color={color} boxSize={6} />
        <Box>
          <Text fontSize="sm" color={secondaryText} fontWeight="medium">
            {label}
          </Text>
          <Text fontSize="xl" fontWeight="bold" color={textColor}>
            {value}
          </Text>
        </Box>
      </HStack>
    </Box>
  );
};

// SellerCard component
interface SellerCardProps {
  seller: TopSeller;
  rank: number;
  tabIndex?: number;
  role?: string;
  'aria-label'?: string;
}

const SellerCard: React.FC<SellerCardProps> = ({ seller, rank, ...props }) => {
  const textColor = useColorModeValue('gray.900', 'white');
  const secondaryText = useColorModeValue('gray.600', 'gray.400');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  return (
    <HStack 
      spacing={3} 
      p={3} 
      borderRadius="lg" 
      _hover={{ bg: hoverBg }} 
      transition="all 0.2s"
      w="100%"
      {...props}
    >
      <Text 
        fontWeight="bold" 
        fontSize="sm" 
        w="24px" 
        color="brand.500"
      >
        {rank}.
      </Text>
      <Avatar 
        size="sm"
        src={seller.avatar_url || undefined}
        name={seller.username}
        bg="brand.500"
        color="white"
      />
      <Box flex="1" minW="0">
        <Text 
          fontWeight="semibold" 
          fontSize="sm" 
          color={textColor} 
          noOfLines={1}
        >
          {seller.username}
        </Text>
        <Text 
          fontSize="xs" 
          color={secondaryText}
        >
          {seller.total_subscriber_count} Abone
        </Text>
      </Box>
      <Button 
        as={ReactRouterLink} 
        to={`/user/${seller.url_slug}`} 
        size="xs"
        variant="outline" 
        colorScheme="brand"
      >
        İncele
      </Button>
    </HStack>
  );
};

const MarketplacePage = () => {
  // Hook'ları component'in en başında tanımla
  const { user } = useAuth();
  const toast = useToastHelper();
  const navigate = useNavigate();
  
  // Tema renkleri - tüm useColorModeValue hook'larını burada tanımla
  const textColor = useColorModeValue('navy.700', 'white');
  const inputBg = useColorModeValue('secondaryGray.300', 'navy.900');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');

  const cardBg = useColorModeValue('white', 'gray.800');
  
  // Arama state'i
  const [searchTerm, setSearchTerm] = useState<string>('');
  
  // Top satıcılar state'i
  const [topSellers, setTopSellers] = useState<TopSeller[]>([]);
  
  // Marketplace data hook'u - userSubscriptions çakışmasını önlemek için destructure'da başka isim kullan
  const {
    trendingRobots,
    recentRobots,
    userSubscriptions: hookUserSubscriptions,
    isLoading,
    error,
    refetch
  } = useMarketplaceData({ searchTerm });

  // Platform statistics hook'u
  const {
    activeUsersCount,
    totalTransactionsCount,
    isLoading: statsLoading,
    // Error: statsError
  } = usePlatformStatistics();

  // Helper function to format numbers with commas
  const formatNumber = (num: number): string => {
    return num.toLocaleString('tr-TR');
  };

  // Top Sellers verisini çekme
  useEffect(() => {
    const fetchTopSellers = async () => {
      try {
        const { data, error } = await supabase
          .from('v_top_sellers_with_subscriber_counts')
          .select('*')
          .order('total_subscriber_count', { ascending: false })
          .limit(5);

        if (error) {
          console.error('[MarketplacePage] Top sellers fetch error:', error);
          setTopSellers([]);
          return;
        }

        if (data) {
          // Gelen verileri işleyerek eksik verileri tamamla
          const processedSellers = data.map((seller, index) => ({
            ...seller,
            id: seller.id || `index-${index}`, // ID yoksa indeks kullan
            username: seller.username || `Yapımcı ${index + 1}`, // Username yoksa default değer
            url_slug: seller.url_slug || seller.username || `yapimci-${index + 1}`, // URL slug yoksa username veya default
            total_subscriber_count: seller.total_subscriber_count || 0 // Count yoksa 0
          }));
          setTopSellers(processedSellers);
        } else {
          setTopSellers([]);
        }
      } catch (err) {
        console.error('[MarketplacePage] Unexpected error fetching top sellers:', err);
        setTopSellers([]); // Hata durumunda boş array
      }
    };

    fetchTopSellers();
  }, []);

  // Abone olma fonksiyonu
  const handleSubscribe = async (robotId: string) => {
    if (!user?.id) {
      toast.showWarningToast('Giriş Gerekli', 'Abone olmak için giriş yapmalısınız.');
      return;
    }
    
    // Kullanıcının zaten bu robota abone olup olmadığını kontrol et
    if (hookUserSubscriptions.includes(robotId)) {
      console.log(`[MarketplacePage] User ${user.id} is already subscribed to robot ${robotId}.`);
      toast.showInfoToast('Bilgi', 'Bu robota zaten abonesiniz.');
      return; // INSERT işlemi yapma
    }
    
    console.log(`[MarketplacePage] Subscribing user ${user.id} to robot ${robotId}...`);
    
    try {
      // Veritabanında aktif abonelik kontrolü yap - single() yerine limit(1) kullan
      const { data: existingSubscriptions, error: checkError } = await supabase
        .from('subscriptions')
        .select('id, is_active, is_deleted')
        .eq('user_id', user.id)
        .eq('robot_id', robotId)
        .limit(1);
      
      if (checkError) {
        console.error('[MarketplacePage] Abonelik kontrolü hatası:', checkError);
        toast.showErrorToast('Hata', 'Abonelik durumu kontrol edilemedi. Lütfen tekrar deneyin.');
        return;
      }
      
      const existingSubscription = existingSubscriptions?.[0];
      
      // Eğer aktif bir abonelik varsa, kullanıcıya bilgi ver ve işlemi durdur
      if (existingSubscription && existingSubscription.is_active === true && existingSubscription.is_deleted === false) {
        console.log(`[MarketplacePage] User ${user.id} already has an active subscription to robot ${robotId}.`);
        toast.showInfoToast('Bilgi', 'Bu robota zaten abonesiniz.');
        return;
      }
      
      // Eğer silinmiş bir abonelik varsa, onu güncelle
      if (existingSubscription && existingSubscription.is_deleted === true) {
        const { error: updateError } = await supabase
          .from('subscriptions')
          .update({
            is_active: true,
            is_deleted: false,
            started_at: new Date().toISOString()
          })
          .eq('id', existingSubscription.id);
          
        if (updateError) {
          console.error(`[MarketplacePage] Abonelik güncelleme hatası: ID=${robotId}`, updateError);
          toast.showErrorToast('Hata', `Abonelik güncellenemedi: ${updateError.message || JSON.stringify(updateError)}`);
          return;
        }
        
        console.log(`[MarketplacePage] Successfully reactivated subscription for user ${user.id} to robot ${robotId}.`);
        refetch();
        toast.showSuccessToast('Başarılı', 'Robota aboneliğiniz yeniden aktifleştirildi.');
        return;
      }
      
      // Yeni abonelik oluştur
      const { error } = await supabase
        .from('subscriptions')
        .insert({
          user_id: user.id,
          robot_id: robotId,
          is_active: true,
          started_at: new Date().toISOString(),
          is_deleted: false
        });
        
      if (error) {
        console.error(`[MarketplacePage] Abone olma hatası: ID=${robotId}`, error);
        toast.showErrorToast('Hata', `Abone olunamadı: ${error.message || JSON.stringify(error)}`);
      } else {
        console.log(`[MarketplacePage] Successfully subscribed user ${user.id} to robot ${robotId}.`);
        
        // Verileri yeniden çek
        refetch();
        
        toast.showSuccessToast('Başarılı', 'Robota abone oldunuz.');
      }
    } catch (error) {
      console.error('[MarketplacePage] Beklenmeyen abone olma hatası:', error);
      toast.showErrorToast('Hata', 'Abonelik işlemi sırasında bir sorun oluştu.');
    }
  };

  // RPC ile bir abonelikten çıkma
  const handleUnsubscribe = async (robotId: string) => {
    if (!user?.id) {
      toast.showWarningToast('Giriş Gerekli', 'Bu işlem için giriş yapmalısınız.');
      return;
    }
    
    try {
      // Önce kullanıcının bu robot için aboneliğini bul
      const { data: subscriptions, error: findError } = await supabase
        .from('subscriptions')
        .select('id')
        .eq('user_id', user.id)
        .eq('robot_id', robotId)
        .eq('is_active', true)
        .eq('is_deleted', false)
        .limit(1);

      if (findError) {
        console.error(`[MarketplacePage] Abonelik bulma hatası: ID=${robotId}`, findError);
        toast.showErrorToast('Hata', 'Abonelik bulunamadı.');
        return;
      }

      if (!subscriptions || subscriptions.length === 0) {
        toast.showErrorToast('Hata', 'Aktif abonelik bulunamadı.');
        return;
      }

      // Abonelikten çık
      const { error } = await supabase.rpc('soft_delete_subscription', {
        p_subscription_id_to_delete: subscriptions[0].id,
        p_user_id: user.id
      });
      
      if (error) {
        console.error(`[MarketplacePage] Abonelikten çıkma hatası: ID=${robotId}`, error);
        toast.showErrorToast('Hata', `Abonelikten çıkılamadı: ${error.message || JSON.stringify(error)}`);
      } else {
        // Verileri yeniden çek
        refetch();
        toast.showSuccessToast('Başarılı', 'Abonelikten çıktınız.');
      }
    } catch (error) {
      console.error('[MarketplacePage] Beklenmeyen abonelikten çıkma hatası:', error);
      toast.showErrorToast('Hata', 'Abonelikten çıkma işlemi sırasında bir sorun oluştu.');
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Yükleniyor ise
  if (isLoading && !trendingRobots.length && !recentRobots.length) {
    return (
      <Box>
        <Flex justify="center" align="center" direction="column" minH="60vh">
          <Spinner size="xl" color="brand.500" thickness="4px" speed="0.65s" />
          <Text mt={4} fontWeight="500" color={textColor}>Robotlar yükleniyor...</Text>
        </Flex>
      </Box>
    );
  }

  // Hata oluştu ise
  if (error && !trendingRobots.length && !recentRobots.length) {
    return (
      <Box>
        <Alert status="error" borderRadius="20px" mb={4}>
          <AlertIcon />
          <Box>
            <Text fontWeight="bold">Robotlar yüklenirken bir hata oluştu</Text>
            <Text fontSize="sm">{error.message || 'Bilinmeyen bir hata oluştu'}</Text>
          </Box>
        </Alert>
        <Button onClick={() => refetch()} variant="brand" size="md">
          Tekrar Dene
        </Button>
      </Box>
    );
  }

  // Normal render
  return (
    <Box
      px={{ base: '16px', md: '24px', lg: '30px' }}
      pb={{ base: '20px', md: '30px' }}
    >
      <Helmet>
        <title>Algobir - Robot Pazar Yeri</title>
      </Helmet>
      
      {/* Enhanced Banner Card */}
      <Card
        mb={{ base: 6, md: 8 }}
        variant="elevated"
        bg="transparent"
        borderRadius="20px"
        overflow="hidden"
        position="relative"
        boxShadow="0 20px 40px rgba(0, 0, 0, 0.1)"
        _before={{
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          bgImage: "linear-gradient(135deg, rgba(66, 42, 251, 0.95) 0%, rgba(117, 81, 255, 0.9) 50%, rgba(14, 165, 233, 0.85) 100%), url('https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80')",
          bgSize: "cover",
          bgPosition: "center",
          zIndex: 0
        }}
        _after={{
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          bg: "linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)",
          backdropFilter: "blur(1px)",
          zIndex: 1
        }}
      >
        <CardBody
          p={{ base: 6, sm: 8, md: 12 }}
          position="relative"
          zIndex={2}
        >
          <Flex
            direction={{ base: 'column', lg: 'row' }}
            align="center"
            textAlign={{ base: 'center', lg: 'left' }}
            gap={{ base: 8, lg: 12 }}
            minH={{ base: 'auto', lg: '300px' }}
          >
            {/* Left Content */}
            <Box flex="1.2" color="white" maxW={{ base: '100%', lg: '600px' }}>
              {/* Badge */}
              <Box
                display="inline-flex"
                bg="rgba(255, 255, 255, 0.2)"
                backdropFilter="blur(10px)"
                borderRadius="full"
                px={4}
                py={2}
                mb={4}
                border="1px solid rgba(255, 255, 255, 0.3)"
              >
                <Text fontSize="sm" fontWeight="600" color="white">
                  🚀 Türkiye'nin En Büyük Robot Pazarı
                </Text>
              </Box>

              <Heading
                as="h1"
                size={{ base: 'xl', sm: '2xl', md: '3xl' }}
                fontWeight="800"
                mb={{ base: 4, md: 6 }}
                letterSpacing="-0.02em"
                fontSize={{ base: '28px', sm: '36px', md: '48px', lg: '52px' }}
                lineHeight="1.1"
                bgGradient="linear(to-r, white, rgba(255, 255, 255, 0.9))"
                bgClip="text"
              >
                Algoritmik Ticaret
                <br />
                <Text as="span" color="rgba(255, 255, 255, 0.9)">
                  Geleceği
                </Text>
              </Heading>

              <Text
                fontSize={{ base: 'lg', md: 'xl' }}
                opacity={0.95}
                mb={{ base: 6, md: 8 }}
                maxW="550px"
                lineHeight="1.7"
                fontWeight="400"
              >
                Profesyonel algoritma geliştiricilerinin oluşturduğu kanıtlanmış stratejilerle
                <Text as="span" fontWeight="600" color="rgba(255, 255, 255, 1)">
                  {" "}yatırım getirilerinizi maksimize edin
                </Text>
                . Güvenli, şeffaf ve sürekli karlı ticaret deneyimi.
              </Text>

              {/* Feature Points */}
              <VStack
                spacing={3}
                align={{ base: 'center', lg: 'flex-start' }}
                mb={{ base: 6, md: 8 }}
                display={{ base: 'none', md: 'flex' }}
              >
                <HStack spacing={3}>
                  <Box
                    w="6"
                    h="6"
                    bg="rgba(34, 197, 94, 0.9)"
                    borderRadius="full"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <Text fontSize="xs" color="white">✓</Text>
                  </Box>
                  <Text fontSize="md" color="rgba(255, 255, 255, 0.95)">
                    7/24 Otomatik Ticaret - İnsan Müdahalesi Gerektirmez
                  </Text>
                </HStack>
                <HStack spacing={3}>
                  <Box
                    w="6"
                    h="6"
                    bg="rgba(34, 197, 94, 0.9)"
                    borderRadius="full"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <Text fontSize="xs" color="white">✓</Text>
                  </Box>
                  <Text fontSize="md" color="rgba(255, 255, 255, 0.95)">
                    Kanıtlanmış Performans - Gerçek Zamanlı Sonuçlar
                  </Text>
                </HStack>
                <HStack spacing={3}>
                  <Box
                    w="6"
                    h="6"
                    bg="rgba(34, 197, 94, 0.9)"
                    borderRadius="full"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                  >
                    <Text fontSize="xs" color="white">✓</Text>
                  </Box>
                  <Text fontSize="md" color="rgba(255, 255, 255, 0.95)">
                    Risk Yönetimi - Güvenli Yatırım Stratejileri
                  </Text>
                </HStack>
              </VStack>

              {/* Action Buttons */}
              <HStack
                spacing={{ base: 3, md: 4 }}
                justify={{ base: 'center', lg: 'flex-start' }}
                wrap="wrap"
                gap={3}
              >
                <Button
                  as={ReactRouterLink}
                  to="/guide"
                  size={{ base: 'lg', md: 'xl' }}
                  h={{ base: '48px', md: '56px' }}
                  px={{ base: 6, md: 8 }}
                  bg="white"
                  color="brand.600"
                  fontWeight="700"
                  fontSize={{ base: 'md', md: 'lg' }}
                  borderRadius="16px"
                  leftIcon={<Icon as={FiStar} boxSize={5} />}
                  _hover={{
                    transform: 'translateY(-2px)',
                    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
                    bg: 'gray.50'
                  }}
                  transition="all 0.3s ease"
                  minW={{ base: '140px', md: '160px' }}
                >
                  Hemen Başla
                </Button>
                <Button
                  as={ReactRouterLink}
                  to="/guide"
                  size={{ base: 'lg', md: 'xl' }}
                  h={{ base: '48px', md: '56px' }}
                  px={{ base: 6, md: 8 }}
                  variant="outline"
                  borderColor="rgba(255, 255, 255, 0.4)"
                  color="white"
                  fontWeight="600"
                  fontSize={{ base: 'md', md: 'lg' }}
                  borderRadius="16px"
                  leftIcon={<Icon as={FiArrowRight} boxSize={5} />}
                  _hover={{
                    bg: 'rgba(255, 255, 255, 0.1)',
                    borderColor: 'rgba(255, 255, 255, 0.6)',
                    transform: 'translateY(-2px)'
                  }}
                  transition="all 0.3s ease"
                  minW={{ base: '140px', md: '160px' }}
                >
                  Nasıl Çalışır?
                </Button>
              </HStack>
            </Box>

            {/* Right Visual Element */}
            <Box
              flex="0.8"
              display={{ base: 'none', lg: 'flex' }}
              justifyContent="center"
              alignItems="center"
              position="relative"
            >
              <Box
                position="relative"
                w="320px"
                h="240px"
                bg="rgba(255, 255, 255, 0.1)"
                backdropFilter="blur(20px)"
                borderRadius="24px"
                border="1px solid rgba(255, 255, 255, 0.2)"
                display="flex"
                alignItems="center"
                justifyContent="center"
                _before={{
                  content: '""',
                  position: 'absolute',
                  top: '-20px',
                  left: '-20px',
                  right: '-20px',
                  bottom: '-20px',
                  bg: 'linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05))',
                  borderRadius: '32px',
                  zIndex: -1,
                  filter: 'blur(20px)'
                }}
              >
                <VStack spacing={4} color="white" textAlign="center">
                  <Icon as={FiActivity} boxSize={12} color="rgba(255, 255, 255, 0.9)" />
                  <VStack spacing={2}>
                    <Text fontSize="2xl" fontWeight="800" color="white">
                      %127
                    </Text>
                    <Text fontSize="sm" color="rgba(255, 255, 255, 0.8)">
                      Ortalama Yıllık Getiri
                    </Text>
                  </VStack>
                  <HStack spacing={6} mt={4}>
                    <VStack spacing={1}>
                      <Text fontSize="lg" fontWeight="700">1,247</Text>
                      <Text fontSize="xs" opacity={0.8}>Aktif Robot</Text>
                    </VStack>
                    <VStack spacing={1}>
                      <Text fontSize="lg" fontWeight="700">15K+</Text>
                      <Text fontSize="xs" opacity={0.8}>Kullanıcı</Text>
                    </VStack>
                  </HStack>
                </VStack>
              </Box>
            </Box>
          </Flex>
        </CardBody>
      </Card>
      
      {/* Arama Kartı */}
      <Card 
        mb={{ base: 6, md: 8 }}
        variant="elevated"
        bg={cardBg}
        borderRadius="20px"
      >
        <CardBody p={{ base: 4, md: 6 }}>
          <Flex 
            direction={{ base: 'column', md: 'row' }} 
            gap={{ base: 3, md: 4 }} 
            align={{ base: 'stretch', md: 'center' }}
          >
            <InputGroup maxW={{ md: '400px' }} flexGrow={1}>
              <InputLeftElement pointerEvents="none" h={{ base: '40px', md: '48px' }}>
                <Icon as={FiSearch} color="gray.500" />
              </InputLeftElement>
              <Input 
                value={searchTerm}
                onChange={handleSearch}
                placeholder="Robot ara..."
                borderRadius="16px"
                bg={inputBg}
                border="1px solid"
                borderColor={borderColor}
                h={{ base: '40px', md: '48px' }}
                fontSize={{ base: 'sm', md: 'md' }}
                _focus={{
                  borderColor: 'brand.500',
                  boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
                }}
                _placeholder={{
                  fontSize: { base: 'sm', md: 'md' }
                }}
              />
            </InputGroup>
          </Flex>
        </CardBody>
      </Card>
      
      {/* Ana İçerik */}
      <Flex 
        direction={{ base: 'column', xl: 'row' }} 
        gap={{ base: '6', md: '8' }}
        align="flex-start"
      >
        {/* Sol Sütun - Robotlar */}
        <Box 
          flex="1" 
          minW="0" // Prevents overflow
          w={{ base: '100%', xl: 'auto' }}
        >
          {/* Trend Robotlar */}
          <Card 
            mb={{ base: '6', md: '8' }}
            variant="elevated"
            bg={cardBg}
            borderRadius="20px"
            role="region"
            aria-labelledby="trending-robots-heading"
          >
            <CardBody p={{ base: '4', md: '6' }}>
              <Flex 
                justify="space-between" 
                align="center" 
                mb={{ base: '4', md: '6' }}
                direction={{ base: 'column', sm: 'row' }}
                gap={{ base: '3', sm: '0' }}
              >
                <HStack 
                  spacing={{ base: '2', md: '3' }}
                  align="center"
                  w={{ base: '100%', sm: 'auto' }}
                  justify={{ base: 'center', sm: 'flex-start' }}
                >
                  <Icon 
                    as={FiTrendingUp} 
                    color="brand.500" 
                    boxSize={{ base: '5', md: '6' }}
                    aria-hidden="true"
                  />
                  <Heading 
                    id="trending-robots-heading"
                    as="h2"
                    size={{ base: 'md', md: 'lg' }} 
                    color={textColor}
                    textAlign={{ base: 'center', sm: 'left' }}
                  >
                    Trend Robotlar
                  </Heading>
                </HStack>
                
                <Button 
                  variant="ghost" 
                  size={{ base: 'sm', md: 'md' }}
                  colorScheme="brand"
                  rightIcon={<Icon as={FiArrowRight} />}
                  _hover={{ transform: 'translateX(2px)' }}
                  transition="transform 0.2s ease"
                  w={{ base: '100%', sm: 'auto' }}
                  aria-label="Tüm trend robotları görüntüle"
                >
                  Tümünü Gör
                </Button>
              </Flex>
              
              {/* Robot Grid - Responsive */}
              <SimpleGrid 
                columns={{ base: 1, md: 2, lg: 3 }} 
                spacing={{ base: '4', md: '6' }}
                w="100%"
              >
                {trendingRobots.slice(0, 6).map((robot) => (
                  <RobotCard
                    key={`trending-${robot.id}`}
                    robot={robot}
                    onSubscribe={handleSubscribe}
                    onUnsubscribe={handleUnsubscribe}
                    isSubscribed={hookUserSubscriptions.includes(robot.id)}
                  />
                ))}
              </SimpleGrid>
              
              {/* Boş durum */}
              {trendingRobots.length === 0 && !isLoading && (
                <Box textAlign="center" py={8}>
                  <Icon as={FiTrendingUp} boxSize={12} color="gray.400" mb={4} />
                  <Heading size="md" color="gray.600" mb={2}>
                    Henüz trend robot bulunmuyor
                  </Heading>
                  <Text color="gray.500" mb={4}>
                    Yakında en popüler robotlar burada görünecek.
                  </Text>
                  <Button colorScheme="brand" size="md">
                    Robotları Keşfet
                  </Button>
                </Box>
              )}
            </CardBody>
          </Card>

          {/* Son Eklenen Robotlar */}
          <Card 
            mb={{ base: '6', md: '8' }}
            variant="elevated"
            bg={cardBg}
            borderRadius="20px"
            role="region"
            aria-labelledby="recent-robots-heading"
          >
            <CardBody p={{ base: '4', md: '6' }}>
              <Flex 
                justify="space-between" 
                align="center" 
                mb={{ base: '4', md: '6' }}
                direction={{ base: 'column', sm: 'row' }}
                gap={{ base: '3', sm: '0' }}
              >
                <HStack 
                  spacing={{ base: '2', md: '3' }}
                  align="center"
                  w={{ base: '100%', sm: 'auto' }}
                  justify={{ base: 'center', sm: 'flex-start' }}
                >
                  <Icon 
                    as={FiClock} 
                    color="success.500" 
                    boxSize={{ base: '5', md: '6' }}
                    aria-hidden="true"
                  />
                  <Heading 
                    id="recent-robots-heading"
                    as="h2"
                    size={{ base: 'md', md: 'lg' }} 
                    color={textColor}
                    textAlign={{ base: 'center', sm: 'left' }}
                  >
                    Son Eklenenler
                  </Heading>
                </HStack>
                
                <Button 
                  variant="ghost" 
                  size={{ base: 'sm', md: 'md' }}
                  colorScheme="green"
                  rightIcon={<Icon as={FiArrowRight} />}
                  _hover={{ transform: 'translateX(2px)' }}
                  transition="transform 0.2s ease"
                  w={{ base: '100%', sm: 'auto' }}
                  aria-label="Tüm yeni robotları görüntüle"
                >
                  Tümünü Gör
                </Button>
              </Flex>
              
              {/* Robot Grid - Responsive */}
              <SimpleGrid 
                columns={{ base: 1, md: 2, lg: 3 }} 
                spacing={{ base: '4', md: '6' }}
                w="100%"
              >
                {recentRobots.slice(0, 6).map((robot) => (
                  <RobotCard
                    key={`recent-${robot.id}`}
                    robot={robot}
                    onSubscribe={handleSubscribe}
                    onUnsubscribe={handleUnsubscribe}
                    isSubscribed={hookUserSubscriptions.includes(robot.id)}
                  />
                ))}
              </SimpleGrid>
              
              {/* Boş durum */}
              {recentRobots.length === 0 && !isLoading && (
                <Box textAlign="center" py={8}>
                  <Icon as={FiClock} boxSize={12} color="gray.400" mb={4} />
                  <Heading size="md" color="gray.600" mb={2}>
                    Henüz yeni robot bulunmuyor
                  </Heading>
                  <Text color="gray.500" mb={4}>
                    Yakında en yeni robotlar burada görünecek.
                  </Text>
                  <Button 
                    colorScheme="brand" 
                    size="md"
                    onClick={() => navigate('/seller/robots')}
                  >
                    Robot Oluştur
                  </Button>
                </Box>
              )}
            </CardBody>
          </Card>
        </Box>

        {/* Sağ Sütun - İstatistikler ve Top Satıcılar */}
        <Box 
          w={{ base: '100%', xl: '350px' }}
          flexShrink="0"
        >
          {/* İstatistikler Kartı */}
          <Card 
            mb={{ base: '6', md: '8' }}
            variant="elevated"
            bg={cardBg}
            borderRadius="20px"
            role="region"
            aria-labelledby="stats-heading"
          >
            <CardBody p={{ base: '4', md: '6' }}>
              <HStack 
                spacing={{ base: '2', md: '3' }}
                mb={{ base: '4', md: '6' }}
                justify={{ base: 'center', xl: 'flex-start' }}
              >
                <Icon 
                  as={FiBarChart} 
                  color="purple.500" 
                  boxSize={{ base: '5', md: '6' }}
                  aria-hidden="true"
                />
                <Heading 
                  id="stats-heading"
                  as="h2"
                  size={{ base: 'md', md: 'lg' }} 
                  color={textColor}
                  textAlign={{ base: 'center', xl: 'left' }}
                >
                  Platform İstatistikleri
                </Heading>
              </HStack>
              
              <VStack 
                spacing={{ base: '3', md: '4' }}
                align="stretch"
              >
                <StatCard
                  label="Toplam Robot"
                  value={trendingRobots.length + recentRobots.length}
                  icon={FiBox}
                  color="blue.500"
                  role="region"
                  aria-label={`Toplam ${trendingRobots.length + recentRobots.length} robot mevcut`}
                />
                <StatCard
                  label="Aktif Kullanıcı"
                  value={statsLoading ? "..." : formatNumber(activeUsersCount)}
                  icon={FiUsers}
                  color="green.500"
                  role="region"
                  aria-label={`${formatNumber(activeUsersCount)} aktif kullanıcı`}
                />
                <StatCard
                  label="Toplam İşlem"
                  value={statsLoading ? "..." : formatNumber(totalTransactionsCount)}
                  icon={FiActivity}
                  color="orange.500"
                  role="region"
                  aria-label={`${formatNumber(totalTransactionsCount)} toplam işlem`}
                />
                <StatCard
                  label="Başarı Oranı"
                  value="87%"
                  icon={FiAward}
                  color="purple.500"
                  role="region"
                  aria-label="%87 başarı oranı"
                />
              </VStack>
            </CardBody>
          </Card>

          {/* Top Satıcılar */}
          <Card 
            variant="elevated"
            bg={cardBg}
            borderRadius="20px"
            role="region"
            aria-labelledby="top-sellers-heading"
          >
            <CardBody p={{ base: '4', md: '6' }}>
              <HStack 
                spacing={{ base: '2', md: '3' }}
                mb={{ base: '4', md: '6' }}
                justify={{ base: 'center', xl: 'flex-start' }}
              >
                <Icon 
                  as={FiStar} 
                  color="yellow.500" 
                  boxSize={{ base: '5', md: '6' }}
                  aria-hidden="true"
                />
                <Heading 
                  id="top-sellers-heading"
                  as="h2"
                  size={{ base: 'md', md: 'lg' }} 
                  color={textColor}
                  textAlign={{ base: 'center', xl: 'left' }}
                >
                  En İyi Satıcılar
                </Heading>
              </HStack>
              
              <VStack 
                spacing={{ base: '3', md: '4' }}
                align="stretch"
              >
                {topSellers.slice(0, 5).map((seller, index) => (
                  <SellerCard
                    key={`seller-${seller.id}-${index}`}
                    seller={seller}
                    rank={index + 1}
                  />
                ))}
              </VStack>
              
              {/* Boş durum */}
              {topSellers.length === 0 && !isLoading && (
                <Box textAlign="center" py={6}>
                  <Icon as={FiStar} boxSize={10} color="gray.400" mb={3} />
                  <Heading size="sm" color="gray.600" mb={2}>
                    Henüz satıcı bulunmuyor
                  </Heading>
                  <Text color="gray.500" fontSize="sm" mb={4}>
                    İlk satıcı olmak için robot oluşturun.
                  </Text>
                  <Button 
                    colorScheme="brand" 
                    size="sm"
                    onClick={() => navigate('/seller/robots')}
                  >
                    Robot Oluştur
                  </Button>
                </Box>
              )}
            </CardBody>
          </Card>
        </Box>
      </Flex>
    </Box>
  );
};

export default MarketplacePage; 