/**
 * Performance Optimizer Unit Tests
 * Phase 3.1: Automated Testing Pipeline Setup
 * Comprehensive testing for Edge Function performance optimization utilities
 */

import { assertEquals, assertExists, assert } from "https://deno.land/std@0.208.0/assert/mod.ts";
import { 
  EdgeFunctionCache, 
  PerformanceMonitor, 
  EdgeDbOptimizer,
  ResponseOptimizer,
  EdgeRateLimiter,
  MemoryManager,
  ConnectionPoolManager,
  createPerformanceMonitor
} from '../_shared/performance-optimizer.ts';

// Mock Supabase client for testing
const createMockSupabaseClient = () => ({
  from: (table: string) => ({
    select: () => ({ data: [], error: null }),
    insert: () => ({ data: [], error: null }),
    update: () => ({ data: [], error: null }),
    delete: () => ({ data: [], error: null }),
    eq: () => ({ data: [], error: null }),
    gte: () => ({ data: [], error: null }),
    lte: () => ({ data: [], error: null }),
    order: () => ({ data: [], error: null }),
    limit: () => ({ data: [], error: null })
  }),
  rpc: () => ({ data: null, error: null })
});

Deno.test("EdgeFunctionCache", async (t) => {
  await t.step("should store and retrieve cached data", () => {
    const testData = { id: 1, name: "test" };
    const cacheKey = "test-key";
    
    EdgeFunctionCache.set(cacheKey, testData, 60000);
    const retrieved = EdgeFunctionCache.get(cacheKey);
    
    assertEquals(retrieved, testData);
  });

  await t.step("should return null for expired cache", async () => {
    const testData = { id: 1, name: "test" };
    const cacheKey = "expired-key";
    
    EdgeFunctionCache.set(cacheKey, testData, 1); // 1ms TTL
    
    // Wait for expiration
    await new Promise(resolve => setTimeout(resolve, 10));
    
    const retrieved = EdgeFunctionCache.get(cacheKey);
    assertEquals(retrieved, null);
  });

  await t.step("should return null for non-existent key", () => {
    const retrieved = EdgeFunctionCache.get("non-existent-key");
    assertEquals(retrieved, null);
  });

  await t.step("should provide cache statistics", () => {
    EdgeFunctionCache.clear(); // Clear cache first
    
    EdgeFunctionCache.set("key1", "value1", 60000);
    EdgeFunctionCache.set("key2", "value2", 60000);
    
    const stats = EdgeFunctionCache.getStats();
    
    assertExists(stats.size);
    assertExists(stats.totalHits);
    assertExists(stats.totalMisses);
    assert(stats.size >= 2);
  });

  await t.step("should handle cache cleanup", () => {
    EdgeFunctionCache.clear();
    
    EdgeFunctionCache.set("key1", "value1", 60000);
    EdgeFunctionCache.set("key2", "value2", 60000);
    
    const statsBefore = EdgeFunctionCache.getStats();
    assert(statsBefore.size >= 2);
    
    EdgeFunctionCache.clear();
    
    const statsAfter = EdgeFunctionCache.getStats();
    assertEquals(statsAfter.size, 0);
  });
});

Deno.test("PerformanceMonitor", async (t) => {
  await t.step("should track performance metrics", () => {
    const monitor = createPerformanceMonitor();
    
    monitor.startDbQuery();
    monitor.endDbQuery();
    monitor.recordCacheHit();
    monitor.recordCacheMiss();
    
    const metrics = monitor.getCurrentMetrics();
    
    assertExists(metrics.dbQueryCount);
    assertExists(metrics.dbQueryTime);
    assertExists(metrics.cacheHits);
    assertExists(metrics.cacheMisses);
    
    assertEquals(metrics.dbQueryCount, 1);
    assertEquals(metrics.cacheHits, 1);
    assertEquals(metrics.cacheMisses, 1);
  });

  await t.step("should calculate cache hit rate", () => {
    const monitor = createPerformanceMonitor();
    
    monitor.recordCacheHit();
    monitor.recordCacheHit();
    monitor.recordCacheMiss();
    
    const metrics = monitor.getCurrentMetrics();
    const hitRate = (metrics.cacheHits / (metrics.cacheHits + metrics.cacheMisses)) * 100;
    
    assertEquals(Math.round(hitRate), 67); // 2 hits, 1 miss = 66.67%
  });

  await t.step("should track request size", () => {
    const monitor = createPerformanceMonitor();
    const testSize = 1024;
    
    monitor.setRequestSize(testSize);
    const metrics = monitor.getCurrentMetrics();
    
    assertEquals(metrics.requestSize, testSize);
  });

  await t.step("should finish with complete metrics", () => {
    const monitor = createPerformanceMonitor();
    
    monitor.startDbQuery();
    monitor.endDbQuery();
    monitor.recordCacheHit();
    monitor.setRequestSize(512);
    
    const finalMetrics = monitor.finish();
    
    assertExists(finalMetrics.totalTime);
    assertExists(finalMetrics.dbQueryCount);
    assertExists(finalMetrics.cacheHits);
    assertExists(finalMetrics.requestSize);
    
    assert(finalMetrics.totalTime > 0);
  });
});

Deno.test("EdgeDbOptimizer", async (t) => {
  await t.step("should execute and cache queries", async () => {
    const mockClient = createMockSupabaseClient();
    const monitor = createPerformanceMonitor();
    
    const queryFn = async () => ({ data: [{ id: 1, name: "test" }], error: null });
    
    const result1 = await EdgeDbOptimizer.executeQuery(
      mockClient,
      "test-query",
      queryFn,
      60000,
      monitor
    );
    
    const result2 = await EdgeDbOptimizer.executeQuery(
      mockClient,
      "test-query",
      queryFn,
      60000,
      monitor
    );
    
    assertEquals(result1.data, result2.data);
    
    // Second call should be from cache
    const metrics = monitor.getCurrentMetrics();
    assertEquals(metrics.cacheHits, 1);
  });

  await t.step("should handle query errors", async () => {
    const mockClient = createMockSupabaseClient();
    const monitor = createPerformanceMonitor();
    
    const errorQueryFn = async () => ({ data: null, error: { message: "Query failed" } });
    
    const result = await EdgeDbOptimizer.executeQuery(
      mockClient,
      "error-query",
      errorQueryFn,
      60000,
      monitor
    );
    
    assertExists(result.error);
    assertEquals(result.error.message, "Query failed");
  });

  await t.step("should execute batch queries", async () => {
    const mockClient = createMockSupabaseClient();
    const monitor = createPerformanceMonitor();
    
    const queries = [
      EdgeDbOptimizer.executeQuery(mockClient, "query1", async () => ({ data: [1], error: null }), 60000, monitor),
      EdgeDbOptimizer.executeQuery(mockClient, "query2", async () => ({ data: [2], error: null }), 60000, monitor),
      EdgeDbOptimizer.executeQuery(mockClient, "query3", async () => ({ data: [3], error: null }), 60000, monitor)
    ];
    
    const results = await EdgeDbOptimizer.executeBatch(queries, monitor);
    
    assertEquals(results.length, 3);
    assertEquals(results[0].data, [1]);
    assertEquals(results[1].data, [2]);
    assertEquals(results[2].data, [3]);
  });
});

Deno.test("ResponseOptimizer", async (t) => {
  await t.step("should create optimized JSON responses", () => {
    const monitor = createPerformanceMonitor();
    const testData = { message: "success", data: [1, 2, 3] };
    const headers = new Headers({ "Custom-Header": "test" });
    
    const response = ResponseOptimizer.createJsonResponse(testData, 200, headers, monitor);
    
    assertEquals(response.status, 200);
    assert(response.headers.has("Content-Type"));
    assert(response.headers.has("Custom-Header"));
    assert(response.headers.has("X-Processing-Time"));
  });

  await t.step("should create error responses", () => {
    const monitor = createPerformanceMonitor();
    const errorMessage = "Something went wrong";
    const headers = new Headers();
    
    const response = ResponseOptimizer.createErrorResponse(errorMessage, 500, headers, monitor);
    
    assertEquals(response.status, 500);
    assert(response.headers.has("Content-Type"));
    assert(response.headers.has("X-Processing-Time"));
  });

  await t.step("should add compression hints for large responses", () => {
    const monitor = createPerformanceMonitor();
    const largeData = { data: new Array(1000).fill("test data") };
    const headers = new Headers();
    
    const response = ResponseOptimizer.createJsonResponse(largeData, 200, headers, monitor);
    
    // Should add compression hint for large responses
    assert(response.headers.has("Content-Encoding") || response.headers.has("Vary"));
  });
});

Deno.test("EdgeRateLimiter", async (t) => {
  await t.step("should allow requests within limit", () => {
    const clientId = "test-client";
    const limit = 5;
    const windowMs = 60000;
    
    for (let i = 0; i < limit; i++) {
      const result = EdgeRateLimiter.checkLimit(clientId, limit, windowMs);
      assert(result.allowed);
      assertEquals(result.remaining, limit - i - 1);
    }
  });

  await t.step("should block requests exceeding limit", () => {
    const clientId = "test-client-2";
    const limit = 2;
    const windowMs = 60000;
    
    // First two requests should be allowed
    let result = EdgeRateLimiter.checkLimit(clientId, limit, windowMs);
    assert(result.allowed);
    
    result = EdgeRateLimiter.checkLimit(clientId, limit, windowMs);
    assert(result.allowed);
    
    // Third request should be blocked
    result = EdgeRateLimiter.checkLimit(clientId, limit, windowMs);
    assert(!result.allowed);
    assertEquals(result.remaining, 0);
  });

  await t.step("should reset after window expires", async () => {
    const clientId = "test-client-3";
    const limit = 1;
    const windowMs = 10; // Very short window
    
    // First request should be allowed
    let result = EdgeRateLimiter.checkLimit(clientId, limit, windowMs);
    assert(result.allowed);
    
    // Second request should be blocked
    result = EdgeRateLimiter.checkLimit(clientId, limit, windowMs);
    assert(!result.allowed);
    
    // Wait for window to expire
    await new Promise(resolve => setTimeout(resolve, 20));
    
    // Request should be allowed again
    result = EdgeRateLimiter.checkLimit(clientId, limit, windowMs);
    assert(result.allowed);
  });
});

Deno.test("MemoryManager", async (t) => {
  await t.step("should check memory usage", () => {
    const memoryStatus = MemoryManager.checkMemoryUsage();
    
    assertExists(memoryStatus.usage);
    assertExists(memoryStatus.limit);
    assertExists(memoryStatus.needsCleanup);
    
    assert(typeof memoryStatus.usage === 'number');
    assert(typeof memoryStatus.limit === 'number');
    assert(typeof memoryStatus.needsCleanup === 'boolean');
  });

  await t.step("should perform cleanup when needed", () => {
    // This test is more about ensuring the method exists and doesn't throw
    try {
      MemoryManager.performCleanup();
      assert(true); // If we get here, cleanup didn't throw
    } catch (error) {
      assert(false, `Cleanup should not throw: ${error.message}`);
    }
  });
});

Deno.test("ConnectionPoolManager", async (t) => {
  await t.step("should create and manage connection pools", () => {
    const poolName = "test-pool";
    const createFn = () => ({ connected: true, id: Math.random() });
    
    const connection1 = ConnectionPoolManager.getPool(poolName, createFn);
    const connection2 = ConnectionPoolManager.getPool(poolName, createFn);
    
    // Should return the same connection for the same pool name
    assertEquals(connection1, connection2);
  });

  await t.step("should create different pools for different names", () => {
    const createFn = () => ({ connected: true, id: Math.random() });
    
    const pool1 = ConnectionPoolManager.getPool("pool-1", createFn);
    const pool2 = ConnectionPoolManager.getPool("pool-2", createFn);
    
    // Should be different connections
    assert(pool1 !== pool2);
  });

  await t.step("should cleanup pools", () => {
    const poolName = "cleanup-test-pool";
    const createFn = () => ({ connected: true, id: Math.random() });
    
    // Create a pool
    ConnectionPoolManager.getPool(poolName, createFn);
    
    // Cleanup should not throw
    try {
      ConnectionPoolManager.cleanup();
      assert(true);
    } catch (error) {
      assert(false, `Pool cleanup should not throw: ${error.message}`);
    }
  });
});
