import { test, expect } from '@playwright/test';

test.describe('Statistics URL Refactoring', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test authentication in localStorage
    await page.goto('http://localhost:3000');

    // Add test authentication data to localStorage
    await page.evaluate(() => {
      const testUser = {
        id: '69742a2d-1e70-4ac5-9d42-b21885aaa623',
        email: '<EMAIL>',
        user_metadata: { full_name: 'Test User' },
        app_metadata: {},
        aud: 'authenticated',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const testSession = {
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        expires_in: 3600,
        expires_at: Math.floor(Date.now() / 1000) + 3600,
        token_type: 'bearer',
        user: testUser
      };

      localStorage.setItem('supabase.auth.token', JSON.stringify(testSession));
      localStorage.setItem('supabase.auth.user', JSON.stringify(testUser));
    });

    // Reload the page to apply authentication
    await page.reload();
    await page.waitForLoadState('networkidle');
  });

  test('should redirect /statistics to /statistics/overview when authenticated', async ({ page }) => {
    // Navigate to the old statistics URL without query parameters
    await page.goto('http://localhost:3000/statistics');

    // Wait for redirect to complete
    await page.waitForURL('**/statistics/overview', { timeout: 10000 });

    // Verify the URL has been redirected to the new format
    expect(page.url()).toContain('/statistics/overview');

    // Verify the statistics layout is loaded
    await expect(page.locator('text=Trading Analizi')).toBeVisible({ timeout: 10000 });
  });

  test('should redirect old query parameter URLs to new nested routes', async ({ page }) => {
    const testCases = [
      { old: '/statistics?tab=overview', new: '/statistics/overview' },
      { old: '/statistics?tab=roi-analysis', new: '/statistics/roi-analysis' },
      { old: '/statistics?tab=solo-robot', new: '/statistics/solo-robot' },
      { old: '/statistics?tab=bro-robots', new: '/statistics/bro-robots' },
      { old: '/statistics?tab=robot-comparison', new: '/statistics/robot-comparison' },
      { old: '/statistics?tab=performance', new: '/statistics/performance' },
      { old: '/statistics?tab=risk-analysis', new: '/statistics/risk-analysis' },
      { old: '/statistics?tab=time-analysis', new: '/statistics/time-analysis' },
      { old: '/statistics?tab=symbol-analysis', new: '/statistics/symbol-analysis' },
      { old: '/statistics?tab=reports', new: '/statistics/reports' }
    ];

    for (const testCase of testCases) {
      // Navigate to old URL format
      await page.goto(`http://localhost:3000${testCase.old}`);

      // Wait for redirect to complete
      await page.waitForURL(`**${testCase.new}`, { timeout: 10000 });

      // Verify the URL has been redirected to the new format
      expect(page.url()).toContain(testCase.new);

      // Verify the statistics layout is loaded
      await expect(page.locator('text=Trading Analizi')).toBeVisible({ timeout: 5000 });
    }
  });

  test('should handle invalid tab parameters by redirecting to overview', async ({ page }) => {
    // Navigate to statistics with invalid tab parameter
    await page.goto('http://localhost:3000/statistics?tab=invalid-tab');
    
    // Wait for redirect to complete
    await page.waitForURL('**/statistics/overview');
    
    // Verify the URL has been redirected to overview
    expect(page.url()).toContain('/statistics/overview');
  });

  test('should allow direct navigation to nested routes', async ({ page }) => {
    const validRoutes = [
      '/statistics/overview',
      '/statistics/roi-analysis',
      '/statistics/solo-robot',
      '/statistics/bro-robots',
      '/statistics/robot-comparison',
      '/statistics/performance',
      '/statistics/risk-analysis',
      '/statistics/time-analysis',
      '/statistics/symbol-analysis',
      '/statistics/reports'
    ];

    for (const route of validRoutes) {
      // Navigate directly to the nested route
      await page.goto(`http://localhost:3000${route}`);
      
      // Wait for the page to load
      await page.waitForLoadState('networkidle');
      
      // Verify the URL remains the same (no redirect)
      expect(page.url()).toContain(route);
      
      // Verify the statistics layout is present
      await expect(page.locator('text=Trading Analizi')).toBeVisible();
    }
  });

  test('should preserve sidebar navigation functionality', async ({ page }) => {
    // Navigate to statistics overview
    await page.goto('http://localhost:3000/statistics/overview');
    await page.waitForLoadState('networkidle');

    // Check if sidebar navigation items are present and clickable
    const sidebarItems = [
      { text: 'Genel Bakış', url: '/statistics/overview' },
      { text: 'ROI Analizi', url: '/statistics/roi-analysis' },
      { text: 'Performans Analizi', url: '/statistics/performance' },
      { text: 'Solo-Robot Analizi', url: '/statistics/solo-robot' },
      { text: 'Bro-Robot Analizi', url: '/statistics/bro-robots' },
      { text: 'Robot Karşılaştırması', url: '/statistics/robot-comparison' },
      { text: 'Risk Analizi', url: '/statistics/risk-analysis' },
      { text: 'Zaman Analizi', url: '/statistics/time-analysis' },
      { text: 'Sembol Analizi', url: '/statistics/symbol-analysis' },
      { text: 'Raporlar', url: '/statistics/reports' }
    ];

    for (const item of sidebarItems.slice(0, 3)) { // Test first 3 items to avoid timeout
      // Click on sidebar item
      await page.click(`text=${item.text}`);
      
      // Wait for navigation to complete
      await page.waitForURL(`**${item.url}`);
      
      // Verify the URL has changed to the correct route
      expect(page.url()).toContain(item.url);
    }
  });

  test('should maintain filtering and real-time functionality', async ({ page }) => {
    // Navigate to statistics overview
    await page.goto('http://localhost:3000/statistics/overview');
    await page.waitForLoadState('networkidle');

    // Check if the statistics header with filters is present
    await expect(page.locator('text=Trading Analizi')).toBeVisible();
    
    // Check if last update time is displayed (indicates real-time functionality)
    await expect(page.locator('text=Son güncelleme:')).toBeVisible();
    
    // Check if refresh button is present and functional
    const refreshButton = page.locator('[aria-label="Yenile"]');
    await expect(refreshButton).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Navigate to a non-existent statistics route
    await page.goto('http://localhost:3000/statistics/non-existent-route');
    
    // Should either redirect to overview or show 404
    // Wait for either redirect or error page
    await page.waitForTimeout(2000);
    
    // Check if we're redirected to overview or if there's an error message
    const currentUrl = page.url();
    const hasErrorMessage = await page.locator('text=404').isVisible();
    
    expect(currentUrl.includes('/statistics/overview') || hasErrorMessage).toBeTruthy();
  });

  test('should preserve URL state during navigation', async ({ page }) => {
    // Navigate to a specific statistics page
    await page.goto('http://localhost:3000/statistics/performance');
    await page.waitForLoadState('networkidle');

    // Verify the URL is correct
    expect(page.url()).toContain('/statistics/performance');

    // Navigate to another page within statistics
    await page.click('text=ROI Analizi');
    await page.waitForURL('**/statistics/roi-analysis');

    // Verify the URL has changed correctly
    expect(page.url()).toContain('/statistics/roi-analysis');

    // Use browser back button
    await page.goBack();
    await page.waitForURL('**/statistics/performance');

    // Verify we're back to the previous page
    expect(page.url()).toContain('/statistics/performance');
  });
});
