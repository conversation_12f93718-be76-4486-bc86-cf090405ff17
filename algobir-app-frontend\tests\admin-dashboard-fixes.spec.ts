import { test, expect } from '@playwright/test';
import { 
  loginAsAdmin, 
  waitForPageLoad,
  TEST_USER
} from './utils/test-helpers';

test.describe('Admin Dashboard Veri Düzeltmeleri', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsAdmin(page);
    await waitForPageLoad(page);
  });

  test('Admin dashboard ana kartları doğru veri gösteriyor', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Dashboard başlığını kontrol et
    await expect(page.locator('h1').filter({ hasText: 'Algobir Yönetim Paneli' })).toBeVisible();
    
    // Ana metrik kartlarını kontrol et
    const metrics = [
      { name: 'Toplam Kullanıcı', expectedMin: 40 },
      { name: 'Toplam İşlem', expectedMin: 3000 },
      { name: '<PERSON><PERSON><PERSON>', expectedMin: 5 },
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', expectedMin: 0 },
      { name: '<PERSON><PERSON> Haftaki İşlemler', expectedMin: 0 }
    ];
    
    for (const metric of metrics) {
      const card = page.locator(`h2:has-text("${metric.name}")`).locator('..').locator('..');
      await expect(card).toBeVisible();
      
      // Değeri al
      const valueElement = card.locator('dd').first();
      const valueText = await valueElement.textContent();
      const value = parseInt(valueText?.replace(/[^\d]/g, '') || '0');
      
      expect(value).toBeGreaterThanOrEqual(metric.expectedMin);
      console.log(`✅ ${metric.name}: ${valueText} (≥ ${metric.expectedMin})`);
    }
  });

  test('Toplam İşlem sayısı gerçek veriyi gösteriyor', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Toplam İşlem kartını bul
    const totalTradesCard = page.locator('h2:has-text("Toplam İşlem")').locator('..').locator('..');
    const valueElement = totalTradesCard.locator('dd').first();
    const valueText = await valueElement.textContent();
    const value = parseInt(valueText?.replace(/[^\d]/g, '') || '0');
    
    // Değerin 3000'den fazla olduğunu kontrol et (gerçek veri)
    expect(value).toBeGreaterThan(3000);
    
    // Değerin 1000 olmadığını kontrol et (eski hardcoded değer)
    expect(value).not.toBe(1000);
    
    console.log(`✅ Toplam İşlem: ${valueText} (Gerçek veri)`);
  });

  test('Bugünkü İşlemler sayısı doğru hesaplanıyor', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Bugünkü İşlemler kartını bul
    const todayTradesCard = page.locator('h2:has-text("Bugünkü İşlemler")').locator('..').locator('..');
    const valueElement = todayTradesCard.locator('dd').first();
    const valueText = await valueElement.textContent();
    const value = parseInt(valueText?.replace(/[^\d]/g, '') || '0');
    
    // Değerin 0 veya pozitif olduğunu kontrol et
    expect(value).toBeGreaterThanOrEqual(0);
    
    // Eğer bugün işlem varsa, 0'dan büyük olmalı
    if (value > 0) {
      console.log(`✅ Bugünkü İşlemler: ${valueText} (Aktif)`);
    } else {
      console.log(`ℹ️ Bugünkü İşlemler: ${valueText} (Bugün işlem yok)`);
    }
  });

  test('Bu Haftaki İşlemler sayısı doğru hesaplanıyor', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Bu Haftaki İşlemler kartını bul
    const weekTradesCard = page.locator('h2:has-text("Bu Haftaki İşlemler")').locator('..').locator('..');
    const valueElement = weekTradesCard.locator('dd').first();
    const valueText = await valueElement.textContent();
    const value = parseInt(valueText?.replace(/[^\d]/g, '') || '0');
    
    // Değerin 0 veya pozitif olduğunu kontrol et
    expect(value).toBeGreaterThanOrEqual(0);
    
    // Bu hafta en az birkaç işlem olması beklenir
    if (value > 0) {
      console.log(`✅ Bu Haftaki İşlemler: ${valueText}`);
    } else {
      console.log(`ℹ️ Bu Haftaki İşlemler: ${valueText} (Bu hafta işlem yok)`);
    }
  });

  test('Aktif Kullanıcı sayısı mantıklı aralıkta', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Aktif Kullanıcı kartını bul
    const activeUsersCard = page.locator('h2:has-text("Aktif Kullanıcı")').locator('..').locator('..');
    const valueElement = activeUsersCard.locator('dd').first();
    const valueText = await valueElement.textContent();
    const value = parseInt(valueText?.replace(/[^\d]/g, '') || '0');
    
    // Aktif kullanıcı sayısının mantıklı aralıkta olduğunu kontrol et
    expect(value).toBeGreaterThanOrEqual(1);
    expect(value).toBeLessThanOrEqual(100); // Makul üst limit
    
    console.log(`✅ Aktif Kullanıcı: ${valueText}`);
  });

  test('Toplam Kullanıcı sayısı doğru', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Toplam Kullanıcı kartını bul
    const totalUsersCard = page.locator('h2:has-text("Toplam Kullanıcı")').locator('..').locator('..');
    const valueElement = totalUsersCard.locator('dd').first();
    const valueText = await valueElement.textContent();
    const value = parseInt(valueText?.replace(/[^\d]/g, '') || '0');
    
    // Toplam kullanıcı sayısının mantıklı olduğunu kontrol et
    expect(value).toBeGreaterThanOrEqual(40);
    expect(value).toBeLessThanOrEqual(1000); // Makul üst limit
    
    console.log(`✅ Toplam Kullanıcı: ${valueText}`);
  });

  test('Dashboard kartları yükleme durumu', async ({ page }) => {
    await page.goto('/admin');
    
    // Sayfa yüklenirken loading durumunu kontrol et
    const loadingElement = page.locator('text=Loading');
    if (await loadingElement.isVisible()) {
      console.log('ℹ️ Loading durumu görüldü');
      
      // Loading'in kaybolmasını bekle
      await expect(loadingElement).not.toBeVisible({ timeout: 10000 });
    }
    
    await waitForPageLoad(page);
    
    // Tüm kartların yüklendiğini kontrol et
    const cards = page.locator('[role="group"]').filter({ hasText: /Toplam|Bugünkü|Bu Haftaki|Aktif/ });
    const cardCount = await cards.count();
    
    expect(cardCount).toBeGreaterThanOrEqual(4);
    console.log(`✅ ${cardCount} dashboard kartı yüklendi`);
  });

  test('Dashboard hızlı erişim menüsü çalışıyor', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Hızlı erişim linklerini test et
    const quickAccessLinks = [
      { text: 'Kullanıcı Yönetimi', url: '/admin/users' },
      { text: 'İşlem Yönetimi', url: '/admin/trades' },
      { text: 'Bildirim Yönetimi', url: '/admin/notifications' },
      { text: 'İstatistikler', url: '/admin/statistics' }
    ];
    
    for (const link of quickAccessLinks) {
      const linkElement = page.locator(`a:has-text("${link.text}")`);
      await expect(linkElement).toBeVisible();
      
      // Link'e tıkla
      await linkElement.click();
      await waitForPageLoad(page);
      
      // URL'nin doğru olduğunu kontrol et
      expect(page.url()).toContain(link.url);
      console.log(`✅ ${link.text} linki çalışıyor: ${link.url}`);
      
      // Geri dön
      await page.goto('/admin');
      await waitForPageLoad(page);
    }
  });

  test('Sistem sağlık durumu gösteriliyor', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Sistem sağlık durumu bölümünü kontrol et
    const healthSection = page.locator('h2:has-text("Sistem Sağlık Durumu")').locator('..');
    await expect(healthSection).toBeVisible();
    
    // Sağlık durumu göstergelerini kontrol et
    const healthIndicators = [
      'Platform Hızı',
      'Güvenlik',
      'Veritabanı',
      'API Durumu'
    ];
    
    for (const indicator of healthIndicators) {
      const indicatorElement = healthSection.locator(`text=${indicator}`);
      await expect(indicatorElement).toBeVisible();
      console.log(`✅ ${indicator} göstergesi mevcut`);
    }
  });

  test('Dashboard responsive tasarım', async ({ page }) => {
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Desktop görünümü
    await page.setViewportSize({ width: 1920, height: 1080 });
    await waitForPageLoad(page);
    
    const desktopCards = await page.locator('[role="group"]').count();
    console.log(`💻 Desktop'ta ${desktopCards} kart görünüyor`);
    
    // Tablet görünümü
    await page.setViewportSize({ width: 768, height: 1024 });
    await waitForPageLoad(page);
    
    const tabletCards = await page.locator('[role="group"]').count();
    console.log(`📱 Tablet'te ${tabletCards} kart görünüyor`);
    
    // Kartların hala görünür olduğunu kontrol et
    expect(tabletCards).toBeGreaterThanOrEqual(4);
    
    // Mobile görünümü
    await page.setViewportSize({ width: 375, height: 667 });
    await waitForPageLoad(page);
    
    const mobileCards = await page.locator('[role="group"]').count();
    console.log(`📱 Mobile'da ${mobileCards} kart görünüyor`);
    
    expect(mobileCards).toBeGreaterThanOrEqual(4);
  });
});
