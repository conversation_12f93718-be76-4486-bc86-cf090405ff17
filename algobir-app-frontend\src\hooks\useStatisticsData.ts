import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabaseClient';
import { useAuth } from '../context/AuthContext';
import { Trade } from '../types/trade';
import { PnLStats } from '../types/stats';

// Statistics.tsx'te kullanılan ek özellikler için genişletilmiş Trade tipi
interface ExtendedTrade extends Omit<Trade, 'signal_name'> {
  trade_category?: string | null;
  signal_name?: string | null;  // Omit kullanarak parent tip ile uyumlu hale getiriyoruz
  system_name?: string | null;
  position_status?: string | null;
}

// Sembol verisi için interface
interface SymbolData {
  symbol: string;
  count: number;
}

// Kategori verisi için interface
interface CategoryData {
  name: string;
  value: number;
}

// Portföy büyüme verisi için interface
export interface PortfolioGrowthData {
  entry_date: string;
  portfolio_value: number;
}

// Sinyal tipi performansı için interface
interface SignalTypePerformance {
  signalType: string;
  tradeCount: number;
  totalPnl: number;
  winRate: number;
  winningTrades: number;
  losingTrades: number;
  averageWin: number;
  averageLoss: number;
  profitFactor: number;
}

// Sembol performansı için interface
interface SymbolPerformance {
  symbol: string;
  tradeCount: number;
  totalPnl: number;
  winRate: number;
  winningTrades: number;
  losingTrades: number;
  averageWin: number;
  averageLoss: number;
  profitFactor: number;
}

// Hook'un dönüş değeri için interface
interface UseStatisticsDataReturn {
  trades: ExtendedTrade[];
  recentTrades: ExtendedTrade[];
  pnlStats: PnLStats;
  portfolioGrowthData: PortfolioGrowthData[];
  symbolData: SymbolData[];
  signalTypeDistributionData: CategoryData[];
  signalTypePerformance: SignalTypePerformance[];
  symbolPerformance: SymbolPerformance[];
  isLoading: boolean;
  loadingPnlStats: boolean;
  loadingPortfolioGrowth: boolean;
  error: string | null;
  pnlStatsError: string | null;
  portfolioGrowthError: string | null;
  refetch: () => void;
}

export function useStatisticsData(): UseStatisticsDataReturn {
  const { user } = useAuth();
  
  // Ana veri state'leri
  const [trades, setTrades] = useState<ExtendedTrade[]>([]);
  const [recentTrades, setRecentTrades] = useState<ExtendedTrade[]>([]);
  const [symbolData, setSymbolData] = useState<SymbolData[]>([]);
  const [signalTypeDistributionData, setSignalTypeDistributionData] = useState<CategoryData[]>([]);
  
  // P/L istatistikleri state'i
  const [pnlStats, setPnlStats] = useState<PnLStats>({
    totalPnl: 0,
    winRate: 0,
    winningTrades: 0,
    losingTrades: 0
  });
  
  // Portföy büyüme verisi state'i
  const [portfolioGrowthData, setPortfolioGrowthData] = useState<PortfolioGrowthData[]>([]);
  
  // Performans metrikleri state'leri
  const [signalTypePerformance, setSignalTypePerformance] = useState<SignalTypePerformance[]>([]);
  const [symbolPerformance, setSymbolPerformance] = useState<SymbolPerformance[]>([]);
  
  // Yükleme durumu state'leri
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loadingPnlStats, setLoadingPnlStats] = useState<boolean>(true);
  const [loadingPortfolioGrowth, setLoadingPortfolioGrowth] = useState<boolean>(true);
  
  // Hata state'leri
  const [error, setError] = useState<string | null>(null);
  const [pnlStatsError, setPnlStatsError] = useState<string | null>(null);
  const [portfolioGrowthError, setPortfolioGrowthError] = useState<string | null>(null);

  // Performans metriklerini hesaplamak için yardımcı fonksiyon
  const calculatePerformanceMetrics = (tradesArray: ExtendedTrade[]) => {
    if (!tradesArray || tradesArray.length === 0) {
      return { 
        tradeCount: 0, 
        totalPnl: 0, 
        winRate: 0, 
        winningTrades: 0, 
        losingTrades: 0, 
        averageWin: 0, 
        averageLoss: 0, 
        profitFactor: 0 
      };
    }

    const closed = tradesArray.filter(t => t.pnl != null);
    if (closed.length === 0) {
      return { 
        tradeCount: tradesArray.length, 
        totalPnl: 0, 
        winRate: 0, 
        winningTrades: 0, 
        losingTrades: 0, 
        averageWin: 0, 
        averageLoss: 0, 
        profitFactor: 0 
      };
    }

    const returns = closed.map(t => t.pnl as number);
    const winning = closed.filter(t => t.pnl != null && t.pnl > 0);
    const losing = closed.filter(t => t.pnl != null && t.pnl < 0);

    const totalPnl = returns.reduce((sum, pnl) => sum + pnl, 0);
    const winRate = closed.length > 0 ? (winning.length / closed.length) * 100 : 0;
    const totalGain = winning.reduce((sum, t) => sum + (t.pnl as number), 0);
    const totalLoss = Math.abs(losing.reduce((sum, t) => sum + (t.pnl as number), 0));
    const averageWin = winning.length > 0 ? totalGain / winning.length : 0;
    const averageLoss = losing.length > 0 ? totalLoss / losing.length : 0;
    const profitFactor = totalLoss > 0 ? totalGain / totalLoss : (totalGain > 0 ? Infinity : 0);

    return {
      tradeCount: tradesArray.length,
      totalPnl,
      winRate,
      winningTrades: winning.length,
      losingTrades: losing.length,
      averageWin,
      averageLoss,
      profitFactor
    };
  };

  // İşlemleri çekme fonksiyonu
  const fetchTrades = useCallback(async () => {
    try {
      if (!user) throw new Error('Kullanıcı bilgisi bulunamadı.');
      
      setIsLoading(true);
      setError(null);
      
      const { data, error } = await supabase
        .from('trades')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_deleted', false);
      
      if (error) throw error;
      
      if (data) {
        // Silinen işlemleri filtrele (backend filtrelemesi olsa bile, frontend'de de kontrol)
        const nonDeletedTrades = data.filter((trade: ExtendedTrade) => !trade.is_deleted);
        
        setTrades(nonDeletedTrades);
        // En son 5 işlemi güncelle
        setRecentTrades(nonDeletedTrades.slice(0, 5));
        
        // Diğer verileri hazırla
        // Sembol bazlı işlem sayıları
        const symbolCounts: Record<string, number> = {};
        
        nonDeletedTrades.forEach((trade: ExtendedTrade) => {
          if (trade.symbol) {
            symbolCounts[trade.symbol] = (symbolCounts[trade.symbol] || 0) + 1;
          }
        });
        
        const symbolDataArray = Object.entries(symbolCounts)
          .map(([symbol, count]) => ({ symbol, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 10); // İlk 10 sembol
          
        setSymbolData(symbolDataArray);
        
        // İşlem tipi dağılımı
        const categoryMap: Record<string, number> = {};
        
        nonDeletedTrades.forEach((trade: ExtendedTrade) => {
          const category = trade.trade_category || 'Bilinmiyor';
          categoryMap[category] = (categoryMap[category] || 0) + 1;
        });
        
        const categoryData = Object.entries(categoryMap)
          .map(([name, value]) => ({ name, value }));
          
        setSignalTypeDistributionData(categoryData);
        
        // Sinyal tipi ve sembol bazında istatistikleri hesapla
        // Sadece kapanmış işlemleri filtrele
        const closedTrades = nonDeletedTrades.filter(t => t.pnl != null);
        
        // Sinyal tipine göre grupla
        const signalTypeGroups: Record<string, ExtendedTrade[]> = {
          'Alım': [],
          'Satım (Kar Alma)': [],
          'Satım (Stop)' : []
        };
        
        closedTrades.forEach(trade => {
          const signalNameStr = String(trade.signal_name || '').toLowerCase();
          const tradeCategory = String(trade.trade_category || '').toLowerCase();
          
          if (signalNameStr.includes('buy') || tradeCategory.includes('alım')) {
            signalTypeGroups['Alım'].push(trade);
          } else if (signalNameStr.includes('ktp') || signalNameStr.includes('atrtp') || 
                    tradeCategory.includes('tp') || tradeCategory.includes('satım (tp)')) {
            signalTypeGroups['Satım (Kar Alma)'].push(trade);
          } else if (signalNameStr.includes('stop') || tradeCategory.includes('stop') || 
                    tradeCategory.includes('satım (stop)')) {
            signalTypeGroups['Satım (Stop)'].push(trade);
          } else {
            // Diğer kategorileri sınıflandıramadığımız durumda
            const signalType = trade.signal_type || 'Bilinmiyor';
            if (!signalTypeGroups[signalType]) {
              signalTypeGroups[signalType] = [];
            }
            signalTypeGroups[signalType].push(trade);
          }
        });
        
        // Her sinyal tipi için performans metrikleri hesapla
        const signalTypeMetrics: SignalTypePerformance[] = Object.entries(signalTypeGroups).map(([signalType, trades]) => {
          const metrics = calculatePerformanceMetrics(trades);
          return {
            signalType,
            ...metrics
          };
        });
        
        // Performansı en yüksek olandan düşüğe doğru sırala
        signalTypeMetrics.sort((a, b) => b.totalPnl - a.totalPnl);
        setSignalTypePerformance(signalTypeMetrics);
        
        // Sembol bazında grupla
        const symbolGroups: Record<string, ExtendedTrade[]> = {};
        
        closedTrades.forEach(trade => {
          const symbol = trade.symbol;
          if (!symbolGroups[symbol]) {
            symbolGroups[symbol] = [];
          }
          symbolGroups[symbol].push(trade);
        });
        
        // Her sembol için performans metrikleri hesapla
        const symbolMetrics: SymbolPerformance[] = Object.entries(symbolGroups).map(([symbol, trades]) => {
          const metrics = calculatePerformanceMetrics(trades);
          return {
            symbol,
            ...metrics
          };
        });
        
        // Performansı en yüksek olandan düşüğe doğru sırala
        symbolMetrics.sort((a, b) => b.totalPnl - a.totalPnl);
        setSymbolPerformance(symbolMetrics);
      }
    } catch (err: any) {
      console.error('İşlemler yüklenirken hata oluştu:', err);
      setError(err.message || 'İşlemler yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // P/L İstatistikleri çekme fonksiyonu
  const fetchPnlStats = useCallback(async () => {
    try {
      // Kullanıcı ID kontrolü ekle
      if (!user) {
        console.warn('P/L istatistikleri yüklenirken hata: Kullanıcı ID bulunamadı');
        setPnlStatsError('Kullanıcı bilgileri alınamadı.');
        return;
      }

      setLoadingPnlStats(true);
      setPnlStatsError(null);
      
      // RPC parametresini düzelt
      const { data, error } = await supabase
        .rpc('get_basic_pnl_stats', { user_id_param: user.id });
        
      if (error) {
        console.error('[STATS ERROR] P/L Stats:', error);
        setPnlStatsError(error.message);
        return;
      }
      
      // Ham veriyi logla
      console.log('Received Basic P/L Stats Data:', JSON.stringify(data, null, 2));
      
      if (data) {
        // Verileri string'den sayıya çevirme yardımcı fonksiyonu
        const toNumber = (value: string | number | null | undefined): number => {
          if (value === null || value === undefined) return 0;
          if (typeof value === 'number') return value;
          const parsed = parseFloat(value);
          return isNaN(parsed) ? 0 : parsed;
        };
        
        // API yanıtı bir dizi içinde bir nesne şeklinde gelebilir
        // veya direkt bir nesne olarak gelebilir, her iki durumu da kontrol edelim
        const statsData = Array.isArray(data) ? data[0] : data;
        
        if (statsData) {
          const updatedStats = {
            totalPnl: toNumber(statsData.total_pnl),
            winRate: toNumber(statsData.win_rate),
            winningTrades: Number(statsData.winning_trades_count) || 0,
            losingTrades: Number(statsData.losing_trades_count) || 0
          };
          
          console.log('Processed PnL stats:', JSON.stringify(updatedStats, null, 2));
          
          // State'i güncelle
          setPnlStats(updatedStats);
        } else {
          console.warn('Stats data is undefined or null within response:', data);
          setPnlStats({
            totalPnl: 0,
            winRate: 0,
            winningTrades: 0,
            losingTrades: 0
          });
        }
      } else {
        console.warn('P/L stats data is empty or undefined:', data);
        setPnlStats({
          totalPnl: 0,
          winRate: 0,
          winningTrades: 0,
          losingTrades: 0
        });
      }
    } catch (err: any) {
      console.error('P/L istatistikleri yüklenirken hata oluştu:', err);
      setPnlStatsError(err.message || 'P/L istatistikleri yüklenirken bir hata oluştu.');
      setPnlStats({
        totalPnl: 0,
        winRate: 0,
        winningTrades: 0,
        losingTrades: 0
      });
    } finally {
      setLoadingPnlStats(false);
    }
  }, [user]);

  // Portföy büyüme verisini çekme fonksiyonu
  const fetchPortfolioGrowth = useCallback(async () => {
    try {
      // Kullanıcı ID kontrolü ekle
      if (!user) {
        console.warn('Portföy büyüme verisi yüklenirken hata: Kullanıcı ID bulunamadı');
        setPortfolioGrowthError('Kullanıcı bilgileri alınamadı.');
        setPortfolioGrowthData([]);
        return;
      }

      setLoadingPortfolioGrowth(true);
      setPortfolioGrowthError(null);
      
      // RPC çağrısını yap - parametre adı düzeltildi
      const { data, error } = await supabase
        .rpc('get_portfolio_growth_series', { p_user_id: user.id });
        
      if (error) {
        console.error('[STATS ERROR] Portfolio Growth:', error);
        setPortfolioGrowthError(`Portföy büyüme verisi alınamadı: ${error.message || 'RPC function not found'}`);
        setPortfolioGrowthData([]);
        return;
      }
      
      // Yeni veri yapısı: [{ entry_date, portfolio_value }]
      if (data && Array.isArray(data) && data.length > 0) {
        setPortfolioGrowthData(data);
      } else {
        setPortfolioGrowthData([]);
      }
    } catch (err: any) {
      console.error('Portföy büyüme verisi yüklenirken hata oluştu:', err);
      setPortfolioGrowthError(err.message || 'Portföy büyüme verisi yüklenirken bir hata oluştu.');
      setPortfolioGrowthData([]);
    } finally {
      setLoadingPortfolioGrowth(false);
    }
  }, [user]);

  // Ana veri yükleme fonksiyonu
  const fetchData = useCallback(async () => {
    if (!user) return;
    
    // İlk olarak tüm işlemleri yükle
    await fetchTrades();
    
    // Diğer veri türlerini yükle
    await Promise.all([
      fetchPnlStats(),
      fetchPortfolioGrowth()
    ]);
  }, [user, fetchTrades, fetchPnlStats, fetchPortfolioGrowth]);

  // Bileşen yüklendiğinde verileri çek
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Hook'un dönüş değeri
  return {
    trades,
    recentTrades,
    pnlStats,
    portfolioGrowthData,
    symbolData,
    signalTypeDistributionData,
    signalTypePerformance,
    symbolPerformance,
    isLoading,
    loadingPnlStats,
    loadingPortfolioGrowth,
    error,
    pnlStatsError,
    portfolioGrowthError,
    refetch: fetchData
  };
} 