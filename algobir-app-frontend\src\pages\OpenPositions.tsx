import React, { useState } from 'react';
import {
  Box, Flex, Text, Spinner, Table, Thead, Tbody, Tr, Th, Td, 
  Badge, Alert, AlertIcon, useColorModeValue, Center, Icon, Tooltip,
  Button, useToast, VStack, HStack, useBreakpointValue
} from '@chakra-ui/react';
import { format, intervalToDuration } from 'date-fns';
import { tr } from 'date-fns/locale';
import Card from '../components/card/Card';
import { MdTimer } from 'react-icons/md';
import { useTradesData } from '../hooks/useTradesData';
import { FaRobot, FaUserAlt, FaQuestionCircle } from 'react-icons/fa';
import { Trade } from '../types/trade';
import { supabase } from '../supabaseClient';
import { useManagementData } from '../hooks/useManagementData';

// Sinyal kaynağı ikonu ve açıklaması
const SignalSourceIcon = ({ trade }: { trade: Trade }) => {
  const soloRobotColor = useColorModeValue('blue.500', 'blue.300');
  const broRobotColor = useColorModeValue('purple.500', 'purple.300');
  const unknownColor = useColorModeValue('gray.500', 'gray.400');
  
  // robot_id veya webhook_id değerine bakarak kaynağı belirle
  if (trade.robot_id) {
    return (
      <Tooltip label="Bro-Robot (Pazar Yeri)" fontSize="sm">
        <Box cursor="pointer">
          <FaRobot color={broRobotColor} size="16px" />
        </Box>
      </Tooltip>
    );
  } else if (trade.webhook_id) {
    return (
      <Tooltip label="Solo-Robot (Kişisel Webhook)" fontSize="sm">
        <Box cursor="pointer">
          <FaUserAlt color={soloRobotColor} size="16px" />
        </Box>
      </Tooltip>
    );
  } else {
    return (
      <Tooltip label="Bilinmeyen Kaynak" fontSize="sm">
        <Box cursor="pointer">
          <FaQuestionCircle color={unknownColor} size="16px" />
        </Box>
      </Tooltip>
    );
  }
};

// Ana bileşen
const OpenPositions: React.FC = () => {
  // Theme renkler
  const textColor = useColorModeValue('navy.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');
  const headerBg = useColorModeValue('secondaryGray.100', 'navy.800');
  const headerTextColor = useColorModeValue('gray.500', 'white');
  const hoverRowBg = useColorModeValue('secondaryGray.100', 'navy.700');
  const stripedRowBg = useColorModeValue('secondaryGray.50', 'navy.900');
  const cardShadow = useColorModeValue('0px 18px 40px rgba(112, 144, 176, 0.12)', 'none');
  const cardBg = useColorModeValue('white', 'navy.700');
  const secondaryTextColor = useColorModeValue('secondaryGray.600', 'white');
  const toast = useToast();
  
  // Pozisyon kapatma durumları için state - her pozisyon için ayrı tracking
  const [closingPositions, setClosingPositions] = useState<Record<number, boolean>>({});

  // Responsive breakpoint kontrolü
  const isMobile = useBreakpointValue({ base: true, lg: false });

  // user_settings için hook
  const { settings } = useManagementData();

  // useTradesData hook'unu filtrelerle kullan
  const { trades: positions, isLoading, error, refetch } = useTradesData({
    position_status: 'Açık',
    is_deleted: false // Silinmiş işlemleri hariç tut
  });

  // Rate limiting and request management state
  const [lastRequestTimes, setLastRequestTimes] = useState<Record<number, number>>({});
  const [retryAttempts, setRetryAttempts] = useState<Record<number, number>>({});
  
  // Rate limiting constants
  const MIN_REQUEST_INTERVAL = 3000; // Minimum 3 seconds between requests for same position
  const MAX_RETRY_ATTEMPTS = 3;
  const RETRY_BASE_DELAY = 2000; // 2 seconds base delay for exponential backoff

  // Helper function to check if rate limit allows request
  const canMakeRequest = (positionId: number): boolean => {
    const lastRequestTime = lastRequestTimes[positionId] || 0;
    const timeSinceLastRequest = Date.now() - lastRequestTime;
    return timeSinceLastRequest >= MIN_REQUEST_INTERVAL;
  };

  // Helper function to calculate exponential backoff delay
  const calculateBackoffDelay = (attemptNumber: number): number => {
    return RETRY_BASE_DELAY * Math.pow(2, attemptNumber - 1); // 2s, 4s, 8s...
  };

  // Helper function to update last request time
  const updateLastRequestTime = (positionId: number) => {
    setLastRequestTimes(prev => ({ ...prev, [positionId]: Date.now() }));
  };

  // Helper function to manage retry attempts
  const incrementRetryAttempt = (positionId: number): number => {
    const currentAttempts = retryAttempts[positionId] || 0;
    const newAttempts = currentAttempts + 1;
    setRetryAttempts(prev => ({ ...prev, [positionId]: newAttempts }));
    return newAttempts;
  };

  // Helper function to reset retry attempts
  const resetRetryAttempts = (positionId: number) => {
    setRetryAttempts(prev => ({ ...prev, [positionId]: 0 }));
  };

  // Helper functions for managing closing state
  const setPositionClosing = (positionId: number, isClosing: boolean) => {
    setClosingPositions(prev => ({ ...prev, [positionId]: isClosing }));
  };

  const isPositionClosing = (positionId: number): boolean => {
    return closingPositions[positionId] || false;
  };

  // Sayısal formatlama
  const formatNumber = (value: number | null, minimumFractionDigits = 2): string => {
    if (value === null || value === undefined) return '-';
    return value.toLocaleString('tr-TR', { minimumFractionDigits });
  };

  // Para birimi formatlama
  const formatCurrency = (value: number | null): string => {
    if (value === null || value === undefined) return '-';
    return new Intl.NumberFormat('tr-TR', { 
      style: 'currency', 
      currency: 'TRY',
      minimumFractionDigits: 2 
    }).format(value);
  };

  // Tarihi formatlama
  const formatDate = (dateString: string): string => {
    return format(new Date(dateString), 'd MMMM yyyy, HH:mm', { locale: tr });
  };
  
  // Pozisyon sürelerini hesaplama
  const calculatePositionDuration = (receivedAt: string): string => {
    const startDate = new Date(receivedAt);
    const now = new Date();
    
    // Pozisyon süresini hesapla
    const duration = intervalToDuration({
      start: startDate,
      end: now
    });
    
    // Süreyi okunabilir formata dönüştür
    let formattedDuration = '';
    if (duration.years && duration.years > 0) {
      formattedDuration += `${duration.years} yıl `;
    }
    if (duration.months && duration.months > 0) {
      formattedDuration += `${duration.months} ay `;
    }
    if (duration.days && duration.days > 0) {
      formattedDuration += `${duration.days} gün `;
    }
    if (duration.hours && duration.hours > 0) {
      formattedDuration += `${duration.hours} saat `;
    }
    if (duration.minutes && duration.minutes > 0) {
      formattedDuration += `${duration.minutes} dk`;
    }
    
    return formattedDuration.trim();
  };

  // Pozisyonu kapatma fonksiyonu - Improved with 429 handling
  const handleClosePosition = async (trade: Trade, isRetry: boolean = false) => {
    // Rate limiting check (skip for retries)
    if (!isRetry && !canMakeRequest(trade.id)) {
      const timeUntilNextRequest = MIN_REQUEST_INTERVAL - (Date.now() - (lastRequestTimes[trade.id] || 0));
      const secondsToWait = Math.ceil(timeUntilNextRequest / 1000);
      
      toast({
        title: 'İstek Çok Hızlı',
        description: `Bu pozisyon için ${secondsToWait} saniye sonra tekrar deneyebilirsiniz.`,
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      
      console.log(`[OpenPositions] Rate limit: Position ${trade.id} must wait ${secondsToWait} seconds`);
      return;
    }

    // Prevent multiple simultaneous close requests for the same position
    if (isPositionClosing(trade.id)) {
      console.log(`[OpenPositions] Position ${trade.id} is already being closed, ignoring request`);
      return;
    }

    try {
      // Set loading state immediately to prevent multiple clicks
      setPositionClosing(trade.id, true);
      
      // Update last request time for rate limiting
      if (!isRetry) {
        updateLastRequestTime(trade.id);
      }
      
      console.log(`[OpenPositions] Attempting to close position for symbol: ${trade.symbol}, trade ID: ${trade.id}, isRetry: ${isRetry}`);
      
      // Önce settings kontrolü yap
      if (!settings) {
        throw new Error('Kullanıcı ayarları yüklenemedi. Lütfen sayfayı yenileyip tekrar deneyin.');
      }
      
      // Custom webhook URL kontrol et
      const customWebhookUrl = settings.custom_webhook_url;
      
      if (!customWebhookUrl) {
        throw new Error('Özel webhook URL\'i bulunamadı. Lütfen "Yönetim" sayfasından webhook URL\'inizi ekleyin.');
      }
      
      console.log('[OpenPositions] Calling get_decrypted_api_credentials for trade:', trade.id);
      
      // API kimlik bilgilerini çek
      const { data: credentialsData, error: credentialsError } = await supabase.rpc('get_decrypted_api_credentials');
      
      // YENİ: Ham RPC yanıtını logla
      console.log('[OpenPositions] RAW credentialsData from RPC:', JSON.stringify(credentialsData, null, 2));
      console.log('[OpenPositions] RAW credentialsError from RPC:', JSON.stringify(credentialsError, null, 2));
      
      // API anahtarları hata kontrolü - detaylı hata mesajı ve yönlendirme
      if (credentialsError) {
        console.error('[OpenPositions] RPC error fetching credentials for trade', trade.id, ':', credentialsError);
        throw new Error(`API kimlik bilgileri alınamadı: ${credentialsError.message}. Lütfen daha sonra tekrar deneyin veya "Yönetim" sayfasından kimlik bilgilerinizi güncelleyin.`);
      }
      
      // Kimlik bilgileri veri kontrolü
      if (!credentialsData || credentialsData.length === 0) {
        console.warn('[OpenPositions] API kimlik bilgileri bulunamadı:', credentialsData);
        throw new Error('API kimlik bilgileri Vault\'ta bulunamadı. Lütfen "Yönetim" sayfasından API anahtarı ve token bilgilerinizi kaydettiğinizden emin olun.');
      }
      
      const { api_key, api_token } = credentialsData[0];
      
      // API anahtarı veya token mevcudiyeti loglama
      console.log('[OpenPositions] Credentials check:', { 
        api_key_exists: !!api_key, 
        api_token_exists: !!api_token,
        api_key_length: api_key ? api_key.length : 0,
        api_token_length: api_token ? api_token.length : 0
      });
      
      // API anahtarı veya token eksikse
      if (!api_key && !api_token) {
        console.error('[OpenPositions] API anahtarı ve token eksik. credentialsData:', credentialsData);
        toast({
          title: 'API Bilgileri Gerekli',
          description: 'API anahtarı ve token bilgileri eksik. Lütfen "Yönetim" sayfasından API kimlik bilgilerinizi ekleyin.',
          status: 'warning',
          duration: 10000,
          isClosable: true,
        });
        throw new Error('API anahtarı ve token bilgileri eksik. Lütfen "Yönetim" sayfasından API kimlik bilgilerinizi ekleyin.');
      } else if (!api_key) {
        console.error('[OpenPositions] API anahtarı eksik. credentialsData:', credentialsData);
        toast({
          title: 'API Anahtarı Gerekli',
          description: 'API anahtarı eksik. Lütfen "Yönetim" sayfasından API anahtarınızı ekleyin.',
          status: 'warning',
          duration: 10000,
          isClosable: true,
        });
        throw new Error('API anahtarı eksik. Lütfen "Yönetim" sayfasından API anahtarınızı ekleyin.');
      } else if (!api_token) {
        console.error('[OpenPositions] Token eksik. credentialsData:', credentialsData);
        toast({
          title: 'API Token Gerekli',
          description: 'API token bilgisi eksik. Lütfen "Yönetim" sayfasından token bilginizi ekleyin.',
          status: 'warning',
          duration: 10000,
          isClosable: true,
        });
        throw new Error('API token eksik. Lütfen "Yönetim" sayfasından token bilginizi ekleyin.');
      }
      
      // JSON payload oluştur - manual close için enhanced payload
      const payload = {
        name: "ManualClose",
        symbol: trade.symbol,
        orderSide: "sell",
        orderType: "mktbest",
        price: trade.price?.toString() || "0",
        quantity: trade.calculated_quantity?.toString() || "0",
        timeInForce: "day",
        apiKey: api_key,
        token: api_token,
        // Manual close için additional metadata
        signal_type: "manual_close",
        source: "manual_close_ui",
        trade_id: trade.id,
        user_initiated: true
      };
      
      console.log(`[OpenPositions] ${trade.symbol} için pozisyon kapatma isteği gönderiliyor`);
      
      // Enhanced session check and refresh if needed
      let { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        console.log('[OpenPositions] Session bulunamadı, refresh token deneniyor...');
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
        
        if (refreshError || !refreshData.session) {
          console.error('[OpenPositions] Session refresh başarısız:', refreshError);
          throw new Error('Oturum süresi dolmuş. Lütfen yeniden giriş yapın.');
        }
        
        session = refreshData.session;
        console.log('[OpenPositions] Session başarıyla yenilendi');
      }
      
      // Verify session is valid and not expired
      const currentTime = Math.floor(Date.now() / 1000);
      if (session.expires_at && session.expires_at <= currentTime) {
        console.log('[OpenPositions] Session expired, refreshing...');
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
        
        if (refreshError || !refreshData.session) {
          console.error('[OpenPositions] Session refresh başarısız:', refreshError);
          throw new Error('Oturum süresi dolmuş. Lütfen yeniden giriş yapın.');
        }
        
        session = refreshData.session;
        console.log('[OpenPositions] Expired session başarıyla yenilendi');
      }
      
      const accessToken = session.access_token;
      console.log(`[OpenPositions] Access token length: ${accessToken?.length || 0}, expires_at: ${session.expires_at}`);
      
      // webhook-proxy Edge Function'ını kullan (authentication ile güvenli)
      const proxyUrl = 'https://fllklckmycxcgwhboiji.supabase.co/functions/v1/webhook-proxy';
      console.log(`[OpenPositions] Sending request to webhook-proxy: ${proxyUrl}`);
      
      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          targetUrl: customWebhookUrl,
          payload: payload
        })
      });
      
      console.log(`[OpenPositions] Webhook proxy yanıtı:`, response.status);
      
      let responseData;
      try {
        responseData = await response.json();
      } catch {
        // JSON parse edilemezse response text'ini al
        const responseText = await response.text();
        responseData = { response: responseText };
      }
      
      console.log(`[OpenPositions] Webhook proxy detayları:`, responseData);
      
      // Enhanced error handling for 401, 429 and other HTTP errors
      if (!response.ok || !responseData.success) {
        console.error(`[OpenPositions] Webhook hatası:`, responseData);
        
        // Specific handling for 401 Unauthorized
        if (response.status === 401) {
          throw new Error('Pozisyon kapatma yetkiniz bulunmuyor veya oturumunuz zaman aşımına uğradı. Lütfen tekrar giriş yapın.');
        }
        
        // Specific handling for 429 Too Many Requests - Improved for webhook.site
        if (response.status === 429 || responseData?.error?.includes('istek limitine ulaştı')) {
          const isWebhookSite = responseData?.response?.response?.includes?.('webhook.site') || 
                                responseData?.response?.includes?.('webhook.site') ||
                                customWebhookUrl?.includes?.('webhook.site');
          
          if (isWebhookSite) {
            throw new Error('Webhook.site servisi istek limitine ulaştı. Bu durum webhook.site\'nin ücretsiz kullanım limitlerinden kaynaklanır. Lütfen birkaç dakika bekleyin veya kendi webhook URL\'inizi kullanın.');
          } else {
            throw new Error('Çok fazla istek gönderildi. Lütfen birkaç dakika bekleyip tekrar deneyin. Bu durum sistemin aşırı yüklenmesini önlemek için oluşur.');
          }
        }
        
        // Check if retries were exhausted (from webhook-proxy)
        if (responseData?.retriesExhausted) {
          const isWebhookSite = responseData?.response?.response?.includes?.('webhook.site') || 
                                responseData?.response?.includes?.('webhook.site') ||
                                customWebhookUrl?.includes?.('webhook.site');
          
          if (isWebhookSite) {
            throw new Error('Webhook.site servisi sürekli istek limitine takılıyor. Bu durumda webhook.site premium hesabı gerekebilir veya alternatif bir webhook servisi kullanabilirsiniz.');
          } else {
            throw new Error('Webhook servisi sürekli olarak erişilemez durumda. Lütfen webhook URL\'inizi kontrol edin veya daha sonra tekrar deneyin.');
          }
        }
        
        // Specific handling for other HTTP status codes
        const errorMessage = responseData?.error || responseData?.message || `HTTP ${response.status}: ${response.statusText}`;
        throw new Error(`Kapama sinyali gönderilemedi: ${errorMessage}`);
      }
      
      // Pozisyon başarıyla kapatıldı, şimdi veritabanında manuel kapatma işlemini gerçekleştir
      console.log(`[OpenPositions] Webhook başarılı, şimdi manual_close_position RPC çağrılıyor, trade ID: ${trade.id}`);
      
      const { data: manualCloseData, error: manualCloseError } = await supabase.rpc(
        'manual_close_position',
        { p_trade_id: trade.id }
      );
      
      if (manualCloseError) {
        console.error(`[OpenPositions] Manuel kapatma RPC hatası:`, manualCloseError);
        // Webhook başarılı olduğu için kullanıcıya hata gösterme, sadece loglama yap
        console.warn(`[OpenPositions] Webhook başarılı oldu ancak veritabanı güncellemesi başarısız oldu. Trade ID: ${trade.id}`);
      } else {
        console.log(`[OpenPositions] Manuel kapatma RPC sonucu:`, manualCloseData);
        if (manualCloseData?.pnl) {
          console.log(`[OpenPositions] Hesaplanan K/Z: ${manualCloseData.pnl}`);
        }
      }
      
      // Success case - reset retry attempts
      resetRetryAttempts(trade.id);
      
      toast({
        title: 'Başarılı',
        description: `${trade.symbol} için kapama sinyali gönderildi!`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      
      // Veriyi yenile
      setTimeout(() => {
        refetch();
      }, 2000);
      
    } catch (err: any) {
      console.error('[OpenPositions] Pozisyon kapatma hatası:', err);
      
      // Check if this is a 429 error and we can retry
      const is429Error = err.message && (
        err.message.includes('Çok fazla istek') || 
        err.message.includes('istek limitine ulaştı') ||
        err.message.includes('webhook.site')
      );
      const currentAttempts = retryAttempts[trade.id] || 0;
      const canRetry = is429Error && currentAttempts < MAX_RETRY_ATTEMPTS && !isRetry;
      
      if (canRetry) {
        const attemptNumber = incrementRetryAttempt(trade.id);
        
        // Enhanced backoff delay calculation for webhook services
        const isWebhookSite = err.message.includes('webhook.site');
        let backoffDelay = calculateBackoffDelay(attemptNumber);
        
        // For webhook.site rate limits, use longer delays
        if (isWebhookSite) {
          backoffDelay = Math.max(backoffDelay, 30000 * attemptNumber); // 30s, 60s, 90s for webhook.site
        }
        
        console.log(`[OpenPositions] 429 error detected, scheduling retry ${attemptNumber}/${MAX_RETRY_ATTEMPTS} for position ${trade.id} after ${backoffDelay}ms`);
        
        toast({
          title: 'Tekrar Deneme',
          description: isWebhookSite 
            ? `Webhook.site istek limitine takıldı. ${Math.ceil(backoffDelay / 1000)} saniye sonra otomatik olarak tekrar denenecek... (${attemptNumber}/${MAX_RETRY_ATTEMPTS})`
            : `İstek limitine takıldı. ${Math.ceil(backoffDelay / 1000)} saniye sonra otomatik olarak tekrar denenecek... (${attemptNumber}/${MAX_RETRY_ATTEMPTS})`,
          status: 'info',
          duration: Math.min(backoffDelay, 10000), // Maximum 10 seconds for toast display
          isClosable: true,
        });
        
        // Keep loading state during backoff
        setTimeout(async () => {
          try {
            await handleClosePosition(trade, true); // Retry with isRetry=true
          } catch (retryError) {
            console.error(`[OpenPositions] Retry attempt ${attemptNumber} failed for position ${trade.id}:`, retryError);
            // If retry also fails, this will be handled by the final catch below
          }
        }, backoffDelay);
        
        return; // Don't show error toast or reset loading state yet
      }
      
      // Enhanced error handling with specific messages for different error types
      let errorTitle = 'Pozisyon Kapatma Hatası';
      let errorDescription = err.message || 'Pozisyon kapatılırken bir hata oluştu.';
      let errorStatus: 'error' | 'warning' = 'error';
      let errorDuration = 7000;
      
      // Check if this is a webhook.site URL at the beginning
      const isWebhookSite = err.message.includes('webhook.site');
      
      // Handle 429 errors specifically
      if (is429Error) {
        if (currentAttempts >= MAX_RETRY_ATTEMPTS) {
          errorTitle = 'Maksimum Deneme Sayısına Ulaşıldı';
          if (isWebhookSite) {
            errorDescription = `Webhook.site servisi sürekli istek limitine takılıyor. Bu durumda alternatif çözümler:
            
• Webhook.site premium hesabı edinebilirsiniz
• RequestBin.com veya ngrok.io gibi alternatif webhook servislerini kullanabilirsiniz
• Kendinize ait bir webhook endpoint'i kurabilirsiniz
• Birkaç dakika bekleyip manuel olarak tekrar deneyebilirsiniz`;
          } else {
            errorDescription = `${MAX_RETRY_ATTEMPTS} kez denendi ancak başarısız oldu. Lütfen birkaç dakika bekleyip manuel olarak tekrar deneyin.`;
          }
          resetRetryAttempts(trade.id); // Reset for future attempts
        } else {
          errorTitle = 'İstek Limiti Aşıldı';
          if (isWebhookSite) {
            errorDescription = 'Webhook.site ücretsiz kullanım limiti aşıldı. Tekrar deneme devam ediyor...';
          }
        }
        errorStatus = 'warning';
        errorDuration = isWebhookSite ? 15000 : 10000; // Webhook.site için daha uzun gösterim
      }
      
      toast({
        title: errorTitle,
        description: errorDescription,
        status: errorStatus,
        duration: errorDuration,
        isClosable: true,
      });
    } finally {
      // Always reset loading state in finally block (unless we're in a retry scenario)
      if (!isRetry || (retryAttempts[trade.id] || 0) >= MAX_RETRY_ATTEMPTS) {
        setPositionClosing(trade.id, false);
      }
    }
  };

  // Mobile Position Card Component
  const PositionCard = ({ position }: { position: Trade }) => (
    <Card
      p={{ base: '16px', md: '20px' }}
      mb="12px"
      bg={cardBg}
      borderRadius="16px"
      boxShadow="0px 8px 24px rgba(112, 144, 176, 0.08)"
      border="1px solid"
      borderColor={borderColor}
      position="relative"
    >
      {/* Header Row */}
      <Flex justify="space-between" align="flex-start" mb="12px">
        <VStack align="flex-start" spacing="4px" flex="1">
          <HStack spacing="8px">
            <SignalSourceIcon trade={position} />
            <Text fontSize="lg" fontWeight="700" color={textColor}>
              {position.symbol}
            </Text>
            <Badge
              colorScheme="green"
              fontSize="xs"
              px="8px"
              py="2px"
              borderRadius="6px"
            >
              AÇIK
            </Badge>
          </HStack>
          <Text fontSize="sm" color={secondaryTextColor}>
            {formatDate(position.received_at)}
          </Text>
        </VStack>
        
        {/* Close Button */}
        <Button
          size="sm"
          colorScheme="red"
          isLoading={isPositionClosing(position.id)}
          loadingText="Kapatılıyor..."
          disabled={isPositionClosing(position.id)}
          onClick={() => handleClosePosition(position)}
          fontSize="xs"
          px="12px"
          h="32px"
        >
          {isPositionClosing(position.id) ? 'Kapatılıyor...' : 'Kapat'}
        </Button>
      </Flex>

      {/* Details Grid */}
      <VStack spacing="8px" align="stretch">
        <Flex justify="space-between">
          <Text fontSize="sm" color={secondaryTextColor}>Lot Sayısı:</Text>
          <Text fontSize="sm" fontWeight="500" color={textColor}>
            {position.calculated_quantity ? 
              Math.floor(position.calculated_quantity).toLocaleString('tr-TR', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
              }) : '-'}
          </Text>
        </Flex>
        
        <Flex justify="space-between">
          <Text fontSize="sm" color={secondaryTextColor}>Alış Fiyatı:</Text>
          <Text fontSize="sm" fontWeight="500" color={textColor}>
            {formatNumber(position.price)}
          </Text>
        </Flex>
        
        <Flex justify="space-between" align="center" pt="4px" borderTop="1px solid" borderColor={borderColor}>
          <Text fontSize="sm" color={secondaryTextColor} fontWeight="600">Toplam Tutar:</Text>
          <Text fontSize="sm" fontWeight="700" color={textColor}>
            {position.price && position.calculated_quantity 
              ? formatCurrency(position.price * Math.floor(position.calculated_quantity))
              : '-'}
          </Text>
        </Flex>
        
        <Flex justify="space-between" align="center">
          <Text fontSize="sm" color={secondaryTextColor}>Pozisyon Süresi:</Text>
          <HStack spacing="4px">
            <Icon as={MdTimer} color="blue.500" boxSize={3} />
            <Badge 
              colorScheme="blue" 
              fontSize="xs" 
              borderRadius="6px"
              py="2px"
              px="6px"
            >
              {calculatePositionDuration(position.received_at)}
            </Badge>
          </HStack>
        </Flex>
      </VStack>
    </Card>
  );

  // Yükleniyor durumu
  if (isLoading) {
    return (
      <Box>
        <Card 
          mb='20px'
          boxShadow={cardShadow}
        >
          <Center py={10}>
            <Spinner thickness='4px' speed='0.65s' size='xl' color='brand.500' />
            <Text ml={4} color={textColor} fontWeight="500">Açık pozisyonlar yükleniyor...</Text>
          </Center>
        </Card>
      </Box>
    );
  }

  // Hata durumu
  if (error) {
    return (
      <Box>
        <Card 
          mb='20px'
          boxShadow={cardShadow}
        >
          <Box py={4}>
            <Alert status="error" borderRadius="12px">
              <AlertIcon />
              {error}
            </Alert>
          </Box>
        </Card>
      </Box>
    );
  }

  // Veri yoksa
  if (positions.length === 0) {
    return (
      <Box>
        <Card 
          mb='20px'
          boxShadow={cardShadow}
        >
          <Box py={4}>
            <Alert status="info" borderRadius="12px">
              <AlertIcon />
              Açık pozisyon bulunmuyor.
            </Alert>
          </Box>
        </Card>
      </Box>
    );
  }

  // Ana bileşen
  return (
    <Box>
      <Card 
        flexDirection='column' 
        w='100%' 
        px='0px' 
        overflowX={{ base: 'visible', lg: 'hidden' }}
        boxShadow={cardShadow}
        mb="20px"
        overflow="hidden"
      >
        <Flex 
          px={{ base: '16px', md: '25px' }}
          justify='space-between' 
          mb='20px' 
          py="16px" 
          borderBottom="1px solid" 
          borderColor={borderColor}
        >
          <Text
            color={textColor}
            fontSize={{ base: 'md', md: 'lg' }}
            fontWeight='700'
            lineHeight='100%'
          >
            Açık İşlemler
            <Badge ml={2} colorScheme="green" fontSize="sm" py={1} px={2} borderRadius="8px">
              {positions.length}
            </Badge>
          </Text>
        </Flex>

        {isMobile ? (
          // Mobile Card View
          <Box px="16px" pb="16px">
            {positions.length === 0 ? (
              <Text textAlign="center" color={secondaryTextColor} py="40px">
                Açık pozisyon bulunmuyor.
              </Text>
            ) : (
              <VStack spacing="0">
                {positions.map((position) => (
                  <PositionCard key={position.id} position={position} />
                ))}
              </VStack>
            )}
          </Box>
        ) : (
          // Desktop Table View
          <Box overflowX="auto">
            <Table variant='simple' color='gray.500' mb='0px' minW="900px">
              <Thead>
                <Tr>
                  <Th
                    borderColor={borderColor}
                    cursor='default'
                    py={{ base: '12px', md: '14px' }}
                    px={{ base: '12px', md: '16px' }}
                    bg={headerBg}
                    borderBottom="1px solid"
                    borderBottomColor={borderColor}
                    fontSize={{ base: '10px', md: '12px' }}
                    _first={{
                      pl: { base: '16px', md: '24px' }
                    }}
                  >
                    <Flex align='center'>
                      <Text
                        fontSize={{ sm: '10px', lg: '12px' }}
                        color={headerTextColor}
                        fontWeight="700"
                        textTransform="uppercase">
                        HİSSE ADI
                      </Text>
                    </Flex>
                  </Th>
                  <Th
                    borderColor={borderColor}
                    cursor='default'
                    py={{ base: '12px', md: '14px' }}
                    px={{ base: '12px', md: '16px' }}
                    bg={headerBg}
                    borderBottom="1px solid"
                    borderBottomColor={borderColor}
                    fontSize={{ base: '10px', md: '12px' }}
                  >
                    <Flex align='center' justifyContent='center'>
                      <Text
                        fontSize={{ sm: '10px', lg: '12px' }}
                        color={headerTextColor}
                        fontWeight="700"
                        textTransform="uppercase">
                        KAYNAK
                      </Text>
                    </Flex>
                  </Th>
                  <Th
                    borderColor={borderColor}
                    cursor='default'
                    py={{ base: '12px', md: '14px' }}
                    px={{ base: '12px', md: '16px' }}
                    bg={headerBg}
                    borderBottom="1px solid"
                    borderBottomColor={borderColor}
                    fontSize={{ base: '10px', md: '12px' }}
                  >
                    <Flex align='center' justifyContent='flex-end'>
                      <Text
                        fontSize={{ sm: '10px', lg: '12px' }}
                        color={headerTextColor}
                        fontWeight="700"
                        textTransform="uppercase">
                        LOT SAYISI
                      </Text>
                    </Flex>
                  </Th>
                  <Th
                    borderColor={borderColor}
                    cursor='default'
                    py={{ base: '12px', md: '14px' }}
                    px={{ base: '12px', md: '16px' }}
                    bg={headerBg}
                    borderBottom="1px solid"
                    borderBottomColor={borderColor}
                    fontSize={{ base: '10px', md: '12px' }}
                  >
                    <Flex align='center' justifyContent='flex-end'>
                      <Text
                        fontSize={{ sm: '10px', lg: '12px' }}
                        color={headerTextColor}
                        fontWeight="700"
                        textTransform="uppercase">
                        ALIŞ FİYATI
                      </Text>
                    </Flex>
                  </Th>
                  <Th
                    borderColor={borderColor}
                    cursor='default'
                    py={{ base: '12px', md: '14px' }}
                    px={{ base: '12px', md: '16px' }}
                    bg={headerBg}
                    borderBottom="1px solid"
                    borderBottomColor={borderColor}
                    fontSize={{ base: '10px', md: '12px' }}
                  >
                    <Flex align='center' justifyContent='flex-end'>
                      <Text
                        fontSize={{ sm: '10px', lg: '12px' }}
                        color={headerTextColor}
                        fontWeight="700"
                        textTransform="uppercase">
                        TOPLAM TUTAR
                      </Text>
                    </Flex>
                  </Th>
                  <Th
                    borderColor={borderColor}
                    cursor='default'
                    py={{ base: '12px', md: '14px' }}
                    px={{ base: '12px', md: '16px' }}
                    bg={headerBg}
                    borderBottom="1px solid"
                    borderBottomColor={borderColor}
                    fontSize={{ base: '10px', md: '12px' }}
                  >
                    <Flex align='center'>
                      <Text
                        fontSize={{ sm: '10px', lg: '12px' }}
                        color={headerTextColor}
                        fontWeight="700"
                        textTransform="uppercase">
                        ALIŞ TARİHİ
                      </Text>
                    </Flex>
                  </Th>
                  <Th
                    borderColor={borderColor}
                    cursor='default'
                    py={{ base: '12px', md: '14px' }}
                    px={{ base: '12px', md: '16px' }}
                    bg={headerBg}
                    borderBottom="1px solid"
                    borderBottomColor={borderColor}
                    fontSize={{ base: '10px', md: '12px' }}
                  >
                    <Flex align='center'>
                      <Text
                        fontSize={{ sm: '10px', lg: '12px' }}
                        color={headerTextColor}
                        fontWeight="700"
                        textTransform="uppercase">
                        POZİSYON SÜRESİ
                      </Text>
                    </Flex>
                  </Th>
                  <Th
                    borderColor={borderColor}
                    cursor='default'
                    py={{ base: '12px', md: '14px' }}
                    px={{ base: '12px', md: '16px' }}
                    bg={headerBg}
                    borderBottom="1px solid"
                    borderBottomColor={borderColor}
                    fontSize={{ base: '10px', md: '12px' }}
                    _last={{
                      pr: { base: '16px', md: '24px' }
                    }}
                  >
                    <Flex align='center' justifyContent='center'>
                      <Text
                        fontSize={{ sm: '10px', lg: '12px' }}
                        color={headerTextColor}
                        fontWeight="700"
                        textTransform="uppercase">
                        İŞLEMLER
                      </Text>
                    </Flex>
                  </Th>
                </Tr>
              </Thead>
              <Tbody>
                {positions.map((position, rowIndex) => (
                  <Tr 
                    key={position.id}
                    _hover={{ bg: hoverRowBg }}
                    transition="all 0.2s ease"
                    bg={rowIndex % 2 === 0 ? 'transparent' : stripedRowBg}
                    cursor="default"
                  >
                    <Td
                      borderColor={borderColor}
                      py={{ base: '12px', md: '16px' }}
                      px={{ base: '12px', md: '16px' }}
                      borderBottom="1px solid"
                      pl={{ base: '16px', md: '24px' }}
                      fontSize={{ base: '12px', md: '14px' }}
                    >
                      <Text color={textColor} fontSize='sm' fontWeight='700'>
                        {position.symbol}
                      </Text>
                    </Td>
                    <Td
                      borderColor={borderColor}
                      py={{ base: '12px', md: '16px' }}
                      px={{ base: '12px', md: '16px' }}
                      borderBottom="1px solid"
                      textAlign="center"
                      fontSize={{ base: '12px', md: '14px' }}
                    >
                      <Box display="flex" justifyContent="center">
                        <SignalSourceIcon trade={position} />
                      </Box>
                    </Td>
                    <Td
                      borderColor={borderColor}
                      py={{ base: '12px', md: '16px' }}
                      px={{ base: '12px', md: '16px' }}
                      borderBottom="1px solid"
                      isNumeric
                      fontSize={{ base: '12px', md: '14px' }}
                    >
                      <Text color={textColor} fontSize='sm' fontWeight='500' textAlign='right'>
                        {position.calculated_quantity ? 
                          Math.floor(position.calculated_quantity).toLocaleString('tr-TR', {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0
                          }) : '-'}
                      </Text>
                    </Td>
                    <Td
                      borderColor={borderColor}
                      py={{ base: '12px', md: '16px' }}
                      px={{ base: '12px', md: '16px' }}
                      borderBottom="1px solid"
                      isNumeric
                      fontSize={{ base: '12px', md: '14px' }}
                    >
                      <Text color={textColor} fontSize='sm' fontWeight='500' textAlign='right'>
                        {formatNumber(position.price)}
                      </Text>
                    </Td>
                    <Td
                      borderColor={borderColor}
                      py={{ base: '12px', md: '16px' }}
                      px={{ base: '12px', md: '16px' }}
                      borderBottom="1px solid"
                      isNumeric
                      fontSize={{ base: '12px', md: '14px' }}
                    >
                      <Text color={textColor} fontSize='sm' fontWeight='700' textAlign='right'>
                        {position.price && position.calculated_quantity 
                          ? formatCurrency(position.price * Math.floor(position.calculated_quantity))
                          : '-'}
                      </Text>
                    </Td>
                    <Td
                      borderColor={borderColor}
                      py={{ base: '12px', md: '16px' }}
                      px={{ base: '12px', md: '16px' }}
                      borderBottom="1px solid"
                      fontSize={{ base: '12px', md: '14px' }}
                    >
                      <Text color={textColor} fontSize='sm' fontWeight='500'>
                        {formatDate(position.received_at)}
                      </Text>
                    </Td>
                    <Td
                      borderColor={borderColor}
                      py={{ base: '12px', md: '16px' }}
                      px={{ base: '12px', md: '16px' }}
                      borderBottom="1px solid"
                      fontSize={{ base: '12px', md: '14px' }}
                    >
                      <Flex align="center">
                        <Icon as={MdTimer} color="blue.500" boxSize={4} me='6px' />
                        <Badge 
                          colorScheme="blue" 
                          fontSize="xs" 
                          borderRadius="8px"
                          py="4px"
                          px="10px"
                          textTransform="capitalize"
                          letterSpacing="0.5px"
                        >
                          {calculatePositionDuration(position.received_at)}
                        </Badge>
                      </Flex>
                    </Td>
                    <Td
                      borderColor={borderColor}
                      py={{ base: '12px', md: '16px' }}
                      px={{ base: '12px', md: '16px' }}
                      borderBottom="1px solid"
                      textAlign="center"
                      pr={{ base: '16px', md: '24px' }}
                      fontSize={{ base: '12px', md: '14px' }}
                    >
                      <Button
                        size={{ base: 'xs', md: 'sm' }}
                        colorScheme="red"
                        isLoading={isPositionClosing(position.id)}
                        loadingText="Kapatılıyor..."
                        disabled={isPositionClosing(position.id)}
                        onClick={() => handleClosePosition(position)}
                        fontSize={{ base: '11px', md: '13px' }}
                        px={{ base: '8px', md: '12px' }}
                        h={{ base: '28px', md: '32px' }}
                      >
                        {isPositionClosing(position.id) ? 'Kapatılıyor...' : 'Kapat'}
                      </Button>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>
        )}
      </Card>
    </Box>
  );
};

export default OpenPositions; 