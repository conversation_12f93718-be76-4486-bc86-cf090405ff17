import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,

  useColorModeValue,

  Badge,
  Flex,
  Spacer,

  Switch,
  FormControl,
  FormLabel,
  Select,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb
} from '@chakra-ui/react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  ReferenceLine,
  Cell
} from 'recharts';
import { SymbolMetrics } from '../../../types/symbolAnalysis';

interface SymbolRiskReturnScatterPlotProps {
  data: SymbolMetrics[];
  height?: number;
  onSymbolClick?: (symbol: string) => void;
  selectedSymbols?: string[];
  showBenchmark?: boolean;
  benchmarkReturn?: number;
  benchmarkRisk?: number;
}

type QuadrantType = 'high-risk-high-return' | 'high-risk-low-return' | 'low-risk-high-return' | 'low-risk-low-return';

interface ScatterDataPoint {
  symbol: string;
  x: number; // Risk (volatility)
  y: number; // Return (ROI)
  z: number; // Size (total investment or trade count)
  quadrant: QuadrantType;
  color: string;
  totalPnl: number;
  totalTrades: number;
  winRate: number;
  sharpeRatio: number;
}

const SymbolRiskReturnScatterPlot: React.FC<SymbolRiskReturnScatterPlotProps> = ({
  data,
  height = 500,
  onSymbolClick,
  selectedSymbols = [],
  showBenchmark = true,
  benchmarkReturn = 5, // Default 5% return
  benchmarkRisk = 10 // Default 10% risk
}) => {
  const [sizeMetric, setSizeMetric] = useState<'totalInvestment' | 'totalTrades'>('totalInvestment');
  const [showQuadrants, setShowQuadrants] = useState(true);
  const [showLabels, setShowLabels] = useState(true);
  const [bubbleScale, setBubbleScale] = useState(1);
  const [filterQuadrant, setFilterQuadrant] = useState<QuadrantType | 'all'>('all');

  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const gridColor = useColorModeValue('#f0f0f0', '#2d3748');

  // Quadrant colors
  const quadrantColors = {
    'high-risk-high-return': '#48BB78', // Green - Good
    'low-risk-high-return': '#38A169',  // Dark Green - Best
    'high-risk-low-return': '#F56565',  // Red - Bad
    'low-risk-low-return': '#ED8936'    // Orange - Neutral
  };

  // Calculate scatter plot data
  const scatterData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Calculate average return and risk for quadrant division
    const avgReturn = data.reduce((sum, item) => sum + item.roi, 0) / data.length;
    const avgRisk = data.reduce((sum, item) => sum + item.volatility, 0) / data.length;

    // Use benchmark if provided, otherwise use averages
    const returnThreshold = showBenchmark ? benchmarkReturn : avgReturn;
    const riskThreshold = showBenchmark ? benchmarkRisk : avgRisk;

    const processedData: ScatterDataPoint[] = data.map(item => {
      // Determine quadrant
      const isHighReturn = item.roi > returnThreshold;
      const isHighRisk = item.volatility > riskThreshold;
      
      let quadrant: QuadrantType;
      if (isHighRisk && isHighReturn) {
        quadrant = 'high-risk-high-return';
      } else if (!isHighRisk && isHighReturn) {
        quadrant = 'low-risk-high-return';
      } else if (isHighRisk && !isHighReturn) {
        quadrant = 'high-risk-low-return';
      } else {
        quadrant = 'low-risk-low-return';
      }

      return {
        symbol: item.symbol,
        x: item.volatility,
        y: item.roi,
        z: sizeMetric === 'totalInvestment' ? item.totalInvestment : item.totalTrades,
        quadrant,
        color: quadrantColors[quadrant],
        totalPnl: item.totalPnl,
        totalTrades: item.totalTrades,
        winRate: item.winRate,
        sharpeRatio: item.sharpeRatio
      };
    });

    // Filter by quadrant if selected
    if (filterQuadrant !== 'all') {
      return processedData.filter(item => item.quadrant === filterQuadrant);
    }

    return processedData;
  }, [data, sizeMetric, showBenchmark, benchmarkReturn, benchmarkRisk, filterQuadrant]);

  // Calculate bubble size
  const calculateBubbleSize = (value: number, minSize = 50, maxSize = 400) => {
    if (scatterData.length === 0) return minSize;
    
    const values = scatterData.map(d => d.z);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    
    if (maxValue === minValue) return minSize;
    
    const normalizedValue = (value - minValue) / (maxValue - minValue);
    return (minSize + (maxSize - minSize) * normalizedValue) * bubbleScale;
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload.length) return null;

    const data = payload[0].payload;
    
    return (
      <Box
        bg={bgColor}
        p={4}
        borderRadius="md"
        boxShadow="lg"
        border="1px solid"
        borderColor={borderColor}
        minW="250px"
      >
        <Text fontWeight="bold" mb={2} color={textColor}>
          {data.symbol}
        </Text>
        <VStack align="start" spacing={1}>
          <Text fontSize="sm" color={textColor}>
            <strong>Risk (Volatilite):</strong> {data.x.toFixed(2)}%
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>Return (ROI):</strong> {data.y.toFixed(2)}%
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>Sharpe Oranı:</strong> {data.sharpeRatio.toFixed(3)}
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>Toplam P&L:</strong> ₺{data.totalPnl.toFixed(2)}
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>İşlem Sayısı:</strong> {data.totalTrades}
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>Kazanma Oranı:</strong> {data.winRate.toFixed(1)}%
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>Yatırım:</strong> ₺{(sizeMetric === 'totalInvestment' ? data.z : 0).toFixed(0)}
          </Text>
          <Badge colorScheme={getQuadrantColorScheme(data.quadrant)} variant="subtle">
            {getQuadrantLabel(data.quadrant)}
          </Badge>
        </VStack>
      </Box>
    );
  };

  // Get quadrant color scheme for badges
  const getQuadrantColorScheme = (quadrant: QuadrantType) => {
    switch (quadrant) {
      case 'low-risk-high-return': return 'green';
      case 'high-risk-high-return': return 'blue';
      case 'low-risk-low-return': return 'orange';
      case 'high-risk-low-return': return 'red';
      default: return 'gray';
    }
  };

  // Get quadrant label
  const getQuadrantLabel = (quadrant: QuadrantType) => {
    switch (quadrant) {
      case 'low-risk-high-return': return 'Düşük Risk - Yüksek Getiri';
      case 'high-risk-high-return': return 'Yüksek Risk - Yüksek Getiri';
      case 'low-risk-low-return': return 'Düşük Risk - Düşük Getiri';
      case 'high-risk-low-return': return 'Yüksek Risk - Düşük Getiri';
      default: return '';
    }
  };

  // Handle bubble click
  const handleBubbleClick = (data: any) => {
    onSymbolClick?.(data.symbol);
  };

  // Calculate reference lines
  const avgReturn = scatterData.length > 0 ? 
    scatterData.reduce((sum, item) => sum + item.y, 0) / scatterData.length : 0;
  const avgRisk = scatterData.length > 0 ? 
    scatterData.reduce((sum, item) => sum + item.x, 0) / scatterData.length : 0;

  const returnLine = showBenchmark ? benchmarkReturn : avgReturn;
  const riskLine = showBenchmark ? benchmarkRisk : avgRisk;

  return (
    <VStack spacing={4} align="stretch">
      {/* Controls */}
      <Flex wrap="wrap" gap={4} align="center">
        {/* Size Metric */}
        <HStack>
          <Text fontSize="sm" fontWeight="medium" color={textColor}>
            Bubble Boyutu:
          </Text>
          <Select
            value={sizeMetric}
            onChange={(e) => setSizeMetric(e.target.value as 'totalInvestment' | 'totalTrades')}
            size="sm"
            w="150px"
          >
            <option value="totalInvestment">Yatırım Tutarı</option>
            <option value="totalTrades">İşlem Sayısı</option>
          </Select>
        </HStack>

        {/* Quadrant Filter */}
        <HStack>
          <Text fontSize="sm" fontWeight="medium" color={textColor}>
            Kadran:
          </Text>
          <Select
            value={filterQuadrant}
            onChange={(e) => setFilterQuadrant(e.target.value as QuadrantType | 'all')}
            size="sm"
            w="200px"
          >
            <option value="all">Tümü</option>
            <option value="low-risk-high-return">Düşük Risk - Yüksek Getiri</option>
            <option value="high-risk-high-return">Yüksek Risk - Yüksek Getiri</option>
            <option value="low-risk-low-return">Düşük Risk - Düşük Getiri</option>
            <option value="high-risk-low-return">Yüksek Risk - Düşük Getiri</option>
          </Select>
        </HStack>

        <Spacer />

        {/* Options */}
        <HStack>
          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="quadrants" mb="0" fontSize="sm">
              Kadranlar
            </FormLabel>
            <Switch
              id="quadrants"
              isChecked={showQuadrants}
              onChange={(e) => setShowQuadrants(e.target.checked)}
              size="sm"
            />
          </FormControl>

          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="labels" mb="0" fontSize="sm">
              Etiketler
            </FormLabel>
            <Switch
              id="labels"
              isChecked={showLabels}
              onChange={(e) => setShowLabels(e.target.checked)}
              size="sm"
            />
          </FormControl>
        </HStack>
      </Flex>

      {/* Bubble Scale Slider */}
      <HStack>
        <Text fontSize="sm" color={textColor}>
          Bubble Ölçeği:
        </Text>
        <Box w="200px">
          <Slider
            value={bubbleScale}
            onChange={setBubbleScale}
            min={0.5}
            max={2}
            step={0.1}
          >
            <SliderTrack>
              <SliderFilledTrack />
            </SliderTrack>
            <SliderThumb />
          </Slider>
        </Box>
        <Text fontSize="sm" color={textColor}>
          {bubbleScale.toFixed(1)}x
        </Text>
      </HStack>

      {/* Chart */}
      <Box h={`${height}px`} w="100%">
        <ResponsiveContainer width="100%" height="100%">
          <ScatterChart
            data={scatterData}
            margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
            <XAxis
              type="number"
              dataKey="x"
              name="Risk (Volatilite)"
              unit="%"
              stroke={textColor}
              fontSize={12}
              label={{ value: 'Risk (Volatilite %)', position: 'insideBottom', offset: -10 }}
            />
            <YAxis
              type="number"
              dataKey="y"
              name="Return (ROI)"
              unit="%"
              stroke={textColor}
              fontSize={12}
              label={{ value: 'Return (ROI %)', angle: -90, position: 'insideLeft' }}
            />
            <RechartsTooltip content={<CustomTooltip />} />
            
            {/* Reference Lines for Quadrants */}
            {showQuadrants && (
              <>
                <ReferenceLine
                  x={riskLine}
                  stroke="#666"
                  strokeDasharray="5 5"
                  label={{ value: "Ortalama Risk", position: "top" }}
                />
                <ReferenceLine
                  y={returnLine}
                  stroke="#666"
                  strokeDasharray="5 5"
                  label={{ value: "Ortalama Getiri", position: "top" }}
                />
              </>
            )}

            <Scatter
              name="Semboller"
              data={scatterData}
              fill="#8884d8"
              onClick={handleBubbleClick}
            >
              {scatterData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={entry.color}
                  stroke={selectedSymbols.includes(entry.symbol) ? '#2B6CB0' : 'none'}
                  strokeWidth={selectedSymbols.includes(entry.symbol) ? 3 : 0}
                  r={calculateBubbleSize(entry.z)}
                  style={{ cursor: 'pointer' }}
                />
              ))}
            </Scatter>
          </ScatterChart>
        </ResponsiveContainer>
      </Box>

      {/* Quadrant Legend */}
      {showQuadrants && (
        <Box>
          <Text fontSize="sm" fontWeight="medium" mb={2} color={textColor}>
            Kadran Analizi:
          </Text>
          <Flex wrap="wrap" gap={4}>
            {Object.entries(quadrantColors).map(([quadrant, color]) => {
              const count = scatterData.filter(d => d.quadrant === quadrant).length;
              return (
                <HStack key={quadrant}>
                  <Box w={4} h={4} borderRadius="full" bg={color} />
                  <Text fontSize="sm" color={textColor}>
                    {getQuadrantLabel(quadrant as QuadrantType)} ({count})
                  </Text>
                </HStack>
              );
            })}
          </Flex>
        </Box>
      )}

      {/* Summary Stats */}
      <HStack justify="space-between" fontSize="sm" color="gray.500">
        <Text>
          Toplam Sembol: {scatterData.length}
        </Text>
        <Text>
          Ortalama ROI: {avgReturn.toFixed(2)}%
        </Text>
        <Text>
          Ortalama Risk: {avgRisk.toFixed(2)}%
        </Text>
        <Text>
          En İyi Sharpe: {scatterData.length > 0 ? 
            Math.max(...scatterData.map(d => d.sharpeRatio)).toFixed(3) : 'N/A'}
        </Text>
      </HStack>
    </VStack>
  );
};

export default SymbolRiskReturnScatterPlot;
