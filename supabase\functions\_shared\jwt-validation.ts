/**
 * JWT Validation Utilities for Supabase Edge Functions
 * Enhanced security with comprehensive token validation
 */

import { createClient } from 'jsr:@supabase/supabase-js@2';

interface JWTValidationResult {
  isValid: boolean;
  user?: any;
  error?: string;
  userId?: string;
  email?: string;
  role?: string;
}

interface JWTPayload {
  sub: string;
  email?: string;
  role?: string;
  aud: string;
  exp: number;
  iat: number;
  iss: string;
  [key: string]: any;
}

// Rate limiting for JWT validation attempts
const jwtRateLimitMap = new Map<string, { count: number; resetTime: number }>();
const JWT_RATE_LIMIT = 50; // attempts per minute per IP
const JWT_RATE_WINDOW = 60 * 1000; // 1 minute

const checkJWTRateLimit = (identifier: string): boolean => {
  const now = Date.now();
  const entry = jwtRateLimitMap.get(identifier);
  
  if (!entry || now > entry.resetTime) {
    jwtRateLimitMap.set(identifier, { count: 1, resetTime: now + JWT_RATE_WINDOW });
    return true;
  }
  
  if (entry.count >= JWT_RATE_LIMIT) {
    return false;
  }
  
  entry.count++;
  return true;
};

// Extract JWT token from Authorization header
export function extractJWTToken(request: Request): string | null {
  const authHeader = request.headers.get('Authorization');
  
  if (!authHeader) {
    return null;
  }
  
  // Support both "Bearer token" and "token" formats
  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Direct token (for backward compatibility)
  return authHeader;
}

// Basic JWT format validation (client-side checks)
export function isValidJWTFormat(token: string): boolean {
  if (!token || typeof token !== 'string') {
    return false;
  }
  
  const parts = token.split('.');
  if (parts.length !== 3) {
    return false;
  }
  
  try {
    // Validate each part is valid base64
    parts.forEach(part => {
      if (!part) throw new Error('Empty JWT part');
      // Convert URL-safe base64 to regular base64
      const base64 = part.replace(/-/g, '+').replace(/_/g, '/');
      atob(base64);
    });
    return true;
  } catch {
    return false;
  }
}

// Decode JWT payload without verification (for basic checks)
export function decodeJWTPayload(token: string): JWTPayload | null {
  if (!isValidJWTFormat(token)) {
    return null;
  }
  
  try {
    const payload = token.split('.')[1];
    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
    const decoded = atob(base64);
    return JSON.parse(decoded) as JWTPayload;
  } catch {
    return null;
  }
}

// Check if JWT is expired
export function isJWTExpired(token: string): boolean {
  const payload = decodeJWTPayload(token);
  if (!payload || !payload.exp) {
    return true;
  }
  
  // Add 30 second buffer for clock skew
  const now = Math.floor(Date.now() / 1000) + 30;
  return payload.exp < now;
}

// Validate JWT with Supabase
export async function validateJWTWithSupabase(
  token: string,
  request?: Request
): Promise<JWTValidationResult> {
  // Rate limiting check
  const clientIP = request?.headers.get('x-forwarded-for') || 
                   request?.headers.get('x-real-ip') || 
                   'unknown';
  
  if (!checkJWTRateLimit(clientIP)) {
    console.warn(`[JWT] Rate limit exceeded for IP: ${clientIP}`);
    return {
      isValid: false,
      error: 'Rate limit exceeded for JWT validation'
    };
  }
  
  // Basic format validation
  if (!isValidJWTFormat(token)) {
    console.warn(`[JWT] Invalid token format from IP: ${clientIP}`);
    return {
      isValid: false,
      error: 'Invalid JWT format'
    };
  }
  
  // Check expiration before making API call
  if (isJWTExpired(token)) {
    console.warn(`[JWT] Expired token from IP: ${clientIP}`);
    return {
      isValid: false,
      error: 'JWT token has expired'
    };
  }
  
  // Get Supabase credentials
  const supabaseUrl = Deno.env.get('SUPABASE_URL');
  const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
  
  if (!supabaseUrl || !serviceRoleKey) {
    console.error('[JWT] Missing Supabase credentials');
    return {
      isValid: false,
      error: 'Server configuration error'
    };
  }
  
  try {
    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    
    // Validate token with Supabase
    const { data, error } = await supabase.auth.getUser(token);
    
    if (error) {
      console.warn(`[JWT] Supabase validation error: ${error.message}`);
      return {
        isValid: false,
        error: error.message
      };
    }
    
    if (!data.user) {
      console.warn(`[JWT] No user found for token`);
      return {
        isValid: false,
        error: 'User not found'
      };
    }
    
    // Additional security checks
    const payload = decodeJWTPayload(token);
    if (payload) {
      // Verify audience
      if (payload.aud !== 'authenticated') {
        console.warn(`[JWT] Invalid audience: ${payload.aud}`);
        return {
          isValid: false,
          error: 'Invalid token audience'
        };
      }
      
      // Verify issuer (should be Supabase)
      if (!payload.iss || !payload.iss.includes('supabase')) {
        console.warn(`[JWT] Invalid issuer: ${payload.iss}`);
        return {
          isValid: false,
          error: 'Invalid token issuer'
        };
      }
    }
    
    console.log(`[JWT] Successfully validated token for user: ${data.user.id}`);
    
    return {
      isValid: true,
      user: data.user,
      userId: data.user.id,
      email: data.user.email,
      role: data.user.role || 'authenticated'
    };
    
  } catch (error) {
    console.error(`[JWT] Validation error:`, error);
    return {
      isValid: false,
      error: 'JWT validation failed'
    };
  }
}

// Middleware function for JWT validation
export function withJWTValidation(
  handler: (request: Request, user: any) => Promise<Response>
) {
  return async (request: Request): Promise<Response> => {
    // Extract token
    const token = extractJWTToken(request);
    
    if (!token) {
      return new Response(JSON.stringify({
        error: 'Missing authorization token',
        code: 'MISSING_TOKEN'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          'WWW-Authenticate': 'Bearer'
        }
      });
    }
    
    // Validate token
    const validation = await validateJWTWithSupabase(token, request);
    
    if (!validation.isValid) {
      return new Response(JSON.stringify({
        error: validation.error || 'Invalid token',
        code: 'INVALID_TOKEN'
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json',
          'WWW-Authenticate': 'Bearer'
        }
      });
    }
    
    // Call the original handler with validated user
    return handler(request, validation.user);
  };
}

// Check if user has admin privileges
export async function isUserAdmin(userId: string): Promise<boolean> {
  const supabaseUrl = Deno.env.get('SUPABASE_URL');
  const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
  
  if (!supabaseUrl || !serviceRoleKey) {
    return false;
  }
  
  try {
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    
    const { data, error } = await supabase
      .from('user_settings')
      .select('is_superuser')
      .eq('id', userId)
      .single();
    
    if (error || !data) {
      return false;
    }
    
    return Boolean(data.is_superuser);
  } catch {
    return false;
  }
}

// Admin-only middleware
export function withAdminValidation(
  handler: (request: Request, user: any) => Promise<Response>
) {
  return withJWTValidation(async (request: Request, user: any) => {
    const isAdmin = await isUserAdmin(user.id);
    
    if (!isAdmin) {
      return new Response(JSON.stringify({
        error: 'Admin privileges required',
        code: 'INSUFFICIENT_PRIVILEGES'
      }), {
        status: 403,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    return handler(request, user);
  });
}

export default {
  extractJWTToken,
  isValidJWTFormat,
  decodeJWTPayload,
  isJWTExpired,
  validateJWTWithSupabase,
  withJWTValidation,
  withAdminValidation,
  isUserAdmin
};
