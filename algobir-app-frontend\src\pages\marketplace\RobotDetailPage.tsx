import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link as RouterLink } from 'react-router-dom';
import {
  Box,
  VStack,
  Heading,
  Text,
  Image,
  Button,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertDescription,
  Flex,
  Badge,
  Spinner,
  Center,
  useBreakpointValue,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Avatar,
  HStack,
  Divider,
  Icon,
  Link
} from '@chakra-ui/react';
import { FiArrowLeft, FiUser, FiCalendar, FiDollarSign, FiClock, FiTrendingUp, FiShield } from 'react-icons/fi';
import { RiRobotFill } from 'react-icons/ri';
import Card from '../../components/card/Card';
import { supabase } from '../../supabaseClient';
import { useAuth } from '../../context/AuthContext';
import { useToastHelper } from '../../components/ToastHelper';
import { Robot } from '../../types/robot';

const RobotDetailPage: React.FC = () => {
  const { robotId } = useParams<{ robotId: string }>();
  const { user } = useAuth();
  const toast = useToastHelper();
  
  const [robot, setRobot] = useState<Robot | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriptionLoading, setSubscriptionLoading] = useState(false);

  // Responsive değerler
  const cardPadding = useBreakpointValue({ base: '16px', sm: '20px', md: '24px' });
  const buttonSize = useBreakpointValue({ base: 'sm', md: 'md' });
  const fontSize = useBreakpointValue({ base: 'sm', md: 'md' });

  // Horizon UI renk tema değerleri
  const textColor = useColorModeValue('navy.700', 'white');
  const textColorSecondary = useColorModeValue('secondaryGray.600', 'white');
  const brandColor = useColorModeValue('brand.500', 'white');
  const cardShadow = useColorModeValue("0px 18px 40px rgba(112, 144, 176, 0.12)", "none");
  const cardBg = useColorModeValue('white', 'navy.800');

  useEffect(() => {
    const fetchRobotDetails = async () => {
      if (!robotId) {
        setError('Robot ID belirtilmemiş');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        
        // Robot detaylarını al
        const { data: robotData, error: robotError } = await supabase
          .from('v_marketplace_robots')
          .select('*')
          .eq('id', robotId)
          .single();

        if (robotError) throw robotError;

        if (!robotData) {
          setError('Robot bulunamadı');
          return;
        }

        setRobot(robotData);

        // Kullanıcı giriş yapmışsa abonelik durumunu kontrol et
        if (user?.id) {
          const { data: subscriptionData, error: subError } = await supabase
            .from('subscriptions')
            .select('id')
            .eq('user_id', user.id)
            .eq('robot_id', robotId)
            .eq('is_active', true)
            .eq('is_deleted', false)
            .maybeSingle();

          if (subError && subError.code !== 'PGRST116') {
            console.error('Abonelik kontrolü hatası:', subError);
          } else {
            setIsSubscribed(!!subscriptionData);
          }
        }
      } catch (error: any) {
        console.error('Robot detayları çekme hatası:', error);
        setError('Robot detayları yüklenirken bir hata oluştu');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRobotDetails();
  }, [robotId, user?.id]);

  const handleSubscribe = async () => {
    if (!user?.id) {
      toast.showWarningToast('Giriş Gerekli', 'Abone olmak için giriş yapmalısınız.');
      return;
    }

    if (!robotId) return;

    setSubscriptionLoading(true);
    try {
      if (isSubscribed) {
        // Abonelikten çık
        const { error } = await supabase
          .from('subscriptions')
          .update({ is_deleted: true, is_active: false })
          .eq('user_id', user.id)
          .eq('robot_id', robotId);

        if (error) throw error;

        setIsSubscribed(false);
        toast.showSuccessToast('Başarılı', 'Abonelikten çıktınız.');
      } else {
        // Abone ol
        const { error } = await supabase
          .from('subscriptions')
          .insert({
            user_id: user.id,
            robot_id: robotId,
            is_active: true,
            started_at: new Date().toISOString(),
            is_deleted: false
          });

        if (error) throw error;

        setIsSubscribed(true);
        toast.showSuccessToast('Başarılı', 'Robota abone oldunuz.');
      }
    } catch (error: any) {
      console.error('Abonelik işlemi hatası:', error);
      toast.showErrorToast('Hata', 'Abonelik işlemi sırasında bir hata oluştu.');
    } finally {
      setSubscriptionLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <Box 
        pt={{ base: '130px', md: '80px', xl: '80px' }}
        px={{ base: '16px', md: '24px', lg: '30px' }}
        pb={{ base: '20px', md: '30px' }}
      >
        <Center minH="400px">
          <VStack spacing={4}>
            <Spinner 
              size="xl" 
              color={brandColor}
              thickness="4px"
            />
            <Text fontSize={fontSize} color={textColorSecondary}>
              Robot detayları yükleniyor...
            </Text>
          </VStack>
        </Center>
      </Box>
    );
  }

  if (error || !robot) {
    return (
      <Box 
        pt={{ base: '130px', md: '80px', xl: '80px' }}
        px={{ base: '16px', md: '24px', lg: '30px' }}
        pb={{ base: '20px', md: '30px' }}
      >
        <VStack spacing={{ base: 6, md: 8 }} align="stretch" maxW="800px" mx="auto">
          <Card
            p={cardPadding}
            borderRadius="20px"
            boxShadow={cardShadow}
            bg={cardBg}
          >
            <Alert 
              status="error" 
              borderRadius="16px" 
              variant="subtle"
              flexDirection="column"
              alignItems="center"
              textAlign="center"
              py={8}
            >
              <AlertIcon boxSize="40px" mb={4} />
              <AlertDescription fontSize={{ base: 'md', md: 'lg' }} fontWeight="500">
                {error || 'Robot bulunamadı'}
              </AlertDescription>
              <Button
                as={RouterLink}
                to="/marketplace"
                colorScheme="brand"
                variant="outline"
                mt={4}
                size={buttonSize}
                leftIcon={<FiArrowLeft />}
              >
                Pazar Yerine Dön
              </Button>
            </Alert>
          </Card>
        </VStack>
      </Box>
    );
  }

  return (
    <Box 
      pt={{ base: '130px', md: '80px', xl: '80px' }}
      px={{ base: '16px', md: '24px', lg: '30px' }}
      pb={{ base: '20px', md: '30px' }}
    >
      <VStack spacing={{ base: 6, md: 8 }} align="stretch" maxW="1200px" mx="auto">
        {/* Geri Dön Butonu */}
        <Button
          as={RouterLink}
          to="/marketplace"
          leftIcon={<FiArrowLeft />}
          variant="ghost"
          size={buttonSize}
          alignSelf="flex-start"
          color={textColorSecondary}
          _hover={{ color: brandColor }}
        >
          Pazar Yerine Dön
        </Button>

        {/* Ana Robot Kartı */}
        <Card
          p={cardPadding}
          borderRadius="20px"
          boxShadow={cardShadow}
          bg={cardBg}
          transition="all 0.3s"
        >
          <Flex
            direction={{ base: 'column', lg: 'row' }}
            gap={{ base: 6, lg: 8 }}
          >
            {/* Sol Taraf - Robot Resmi */}
            <Box flex="1" maxW={{ lg: '400px' }}>
              <Image
                src={robot.image_url || ''}
                alt={robot.name}
                width="100%"
                height={{ base: '200px', md: '300px' }}
                objectFit="cover"
                borderRadius="16px"
                fallbackSrc="https://placehold.co/400x300?text=Robot"
                onError={(e) => { e.currentTarget.src = 'https://placehold.co/400x300?text=Robot'; }}
                boxShadow="md"
              />
            </Box>

            {/* Sağ Taraf - Robot Bilgileri */}
            <VStack flex="1" align="stretch" spacing={4}>
              <Flex justify="space-between" align="flex-start" wrap="wrap" gap={2}>
                <VStack align="flex-start" spacing={2}>
                  <HStack>
                    <Icon as={RiRobotFill} color={brandColor} boxSize={6} />
                    <Heading 
                      size={{ base: 'lg', md: 'xl' }}
                      color={textColor}
                      fontSize={{ base: '24px', md: '32px' }}
                    >
                      {robot.name}
                    </Heading>
                  </HStack>
                  <Badge 
                    colorScheme={robot.status === 'active' ? 'green' : 'gray'}
                    fontSize="sm"
                    px={3}
                    py={1}
                    borderRadius="full"
                  >
                    {robot.status === 'active' ? 'Aktif' : 'Pasif'}
                  </Badge>
                </VStack>
                
                {robot.price !== undefined && robot.price !== null && (
                  <Stat maxW="150px" textAlign="right">
                    <StatLabel fontSize="sm" color={textColorSecondary}>Fiyat</StatLabel>
                    <StatNumber color={brandColor} fontSize={{ base: 'xl', md: '2xl' }}>
                      ₺{robot.price}
                    </StatNumber>
                    <StatHelpText fontSize="xs">
                      {robot.subscription_period && `${robot.subscription_period} gün`}
                    </StatHelpText>
                  </Stat>
                )}
              </Flex>

              {robot.description && (
                <Text 
                  color={textColor} 
                  fontSize={{ base: 'sm', md: 'md' }}
                  lineHeight="1.6"
                >
                  {robot.description}
                </Text>
              )}

              {robot.strategy_type && (
                <HStack>
                  <Icon as={FiTrendingUp} color={brandColor} />
                  <Text fontSize="sm" color={textColorSecondary}>
                    Strateji: <Text as="span" color={textColor} fontWeight="600">{robot.strategy_type}</Text>
                  </Text>
                </HStack>
              )}

              <Divider />

              {/* Yaratıcı Bilgileri */}
              {robot.seller_username && (
                <HStack spacing={3}>
                  <Avatar 
                    size="sm"
                    src={robot.seller_avatar_url || undefined}
                    name={robot.seller_full_name || robot.seller_username}
                  />
                  <VStack align="flex-start" spacing={0}>
                    <Text fontSize="xs" color={textColorSecondary}>Yaratıcı</Text>
                    {robot.seller_url_slug ? (
                      <Link 
                        as={RouterLink} 
                        to={`/user/${robot.seller_url_slug}`}
                        fontWeight="600" 
                        color={brandColor}
                        fontSize="sm"
                        _hover={{ textDecoration: "underline" }}
                      >
                        {robot.seller_full_name || robot.seller_username}
                      </Link>
                    ) : (
                      <Text fontSize="sm" fontWeight="600" color={textColor}>
                        {robot.seller_full_name || robot.seller_username}
                      </Text>
                    )}
                  </VStack>
                </HStack>
              )}

              <Divider />

              {/* Abone Ol Butonu */}
              <Button
                colorScheme={isSubscribed ? "red" : "brand"}
                size="lg"
                width="100%"
                isLoading={subscriptionLoading}
                isDisabled={!user?.id}
                onClick={handleSubscribe}
                leftIcon={isSubscribed ? <FiShield /> : <FiUser />}
              >
                {!user?.id 
                  ? "Abone Olmak İçin Giriş Yapın" 
                  : isSubscribed 
                    ? "Abonelikten Çık" 
                    : "Abone Ol"
                }
              </Button>
            </VStack>
          </Flex>
        </Card>

        {/* Detaylar Kartı */}
        <Card
          p={cardPadding}
          borderRadius="20px"
          boxShadow={cardShadow}
          bg={cardBg}
        >
          <VStack spacing={6} align="stretch">
            <Heading 
              size="md" 
              color={textColor}
              fontSize={{ base: '18px', md: '20px' }}
            >
              Detaylı Bilgiler
            </Heading>

            <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
              <Stat>
                <StatLabel>
                  <HStack>
                    <Icon as={FiCalendar} color={brandColor} />
                    <Text>Oluşturulma Tarihi</Text>
                  </HStack>
                </StatLabel>
                <StatNumber fontSize="lg" color={textColor}>
                  {formatDate(robot.created_at)}
                </StatNumber>
              </Stat>

              {robot.updated_at && (
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={FiClock} color={brandColor} />
                      <Text>Son Güncelleme</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber fontSize="lg" color={textColor}>
                    {formatDate(robot.updated_at)}
                  </StatNumber>
                </Stat>
              )}

              {robot.investment_amount_per_position && (
                <Stat>
                  <StatLabel>
                    <HStack>
                      <Icon as={FiDollarSign} color={brandColor} />
                      <Text>Pozisyon Başına Yatırım</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber fontSize="lg" color={textColor}>
                    ₺{robot.investment_amount_per_position}
                  </StatNumber>
                </Stat>
              )}
            </SimpleGrid>

            {robot.version && (
              <Box>
                <Text fontSize="sm" color={textColorSecondary} mb={1}>Versiyon</Text>
                <Badge colorScheme="blue" fontSize="sm" px={3} py={1}>
                  v{robot.version}
                </Badge>
              </Box>
            )}
          </VStack>
        </Card>
      </VStack>
    </Box>
  );
};

export default RobotDetailPage; 