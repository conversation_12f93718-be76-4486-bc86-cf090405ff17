// supabase/functions/platform-statistics-updater/index.ts
// Scheduled function to update platform statistics cache
// This function should be scheduled to run at the 18th minute of every hour (XX:18:00)

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.8'
import { getCorsHeaders } from '../_shared/cors.ts'

console.log('--- platform-statistics-updater: Scheduled Statistics Update Handler starting ---')

const corsHeaders = getCorsHeaders()

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('[STATS-UPDATER] Starting platform statistics update...')
    
    // Initialize Supabase client with service role key for admin operations
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)
    
    // Call the update_platform_statistics function
    const { data, error } = await supabase.rpc('update_platform_statistics')
    
    if (error) {
      console.error('[STATS-UPDATER] Error updating platform statistics:', error)
      return new Response(JSON.stringify({ 
        success: false, 
        error: error.message 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    console.log('[STATS-UPDATER] Platform statistics updated successfully:', data)
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Platform statistics updated successfully',
      data: data,
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('[STATS-UPDATER] Unexpected error:', error)
    return new Response(JSON.stringify({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})
