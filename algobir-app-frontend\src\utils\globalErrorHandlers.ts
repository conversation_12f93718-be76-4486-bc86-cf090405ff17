import logger from "./logger"; // Path relative to algobir-app-frontend/src/utils

export function initializeGlobalErrorHandlers(): void {
  window.onerror = (message, source, lineno, colno, error) => {
    logger.logError(error || message, {
      source,
      lineno,
      colno,
      type: "window.onerror",
    });
    // alert("Beklenmeyen bir hata oluştu. Lütfen konsolu kontrol edin.");
    return true;
  };

  window.onunhandledrejection = (event: PromiseRejectionEvent) => {
    logger.logError(event.reason, {
      type: "unhandledrejection",
      originalEvent: event, // Storing the original event might be useful for later inspection
    });
    // alert("İşlenmemiş bir promise reddi tespit edildi. Lütfen konsolu kontrol edin.");
  };

  logger.logInfo("Global error handlers initialized.");
} 