import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Box,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,


  VStack,
  HStack,
  Badge,
  Progress,
  useColorModeValue,
  Icon,

  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Button,
  ButtonGroup,
  Select,

  Flex,

  useBreakpointValue,
  Divider,
  Container,
  Wrap,
  WrapItem,
  useToast
} from '@chakra-ui/react';
import {

  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,

  Legend
} from 'recharts';
import {
  FiTrendingUp,
  FiTarget,
  FiShield,
  FiActivity,

  FiDollarSign,
  FiFilter,

  FiRefreshCw,
  FiDownload,
  FiBarChart,
  FiPieChart,
  FiTrendingDown,

} from 'react-icons/fi';
import { RobotStats } from '../../../hooks/useEnhancedStatistics';
import SoloRobotFilters, { SoloRobotFilterState } from '../filters/SoloRobotFilters';
import EnhancedPerformanceMetrics from '../metrics/EnhancedPerformanceMetrics';
import CumulativePnLChart from '../charts/CumulativePnLChart';
import PerformanceHeatmap from '../charts/PerformanceHeatmap';
import RiskReturnScatterPlot from '../charts/RiskReturnScatterPlot';
import InteractiveTimeAnalysisChart from '../charts/InteractiveTimeAnalysisChart';

interface SoloRobotAnalyticsProps {
  robotStats: RobotStats | null;
}

const SoloRobotAnalytics: React.FC<SoloRobotAnalyticsProps> = ({ robotStats }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const toast = useToast();

  // Enhanced state management with URL persistence
  const [filters, setFilters] = useState<SoloRobotFilterState>({
    dateRange: {
      preset: 'all',
      startDate: '',
      endDate: ''
    },
    performance: {
      minROI: -100,
      maxROI: 1000,
      minWinRate: 0,
      maxWinRate: 100,
      minTrades: 0,
      maxTrades: 10000
    },
    metrics: {
      showProfitable: true,
      showUnprofitable: true,
      minProfitFactor: 0,
      maxDrawdown: 100
    },
    display: {
      chartType: 'area',
      timeframe: 'monthly',
      groupBy: 'time'
    }
  });

  const [activeTab, setActiveTab] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isFilterLoading, setIsFilterLoading] = useState(false);

  // Responsive design values
  const cardBg = useColorModeValue('white', 'gray.700');
  const textColor = useColorModeValue('gray.700', 'white');
  const accentColor = useColorModeValue('blue.500', 'blue.300');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const bgColor = useColorModeValue('gray.50', 'gray.800');

  // Responsive breakpoints
  const isMobile = useBreakpointValue({ base: true, md: false });
  const chartHeight = useBreakpointValue({ base: '250px', md: '300px', lg: '350px' });
  const containerMaxW = useBreakpointValue({ base: 'full', xl: '7xl' });

  // Enhanced formatting functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value / 100);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('tr-TR').format(value);
  };

  // Enhanced color schemes
  const chartColors = {
    primary: useColorModeValue('#3182CE', '#63B3ED'),
    success: useColorModeValue('#38A169', '#68D391'),
    danger: useColorModeValue('#E53E3E', '#FC8181'),
    warning: useColorModeValue('#D69E2E', '#F6E05E'),
    info: useColorModeValue('#3182CE', '#63B3ED'),
    purple: useColorModeValue('#9F7AEA', '#B794F6'),
    gradient: ['#3182CE', '#38A169', '#E53E3E', '#D69E2E', '#9F7AEA', '#00B5D8']
  };

  // URL persistence for filters
  useEffect(() => {
    const urlFilters = searchParams.get('filters');
    if (urlFilters) {
      try {
        const parsedFilters = JSON.parse(decodeURIComponent(urlFilters));
        setFilters(prev => ({ ...prev, ...parsedFilters }));
      } catch (error) {
        console.warn('Failed to parse filters from URL:', error);
      }
    }
  }, [searchParams]);

  // Enhanced filter handlers
  const handleFiltersChange = useCallback((newFilters: SoloRobotFilterState) => {
    setFilters(newFilters);
    setIsFilterLoading(true);

    // Update URL with filters
    const params = new URLSearchParams(searchParams);
    params.set('filters', encodeURIComponent(JSON.stringify(newFilters)));
    setSearchParams(params);

    // Simulate filter application
    setTimeout(() => {
      setIsFilterLoading(false);
      toast({
        title: 'Filtreler Uygulandı',
        description: 'Veriler güncellendi',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    }, 1000);
  }, [searchParams, setSearchParams, toast]);

  const handleFilterReset = useCallback(() => {
    const defaultFilters: SoloRobotFilterState = {
      dateRange: {
        preset: 'all',
        startDate: '',
        endDate: ''
      },
      performance: {
        minROI: -100,
        maxROI: 1000,
        minWinRate: 0,
        maxWinRate: 100,
        minTrades: 0,
        maxTrades: 10000
      },
      metrics: {
        showProfitable: true,
        showUnprofitable: true,
        minProfitFactor: 0,
        maxDrawdown: 100
      },
      display: {
        chartType: 'area',
        timeframe: 'monthly',
        groupBy: 'time'
      }
    };

    setFilters(defaultFilters);
    const params = new URLSearchParams(searchParams);
    params.delete('filters');
    setSearchParams(params);

    toast({
      title: 'Filtreler Sıfırlandı',
      description: 'Tüm filtreler varsayılan değerlere döndürüldü',
      status: 'info',
      duration: 2000,
      isClosable: true,
    });
  }, [searchParams, setSearchParams, toast]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setIsRefreshing(false);
      toast({
        title: 'Veriler Yenilendi',
        description: 'En güncel veriler yüklendi',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    }, 1000);
  };

  // Enhanced loading and empty states
  if (!robotStats) {
    return (
      <Container maxW={containerMaxW} py={6}>
        <VStack spacing={6} align="stretch">
          {/* Enhanced Empty State */}
          <Card bg={cardBg} borderRadius="xl" shadow="sm">
            <CardBody p={8}>
              <VStack spacing={6} textAlign="center">
                <Icon as={FiActivity} boxSize={16} color="gray.400" />
                <VStack spacing={2}>
                  <Heading size="lg" color={textColor}>
                    Solo-Robot Verisi Bulunamadı
                  </Heading>
                  <Text color="gray.500" maxW="md">
                    Henüz Solo-Robot ile gerçekleştirilen işlem bulunmuyor.
                    Solo-Robot'unuzu aktifleştirdikten sonra performans verilerini burada görebileceksiniz.
                  </Text>
                </VStack>
                <Button
                  colorScheme="blue"
                  size="lg"
                  leftIcon={<FiTrendingUp />}
                >
                  Solo-Robot'u Aktifleştir
                </Button>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </Container>
    );
  }



  // Enhanced data filtering based on filter state
  const filteredData = useMemo(() => {
    if (!robotStats) return null;

    // Apply performance filters
    const meetsPerformanceCriteria =
      robotStats.roi >= filters.performance.minROI &&
      robotStats.roi <= filters.performance.maxROI &&
      robotStats.winRate >= filters.performance.minWinRate &&
      robotStats.winRate <= filters.performance.maxWinRate &&
      robotStats.totalTrades >= filters.performance.minTrades &&
      robotStats.totalTrades <= filters.performance.maxTrades;

    // Apply metric filters
    const meetsProfitabilityFilter =
      (filters.metrics.showProfitable && robotStats.totalPnl > 0) ||
      (filters.metrics.showUnprofitable && robotStats.totalPnl <= 0);

    if (!meetsPerformanceCriteria || !meetsProfitabilityFilter) {
      return null;
    }

    // Filter time-based data based on date range
    let filteredMonthlyData = robotStats.monthlyPerformance;
    let filteredTimeData = robotStats.timePerformance;
    let filteredSymbolData = robotStats.symbolPerformance;

    if (filters.dateRange.preset !== 'all') {
      const now = new Date();
      let cutoffDate = new Date();

      switch (filters.dateRange.preset) {
        case '1D':
          cutoffDate.setDate(now.getDate() - 1);
          break;
        case '1W':
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case '1M':
          cutoffDate.setMonth(now.getMonth() - 1);
          break;
        case '3M':
          cutoffDate.setMonth(now.getMonth() - 3);
          break;
        case '6M':
          cutoffDate.setMonth(now.getMonth() - 6);
          break;
        case '1Y':
          cutoffDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      // Filter data based on cutoff date (simplified - in real app would use actual dates)
      const monthsToShow = Math.max(1, Math.ceil((now.getTime() - cutoffDate.getTime()) / (1000 * 60 * 60 * 24 * 30)));
      filteredMonthlyData = robotStats.monthlyPerformance.slice(-monthsToShow);
    }

    return {
      ...robotStats,
      monthlyPerformance: filteredMonthlyData,
      timePerformance: filteredTimeData,
      symbolPerformance: filteredSymbolData
    };
  }, [robotStats, filters]);

  // Use filtered data for display
  const displayData = filteredData || robotStats;

  // Enhanced chart data processing
  const winLossData = useMemo(() => {
    if (!displayData) return [];
    return [
      {
        name: 'Kazançlı İşlemler',
        value: displayData.winningTrades,
        color: chartColors.success,
        percentage: (displayData.winningTrades / displayData.totalTrades) * 100
      },
      {
        name: 'Zararlı İşlemler',
        value: displayData.losingTrades,
        color: chartColors.danger,
        percentage: (displayData.losingTrades / displayData.totalTrades) * 100
      }
    ];
  }, [displayData, chartColors]);



  return (
    <Container maxW={containerMaxW} py={6}>
      <VStack spacing={8} align="stretch">
        {/* Enhanced Header with Actions */}
        <Card bg={cardBg} borderRadius="xl" shadow="sm">
          <CardBody p={6}>
            <Flex direction={{ base: 'column', lg: 'row' }} gap={6}>
              {/* Title Section */}
              <VStack align="start" spacing={3} flex={1}>
                <HStack spacing={4}>
                  <Icon as={FiActivity} boxSize={10} color={accentColor} />
                  <VStack align="start" spacing={1}>
                    <Heading size="xl" color={textColor}>
                      Solo-Robot Performans Analizi
                    </Heading>
                    <Text color="gray.500" fontSize="lg">
                      Otomatik trading robot detaylı performans metrikleri
                    </Text>
                  </VStack>
                </HStack>

                {/* Key Metrics Badges */}
                <Wrap spacing={3}>
                  <WrapItem>
                    <Badge
                      colorScheme="blue"
                      variant="subtle"
                      px={4}
                      py={2}
                      borderRadius="full"
                      fontSize="sm"
                    >
                      <Icon as={FiBarChart} mr={2} />
                      {formatNumber(robotStats.totalTrades)} Toplam İşlem
                    </Badge>
                  </WrapItem>
                  <WrapItem>
                    <Badge
                      colorScheme={robotStats.totalPnl > 0 ? 'green' : 'red'}
                      variant="subtle"
                      px={4}
                      py={2}
                      borderRadius="full"
                      fontSize="sm"
                    >
                      <Icon as={FiDollarSign} mr={2} />
                      {formatCurrency(robotStats.totalPnl)} Net P&L
                    </Badge>
                  </WrapItem>
                  <WrapItem>
                    <Badge
                      colorScheme="purple"
                      variant="subtle"
                      px={4}
                      py={2}
                      borderRadius="full"
                      fontSize="sm"
                    >
                      <Icon as={FiTrendingUp} mr={2} />
                      {formatCurrency(robotStats.totalInvestment)} Toplam Yatırım
                    </Badge>
                  </WrapItem>
                  <WrapItem>
                    <Badge
                      colorScheme={robotStats.roi > 0 ? 'green' : 'red'}
                      variant="solid"
                      px={4}
                      py={2}
                      borderRadius="full"
                      fontSize="sm"
                    >
                      <Icon as={FiTarget} mr={2} />
                      {formatPercent(robotStats.roi)} ROI
                    </Badge>
                  </WrapItem>
                </Wrap>
              </VStack>

              {/* Action Buttons */}
              <VStack spacing={3} align="stretch" minW={{ base: 'full', lg: '200px' }}>
                <Button
                  leftIcon={<FiRefreshCw />}
                  colorScheme="blue"
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  isLoading={isRefreshing}
                  loadingText="Yenileniyor"
                >
                  Verileri Yenile
                </Button>
                <Button
                  leftIcon={<FiDownload />}
                  colorScheme="green"
                  variant="outline"
                  size="sm"
                >
                  Rapor İndir
                </Button>
                <Button
                  leftIcon={<FiFilter />}
                  colorScheme="gray"
                  variant="outline"
                  size="sm"
                >
                  Filtrele
                </Button>
              </VStack>
            </Flex>
          </CardBody>
        </Card>

        {/* Advanced Filters */}
        <SoloRobotFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onReset={handleFilterReset}
          isLoading={isFilterLoading}
        />

        {/* Enhanced Performance Metrics */}
        <EnhancedPerformanceMetrics
          robotStats={displayData}
          isRealTime={true}
          showComparison={true}
          compactView={isMobile}
        />

        {/* Enhanced Charts Section with Tabs */}
        <Card bg={cardBg} borderRadius="xl" shadow="sm">
          <CardHeader pb={0}>
            <HStack spacing={4} justify="space-between" flexWrap="wrap">
              <Heading size="lg" color={textColor}>
                Performans Analizi
              </Heading>
              <HStack spacing={2}>
                <Select
                  size="sm"
                  value={filters.display.timeframe}
                  onChange={(e) => handleFiltersChange({
                    ...filters,
                    display: { ...filters.display, timeframe: e.target.value as any }
                  })}
                  w="auto"
                >
                  <option value="hourly">Saatlik</option>
                  <option value="daily">Günlük</option>
                  <option value="weekly">Haftalık</option>
                  <option value="monthly">Aylık</option>
                </Select>
                <ButtonGroup size="sm" isAttached variant="outline">
                  <Button
                    isActive={filters.display.chartType === 'area'}
                    onClick={() => handleFiltersChange({
                      ...filters,
                      display: { ...filters.display, chartType: 'area' }
                    })}
                  >
                    <FiPieChart />
                  </Button>
                  <Button
                    isActive={filters.display.chartType === 'line'}
                    onClick={() => handleFiltersChange({
                      ...filters,
                      display: { ...filters.display, chartType: 'line' }
                    })}
                  >
                    <FiTrendingUp />
                  </Button>
                  <Button
                    isActive={filters.display.chartType === 'bar'}
                    onClick={() => handleFiltersChange({
                      ...filters,
                      display: { ...filters.display, chartType: 'bar' }
                    })}
                  >
                    <FiBarChart />
                  </Button>
                </ButtonGroup>
              </HStack>
            </HStack>
          </CardHeader>
          <CardBody>
            <Tabs index={activeTab} onChange={setActiveTab} variant="enclosed" colorScheme="blue">
              <TabList>
                <Tab>Kümülatif P&L</Tab>
                <Tab>Performans Haritası</Tab>
                <Tab>Risk-Getiri Analizi</Tab>
                <Tab>İnteraktif Zaman</Tab>
                <Tab>Kazanç/Kayıp Detayı</Tab>
              </TabList>

              <TabPanels>
                {/* Cumulative P&L Chart Tab */}
                <TabPanel px={0}>
                  <CumulativePnLChart
                    data={displayData.monthlyPerformance}
                    chartType={filters.display.chartType === 'bar' || filters.display.chartType === 'candlestick' ? 'line' : filters.display.chartType}
                    showBrush={true}
                    showReferenceLine={true}
                    height={chartHeight}
                    title="Kümülatif Kar/Zarar Trendi"
                  />
                </TabPanel>

                {/* Performance Heatmap Tab */}
                <TabPanel px={0}>
                  <VStack spacing={6} align="stretch">
                    <PerformanceHeatmap
                      timeData={displayData.timePerformance}
                      symbolData={displayData.symbolPerformance}
                      monthlyData={displayData.monthlyPerformance}
                      type="time"
                      title="Saatlik Performans Haritası"
                    />
                    <PerformanceHeatmap
                      timeData={displayData.timePerformance}
                      symbolData={displayData.symbolPerformance}
                      monthlyData={displayData.monthlyPerformance}
                      type="symbol"
                      title="Sembol Performans Haritası"
                    />
                  </VStack>
                </TabPanel>

                {/* Risk-Return Analysis Tab */}
                <TabPanel px={0}>
                  <VStack spacing={6} align="stretch">
                    <RiskReturnScatterPlot
                      symbolData={displayData.symbolPerformance}
                      monthlyData={displayData.monthlyPerformance}
                      dataType="symbol"
                      height={chartHeight}
                      title="Sembol Risk-Getiri Analizi"
                    />
                    <RiskReturnScatterPlot
                      symbolData={displayData.symbolPerformance}
                      monthlyData={displayData.monthlyPerformance}
                      dataType="monthly"
                      height="350px"
                      title="Aylık Risk-Getiri Analizi"
                    />
                  </VStack>
                </TabPanel>

                {/* Interactive Time Analysis Tab */}
                <TabPanel px={0}>
                  <InteractiveTimeAnalysisChart
                    timeData={displayData.timePerformance}
                    monthlyData={displayData.monthlyPerformance}
                    height={chartHeight}
                    title="İnteraktif Zaman Analizi"
                  />
                </TabPanel>

                {/* Detailed Win/Loss Analysis Tab */}
                <TabPanel px={0}>
                  <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                    {/* Pie Chart */}
                    <Box>
                      <Text fontSize="lg" fontWeight="semibold" mb={4} color={textColor}>
                        İşlem Dağılımı
                      </Text>
                      <Box h="300px">
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={winLossData}
                              cx="50%"
                              cy="50%"
                              outerRadius={100}
                              dataKey="value"
                              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                              labelLine={false}
                            >
                              {winLossData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                            <RechartsTooltip
                              formatter={(value: number, name: string) => [
                                `${formatNumber(value)} işlem`,
                                name
                              ]}
                              contentStyle={{
                                backgroundColor: cardBg,
                                border: `1px solid ${borderColor}`,
                                borderRadius: '8px'
                              }}
                            />
                            <Legend />
                          </PieChart>
                        </ResponsiveContainer>
                      </Box>
                    </Box>

                    {/* Win/Loss Statistics */}
                    <VStack spacing={4} align="stretch">
                      <Text fontSize="lg" fontWeight="semibold" color={textColor}>
                        İşlem İstatistikleri
                      </Text>

                      <SimpleGrid columns={1} spacing={4}>
                        <Card bg={bgColor} borderRadius="lg">
                          <CardBody p={4}>
                            <HStack justify="space-between">
                              <VStack align="start" spacing={1}>
                                <Text fontSize="sm" color="gray.500">Kazançlı İşlemler</Text>
                                <Text fontSize="2xl" fontWeight="bold" color="green.500">
                                  {formatNumber(displayData.winningTrades)}
                                </Text>
                              </VStack>
                              <Icon as={FiTrendingUp} boxSize={8} color="green.500" />
                            </HStack>
                          </CardBody>
                        </Card>

                        <Card bg={bgColor} borderRadius="lg">
                          <CardBody p={4}>
                            <HStack justify="space-between">
                              <VStack align="start" spacing={1}>
                                <Text fontSize="sm" color="gray.500">Zararlı İşlemler</Text>
                                <Text fontSize="2xl" fontWeight="bold" color="red.500">
                                  {formatNumber(displayData.losingTrades)}
                                </Text>
                              </VStack>
                              <Icon as={FiTrendingDown} boxSize={8} color="red.500" />
                            </HStack>
                          </CardBody>
                        </Card>

                        <Card bg={bgColor} borderRadius="lg">
                          <CardBody p={4}>
                            <VStack align="stretch" spacing={3}>
                              <HStack justify="space-between">
                                <Text fontSize="sm" color="gray.500">Kazanma Oranı</Text>
                                <Text fontSize="lg" fontWeight="bold" color={displayData.winRate > 50 ? 'green.500' : 'red.500'}>
                                  {formatPercent(displayData.winRate)}
                                </Text>
                              </HStack>
                              <Progress
                                value={displayData.winRate}
                                colorScheme={displayData.winRate > 50 ? 'green' : 'red'}
                                size="lg"
                                borderRadius="full"
                              />
                            </VStack>
                          </CardBody>
                        </Card>

                        <Card bg={bgColor} borderRadius="lg">
                          <CardBody p={4}>
                            <SimpleGrid columns={2} spacing={4}>
                              <VStack spacing={1}>
                                <Text fontSize="sm" color="gray.500">En İyi İşlem</Text>
                                <Text fontSize="lg" fontWeight="bold" color="green.500">
                                  {formatCurrency(displayData.bestTrade)}
                                </Text>
                              </VStack>
                              <VStack spacing={1}>
                                <Text fontSize="sm" color="gray.500">En Kötü İşlem</Text>
                                <Text fontSize="lg" fontWeight="bold" color="red.500">
                                  {formatCurrency(displayData.worstTrade)}
                                </Text>
                              </VStack>
                            </SimpleGrid>
                          </CardBody>
                        </Card>
                      </SimpleGrid>
                    </VStack>
                  </SimpleGrid>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </CardBody>
        </Card>

        {/* Enhanced Trading Summary */}
        <Card bg={cardBg} borderRadius="xl" shadow="sm">
          <CardHeader>
            <HStack justify="space-between" flexWrap="wrap">
              <Heading size="lg" color={textColor}>
                İşlem Özeti ve Risk Analizi
              </Heading>
              <Badge colorScheme="blue" variant="outline" px={3} py={1}>
                Son Güncelleme: {new Date().toLocaleString('tr-TR')}
              </Badge>
            </HStack>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
              {/* Best Trade */}
              <Card bg={bgColor} borderRadius="lg" borderLeft="4px" borderLeftColor="green.500">
                <CardBody p={4}>
                  <VStack spacing={3} align="stretch">
                    <HStack justify="space-between">
                      <Text fontSize="sm" fontWeight="medium" color="gray.500">
                        En İyi İşlem
                      </Text>
                      <Icon as={FiTrendingUp} color="green.500" boxSize={5} />
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="green.500">
                      {formatCurrency(robotStats.bestTrade)}
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      Tek işlemde elde edilen maksimum kar
                    </Text>
                  </VStack>
                </CardBody>
              </Card>

              {/* Worst Trade */}
              <Card bg={bgColor} borderRadius="lg" borderLeft="4px" borderLeftColor="red.500">
                <CardBody p={4}>
                  <VStack spacing={3} align="stretch">
                    <HStack justify="space-between">
                      <Text fontSize="sm" fontWeight="medium" color="gray.500">
                        En Kötü İşlem
                      </Text>
                      <Icon as={FiTrendingDown} color="red.500" boxSize={5} />
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="red.500">
                      {formatCurrency(robotStats.worstTrade)}
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      Tek işlemde yaşanan maksimum zarar
                    </Text>
                  </VStack>
                </CardBody>
              </Card>

              {/* Consecutive Wins */}
              <Card bg={bgColor} borderRadius="lg" borderLeft="4px" borderLeftColor="blue.500">
                <CardBody p={4}>
                  <VStack spacing={3} align="stretch">
                    <HStack justify="space-between">
                      <Text fontSize="sm" fontWeight="medium" color="gray.500">
                        Ardışık Kazanç
                      </Text>
                      <Icon as={FiTarget} color="blue.500" boxSize={5} />
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="blue.500">
                      {formatNumber(robotStats.consecutiveWins)}
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      Peş peşe kazançlı işlem sayısı
                    </Text>
                  </VStack>
                </CardBody>
              </Card>

              {/* Consecutive Losses */}
              <Card bg={bgColor} borderRadius="lg" borderLeft="4px" borderLeftColor="orange.500">
                <CardBody p={4}>
                  <VStack spacing={3} align="stretch">
                    <HStack justify="space-between">
                      <Text fontSize="sm" fontWeight="medium" color="gray.500">
                        Ardışık Kayıp
                      </Text>
                      <Icon as={FiShield} color="orange.500" boxSize={5} />
                    </HStack>
                    <Text fontSize="2xl" fontWeight="bold" color="orange.500">
                      {formatNumber(robotStats.consecutiveLosses)}
                    </Text>
                    <Text fontSize="xs" color="gray.500">
                      Peş peşe zararlı işlem sayısı
                    </Text>
                  </VStack>
                </CardBody>
              </Card>
            </SimpleGrid>

            <Divider my={6} />

            {/* Risk Analysis Summary */}
            <VStack spacing={4} align="stretch">
              <Text fontSize="lg" fontWeight="semibold" color={textColor}>
                Risk Analizi Özeti
              </Text>
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
                <VStack spacing={2} align="center" p={4} bg={bgColor} borderRadius="lg">
                  <Icon as={FiShield} boxSize={8} color={Math.abs(robotStats.maxDrawdown) < 10 ? 'green.500' : 'red.500'} />
                  <Text fontSize="sm" color="gray.500">Risk Seviyesi</Text>
                  <Text fontSize="lg" fontWeight="bold" color={Math.abs(robotStats.maxDrawdown) < 10 ? 'green.500' : 'red.500'}>
                    {Math.abs(robotStats.maxDrawdown) < 10 ? 'Düşük' : Math.abs(robotStats.maxDrawdown) < 20 ? 'Orta' : 'Yüksek'}
                  </Text>
                </VStack>

                <VStack spacing={2} align="center" p={4} bg={bgColor} borderRadius="lg">
                  <Icon as={FiActivity} boxSize={8} color={robotStats.sharpeRatio > 1 ? 'green.500' : 'orange.500'} />
                  <Text fontSize="sm" color="gray.500">Risk/Getiri Dengesi</Text>
                  <Text fontSize="lg" fontWeight="bold" color={robotStats.sharpeRatio > 1 ? 'green.500' : 'orange.500'}>
                    {robotStats.sharpeRatio > 1 ? 'İyi' : robotStats.sharpeRatio > 0.5 ? 'Orta' : 'Zayıf'}
                  </Text>
                </VStack>

                <VStack spacing={2} align="center" p={4} bg={bgColor} borderRadius="lg">
                  <Icon as={FiDollarSign} boxSize={8} color={robotStats.profitFactor > 1.5 ? 'green.500' : 'orange.500'} />
                  <Text fontSize="sm" color="gray.500">Karlılık Durumu</Text>
                  <Text fontSize="lg" fontWeight="bold" color={robotStats.profitFactor > 1.5 ? 'green.500' : 'orange.500'}>
                    {robotStats.profitFactor > 1.5 ? 'Çok İyi' : robotStats.profitFactor > 1 ? 'İyi' : 'Zayıf'}
                  </Text>
                </VStack>
              </SimpleGrid>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Container>
  );
};

export default SoloRobotAnalytics;
