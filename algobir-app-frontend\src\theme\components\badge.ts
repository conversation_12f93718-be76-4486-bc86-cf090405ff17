import { mode } from '@chakra-ui/theme-tools';

export const BadgeComponent = {
  components: {
    Badge: {
      baseStyle: {
        borderRadius: '10px',
        lineHeight: 1.2,
        padding: '7px',
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '0.75em',
        fontWeight: 'bold',
        textTransform: 'uppercase',
      },
      variants: {
        brand: (props: any) => ({
          bg: mode('linear-gradient(112deg, #422AFB 21.4%, #282EFF 78.6%)', 'linear-gradient(112deg, #422AFB 21.4%, #282EFF 78.6%)')(props),
          color: 'white',
        }),
        success: (props: any) => ({
          bg: mode('green.100', 'green.800')(props),
          color: mode('green.500', 'green.200')(props),
        }),
        error: (props: any) => ({
          bg: mode('red.100', 'red.800')(props),
          color: mode('red.500', 'red.200')(props),
        }),
        warning: (props: any) => ({
          bg: mode('orange.100', 'orange.800')(props),
          color: mode('orange.500', 'orange.200')(props),
        }),
      },
    },
  },
}; 