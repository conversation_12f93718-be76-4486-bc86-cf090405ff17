import { extendTheme } from '@chakra-ui/react';

// Custom breakpoints with mobile-first approach
const breakpoints = {
  base: '0em',    // 0px
  xs: '20em',     // 320px - Extra small phones
  sm: '30em',     // 480px - Small phones
  md: '48em',     // 768px - Tablets
  lg: '62em',     // 992px - Small desktops
  xl: '80em',     // 1280px - Large desktops
  '2xl': '96em',  // 1536px - Extra large screens
  '3xl': '120em', // 1920px - Ultra wide screens
};

// Consistent spacing scale
const space = {
  '0': '0',
  'px': '1px',
  '0.5': '0.125rem',  // 2px
  '1': '0.25rem',     // 4px
  '1.5': '0.375rem',  // 6px
  '2': '0.5rem',      // 8px
  '2.5': '0.625rem',  // 10px
  '3': '0.75rem',     // 12px
  '3.5': '0.875rem',  // 14px
  '4': '1rem',        // 16px
  '5': '1.25rem',     // 20px
  '6': '1.5rem',      // 24px
  '7': '1.75rem',     // 28px
  '8': '2rem',        // 32px
  '9': '2.25rem',     // 36px
  '10': '2.5rem',     // 40px
  '12': '3rem',       // 48px
  '14': '3.5rem',     // 56px
  '16': '4rem',       // 64px
  '20': '5rem',       // 80px
  '24': '6rem',       // 96px
  '28': '7rem',       // 112px
  '32': '8rem',       // 128px
  '36': '9rem',       // 144px
  '40': '10rem',      // 160px
  '44': '11rem',      // 176px
  '48': '12rem',      // 192px
  '52': '13rem',      // 208px
  '56': '14rem',      // 224px
  '60': '15rem',      // 240px
  '64': '16rem',      // 256px
  '72': '18rem',      // 288px
  '80': '20rem',      // 320px
  '96': '24rem',      // 384px
};

// Accessibility-friendly font sizes
const fontSizes = {
  '2xs': '0.625rem',   // 10px
  'xs': '0.75rem',     // 12px
  'sm': '0.875rem',    // 14px
  'md': '1rem',        // 16px - Base font size for accessibility
  'lg': '1.125rem',    // 18px
  'xl': '1.25rem',     // 20px
  '2xl': '1.5rem',     // 24px
  '3xl': '1.875rem',   // 30px
  '4xl': '2.25rem',    // 36px
  '5xl': '3rem',       // 48px
  '6xl': '3.75rem',    // 60px
  '7xl': '4.5rem',     // 72px
  '8xl': '6rem',       // 96px
  '9xl': '8rem',       // 128px
};

// WCAG compliant colors with sufficient contrast
const colors = {
  brand: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',  // Primary brand color - AAA compliant
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',  // AAA compliant for large text
    600: '#4b5563',  // AAA compliant for normal text
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  // Success color - accessible green
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',  // AAA compliant
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  // Warning color - accessible amber
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',  // AAA compliant
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  // Error color - accessible red
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',  // AAA compliant
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
};

// Consistent z-index scale
const zIndices = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
  menu: 1900, // Navbar menüleri için özel z-index
};

// Component style overrides with accessibility
const components = {
  Button: {
    baseStyle: {
      fontWeight: 'medium',
      borderRadius: 'md',
      _focus: {
        boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)', // Visible focus ring
        outline: 'none',
      },
      _focusVisible: {
        boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
        outline: 'none',
      },
    },
    sizes: {
      xs: {
        h: '6',
        minW: '6',
        fontSize: 'xs',
        px: '2',
      },
      sm: {
        h: '8',
        minW: '8',
        fontSize: 'sm',
        px: '3',
      },
      md: {
        h: '10',
        minW: '10',
        fontSize: 'md',
        px: '4',
      },
      lg: {
        h: '12',
        minW: '12',
        fontSize: 'lg',
        px: '6',
      },
    },
  },
  Input: {
    baseStyle: {
      field: {
        _focus: {
          borderColor: 'brand.500',
          boxShadow: '0 0 0 1px rgba(14, 165, 233, 0.6)',
          outline: 'none',
        },
        _focusVisible: {
          borderColor: 'brand.500',
          boxShadow: '0 0 0 1px rgba(14, 165, 233, 0.6)',
          outline: 'none',
        },
      },
    },
  },
  Card: {
    baseStyle: {
      container: {
        borderRadius: 'xl',
        boxShadow: 'sm',
        border: '1px solid',
        borderColor: 'gray.200',
        _dark: {
          borderColor: 'gray.700',
        },
      },
    },
  },
  Heading: {
    baseStyle: {
      lineHeight: '1.2',
      letterSpacing: '-0.025em',
    },
  },
  Text: {
    baseStyle: {
      lineHeight: '1.6', // Better readability
    },
  },
};

// Global styles
const styles = {
  global: {
    // High contrast mode support
    '@media (prefers-contrast: high)': {
      '*': {
        borderColor: 'black !important',
        textShadow: 'none !important',
        boxShadow: 'none !important',
      },
    },
    // Reduced motion support
    '@media (prefers-reduced-motion: reduce)': {
      '*': {
        animationDuration: '0.01ms !important',
        animationIterationCount: '1 !important',
        transitionDuration: '0.01ms !important',
      },
    },
    // Focus-visible polyfill
    '*:focus:not(:focus-visible)': {
      outline: 'none',
      boxShadow: 'none',
    },
    '*:focus-visible': {
      outline: '2px solid rgba(14, 165, 233, 0.6)',
      outlineOffset: '2px',
    },
    // Base font for better readability
    'html': {
      fontSize: '16px', // Ensures 1rem = 16px
      lineHeight: '1.6',
    },
    'body': {
      fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      WebkitFontSmoothing: 'antialiased',
      MozOsxFontSmoothing: 'grayscale',
      textRendering: 'optimizeLegibility',
    },
    // Skip links for accessibility
    '.skip-link': {
      position: 'absolute',
      top: '-40px',
      left: '6px',
      background: 'brand.500',
      color: 'white',
      padding: '8px',
      borderRadius: '4px',
      textDecoration: 'none',
      zIndex: 'skipLink',
      _focus: {
        top: '6px',
      },
    },
  },
};

// Text styles for consistency
const textStyles = {
  h1: {
    fontSize: { base: '2xl', md: '4xl' },
    fontWeight: 'bold',
    lineHeight: '1.2',
    letterSpacing: '-0.025em',
  },
  h2: {
    fontSize: { base: 'xl', md: '3xl' },
    fontWeight: 'semibold',
    lineHeight: '1.3',
    letterSpacing: '-0.025em',
  },
  h3: {
    fontSize: { base: 'lg', md: '2xl' },
    fontWeight: 'semibold',
    lineHeight: '1.3',
  },
  h4: {
    fontSize: { base: 'md', md: 'xl' },
    fontWeight: 'semibold',
    lineHeight: '1.4',
  },
  body: {
    fontSize: { base: 'sm', md: 'md' },
    lineHeight: '1.6',
  },
  caption: {
    fontSize: { base: 'xs', md: 'sm' },
    lineHeight: '1.5',
    color: 'gray.600',
  },
};

const theme = extendTheme({
  breakpoints,
  space,
  fontSizes,
  colors,
  zIndices,
  components,
  styles,
  textStyles,
  config: {
    initialColorMode: 'light',
    useSystemColorMode: true,
    disableTransitionOnChange: false,
  },
  // Semantic tokens for better theming
  semanticTokens: {
    colors: {
      'bg-primary': {
        default: 'white',
        _dark: 'gray.900',
      },
      'bg-secondary': {
        default: 'gray.50',
        _dark: 'gray.800',
      },
      'text-primary': {
        default: 'gray.900',
        _dark: 'gray.100',
      },
      'text-secondary': {
        default: 'gray.600',
        _dark: 'gray.400',
      },
      'border-primary': {
        default: 'gray.200',
        _dark: 'gray.700',
      },
    },
  },
});

export default theme; 