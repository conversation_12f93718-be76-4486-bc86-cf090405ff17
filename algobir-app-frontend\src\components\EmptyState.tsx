import React from 'react';
import { Box, Heading, Text, Button, Icon, Center, VStack, useColorModeValue } from '@chakra-ui/react';
import { FiInbox } from 'react-icons/fi';

export interface EmptyStateProps {
  title: string;
  message: string;
  actionLabel?: string;
  onAction?: () => void;
  icon?: React.ElementType;
}

/**
 * Boş durum görünümü
 */
const EmptyState: React.FC<EmptyStateProps> = ({ 
  title, 
  message, 
  actionLabel, 
  onAction, 
  icon = FiInbox 
}) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const headingColor = useColorModeValue('gray.800', 'white');

  return (
    <Center h="70vh">
      <Box 
        p={8} 
        borderWidth="1px" 
        borderRadius="lg" 
        bg={bgColor} 
        borderColor={borderColor}
        boxShadow="sm"
        maxW="md"
        w="100%"
        textAlign="center"
      >
        <VStack spacing={5}>
          <Icon 
            as={icon} 
            w={16} 
            h={16} 
            color="brand.500" 
            opacity={0.7}
          />
          
          <Heading 
            size="md" 
            color={headingColor}
          >
            {title}
          </Heading>
          
          <Text color={textColor}>
            {message}
          </Text>
          
          {actionLabel && onAction && (
            <Button
              colorScheme="brand"
              variant="solid"
              onClick={onAction}
              mt={4}
            >
              {actionLabel}
            </Button>
          )}
        </VStack>
      </Box>
    </Center>
  );
};

export default EmptyState; 