export interface Robot {
  id: string;
  seller_id: string; // Original seller_id (references auth.users.id)
  name: string;
  description: string | null;
  image_url?: string | null;
  strategy_type: string | null;
  version?: string | null;
  is_public: boolean;
  deleted_at?: string | null;
  created_at: string;
  updated_at?: string;
  price?: number | null;
  subscription_period?: number | null;
  // Ensure these fields are in your 'robots' table and selected by the VIEW
  investment_amount_per_position?: number | null;
  is_emergency_stopped?: boolean;
  status?: string;

  // Flattened seller profile information from the VIEW
  seller_username?: string | null;
  seller_full_name?: string | null;
  seller_avatar_url?: string | null;
  seller_url_slug?: string | null;
}

export interface RobotSubscriber {
  subscription_id: string;
  user_id: string;
  subscribed_at: string; // ISO string
  is_subscription_active: boolean;
  username?: string | null;
  full_name?: string | null;
  avatar_url?: string | null;
  expires_at?: string | null;
} 