import React, { useCallback } from 'react';
import { Link as ChakraLink, LinkProps as ChakraLinkProps } from '@chakra-ui/react';
import { Link as RouterLink, LinkProps as RouterLinkProps } from 'react-router-dom';
import { preloadOnHover, cancelHoverPreload } from '../utils/routePreloader';

/**
 * Enhanced navigation link props
 */
interface PerformantNavLinkProps extends Omit<ChakraLinkProps, 'as' | 'color'>, Omit<RouterLinkProps, 'to'> {
  to: string;
  preload?: boolean;
  preloadDelay?: number;
  children: React.ReactNode;
}

/**
 * Performant Navigation Link Component
 * 
 * Features:
 * - Hover-based route preloading for instant navigation
 * - Configurable preload delay to avoid unnecessary preloads
 * - Automatic preload cancellation on mouse leave
 * - Full compatibility with React Router and Chakra UI
 * - Performance monitoring integration
 */
const PerformantNavLink: React.FC<PerformantNavLinkProps> = ({
  to,
  preload = true,
  preloadDelay = 200,
  children,
  onMouseEnter,
  onMouseLeave,
  onFocus,
  onBlur,
  ...props
}) => {
  // Handle mouse enter with preloading
  const handleMouseEnter = useCallback((event: React.MouseEvent<HTMLAnchorElement>) => {
    if (preload) {
      preloadOnHover(to);
    }
    onMouseEnter?.(event);
  }, [to, preload, onMouseEnter]);

  // Handle mouse leave with preload cancellation
  const handleMouseLeave = useCallback((event: React.MouseEvent<HTMLAnchorElement>) => {
    if (preload) {
      cancelHoverPreload();
    }
    onMouseLeave?.(event);
  }, [preload, onMouseLeave]);

  // Handle focus for keyboard navigation
  const handleFocus = useCallback((event: React.FocusEvent<HTMLAnchorElement>) => {
    if (preload) {
      preloadOnHover(to);
    }
    onFocus?.(event);
  }, [to, preload, onFocus]);

  // Handle blur for keyboard navigation
  const handleBlur = useCallback((event: React.FocusEvent<HTMLAnchorElement>) => {
    if (preload) {
      cancelHoverPreload();
    }
    onBlur?.(event);
  }, [preload, onBlur]);

  return (
    <ChakraLink
      as={RouterLink}
      to={to}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      onBlur={handleBlur}
      {...props}
    >
      {children}
    </ChakraLink>
  );
};

export default PerformantNavLink;

/**
 * Prebuilt performant navigation variants for common use cases
 */

// Sidebar navigation link with enhanced styling
export const SidebarNavLink: React.FC<PerformantNavLinkProps & { isActive?: boolean }> = ({
  isActive = false,
  ...props
}) => (
  <PerformantNavLink
    display="flex"
    alignItems="center"
    p={3}
    borderRadius="lg"
    transition="all 0.2s"
    bg={isActive ? 'brand.50' : 'transparent'}
    color={isActive ? 'brand.600' : 'gray.600'}
    _hover={{
      bg: isActive ? 'brand.100' : 'gray.50',
      color: isActive ? 'brand.700' : 'gray.700',
      textDecoration: 'none'
    }}
    _focus={{
      boxShadow: 'outline',
      bg: isActive ? 'brand.100' : 'gray.50'
    }}
    fontWeight={isActive ? 'semibold' : 'medium'}
    {...props}
  />
);

// Header navigation link with minimal styling
export const HeaderNavLink: React.FC<PerformantNavLinkProps> = (props) => (
  <PerformantNavLink
    px={3}
    py={2}
    borderRadius="md"
    transition="all 0.2s"
    _hover={{
      bg: 'gray.100',
      textDecoration: 'none'
    }}
    _focus={{
      boxShadow: 'outline'
    }}
    {...props}
  />
);

// Button-style navigation link
export const ButtonNavLink: React.FC<PerformantNavLinkProps & { variant?: 'solid' | 'outline' | 'ghost' }> = ({
  variant = 'solid',
  ...props
}) => {
  const variantStyles = {
    solid: {
      bg: 'brand.500',
      color: 'white',
      _hover: { bg: 'brand.600' },
      _active: { bg: 'brand.700' }
    },
    outline: {
      border: '1px solid',
      borderColor: 'brand.500',
      color: 'brand.500',
      _hover: { bg: 'brand.50' },
      _active: { bg: 'brand.100' }
    },
    ghost: {
      color: 'brand.500',
      _hover: { bg: 'brand.50' },
      _active: { bg: 'brand.100' }
    }
  };

  return (
    <PerformantNavLink
      display="inline-flex"
      alignItems="center"
      justifyContent="center"
      px={4}
      py={2}
      borderRadius="md"
      fontWeight="medium"
      transition="all 0.2s"
      textDecoration="none"
      _focus={{
        boxShadow: 'outline'
      }}
      {...variantStyles[variant as keyof typeof variantStyles]}
      {...props}
    />
  );
};

// Breadcrumb navigation link
export const BreadcrumbNavLink: React.FC<PerformantNavLinkProps> = (props) => (
  <PerformantNavLink
    color="gray.500"
    fontSize="sm"
    _hover={{
      color: 'brand.500',
      textDecoration: 'underline'
    }}
    _focus={{
      color: 'brand.500',
      textDecoration: 'underline',
      outline: 'none'
    }}
    {...props}
  />
);

// Tab navigation link
export const TabNavLink: React.FC<PerformantNavLinkProps & { isActive?: boolean }> = ({
  isActive = false,
  ...props
}) => (
  <PerformantNavLink
    px={4}
    py={2}
    borderBottom="2px solid"
    borderColor={isActive ? 'brand.500' : 'transparent'}
    color={isActive ? 'brand.600' : 'gray.600'}
    fontWeight={isActive ? 'semibold' : 'medium'}
    transition="all 0.2s"
    _hover={{
      color: isActive ? 'brand.700' : 'brand.500',
      borderColor: isActive ? 'brand.600' : 'brand.200',
      textDecoration: 'none'
    }}
    _focus={{
      color: 'brand.600',
      borderColor: 'brand.500',
      outline: 'none'
    }}
    {...props}
  />
);
