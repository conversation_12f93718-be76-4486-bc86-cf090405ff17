/**
 * Monitoring Data Collector Edge Function
 * Phase 3.3: Monitoring & Alerting Systems
 * Collects and processes performance metrics, errors, and business events
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Types
interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

interface ErrorReport {
  message: string;
  stack?: string;
  timestamp: number;
  url: string;
  userAgent: string;
  userId?: string;
  sessionId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
}

interface BusinessMetric {
  event: string;
  value: number;
  timestamp: number;
  userId?: string;
  properties?: Record<string, any>;
}

interface MonitoringPayload {
  type: 'performance' | 'error' | 'business';
  data: PerformanceMetric[] | ErrorReport[] | BusinessMetric[];
  sessionId: string;
  url: string;
  userAgent: string;
  timestamp: number;
}

// Configuration
const MONITORING_CONFIG = {
  maxBatchSize: 100,
  retentionDays: 30,
  alertThresholds: {
    errorRate: 0.05, // 5% error rate
    responseTime: 3000, // 3 seconds
    crashRate: 0.01 // 1% crash rate
  }
};

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Utility functions
const generateId = () => `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const validatePayload = (payload: any): payload is MonitoringPayload => {
  return (
    payload &&
    typeof payload === 'object' &&
    ['performance', 'error', 'business'].includes(payload.type) &&
    Array.isArray(payload.data) &&
    typeof payload.sessionId === 'string' &&
    typeof payload.url === 'string' &&
    typeof payload.userAgent === 'string' &&
    typeof payload.timestamp === 'number'
  );
};

// Performance metrics processing
const processPerformanceMetrics = async (metrics: PerformanceMetric[], sessionId: string, url: string) => {
  const processedMetrics = metrics.map(metric => ({
    id: generateId(),
    session_id: sessionId,
    metric_name: metric.name,
    metric_value: metric.value,
    timestamp: new Date(metric.timestamp).toISOString(),
    url,
    tags: metric.tags || {},
    metadata: metric.metadata || {},
    created_at: new Date().toISOString()
  }));

  // Insert into performance_metrics table
  const { error } = await supabase
    .from('performance_metrics')
    .insert(processedMetrics);

  if (error) {
    console.error('Failed to insert performance metrics:', error);
    throw error;
  }

  // Check for performance alerts
  await checkPerformanceAlerts(metrics);

  return { processed: processedMetrics.length };
};

// Error reports processing
const processErrorReports = async (errors: ErrorReport[], sessionId: string, url: string) => {
  const processedErrors = errors.map(error => ({
    id: generateId(),
    session_id: sessionId,
    message: error.message,
    stack: error.stack || null,
    timestamp: new Date(error.timestamp).toISOString(),
    url: error.url,
    user_agent: error.userAgent,
    user_id: error.userId || null,
    severity: error.severity,
    context: error.context || {},
    created_at: new Date().toISOString()
  }));

  // Insert into error_reports table
  const { error } = await supabase
    .from('error_reports')
    .insert(processedErrors);

  if (error) {
    console.error('Failed to insert error reports:', error);
    throw error;
  }

  // Check for error alerts
  await checkErrorAlerts(errors);

  return { processed: processedErrors.length };
};

// Business metrics processing
const processBusinessMetrics = async (metrics: BusinessMetric[], sessionId: string, url: string) => {
  const processedMetrics = metrics.map(metric => ({
    id: generateId(),
    session_id: sessionId,
    event_name: metric.event,
    event_value: metric.value,
    timestamp: new Date(metric.timestamp).toISOString(),
    user_id: metric.userId || null,
    properties: metric.properties || {},
    url,
    created_at: new Date().toISOString()
  }));

  // Insert into business_metrics table
  const { error } = await supabase
    .from('business_metrics')
    .insert(processedMetrics);

  if (error) {
    console.error('Failed to insert business metrics:', error);
    throw error;
  }

  return { processed: processedMetrics.length };
};

// Alert checking functions
const checkPerformanceAlerts = async (metrics: PerformanceMetric[]) => {
  for (const metric of metrics) {
    // Check response time alerts
    if (metric.name.includes('response_time') && metric.value > MONITORING_CONFIG.alertThresholds.responseTime) {
      await triggerAlert({
        type: 'performance',
        severity: 'warning',
        message: `High response time detected: ${metric.value}ms`,
        metric: metric.name,
        value: metric.value,
        threshold: MONITORING_CONFIG.alertThresholds.responseTime
      });
    }

    // Check Web Vitals alerts
    if (metric.name === 'web_vitals_lcp' && metric.value > 2500) {
      await triggerAlert({
        type: 'performance',
        severity: 'warning',
        message: `Poor LCP detected: ${metric.value}ms`,
        metric: metric.name,
        value: metric.value,
        threshold: 2500
      });
    }

    if (metric.name === 'web_vitals_fid' && metric.value > 100) {
      await triggerAlert({
        type: 'performance',
        severity: 'warning',
        message: `Poor FID detected: ${metric.value}ms`,
        metric: metric.name,
        value: metric.value,
        threshold: 100
      });
    }

    if (metric.name === 'web_vitals_cls' && metric.value > 0.1) {
      await triggerAlert({
        type: 'performance',
        severity: 'warning',
        message: `Poor CLS detected: ${metric.value}`,
        metric: metric.name,
        value: metric.value,
        threshold: 0.1
      });
    }
  }
};

const checkErrorAlerts = async (errors: ErrorReport[]) => {
  for (const error of errors) {
    // Trigger immediate alert for critical errors
    if (error.severity === 'critical') {
      await triggerAlert({
        type: 'error',
        severity: 'critical',
        message: `Critical error: ${error.message}`,
        error: error.message,
        stack: error.stack,
        url: error.url
      });
    }

    // Check for high error rates (implement rate checking logic)
    // This would typically involve querying recent error counts
  }
};

// Alert triggering
const triggerAlert = async (alert: any) => {
  const alertRecord = {
    id: generateId(),
    type: alert.type,
    severity: alert.severity,
    message: alert.message,
    data: alert,
    timestamp: new Date().toISOString(),
    acknowledged: false,
    created_at: new Date().toISOString()
  };

  // Insert into alerts table
  const { error } = await supabase
    .from('monitoring_alerts')
    .insert([alertRecord]);

  if (error) {
    console.error('Failed to insert alert:', error);
  }

  // Send notification (implement notification logic)
  console.log('Alert triggered:', alert);
};

// Data cleanup function
const cleanupOldData = async () => {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - MONITORING_CONFIG.retentionDays);
  const cutoffISO = cutoffDate.toISOString();

  // Clean up old performance metrics
  await supabase
    .from('performance_metrics')
    .delete()
    .lt('created_at', cutoffISO);

  // Clean up old error reports
  await supabase
    .from('error_reports')
    .delete()
    .lt('created_at', cutoffISO);

  // Clean up old business metrics
  await supabase
    .from('business_metrics')
    .delete()
    .lt('created_at', cutoffISO);

  console.log(`Cleaned up data older than ${MONITORING_CONFIG.retentionDays} days`);
};

// Main handler
serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  // Handle GET requests for health check
  if (req.method === 'GET') {
    return new Response(
      JSON.stringify({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      {
        status: 405,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    const payload = await req.json();

    // Validate payload
    if (!validatePayload(payload)) {
      return new Response(
        JSON.stringify({ error: 'Invalid payload format' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Check batch size
    if (payload.data.length > MONITORING_CONFIG.maxBatchSize) {
      return new Response(
        JSON.stringify({ error: `Batch size exceeds maximum of ${MONITORING_CONFIG.maxBatchSize}` }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    let result;

    // Process based on type
    switch (payload.type) {
      case 'performance':
        result = await processPerformanceMetrics(
          payload.data as PerformanceMetric[],
          payload.sessionId,
          payload.url
        );
        break;

      case 'error':
        result = await processErrorReports(
          payload.data as ErrorReport[],
          payload.sessionId,
          payload.url
        );
        break;

      case 'business':
        result = await processBusinessMetrics(
          payload.data as BusinessMetric[],
          payload.sessionId,
          payload.url
        );
        break;

      default:
        return new Response(
          JSON.stringify({ error: 'Invalid monitoring type' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
    }

    // Periodic cleanup (run occasionally)
    if (Math.random() < 0.01) { // 1% chance
      cleanupOldData().catch(console.error);
    }

    // Check alert rules periodically
    if (Math.random() < 0.05) { // 5% chance
      checkAlertRules().catch(console.error);
    }

    return new Response(
      JSON.stringify({
        success: true,
        type: payload.type,
        ...result,
        timestamp: new Date().toISOString()
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Monitoring collector error:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error.message
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

// Alert rules checking function
const checkAlertRules = async () => {
  try {
    const { data, error } = await supabase.rpc('check_alert_rules');

    if (error) {
      console.error('Failed to check alert rules:', error);
      return;
    }

    if (data > 0) {
      console.log(`Created ${data} new alerts based on rules`);
    }
  } catch (error) {
    console.error('Alert rules check error:', error);
  }
};
