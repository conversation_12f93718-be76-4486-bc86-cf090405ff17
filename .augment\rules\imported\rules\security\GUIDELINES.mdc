---
type: "manual"
priority: "critical"
scope: ["security", "authentication", "authorization", "data-protection", "api-security", "input-validation", "xss-prevention", "csrf-protection"]
last_updated: "2025-01-30"
security_enhancements: ["comprehensive-validation", "advanced-cors", "jwt-validation", "rate-limiting", "xss-prevention"]
---

# Security Guidelines & Implementation (Enhanced)

## 🔐 Authentication & Authorization

### JWT-based Authentication
```typescript
// Supabase Auth configuration
const supabaseAuthConfig = {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
  },
  jwt_expiry: 3600, // 1 hour
  enable_manual_linking: false
};

// Auth context with security checks
interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  isAdmin: boolean;
  isSuperuser: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
}

// Secure session management
const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Get initial session with timeout
    const getSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Session error:', error);
          return;
        }
        
        setSession(session);
        setUser(session?.user ?? null);
        
        // Validate session integrity
        if (session) {
          await validateSessionIntegrity(session);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        // Force logout on auth errors
        await supabase.auth.signOut();
      } finally {
        setLoading(false);
      }
    };
    
    getSession();
    
    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        
        if (event === 'SIGNED_OUT') {
          // Clear sensitive data
          clearUserData();
        }
      }
    );
    
    return () => subscription.unsubscribe();
  }, []);
  
  const validateSessionIntegrity = async (session: Session) => {
    // Verify JWT token hasn't been tampered with
    const { data, error } = await supabase.auth.getUser(session.access_token);
    
    if (error || !data.user) {
      console.error('Session integrity check failed');
      await supabase.auth.signOut();
      throw new Error('Invalid session');
    }
  };
  
  return (
    <AuthContext.Provider value={{ user, session, loading, /* ... */ }}>
      {children}
    </AuthContext.Provider>
  );
};
```

### Role-based Access Control
```typescript
// Permission system
interface UserPermissions {
  canViewTrades: boolean;
  canCreateRobots: boolean;
  canManageUsers: boolean;
  canAccessAdmin: boolean;
  canViewStatistics: boolean;
  canManageSubscriptions: boolean;
}

// Permission checker hook
const usePermissions = (): UserPermissions => {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState<UserPermissions>({
    canViewTrades: false,
    canCreateRobots: false,
    canManageUsers: false,
    canAccessAdmin: false,
    canViewStatistics: false,
    canManageSubscriptions: false
  });
  
  useEffect(() => {
    if (!user) {
      setPermissions({
        canViewTrades: false,
        canCreateRobots: false,
        canManageUsers: false,
        canAccessAdmin: false,
        canViewStatistics: false,
        canManageSubscriptions: false
      });
      return;
    }
    
    const fetchPermissions = async () => {
      const { data: userSettings } = await supabase
        .from('user_settings')
        .select('is_superuser, is_active')
        .eq('id', user.id)
        .single();
      
      setPermissions({
        canViewTrades: userSettings?.is_active || false,
        canCreateRobots: userSettings?.is_active || false,
        canManageUsers: userSettings?.is_superuser || false,
        canAccessAdmin: userSettings?.is_superuser || false,
        canViewStatistics: userSettings?.is_active || false,
        canManageSubscriptions: userSettings?.is_active || false
      });
    };
    
    fetchPermissions();
  }, [user]);
  
  return permissions;
};

// Protected route component
const ProtectedRoute = ({ 
  children, 
  requiredPermission 
}: { 
  children: React.ReactNode;
  requiredPermission?: keyof UserPermissions;
}) => {
  const { user, loading } = useAuth();
  const permissions = usePermissions();
  
  if (loading) return <PageLoader />;
  
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  
  if (requiredPermission && !permissions[requiredPermission]) {
    return <Navigate to="/unauthorized" replace />;
  }
  
  return <>{children}</>;
};
```

## 🛡️ Row Level Security (RLS)

### Database Security Policies
```sql
-- Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE robots ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- User can only access their own profile
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- User settings access control
CREATE POLICY "Users can view own settings" ON user_settings
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own settings" ON user_settings
  FOR UPDATE USING (auth.uid() = id);

-- Trades access control
CREATE POLICY "Users can view own trades" ON trades
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own trades" ON trades
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Prevent users from modifying existing trades
CREATE POLICY "Users cannot update trades" ON trades
  FOR UPDATE USING (false);

CREATE POLICY "Users cannot delete trades" ON trades
  FOR DELETE USING (false);

-- Robot access control
CREATE POLICY "Users can view active robots" ON robots
  FOR SELECT USING (is_active = true AND deleted_at IS NULL);

CREATE POLICY "Sellers can manage own robots" ON robots
  FOR ALL USING (auth.uid() = seller_id);

-- Subscription access control
CREATE POLICY "Users can view own subscriptions" ON subscriptions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own subscriptions" ON subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admin access policies
CREATE POLICY "Admins can view all data" ON trades
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_settings 
      WHERE id = auth.uid() AND is_superuser = TRUE
    )
  );

CREATE POLICY "Admins can manage users" ON user_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_settings 
      WHERE id = auth.uid() AND is_superuser = TRUE
    )
  );
```

### API Security Functions
```sql
-- Secure function to check admin privileges
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM user_settings 
    WHERE id = auth.uid() AND is_superuser = TRUE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Secure function to validate user ownership
CREATE OR REPLACE FUNCTION validate_user_ownership(
  p_user_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
  -- Allow if user is accessing their own data or is admin
  RETURN (auth.uid() = p_user_id) OR is_admin();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Secure RPC function with validation
CREATE OR REPLACE FUNCTION get_user_trades(
  p_user_id UUID,
  p_limit INTEGER DEFAULT 50
) RETURNS TABLE (
  id BIGINT,
  symbol TEXT,
  order_side TEXT,
  quantity NUMERIC,
  price NUMERIC,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  -- Validate user can access this data
  IF NOT validate_user_ownership(p_user_id) THEN
    RAISE EXCEPTION 'Access denied';
  END IF;
  
  RETURN QUERY
  SELECT t.id, t.symbol, t.order_side, t.quantity, t.price, t.created_at
  FROM trades t
  WHERE t.user_id = p_user_id
  ORDER BY t.created_at DESC
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 🔒 API Key & Token Security

### Encryption & Storage
```typescript
// API key encryption using Supabase Vault
const secureApiKeyStorage = {
  async storeApiKey(userId: string, apiKey: string, token: string) {
    try {
      // Encrypt and store in Vault
      await upsertVaultSecret(supabase, `api_key_${userId}`, apiKey);
      await upsertVaultSecret(supabase, `token_${userId}`, token);
      
      // Update user settings with encryption flags
      const { error } = await supabase
        .from('user_settings')
        .update({
          api_key_set: true,
          token_set: true,
          encrypted_api_key: 'vault:encrypted',
          encrypted_token: 'vault:encrypted'
        })
        .eq('id', userId);
      
      if (error) throw error;
      
      return { success: true };
    } catch (error) {
      console.error('API key storage error:', error);
      throw new Error('Failed to store API credentials securely');
    }
  },
  
  async retrieveApiKey(userId: string): Promise<{ apiKey: string; token: string } | null> {
    try {
      const apiKey = await getVaultSecret(supabase, `api_key_${userId}`);
      const token = await getVaultSecret(supabase, `token_${userId}`);
      
      if (!apiKey || !token) {
        return null;
      }
      
      return { apiKey, token };
    } catch (error) {
      console.error('API key retrieval error:', error);
      return null;
    }
  },
  
  async deleteApiKey(userId: string) {
    try {
      await deleteVaultSecret(supabase, `api_key_${userId}`);
      await deleteVaultSecret(supabase, `token_${userId}`);
      
      await supabase
        .from('user_settings')
        .update({
          api_key_set: false,
          token_set: false,
          encrypted_api_key: null,
          encrypted_token: null
        })
        .eq('id', userId);
      
      return { success: true };
    } catch (error) {
      console.error('API key deletion error:', error);
      throw error;
    }
  }
};
```

### Input Validation & Sanitization
```typescript
// Input validation schemas
import { z } from 'zod';

const tradingSignalSchema = z.object({
  signal_name: z.string().min(1).max(100).regex(/^[a-zA-Z0-9\s]+$/),
  symbol: z.string().min(2).max(10).regex(/^[A-Z]+$/),
  price: z.number().positive().max(999999),
  quantity: z.number().positive().max(999999),
  order_side: z.enum(['BUY', 'SELL'])
});

const userRegistrationSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8).max(128),
  username: z.string().min(3).max(30).regex(/^[a-zA-Z0-9_]+$/),
  full_name: z.string().min(1).max(100)
});

// Validation middleware
const validateInput = <T>(schema: z.ZodSchema<T>) => {
  return (input: unknown): T => {
    try {
      return schema.parse(input);
    } catch (error) {
      if (error instanceof z.ZodError) {
        throw new Error(`Validation error: ${error.errors.map(e => e.message).join(', ')}`);
      }
      throw error;
    }
  };
};

// Usage in API functions
const processSignal = async (rawSignalData: unknown) => {
  // Validate input
  const signalData = validateInput(tradingSignalSchema)(rawSignalData);
  
  // Sanitize string inputs
  const sanitizedSignal = {
    ...signalData,
    signal_name: sanitizeString(signalData.signal_name),
    symbol: signalData.symbol.toUpperCase()
  };
  
  // Process validated and sanitized data
  return await createTradeFromSignal(sanitizedSignal);
};

// String sanitization
const sanitizeString = (input: string): string => {
  return input
    .trim()
    .replace(/[<>\"'&]/g, '') // Remove potentially dangerous characters
    .substring(0, 1000); // Limit length
};
```

## 🌐 API Security

### Rate Limiting
```typescript
// Rate limiting implementation
class RateLimiter {
  private static requests = new Map<string, number[]>();
  private static readonly WINDOW_SIZE = 60 * 1000; // 1 minute
  private static readonly MAX_REQUESTS = {
    webhook: 30,      // 30 requests per minute for webhooks
    api: 100,         // 100 requests per minute for API
    auth: 5           // 5 auth attempts per minute
  };
  
  static checkRateLimit(
    identifier: string, 
    type: keyof typeof RateLimiter.MAX_REQUESTS
  ): boolean {
    const now = Date.now();
    const windowStart = now - this.WINDOW_SIZE;
    
    if (!this.requests.has(identifier)) {
      this.requests.set(identifier, []);
    }
    
    const userRequests = this.requests.get(identifier)!;
    
    // Remove old requests outside the window
    const validRequests = userRequests.filter(time => time > windowStart);
    
    if (validRequests.length >= this.MAX_REQUESTS[type]) {
      return false; // Rate limit exceeded
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
    
    return true;
  }
  
  static getRemainingRequests(
    identifier: string,
    type: keyof typeof RateLimiter.MAX_REQUESTS
  ): number {
    const now = Date.now();
    const windowStart = now - this.WINDOW_SIZE;
    
    const userRequests = this.requests.get(identifier) || [];
    const validRequests = userRequests.filter(time => time > windowStart);
    
    return Math.max(0, this.MAX_REQUESTS[type] - validRequests.length);
  }
}

// Rate limiting middleware for Edge Functions
const withRateLimit = (
  handler: (req: Request) => Promise<Response>,
  type: keyof typeof RateLimiter.MAX_REQUESTS
) => {
  return async (req: Request): Promise<Response> => {
    const identifier = req.headers.get('x-forwarded-for') || 'unknown';
    
    if (!RateLimiter.checkRateLimit(identifier, type)) {
      return new Response(JSON.stringify({
        error: 'Rate limit exceeded',
        retry_after: 60
      }), {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': '60'
        }
      });
    }
    
    return handler(req);
  };
};
```

### CORS Security
```typescript
// Secure CORS configuration
export const getCorsHeaders = (requestHeaders: Headers): Headers => {
  const corsHeaders = new Headers();
  
  // Get origin from request
  const origin = requestHeaders.get('origin');
  
  // Allowed origins
  const allowedOrigins = [
    'https://algobir.vercel.app',
    'https://algobir.com',
    'http://localhost:3000',
    'http://127.0.0.1:3000'
  ];
  
  // Set CORS headers
  if (origin && allowedOrigins.includes(origin)) {
    corsHeaders.set('Access-Control-Allow-Origin', origin);
  }
  
  corsHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  corsHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-client-info');
  corsHeaders.set('Access-Control-Max-Age', '86400'); // 24 hours
  corsHeaders.set('Access-Control-Allow-Credentials', 'true');
  
  return corsHeaders;
};
```

## 🔍 Security Monitoring

### Audit Logging
```sql
-- Audit log table
CREATE TABLE audit_logs (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id TEXT,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO audit_logs (
    user_id, action, resource_type, resource_id,
    old_values, new_values, ip_address
  ) VALUES (
    auth.uid(),
    TG_OP,
    TG_TABLE_NAME,
    COALESCE(NEW.id::TEXT, OLD.id::TEXT),
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN to_jsonb(NEW) ELSE NULL END,
    inet_client_addr()
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER audit_user_settings
  AFTER INSERT OR UPDATE OR DELETE ON user_settings
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_trades
  AFTER INSERT OR UPDATE OR DELETE ON trades
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

### Security Alerts
```typescript
// Security monitoring and alerting
class SecurityMonitor {
  static async logSecurityEvent(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    details: any
  ) {
    const securityEvent = {
      event,
      severity,
      details,
      timestamp: new Date().toISOString(),
      user_id: details.user_id || null,
      ip_address: details.ip_address || null
    };
    
    // Log to database
    await supabase
      .from('security_events')
      .insert([securityEvent]);
    
    // Send alert for high/critical events
    if (severity === 'high' || severity === 'critical') {
      await this.sendSecurityAlert(securityEvent);
    }
  }
  
  private static async sendSecurityAlert(event: any) {
    // Send to admin notification system
    await supabase.rpc('create_admin_notification', {
      p_title: `Security Alert: ${event.event}`,
      p_message: `Severity: ${event.severity}\nDetails: ${JSON.stringify(event.details)}`,
      p_type: 'security_alert',
      p_severity: 'error'
    });
  }
  
  // Monitor for suspicious activities
  static async checkSuspiciousActivity(userId: string, action: string) {
    const recentActions = await supabase
      .from('audit_logs')
      .select('*')
      .eq('user_id', userId)
      .eq('action', action)
      .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString());
    
    if (recentActions.data && recentActions.data.length > 10) {
      await this.logSecurityEvent('suspicious_activity', 'high', {
        user_id: userId,
        action,
        count: recentActions.data.length,
        timeframe: '5_minutes'
      });
    }
  }
}
```

## Update Requirements

## 🛡️ Advanced Security Enhancements (2025-01-30)

### Comprehensive Input Validation & Sanitization
```typescript
// security.ts - Advanced security utilities
import { sanitizeString, sanitizeEmail, isValidEmail, validateForm } from '../utils/security';

// Input sanitization functions
export const sanitizeString = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .slice(0, 1000); // Limit length
};

// XSS Prevention
export const escapeHtml = (unsafe: string): string => {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
};

// Form validation with security
const userSchema = {
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    maxLength: 254,
    message: 'Please enter a valid email address'
  },
  password: {
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,128}$/,
    message: 'Password must be 8-128 characters with uppercase, lowercase, and number'
  }
};

const { isValid, errors } = validateForm(formData, userSchema);
```

### Enhanced CORS Configuration
```typescript
// cors.ts - Advanced CORS with security
export function getCorsHeaders(requestHeaders: Headers): Headers {
  const origin = requestHeaders.get('Origin') || '';
  const headers = new Headers();

  // Environment-based allowed origins
  const allowedOrigins = getAllowedOrigins();
  const isValidOrigin = allowedOrigins.includes(origin);

  // Rate limiting for CORS requests
  if (origin && !checkCorsRateLimit(origin)) {
    console.warn(`[CORS] Rate limit exceeded for origin: ${origin}`);
    return headers; // Don't set CORS headers for rate-limited origins
  }

  // Enhanced security headers
  if (isValidOrigin) {
    headers.set('Access-Control-Allow-Origin', origin);
    headers.set('Access-Control-Allow-Credentials', 'true');
  }

  headers.set('X-Content-Type-Options', 'nosniff');
  headers.set('X-Frame-Options', 'DENY');
  headers.set('X-XSS-Protection', '1; mode=block');
  headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  return headers;
}
```

### Advanced JWT Validation
```typescript
// jwt-validation.ts - Comprehensive JWT security
export async function validateJWTWithSupabase(
  token: string,
  request?: Request
): Promise<JWTValidationResult> {
  // Rate limiting check
  const clientIP = request?.headers.get('x-forwarded-for') || 'unknown';
  if (!checkJWTRateLimit(clientIP)) {
    return { isValid: false, error: 'Rate limit exceeded' };
  }

  // Format validation
  if (!isValidJWTFormat(token)) {
    return { isValid: false, error: 'Invalid JWT format' };
  }

  // Expiration check
  if (isJWTExpired(token)) {
    return { isValid: false, error: 'JWT token has expired' };
  }

  // Supabase validation with additional security checks
  const { data, error } = await supabase.auth.getUser(token);

  if (error || !data.user) {
    return { isValid: false, error: error?.message || 'User not found' };
  }

  // Additional security validations
  const payload = decodeJWTPayload(token);
  if (payload?.aud !== 'authenticated') {
    return { isValid: false, error: 'Invalid token audience' };
  }

  return {
    isValid: true,
    user: data.user,
    userId: data.user.id,
    email: data.user.email
  };
}
```

### Advanced Rate Limiting
```typescript
// rate-limiter.ts - Multi-strategy rate limiting
export enum RateLimitStrategy {
  FIXED_WINDOW = 'fixed_window',
  SLIDING_WINDOW = 'sliding_window',
  TOKEN_BUCKET = 'token_bucket',
  ADAPTIVE = 'adaptive'
}

export const RateLimitPresets = {
  API_STRICT: { windowMs: 60000, maxRequests: 10 },
  API_MODERATE: { windowMs: 60000, maxRequests: 30 },
  WEBHOOK_TRADING: { windowMs: 60000, maxRequests: 60 },
  AUTH_LOGIN: { windowMs: 900000, maxRequests: 5 }, // 5 attempts per 15 min
  ADMIN_OPERATIONS: { windowMs: 60000, maxRequests: 20 }
};

// Usage with middleware
export function withRateLimit(config: RateLimitConfig, strategy = RateLimitStrategy.ADAPTIVE) {
  return (handler: (request: Request) => Promise<Response>) => {
    return async (request: Request): Promise<Response> => {
      const identifier = globalRateLimiter.getIdentifier(request);
      const result = globalRateLimiter.check(identifier, config, strategy);

      if (!result.allowed) {
        return new Response(JSON.stringify({
          error: 'Rate limit exceeded',
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
        }), { status: 429 });
      }

      return handler(request);
    };
  };
}
```

### CSRF Protection
```typescript
// CSRF token management
export const generateCSRFToken = (): string => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

export const validateCSRFToken = (token: string): boolean => {
  const storedToken = sessionStorage.getItem('csrf_token');
  return storedToken !== null && storedToken === token;
};

// Usage in forms
const csrfToken = generateCSRFToken();
setCSRFToken(csrfToken);

// Include in API requests
headers: {
  'X-CSRF-Token': getCSRFToken(),
  'Content-Type': 'application/json'
}
```

### Security Headers
```typescript
// Security headers for all responses
export const getSecurityHeaders = (): Record<string, string> => {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
  };
};
```

## 📝 Recent Security Implementations (2025-01-30)

### Bio Textarea Security Enhancement
```typescript
// Real-time input sanitization in Profile.tsx
onChange={(e) => {
  const newValue = e.target.value;
  if (newValue.length <= 500) {
    // Basic sanitization - remove potential HTML tags
    const sanitizedValue = newValue.replace(/[<>]/g, '');
    setFormData(prev => ({ ...prev, bio: sanitizedValue }));
  }
}}

// Auto-save with comprehensive sanitization
const sanitizedBio = formData.bio
  .replace(/[<>]/g, '')           // Remove HTML tags
  .replace(/javascript:/gi, '')   // Remove javascript: protocol
  .trim()
  .slice(0, 500);                // Enforce length limit
```

### Input Validation Pattern Applied
```typescript
// Following security guidelines for user input
const validateAndSanitizeBio = (input: string): string => {
  return input
    .replace(/[<>]/g, '')           // XSS prevention
    .replace(/javascript:/gi, '')   // Script injection prevention
    .replace(/on\w+=/gi, '')       // Event handler removal
    .trim()                        // Remove whitespace
    .slice(0, 500);               // Length enforcement
};
```

### When to Update This File
- New security vulnerabilities discovered
- Authentication system changes
- Authorization policy modifications
- API security improvements
- Data protection regulation updates
- Security monitoring enhancements
- Input validation improvements
- Rate limiting adjustments
- CORS policy changes
- Real-time input sanitization implementations

### Related Files to Update
- Update `backend/SUPABASE_EDGE.mdc` for database security
- Update `frontend/MAIN_APP.mdc` for client-side security
- Update `features/ADMIN_PANEL.mdc` for admin security
- Update `core/PROJECT_CORE.mdc` for security architecture
- Update `deployment/PRODUCTION.mdc` for production security
- Update `performance/OPTIMIZATION.mdc` for security performance impact
