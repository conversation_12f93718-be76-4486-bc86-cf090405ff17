import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Button, 
  FormControl, 
  FormLabel, 
  Input, 
  VStack,
  Checkbox, 
  Text, 
  Flex, 
  InputGroup, 
  InputRightElement, 
  IconButton,
  Link,
  Alert, 
  AlertIcon, 
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton, 
} from '@chakra-ui/react';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { supabase } from '../../supabaseClient';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [resetPasswordEmail, setResetPasswordEmail] = useState('');
  const [isPasswordResetModalOpen, setIsPasswordResetModalOpen] = useState(false);
  const [resetLoading, setResetLoading] = useState(false);
  const navigate = useNavigate();
  const toast = useToast();

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        setError(error.message);
        toast({
          title: 'Giriş başarısız',
          description: error.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } else if (data.session) {
        // Beni hatırla seçili olduğunda tarayıcıda session süresini uzatabilirsiniz
        if (rememberMe) {
          // İlgili işlem
        }
        
        navigate('/');
      }
    } catch (err) {
      const errorMessage = (err as Error).message;
      setError(errorMessage);
      toast({
        title: 'Bir hata oluştu',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setResetLoading(true);
    setError(null);

    try {
      // Şifre sıfırlama e-postası gönder
      const { error } = await supabase.auth.resetPasswordForEmail(resetPasswordEmail, {
        redirectTo: window.location.origin + '/reset-password',
      });

      if (error) {
        toast({
          title: 'Şifre sıfırlama başarısız',
          description: error.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } else {
        toast({
          title: 'Şifre sıfırlama bağlantısı gönderildi',
          description: 'Lütfen e-posta adresinizi kontrol edin ve talimatları izleyin.',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        setIsPasswordResetModalOpen(false);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Bilinmeyen bir hata oluştu';
      toast({
        title: 'Bir hata oluştu',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setResetLoading(false);
    }
  };

  return (
    <Box as="form" onSubmit={onSubmit}>
      {error && (
        <Alert status="error" mb={5} borderRadius="md">
          <AlertIcon />
          {error}
        </Alert>
      )}

      <VStack spacing={5}>
        <FormControl isRequired>
          <FormLabel fontWeight="medium">E-posta</FormLabel>
          <InputGroup>
            <Input 
              type="email" 
              placeholder="E-posta adresiniz" 
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              size="lg"
              borderRadius="md"
              autoComplete="email"
              _focus={{ borderColor: "brand.500", boxShadow: "0 0 0 1px var(--chakra-colors-brand-500)" }}
            />
          </InputGroup>
        </FormControl>
        
        <FormControl isRequired>
          <FormLabel fontWeight="medium">Şifre</FormLabel>
          <InputGroup size="lg">
            <Input 
              type={showPassword ? "text" : "password"}
              placeholder="Şifreniz" 
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              borderRadius="md"
              autoComplete="current-password"
              _focus={{ borderColor: "brand.500", boxShadow: "0 0 0 1px var(--chakra-colors-brand-500)" }}
            />
            <InputRightElement>
              <IconButton
                bg="transparent"
                variant="ghost"
                aria-label={showPassword ? "Şifreyi gizle" : "Şifreyi göster"}
                icon={showPassword ? <FaEyeSlash /> : <FaEye />}
                onClick={toggleShowPassword}
                size="sm"
                _focus={{ boxShadow: "none" }}
              />
            </InputRightElement>
          </InputGroup>
        </FormControl>
        
        <Flex width="100%" justifyContent="space-between" alignItems="center">
          <Checkbox 
            colorScheme="brand"
            isChecked={rememberMe}
            onChange={(e) => setRememberMe(e.target.checked)}
          >
            <Text fontSize="sm">Beni Hatırla</Text>
          </Checkbox>
          <Link 
            fontSize="sm" 
            color="brand.500" 
            _hover={{ color: "brand.600", textDecoration: "none" }}
            fontWeight="medium"
            onClick={() => {
              setResetPasswordEmail(email);
              setIsPasswordResetModalOpen(true);
            }}
            cursor="pointer"
          >
            Şifremi Unuttum
          </Link>
        </Flex>
        
        <Button 
          colorScheme="brand" 
          width="100%"
          size="lg"
          mt={2}
          type="submit"
          isLoading={isLoading}
          isDisabled={isLoading}
          loadingText="Giriş yapılıyor..."
          boxShadow="md"
          _hover={{
            transform: "translateY(-1px)",
            boxShadow: "lg",
          }}
        >
          Giriş Yap
        </Button>
      </VStack>

      {/* Şifre Sıfırlama Modal */}
      <Modal isOpen={isPasswordResetModalOpen} onClose={() => setIsPasswordResetModalOpen(false)}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Şifre Sıfırlama</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <Text mb={4}>
              E-posta adresinizi girin ve şifre sıfırlama bağlantısı size gönderilecektir.
            </Text>
            <FormControl>
              <FormLabel>E-posta Adresi</FormLabel>
              <Input 
                placeholder="E-posta adresiniz"
                value={resetPasswordEmail}
                onChange={(e) => setResetPasswordEmail(e.target.value)}
              />
            </FormControl>
          </ModalBody>

          <ModalFooter>
            <Button 
              colorScheme="brand" 
              mr={3} 
              onClick={handleResetPassword}
              isLoading={resetLoading}
              loadingText="Gönderiliyor..."
            >
              Sıfırlama Bağlantısı Gönder
            </Button>
            <Button onClick={() => setIsPasswordResetModalOpen(false)}>İptal</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default Login; 