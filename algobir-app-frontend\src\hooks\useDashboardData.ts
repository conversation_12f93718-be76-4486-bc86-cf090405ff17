import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '../supabaseClient';
import { Trade } from '../types/trade';
import { useAuth } from '../context/AuthContext';

export const useDashboardData = () => {
  const { user } = useAuth();
  const [totalInvestment, setTotalInvestment] = useState(0);
  const [totalPnL, setTotalPnL] = useState(0);
  const [openPositionsCount, setOpenPositionsCount] = useState(0);
  const [totalTradesCount, setTotalTradesCount] = useState(0);
  const [winRate, setWinRate] = useState(0);
  const [last10ClosedTrades, setLast10ClosedTrades] = useState<Trade[]>([]);
  const [todayTradesCount, setTodayTradesCount] = useState(0);
  const [lastTradeTime, setLastTradeTime] = useState<string | null>(null);
  const [apiCredentialsExpiryDate, setApiCredentialsExpiryDate] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Performance optimization: Cache dashboard data (90 seconds cache)
  const dashboardCache = useRef<{
    data: {
      totalInvestment: number;
      totalPnL: number;
      openPositionsCount: number;
      totalTradesCount: number;
      winRate: number;
      last10ClosedTrades: Trade[];
      todayTradesCount: number;
      lastTradeTime: string | null;
      apiCredentialsExpiryDate: string | null;
    };
    timestamp: number;
    userId: string;
  } | null>(null);
  const CACHE_DURATION = 90000; // 90 seconds for dashboard (more frequent updates needed)

  const fetchData = useCallback(async () => {
    if (!user) return;
    setLoading(true);
    setError(null);

    try {
      // Check cache first
      const now = Date.now();
      if (dashboardCache.current && 
          dashboardCache.current.userId === user.id &&
          (now - dashboardCache.current.timestamp < CACHE_DURATION)) {
        console.log('Using cached dashboard data');
        const cachedData = dashboardCache.current.data;
        setTotalInvestment(cachedData.totalInvestment);
        setTotalPnL(cachedData.totalPnL);
        setOpenPositionsCount(cachedData.openPositionsCount);
        setTotalTradesCount(cachedData.totalTradesCount);
        setWinRate(cachedData.winRate);
        setLast10ClosedTrades(cachedData.last10ClosedTrades);
        setTodayTradesCount(cachedData.todayTradesCount);
        setLastTradeTime(cachedData.lastTradeTime);
        setApiCredentialsExpiryDate(cachedData.apiCredentialsExpiryDate);
        setLoading(false);
        return;
      }
      // Fetch user settings for total investment and API credentials expiry date
      const { data: settingsData, error: settingsError } = await supabase
        .from('user_settings')
        .select('total_investment_amount, api_credentials_expiry_date')
        .eq('id', user.id)
        .single();

      if (settingsError) throw settingsError;
      setTotalInvestment(settingsData?.total_investment_amount || 0);
      setApiCredentialsExpiryDate(settingsData?.api_credentials_expiry_date || null);

      // Fetch trades data for calculations
      const { data: tradesData, error: tradesError } = await supabase
        .from('trades')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_deleted', false);

      if (tradesError) throw tradesError;
      
      setTotalTradesCount(tradesData.length);

      const closedTrades = tradesData.filter(t => t.position_status === 'Kapalı' && t.pnl !== null);
      const openTrades = tradesData.filter(t => t.position_status === 'Açık');
      
      setOpenPositionsCount(openTrades.length);

      const totalPnlAmount = closedTrades.reduce((acc, trade) => acc + (trade.pnl || 0), 0);
      setTotalPnL(totalPnlAmount);
      
      const winningTrades = closedTrades.filter(t => t.pnl && t.pnl > 0);
      const winRateValue = closedTrades.length > 0 ? (winningTrades.length / closedTrades.length) * 100 : 0;
      setWinRate(winRateValue);

      // Calculate today's trades count
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTrades = tradesData.filter(trade => {
        const tradeDate = new Date(trade.received_at);
        tradeDate.setHours(0, 0, 0, 0);
        return tradeDate.getTime() === today.getTime();
      });
      setTodayTradesCount(todayTrades.length);

      // Get last trade time
      const sortedTrades = tradesData
        .filter(trade => trade.received_at)
        .sort((a, b) => new Date(b.received_at).getTime() - new Date(a.received_at).getTime());
      setLastTradeTime(sortedTrades.length > 0 ? sortedTrades[0].received_at : null);

      // Fetch last 10 closed trades specifically for the new table
      const { data: lastTradesData, error: lastTradesError } = await supabase
        .from('trades')
        .select('*')
        .eq('user_id', user.id)
        .eq('position_status', 'Kapalı')
        .eq('order_side', 'SELL')
        .not('pnl_calculated_at', 'is', null)
        .order('pnl_calculated_at', { ascending: false })
        .limit(10);
      
      if (lastTradesError) throw lastTradesError;
      setLast10ClosedTrades(lastTradesData || []);

      // Cache all the data
      dashboardCache.current = {
        data: {
          totalInvestment: settingsData?.total_investment_amount || 0,
          totalPnL: totalPnlAmount,
          openPositionsCount: openTrades.length,
          totalTradesCount: tradesData.length,
          winRate: winRateValue,
          last10ClosedTrades: lastTradesData || [],
          todayTradesCount: todayTrades.length,
          lastTradeTime: sortedTrades.length > 0 ? sortedTrades[0].received_at : null,
          apiCredentialsExpiryDate: settingsData?.api_credentials_expiry_date || null
        },
        timestamp: now,
        userId: user.id
      };

    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      setError(error instanceof Error ? error.message : 'Veri yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    loading,
    error,
    totalInvestment,
    totalPnL,
    openPositionsCount,
    totalTradesCount,
    winRate,
    last10ClosedTrades,
    todayTradesCount,
    lastTradeTime,
    apiCredentialsExpiryDate,
    refetch: fetchData
  };
}; 