import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Badge,
  useColorModeValue,
  Icon,
  Tooltip,
  Divider,
  Collapse,
  useDisclosure
} from '@chakra-ui/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  FiTrendingUp, 
  FiBar<PERSON>hart,
  FiPieChart, 
  FiActivity,
  FiTarget,
  FiShield,
  FiClock,
  FiUsers,
  FiFileText,
  FiDownload,
  FiChevronDown,
  FiChevronRight
} from 'react-icons/fi';
import { EnhancedStatsData } from '../../hooks/useEnhancedStatistics';

interface NavigationItem {
  id: string;
  label: string;
  icon: any;
  description?: string;
  badge?: string | number;
  color?: string;
  children?: NavigationItem[];
}

interface StatisticsNavigationProps {
  stats: EnhancedStatsData;
  isMobile?: boolean;
}

const StatisticsNavigation: React.FC<StatisticsNavigationProps> = ({
  stats,
  isMobile = false
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Extract current tab from URL path
  const currentPath = location.pathname;
  const activeTab = currentPath.split('/').pop() || 'overview';
  const cardBg = useColorModeValue('white', 'gray.700');
  const textColor = useColorModeValue('gray.700', 'white');
  const hoverBg = useColorModeValue('gray.50', 'gray.600');
  const activeBg = useColorModeValue('blue.50', 'blue.900');
  const activeColor = useColorModeValue('blue.600', 'blue.300');

  // Navigation structure
  const navigationItems: NavigationItem[] = [
    {
      id: 'overview',
      label: 'Genel Bakış',
      icon: FiTrendingUp,
      description: 'Performans özeti ve temel metrikler',
      badge: stats.totalTrades,
      color: 'blue'
    },
    {
      id: 'roi-analysis',
      label: 'ROI Analizi',
      icon: FiTarget,
      description: 'Yatırım getirisi ve karlılık analizi',
      badge: `${stats.roiAnalysis.totalROI.toFixed(1)}%`,
      color: stats.roiAnalysis.totalROI > 0 ? 'green' : 'red'
    },
    {
      id: 'robot-analysis',
      label: 'Robot Analizi',
      icon: FiActivity,
      description: 'Robot performans karşılaştırması',
      badge: stats.realtimeMetrics.activeRobots,
      color: 'purple',
      children: [
        {
          id: 'solo-robot',
          label: 'Solo-Robot',
          icon: FiUsers,
          badge: stats.soloRobotStats ? stats.soloRobotStats.totalTrades : 0,
          color: 'cyan'
        },
        {
          id: 'bro-robots',
          label: 'Bro-Robotlar',
          icon: FiUsers,
          badge: stats.broRobotStats.length,
          color: 'orange'
        },
        {
          id: 'robot-comparison',
          label: 'Robot Karşılaştırması',
          icon: FiBarChart,
          color: 'teal'
        }
      ]
    },
    {
      id: 'performance',
      label: 'Performans Analizi',
      icon: FiBarChart,
      description: 'Detaylı performans metrikleri',
      color: 'green',
      children: [
        {
          id: 'profit-loss',
          label: 'Kar/Zarar Analizi',
          icon: FiTrendingUp,
          color: 'green'
        },
        {
          id: 'win-rate',
          label: 'Kazanma Oranı',
          icon: FiTarget,
          badge: `${stats.winRate.toFixed(1)}%`,
          color: stats.winRate > 50 ? 'green' : 'red'
        },
        {
          id: 'trading-patterns',
          label: 'İşlem Desenleri',
          icon: FiActivity,
          color: 'blue'
        }
      ]
    },
    {
      id: 'risk-analysis',
      label: 'Risk Analizi',
      icon: FiShield,
      description: 'Risk metrikleri ve güvenlik analizi',
      badge: `${stats.maxDrawdownPercent.toFixed(1)}%`,
      color: 'red',
      children: [
        {
          id: 'drawdown',
          label: 'Drawdown Analizi',
          icon: FiShield,
          color: 'red'
        },
        {
          id: 'volatility',
          label: 'Volatilite',
          icon: FiActivity,
          color: 'orange'
        },
        {
          id: 'risk-metrics',
          label: 'Risk Metrikleri',
          icon: FiBarChart,
          color: 'yellow'
        }
      ]
    },
    {
      id: 'time-analysis',
      label: 'Zaman Analizi',
      icon: FiClock,
      description: 'Zamansal performans analizi',
      color: 'indigo',
      children: [
        {
          id: 'hourly',
          label: 'Saatlik Analiz',
          icon: FiClock,
          color: 'indigo'
        },
        {
          id: 'daily',
          label: 'Günlük Analiz',
          icon: FiClock,
          color: 'blue'
        },
        {
          id: 'monthly',
          label: 'Aylık Analiz',
          icon: FiClock,
          color: 'purple'
        }
      ]
    },
    {
      id: 'symbol-analysis',
      label: 'Sembol Analizi',
      icon: FiPieChart,
      description: 'Sembol bazlı performans',
      color: 'pink'
    },
    {
      id: 'reports',
      label: 'Raporlar',
      icon: FiFileText,
      description: 'Detaylı raporlar ve dışa aktarma',
      color: 'gray',
      children: [
        {
          id: 'detailed-report',
          label: 'Detaylı Rapor',
          icon: FiFileText,
          color: 'gray'
        },
        {
          id: 'export-data',
          label: 'Veri Dışa Aktarma',
          icon: FiDownload,
          color: 'blue'
        }
      ]
    }
  ];

  const NavigationButton: React.FC<{ 
    item: NavigationItem; 
    level?: number;
    isChild?: boolean;
  }> = ({ item, level = 0, isChild = false }) => {
    const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: true });
    const hasChildren = item.children && item.children.length > 0;
    const isActive = activeTab === item.id;
    const paddingLeft = level * 4 + (isChild ? 8 : 4);

    return (
      <Box>
        <Tooltip 
          label={item.description} 
          placement="right" 
          isDisabled={isMobile || !item.description}
        >
          <Button
            variant="ghost"
            justifyContent="flex-start"
            w="full"
            h="auto"
            py={3}
            px={paddingLeft}
            bg={isActive ? activeBg : 'transparent'}
            color={isActive ? activeColor : textColor}
            _hover={{ bg: isActive ? activeBg : hoverBg }}
            onClick={() => {
              if (hasChildren) {
                onToggle();
              } else {
                navigate(`/statistics/${item.id}`);
              }
            }}
            borderRadius="md"
            fontWeight={isActive ? 'semibold' : 'normal'}
            fontSize={isChild ? 'sm' : 'md'}
          >
            <HStack spacing={3} w="full">
              <HStack spacing={2} flex={1}>
                {hasChildren && (
                  <Icon 
                    as={isOpen ? FiChevronDown : FiChevronRight} 
                    boxSize={3}
                  />
                )}
                <Icon as={item.icon} boxSize={isChild ? 4 : 5} />
                <Text fontSize={isChild ? 'sm' : 'md'} noOfLines={1}>
                  {item.label}
                </Text>
              </HStack>
              
              {item.badge !== undefined && (
                <Badge 
                  colorScheme={item.color} 
                  variant="subtle" 
                  fontSize="xs"
                  borderRadius="full"
                >
                  {item.badge}
                </Badge>
              )}
            </HStack>
          </Button>
        </Tooltip>

        {hasChildren && (
          <Collapse in={isOpen} animateOpacity>
            <VStack spacing={1} align="stretch" mt={1}>
              {item.children!.map((child) => (
                <NavigationButton 
                  key={child.id} 
                  item={child} 
                  level={level + 1}
                  isChild={true}
                />
              ))}
            </VStack>
          </Collapse>
        )}
      </Box>
    );
  };

  return (
    <Box bg={cardBg} borderRadius="lg" p={4}>
      <VStack spacing={1} align="stretch">
        <Text fontSize="md" fontWeight="bold" color={textColor} mb={2}>
          📊 Analiz Menüsü
        </Text>
        
        {navigationItems.map((item, index) => (
          <Box key={item.id}>
            <NavigationButton item={item} />
            {index < navigationItems.length - 1 && (
              <Divider my={2} opacity={0.3} />
            )}
          </Box>
        ))}
      </VStack>
    </Box>
  );
};

export default StatisticsNavigation;
