import { test, expect } from '@playwright/test';
import { 
  loginUser, 
  goToNotifications,
  simulateRobotSignal,
  waitForPageLoad,
  expectToast,
  findNotificationByTitle,
  disableDebugMode
} from './utils/test-helpers';

test.describe('Robot Bildirim Entegrasyonu', () => {
  test.beforeEach(async ({ page }) => {
    await loginUser(page);
    await waitForPageLoad(page);
  });

  test('Solo-robot BUY sinyali bildirimi oluşturuyor', async ({ page }) => {
    // Management sayfasına git
    await page.goto('/management');
    await waitForPageLoad(page);
    
    // Solo-robot BUY test butonunu bul
    const buyTestButton = page.locator('button:has-text("Solo BUY Test"), button:has-text("BUY Test")');
    
    if (await buyTestButton.isVisible()) {
      await buyTestButton.click();
      
      // Toast mesajını bekle
      await expectToast(page, 'başarılı', 'success');
      
      // Bildirimlere git ve yeni bildirimi kontrol et
      await goToNotifications(page);
      await disableDebugMode(page);
      
      // "Yeni Pozisyon Açıldı" bildirimi aranır
      const notification = await findNotificationByTitle(page, 'Yeni Pozisyon Açıldı');
      if (await notification.isVisible()) {
        await expect(notification).toBeVisible();
        
        // Bildirim içeriğini kontrol et
        const message = await notification.locator('p').textContent();
        expect(message).toContain('alındı');
        expect(message).toContain('Solo-robot');
        expect(message).toMatch(/\d+,\d+ TL/); // Fiyat formatı
        expect(message).toMatch(/₺[\d,]+/); // Toplam tutar formatı
      }
    }
  });

  test('Solo-robot SELL sinyali bildirimi oluşturuyor', async ({ page }) => {
    await page.goto('/management');
    await waitForPageLoad(page);
    
    // Solo-robot SELL test butonunu bul
    const sellTestButton = page.locator('button:has-text("Solo SELL Test"), button:has-text("SELL Test")');
    
    if (await sellTestButton.isVisible()) {
      await sellTestButton.click();
      
      // Toast mesajını bekle
      await expectToast(page, 'başarılı', 'success');
      
      // Bildirimlere git ve yeni bildirimi kontrol et
      await goToNotifications(page);
      await disableDebugMode(page);
      
      // "Pozisyon Kapatıldı" bildirimi aranır
      const notification = await findNotificationByTitle(page, 'Pozisyon Kapatıldı');
      if (await notification.isVisible()) {
        await expect(notification).toBeVisible();
        
        // Bildirim içeriğini kontrol et
        const message = await notification.locator('p').textContent();
        expect(message).toContain('satıldı');
        expect(message).toContain('Solo-robot');
        expect(message).toMatch(/\d+,\d+ TL/); // Fiyat formatı
        expect(message).toMatch(/₺[\d,]+/); // Toplam tutar formatı
      }
    }
  });

  test('Bro-robot BUY sinyali bildirimi oluşturuyor', async ({ page }) => {
    await page.goto('/management');
    await waitForPageLoad(page);
    
    // Bro-robot BUY test butonunu bul
    const broBuyTestButton = page.locator('button:has-text("Bro BUY Test"), button:has-text("Bro Robot BUY")');
    
    if (await broBuyTestButton.isVisible()) {
      await broBuyTestButton.click();
      
      // Toast mesajını bekle
      await expectToast(page, 'başarılı', 'success');
      
      // Bildirimlere git ve yeni bildirimi kontrol et
      await goToNotifications(page);
      await disableDebugMode(page);
      
      // "Yeni Pozisyon Açıldı" bildirimi aranır
      const notification = await findNotificationByTitle(page, 'Yeni Pozisyon Açıldı');
      if (await notification.isVisible()) {
        await expect(notification).toBeVisible();
        
        // Bildirim içeriğini kontrol et
        const message = await notification.locator('p').textContent();
        expect(message).toContain('alındı');
        expect(message).toContain('Bro'); // Bro-robot veya robot ismi
        expect(message).toMatch(/\d+,\d+ TL/); // Fiyat formatı
        expect(message).toMatch(/₺[\d,]+/); // Toplam tutar formatı
      }
    }
  });

  test('Bro-robot SELL sinyali bildirimi oluşturuyor', async ({ page }) => {
    await page.goto('/management');
    await waitForPageLoad(page);
    
    // Bro-robot SELL test butonunu bul
    const broSellTestButton = page.locator('button:has-text("Bro SELL Test"), button:has-text("Bro Robot SELL")');
    
    if (await broSellTestButton.isVisible()) {
      await broSellTestButton.click();
      
      // Toast mesajını bekle
      await expectToast(page, 'başarılı', 'success');
      
      // Bildirimlere git ve yeni bildirimi kontrol et
      await goToNotifications(page);
      await disableDebugMode(page);
      
      // "Pozisyon Kapatıldı" bildirimi aranır
      const notification = await findNotificationByTitle(page, 'Pozisyon Kapatıldı');
      if (await notification.isVisible()) {
        await expect(notification).toBeVisible();
        
        // Bildirim içeriğini kontrol et
        const message = await notification.locator('p').textContent();
        expect(message).toContain('satıldı');
        expect(message).toContain('Bro'); // Bro-robot veya robot ismi
        expect(message).toMatch(/\d+,\d+ TL/); // Fiyat formatı
        expect(message).toMatch(/₺[\d,]+/); // Toplam tutar formatı
      }
    }
  });

  test('Robot bildirim formatları doğru', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Trade bildirimleri aranır
    const tradeNotifications = page.locator('[data-testid="notification-item"]:has-text("alındı"), [data-testid="notification-item"]:has-text("satıldı")');
    
    if (await tradeNotifications.count() > 0) {
      const firstTrade = tradeNotifications.first();
      const message = await firstTrade.locator('p').textContent();
      
      if (message) {
        // Gerekli format kontrolü
        expect(message).toMatch(/\w+ hissesinden/); // Sembol
        expect(message).toMatch(/\d+,\d+ TL fiyatla/); // Fiyat
        expect(message).toMatch(/\d+ adet/); // Miktar
        expect(message).toMatch(/₺[\d,]+/); // Toplam tutar
        expect(message).toMatch(/(Solo-robot|Bro.*Robot)/); // Robot tipi
        expect(message).toMatch(/(açıldı|kapandı)/); // İşlem durumu
      }
    }
  });

  test('Robot bildirim metadata\'sı doğru', async ({ page }) => {
    await goToNotifications(page);
    
    // Debug bilgilerinden metadata kontrol et
    const debugInfo = page.locator('[data-testid="debug-info"]');
    if (await debugInfo.isVisible()) {
      const debugText = await debugInfo.textContent();
      
      if (debugText && debugText.includes('metadata')) {
        // Metadata alanlarını kontrol et
        expect(debugText).toContain('trade_id');
        expect(debugText).toContain('symbol');
        expect(debugText).toContain('order_side');
        expect(debugText).toContain('quantity');
        expect(debugText).toContain('price');
        expect(debugText).toContain('total_amount');
        expect(debugText).toContain('source');
      }
    }
  });

  test('Robot bildirim tipleri doğru ikonlarla görüntüleniyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Trade opened bildirimleri
    const openedNotifications = page.locator('[data-testid="notification-item"]:has-text("Pozisyon Açıldı")');
    if (await openedNotifications.count() > 0) {
      const icon = openedNotifications.first().locator('img');
      await expect(icon).toBeVisible();
    }
    
    // Trade closed bildirimleri
    const closedNotifications = page.locator('[data-testid="notification-item"]:has-text("Pozisyon Kapatıldı")');
    if (await closedNotifications.count() > 0) {
      const icon = closedNotifications.first().locator('img');
      await expect(icon).toBeVisible();
    }
  });

  test('Robot bildirim aksiyon linkleri çalışıyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // İlk trade bildirimi
    const tradeNotification = page.locator('[data-testid="notification-item"]:has-text("alındı"), [data-testid="notification-item"]:has-text("satıldı")').first();
    
    if (await tradeNotification.isVisible()) {
      // "İşlemleri Görüntüle" linkini bul
      const actionLink = tradeNotification.locator('a:has-text("İşlemleri Görüntüle")');
      
      if (await actionLink.isVisible()) {
        await actionLink.click();
        
        // Trades sayfasına yönlendirildiğini kontrol et
        await expect(page).toHaveURL('/trades');
        await waitForPageLoad(page);
      }
    }
  });

  test('Çoklu robot bildirimleri sıralı görüntüleniyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Tüm trade bildirimlerini al
    const tradeNotifications = page.locator('[data-testid="notification-item"]:has-text("alındı"), [data-testid="notification-item"]:has-text("satıldı")');
    const count = await tradeNotifications.count();
    
    if (count > 1) {
      // İlk iki bildirimin tarihlerini karşılaştır
      const firstDate = await tradeNotifications.first().locator('[data-testid="notification-date"]').textContent();
      const secondDate = await tradeNotifications.nth(1).locator('[data-testid="notification-date"]').textContent();
      
      // Tarih formatı kontrolü (en yeni önce olmalı)
      expect(firstDate).toBeTruthy();
      expect(secondDate).toBeTruthy();
    }
  });

  test('Robot bildirim filtreleme', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Filtre alanı varsa test et
    const filterSelect = page.locator('select[name*="type"], select[aria-label*="tip"]');
    if (await filterSelect.isVisible()) {
      // Trade bildirimleri filtresi
      await filterSelect.selectOption('trade_opened');
      await waitForPageLoad(page);
      
      // Sadece "Pozisyon Açıldı" bildirimlerinin görünür olduğunu kontrol et
      const visibleNotifications = page.locator('[data-testid="notification-item"]:visible');
      const count = await visibleNotifications.count();
      
      for (let i = 0; i < count; i++) {
        const notification = visibleNotifications.nth(i);
        const text = await notification.textContent();
        expect(text).toContain('Pozisyon Açıldı');
      }
    }
  });

  test('Robot bildirim real-time güncellemeleri', async ({ page }) => {
    await goToNotifications(page);
    
    // İlk bildirim sayısını al
    const initialCount = await page.locator('[data-testid="notification-item"]').count();
    
    // Yeni bir robot sinyali simüle et
    await simulateRobotSignal(page, 'BUY');
    
    // Bildirimlere geri dön ve yenile
    await goToNotifications(page);
    await page.reload();
    await waitForPageLoad(page);
    
    // Yeni bildirim sayısını kontrol et
    const newCount = await page.locator('[data-testid="notification-item"]').count();
    expect(newCount).toBeGreaterThanOrEqual(initialCount);
  });
});
