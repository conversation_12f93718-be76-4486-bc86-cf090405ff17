import React, { useState } from 'react';
import {
  VStack,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  HStack,
  Box,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Button,

  Flex,

  Badge,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription
} from '@chakra-ui/react';
import { useEnhancedStatistics } from '../../hooks/useEnhancedStatistics';
import InteractivePieChart from '../../components/statistics/charts/InteractivePieChart';
import SymbolPerformanceBarChart from '../../components/statistics/charts/SymbolPerformanceBarChart';
import SymbolTradingFrequencyChart from '../../components/statistics/charts/SymbolTradingFrequencyChart';
import SymbolRiskReturnScatterPlot from '../../components/statistics/charts/SymbolRiskReturnScatterPlot';
import SymbolPerformanceHeatmap from '../../components/statistics/charts/SymbolPerformanceHeatmap';
import SymbolCorrelationMatrix from '../../components/statistics/charts/SymbolCorrelationMatrix';
import SymbolAnalysisFilters from '../../components/statistics/filters/SymbolAnalysisFilters';
import SymbolDetailModal from '../../components/statistics/modals/SymbolDetailModal';
import SymbolComparisonModal from '../../components/statistics/comparison/SymbolComparisonModal';
import SymbolAlertsAndInsights from '../../components/statistics/insights/SymbolAlertsAndInsights';
import { useSymbolFiltering } from '../../hooks/useSymbolFiltering';
import { useDisclosure } from '@chakra-ui/react';

const SymbolAnalysisPage: React.FC = () => {
  // ALL HOOKS MUST BE CALLED FIRST - BEFORE ANY CONDITIONAL LOGIC OR EARLY RETURNS
  const { stats, loading, error } = useEnhancedStatistics();
  const [selectedSymbols, setSelectedSymbols] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState(0);
  const [selectedSymbolForDetail, setSelectedSymbolForDetail] = useState<string | null>(null);

  // Modal controls
  const { isOpen: isDetailModalOpen, onOpen: openDetailModal, onClose: closeDetailModal } = useDisclosure();
  const { isOpen: isComparisonModalOpen, onOpen: openComparisonModal, onClose: closeComparisonModal } = useDisclosure();

  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const cardBgColor = useColorModeValue('gray.50', 'gray.700');
  const selectedSymbolBgColor = useColorModeValue('blue.50', 'blue.900');

  // Prepare data for hooks (must be done before calling useSymbolFiltering)
  const hasEnhancedData = stats?.enhancedSymbolAnalysis && stats.enhancedSymbolAnalysis.symbolMetrics.length > 0;
  const symbolMetrics = hasEnhancedData ? stats.enhancedSymbolAnalysis.symbolMetrics : [];

  // Initialize filtering system (must be called after symbolMetrics is defined but before any returns)
  const {
    filters,
    filteredData: filteredSymbolMetrics,
    updateFilters,
    resetFilters,
    filterSummary,
    applyQuickFilter
  } = useSymbolFiltering(symbolMetrics);

  // NOW we can do conditional rendering and early returns
  if (loading) {
    return (
      <Box p={8} textAlign="center">
        <Text>Yükleniyor...</Text>
      </Box>
    );
  }

  if (error || !stats) {
    return (
      <Box p={8} textAlign="center">
        <Text color="red.500">Veri yüklenirken hata oluştu</Text>
      </Box>
    );
  }

  const formatCurrency = (value: number) => `₺${value.toFixed(2)}`;

  // Enhanced symbol analysis data (now safe to access stats)
  const symbolTimePerformance = hasEnhancedData ? stats.enhancedSymbolAnalysis.symbolTimePerformance : [];
  const symbolTradingFrequency = hasEnhancedData ? stats.enhancedSymbolAnalysis.symbolTradingFrequency : [];
  const correlationMatrix = hasEnhancedData ? stats.enhancedSymbolAnalysis.correlationMatrix : [];
  const alerts = hasEnhancedData ? stats.enhancedSymbolAnalysis.alerts : [];
  const insights = hasEnhancedData ? stats.enhancedSymbolAnalysis.insights : [];
  const summary = hasEnhancedData ? stats.enhancedSymbolAnalysis.summary : null;

  // Fallback to basic data if enhanced data is not available
  const symbolPerformanceData = Object.entries(stats.pnlBySymbol || {})
    .map(([symbol, data]) => {
      const pnl = typeof data === 'object' && data !== null ? (data as any).pnl || 0 : (data as number) || 0;
      return {
        name: symbol,
        value: pnl,
        color: pnl > 0 ? '#48BB78' : '#F56565'
      };
    })
    .sort((a, b) => b.value - a.value)
    .slice(0, 10);

  const symbolDistributionData = Object.entries(stats.pnlBySymbol || {})
    .map(([symbol, data]) => {
      const pnl = typeof data === 'object' && data !== null ? (data as any).pnl || 0 : (data as number) || 0;
      return {
        name: symbol,
        value: Math.abs(pnl),
        percentage: 0
      };
    });

  const totalVolume = symbolDistributionData.reduce((sum, item) => sum + item.value, 0);
  symbolDistributionData.forEach(item => {
    item.percentage = totalVolume > 0 ? (item.value / totalVolume) * 100 : 0;
  });

  const topPerformingSymbols = symbolPerformanceData
    .filter(item => item.value > 0)
    .slice(0, 5);

  // Symbol statistics (enhanced or basic, using filtered data when available)
  const symbolStats = hasEnhancedData && filteredSymbolMetrics.length > 0 ? {
    totalSymbols: filteredSymbolMetrics.length,
    profitableSymbols: filteredSymbolMetrics.filter(s => s.totalPnl > 0).length,
    bestSymbol: filteredSymbolMetrics.length > 0 ? filteredSymbolMetrics[0].symbol : 'N/A',
    worstSymbol: filteredSymbolMetrics.length > 0 ?
      filteredSymbolMetrics.sort((a, b) => a.totalPnl - b.totalPnl)[0].symbol : 'N/A'
  } : summary ? {
    totalSymbols: summary.totalSymbols,
    profitableSymbols: summary.profitableSymbols,
    bestSymbol: summary.bestPerformingSymbol,
    worstSymbol: summary.worstPerformingSymbol
  } : {
    totalSymbols: Object.keys(stats.pnlBySymbol || {}).length,
    profitableSymbols: Object.values(stats.pnlBySymbol || {}).filter((pnl: any) => pnl > 0).length,
    bestSymbol: symbolPerformanceData[0]?.name || 'N/A',
    worstSymbol: symbolPerformanceData[symbolPerformanceData.length - 1]?.name || 'N/A'
  };

  // Handle symbol selection
  const handleSymbolSelect = (symbols: string[]) => {
    setSelectedSymbols(symbols);
  };

  const handleSymbolClick = (symbol: string) => {
    // Open detail modal for the symbol
    setSelectedSymbolForDetail(symbol);
    openDetailModal();
  };

  const handleSymbolToggle = (symbol: string) => {
    // Toggle symbol selection for comparison
    setSelectedSymbols(prev =>
      prev.includes(symbol)
        ? prev.filter(s => s !== symbol)
        : [...prev, symbol]
    );
  };

  // Get data for selected symbol
  const selectedSymbolData = selectedSymbolForDetail
    ? symbolMetrics.find(s => s.symbol === selectedSymbolForDetail)
    : undefined;

  const selectedSymbolTimePerformance = selectedSymbolForDetail
    ? symbolTimePerformance.find(s => s.symbol === selectedSymbolForDetail)
    : undefined;

  const selectedSymbolTradingFrequency = selectedSymbolForDetail
    ? symbolTradingFrequency.find(s => s.symbol === selectedSymbolForDetail)
    : undefined;

  // Handle watchlist and export actions
  const handleAddToWatchlist = (symbol: string) => {
    // TODO: Implement watchlist functionality
    console.log(`Adding ${symbol} to watchlist`);
  };

  const handleExportData = (symbol: string) => {
    // TODO: Implement export functionality
    console.log(`Exporting data for ${symbol}`);
  };

  // Handle comparison actions
  const handleOpenComparison = () => {
    if (selectedSymbols.length > 0) {
      openComparisonModal();
    }
  };

  const handleRemoveFromComparison = (symbol: string) => {
    setSelectedSymbols(prev => prev.filter(s => s !== symbol));
  };

  const handleExportComparison = (symbols: string[]) => {
    // TODO: Implement comparison export functionality
    console.log(`Exporting comparison for symbols: ${symbols.join(', ')}`);
  };

  return (
    <VStack spacing={6} align="stretch">
      {/* Header */}
      <VStack spacing={4} align="stretch">
        <VStack align="start" spacing={1}>
          <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold">Gelişmiş Sembol Analizi</Text>
          <Text fontSize="sm" color="gray.500">
            Detaylı sembol performansı, risk analizi ve trading frekansı
          </Text>
        </VStack>

        {selectedSymbols.length > 0 && (
          <Flex
            direction={{ base: "column", md: "row" }}
            gap={3}
            align={{ base: "stretch", md: "center" }}
            justify={{ base: "start", md: "space-between" }}
          >
            <Badge colorScheme="blue" variant="subtle" fontSize="sm" textAlign="center">
              {selectedSymbols.length} sembol seçili
            </Badge>
            <HStack spacing={2} justify={{ base: "center", md: "flex-end" }}>
              <Button
                size={{ base: "sm", md: "sm" }}
                colorScheme="blue"
                onClick={handleOpenComparison}
                isDisabled={selectedSymbols.length < 2}
                fontSize={{ base: "xs", md: "sm" }}
              >
                Karşılaştır ({selectedSymbols.length})
              </Button>
              <Button
                size={{ base: "sm", md: "sm" }}
                variant="outline"
                onClick={() => setSelectedSymbols([])}
                fontSize={{ base: "xs", md: "sm" }}
              >
                Seçimi Temizle
              </Button>
            </HStack>
          </Flex>
        )}
      </VStack>

      {/* Enhanced Data Warning */}
      {!hasEnhancedData && (
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <Box>
            <AlertTitle>Temel Analiz Modu</AlertTitle>
            <AlertDescription>
              Gelişmiş sembol analizi verileri hesaplanıyor. Şu anda temel analiz görünümü gösteriliyor.
            </AlertDescription>
          </Box>
        </Alert>
      )}

      {/* Summary Statistics Cards */}
      <SimpleGrid columns={{ base: 1, sm: 2, lg: 4 }} spacing={{ base: 4, md: 6 }}>
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="start" spacing={2}>
              <HStack>
                <Text fontSize="2xl">📊</Text>
                <Text fontSize="sm" color="gray.500" fontWeight="medium">
                  Toplam Sembol
                </Text>
              </HStack>
              <Text fontSize="2xl" fontWeight="bold" color="blue.500">
                {symbolStats.totalSymbols}
              </Text>
              {summary && (
                <Text fontSize="xs" color="blue.400">
                  {summary.totalTrades} toplam işlem
                </Text>
              )}
            </VStack>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="start" spacing={2}>
              <HStack>
                <Text fontSize="2xl">✅</Text>
                <Text fontSize="sm" color="gray.500" fontWeight="medium">
                  Karlı Sembol
                </Text>
              </HStack>
              <Text fontSize="2xl" fontWeight="bold" color="green.500">
                {symbolStats.profitableSymbols}
              </Text>
              <Text fontSize="xs" color="green.400">
                {symbolStats.totalSymbols > 0 ?
                  `${((symbolStats.profitableSymbols / symbolStats.totalSymbols) * 100).toFixed(1)}% başarı`
                  : '0% başarı'
                }
              </Text>
            </VStack>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="start" spacing={2}>
              <HStack>
                <Text fontSize="2xl">🏆</Text>
                <Text fontSize="sm" color="gray.500" fontWeight="medium">
                  En İyi Sembol
                </Text>
              </HStack>
              <Text fontSize="lg" fontWeight="bold" color="green.500">
                {symbolStats.bestSymbol}
              </Text>
              <Text fontSize="xs" color="green.400">
                {summary ? formatCurrency(summary.totalPnl) :
                 (symbolPerformanceData[0] ? formatCurrency(symbolPerformanceData[0].value) : '₺0.00')}
              </Text>
            </VStack>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="start" spacing={2}>
              <HStack>
                <Text fontSize="2xl">📈</Text>
                <Text fontSize="sm" color="gray.500" fontWeight="medium">
                  En Yüksek ROI
                </Text>
              </HStack>
              <Text fontSize="lg" fontWeight="bold" color="purple.500">
                {summary ? summary.bestRiskAdjustedSymbol : symbolStats.worstSymbol}
              </Text>
              <Text fontSize="xs" color="purple.400">
                {summary ? `${summary.overallROI.toFixed(2)}% ROI` : 'Hesaplanıyor...'}
              </Text>
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Alerts Summary Card */}
      {hasEnhancedData && (alerts.length > 0 || insights.length > 0) && (
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <HStack justify="space-between" align="center">
              <HStack>
                <Text fontSize="2xl">🔔</Text>
                <VStack align="start" spacing={1}>
                  <Text fontSize="md" fontWeight="bold">Akıllı Analiz Özeti</Text>
                  <HStack spacing={3}>
                    {alerts.length > 0 && (
                      <Badge colorScheme="orange" variant="solid">
                        {alerts.length} Uyarı
                      </Badge>
                    )}
                    {insights.length > 0 && (
                      <Badge colorScheme="green" variant="solid">
                        {insights.length} Öngörü
                      </Badge>
                    )}
                  </HStack>
                </VStack>
              </HStack>
              <Button
                size="sm"
                colorScheme="blue"
                variant="outline"
                onClick={() => setActiveTab(5)} // Navigate to Alerts & Insights tab
              >
                Detayları Görüntüle
              </Button>
            </HStack>
          </CardBody>
        </Card>
      )}

      {/* Filters Section */}
      {hasEnhancedData && (
        <SymbolAnalysisFilters
          data={symbolMetrics}
          filters={filters}
          onFiltersChange={updateFilters}
          onReset={resetFilters}
          compact={true}
        />
      )}

      {/* Filter Summary */}
      {hasEnhancedData && filterSummary.filtered !== filterSummary.total && (
        <Alert status="info" borderRadius="md">
          <AlertIcon />
          <Box>
            <AlertTitle>Filtre Uygulandı</AlertTitle>
            <AlertDescription>
              {filterSummary.total} sembolden {filterSummary.filtered} tanesi gösteriliyor
              ({filterSummary.percentage.toFixed(1)}%). {filterSummary.hidden} sembol gizlendi.
            </AlertDescription>
          </Box>
        </Alert>
      )}

      {/* Quick Filter Buttons */}
      {hasEnhancedData && (
        <VStack spacing={3} align="stretch">
          <Text fontSize="sm" fontWeight="medium" color="gray.600" display={{ base: "block", md: "none" }}>
            Hızlı Filtreler:
          </Text>
          <Flex
            direction={{ base: "column", md: "row" }}
            wrap="wrap"
            gap={2}
            align={{ base: "stretch", md: "center" }}
            justify={{ base: "start", md: "space-between" }}
          >
            <HStack wrap="wrap" spacing={2} justify={{ base: "center", md: "start" }}>
              <Text fontSize="sm" fontWeight="medium" color="gray.600" display={{ base: "none", md: "block" }}>
                Hızlı Filtreler:
              </Text>
              <Button
                size={{ base: "xs", md: "sm" }}
                variant="outline"
                onClick={() => applyQuickFilter('profitable')}
                fontSize={{ base: "xs", md: "sm" }}
              >
                Karlı Semboller
              </Button>
              <Button
                size={{ base: "xs", md: "sm" }}
                variant="outline"
                onClick={() => applyQuickFilter('highVolume')}
                fontSize={{ base: "xs", md: "sm" }}
              >
                Yüksek Hacim
              </Button>
              <Button
                size={{ base: "xs", md: "sm" }}
                variant="outline"
                onClick={() => applyQuickFilter('lowRisk')}
                fontSize={{ base: "xs", md: "sm" }}
              >
                Düşük Risk
              </Button>
              <Button
                size={{ base: "xs", md: "sm" }}
                variant="outline"
                onClick={() => applyQuickFilter('highPerformance')}
                fontSize={{ base: "xs", md: "sm" }}
              >
                Yüksek Performans
              </Button>
              <Button
                size={{ base: "xs", md: "sm" }}
                variant="outline"
                onClick={() => applyQuickFilter('recent')}
                fontSize={{ base: "xs", md: "sm" }}
              >
                Son 30 Gün
              </Button>
            </HStack>

            {selectedSymbols.length >= 2 && (
              <Button
                size={{ base: "sm", md: "sm" }}
                colorScheme="blue"
                variant="solid"
                onClick={handleOpenComparison}
                fontSize={{ base: "xs", md: "sm" }}
                w={{ base: "full", md: "auto" }}
              >
                {selectedSymbols.length} Sembol Karşılaştır
              </Button>
            )}
          </Flex>
        </VStack>
      )}

      {/* Enhanced Analysis Tabs */}
      <Tabs index={activeTab} onChange={setActiveTab} variant="enclosed" colorScheme="blue">
        <TabList>
          <Tab>Performans Analizi</Tab>
          <Tab>Risk-Getiri Analizi</Tab>
          <Tab>Trading Frekansı</Tab>
          <Tab>Zaman Analizi</Tab>
          <Tab>Korelasyon Analizi</Tab>
          <Tab>Uyarılar & Öngörüler</Tab>
          <Tab>Geleneksel Görünüm</Tab>
        </TabList>

        <TabPanels>
          {/* Performance Analysis Tab */}
          <TabPanel px={0}>
            {hasEnhancedData ? (
              <VStack spacing={6} align="stretch">
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <VStack align="stretch" spacing={4}>
                      <Text fontSize="lg" fontWeight="bold">Sembol Performans Karşılaştırması</Text>
                      <SymbolPerformanceBarChart
                        data={filteredSymbolMetrics}
                        height={400}
                        onSymbolClick={handleSymbolClick}
                        onSymbolSelect={handleSymbolSelect}
                        showComparison={true}
                        maxSymbols={20}
                      />
                    </VStack>
                  </CardBody>
                </Card>
              </VStack>
            ) : (
              <Alert status="warning" borderRadius="md">
                <AlertIcon />
                <AlertDescription>
                  Gelişmiş performans analizi verileri henüz hazır değil. Lütfen birkaç dakika sonra tekrar deneyin.
                </AlertDescription>
              </Alert>
            )}
          </TabPanel>

          {/* Risk-Return Analysis Tab */}
          <TabPanel px={0}>
            {hasEnhancedData ? (
              <VStack spacing={6} align="stretch">
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <VStack align="stretch" spacing={4}>
                      <Text fontSize="lg" fontWeight="bold">Risk-Getiri Scatter Plot</Text>
                      <SymbolRiskReturnScatterPlot
                        data={filteredSymbolMetrics}
                        height={500}
                        onSymbolClick={handleSymbolClick}
                        selectedSymbols={selectedSymbols}
                        showBenchmark={true}
                        benchmarkReturn={summary?.overallROI || 5}
                        benchmarkRisk={15}
                      />
                    </VStack>
                  </CardBody>
                </Card>
              </VStack>
            ) : (
              <Alert status="warning" borderRadius="md">
                <AlertIcon />
                <AlertDescription>
                  Risk-getiri analizi verileri henüz hazır değil. Lütfen birkaç dakika sonra tekrar deneyin.
                </AlertDescription>
              </Alert>
            )}
          </TabPanel>

          {/* Trading Frequency Tab */}
          <TabPanel px={0}>
            {hasEnhancedData ? (
              <VStack spacing={6} align="stretch">
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <VStack align="stretch" spacing={4}>
                      <Text fontSize="lg" fontWeight="bold">Trading Frekans Analizi</Text>
                      <SymbolTradingFrequencyChart
                        data={symbolTradingFrequency}
                        height={400}
                        selectedSymbols={selectedSymbols}
                        onSymbolToggle={handleSymbolToggle}
                        maxSymbols={10}
                      />
                    </VStack>
                  </CardBody>
                </Card>
              </VStack>
            ) : (
              <Alert status="warning" borderRadius="md">
                <AlertIcon />
                <AlertDescription>
                  Trading frekans analizi verileri henüz hazır değil. Lütfen birkaç dakika sonra tekrar deneyin.
                </AlertDescription>
              </Alert>
            )}
          </TabPanel>

          {/* Time Analysis Tab */}
          <TabPanel px={0}>
            {hasEnhancedData ? (
              <VStack spacing={6} align="stretch">
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <VStack align="stretch" spacing={4}>
                      <Text fontSize="lg" fontWeight="bold">Zaman Bazlı Performans Analizi</Text>
                      <SymbolPerformanceHeatmap
                        data={symbolTimePerformance}
                        height={500}
                        selectedSymbols={selectedSymbols}
                        onSymbolClick={handleSymbolClick}
                        maxSymbols={15}
                      />
                    </VStack>
                  </CardBody>
                </Card>
              </VStack>
            ) : (
              <Alert status="warning" borderRadius="md">
                <AlertIcon />
                <AlertDescription>
                  Zaman analizi verileri henüz hazır değil. Lütfen birkaç dakika sonra tekrar deneyin.
                </AlertDescription>
              </Alert>
            )}
          </TabPanel>

          {/* Correlation Analysis Tab */}
          <TabPanel px={0}>
            {hasEnhancedData ? (
              <VStack spacing={6} align="stretch">
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <VStack align="stretch" spacing={4}>
                      <VStack align="start" spacing={2}>
                        <Text fontSize="lg" fontWeight="bold">Sembol Korelasyon Matrisi</Text>
                        <Text fontSize="sm" color="gray.500">
                          Semboller arasındaki korelasyon ilişkilerini analiz edin. Pozitif korelasyon (mavi) benzer hareket,
                          negatif korelasyon (kırmızı) zıt hareket anlamına gelir.
                        </Text>
                      </VStack>
                      <SymbolCorrelationMatrix
                        data={correlationMatrix}
                        height={600}
                        selectedSymbols={selectedSymbols}
                        onSymbolClick={handleSymbolClick}
                        maxSymbols={12}
                      />
                    </VStack>
                  </CardBody>
                </Card>
              </VStack>
            ) : (
              <Alert status="warning" borderRadius="md">
                <AlertIcon />
                <AlertDescription>
                  Korelasyon analizi verileri henüz hazır değil. Lütfen birkaç dakika sonra tekrar deneyin.
                </AlertDescription>
              </Alert>
            )}
          </TabPanel>

          {/* Alerts and Insights Tab */}
          <TabPanel px={0}>
            {hasEnhancedData ? (
              <VStack spacing={6} align="stretch">
                <SymbolAlertsAndInsights
                  alerts={alerts}
                  insights={insights}
                  onSymbolClick={handleSymbolClick}
                  compact={false}
                />
              </VStack>
            ) : (
              <Alert status="warning" borderRadius="md">
                <AlertIcon />
                <AlertDescription>
                  Uyarı ve öngörü verileri henüz hazır değil. Lütfen birkaç dakika sonra tekrar deneyin.
                </AlertDescription>
              </Alert>
            )}
          </TabPanel>

          {/* Traditional View Tab */}
          <TabPanel px={0}>
            <VStack spacing={6} align="stretch">
              {/* Traditional Charts Grid */}
              <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                {/* Symbol Performance Chart */}
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <VStack align="stretch" spacing={4}>
                      <Text fontSize="lg" fontWeight="bold">Sembol Performansı (Top 10)</Text>
                      <Box h="350px">
                        <InteractivePieChart
                          data={symbolPerformanceData}
                          title=""
                          height={350}
                          formatValue={formatCurrency}
                          showPercentages={false}
                          showValues={true}
                        />
                      </Box>
                    </VStack>
                  </CardBody>
                </Card>

                {/* Symbol Distribution Chart */}
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <VStack align="stretch" spacing={4}>
                      <Text fontSize="lg" fontWeight="bold">Sembol Dağılımı</Text>
                      <Box h="350px">
                        <InteractivePieChart
                          data={symbolDistributionData.slice(0, 8)}
                          title=""
                          height={350}
                          formatValue={(value) => `${value.toFixed(1)}%`}
                          showPercentages={true}
                          viewMode="donut"
                        />
                      </Box>
                    </VStack>
                  </CardBody>
                </Card>
              </SimpleGrid>

              {/* Top Performing Symbols Table */}
              <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                <CardBody>
                  <VStack align="stretch" spacing={4}>
                    <Text fontSize="lg" fontWeight="bold">En İyi Performans Gösteren Semboller</Text>
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                      {topPerformingSymbols.map((symbol, index) => (
                        <Box
                          key={symbol.name}
                          p={4}
                          borderRadius="12px"
                          bg={cardBgColor}
                          cursor="pointer"
                          onClick={() => handleSymbolClick(symbol.name)}
                          _hover={{ transform: 'translateY(-2px)', boxShadow: 'md' }}
                          transition="all 0.2s"
                          border={selectedSymbols.includes(symbol.name) ? '2px solid' : '1px solid transparent'}
                          borderColor={selectedSymbols.includes(symbol.name) ? 'blue.500' : 'transparent'}
                        >
                          <HStack justify="space-between">
                            <VStack align="start" spacing={1}>
                              <Text fontWeight="bold" fontSize="lg">{symbol.name}</Text>
                              <Text fontSize="sm" color="gray.500">#{index + 1} Sırada</Text>
                            </VStack>
                            <VStack align="end" spacing={1}>
                              <Text fontWeight="bold" color="green.500" fontSize="lg">
                                {formatCurrency(symbol.value)}
                              </Text>
                              <Text fontSize="xs" color="green.400">Kar</Text>
                            </VStack>
                          </HStack>
                        </Box>
                      ))}
                    </SimpleGrid>
                  </VStack>
                </CardBody>
              </Card>
            </VStack>
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Selected Symbols Summary */}
      {selectedSymbols.length > 0 && (
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="stretch" spacing={4}>
              <Text fontSize="lg" fontWeight="bold">Seçili Semboller Özeti</Text>
              <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
                {selectedSymbols.map(symbol => {
                  const symbolData = hasEnhancedData
                    ? symbolMetrics.find(s => s.symbol === symbol)
                    : null;

                  return (
                    <Box key={symbol} p={3} borderRadius="md" bg={selectedSymbolBgColor}>
                      <VStack align="start" spacing={1}>
                        <Text fontWeight="bold" color="blue.600">{symbol}</Text>
                        {symbolData && (
                          <>
                            <Text fontSize="sm">ROI: {symbolData.roi.toFixed(2)}%</Text>
                            <Text fontSize="sm">Kazanma: {symbolData.winRate.toFixed(1)}%</Text>
                            <Text fontSize="sm">İşlem: {symbolData.totalTrades}</Text>
                          </>
                        )}
                      </VStack>
                    </Box>
                  );
                })}
              </SimpleGrid>
            </VStack>
          </CardBody>
        </Card>
      )}

      {/* Symbol Detail Modal */}
      <SymbolDetailModal
        isOpen={isDetailModalOpen}
        onClose={closeDetailModal}
        symbol={selectedSymbolForDetail || ''}
        symbolData={selectedSymbolData}
        timePerformance={selectedSymbolTimePerformance}
        tradingFrequency={selectedSymbolTradingFrequency}
        onAddToWatchlist={handleAddToWatchlist}
        onExportData={handleExportData}
      />

      {/* Symbol Comparison Modal */}
      <SymbolComparisonModal
        isOpen={isComparisonModalOpen}
        onClose={closeComparisonModal}
        symbols={selectedSymbols}
        symbolsData={symbolMetrics}
        onRemoveSymbol={handleRemoveFromComparison}
        onExportComparison={handleExportComparison}
      />
    </VStack>
  );
};

export default SymbolAnalysisPage;
