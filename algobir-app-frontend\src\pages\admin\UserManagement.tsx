// UserManagement.tsx - Admin panelinde kullanıcı yönetimi için bileşen
import { useState, useEffect } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Switch,
  Badge,
  Text,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Flex,
  Heading,
  Input,
  InputGroup,
  InputLeftElement,
  Icon,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  FormHelperText,
  InputRightElement,
  NumberInput,
  NumberInputField,
  InputGroup as InputNumberGroup,
  useDisclosure,
  IconButton,
  VStack,
  useColorModeValue
} from '@chakra-ui/react';
import { FiSearch, FiEdit2, FiEye, FiEyeOff, FiUsers, FiUser, FiSettings } from 'react-icons/fi';
import { supabase } from '../../supabaseClient';

// Custom Components
import Card from '../../components/card/Card';

// Kullanıcı ve kullanıcı ayarları tipleri
type UserWithEmail = {
  id?: string;
  user_id?: string; // RPC'den dönen kullanıcı ID alanı
  email: string;
  webhook_id?: string | null;
  user_created_at?: string; // RPC'den dönen kullanıcı oluşturma tarihi
  // Diğer RPC'den dönen alanlar
  [key: string]: any;
};

type UserSettings = {
  id: string;
  created_at: string;
  is_superuser: boolean;
  display_name?: string | null;
  email?: string | null;
  webhook_id?: string | null;
  api_key?: string | null;
  token?: string | null;
  total_investment_amount?: number | null;
  custom_webhook_url?: string | null;
  is_active?: boolean | null;
  webhook_url?: string | null; // Webhok URL için alan ekliyoruz
};

// Kullanıcılar state'i için genişletilmiş tip
type UserWithWebhookUrl = UserSettings & { webhook_url: string };

// Webhook URL için ortam değişkeni
const WEBHOOK_BASE_URL = import.meta.env.VITE_WEBHOOK_BASE_URL || '';

const UserManagement = () => {
  const [users, setUsers] = useState<UserWithWebhookUrl[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserWithWebhookUrl[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updatingUsers, setUpdatingUsers] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const toast = useToast();
  
  // Kullanıcı detay modalı için state'ler
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedUser, setSelectedUser] = useState<UserWithWebhookUrl | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showToken, setShowToken] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  
  // Form değerleri için state'ler
  const [displayName, setDisplayName] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [token, setToken] = useState('');
  const [totalInvestmentAmount, setTotalInvestmentAmount] = useState('');
  const [customWebhookUrl, setCustomWebhookUrl] = useState('');
  const [isActive, setIsActive] = useState(true);

  // Theme colors
  const textColor = useColorModeValue('navy.700', 'white');
  const textColorSecondary = useColorModeValue('secondaryGray.600', 'secondaryGray.400');
  const brandColor = useColorModeValue('brand.500', 'brand.400');
  const bgCard = useColorModeValue('white', 'navy.700');
  const borderColor = useColorModeValue('secondaryGray.200', 'whiteAlpha.100');
  const headerTextColor = useColorModeValue('secondaryGray.600', 'white');
  const tableBorderColor = useColorModeValue('secondaryGray.200', 'whiteAlpha.100');
  const tableHeaderBg = useColorModeValue('secondaryGray.100', 'navy.800');
  const inputBg = useColorModeValue('secondaryGray.300', 'navy.900');
  const placeholderColor = useColorModeValue('secondaryGray.600', 'whiteAlpha.600');
  const hoverRowBg = useColorModeValue('secondaryGray.100', 'navy.800');
  const shadow = useColorModeValue('0px 18px 40px rgba(112, 144, 176, 0.12)', 'none');
  const iconBoxBg = useColorModeValue('secondaryGray.300', 'navy.700');
  const formControlBgColor = useColorModeValue('secondaryGray.100', 'navy.800');
  const modalFooterBgColor = useColorModeValue('secondaryGray.100', 'whiteAlpha.50');
  const buttonHoverBgColor = useColorModeValue('gray.100', 'whiteAlpha.100');

  // Kullanıcıları getiren fonksiyon
  const fetchUsers = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase.rpc('list_all_users_admin');
      if (error) {
        throw error;
      }
      
      console.log('RPC ile kullanıcılar getirildi:', data);
      // RPC dönüş formatını daha iyi incelemek için örnek kullanıcı
      if (data && data.length > 0) {
        console.log('Örnek RPC dönüş formatı (ilk kullanıcı):', JSON.stringify(data[0], null, 2));
        console.log('user_id mevcut mu?', data[0].user_id !== undefined);
        console.log('id mevcut mu?', data[0].id !== undefined);
      }
      
      // Kullanıcıları işle
      if (data) {
        // Her kullanıcının webhook_url'sini oluştur ve geçerli UUID'si olanları filtrele
        const processedUsers = data
          .map((user: UserWithEmail) => {
            // user.id yerine user.user_id kullanıyoruz
            const userId = user.user_id || user.id; // RPC'den user_id dönüyorsa kullan, yoksa id'yi dene
            
            // UUID doğrulama
            const isValidUUID = userId && typeof userId === 'string' && userId.length === 36;
            
            if (!isValidUUID) {
              console.warn(`Kullanıcı atlanıyor (Geçersiz/Eksik UUID). Email: ${user.email}, Alınan ID:`, userId);
              return null; // Bu kullanıcıyı filtrele
            }
            
            const webhookUrl = user.webhook_id
              ? `${WEBHOOK_BASE_URL}/${user.webhook_id}`
              : 'Webhook ID tanımlanmamış';
            
            // Kullanıcı verilerini işleme - güvenli dönüşüm
            const processedUser: UserWithWebhookUrl = {
              id: userId, // Frontend'in beklediği id alanını ayarla
              created_at: user.created_at || user.user_created_at || '', // RPC'den dönen doğru alan adını kullan
              is_superuser: Boolean(user.is_superuser),
              email: user.email || '',
              display_name: user.display_name || null,
              webhook_id: user.webhook_id || null,
              api_key: user.api_key || null,
              token: user.token || null,
              total_investment_amount: typeof user.total_investment_amount === 'number' ? user.total_investment_amount : null,
              custom_webhook_url: user.custom_webhook_url || null,
              is_active: user.is_active !== false, // undefined veya null ise true kabul et
              webhook_url: webhookUrl,
            };
            
            return processedUser;
          })
          .filter((user: UserWithWebhookUrl | null): user is UserWithWebhookUrl => user !== null); // null değerleri kaldır
        
        console.log('Geçerli UUIDye sahip kullanıcı sayısı:', processedUsers.length);
        setUsers(processedUsers);
        setFilteredUsers(processedUsers); // Filtrelenmiş kullanıcıları da başlangıçta güncelle
      }
    } catch (error) {
      console.error('Kullanıcılar getirilirken hata oluştu:', error);
      setError('Kullanıcılar getirilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.');
      toast({
        title: 'Hata',
        description: 'Kullanıcılar getirilirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // Kullanıcı araması yap
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredUsers(users);
    } else {
      const lowerCaseQuery = searchQuery.toLowerCase();
      const filtered = users.filter(user => {
        // ID kontrolü
        const idMatch = (user.id && typeof user.id === 'string') 
          ? user.id.toLowerCase().includes(lowerCaseQuery) 
          : false;
        
        // Email kontrolü
        const emailMatch = (user.email && typeof user.email === 'string') 
          ? user.email.toLowerCase().includes(lowerCaseQuery) 
          : false;
        
        // Display name kontrolü
        const displayNameMatch = (user.display_name && typeof user.display_name === 'string')
          ? user.display_name.toLowerCase().includes(lowerCaseQuery)
          : false;
        
        return idMatch || emailMatch || displayNameMatch;
      });
      setFilteredUsers(filtered);
    }
  }, [searchQuery, users]);

  // Kullanıcının süper kullanıcı durumunu güncelle
  const handleSuperUserChange = async (user: UserWithWebhookUrl, currentStatus: boolean) => {
    // Kullanıcı ID'sini ve e-postasını kontrol et
    const userId = user?.id;
    const userEmail = user?.email; // Loglama için
    
    console.log(`[DEBUG] handleSuperUserChange: User ID: ${userId}, Email: ${userEmail}`);

    if (!userId) {
      console.error(`[ADMIN UPDATE ERROR] Stopping update. Missing user ID for user ${userEmail || 'UNKNOWN'}`);
      toast({
        title: 'Hata',
        description: `Kullanıcı ID bulunamadı`,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return; // ID yoksa işlemi durdur
    }
    
    // Önce kullanıcıyı updatingUsers listesine ekle ve UI'ı güncelle
    setUpdatingUsers(prev => [...prev, userId]);
    
    try {
      // Gönderilecek veriyi oluştur - yeni süper kullanıcı durumu (currentStatus'un tersi)
      const settingsToUpdate = { is_superuser: !currentStatus };
      
      // Gönderilecek veriyi konsola yazdır
      console.log('[ADMIN SUPERUSER UPDATE] Data being sent to RPC:', settingsToUpdate);
      console.log(`[ADMIN UPDATE] Calling RPC 'update_user_settings_admin' with target_user_id: ${userId}`);
      
      const { data: rpcResult, error } = await supabase.rpc(
        'update_user_settings_admin',
        {
          target_user_id: userId,
          settings_data: settingsToUpdate // Doğru objeyi gönder
        }
      );
      
      if (error) {
        console.error(`Kullanıcı yetkileri güncellenirken RPC hatası:`, error);
        throw error;
      }
      
      console.log(`Kullanıcı yetkileri başarıyla güncellendi. RPC result:`, rpcResult);
      
      // RPC'den dönen mesajı kontrol et
      if (rpcResult && !rpcResult.startsWith('Başarılı')) {
        console.warn('[ADMIN SUPERUSER UPDATE INFO/WARN] RPC returned:', rpcResult);
        toast({
          title: 'Bilgi/Uyarı',
          description: rpcResult,
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
      } else {
        // Başarılı ise MUTLAKA listeyi yenile!
        await fetchUsers();
        
        toast({
          title: 'Yetki güncellendi',
          description: rpcResult || `Kullanıcı yetkileri başarıyla güncellendi.`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (err) {
      console.error('Yetki güncellenirken yakalanan hata:', err);
      toast({
        title: 'Hata',
        description: 'Yetki güncellenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setUpdatingUsers(prev => prev.filter(id => id !== userId));
    }
  };
  
  // Kullanıcı detaylarını göster
  const handleViewUserDetails = (user: UserWithWebhookUrl) => {
    setSelectedUser(user);
    setDisplayName(user.display_name || '');
    setApiKey(user.api_key || '');
    setToken(user.token || '');
    setTotalInvestmentAmount(user.total_investment_amount?.toString() || '');
    setCustomWebhookUrl(user.custom_webhook_url || '');
    setIsActive(user.is_active !== false); // undefined veya null ise true kabul et
    onOpen();
  };
  
  // Kullanıcı ayarlarını güncelle
  const handleUpdateUser = async () => {
    if (!selectedUser || !selectedUser.id) {
      console.error("[ADMIN UPDATE ERROR] Invalid or missing selectedUser.id:", selectedUser?.id);
      toast({
        title: 'Hata',
        description: 'Güncellenecek kullanıcı seçilmedi veya kullanıcı kimliği geçersiz.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      setError("Geçersiz kullanıcı kimliği.");
      return;
    }

    const userId = selectedUser.id;
    const userEmail = selectedUser.email;
    
    console.log(`[DEBUG] handleUpdateUser: User ID: ${userId}, Email: ${userEmail}`);
    
    try {
      console.log(`[ADMIN UPDATE] Starting update for user ${userEmail} with ID ${userId}`);
      setIsSubmitting(true);
      
      // Sadece user_settings tablosu için geçerli alanları içeren objeyi oluştur
      const settingsToUpdate: Record<string, any> = {};
      
      // user_settings tablosunda izin verilen alanları kontrol et ve mevcut değerlerden farklıysa ekle
      
      // total_investment_amount alanı
      if (totalInvestmentAmount.trim() !== '') {
        const numericAmount = parseFloat(totalInvestmentAmount);
        if (!isNaN(numericAmount) && numericAmount !== selectedUser.total_investment_amount) {
          settingsToUpdate.total_investment_amount = numericAmount;
        }
      }
      
      // custom_webhook_url alanı
      if (customWebhookUrl.trim() !== selectedUser.custom_webhook_url) {
        settingsToUpdate.custom_webhook_url = customWebhookUrl.trim() || null;
      }
      
      // is_active alanı - mevcut değerden farklıysa ekle
      if (isActive !== selectedUser.is_active) {
        settingsToUpdate.is_active = isActive;
      }
      
      // KRİTİK GÜVENLİK DÜZELTMESİ: 
      // API anahtarı ve token alanları admin paneli üzerinden güncellenmemeli!
      // Bu veriler sadece kullanıcının secure-save-api-key Edge Function'ı ile yönetilmelidir.
      // Supabase loglarında bu alanları gönderme işlemi uyarı oluşturuyor.
      // Bu alanlar RPC fonksiyonunda ignore edilse de, gönderilmemeleri gerekir.
      
      // REMOVED: api_key ve token alanları artık admin panelinden gönderilemez
      // Bu sayede Supabase loglarındaki uyarılar sona erecektir.
      
      // NOT: display_name gibi profiles tablosuna ait alanlar burada ele alınmıyor
      // bunun için ayrı bir mekanizma gerekir
      
      // Gönderilecek veriyi daha detaylı konsola yazdır
      console.log('[ADMIN UPDATE] Data being sent to RPC:', JSON.stringify(settingsToUpdate, null, 2));
      
      // Eğer güncellenecek bir şey yoksa RPC'yi çağırma
      if (Object.keys(settingsToUpdate).length === 0) {
        toast({
          title: 'Bilgi',
          description: 'Değişiklik yapılmadı.',
          status: 'info',
          duration: 5000,
          isClosable: true
        });
        setIsSubmitting(false);
        return;
      }
      
      console.log(`[ADMIN UPDATE] Calling RPC 'update_user_settings_admin' with target_user_id: ${userId}`);
      
      const { data: rpcResult, error } = await supabase.rpc(
        'update_user_settings_admin',
        {
          target_user_id: userId,
          settings_data: settingsToUpdate
        }
      );
      
      if (error) {
        console.error('[ADMIN UPDATE ERROR] RPC error:', error);
        throw error;
      }
      
      console.log('[ADMIN UPDATE] User settings successfully updated. RPC result:', rpcResult);
      
      // RPC'den dönen mesajı kontrol et
      if (rpcResult && !rpcResult.startsWith('Başarılı')) {
        console.warn('[ADMIN UPDATE INFO/WARN] RPC returned:', rpcResult);
        toast({
          title: 'Bilgi/Uyarı',
          description: rpcResult,
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });
      } else {
        // Başarılı ise MUTLAKA listeyi yenile!
        await fetchUsers();
        
        toast({
          title: 'Ayarlar güncellendi',
          description: rpcResult || 'Kullanıcı ayarları başarıyla güncellendi.',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        
        // Modalı kapat
        onClose();
      }
    } catch (err) {
      console.error('Kullanıcı güncellenirken hata:', err);
      toast({
        title: 'Hata',
        description: 'Kullanıcı ayarları güncellenirken bir hata oluştu.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleString('tr-TR', {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (err) {
      console.error('Tarih formatlanırken hata oluştu:', err);
      return dateString;
    }
  };
  
  if (loading) {
    return (
      <Flex justify="center" align="center" height="calc(100vh - 200px)" direction="column">
        <Spinner size="xl" color={brandColor} mb={4} />
        <Text color={textColor}>Kullanıcılar yükleniyor...</Text>
      </Flex>
    );
  }

  return (
    <Box>
      {/* Başlık ve Açıklama */}
      <Card p="0px" mb="20px" boxShadow={shadow} borderRadius="20px">
        <Box p="22px">
          <Flex direction="column">
            <Flex align="center" mb="20px">
              <Box
                me="16px"
                w="58px"
                h="58px"
                bg={iconBoxBg}
                borderRadius="16px"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Icon as={FiUsers} color={brandColor} w="32px" h="32px" />
              </Box>
              <Box>
                <Heading color={textColor} fontSize="2xl" fontWeight="700">
                  Kullanıcı Yönetimi
                </Heading>
                <Text color={textColorSecondary} fontSize="md">
                  Tüm kullanıcıları görüntüleyin ve yönetin
                </Text>
              </Box>
            </Flex>
            
            <Flex justify="flex-end" w="100%" mb="10px">
              <InputGroup maxW={{ base: '100%', md: '320px' }}>
                <InputLeftElement pointerEvents='none'>
                  <Icon as={FiSearch} color={textColorSecondary} />
                </InputLeftElement>
                <Input
                  type='text'
                  placeholder='Kullanıcı ara...'
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  bg={inputBg}
                  fontSize="sm"
                  borderRadius="16px"
                  border="1px solid"
                  borderColor={borderColor}
                  h="44px"
                  _placeholder={{ color: placeholderColor }}
                  _focus={{ 
                    borderColor: brandColor,
                    boxShadow: '0 0 0 2px var(--chakra-colors-brand-300)' 
                  }}
                />
              </InputGroup>
            </Flex>
          </Flex>
        </Box>
      </Card>
      
      {/* Kullanıcı Tablosu */}
      <Card 
        flexDirection='column' 
        w='100%' 
        p='0px' 
        overflowX={{ sm: 'scroll', lg: 'hidden' }} 
        boxShadow={shadow}
        borderRadius="20px"
      >
        <Flex p="22px" mb="10px" justifyContent='space-between' align='center'>
          <Text color={textColor} fontSize="xl" fontWeight='700' lineHeight='100%'>
            Kullanıcı Listesi
          </Text>
        </Flex>
        <Box px="11px">
          <Table variant='simple' color={textColorSecondary} mb="24px">
            <Thead>
              <Tr bg={tableHeaderBg}>
                <Th 
                  borderColor={tableBorderColor} 
                  borderTopLeftRadius="12px"
                  color={headerTextColor} 
                  fontSize={{ sm: '10px', lg: '12px' }} 
                  fontWeight="700" 
                  textTransform="uppercase" 
                  ps="24px"
                  py="16px"
                >
                  E-Posta
                </Th>
                <Th 
                  borderColor={tableBorderColor} 
                  color={headerTextColor} 
                  fontSize={{ sm: '10px', lg: '12px' }} 
                  fontWeight="700" 
                  textTransform="uppercase"
                  py="16px"
                >
                  Görünen Ad
                </Th>
                <Th 
                  borderColor={tableBorderColor} 
                  color={headerTextColor} 
                  fontSize={{ sm: '10px', lg: '12px' }} 
                  fontWeight="700" 
                  textTransform="uppercase"
                  py="16px"
                >
                  Kayıt Tarihi
                </Th>
                <Th 
                  borderColor={tableBorderColor} 
                  color={headerTextColor} 
                  fontSize={{ sm: '10px', lg: '12px' }} 
                  fontWeight="700" 
                  textTransform="uppercase" 
                  textAlign="center"
                  py="16px"
                >
                  Aktif
                </Th>
                <Th 
                  borderColor={tableBorderColor} 
                  color={headerTextColor} 
                  fontSize={{ sm: '10px', lg: '12px' }} 
                  fontWeight="700" 
                  textTransform="uppercase" 
                  textAlign="center"
                  py="16px"
                >
                  Süper Kullanıcı
                </Th>
                <Th 
                  borderColor={tableBorderColor} 
                  color={headerTextColor} 
                  fontSize={{ sm: '10px', lg: '12px' }} 
                  fontWeight="700" 
                  textTransform="uppercase" 
                  textAlign="right"
                  py="16px"
                >
                  Yatırım Tutarı
                </Th>
                <Th 
                  borderColor={tableBorderColor} 
                  borderTopRightRadius="12px"
                  color={headerTextColor} 
                  fontSize={{ sm: '10px', lg: '12px' }} 
                  fontWeight="700" 
                  textTransform="uppercase" 
                  pe="24px" 
                  textAlign="center"
                  py="16px"
                >
                  İşlemler
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <Tr 
                    key={user.id} 
                    _hover={{ bg: hoverRowBg, transition: 'all 0.3s ease' }}
                  >
                    <Td borderColor={tableBorderColor} fontSize="sm" fontWeight="500" ps="24px" py="16px">
                      <Flex align="center">
                        <Box
                          w="36px"
                          h="36px"
                          me="12px"
                          bg={iconBoxBg}
                          borderRadius="12px"
                          display="flex"
                          alignItems="center"
                          justifyContent="center"
                        >
                          <Icon as={FiUser} color={brandColor} w="20px" h="20px" />
                        </Box>
                        <Flex direction="column">
                          <Text color={textColor} fontSize="sm" fontWeight="700">
                            {user.email || '-'}
                          </Text>
                          <Text color="secondaryGray.500" fontSize="xs" fontWeight="400">
                            ID: {user.id.substring(0, 10)}...
                          </Text>
                        </Flex>
                      </Flex>
                    </Td>
                    <Td borderColor={tableBorderColor} fontSize="sm" fontWeight="500" py="16px">
                      <Text color={textColor} fontSize="sm" fontWeight="500">
                        {user.display_name || '-'}
                      </Text>
                    </Td>
                    <Td borderColor={tableBorderColor} fontSize="sm" fontWeight="500" py="16px">
                      <Text color={textColor} fontSize="sm" fontWeight="500">
                        {formatDate(user.created_at)}
                      </Text>
                    </Td>
                    <Td borderColor={tableBorderColor} textAlign="center" py="16px">
                      <Badge 
                        colorScheme={user.is_active ? 'green' : 'red'} 
                        px="8px" 
                        py="4px" 
                        borderRadius="8px"
                        fontSize="xs"
                        fontWeight="500"
                      >
                        {user.is_active ? 'Aktif' : 'Pasif'}
                      </Badge>
                    </Td>
                    <Td borderColor={tableBorderColor} textAlign="center" py="16px">
                      <Flex justify="center">
                        <Switch 
                          isChecked={user.is_superuser} 
                          onChange={() => handleSuperUserChange(user, user.is_superuser)}
                          isDisabled={updatingUsers.includes(user.id)}
                          colorScheme="brand"
                          size="md"
                        />
                        {updatingUsers.includes(user.id) && <Spinner size="sm" ml={2} color={brandColor} />}
                      </Flex>
                    </Td>
                    <Td borderColor={tableBorderColor} fontSize="sm" fontWeight="700" textAlign="right" py="16px">
                      {user.total_investment_amount !== null && user.total_investment_amount !== undefined
                        ? <Text color={textColor} fontSize="sm" fontWeight="700">{user.total_investment_amount.toLocaleString('tr-TR')} ₺</Text>
                        : <Text color="gray.400" fontSize="sm">-</Text>}
                    </Td>
                    <Td borderColor={tableBorderColor} pe="24px" textAlign="center" py="16px">
                      <Flex justify="center">
                        <IconButton
                          icon={<FiEdit2 />}
                          aria-label="Kullanıcıyı düzenle"
                          onClick={() => handleViewUserDetails(user)}
                          colorScheme="brand"
                          variant="outline"
                          size="sm"
                          borderRadius="12px"
                          boxShadow="0px 2px 4px rgba(0, 0, 0, 0.05)"
                          _hover={{ 
                            transform: 'translateY(-2px)', 
                            boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)',
                            bg: brandColor,
                            color: 'white' 
                          }}
                          transition="all 0.3s ease"
                        />
                      </Flex>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={7} textAlign="center" py={6} fontSize="sm" color={textColorSecondary}>
                    {searchQuery ? 'Arama kriterlerinize uygun kullanıcı bulunamadı.' : 'Henüz hiç kullanıcı bulunmuyor.'}
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>
      </Card>

      {/* Kullanıcı Düzenleme Modalı */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay backdropFilter="blur(4px)" bg="blackAlpha.300" />
        <ModalContent bg={bgCard} borderRadius="20px" boxShadow={shadow}>
          <ModalHeader color={textColor} borderBottomWidth="1px" borderColor={borderColor} px={6} py={4}>
            <Flex align="center" gap={2}>
              <Box
                w="36px"
                h="36px"
                me="12px"
                bg={iconBoxBg}
                borderRadius="12px"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Icon as={FiUser} color={brandColor} w="20px" h="20px" />
              </Box>
              <Text fontWeight="700" fontSize="lg">Kullanıcı Bilgilerini Düzenle</Text>
            </Flex>
          </ModalHeader>
          <ModalCloseButton mt={2} me={2} />
          <ModalBody px={6} py={4}>
            {/* Kullanıcı bilgileri formu */}
            <VStack spacing={5} align="stretch">
              {/* E-posta - Salt okunur */}
              <FormControl>
                <FormLabel fontSize="sm" fontWeight="700" color={textColor}>E-Posta</FormLabel>
                <Input 
                  value={selectedUser?.email || ''} 
                  isReadOnly
                  bg={inputBg}
                  color={textColor}
                  borderRadius="16px"
                  fontSize="sm"
                  border="1px solid"
                  borderColor={borderColor}
                  h="44px"
                  opacity="0.8"
                  cursor="not-allowed"
                  _focus={{ borderColor: brandColor }}
                />
              </FormControl>
              
              {/* Görünen Ad */}
              <FormControl>
                <FormLabel fontSize="sm" fontWeight="700" color={textColor}>Görünen Ad</FormLabel>
                <Input 
                  value={displayName} 
                  onChange={(e) => setDisplayName(e.target.value)}
                  placeholder="Görünen ad"
                  bg={inputBg}
                  borderRadius="16px"
                  fontSize="sm"
                  border="1px solid"
                  borderColor={borderColor}
                  h="44px"
                  _placeholder={{ color: placeholderColor }}
                  _focus={{ 
                    borderColor: brandColor,
                    boxShadow: '0 0 0 2px var(--chakra-colors-brand-300)' 
                  }}
                />
                <FormHelperText color={textColorSecondary}>Kullanıcının sistem içinde görünecek adı</FormHelperText>
              </FormControl>
              
              {/* API Anahtarı */}
              <FormControl>
                <FormLabel fontSize="sm" fontWeight="700" color={textColor}>API Anahtarı</FormLabel>
                <InputGroup>
                  <Input 
                    value={apiKey} 
                    onChange={(e) => setApiKey(e.target.value)}
                    type={showApiKey ? 'text' : 'password'}
                    placeholder="API anahtarı"
                    bg={inputBg}
                    borderRadius="16px"
                    fontSize="sm"
                    border="1px solid"
                    borderColor={borderColor}
                    h="44px"
                    autoComplete="new-password"
                    _placeholder={{ color: placeholderColor }}
                    _focus={{ 
                      borderColor: brandColor,
                      boxShadow: '0 0 0 2px var(--chakra-colors-brand-300)' 
                    }}
                  />
                  <InputRightElement h="44px">
                    <IconButton
                      size="sm"
                      variant="ghost"
                      aria-label={showApiKey ? 'Gizle' : 'Göster'}
                      icon={showApiKey ? <FiEyeOff /> : <FiEye />}
                      onClick={() => setShowApiKey(!showApiKey)}
                      color={textColorSecondary}
                      _hover={{ bg: 'transparent', color: brandColor }}
                    />
                  </InputRightElement>
                </InputGroup>
                <FormHelperText color={textColorSecondary}>Aracı kurumunuzun API anahtarı</FormHelperText>
              </FormControl>
              
              {/* Token */}
              <FormControl>
                <FormLabel fontSize="sm" fontWeight="700" color={textColor}>Token</FormLabel>
                <InputGroup>
                  <Input 
                    value={token} 
                    onChange={(e) => setToken(e.target.value)}
                    type={showToken ? 'text' : 'password'}
                    placeholder="Token"
                    bg={inputBg}
                    borderRadius="16px"
                    fontSize="sm"
                    border="1px solid"
                    borderColor={borderColor}
                    h="44px"
                    autoComplete="new-password"
                    _placeholder={{ color: placeholderColor }}
                    _focus={{ 
                      borderColor: brandColor,
                      boxShadow: '0 0 0 2px var(--chakra-colors-brand-300)' 
                    }}
                  />
                  <InputRightElement h="44px">
                    <IconButton
                      size="sm"
                      variant="ghost"
                      aria-label={showToken ? 'Gizle' : 'Göster'}
                      icon={showToken ? <FiEyeOff /> : <FiEye />}
                      onClick={() => setShowToken(!showToken)}
                      color={textColorSecondary}
                      _hover={{ bg: 'transparent', color: brandColor }}
                    />
                  </InputRightElement>
                </InputGroup>
                <FormHelperText color={textColorSecondary}>Aracı kurum API token değeri</FormHelperText>
              </FormControl>
              
              {/* Toplam Yatırım Tutarı */}
              <FormControl>
                <FormLabel fontSize="sm" fontWeight="700" color={textColor}>Toplam Yatırım Tutarı</FormLabel>
                <InputNumberGroup>
                  <NumberInput
                    value={totalInvestmentAmount}
                    onChange={setTotalInvestmentAmount}
                    min={0}
                    precision={2}
                    step={100}
                  >
                    <NumberInputField
                      placeholder="Yatırım tutarı"
                      bg={inputBg}
                      borderRadius="16px"
                      fontSize="sm"
                      border="1px solid"
                      borderColor={borderColor}
                      h="44px"
                      _placeholder={{ color: placeholderColor }}
                      _focus={{ 
                        borderColor: brandColor,
                        boxShadow: '0 0 0 2px var(--chakra-colors-brand-300)' 
                      }}
                    />
                  </NumberInput>
                </InputNumberGroup>
                <FormHelperText color={textColorSecondary}>Kullanıcının toplam yatırım limiti (TL)</FormHelperText>
              </FormControl>
              
              {/* Özel Webhook URL */}
              <FormControl>
                <FormLabel fontSize="sm" fontWeight="700" color={textColor}>Özel Webhook URL</FormLabel>
                <Input
                  value={customWebhookUrl}
                  onChange={(e) => setCustomWebhookUrl(e.target.value)}
                  placeholder="https://..."
                  bg={inputBg}
                  borderRadius="16px"
                  fontSize="sm"
                  border="1px solid"
                  borderColor={borderColor}
                  h="44px"
                  _placeholder={{ color: placeholderColor }}
                  _focus={{ 
                    borderColor: brandColor,
                    boxShadow: '0 0 0 2px var(--chakra-colors-brand-300)' 
                  }}
                />
                <FormHelperText color={textColorSecondary}>Sinyallerin yönlendirileceği özel URL (opsiyonel)</FormHelperText>
              </FormControl>
              
              {/* Aktif Durumu */}
              <FormControl>
                <Flex 
                  align="center" 
                  justify="space-between" 
                  p={4} 
                  borderRadius="16px" 
                  border="1px solid" 
                  borderColor={borderColor}
                  bg={formControlBgColor}
                >
                  <Box>
                    <FormLabel fontSize="sm" fontWeight="700" color={textColor} mb="0">Kullanıcı Aktif</FormLabel>
                    <Text fontSize="xs" color={textColorSecondary}>Kullanıcı hesabının aktif olup olmadığı</Text>
                  </Box>
                  <Switch 
                    isChecked={isActive} 
                    onChange={(e) => setIsActive(e.target.checked)} 
                    colorScheme="brand" 
                    size="lg"
                  />
                </Flex>
              </FormControl>
              
              {/* Hata Mesajı */}
              {error && (
                <Alert status="error" mt={2} borderRadius="16px">
                  <AlertIcon />
                  <Box>
                    <AlertTitle fontWeight="700" fontSize="sm">Hata!</AlertTitle>
                    <AlertDescription fontSize="sm">{error}</AlertDescription>
                  </Box>
                </Alert>
              )}
            </VStack>
          </ModalBody>

          <ModalFooter 
            borderTopWidth="1px" 
            borderColor={borderColor} 
            p={4}
            bg={modalFooterBgColor}
            borderBottomRadius="20px"
          >
            <Button 
              onClick={onClose} 
              variant="outline" 
              mr={3} 
              borderRadius="16px" 
              fontWeight="500"
              borderColor={borderColor}
              h="44px"
              fontSize="sm"
              _hover={{ bg: buttonHoverBgColor }}
            >
              İptal
            </Button>
            <Button 
              colorScheme="brand" 
              onClick={handleUpdateUser} 
              isLoading={isSubmitting}
              leftIcon={<FiSettings />}
              borderRadius="16px"
              fontWeight="500"
              h="44px"
              fontSize="sm"
              boxShadow="0px 4px 10px rgba(71, 63, 151, 0.1)"
              _hover={{ 
                transform: 'translateY(-2px)', 
                boxShadow: '0px 6px 15px rgba(71, 63, 151, 0.2)' 
              }}
              transition="all 0.3s ease"
            >
              Kaydet
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Hata mesajı */}
      {error && (
        <Alert 
          status="error" 
          borderRadius="16px" 
          mt={4}
          boxShadow={shadow}
        >
          <AlertIcon />
          <Box flex="1">
            <AlertTitle fontWeight="700">Hata!</AlertTitle>
            <AlertDescription display="block">{error}</AlertDescription>
          </Box>
        </Alert>
      )}
    </Box>
  );
};

export default UserManagement; 