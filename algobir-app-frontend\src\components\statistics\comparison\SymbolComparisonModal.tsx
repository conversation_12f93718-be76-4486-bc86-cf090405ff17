import React, { useMemo } from 'react';
import {
  <PERSON>dal,
  ModalOverlay,
  <PERSON>dalContent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  Text,
  Box,
  SimpleGrid,
  Card,
  CardBody,
  Badge,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Alert,
  AlertIcon,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,

  IconButton,
  Tooltip
} from '@chakra-ui/react';
import { CloseIcon, DownloadIcon } from '@chakra-ui/icons';
import { SymbolMetrics } from '../../../types/symbolAnalysis';
import { 

  BarChart, 
  Bar, 
  RadarChart, 
  PolarGrid, 
  PolarAngleAxis, 
  PolarRadiusAxis, 
  Radar,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  ResponsiveContainer,
  Legend
} from 'recharts';

interface SymbolComparisonModalProps {
  isOpen: boolean;
  onClose: () => void;
  symbols: string[];
  symbolsData: SymbolMetrics[];
  onRemoveSymbol?: (symbol: string) => void;
  onExportComparison?: (symbols: string[]) => void;
}

const SymbolComparisonModal: React.FC<SymbolComparisonModalProps> = ({
  isOpen,
  onClose,
  symbols,
  symbolsData,
  onRemoveSymbol,
  onExportComparison
}) => {
  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');

  // Color palette for different symbols
  const symbolColors = [
    '#3182CE', '#38A169', '#9F7AEA', '#ED8936', '#E53E3E',
    '#00B5D8', '#D69E2E', '#805AD5', '#DD6B20', '#319795'
  ];

  // Get metric label function (must be defined before useMemo hooks that use it)
  const getMetricLabel = (metric: string): string => {
    const labels: { [key: string]: string } = {
      totalPnl: 'Toplam P&L',
      roi: 'ROI (%)',
      winRate: 'Kazanma Oranı (%)',
      sharpeRatio: 'Sharpe Oranı',
      volatility: 'Volatilite',
      totalTrades: 'İşlem Sayısı',
      profitFactor: 'Kar Faktörü'
    };
    return labels[metric] || metric;
  };

  // Filter symbol data for selected symbols
  const comparisonData = useMemo(() => {
    return symbols.map(symbol =>
      symbolsData.find(data => data.symbol === symbol)
    ).filter(Boolean) as SymbolMetrics[];
  }, [symbols, symbolsData]);

  // Prepare chart data for comparison
  const chartData = useMemo(() => {
    const metrics = ['totalPnl', 'roi', 'winRate', 'sharpeRatio', 'volatility'];
    
    return metrics.map(metric => {
      const dataPoint: any = { metric: getMetricLabel(metric) };
      
      comparisonData.forEach((symbolData) => {
        dataPoint[symbolData.symbol] = symbolData[metric as keyof SymbolMetrics] as number;
      });
      
      return dataPoint;
    });
  }, [comparisonData]);

  // Prepare radar chart data
  const radarData = useMemo(() => {
    return comparisonData.map(symbolData => ({
      symbol: symbolData.symbol,
      'ROI': Math.max(0, Math.min(symbolData.roi, 100)), // Normalize to 0-100
      'Kazanma Oranı': symbolData.winRate,
      'Sharpe': Math.max(0, Math.min(symbolData.sharpeRatio * 20, 100)), // Normalize to 0-100
      'Kar Faktörü': Math.max(0, Math.min(symbolData.profitFactor * 10, 100)), // Normalize to 0-100
      'Düşük Risk': Math.max(0, 100 - (symbolData.volatility * 2)) // Invert volatility
    }));
  }, [comparisonData]);

  // Format value based on metric type
  const formatValue = (value: number, metric: string): string => {
    switch (metric) {
      case 'totalPnl':
      case 'averageTradeSize':
      case 'totalInvestment':
        return `₺${value.toFixed(2)}`;
      case 'roi':
      case 'winRate':
      case 'maxDrawdownPercent':
        return `${value.toFixed(2)}%`;
      case 'sharpeRatio':
      case 'volatility':
      case 'profitFactor':
        return value.toFixed(3);
      default:
        return value.toString();
    }
  };

  // Get color for value comparison
  const getComparisonColor = (value: number, metric: string, allValues: number[]): string => {
    const max = Math.max(...allValues);
    const min = Math.min(...allValues);
    
    // For metrics where higher is better
    const higherIsBetter = ['totalPnl', 'roi', 'winRate', 'sharpeRatio', 'profitFactor'];
    // For metrics where lower is better
    const lowerIsBetter = ['volatility', 'maxDrawdownPercent'];
    
    if (higherIsBetter.includes(metric)) {
      if (value === max) return 'green.500';
      if (value === min) return 'red.500';
    } else if (lowerIsBetter.includes(metric)) {
      if (value === min) return 'green.500';
      if (value === max) return 'red.500';
    }
    
    return textColor;
  };

  // Calculate comparison metrics
  const comparisonMetrics = useMemo(() => {
    if (comparisonData.length === 0) return null;

    const totalPnl = comparisonData.reduce((sum, data) => sum + data.totalPnl, 0);
    const totalInvestment = comparisonData.reduce((sum, data) => sum + data.totalInvestment, 0);
    const totalTrades = comparisonData.reduce((sum, data) => sum + data.totalTrades, 0);
    const avgROI = comparisonData.reduce((sum, data) => sum + data.roi, 0) / comparisonData.length;
    const avgWinRate = comparisonData.reduce((sum, data) => sum + data.winRate, 0) / comparisonData.length;

    const bestPerformer = comparisonData.reduce((best, current) => 
      current.totalPnl > best.totalPnl ? current : best
    );
    
    const worstPerformer = comparisonData.reduce((worst, current) => 
      current.totalPnl < worst.totalPnl ? current : worst
    );

    return {
      totalPnl,
      totalInvestment,
      totalTrades,
      avgROI,
      avgWinRate,
      bestPerformer: bestPerformer.symbol,
      worstPerformer: worstPerformer.symbol,
      portfolioROI: totalInvestment > 0 ? (totalPnl / totalInvestment) * 100 : 0
    };
  }, [comparisonData]);

  if (comparisonData.length === 0) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Sembol Karşılaştırması</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Alert status="warning">
              <AlertIcon />
              <Text>Karşılaştırma için en az bir sembol seçmelisiniz.</Text>
            </Alert>
          </ModalBody>
          <ModalFooter>
            <Button onClick={onClose}>Kapat</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size={{ base: "full", md: "6xl" }}>
      <ModalOverlay />
      <ModalContent
        maxW={{ base: "100vw", md: "95vw" }}
        maxH={{ base: "100vh", md: "90vh" }}
        m={{ base: 0, md: 4 }}
        borderRadius={{ base: 0, md: "md" }}
      >
        <ModalHeader>
          <HStack justify="space-between">
            <VStack align="start" spacing={1}>
              <Text fontSize="2xl" fontWeight="bold">Sembol Karşılaştırması</Text>
              <Text fontSize="sm" color="gray.500">
                {comparisonData.length} sembol karşılaştırılıyor
              </Text>
            </VStack>
            <HStack>
              <Tooltip label="Karşılaştırmayı dışa aktar">
                <IconButton
                  aria-label="Export comparison"
                  icon={<DownloadIcon />}
                  size="sm"
                  variant="outline"
                  onClick={() => onExportComparison?.(symbols)}
                />
              </Tooltip>
            </HStack>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody overflowY="auto">
          <VStack spacing={6} align="stretch">
            {/* Selected Symbols */}
            <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
              <CardBody>
                <Text fontSize="md" fontWeight="bold" mb={3}>Seçili Semboller</Text>
                <HStack wrap="wrap" spacing={2}>
                  {comparisonData.map((symbolData, index) => (
                    <Badge
                      key={symbolData.symbol}
                      colorScheme="blue"
                      variant="solid"
                      fontSize="sm"
                      px={3}
                      py={1}
                      display="flex"
                      alignItems="center"
                      gap={2}
                    >
                      <Box
                        w={3}
                        h={3}
                        borderRadius="full"
                        bg={symbolColors[index % symbolColors.length]}
                      />
                      {symbolData.symbol}
                      {onRemoveSymbol && (
                        <IconButton
                          aria-label={`Remove ${symbolData.symbol}`}
                          icon={<CloseIcon />}
                          size="xs"
                          variant="ghost"
                          color="white"
                          onClick={() => onRemoveSymbol(symbolData.symbol)}
                        />
                      )}
                    </Badge>
                  ))}
                </HStack>
              </CardBody>
            </Card>

            {/* Portfolio Summary */}
            {comparisonMetrics && (
              <SimpleGrid columns={{ base: 1, sm: 2, md: 3, lg: 6 }} spacing={{ base: 3, md: 4 }}>
                <Stat>
                  <StatLabel>Toplam P&L</StatLabel>
                  <StatNumber color={comparisonMetrics.totalPnl > 0 ? 'green.500' : 'red.500'}>
                    ₺{comparisonMetrics.totalPnl.toFixed(2)}
                  </StatNumber>
                  <StatHelpText>
                    <StatArrow type={comparisonMetrics.totalPnl > 0 ? 'increase' : 'decrease'} />
                    Portföy toplamı
                  </StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Portföy ROI</StatLabel>
                  <StatNumber color={comparisonMetrics.portfolioROI > 0 ? 'green.500' : 'red.500'}>
                    {comparisonMetrics.portfolioROI.toFixed(2)}%
                  </StatNumber>
                  <StatHelpText>Toplam getiri</StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Ortalama ROI</StatLabel>
                  <StatNumber>{comparisonMetrics.avgROI.toFixed(2)}%</StatNumber>
                  <StatHelpText>Sembol ortalaması</StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>Ortalama Kazanma</StatLabel>
                  <StatNumber>{comparisonMetrics.avgWinRate.toFixed(1)}%</StatNumber>
                  <StatHelpText>Sembol ortalaması</StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>En İyi</StatLabel>
                  <StatNumber color="green.500">{comparisonMetrics.bestPerformer}</StatNumber>
                  <StatHelpText>Performans lideri</StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel>En Kötü</StatLabel>
                  <StatNumber color="red.500">{comparisonMetrics.worstPerformer}</StatNumber>
                  <StatHelpText>Performans sonu</StatHelpText>
                </Stat>
              </SimpleGrid>
            )}

            {/* Comparison Tabs */}
            <Tabs variant="enclosed" colorScheme="blue">
              <TabList>
                <Tab>Detaylı Karşılaştırma</Tab>
                <Tab>Performans Grafikleri</Tab>
                <Tab>Radar Analizi</Tab>
              </TabList>

              <TabPanels>
                {/* Detailed Comparison Tab */}
                <TabPanel px={0}>
                  <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                    <CardBody>
                      <Text fontSize="md" fontWeight="bold" mb={4}>Detaylı Metrik Karşılaştırması</Text>
                      <TableContainer>
                        <Table variant="simple" size="sm">
                          <Thead>
                            <Tr>
                              <Th>Metrik</Th>
                              {comparisonData.map((symbolData, index) => (
                                <Th key={symbolData.symbol} color={symbolColors[index % symbolColors.length]}>
                                  {symbolData.symbol}
                                </Th>
                              ))}
                            </Tr>
                          </Thead>
                          <Tbody>
                            {[
                              'totalPnl', 'roi', 'winRate', 'totalTrades', 'sharpeRatio', 
                              'volatility', 'profitFactor', 'maxDrawdownPercent', 'averageTradeSize'
                            ].map(metric => {
                              const values = comparisonData.map(data => data[metric as keyof SymbolMetrics] as number);
                              
                              return (
                                <Tr key={metric}>
                                  <Td fontWeight="medium">{getMetricLabel(metric)}</Td>
                                  {comparisonData.map((symbolData) => {
                                    const value = symbolData[metric as keyof SymbolMetrics] as number;
                                    return (
                                      <Td 
                                        key={symbolData.symbol}
                                        color={getComparisonColor(value, metric, values)}
                                        fontWeight={
                                          value === Math.max(...values) || value === Math.min(...values) 
                                            ? 'bold' : 'normal'
                                        }
                                      >
                                        {formatValue(value, metric)}
                                      </Td>
                                    );
                                  })}
                                </Tr>
                              );
                            })}
                          </Tbody>
                        </Table>
                      </TableContainer>
                    </CardBody>
                  </Card>
                </TabPanel>

                {/* Performance Charts Tab */}
                <TabPanel px={0}>
                  <VStack spacing={6} align="stretch">
                    {/* Bar Chart Comparison */}
                    <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                      <CardBody>
                        <Text fontSize="md" fontWeight="bold" mb={4}>Metrik Karşılaştırması</Text>
                        <Box h="400px">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="metric" fontSize={12} />
                              <YAxis fontSize={12} />
                              <RechartsTooltip />
                              <Legend />
                              {comparisonData.map((symbolData, index) => (
                                <Bar
                                  key={symbolData.symbol}
                                  dataKey={symbolData.symbol}
                                  fill={symbolColors[index % symbolColors.length]}
                                />
                              ))}
                            </BarChart>
                          </ResponsiveContainer>
                        </Box>
                      </CardBody>
                    </Card>

                    {/* Line Chart for ROI Comparison */}
                    <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                      <CardBody>
                        <Text fontSize="md" fontWeight="bold" mb={4}>ROI Karşılaştırması</Text>
                        <Box h="300px">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart 
                              data={comparisonData.map(data => ({ symbol: data.symbol, roi: data.roi }))}
                              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="symbol" fontSize={12} />
                              <YAxis fontSize={12} />
                              <RechartsTooltip formatter={(value) => [`${value}%`, 'ROI']} />
                              <Bar dataKey="roi" fill="#3182CE" />
                            </BarChart>
                          </ResponsiveContainer>
                        </Box>
                      </CardBody>
                    </Card>
                  </VStack>
                </TabPanel>

                {/* Radar Analysis Tab */}
                <TabPanel px={0}>
                  <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                    <CardBody>
                      <Text fontSize="md" fontWeight="bold" mb={4}>Radar Performans Analizi</Text>
                      <Text fontSize="sm" color="gray.500" mb={4}>
                        Her sembolün farklı metriklerdeki performansını karşılaştırın. 
                        Dış kenar daha iyi performansı gösterir.
                      </Text>
                      <Box h="500px">
                        <ResponsiveContainer width="100%" height="100%">
                          <RadarChart data={radarData[0] ? Object.keys(radarData[0]).filter(key => key !== 'symbol').map(key => {
                            const dataPoint: any = { metric: key };
                            radarData.forEach((data) => {
                              dataPoint[data.symbol] = data[key as keyof typeof data];
                            });
                            return dataPoint;
                          }) : []}>
                            <PolarGrid />
                            <PolarAngleAxis dataKey="metric" fontSize={12} />
                            <PolarRadiusAxis angle={90} domain={[0, 100]} fontSize={10} />
                            {comparisonData.map((symbolData, index) => (
                              <Radar
                                key={symbolData.symbol}
                                name={symbolData.symbol}
                                dataKey={symbolData.symbol}
                                stroke={symbolColors[index % symbolColors.length]}
                                fill={symbolColors[index % symbolColors.length]}
                                fillOpacity={0.1}
                                strokeWidth={2}
                              />
                            ))}
                            <Legend />
                            <RechartsTooltip />
                          </RadarChart>
                        </ResponsiveContainer>
                      </Box>
                    </CardBody>
                  </Card>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="outline" onClick={() => onExportComparison?.(symbols)}>
              Karşılaştırmayı Dışa Aktar
            </Button>
            <Button colorScheme="blue" onClick={onClose}>
              Kapat
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SymbolComparisonModal;
