import React, { useState, use<PERSON>emo, useCallback } from 'react';
import {
  Box,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Select,
  Button,
  ButtonGroup,
  useColorModeValue,
  Icon,
  Alert,
  AlertIcon,

  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,

  Tooltip,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  Flex,
  useBreakpointValue,
  Skeleton,
  SkeletonText,
  Progress,
  Divider,
  Switch,
  FormControl,
  FormLabel,
  RangeSlider,
  RangeSliderTrack,
  RangeSliderFilledTrack,
  RangeSliderThumb,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer
} from '@chakra-ui/react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,

  Line,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Legend,
  PieChart,
  Pie,
  Cell,

  ComposedChart,
  Area,
  AreaChart,

} from 'recharts';
import {
  FiUsers,
  FiTrendingUp,
  FiTarget,

  FiActivity,
  FiSearch,
  FiFilter,
  FiRefreshCw,
  FiDownload,
  FiEye,
  FiBarChart,

  FiSettings,
  FiMaximize2,
  FiMinimize2,

  FiTrendingDown,
  FiDollarSign,
  FiPercent,

  FiCalendar,
  FiStar,
  FiAlertTriangle
} from 'react-icons/fi';
import { motion, AnimatePresence } from 'framer-motion';
import { RobotStats } from '../../../hooks/useEnhancedStatistics';

// Motion components
const MotionBox = motion(Box);
const MotionCard = motion(Card);

// Enhanced interfaces
interface BroRobotAnalyticsProps {
  broRobotStats: RobotStats[];
  isLoading?: boolean;
  onRefresh?: () => void;
  realTimeEnabled?: boolean;
}

interface FilterOptions {
  search: string;
  minROI: number;
  maxROI: number;
  minWinRate: number;
  maxWinRate: number;
  sortBy: 'roi' | 'winRate' | 'totalPnl' | 'totalTrades' | 'profitFactor';
  sortOrder: 'asc' | 'desc';
  showOnlyProfitable: boolean;
  timeRange: 'all' | '1m' | '3m' | '6m' | '1y';
}

interface ChartConfig {
  type: 'bar' | 'line' | 'area' | 'scatter';
  showGrid: boolean;
  showLegend: boolean;
  showTooltip: boolean;
  animated: boolean;
}

const BroRobotAnalytics: React.FC<BroRobotAnalyticsProps> = ({
  broRobotStats,
  isLoading = false,
  onRefresh,
  realTimeEnabled = false
}) => {
  // State management
  const [selectedRobot, setSelectedRobot] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'overview' | 'individual' | 'comparison' | 'advanced'>('overview');
  const [filters, setFilters] = useState<FilterOptions>({
    search: '',
    minROI: -100,
    maxROI: 100,
    minWinRate: 0,
    maxWinRate: 100,
    sortBy: 'roi',
    sortOrder: 'desc',
    showOnlyProfitable: false,
    timeRange: 'all'
  });
  const [chartConfig] = useState<ChartConfig>({
    type: 'bar',
    showGrid: true,
    showLegend: true,
    showTooltip: true,
    animated: true
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState<'roi' | 'winRate' | 'pnl' | 'trades'>('roi');

  // Modal states
  const { isOpen: isFilterOpen, onOpen: onFilterOpen, onClose: onFilterClose } = useDisclosure();
  const { isOpen: isDetailOpen, onOpen: onDetailOpen, onClose: onDetailClose } = useDisclosure();
  const [detailRobot, setDetailRobot] = useState<RobotStats | null>(null);

  // Theme and responsive configuration
  const cardBg = useColorModeValue('white', 'gray.700');
  const textColor = useColorModeValue('gray.700', 'white');
  const accentColor = useColorModeValue('blue.500', 'blue.300');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const hoverBg = useColorModeValue('gray.50', 'gray.600');
  const successColor = useColorModeValue('green.500', 'green.300');
  const errorColor = useColorModeValue('red.500', 'red.300');
  const warningColor = useColorModeValue('orange.500', 'orange.300');

  // Responsive values

  const chartHeight = useBreakpointValue({ base: '300px', md: '400px', lg: '500px' });
  const gridColumns = useBreakpointValue({ base: 1, md: 2, lg: 3, xl: 4 });
  const cardSpacing = useBreakpointValue({ base: 4, md: 6 });

  // Chart colors
  const chartColors = [
    accentColor,
    successColor,
    errorColor,
    warningColor,
    useColorModeValue('purple.500', 'purple.300'),
    useColorModeValue('teal.500', 'teal.300'),
    useColorModeValue('pink.500', 'pink.300'),
    useColorModeValue('cyan.500', 'cyan.300')
  ];

  // Utility functions
  const formatCurrency = useCallback((value: number) => `₺${value.toFixed(2)}`, []);
  const formatPercent = useCallback((value: number) => `${value.toFixed(2)}%`, []);
  const formatNumber = useCallback((value: number) => value.toLocaleString('tr-TR'), []);

  // Enhanced filtering and sorting
  const filteredAndSortedRobots = useMemo(() => {
    if (!broRobotStats || broRobotStats.length === 0) return [];

    let filtered = broRobotStats.filter(robot => {
      // Search filter
      if (filters.search && !robot.robotName.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }

      // ROI filter
      if (robot.roi < filters.minROI || robot.roi > filters.maxROI) {
        return false;
      }

      // Win rate filter
      if (robot.winRate < filters.minWinRate || robot.winRate > filters.maxWinRate) {
        return false;
      }

      // Profitability filter
      if (filters.showOnlyProfitable && robot.totalPnl <= 0) {
        return false;
      }

      return true;
    });

    // Sort
    filtered.sort((a, b) => {
      const aValue = a[filters.sortBy];
      const bValue = b[filters.sortBy];
      const multiplier = filters.sortOrder === 'asc' ? 1 : -1;
      return (aValue - bValue) * multiplier;
    });

    return filtered;
  }, [broRobotStats, filters]);

  // Performance metrics calculations
  const performanceMetrics = useMemo(() => {
    if (!filteredAndSortedRobots.length) return null;

    const totalTrades = filteredAndSortedRobots.reduce((sum, robot) => sum + robot.totalTrades, 0);
    const totalPnl = filteredAndSortedRobots.reduce((sum, robot) => sum + robot.totalPnl, 0);
    const totalInvestment = filteredAndSortedRobots.reduce((sum, robot) => sum + robot.totalInvestment, 0);
    const avgWinRate = filteredAndSortedRobots.reduce((sum, robot) => sum + robot.winRate, 0) / filteredAndSortedRobots.length;
    const avgROI = filteredAndSortedRobots.reduce((sum, robot) => sum + robot.roi, 0) / filteredAndSortedRobots.length;
    const bestPerformer = filteredAndSortedRobots.reduce((best, robot) => robot.roi > best.roi ? robot : best, filteredAndSortedRobots[0]);
    const worstPerformer = filteredAndSortedRobots.reduce((worst, robot) => robot.roi < worst.roi ? robot : worst, filteredAndSortedRobots[0]);
    const profitableRobots = filteredAndSortedRobots.filter(robot => robot.totalPnl > 0).length;
    const profitablePercentage = (profitableRobots / filteredAndSortedRobots.length) * 100;

    return {
      totalTrades,
      totalPnl,
      totalInvestment,
      avgWinRate,
      avgROI,
      bestPerformer,
      worstPerformer,
      profitableRobots,
      profitablePercentage,
      totalRobots: filteredAndSortedRobots.length,
      avgProfitFactor: filteredAndSortedRobots.reduce((sum, robot) => sum + robot.profitFactor, 0) / filteredAndSortedRobots.length,
      avgSharpeRatio: filteredAndSortedRobots.reduce((sum, robot) => sum + robot.sharpeRatio, 0) / filteredAndSortedRobots.length
    };
  }, [filteredAndSortedRobots]);

  // Chart data preparation
  const chartData = useMemo(() => {
    if (!filteredAndSortedRobots.length) return [];

    return filteredAndSortedRobots.map((robot, index) => ({
      name: robot.robotName.length > 15 ? robot.robotName.slice(0, 15) + '...' : robot.robotName,
      fullName: robot.robotName,
      roi: robot.roi,
      winRate: robot.winRate,
      profitFactor: robot.profitFactor,
      trades: robot.totalTrades,
      pnl: robot.totalPnl,
      investment: robot.totalInvestment,
      sharpeRatio: robot.sharpeRatio,
      maxDrawdown: robot.maxDrawdown,
      color: chartColors[index % chartColors.length]
    }));
  }, [filteredAndSortedRobots, chartColors]);

  // Pie chart data for robot distribution
  const pieChartData = useMemo(() => {
    if (!filteredAndSortedRobots.length) return [];

    const profitable = filteredAndSortedRobots.filter(robot => robot.totalPnl > 0).length;
    const unprofitable = filteredAndSortedRobots.length - profitable;

    return [
      { name: 'Karlı Robotlar', value: profitable, color: successColor },
      { name: 'Zararlı Robotlar', value: unprofitable, color: errorColor }
    ];
  }, [filteredAndSortedRobots, successColor, errorColor]);

  // Radar chart data for top performers
  const radarData = useMemo(() => {
    if (!filteredAndSortedRobots.length) return [];

    return filteredAndSortedRobots.slice(0, 5).map(robot => ({
      robot: robot.robotName.slice(0, 8),
      ROI: Math.max(0, Math.min(100, robot.roi + 50)),
      'Win Rate': robot.winRate,
      'Profit Factor': Math.min(100, robot.profitFactor * 20),
      'Trades': Math.min(100, (robot.totalTrades / 100) * 100),
      'Sharpe': Math.max(0, Math.min(100, (robot.sharpeRatio + 2) * 25))
    }));
  }, [filteredAndSortedRobots]);

  // Handle empty state
  if (!broRobotStats || broRobotStats.length === 0) {
    return (
      <MotionBox
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Alert status="info" borderRadius="lg" bg={cardBg} border="1px" borderColor={borderColor}>
          <AlertIcon color={accentColor} />
          <VStack align="start" spacing={2}>
            <Text fontWeight="bold" color={textColor}>Bro-Robot Verisi Bulunamadı</Text>
            <Text fontSize="sm" color={textColor} opacity={0.8}>
              Henüz Bro-Robot aboneliğiniz bulunmuyor.
              Marketplace'den robot satın aldıktan sonra performans verilerini burada görebileceksiniz.
            </Text>
            <Button
              size="sm"
              colorScheme="blue"
              variant="outline"
              leftIcon={<Icon as={FiUsers} />}
            >
              Robot Pazarını Keşfet
            </Button>
          </VStack>
        </Alert>
      </MotionBox>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <VStack spacing={6} align="stretch">
        <Skeleton height="60px" borderRadius="lg" />
        <SimpleGrid columns={gridColumns} spacing={cardSpacing}>
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} bg={cardBg}>
              <CardBody>
                <SkeletonText noOfLines={3} spacing="4" />
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>
        <Skeleton height="400px" borderRadius="lg" />
      </VStack>
    );
  }

  // Event handlers
  const handleFilterChange = useCallback((newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);



  const handleRobotDetail = useCallback((robot: RobotStats) => {
    setDetailRobot(robot);
    onDetailOpen();
  }, [onDetailOpen]);

  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();
    }
  }, [onRefresh]);

  const selectedRobotData = useMemo(() => {
    if (selectedRobot === 'all') return null;
    return filteredAndSortedRobots.find(robot => robot.robotId === selectedRobot);
  }, [selectedRobot, filteredAndSortedRobots]);

  return (
    <MotionBox
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <VStack spacing={cardSpacing} align="stretch">
        {/* Enhanced Header */}
        <MotionCard
          bg={cardBg}
          borderRadius="xl"
          shadow="sm"
          border="1px"
          borderColor={borderColor}
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <CardBody>
            <Flex direction={{ base: 'column', lg: 'row' }} justify="space-between" align={{ base: 'start', lg: 'center' }} gap={4}>
              <HStack spacing={4}>
                <Box
                  p={3}
                  bg={accentColor}
                  borderRadius="lg"
                  color="white"
                >
                  <Icon as={FiUsers} boxSize={6} />
                </Box>
                <VStack align="start" spacing={1}>
                  <Heading size="lg" color={textColor}>
                    Bro-Robot Performans Analizi
                  </Heading>
                  <Text color={textColor} opacity={0.7} fontSize="sm">
                    {filteredAndSortedRobots.length} robot • Son güncelleme: {new Date().toLocaleTimeString('tr-TR')}
                  </Text>
                </VStack>
              </HStack>

              <HStack spacing={2}>
                {realTimeEnabled && (
                  <Badge colorScheme="green" variant="subtle" px={3} py={1}>
                    <HStack spacing={1}>
                      <Box w={2} h={2} bg="green.500" borderRadius="full" />
                      <Text fontSize="xs">Canlı</Text>
                    </HStack>
                  </Badge>
                )}

                <Tooltip label="Filtreleri aç">
                  <IconButton
                    aria-label="Filtreler"
                    icon={<FiFilter />}
                    variant="ghost"
                    onClick={onFilterOpen}
                    size="sm"
                  />
                </Tooltip>

                <Tooltip label="Verileri yenile">
                  <IconButton
                    aria-label="Yenile"
                    icon={<FiRefreshCw />}
                    variant="ghost"
                    onClick={handleRefresh}
                    isLoading={isLoading}
                    size="sm"
                  />
                </Tooltip>

                <Tooltip label="Tam ekran">
                  <IconButton
                    aria-label="Tam ekran"
                    icon={isFullscreen ? <FiMinimize2 /> : <FiMaximize2 />}
                    variant="ghost"
                    onClick={() => setIsFullscreen(!isFullscreen)}
                    size="sm"
                  />
                </Tooltip>
              </HStack>
            </Flex>

            {/* Performance Summary Badges */}
            <Flex mt={4} gap={3} flexWrap="wrap">
              <Badge colorScheme="blue" variant="subtle" px={3} py={2} borderRadius="md">
                <HStack spacing={2}>
                  <Icon as={FiUsers} />
                  <Text fontWeight="medium">{filteredAndSortedRobots.length} Aktif Robot</Text>
                </HStack>
              </Badge>

              {performanceMetrics && (
                <>
                  <Badge
                    colorScheme={performanceMetrics.totalPnl > 0 ? 'green' : 'red'}
                    variant="subtle"
                    px={3}
                    py={2}
                    borderRadius="md"
                  >
                    <HStack spacing={2}>
                      <Icon as={FiDollarSign} />
                      <Text fontWeight="medium">{formatCurrency(performanceMetrics.totalPnl)} Toplam P&L</Text>
                    </HStack>
                  </Badge>

                  <Badge colorScheme="purple" variant="subtle" px={3} py={2} borderRadius="md">
                    <HStack spacing={2}>
                      <Icon as={FiActivity} />
                      <Text fontWeight="medium">{formatNumber(performanceMetrics.totalTrades)} İşlem</Text>
                    </HStack>
                  </Badge>

                  <Badge
                    colorScheme={performanceMetrics.profitablePercentage > 50 ? 'green' : 'orange'}
                    variant="subtle"
                    px={3}
                    py={2}
                    borderRadius="md"
                  >
                    <HStack spacing={2}>
                      <Icon as={FiPercent} />
                      <Text fontWeight="medium">{performanceMetrics.profitablePercentage.toFixed(1)}% Karlı</Text>
                    </HStack>
                  </Badge>
                </>
              )}
            </Flex>
          </CardBody>
        </MotionCard>

        {/* Enhanced Controls */}
        <MotionCard
          bg={cardBg}
          borderRadius="xl"
          shadow="sm"
          border="1px"
          borderColor={borderColor}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <CardBody>
            <Flex direction={{ base: 'column', lg: 'row' }} gap={6}>
              {/* View Mode Selection */}
              <VStack align="start" spacing={3} flex={1}>
                <Text fontSize="sm" fontWeight="600" color={textColor}>Görünüm Modu</Text>
                <ButtonGroup size="sm" isAttached variant="outline" w="full">
                  <Button
                    isActive={viewMode === 'overview'}
                    onClick={() => setViewMode('overview')}
                    leftIcon={<FiBarChart />}
                    flex={1}
                  >
                    Genel Bakış
                  </Button>
                  <Button
                    isActive={viewMode === 'individual'}
                    onClick={() => setViewMode('individual')}
                    leftIcon={<FiEye />}
                    flex={1}
                  >
                    Bireysel
                  </Button>
                  <Button
                    isActive={viewMode === 'comparison'}
                    onClick={() => setViewMode('comparison')}
                    leftIcon={<FiTarget />}
                    flex={1}
                  >
                    Karşılaştırma
                  </Button>
                  <Button
                    isActive={viewMode === 'advanced'}
                    onClick={() => setViewMode('advanced')}
                    leftIcon={<FiSettings />}
                    flex={1}
                  >
                    Gelişmiş
                  </Button>
                </ButtonGroup>
              </VStack>

              {/* Robot Selection for Individual View */}
              {viewMode === 'individual' && (
                <VStack align="start" spacing={3} flex={1}>
                  <Text fontSize="sm" fontWeight="600" color={textColor}>Robot Seçimi</Text>
                  <Select
                    value={selectedRobot}
                    onChange={(e) => setSelectedRobot(e.target.value)}
                    size="sm"
                    bg={cardBg}
                    borderColor={borderColor}
                  >
                    <option value="all">Tüm Robotlar</option>
                    {filteredAndSortedRobots.map(robot => (
                      <option key={robot.robotId} value={robot.robotId}>
                        {robot.robotName}
                      </option>
                    ))}
                  </Select>
                </VStack>
              )}

              {/* Quick Filters */}
              <VStack align="start" spacing={3} flex={1}>
                <Text fontSize="sm" fontWeight="600" color={textColor}>Hızlı Filtreler</Text>
                <HStack spacing={2} flexWrap="wrap">
                  <Button
                    size="xs"
                    variant={filters.showOnlyProfitable ? "solid" : "outline"}
                    colorScheme="green"
                    onClick={() => handleFilterChange({ showOnlyProfitable: !filters.showOnlyProfitable })}
                    leftIcon={<FiTrendingUp />}
                  >
                    Sadece Karlı
                  </Button>
                  <Menu>
                    <MenuButton as={Button} size="xs" variant="outline" rightIcon={<FiCalendar />}>
                      {filters.timeRange === 'all' ? 'Tüm Zamanlar' :
                       filters.timeRange === '1m' ? 'Son 1 Ay' :
                       filters.timeRange === '3m' ? 'Son 3 Ay' :
                       filters.timeRange === '6m' ? 'Son 6 Ay' : 'Son 1 Yıl'}
                    </MenuButton>
                    <MenuList>
                      <MenuItem onClick={() => handleFilterChange({ timeRange: 'all' })}>Tüm Zamanlar</MenuItem>
                      <MenuItem onClick={() => handleFilterChange({ timeRange: '1m' })}>Son 1 Ay</MenuItem>
                      <MenuItem onClick={() => handleFilterChange({ timeRange: '3m' })}>Son 3 Ay</MenuItem>
                      <MenuItem onClick={() => handleFilterChange({ timeRange: '6m' })}>Son 6 Ay</MenuItem>
                      <MenuItem onClick={() => handleFilterChange({ timeRange: '1y' })}>Son 1 Yıl</MenuItem>
                    </MenuList>
                  </Menu>
                </HStack>
              </VStack>

              {/* Search */}
              <VStack align="start" spacing={3} flex={1}>
                <Text fontSize="sm" fontWeight="600" color={textColor}>Arama</Text>
                <InputGroup size="sm">
                  <InputLeftElement>
                    <FiSearch color={textColor} />
                  </InputLeftElement>
                  <Input
                    placeholder="Robot ara..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange({ search: e.target.value })}
                    bg={cardBg}
                    borderColor={borderColor}
                  />
                </InputGroup>
              </VStack>
            </Flex>
          </CardBody>
        </MotionCard>

        {/* Content based on view mode */}
        <AnimatePresence mode="wait">
          {viewMode === 'overview' && performanceMetrics && (
            <MotionBox
              key="overview"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <VStack spacing={cardSpacing} align="stretch">
                {/* Enhanced Statistics Grid */}
                <SimpleGrid columns={gridColumns} spacing={cardSpacing}>
                  {/* Average ROI Card */}
                  <MotionCard
                    bg={cardBg}
                    borderRadius="xl"
                    shadow="sm"
                    border="1px"
                    borderColor={borderColor}
                    _hover={{ shadow: 'md', transform: 'translateY(-2px)' }}
                    transition={{ duration: 0.2 }}
                    whileHover={{ y: -2 }}
                  >
                    <CardBody>
                      <Stat>
                        <HStack justify="space-between" mb={2}>
                          <StatLabel color={textColor} fontSize="sm" fontWeight="medium">
                            Ortalama ROI
                          </StatLabel>
                          <Icon
                            as={performanceMetrics.avgROI > 0 ? FiTrendingUp : FiTrendingDown}
                            color={performanceMetrics.avgROI > 0 ? successColor : errorColor}
                          />
                        </HStack>
                        <StatNumber
                          color={performanceMetrics.avgROI > 0 ? successColor : errorColor}
                          fontSize="2xl"
                          fontWeight="bold"
                        >
                          {formatPercent(performanceMetrics.avgROI)}
                        </StatNumber>
                        <StatHelpText fontSize="xs" color={textColor} opacity={0.7}>
                          <StatArrow type={performanceMetrics.avgROI > 0 ? 'increase' : 'decrease'} />
                          {performanceMetrics.totalRobots} robot ortalaması
                        </StatHelpText>
                        <Progress
                          value={Math.abs(performanceMetrics.avgROI)}
                          max={100}
                          colorScheme={performanceMetrics.avgROI > 0 ? 'green' : 'red'}
                          size="sm"
                          mt={2}
                          borderRadius="full"
                        />
                      </Stat>
                    </CardBody>
                  </MotionCard>

                  {/* Average Win Rate Card */}
                  <MotionCard
                    bg={cardBg}
                    borderRadius="xl"
                    shadow="sm"
                    border="1px"
                    borderColor={borderColor}
                    _hover={{ shadow: 'md', transform: 'translateY(-2px)' }}
                    transition={{ duration: 0.2 }}
                    whileHover={{ y: -2 }}
                  >
                    <CardBody>
                      <Stat>
                        <HStack justify="space-between" mb={2}>
                          <StatLabel color={textColor} fontSize="sm" fontWeight="medium">
                            Ortalama Kazanma Oranı
                          </StatLabel>
                          <Icon
                            as={FiTarget}
                            color={performanceMetrics.avgWinRate > 50 ? successColor : warningColor}
                          />
                        </HStack>
                        <StatNumber
                          color={performanceMetrics.avgWinRate > 50 ? successColor : warningColor}
                          fontSize="2xl"
                          fontWeight="bold"
                        >
                          {formatPercent(performanceMetrics.avgWinRate)}
                        </StatNumber>
                        <StatHelpText fontSize="xs" color={textColor} opacity={0.7}>
                          Tüm robotlar ortalaması
                        </StatHelpText>
                        <Progress
                          value={performanceMetrics.avgWinRate}
                          max={100}
                          colorScheme={performanceMetrics.avgWinRate > 50 ? 'green' : 'orange'}
                          size="sm"
                          mt={2}
                          borderRadius="full"
                        />
                      </Stat>
                    </CardBody>
                  </MotionCard>

                  {/* Best Performer Card */}
                  <MotionCard
                    bg={cardBg}
                    borderRadius="xl"
                    shadow="sm"
                    border="1px"
                    borderColor={borderColor}
                    _hover={{ shadow: 'md', transform: 'translateY(-2px)' }}
                    transition={{ duration: 0.2 }}
                    whileHover={{ y: -2 }}
                  >
                    <CardBody>
                      <Stat>
                        <HStack justify="space-between" mb={2}>
                          <StatLabel color={textColor} fontSize="sm" fontWeight="medium">
                            En İyi Performans
                          </StatLabel>
                          <Icon as={FiStar} color={successColor} />
                        </HStack>
                        <StatNumber color={successColor} fontSize="2xl" fontWeight="bold">
                          {formatPercent(performanceMetrics.bestPerformer.roi)}
                        </StatNumber>
                        <StatHelpText fontSize="xs" color={textColor} opacity={0.7}>
                          {performanceMetrics.bestPerformer.robotName.length > 20
                            ? performanceMetrics.bestPerformer.robotName.slice(0, 20) + '...'
                            : performanceMetrics.bestPerformer.robotName
                          }
                        </StatHelpText>
                        <Button
                          size="xs"
                          variant="ghost"
                          colorScheme="green"
                          mt={2}
                          onClick={() => handleRobotDetail(performanceMetrics.bestPerformer)}
                          leftIcon={<FiEye />}
                        >
                          Detay
                        </Button>
                      </Stat>
                    </CardBody>
                  </MotionCard>

                  {/* Profitability Card */}
                  <MotionCard
                    bg={cardBg}
                    borderRadius="xl"
                    shadow="sm"
                    border="1px"
                    borderColor={borderColor}
                    _hover={{ shadow: 'md', transform: 'translateY(-2px)' }}
                    transition={{ duration: 0.2 }}
                    whileHover={{ y: -2 }}
                  >
                    <CardBody>
                      <Stat>
                        <HStack justify="space-between" mb={2}>
                          <StatLabel color={textColor} fontSize="sm" fontWeight="medium">
                            Karlılık Oranı
                          </StatLabel>
                          <Icon
                            as={performanceMetrics.profitablePercentage > 50 ? FiTrendingUp : FiAlertTriangle}
                            color={performanceMetrics.profitablePercentage > 50 ? successColor : warningColor}
                          />
                        </HStack>
                        <StatNumber
                          color={performanceMetrics.profitablePercentage > 50 ? successColor : warningColor}
                          fontSize="2xl"
                          fontWeight="bold"
                        >
                          {formatPercent(performanceMetrics.profitablePercentage)}
                        </StatNumber>
                        <StatHelpText fontSize="xs" color={textColor} opacity={0.7}>
                          {performanceMetrics.profitableRobots} / {performanceMetrics.totalRobots} robot karlı
                        </StatHelpText>
                        <Progress
                          value={performanceMetrics.profitablePercentage}
                          max={100}
                          colorScheme={performanceMetrics.profitablePercentage > 50 ? 'green' : 'orange'}
                          size="sm"
                          mt={2}
                          borderRadius="full"
                        />
                      </Stat>
                    </CardBody>
                  </MotionCard>
                </SimpleGrid>

                {/* Enhanced Charts Section */}
                <SimpleGrid columns={{ base: 1, xl: 2 }} spacing={cardSpacing}>
                  {/* Performance Comparison Chart */}
                  <MotionCard
                    bg={cardBg}
                    borderRadius="xl"
                    shadow="sm"
                    border="1px"
                    borderColor={borderColor}
                    whileHover={{ y: -2 }}
                  >
                    <CardHeader pb={2}>
                      <HStack justify="space-between">
                        <VStack align="start" spacing={1}>
                          <Heading size="md" color={textColor}>Robot Performans Karşılaştırması</Heading>
                          <Text fontSize="sm" color={textColor} opacity={0.7}>
                            {selectedMetric === 'roi' ? 'ROI Bazlı' :
                             selectedMetric === 'winRate' ? 'Kazanma Oranı Bazlı' :
                             selectedMetric === 'pnl' ? 'P&L Bazlı' : 'İşlem Sayısı Bazlı'} Karşılaştırma
                          </Text>
                        </VStack>
                        <Menu>
                          <MenuButton as={IconButton} icon={<FiSettings />} variant="ghost" size="sm" />
                          <MenuList>
                            <MenuItem onClick={() => setSelectedMetric('roi')}>ROI</MenuItem>
                            <MenuItem onClick={() => setSelectedMetric('winRate')}>Kazanma Oranı</MenuItem>
                            <MenuItem onClick={() => setSelectedMetric('pnl')}>P&L</MenuItem>
                            <MenuItem onClick={() => setSelectedMetric('trades')}>İşlem Sayısı</MenuItem>
                          </MenuList>
                        </Menu>
                      </HStack>
                    </CardHeader>
                    <CardBody pt={0}>
                      <Box h={chartHeight}>
                        <ResponsiveContainer width="100%" height="100%">
                          <ComposedChart data={chartData}>
                            {chartConfig.showGrid && <CartesianGrid strokeDasharray="3 3" opacity={0.3} />}
                            <XAxis
                              dataKey="name"
                              fontSize={12}
                              tick={{ fill: textColor }}
                              axisLine={{ stroke: borderColor }}
                            />
                            <YAxis
                              fontSize={12}
                              tick={{ fill: textColor }}
                              axisLine={{ stroke: borderColor }}
                            />
                            {chartConfig.showTooltip && (
                              <RechartsTooltip
                                contentStyle={{
                                  backgroundColor: cardBg,
                                  border: `1px solid ${borderColor}`,
                                  borderRadius: '8px',
                                  color: textColor
                                }}
                                formatter={(value: number, name: string) => {
                                  if (name === 'roi') return [formatPercent(value), 'ROI'];
                                  if (name === 'pnl') return [formatCurrency(value), 'P&L'];
                                  if (name === 'winRate') return [formatPercent(value), 'Kazanma Oranı'];
                                  if (name === 'trades') return [formatNumber(value), 'İşlem Sayısı'];
                                  return [value, name];
                                }}
                                labelFormatter={(label) => `Robot: ${label}`}
                              />
                            )}
                            {chartConfig.showLegend && <Legend />}
                            <Bar
                              dataKey={selectedMetric}
                              fill={accentColor}
                              name={
                                selectedMetric === 'roi' ? 'ROI %' :
                                selectedMetric === 'winRate' ? 'Kazanma Oranı %' :
                                selectedMetric === 'pnl' ? 'P&L' : 'İşlem Sayısı'
                              }
                              radius={[4, 4, 0, 0]}
                            />
                            <Line
                              type="monotone"
                              dataKey="profitFactor"
                              stroke={successColor}
                              strokeWidth={2}
                              dot={{ fill: successColor, strokeWidth: 2, r: 4 }}
                              name="Profit Factor"
                            />
                          </ComposedChart>
                        </ResponsiveContainer>
                      </Box>
                    </CardBody>
                  </MotionCard>

                  {/* Profitability Distribution Pie Chart */}
                  <MotionCard
                    bg={cardBg}
                    borderRadius="xl"
                    shadow="sm"
                    border="1px"
                    borderColor={borderColor}
                    whileHover={{ y: -2 }}
                  >
                    <CardHeader pb={2}>
                      <VStack align="start" spacing={1}>
                        <Heading size="md" color={textColor}>Karlılık Dağılımı</Heading>
                        <Text fontSize="sm" color={textColor} opacity={0.7}>
                          Karlı vs Zararlı Robot Oranı
                        </Text>
                      </VStack>
                    </CardHeader>
                    <CardBody pt={0}>
                      <Box h={chartHeight}>
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              data={pieChartData}
                              cx="50%"
                              cy="50%"
                              innerRadius={60}
                              outerRadius={120}
                              paddingAngle={5}
                              dataKey="value"
                            >
                              {pieChartData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                            {chartConfig.showTooltip && (
                              <RechartsTooltip
                                contentStyle={{
                                  backgroundColor: cardBg,
                                  border: `1px solid ${borderColor}`,
                                  borderRadius: '8px',
                                  color: textColor
                                }}
                                formatter={(value: number, name: string) => [value, name]}
                              />
                            )}
                            {chartConfig.showLegend && <Legend />}
                          </PieChart>
                        </ResponsiveContainer>
                      </Box>

                      {/* Summary Stats */}
                      <VStack spacing={2} mt={4}>
                        <HStack justify="space-between" w="full">
                          <HStack>
                            <Box w={3} h={3} bg={successColor} borderRadius="full" />
                            <Text fontSize="sm" color={textColor}>Karlı Robotlar</Text>
                          </HStack>
                          <Text fontSize="sm" fontWeight="bold" color={textColor}>
                            {performanceMetrics.profitableRobots} ({formatPercent(performanceMetrics.profitablePercentage)})
                          </Text>
                        </HStack>
                        <HStack justify="space-between" w="full">
                          <HStack>
                            <Box w={3} h={3} bg={errorColor} borderRadius="full" />
                            <Text fontSize="sm" color={textColor}>Zararlı Robotlar</Text>
                          </HStack>
                          <Text fontSize="sm" fontWeight="bold" color={textColor}>
                            {performanceMetrics.totalRobots - performanceMetrics.profitableRobots} ({formatPercent(100 - performanceMetrics.profitablePercentage)})
                          </Text>
                        </HStack>
                      </VStack>
                    </CardBody>
                  </MotionCard>
                </SimpleGrid>
              </VStack>
            </MotionBox>
          )}

          {/* Comparison View */}
          {viewMode === 'comparison' && (
            <MotionBox
              key="comparison"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <VStack spacing={cardSpacing} align="stretch">
                {/* Enhanced Radar Chart */}
                <MotionCard
                  bg={cardBg}
                  borderRadius="xl"
                  shadow="sm"
                  border="1px"
                  borderColor={borderColor}
                  whileHover={{ y: -2 }}
                >
                  <CardHeader>
                    <VStack align="start" spacing={1}>
                      <Heading size="md" color={textColor}>Çok Boyutlu Robot Karşılaştırması</Heading>
                      <Text fontSize="sm" color={textColor} opacity={0.7}>
                        En iyi 5 robotun performans metrikleri
                      </Text>
                    </VStack>
                  </CardHeader>
                  <CardBody>
                    <Box h={chartHeight}>
                      <ResponsiveContainer width="100%" height="100%">
                        <RadarChart data={radarData}>
                          <PolarGrid stroke={borderColor} />
                          <PolarAngleAxis
                            dataKey="robot"
                            tick={{ fill: textColor, fontSize: 12 }}
                          />
                          <PolarRadiusAxis
                            angle={90}
                            domain={[0, 100]}
                            tick={{ fill: textColor, fontSize: 10 }}
                          />
                          {filteredAndSortedRobots.slice(0, 3).map((robot, index) => (
                            <Radar
                              key={robot.robotId}
                              name={robot.robotName.slice(0, 10)}
                              dataKey={robot.robotName.slice(0, 8)}
                              stroke={chartColors[index]}
                              fill={chartColors[index]}
                              fillOpacity={0.1}
                              strokeWidth={2}
                            />
                          ))}
                          {chartConfig.showLegend && <Legend />}
                          {chartConfig.showTooltip && (
                            <RechartsTooltip
                              contentStyle={{
                                backgroundColor: cardBg,
                                border: `1px solid ${borderColor}`,
                                borderRadius: '8px',
                                color: textColor
                              }}
                            />
                          )}
                        </RadarChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardBody>
                </MotionCard>

                {/* Enhanced Comparison Table */}
                <MotionCard
                  bg={cardBg}
                  borderRadius="xl"
                  shadow="sm"
                  border="1px"
                  borderColor={borderColor}
                  whileHover={{ y: -2 }}
                >
                  <CardHeader>
                    <HStack justify="space-between">
                      <VStack align="start" spacing={1}>
                        <Heading size="md" color={textColor}>Detaylı Performans Tablosu</Heading>
                        <Text fontSize="sm" color={textColor} opacity={0.7}>
                          Tüm robotların karşılaştırmalı analizi
                        </Text>
                      </VStack>
                      <Button
                        size="sm"
                        variant="outline"
                        leftIcon={<FiDownload />}
                        onClick={() => {/* Export functionality */}}
                      >
                        Dışa Aktar
                      </Button>
                    </HStack>
                  </CardHeader>
                  <CardBody>
                    <TableContainer>
                      <Table variant="simple" size="sm">
                        <Thead>
                          <Tr>
                            <Th color={textColor}>Robot</Th>
                            <Th color={textColor} isNumeric>ROI</Th>
                            <Th color={textColor} isNumeric>Win Rate</Th>
                            <Th color={textColor} isNumeric>P&L</Th>
                            <Th color={textColor} isNumeric>İşlemler</Th>
                            <Th color={textColor} isNumeric>Profit Factor</Th>
                            <Th color={textColor} isNumeric>Sharpe Ratio</Th>
                            <Th color={textColor}>Aksiyon</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {filteredAndSortedRobots.map((robot) => (
                            <Tr
                              key={robot.robotId}
                              _hover={{ bg: hoverBg }}
                              transition="background-color 0.2s"
                            >
                              <Td>
                                <VStack align="start" spacing={1}>
                                  <Text fontWeight="medium" color={textColor}>
                                    {robot.robotName}
                                  </Text>
                                  <Badge
                                    size="sm"
                                    colorScheme={robot.totalPnl > 0 ? 'green' : 'red'}
                                    variant="subtle"
                                  >
                                    {robot.totalPnl > 0 ? 'Karlı' : 'Zararlı'}
                                  </Badge>
                                </VStack>
                              </Td>
                              <Td isNumeric>
                                <Text
                                  color={robot.roi > 0 ? successColor : errorColor}
                                  fontWeight="medium"
                                >
                                  {formatPercent(robot.roi)}
                                </Text>
                              </Td>
                              <Td isNumeric>
                                <Text
                                  color={robot.winRate > 50 ? successColor : warningColor}
                                  fontWeight="medium"
                                >
                                  {formatPercent(robot.winRate)}
                                </Text>
                              </Td>
                              <Td isNumeric>
                                <Text
                                  color={robot.totalPnl > 0 ? successColor : errorColor}
                                  fontWeight="medium"
                                >
                                  {formatCurrency(robot.totalPnl)}
                                </Text>
                              </Td>
                              <Td isNumeric>
                                <Text color={textColor} fontWeight="medium">
                                  {formatNumber(robot.totalTrades)}
                                </Text>
                              </Td>
                              <Td isNumeric>
                                <Text
                                  color={robot.profitFactor > 1 ? successColor : errorColor}
                                  fontWeight="medium"
                                >
                                  {robot.profitFactor.toFixed(2)}
                                </Text>
                              </Td>
                              <Td isNumeric>
                                <Text
                                  color={robot.sharpeRatio > 0 ? successColor : errorColor}
                                  fontWeight="medium"
                                >
                                  {robot.sharpeRatio.toFixed(2)}
                                </Text>
                              </Td>
                              <Td>
                                <Button
                                  size="xs"
                                  variant="ghost"
                                  colorScheme="blue"
                                  onClick={() => handleRobotDetail(robot)}
                                  leftIcon={<FiEye />}
                                >
                                  Detay
                                </Button>
                              </Td>
                            </Tr>
                          ))}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  </CardBody>
                </MotionCard>
              </VStack>
            </MotionBox>
          )}

          {/* Individual Robot View */}
          {viewMode === 'individual' && selectedRobotData && (
            <MotionBox
              key="individual"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <VStack spacing={cardSpacing} align="stretch">
                {/* Individual Robot Details */}
                <MotionCard
                  bg={cardBg}
                  borderRadius="xl"
                  shadow="sm"
                  border="1px"
                  borderColor={borderColor}
                  whileHover={{ y: -2 }}
                >
                  <CardHeader>
                    <HStack justify="space-between">
                      <VStack align="start" spacing={1}>
                        <Heading size="md" color={textColor}>
                          {selectedRobotData.robotName} - Detaylı Analiz
                        </Heading>
                        <Text fontSize="sm" color={textColor} opacity={0.7}>
                          Bireysel robot performans metrikleri
                        </Text>
                      </VStack>
                      <Badge
                        colorScheme={selectedRobotData.totalPnl > 0 ? 'green' : 'red'}
                        variant="subtle"
                        px={3}
                        py={1}
                      >
                        {selectedRobotData.totalPnl > 0 ? 'Karlı' : 'Zararlı'}
                      </Badge>
                    </HStack>
                  </CardHeader>
                  <CardBody>
                    <SimpleGrid columns={gridColumns} spacing={cardSpacing}>
                      <Stat>
                        <StatLabel color={textColor}>ROI</StatLabel>
                        <StatNumber color={selectedRobotData.roi > 0 ? successColor : errorColor}>
                          {formatPercent(selectedRobotData.roi)}
                        </StatNumber>
                        <StatHelpText>
                          <StatArrow type={selectedRobotData.roi > 0 ? 'increase' : 'decrease'} />
                          Yıllık getiri
                        </StatHelpText>
                      </Stat>
                      <Stat>
                        <StatLabel color={textColor}>Kazanma Oranı</StatLabel>
                        <StatNumber color={selectedRobotData.winRate > 50 ? successColor : warningColor}>
                          {formatPercent(selectedRobotData.winRate)}
                        </StatNumber>
                        <StatHelpText>
                          {selectedRobotData.winningTrades} / {selectedRobotData.totalTrades} işlem
                        </StatHelpText>
                      </Stat>
                      <Stat>
                        <StatLabel color={textColor}>Toplam P&L</StatLabel>
                        <StatNumber color={selectedRobotData.totalPnl > 0 ? successColor : errorColor}>
                          {formatCurrency(selectedRobotData.totalPnl)}
                        </StatNumber>
                        <StatHelpText>
                          Net kar/zarar
                        </StatHelpText>
                      </Stat>
                      <Stat>
                        <StatLabel color={textColor}>İşlem Sayısı</StatLabel>
                        <StatNumber color={accentColor}>
                          {formatNumber(selectedRobotData.totalTrades)}
                        </StatNumber>
                        <StatHelpText>
                          Toplam işlem
                        </StatHelpText>
                      </Stat>
                    </SimpleGrid>
                  </CardBody>
                </MotionCard>

                {/* Individual Robot Charts */}
                <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={cardSpacing}>
                  <MotionCard
                    bg={cardBg}
                    borderRadius="xl"
                    shadow="sm"
                    border="1px"
                    borderColor={borderColor}
                    whileHover={{ y: -2 }}
                  >
                    <CardHeader>
                      <Heading size="md" color={textColor}>Aylık Performans</Heading>
                    </CardHeader>
                    <CardBody>
                      <Box h={chartHeight}>
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart data={selectedRobotData.monthlyPerformance}>
                            <defs>
                              <linearGradient id="colorPnl" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor={accentColor} stopOpacity={0.8}/>
                                <stop offset="95%" stopColor={accentColor} stopOpacity={0.1}/>
                              </linearGradient>
                            </defs>
                            {chartConfig.showGrid && <CartesianGrid strokeDasharray="3 3" opacity={0.3} />}
                            <XAxis
                              dataKey="month"
                              tick={{ fill: textColor, fontSize: 12 }}
                              axisLine={{ stroke: borderColor }}
                            />
                            <YAxis
                              tick={{ fill: textColor, fontSize: 12 }}
                              axisLine={{ stroke: borderColor }}
                            />
                            {chartConfig.showTooltip && (
                              <RechartsTooltip
                                contentStyle={{
                                  backgroundColor: cardBg,
                                  border: `1px solid ${borderColor}`,
                                  borderRadius: '8px',
                                  color: textColor
                                }}
                                formatter={(value: number) => [formatCurrency(value), 'P&L']}
                              />
                            )}
                            <Area
                              type="monotone"
                              dataKey="pnl"
                              stroke={accentColor}
                              fillOpacity={1}
                              fill="url(#colorPnl)"
                              strokeWidth={2}
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </Box>
                    </CardBody>
                  </MotionCard>

                  <MotionCard
                    bg={cardBg}
                    borderRadius="xl"
                    shadow="sm"
                    border="1px"
                    borderColor={borderColor}
                    whileHover={{ y: -2 }}
                  >
                    <CardHeader>
                      <Heading size="md" color={textColor}>Sembol Performansı</Heading>
                    </CardHeader>
                    <CardBody>
                      <Box h={chartHeight}>
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={selectedRobotData.symbolPerformance.slice(0, 8)}>
                            {chartConfig.showGrid && <CartesianGrid strokeDasharray="3 3" opacity={0.3} />}
                            <XAxis
                              dataKey="symbol"
                              tick={{ fill: textColor, fontSize: 12 }}
                              axisLine={{ stroke: borderColor }}
                            />
                            <YAxis
                              tick={{ fill: textColor, fontSize: 12 }}
                              axisLine={{ stroke: borderColor }}
                            />
                            {chartConfig.showTooltip && (
                              <RechartsTooltip
                                contentStyle={{
                                  backgroundColor: cardBg,
                                  border: `1px solid ${borderColor}`,
                                  borderRadius: '8px',
                                  color: textColor
                                }}
                                formatter={(value: number) => [formatCurrency(value), 'P&L']}
                              />
                            )}
                            <Bar
                              dataKey="pnl"
                              fill={accentColor}
                              radius={[4, 4, 0, 0]}
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </Box>
                    </CardBody>
                  </MotionCard>
                </SimpleGrid>
              </VStack>
            </MotionBox>
          )}
        </AnimatePresence>

        {/* Filter Modal */}
        <Modal isOpen={isFilterOpen} onClose={onFilterClose} size="xl">
          <ModalOverlay />
          <ModalContent bg={cardBg}>
            <ModalHeader color={textColor}>Gelişmiş Filtreler</ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              <VStack spacing={6} align="stretch">
                {/* ROI Range */}
                <FormControl>
                  <FormLabel color={textColor}>ROI Aralığı (%)</FormLabel>
                  <RangeSlider
                    min={-100}
                    max={100}
                    step={1}
                    value={[filters.minROI, filters.maxROI]}
                    onChange={(val) => handleFilterChange({ minROI: val[0], maxROI: val[1] })}
                  >
                    <RangeSliderTrack>
                      <RangeSliderFilledTrack />
                    </RangeSliderTrack>
                    <RangeSliderThumb index={0} />
                    <RangeSliderThumb index={1} />
                  </RangeSlider>
                  <HStack justify="space-between" mt={2}>
                    <Text fontSize="sm" color={textColor}>{filters.minROI}%</Text>
                    <Text fontSize="sm" color={textColor}>{filters.maxROI}%</Text>
                  </HStack>
                </FormControl>

                {/* Win Rate Range */}
                <FormControl>
                  <FormLabel color={textColor}>Kazanma Oranı Aralığı (%)</FormLabel>
                  <RangeSlider
                    min={0}
                    max={100}
                    step={1}
                    value={[filters.minWinRate, filters.maxWinRate]}
                    onChange={(val) => handleFilterChange({ minWinRate: val[0], maxWinRate: val[1] })}
                  >
                    <RangeSliderTrack>
                      <RangeSliderFilledTrack />
                    </RangeSliderTrack>
                    <RangeSliderThumb index={0} />
                    <RangeSliderThumb index={1} />
                  </RangeSlider>
                  <HStack justify="space-between" mt={2}>
                    <Text fontSize="sm" color={textColor}>{filters.minWinRate}%</Text>
                    <Text fontSize="sm" color={textColor}>{filters.maxWinRate}%</Text>
                  </HStack>
                </FormControl>

                {/* Sort Options */}
                <FormControl>
                  <FormLabel color={textColor}>Sıralama</FormLabel>
                  <HStack>
                    <Select
                      value={filters.sortBy}
                      onChange={(e) => handleFilterChange({ sortBy: e.target.value as any })}
                      bg={cardBg}
                    >
                      <option value="roi">ROI</option>
                      <option value="winRate">Kazanma Oranı</option>
                      <option value="totalPnl">P&L</option>
                      <option value="totalTrades">İşlem Sayısı</option>
                      <option value="profitFactor">Profit Factor</option>
                    </Select>
                    <Select
                      value={filters.sortOrder}
                      onChange={(e) => handleFilterChange({ sortOrder: e.target.value as any })}
                      bg={cardBg}
                    >
                      <option value="desc">Azalan</option>
                      <option value="asc">Artan</option>
                    </Select>
                  </HStack>
                </FormControl>

                {/* Profitability Filter */}
                <FormControl display="flex" alignItems="center">
                  <FormLabel htmlFor="profitable-only" mb="0" color={textColor}>
                    Sadece karlı robotları göster
                  </FormLabel>
                  <Switch
                    id="profitable-only"
                    isChecked={filters.showOnlyProfitable}
                    onChange={(e) => handleFilterChange({ showOnlyProfitable: e.target.checked })}
                  />
                </FormControl>
              </VStack>
            </ModalBody>
          </ModalContent>
        </Modal>

        {/* Robot Detail Modal */}
        <Modal isOpen={isDetailOpen} onClose={onDetailClose} size="2xl">
          <ModalOverlay />
          <ModalContent bg={cardBg}>
            <ModalHeader color={textColor}>
              {detailRobot?.robotName} - Detaylı Bilgiler
            </ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              {detailRobot && (
                <VStack spacing={4} align="stretch">
                  <SimpleGrid columns={2} spacing={4}>
                    <Stat>
                      <StatLabel color={textColor}>ROI</StatLabel>
                      <StatNumber color={detailRobot.roi > 0 ? successColor : errorColor}>
                        {formatPercent(detailRobot.roi)}
                      </StatNumber>
                    </Stat>
                    <Stat>
                      <StatLabel color={textColor}>Kazanma Oranı</StatLabel>
                      <StatNumber color={detailRobot.winRate > 50 ? successColor : warningColor}>
                        {formatPercent(detailRobot.winRate)}
                      </StatNumber>
                    </Stat>
                    <Stat>
                      <StatLabel color={textColor}>Toplam P&L</StatLabel>
                      <StatNumber color={detailRobot.totalPnl > 0 ? successColor : errorColor}>
                        {formatCurrency(detailRobot.totalPnl)}
                      </StatNumber>
                    </Stat>
                    <Stat>
                      <StatLabel color={textColor}>Profit Factor</StatLabel>
                      <StatNumber color={detailRobot.profitFactor > 1 ? successColor : errorColor}>
                        {detailRobot.profitFactor.toFixed(2)}
                      </StatNumber>
                    </Stat>
                  </SimpleGrid>

                  <Divider />

                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold" color={textColor}>Ek Bilgiler:</Text>
                    <Text fontSize="sm" color={textColor}>
                      <strong>Toplam İşlem:</strong> {formatNumber(detailRobot.totalTrades)}
                    </Text>
                    <Text fontSize="sm" color={textColor}>
                      <strong>Kazanan İşlem:</strong> {detailRobot.winningTrades}
                    </Text>
                    <Text fontSize="sm" color={textColor}>
                      <strong>Kaybeden İşlem:</strong> {detailRobot.losingTrades}
                    </Text>
                    <Text fontSize="sm" color={textColor}>
                      <strong>Sharpe Ratio:</strong> {detailRobot.sharpeRatio.toFixed(2)}
                    </Text>
                    <Text fontSize="sm" color={textColor}>
                      <strong>Max Drawdown:</strong> {formatPercent(detailRobot.maxDrawdown)}
                    </Text>
                  </VStack>
                </VStack>
              )}
            </ModalBody>
          </ModalContent>
        </Modal>
      </VStack>
    </MotionBox>
  );
};

export default BroRobotAnalytics;
