import { Box, Flex, Table, Tbody, Td, Text, Th, Thead, Tr, IconButton, useColorModeValue, Skeleton, Icon, Tooltip, AlertDialog, AlertDialogBody, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogOverlay, Button, useDisclosure, HStack, VStack, Badge, useBreakpointValue } from '@chakra-ui/react';
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  CellContext
} from '@tanstack/react-table';
import * as React from 'react';
import { DeleteIcon, TriangleDownIcon, TriangleUpIcon } from '@chakra-ui/icons';
import { MdCancel, MdCheckCircle, MdOutlineError, MdAutorenew, MdWarning, MdBroadcastOnPersonal } from 'react-icons/md';
import { FaRobot, FaUserAlt, FaQuestionCircle } from 'react-icons/fa';

import Card from '../card/Card';
import { Trade } from '../../types/trade';

interface TradesTableProps {
  trades: Trade[];
  isLoading: boolean;
  onDeleteClick?: (tradeId: number) => void;
  onCancelSignalBatch?: (trade: Trade) => void;
  currentUserId?: string;
  isDeletingTradeId?: number | null;
  isCancellingSignalId?: number | string | null;
}

// Yardımcı fonksiyonlar
const formatNumber = (value: number | null, minimumFractionDigits = 2): string => {
  if (value === null || value === undefined) return '-';
  return value.toLocaleString('tr-TR', { minimumFractionDigits });
};

const formatCurrency = (value: number | null): string => {
  if (value === null || value === undefined) return '-';
  
  try {
    return new Intl.NumberFormat('tr-TR', { 
      style: 'currency', 
      currency: 'TRY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  } catch (err) {
    console.error('Para formatı hatası:', err, 'Değer:', value);
    return value.toFixed(2) + ' ₺'; // Fallback
  }
};

const getSignalTypeDisplay = (signalType: string | null | undefined): string => {
  if (!signalType) return '-';
  
  // Signal type'ı düzgün formatta göster
  switch (signalType.toUpperCase()) {
    case 'BUY':
      return 'ALIM';
    case 'SELL':
      return 'SATIM';
    case 'TP_BUY':
    case 'TP_SELL':
      return 'KAR AL';
    case 'ATRTP':
      return 'KAR AL (ATR)';
    case 'KTP': 
      return 'KAR AL (KTP)';
    case 'SL_BUY':
    case 'SL_SELL':
      return 'ZARAR DURDUR (SL)';
    case 'STOP':
      return 'ZARAR DURDUR';
    case 'CLOSE_ALL':
      return 'TÜMÜNÜ KAPAT';
    case 'N/A':
      return '-';
    default:
      return signalType;
  }
};

const renderPnlCell = (trade: Trade) => {
  // İşlem kategorisi ve durumuna göre PNL hücresini render et
  const positionStatus = trade.position_status?.toLowerCase() || '';
  const orderSide = trade.order_side?.toUpperCase() || '';
  const tradeCategory = trade.trade_category?.toLowerCase() || '';
  const signalType = trade.signal_type?.toUpperCase() || '';
  const status = trade.status || '';
  
  // Direk PNL değeri olan işlemler (özellikle satım işlemleri)
  const hasPnl = trade.pnl !== null && trade.pnl !== undefined && !isNaN(Number(trade.pnl));
  
  // SELL işlemleri için PNL değeri varsa her durumda göster
  // ATRTP, KTP, STOP gibi özel SELL tiplerini de dahil et
  const isSellTrade = 
    orderSide === 'SELL' || 
    tradeCategory === 'satım' || 
    signalType === 'SELL' || 
    signalType === 'ATRTP' || 
    signalType === 'KTP' || 
    signalType === 'STOP';
  
  console.log(`[TradesTable:renderPnlCell] Trade ${trade.id}: signalType=${signalType}, orderSide=${orderSide}, tradeCategory=${tradeCategory}, positionStatus=${positionStatus}, status=${status}, hasPnl=${hasPnl}, isSellTrade=${isSellTrade}`);
  
  // Manuel kapatılan işlemler için özel mesaj göster
  if (status === 'cancelled_by_user') {
    return <Text fontSize="sm" color="orange.500" fontWeight="500" textAlign="right">Manuel Kapatıldı</Text>;
  }
  
  if (hasPnl) {
    const pnlValue = Number(trade.pnl);
    const pnlColor = pnlValue > 0 ? "green.500" : pnlValue < 0 ? "red.500" : "gray.500";
    return (
      <Text fontSize="sm" fontWeight="700" color={pnlColor} textAlign="right">
        {formatCurrency(pnlValue)}
      </Text>
    );
  }
  
  // SELL işlemleri için özel durum kontrolü - hem beklemede hem de kapalı durumlar için
  if (isSellTrade && (positionStatus === 'beklemede' || positionStatus === 'kapalı')) {
    return <Text fontSize="sm" color="purple.500" fontWeight="500" textAlign="right">Hesaplanıyor...</Text>;
  }
  
  // PNL olmayan durumların daha anlaşılır mesajları
  if (positionStatus === 'açık') {
    return <Text fontSize="sm" color="blue.500" fontWeight="500" textAlign="right">İşlem Açık</Text>;
  } 
  
  if (positionStatus === 'kapandı' && !hasPnl) {
    return <Text fontSize="sm" color="purple.500" fontWeight="500" textAlign="right">Hesaplanıyor...</Text>;
  }
  
  if (positionStatus === 'kapalı' && !hasPnl) {
    return <Text fontSize="sm" color="purple.500" fontWeight="500" textAlign="right">Hesaplanıyor...</Text>;
  }
  
  if (positionStatus === 'bakiye yetersiz') {
    return <Text fontSize="sm" fontWeight="500" color="orange.500" textAlign="right">Bakiye Yetersiz</Text>;
  }
  
  if (positionStatus === 'gönderiliyor') {
    return <Text fontSize="sm" color="blue.300" fontWeight="500" textAlign="right">İşleniyor...</Text>;
  }
  
  if (positionStatus === 'pozisyon bulunamadı') {
    return <Text fontSize="sm" color="red.500" fontWeight="500" textAlign="right">Pozisyon Yok</Text>;
  }
  
  if (positionStatus === 'abone yok') {
    return <Text fontSize="sm" color="orange.500" fontWeight="500" textAlign="right">Abone Yok</Text>;
  }
  
  if (positionStatus === 'işleme hatası' || positionStatus === 'iletim hatası') {
    return <Text fontSize="sm" color="red.500" fontWeight="500" textAlign="right">Hata Oluştu</Text>;
  }
  
  // Diğer tüm durumlar için
  return <Text fontSize="sm" color="gray.400" textAlign="right">-</Text>;
};

// İşlem durumu için icon komponentini tanımla
const StatusIcon = ({ status }: { status: string | null }) => {
  if (!status) return null;
  
  const lowerStatus = status.toLowerCase();
  
  switch (lowerStatus) {
    case 'açık':
      return <Icon as={MdCheckCircle} color="green.500" boxSize={4} />;
    case 'kapandı':
      return <Icon as={MdCancel} color="blue.500" boxSize={4} />;
    case 'bakiye yetersiz':
      return <Icon as={MdOutlineError} color="orange.500" boxSize={4} />;
    case 'gönderiliyor':
      return <Icon as={MdAutorenew} color="blue.300" boxSize={4} />;
    case 'pozisyon bulunamadı':
      return <Icon as={MdWarning} color="red.500" boxSize={4} />;
    case 'abone yok':
      return <Icon as={MdWarning} color="orange.500" boxSize={4} />;
    case 'işleme hatası':
    case 'iletim hatası':
      return <Icon as={MdOutlineError} color="red.500" boxSize={4} />;
    case 'bilinmiyor':
      return <Icon as={MdOutlineError} color="gray.500" boxSize={4} />;
    default:
      return null;
  }
};

// Chakra UI'nin Icon bileşenini forwardRef ile sarmalayalım
const ForwardRefIcon = React.forwardRef<SVGSVGElement, any>(
  (props, ref) => <Icon {...props} ref={ref} />
);

ForwardRefIcon.displayName = 'ForwardRefIcon';

// Sinyal kaynağı ikonu ve açıklaması
const SignalSourceIcon = ({ trade }: { trade: Trade }) => {
  const soloRobotColor = useColorModeValue('blue.500', 'blue.300');
  const broRobotColor = useColorModeValue('purple.500', 'purple.300');
  const unknownColor = useColorModeValue('gray.500', 'gray.400');
  
  // robot_id veya webhook_id değerine bakarak kaynağı belirle
  if (trade.robot_id) {
    return (
      <Tooltip label="Bro-Robot (Pazar Yeri)" fontSize="sm">
        <Box cursor="pointer">
          <FaRobot color={broRobotColor} size="16px" />
        </Box>
      </Tooltip>
    );
  } else if (trade.webhook_id) {
    return (
      <Tooltip label="Solo-Robot (Kişisel Webhook)" fontSize="sm">
        <Box cursor="pointer">
          <FaUserAlt color={soloRobotColor} size="16px" />
        </Box>
      </Tooltip>
    );
  } else {
    return (
      <Tooltip label="Bilinmeyen Kaynak" fontSize="sm">
        <Box cursor="pointer">
          <FaQuestionCircle color={unknownColor} size="16px" />
        </Box>
      </Tooltip>
    );
  }
};

// İşlem silme iletişim kutusu komponenti
interface DeleteTradeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  trade: Trade;
}

const DeleteTradeDialog: React.FC<DeleteTradeDialogProps> = ({ isOpen, onClose, onConfirm, trade }) => {
  const cancelRef = React.useRef<HTMLButtonElement>(null);
  
  return (
    <AlertDialog
      isOpen={isOpen}
      leastDestructiveRef={cancelRef}
      onClose={onClose}
      isCentered
    >
      <AlertDialogOverlay>
        <AlertDialogContent borderRadius="12px">
          <AlertDialogHeader fontSize="lg" fontWeight="bold">
            İşlemi Sil
          </AlertDialogHeader>
          
          <AlertDialogBody>
            <Text>
              <strong>{trade.symbol}</strong> sembolüne ait işlemi silmek istediğinize emin misiniz?
            </Text>
            <Text mt={2} fontSize="sm" color="gray.500">
              Bu işlem geri alınamaz.
            </Text>
          </AlertDialogBody>
          
          <AlertDialogFooter gap={3}>
            <Button ref={cancelRef} onClick={onClose} variant="outline">
              Vazgeç
            </Button>
            <Button colorScheme="red" onClick={onConfirm}>
              Sil
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
};

// Sinyal iptal etme iletişim kutusu komponenti
interface CancelSignalDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  trade: Trade;
}

const CancelSignalDialog: React.FC<CancelSignalDialogProps> = ({ isOpen, onClose, onConfirm, trade }) => {
  const cancelRef = React.useRef<HTMLButtonElement>(null);
  
  return (
    <AlertDialog
      isOpen={isOpen}
      leastDestructiveRef={cancelRef}
      onClose={onClose}
      isCentered
    >
      <AlertDialogOverlay>
        <AlertDialogContent borderRadius="12px">
          <AlertDialogHeader fontSize="lg" fontWeight="bold">
            Sinyali İptal Et
          </AlertDialogHeader>
          
          <AlertDialogBody>
            <Text>
              <strong>{trade.symbol}</strong> sembolüne ait sinyali ve tüm abonelerdeki ilişkili işlemleri iptal etmek istediğinize emin misiniz?
            </Text>
            <Text mt={2} fontSize="sm" color="gray.500">
              Bu işlem geri alınamaz ve tüm aboneler için geçerlidir.
            </Text>
          </AlertDialogBody>
          
          <AlertDialogFooter gap={3}>
            <Button ref={cancelRef} onClick={onClose} variant="outline">
              Vazgeç
            </Button>
            <Button colorScheme="orange" onClick={onConfirm}>
              İptal Et
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialogOverlay>
    </AlertDialog>
  );
};

const columnHelper = createColumnHelper<Trade>();

// İşlem silme fonksiyonu ve yetki kontrolünü kapsayacak sütun tanımı
const ActionsColumn = (props: { 
  onDeleteClick?: (tradeId: number) => void, 
  onCancelSignalBatch?: (trade: Trade) => void,
  currentUserId?: string,
  isDeletingTradeId?: number | null,
  isCancellingSignalId?: number | string | null,
  trades?: Trade[]
}) => {
  return columnHelper.accessor('id', {
    id: 'actions',
    header: () => (
      <Text
        justifyContent='center'
        align='center'
        fontSize={{ sm: '10px', lg: '12px' }}
        color="gray.500"
        fontWeight="700"
        textTransform="uppercase">
        İŞLEMLER
      </Text>
    ),
    cell: (info) => {
      const trade = info.row.original;
      // Artık id doğrudan number, geçici id ise string olabilir
      const tradeId = trade.id;
      
      // Silme yetkisi kontrolü
      let canDelete = false;
      
      // Kullanıcı ID'si mevcut mu kontrol et
      if (props.currentUserId) {
        // 1. Solo-robot işlemi mi?
        if ((trade.webhook_id && !trade.robot_id) || (!trade.webhook_id && !trade.robot_id)) {
          // Solo-robot sahibi kendi işlemini silebilir
          if (trade.user_id === props.currentUserId) {
            canDelete = true;
          }
        }
        // 2. Bro-robot işlemi mi?
        else if (trade.robot_id) {
          // Bro-robot satıcısı kendi master işlemini silebilir
          if (trade.robot_seller_id === props.currentUserId && trade.user_id === props.currentUserId) {
            canDelete = true;
          }
          // Bro-robot aboneleri işlem silemez (canDelete = false)
        }
      }
      
      // Sinyal iptal etme yetkisi kontrolü
      let canCancelSignal = false;
      if (props.currentUserId && trade.robot_id) {
        // Kullanıcı robotun satıcısı mı kontrol et
        const isSellerOfThisBot = 
          trade.robot?.seller_id === props.currentUserId || // Robot nesnesi varsa
          trade.robot_seller_id === props.currentUserId;    // Veya robot_seller_id alanı varsa
        
        // Satıcı, kendi robotunun sinyallerini iptal edebilir
        if (isSellerOfThisBot) {
          canCancelSignal = true;
        }
      }
      
      // İşlem silindi mi veya iptal edildi mi diye kontrol et
      const isDeleted = trade.is_deleted === true;
      const isCancelled = trade.is_cancelled_by_seller === true;
      
      // Silme düğmesini göster/gizle ve yükleme durumunu kontrol et
      return (
        <HStack spacing={1} justifyContent="center">
          {/* Kişisel işlem silme butonu */}
          {canDelete && !isDeleted && !isCancelled && (
            <Tooltip label="İşlemi Sil" fontSize="sm">
              <IconButton
                aria-label="İşlemi Sil"
                icon={<DeleteIcon />}
                size="sm"
                colorScheme="red"
                variant="ghost"
                onClick={() => props.onDeleteClick && props.onDeleteClick(tradeId)}
                isLoading={props.isDeletingTradeId === tradeId}
                _hover={{ bg: 'red.50' }}
              />
            </Tooltip>
          )}
          
          {/* Satıcı tarafından sinyal iptal butonu */}
          {canCancelSignal && !isCancelled && (
            <Tooltip label="Aboneler İçin Sinyali İptal Et" fontSize="sm">
              <IconButton
                aria-label="Aboneler İçin Sinyali İptal Et"
                icon={<MdBroadcastOnPersonal />}
                size="sm"
                colorScheme="orange"
                variant="ghost"
                onClick={() => {
                  if (props.onCancelSignalBatch) {
                    props.onCancelSignalBatch(trade);
                  }
                }}
                isLoading={props.isCancellingSignalId === tradeId}
                _hover={{ bg: 'orange.50' }}
              />
            </Tooltip>
          )}
          
          {/* İşlem silindi mesajı */}
          {isDeleted && (
            <Text fontSize="xs" color="red.500" fontStyle="italic">
              Silindi
            </Text>
          )}
          
          {/* İşlem iptal edildi mesajı */}
          {isCancelled && (
            <Text fontSize="xs" color="orange.500" fontStyle="italic">
              İptal Edildi
            </Text>
          )}
        </HStack>
      );
    }
  });
};

export const TradesTable: React.FC<TradesTableProps> = ({ 
  trades, 
  isLoading, 
  onDeleteClick, 
  onCancelSignalBatch,
  currentUserId,
  isDeletingTradeId,
  isCancellingSignalId
}) => {
  const [sorting, setSorting] = React.useState<SortingState>([
    { id: 'received_at', desc: true }
  ]);
  
  // Silme iletişim kutusu için state
  const [tradeToDelete, setTradeToDelete] = React.useState<Trade | null>(null);
  const { isOpen: isDeleteDialogOpen, onOpen: openDeleteDialog, onClose: closeDeleteDialog } = useDisclosure();

  // Sinyal iptal işlemi için state ve dialog
  const [tradeToCancel, setTradeToCancel] = React.useState<Trade | null>(null);
  const { isOpen: isCancelDialogOpen, onOpen: openCancelDialog, onClose: closeCancelDialog } = useDisclosure();
  
  // Responsive breakpoint kontrolü
  const isMobile = useBreakpointValue({ base: true, lg: false });
  
  // İşlemleri filtrele: Solo-robot silinmiş işlemleri gösterme, diğer tüm işlemleri göster
  const tradesToDisplay = React.useMemo(() => {
    return trades.filter(trade => {
      // Solo-robot işlemi mi kontrol et
      const isSoloRobotTrade = !trade.robot_id; // veya (trade.webhook_id && !trade.robot_id)
      
      // Solo-robot işlemi ve silinmiş ise gösterme
      if (isSoloRobotTrade && trade.is_deleted) {
        return false; // Silinmiş solo-robot işlemlerini gösterme
      }
      
      // Diğer tüm işlemleri göster (silinmemiş solo-robot ve tüm bro-robot işlemleri)
      return true;
    });
  }, [trades]);
  
  // Silme butonuna tıklandığında işlemi seç ve iletişim kutusunu aç
  const handleDeleteButtonClick = (tradeId: number) => {
    // Gelen tradeId değerini log ekleyelim
    console.log(`[TradesTable:handleDeleteButtonClick] İşlem silme başlatılıyor, ID: "${tradeId}"`);
    
    const trade = tradesToDisplay.find(t => t.id === tradeId);
    if (trade) {
      console.log(`[TradesTable:handleDeleteButtonClick] İşlem bulundu:`, {
        id: trade.id,
        symbol: trade.symbol,
        isBroRobot: !!trade.robot_id
      });
      setTradeToDelete(trade);
      openDeleteDialog();
    } else {
      console.error(`[DELETE_TRADE] Belirtilen ID ile işlem bulunamadı: "${tradeId}"`);
    }
  };
  
  // Sinyal iptal butonuna tıklandığında
  const handleCancelSignalClick = (trade: Trade) => {
    setTradeToCancel(trade);
    openCancelDialog();
  };
  
  // Silme işlemi onaylandığında
  const handleConfirmDelete = () => {
    if (!tradeToDelete || !onDeleteClick) {
      closeDeleteDialog();
      return;
    }

    // UUID doğrulama ve silme işlemi
    if (typeof tradeToDelete.id === 'number') {
      onDeleteClick(tradeToDelete.id);
    } else {
      console.error(`[DELETE_TRADE] Dialog onayında geçersiz UUID: "${tradeToDelete.id}"`);
    }

    closeDeleteDialog();
  };

  // Sinyal iptalini onaylama işlemi
  const handleConfirmCancel = () => {
    if (!tradeToCancel || !onCancelSignalBatch) {
      closeCancelDialog();
      return;
    }

    // Sinyal iptal işlemini çağır
    onCancelSignalBatch(tradeToCancel);
    closeCancelDialog();
  };
  
  // Theme değerleri
  const textColor = useColorModeValue('navy.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');
  const headerBg = useColorModeValue('secondaryGray.100', 'navy.800');
  const headerTextColor = useColorModeValue('gray.500', 'white');
  const cardShadow = useColorModeValue('0px 18px 40px rgba(112, 144, 176, 0.12)', 'none');
  const sortIconColor = useColorModeValue('brand.500', 'brand.400');
  const cardBg = useColorModeValue('white', 'navy.700');
  const secondaryTextColor = useColorModeValue('secondaryGray.600', 'white');
  
  // Silinen satırlar için stil
  const getRowStyles = (trade: Trade) => {
    // Bro-robot işlemi ve silinmiş ise üstü çizili göster
    const isBroRobotTrade = !!trade.robot_id;
    
    if (isBroRobotTrade && trade.is_deleted) {
      return {
        textDecoration: 'line-through',
        color: 'gray.500',
        opacity: 0.7
      };
    }
    
    return {};
  };

  // Sütun tanımlamaları
  const columns = React.useMemo(() => [
    columnHelper.accessor('received_at', {
      id: 'received_at',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          ALINMA ZAMANI
        </Text>
      ),
      cell: (info: CellContext<Trade, string>) => {
        const trade = info.row.original;
        return (
          <Text 
            color={trade.is_deleted ? 'red.500' : textColor} 
            fontSize='sm' 
            fontWeight='500'
            textDecoration={trade.is_deleted ? 'line-through' : 'none'}
          >
            {new Date(info.getValue()).toLocaleString('tr-TR')}
          </Text>
        );
      }
    }),
    // Signal kaynağı sütunu ekle (Solo-Robot veya Bro-Robot)
    columnHelper.accessor('id', {
      id: 'signal_source',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          KAYNAK
        </Text>
      ),
      cell: (info) => {
        // Cell context'ten row değerini al (cell bilgisi yerine her bir satırın tamamını alalım)
        const row = info.row.original;
        return (
          <Box textAlign="center" opacity={row.is_deleted ? 0.7 : 1}>
            <SignalSourceIcon trade={row} />
          </Box>
        );
      }
    }),
    columnHelper.accessor(row => row.system_name || '', {
      id: 'system_name',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          SİSTEM ADI
        </Text>
      ),
      cell: (info) => {
        const trade = info.row.original;
        return (
          <Text 
            color={trade.is_deleted ? 'red.500' : textColor} 
            fontSize='sm' 
            fontWeight='500'
            textDecoration={trade.is_deleted ? 'line-through' : 'none'}
          >
            {info.getValue() || '-'}
          </Text>
        );
      }
    }),
    columnHelper.accessor('signal_type', {
      id: 'signal_type',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          SİNYAL TİPİ
        </Text>
      ),
      cell: (info: CellContext<Trade, string>) => {
        const trade = info.row.original;
        return (
          <Text 
            color={trade.is_deleted ? 'red.500' : textColor} 
            fontSize='sm' 
            fontWeight='500'
            textDecoration={trade.is_deleted ? 'line-through' : 'none'}
          >
            {getSignalTypeDisplay(info.getValue())}
          </Text>
        );
      }
    }),
    columnHelper.accessor('symbol', {
      id: 'symbol',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          SEMBOL
        </Text>
      ),
      cell: (info: CellContext<Trade, string>) => {
        const trade = info.row.original;
        return (
          <Text 
            color={trade.is_deleted ? 'red.500' : textColor} 
            fontSize='sm' 
            fontWeight='700'
            textDecoration={trade.is_deleted ? 'line-through' : 'none'}
          >
            {info.getValue()}
          </Text>
        );
      }
    }),
    columnHelper.accessor(row => row.trade_category || '', {
      id: 'trade_category',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          KATEGORİ
        </Text>
      ),
      cell: (info) => {
        const category = info.getValue() || '';
        const trade = info.row.original;
        
        // trade_category veya order_side değerine göre Alım veya Satım olarak göster
        if (category === 'Alım') {
          return (
            <Text 
              color={trade.is_deleted ? 'red.500' : 'green.500'} 
              fontSize='sm' 
              fontWeight='semibold'
              textDecoration={trade.is_deleted ? 'line-through' : 'none'}
            >
              Alım
            </Text>
          );
        }
        
        if (category === 'Satım') {
          return (
            <Text 
              color={trade.is_deleted ? 'red.500' : 'red.500'} 
              fontSize='sm' 
              fontWeight='semibold'
              textDecoration={trade.is_deleted ? 'line-through' : 'none'}
            >
              Satım
            </Text>
          );
        }
        
        // Diğer durumlar
        return (
          <Text 
            color={trade.is_deleted ? 'red.500' : textColor} 
            fontSize='sm' 
            fontWeight='500'
            textDecoration={trade.is_deleted ? 'line-through' : 'none'}
          >
            {category || '-'}
          </Text>
        );
      }
    }),
    columnHelper.accessor(row => row.position_status || '', {
      id: 'position_status',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          DURUM
        </Text>
      ),
      cell: (info) => {
        const status = info.getValue();
        const trade = info.row.original;
        return (
          <Flex 
            align="center"
            opacity={trade.is_deleted ? 0.7 : 1}
          >
            <StatusIcon status={status} />
            <Text 
              ml={2} 
              color={trade.is_deleted ? 'red.500' : textColor} 
              fontSize='sm' 
              fontWeight='500'
              textDecoration={trade.is_deleted ? 'line-through' : 'none'}
            >
              {status || '-'}
            </Text>
          </Flex>
        );
      }
    }),
    columnHelper.accessor('price', {
      id: 'price',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase"
          textAlign="right">
          FİYAT
        </Text>
      ),
      cell: (info: CellContext<Trade, number>) => {
        const trade = info.row.original;
        return (
          <Text 
            color={trade.is_deleted ? 'red.500' : textColor} 
            fontSize='sm' 
            fontWeight='500' 
            textAlign="right"
            textDecoration={trade.is_deleted ? 'line-through' : 'none'}
          >
            {formatNumber(info.getValue())}
          </Text>
        );
      }
    }),
    columnHelper.accessor('calculated_quantity', {
      id: 'calculated_quantity',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase"
          textAlign="right">
          MİKTAR
        </Text>
      ),
      cell: (info: CellContext<Trade, number>) => {
        const trade = info.row.original;
        return (
          <Text 
            color={trade.is_deleted ? 'red.500' : textColor} 
            fontSize='sm' 
            fontWeight='500' 
            textAlign="right"
            textDecoration={trade.is_deleted ? 'line-through' : 'none'}
          >
            {info.getValue() !== null && info.getValue() !== undefined ? 
              Math.floor(info.getValue()).toLocaleString('tr-TR', { 
                minimumFractionDigits: 0, 
                maximumFractionDigits: 0 
              }) : '-'}
          </Text>
        );
      }
    }),
    columnHelper.accessor(row => row.price && row.calculated_quantity ? row.price * row.calculated_quantity : null, {
      id: 'total',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase"
          textAlign="right">
          İŞLEM HACMİ
        </Text>
      ),
      cell: (info: CellContext<Trade, number | null>) => {
        const trade = info.row.original;
        return (
          <Text 
            color={trade.is_deleted ? 'red.500' : textColor} 
            fontSize='sm' 
            fontWeight='500' 
            textAlign="right"
            textDecoration={trade.is_deleted ? 'line-through' : 'none'}
          >
            {formatCurrency(info.getValue())}
          </Text>
        );
      }
    }),
    columnHelper.accessor('pnl', {
      id: 'pnl',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          KAR/ZARAR
        </Text>
      ),
      cell: (info) => {
        const trade = info.row.original;
        
        // İşlem silinmişse özel gösterim
        if (trade.is_deleted) {
          return (
            <Text 
              color="red.500" 
              fontSize='sm' 
              fontWeight='500'
              textDecoration='line-through'
              textAlign="right">
              İptal Edildi
            </Text>
          );
        }
        
        // Satıcı tarafından iptal edilmiş işlem
        if (trade.is_cancelled_by_seller) {
          return (
            <Text 
              color="orange.500" 
              fontSize='sm' 
              fontWeight='500'
              textDecoration='line-through'
              textAlign="right">
              Satıcı İptal
            </Text>
          );
        }
        
        // SELL işlemleri için özel kontrol (ATRTP, KTP, STOP dahil)
        const orderSide = trade.order_side?.toUpperCase() || '';
        const tradeCategory = trade.trade_category?.toLowerCase() || '';
        const signalType = trade.signal_type?.toUpperCase() || '';
        const positionStatus = trade.position_status?.toLowerCase() || '';
        
        const isSellTrade = 
          orderSide === 'SELL' || 
          tradeCategory === 'satım' || 
          signalType === 'SELL' || 
          signalType === 'ATRTP' || 
          signalType === 'KTP' || 
          signalType === 'STOP';
        
        // Özel SELL türleri için "beklemede" durumunda bile "Hesaplanıyor..." göster
        if (isSellTrade && positionStatus === 'beklemede') {
          return (
            <Text fontSize="sm" color="purple.500" fontWeight="500" textAlign="right">
              Hesaplanıyor...
            </Text>
          );
        }
        
        // PnL hücresi için özel render fonksiyonunu kullan
        return renderPnlCell(trade);
      }
    }),
    // İşlemler sütunu
    ActionsColumn({
      onDeleteClick: handleDeleteButtonClick, 
      onCancelSignalBatch: handleCancelSignalClick,
      currentUserId,
      isDeletingTradeId,
      isCancellingSignalId,
      trades: tradesToDisplay
    })
  ], [currentUserId, isDeletingTradeId, isCancellingSignalId, tradesToDisplay]);

  // Tablo konfigürasyonu
  const table = useReactTable({
    data: tradesToDisplay,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    
    // Satır ID oluşturma stratejisini güncelle
    getRowId
  });

  // Tablo yapılandırması sonrası debug
  React.useEffect(() => {
    if (!isLoading && tradesToDisplay.length > 0 && table) {
      console.log("[TradesTable:DEBUG] Tablo ve ilk birkaç satır:", {
        tableRowCount: table.getRowModel().rows.length,
        firstThreeRows: table.getRowModel().rows.slice(0, 3).map(row => ({
          rowId: row.id,
          originalId: row.original.id,
          match: row.id.toString() === row.original.id.toString()
        }))
      });
    }
  }, [isLoading, tradesToDisplay, table]);

  // Tablo yüklendikten sonra satır numaralarını ve ID değerlerini kontrol et
  React.useEffect(() => {
    if (!isLoading && tradesToDisplay.length > 0) {
      console.log('[TradesTable:DEBUG] Tablo satırları ve trade.id değerleri:');
      
      // İlk 5 satırı logla
      const rowsToCheck = Math.min(tradesToDisplay.length, 5);
      for (let i = 0; i < rowsToCheck; i++) {
        const trade = tradesToDisplay[i];
        console.log(`[TradesTable:DEBUG] Satır[${i}]:`, {
          // Tanstack table ID'leri
          rowId: `${i}`, // Tanstack Table genellikle satır dizinlerini string olarak kullanır
          
          // Trade verileri
          tradeId: trade.id,
          tradeIdType: typeof trade.id,
          symbol: trade.symbol,
          
          // UI göster/gizle kontrolleri
          isDeleted: trade.is_deleted
        });
      }
    }
  }, [isLoading, tradesToDisplay]);

  // Skeleton sayısı
  const skeletonRowCount = 5;

  // Mobile Card Component
  const TradeCard = ({ trade }: { trade: Trade }) => {
    const getStatusColor = (status: string | null) => {
      if (!status) return 'gray.500';
      const lowerStatus = status.toLowerCase();
      switch (lowerStatus) {
        case 'açık': return 'green.500';
        case 'kapandı': return 'blue.500';
        case 'bakiye yetersiz': return 'orange.500';
        case 'gönderiliyor': return 'blue.300';
        case 'pozisyon bulunamadı': return 'red.500';
        case 'abone yok': return 'orange.500';
        case 'işleme hatası':
        case 'iletim hatası': return 'red.500';
        default: return 'gray.500';
      }
    };

    const getPnlDisplay = () => {
      if (trade.status === 'cancelled_by_user') {
        return { text: 'Manuel Kapatıldı', color: 'orange.500' };
      }
      
      if (trade.pnl !== null && trade.pnl !== undefined && !isNaN(Number(trade.pnl))) {
        const pnlValue = Number(trade.pnl);
        return {
          text: formatCurrency(pnlValue),
          color: pnlValue > 0 ? 'green.500' : pnlValue < 0 ? 'red.500' : 'gray.500'
        };
      }
      
      const positionStatus = trade.position_status?.toLowerCase() || '';
      if (positionStatus === 'açık') return { text: 'İşlem Açık', color: 'blue.500' };
      if (positionStatus === 'kapandı' || positionStatus === 'kapalı') return { text: 'Hesaplanıyor...', color: 'purple.500' };
      if (positionStatus === 'bakiye yetersiz') return { text: 'Bakiye Yetersiz', color: 'orange.500' };
      if (positionStatus === 'gönderiliyor') return { text: 'İşleniyor...', color: 'blue.300' };
      if (positionStatus === 'pozisyon bulunamadı') return { text: 'Pozisyon Yok', color: 'red.500' };
      if (positionStatus === 'abone yok') return { text: 'Abone Yok', color: 'orange.500' };
      if (positionStatus === 'işleme hatası' || positionStatus === 'iletim hatası') return { text: 'Hata Oluştu', color: 'red.500' };
      
      return { text: '-', color: 'gray.400' };
    };

    const pnlDisplay = getPnlDisplay();

    return (
      <Card
        p={{ base: '16px', md: '20px' }}
        mb="12px"
        bg={cardBg}
        borderRadius="16px"
        boxShadow="0px 8px 24px rgba(112, 144, 176, 0.08)"
        border="1px solid"
        borderColor={borderColor}
        opacity={trade.is_deleted ? 0.7 : 1}
        position="relative"
      >
        {/* Header Row */}
        <Flex justify="space-between" align="flex-start" mb="12px">
          <VStack align="flex-start" spacing="4px" flex="1">
            <HStack spacing="8px">
              <SignalSourceIcon trade={trade} />
              <Text fontSize="lg" fontWeight="700" color={textColor}>
                {trade.symbol}
              </Text>
              <Badge
                colorScheme={trade.order_side?.toUpperCase() === 'BUY' ? 'green' : 'red'}
                fontSize="xs"
                px="8px"
                py="2px"
                borderRadius="6px"
              >
                {trade.order_side?.toUpperCase() === 'BUY' ? 'ALIM' : 'SATIM'}
              </Badge>
            </HStack>
            <Text fontSize="sm" color={secondaryTextColor}>
              {new Date(trade.received_at).toLocaleString('tr-TR')}
            </Text>
          </VStack>
          
          {/* Actions */}
          <HStack spacing="4px">
            {onCancelSignalBatch && !trade.is_deleted && (
              <IconButton
                aria-label="Sinyali iptal et"
                icon={<Icon as={MdCancel} />}
                size="sm"
                variant="ghost"
                colorScheme="orange"
                isLoading={isCancellingSignalId === trade.id}
                onClick={() => handleCancelSignalClick(trade)}
                minW="32px"
                h="32px"
              />
            )}
            {onDeleteClick && (
              <IconButton
                aria-label="İşlemi sil"
                icon={<DeleteIcon />}
                size="sm"
                variant="ghost"
                colorScheme="red"
                isLoading={isDeletingTradeId === trade.id}
                onClick={() => handleDeleteButtonClick(trade.id)}
                minW="32px"
                h="32px"
              />
            )}
          </HStack>
        </Flex>

        {/* Details Grid */}
        <VStack spacing="8px" align="stretch">
          <Flex justify="space-between">
            <Text fontSize="sm" color={secondaryTextColor}>Sistem Adı:</Text>
            <Text fontSize="sm" fontWeight="500" color={textColor}>
              {trade.system_name || '-'}
            </Text>
          </Flex>
          
          <Flex justify="space-between">
            <Text fontSize="sm" color={secondaryTextColor}>Sinyal Tipi:</Text>
            <Text fontSize="sm" fontWeight="500" color={textColor}>
              {getSignalTypeDisplay(trade.signal_type)}
            </Text>
          </Flex>
          
          <Flex justify="space-between">
            <Text fontSize="sm" color={secondaryTextColor}>Fiyat:</Text>
            <Text fontSize="sm" fontWeight="500" color={textColor}>
              {trade.price ? formatNumber(trade.price, 4) : '-'}
            </Text>
          </Flex>
          
          <Flex justify="space-between">
            <Text fontSize="sm" color={secondaryTextColor}>Miktar:</Text>
            <Text fontSize="sm" fontWeight="500" color={textColor}>
              {trade.calculated_quantity ? formatNumber(trade.calculated_quantity, 0) : '-'}
            </Text>
          </Flex>
          
          <Flex justify="space-between">
            <Text fontSize="sm" color={secondaryTextColor}>Toplam:</Text>
            <Text fontSize="sm" fontWeight="500" color={textColor}>
              {(trade.price && trade.calculated_quantity) 
                ? formatCurrency(trade.price * trade.calculated_quantity) 
                : '-'
              }
            </Text>
          </Flex>
          
          <Flex justify="space-between" align="center">
            <Text fontSize="sm" color={secondaryTextColor}>Durum:</Text>
                         <HStack spacing="6px">
               <StatusIcon status={trade.position_status || null} />
               <Text fontSize="sm" fontWeight="500" color={getStatusColor(trade.position_status || null)}>
                 {trade.position_status || 'Bilinmiyor'}
               </Text>
             </HStack>
          </Flex>
          
          <Flex justify="space-between" align="center" pt="4px" borderTop="1px solid" borderColor={borderColor}>
            <Text fontSize="sm" color={secondaryTextColor} fontWeight="600">Kar/Zarar:</Text>
            <Text fontSize="sm" fontWeight="700" color={pnlDisplay.color}>
              {pnlDisplay.text}
            </Text>
          </Flex>
        </VStack>
        
        {trade.is_deleted && (
          <Box
            position="absolute"
            top="0"
            left="0"
            right="0"
            bottom="0"
            bg="blackAlpha.100"
            borderRadius="16px"
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Text color="red.500" fontWeight="700" fontSize="lg">
              İPTAL EDİLDİ
            </Text>
          </Box>
        )}
      </Card>
    );
  };

  // Mobile view için skeleton
  const MobileSkeleton = () => (
    <VStack spacing="12px">
      {Array.from({ length: skeletonRowCount }).map((_, index) => (
        <Card
          key={`mobile-skeleton-${index}`}
          p="16px"
          w="100%"
          bg={cardBg}
          borderRadius="16px"
        >
          <VStack spacing="8px" align="stretch">
            <HStack justify="space-between">
              <Skeleton height="24px" width="120px" />
              <Skeleton height="20px" width="60px" />
            </HStack>
            <Skeleton height="16px" width="100px" />
            <VStack spacing="6px" align="stretch">
              {Array.from({ length: 4 }).map((_, i) => (
                <HStack key={i} justify="space-between">
                  <Skeleton height="14px" width="80px" />
                  <Skeleton height="14px" width="100px" />
                </HStack>
              ))}
            </VStack>
          </VStack>
        </Card>
      ))}
    </VStack>
  );

  return (
    <>
      <Card
        flexDirection='column'
        w='100%'
        p='0px'
        overflowX={{ base: 'visible', lg: 'hidden' }}
        boxShadow={cardShadow}
        mb="20px"
      >
        {isLoading ? (
          isMobile ? (
            <Box p="16px">
              <MobileSkeleton />
            </Box>
          ) : (
            <Box>
              <Table variant='simple' color='gray.500' mb='0px'>
                <Thead>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <Tr key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <Th key={header.id} borderColor={borderColor}>
                          <Skeleton height="20px" />
                        </Th>
                      ))}
                    </Tr>
                  ))}
                </Thead>
                <Tbody>
                  {Array.from({ length: skeletonRowCount }).map((_, index) => (
                    <Tr key={`skeleton-${index}`}>
                      {columns.map((_, cellIndex) => (
                        <Td key={`skeleton-${index}-${cellIndex}`}>
                          <Skeleton height="20px" />
                        </Td>
                      ))}
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </Box>
          )
        ) : isMobile ? (
          // Mobile Card View
          <Box p="16px">
            {tradesToDisplay.length === 0 ? (
              <Text textAlign="center" color={secondaryTextColor} py="40px">
                Gösterilecek işlem bulunamadı.
              </Text>
            ) : (
              <VStack spacing="0">
                {tradesToDisplay.map((trade) => (
                  <TradeCard key={trade.id} trade={trade} />
                ))}
              </VStack>
            )}
          </Box>
        ) : (
          // Desktop Table View
          <Box overflowX="auto">
            <Table variant='simple' color='gray.500' mb='0px' minW="1000px">
              <Thead>
                {table.getHeaderGroups().map((headerGroup) => (
                  <Tr key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <Th
                          key={header.id}
                          colSpan={header.colSpan}
                          borderColor={borderColor}
                          cursor='pointer'
                          onClick={header.column.getToggleSortingHandler()}
                          py={{ base: '12px', md: '14px' }}
                          px={{ base: '12px', md: '16px' }}
                          bg={headerBg}
                          borderBottom="1px solid"
                          borderBottomColor={borderColor}
                          fontSize={{ base: '10px', md: '12px' }}
                          _last={{
                            pr: { base: '16px', md: '24px' }
                          }}
                          _first={{
                            pl: { base: '16px', md: '24px' }
                          }}
                        >
                          <Flex
                            justifyContent={header.id === 'price' || header.id === 'calculated_quantity' || header.id === 'total' || header.id === 'pnl' ? 'flex-end' : 'flex-start'}
                            align='center'
                          >
                            {flexRender(header.column.columnDef.header, header.getContext())}
                            {header.column.getCanSort() && (
                              <Flex ml={1} direction="column" h="16px" w="16px" justify="center" align="center">
                                {header.column.getIsSorted() ? (
                                  header.column.getIsSorted() === 'asc' ? (
                                    <TriangleUpIcon color={sortIconColor} boxSize="10px" />
                                  ) : (
                                    <TriangleDownIcon color={sortIconColor} boxSize="10px" />
                                  )
                                ) : (
                                  <>
                                    <TriangleUpIcon color="gray.400" boxSize="8px" mb="-3px" opacity={0.5} />
                                    <TriangleDownIcon color="gray.400" boxSize="8px" mt="-3px" opacity={0.5} />
                                  </>
                                )}
                              </Flex>
                            )}
                          </Flex>
                        </Th>
                      );
                    })}
                  </Tr>
                ))}
              </Thead>
              <Tbody>
                {table.getRowModel().rows.map((row) => {
                  const trade = row.original;
                  
                  return (
                    <Tr key={row.id} {...getRowStyles(trade)}>
                      {row.getVisibleCells().map((cell) => (
                        <Td 
                          key={cell.id}
                          py={{ base: '12px', md: '16px' }}
                          px={{ base: '12px', md: '16px' }}
                          fontSize={{ base: '12px', md: '14px' }}
                          _last={{
                            pr: { base: '16px', md: '24px' }
                          }}
                          _first={{
                            pl: { base: '16px', md: '24px' }
                          }}
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </Td>
                      ))}
                    </Tr>
                  );
                }).filter(Boolean)
              }
              </Tbody>
            </Table>
          </Box>
        )}
      </Card>

      {/* İşlem silme onay dialog'u */}
      {tradeToDelete && (
        <DeleteTradeDialog
          isOpen={isDeleteDialogOpen}
          onClose={closeDeleteDialog}
          onConfirm={handleConfirmDelete}
          trade={tradeToDelete}
        />
      )}

      {/* Sinyal iptal onay dialog'u */}
      {tradeToCancel && (
        <CancelSignalDialog
          isOpen={isCancelDialogOpen}
          onClose={closeCancelDialog}
          onConfirm={handleConfirmCancel}
          trade={tradeToCancel}
        />
      )}
    </>
  );
};

// TradesTable bileşeninde getRowId fonksiyonunu güncelleyelim
const getRowId = (row: Trade): string => {
  return row.id.toString();
};

export default TradesTable;