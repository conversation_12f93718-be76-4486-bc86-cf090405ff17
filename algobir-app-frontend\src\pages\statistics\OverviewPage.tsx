import React from 'react';
import {
  VStack,
  SimpleGrid,
  Card,
  CardBody,
  Text,

  Box
} from '@chakra-ui/react';
import { useEnhancedStatistics } from '../../hooks/useEnhancedStatistics';
import AdvancedLineChart from '../../components/statistics/charts/AdvancedLineChart';
import InteractivePieChart from '../../components/statistics/charts/InteractivePieChart';

// Metric Card Component
const MetricCard: React.FC<{
  title: string;
  value: string;
  color: string;
  icon: string;
}> = ({ title, value, color, icon }) => {
  return (
    <Card>
      <CardBody>
        <VStack spacing={2}>
          <Text fontSize="2xl">{icon}</Text>
          <Text fontSize="sm" color="gray.500" textAlign="center">
            {title}
          </Text>
          <Text fontSize="2xl" fontWeight="bold" color={`${color}.500`}>
            {value}
          </Text>
        </VStack>
      </CardBody>
    </Card>
  );
};

const OverviewPage: React.FC = () => {
  const { stats, loading, error } = useEnhancedStatistics();

  if (loading) {
    return (
      <Box p={8} textAlign="center">
        <Text>Yükleniyor...</Text>
      </Box>
    );
  }

  if (error || !stats) {
    return (
      <Box p={8} textAlign="center">
        <Text color="red.500">Veri yüklenirken hata oluştu</Text>
      </Box>
    );
  }

  const formatCurrency = (value: number) => `₺${value.toFixed(2)}`;
  const formatPercent = (value: number) => `${value.toFixed(2)}%`;

  // Prepare cumulative P&L data for line chart
  const cumulativePnlData = stats.cumulativePnlData.map((item: any) => ({
    date: item.date,
    value: item.value
  }));

  // Prepare win/loss data for pie chart
  const winLossData = [
    { name: 'Kazanç', value: stats.winningTrades, color: '#38A169' },
    { name: 'Kayıp', value: stats.losingTrades, color: '#E53E3E' }
  ];

  return (
    <VStack spacing={6} align="stretch">
      {/* Key Metrics Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
        <MetricCard
          title="Toplam ROI"
          value={formatPercent(stats.roiAnalysis.totalROI)}
          color={stats.roiAnalysis.totalROI > 0 ? 'green' : 'red'}
          icon="📈"
        />
        <MetricCard
          title="Kazanma Oranı"
          value={formatPercent(stats.winRate)}
          color={stats.winRate > 50 ? 'green' : 'red'}
          icon="🎯"
        />
        <MetricCard
          title="Toplam P&L"
          value={formatCurrency(stats.totalPnl)}
          color={stats.totalPnl > 0 ? 'green' : 'red'}
          icon="💰"
        />
        <MetricCard
          title="Aktif Robot"
          value={stats.realtimeMetrics.activeRobots.toString()}
          color="blue"
          icon="🤖"
        />
      </SimpleGrid>

      {/* Charts */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        <AdvancedLineChart
          data={cumulativePnlData}
          title="Kümülatif Kar/Zarar Trendi"
          dataKey="value"
          formatValue={formatCurrency}
          height={350}
          showBrush={true}
          showMovingAverage={true}
        />

        <InteractivePieChart
          data={winLossData}
          title="Kazanç/Kayıp Dağılımı"
          height={350}
          formatValue={(value) => `${value} işlem`}
        />
      </SimpleGrid>
    </VStack>
  );
};

export default OverviewPage;
