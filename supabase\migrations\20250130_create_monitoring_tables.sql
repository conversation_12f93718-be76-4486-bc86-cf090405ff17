-- Phase 3.3: Monitoring & Alerting Systems
-- Database schema for monitoring data collection and alerting

-- Performance metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    metric_name TEXT NOT NULL,
    metric_value NUMERIC NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    url TEXT NOT NULL,
    tags JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance metrics
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_session ON performance_metrics(session_id);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_name ON performance_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_created_at ON performance_metrics(created_at);

-- Error reports table
CREATE TABLE IF NOT EXISTS error_reports (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    message TEXT NOT NULL,
    stack TEXT,
    timestamp TIMESTAMPTZ NOT NULL,
    url TEXT NOT NULL,
    user_agent TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    context JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for error reports
CREATE INDEX IF NOT EXISTS idx_error_reports_timestamp ON error_reports(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_error_reports_session ON error_reports(session_id);
CREATE INDEX IF NOT EXISTS idx_error_reports_severity ON error_reports(severity);
CREATE INDEX IF NOT EXISTS idx_error_reports_user ON error_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_error_reports_created_at ON error_reports(created_at);
CREATE INDEX IF NOT EXISTS idx_error_reports_message ON error_reports USING gin(to_tsvector('english', message));

-- Business metrics table
CREATE TABLE IF NOT EXISTS business_metrics (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    event_name TEXT NOT NULL,
    event_value NUMERIC NOT NULL DEFAULT 1,
    timestamp TIMESTAMPTZ NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    properties JSONB DEFAULT '{}',
    url TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for business metrics
CREATE INDEX IF NOT EXISTS idx_business_metrics_timestamp ON business_metrics(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_business_metrics_session ON business_metrics(session_id);
CREATE INDEX IF NOT EXISTS idx_business_metrics_event ON business_metrics(event_name);
CREATE INDEX IF NOT EXISTS idx_business_metrics_user ON business_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_business_metrics_created_at ON business_metrics(created_at);

-- Monitoring alerts table
CREATE TABLE IF NOT EXISTS monitoring_alerts (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL CHECK (type IN ('performance', 'error', 'business', 'system')),
    severity TEXT NOT NULL CHECK (severity IN ('info', 'warning', 'critical')),
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    timestamp TIMESTAMPTZ NOT NULL,
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    acknowledged_at TIMESTAMPTZ,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for monitoring alerts
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_timestamp ON monitoring_alerts(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_type ON monitoring_alerts(type);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_severity ON monitoring_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_acknowledged ON monitoring_alerts(acknowledged);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_resolved ON monitoring_alerts(resolved);
CREATE INDEX IF NOT EXISTS idx_monitoring_alerts_created_at ON monitoring_alerts(created_at);

-- Alert rules table for configurable alerting
CREATE TABLE IF NOT EXISTS alert_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL CHECK (type IN ('performance', 'error', 'business')),
    metric_name TEXT NOT NULL,
    condition TEXT NOT NULL CHECK (condition IN ('>', '<', '>=', '<=', '=', '!=')),
    threshold NUMERIC NOT NULL,
    time_window_minutes INTEGER NOT NULL DEFAULT 5,
    severity TEXT NOT NULL CHECK (severity IN ('info', 'warning', 'critical')),
    enabled BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for alert rules
CREATE INDEX IF NOT EXISTS idx_alert_rules_enabled ON alert_rules(enabled);
CREATE INDEX IF NOT EXISTS idx_alert_rules_type ON alert_rules(type);
CREATE INDEX IF NOT EXISTS idx_alert_rules_metric ON alert_rules(metric_name);

-- System health metrics table
CREATE TABLE IF NOT EXISTS system_health_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_name TEXT NOT NULL,
    metric_value NUMERIC NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL,
    source TEXT NOT NULL, -- 'edge_function', 'database', 'frontend', etc.
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for system health metrics
CREATE INDEX IF NOT EXISTS idx_system_health_timestamp ON system_health_metrics(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_system_health_source ON system_health_metrics(source);
CREATE INDEX IF NOT EXISTS idx_system_health_metric ON system_health_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_system_health_created_at ON system_health_metrics(created_at);

-- Row Level Security (RLS) policies

-- Performance metrics - Allow authenticated users to read their own data
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read performance metrics" ON performance_metrics
    FOR SELECT USING (true); -- Allow all authenticated users to read performance data

CREATE POLICY "Service role can insert performance metrics" ON performance_metrics
    FOR INSERT WITH CHECK (true); -- Allow service role to insert

-- Error reports - Allow authenticated users to read, service role to insert
ALTER TABLE error_reports ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read error reports" ON error_reports
    FOR SELECT USING (true); -- Allow all authenticated users to read error data

CREATE POLICY "Service role can insert error reports" ON error_reports
    FOR INSERT WITH CHECK (true); -- Allow service role to insert

-- Business metrics - Allow users to read their own data
ALTER TABLE business_metrics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read business metrics" ON business_metrics
    FOR SELECT USING (true); -- Allow all authenticated users to read business data

CREATE POLICY "Service role can insert business metrics" ON business_metrics
    FOR INSERT WITH CHECK (true); -- Allow service role to insert

-- Monitoring alerts - Allow authenticated users to read and acknowledge
ALTER TABLE monitoring_alerts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read monitoring alerts" ON monitoring_alerts
    FOR SELECT USING (true); -- Allow all authenticated users to read alerts

CREATE POLICY "Users can acknowledge alerts" ON monitoring_alerts
    FOR UPDATE USING (auth.uid() IS NOT NULL)
    WITH CHECK (
        acknowledged_by = auth.uid() OR 
        (acknowledged = false AND acknowledged_by IS NULL)
    );

CREATE POLICY "Service role can manage alerts" ON monitoring_alerts
    FOR ALL WITH CHECK (true); -- Allow service role full access

-- Alert rules - Allow authenticated users to manage their own rules
ALTER TABLE alert_rules ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read alert rules" ON alert_rules
    FOR SELECT USING (true); -- Allow all authenticated users to read rules

CREATE POLICY "Users can manage their own alert rules" ON alert_rules
    FOR ALL USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- System health metrics - Read-only for authenticated users
ALTER TABLE system_health_metrics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read system health metrics" ON system_health_metrics
    FOR SELECT USING (true); -- Allow all authenticated users to read system health

CREATE POLICY "Service role can insert system health metrics" ON system_health_metrics
    FOR INSERT WITH CHECK (true); -- Allow service role to insert

-- Functions for data aggregation and cleanup

-- Function to get performance metrics summary
CREATE OR REPLACE FUNCTION get_performance_summary(
    time_range_hours INTEGER DEFAULT 24
)
RETURNS TABLE (
    metric_name TEXT,
    avg_value NUMERIC,
    min_value NUMERIC,
    max_value NUMERIC,
    p95_value NUMERIC,
    count_value BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pm.metric_name,
        AVG(pm.metric_value) as avg_value,
        MIN(pm.metric_value) as min_value,
        MAX(pm.metric_value) as max_value,
        PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY pm.metric_value) as p95_value,
        COUNT(*) as count_value
    FROM performance_metrics pm
    WHERE pm.timestamp >= NOW() - (time_range_hours || ' hours')::INTERVAL
    GROUP BY pm.metric_name
    ORDER BY pm.metric_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get error summary
CREATE OR REPLACE FUNCTION get_error_summary(
    time_range_hours INTEGER DEFAULT 24
)
RETURNS TABLE (
    severity TEXT,
    count_value BIGINT,
    latest_timestamp TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        er.severity,
        COUNT(*) as count_value,
        MAX(er.timestamp) as latest_timestamp
    FROM error_reports er
    WHERE er.timestamp >= NOW() - (time_range_hours || ' hours')::INTERVAL
    GROUP BY er.severity
    ORDER BY 
        CASE er.severity
            WHEN 'critical' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
        END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup old monitoring data
CREATE OR REPLACE FUNCTION cleanup_monitoring_data(
    retention_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    table_name TEXT,
    deleted_count BIGINT
) AS $$
DECLARE
    cutoff_date TIMESTAMPTZ;
    perf_deleted BIGINT;
    error_deleted BIGINT;
    business_deleted BIGINT;
    alert_deleted BIGINT;
    health_deleted BIGINT;
BEGIN
    cutoff_date := NOW() - (retention_days || ' days')::INTERVAL;
    
    -- Delete old performance metrics
    DELETE FROM performance_metrics WHERE created_at < cutoff_date;
    GET DIAGNOSTICS perf_deleted = ROW_COUNT;
    
    -- Delete old error reports
    DELETE FROM error_reports WHERE created_at < cutoff_date;
    GET DIAGNOSTICS error_deleted = ROW_COUNT;
    
    -- Delete old business metrics
    DELETE FROM business_metrics WHERE created_at < cutoff_date;
    GET DIAGNOSTICS business_deleted = ROW_COUNT;
    
    -- Delete old resolved alerts
    DELETE FROM monitoring_alerts WHERE created_at < cutoff_date AND resolved = true;
    GET DIAGNOSTICS alert_deleted = ROW_COUNT;
    
    -- Delete old system health metrics
    DELETE FROM system_health_metrics WHERE created_at < cutoff_date;
    GET DIAGNOSTICS health_deleted = ROW_COUNT;
    
    -- Return summary
    RETURN QUERY VALUES 
        ('performance_metrics', perf_deleted),
        ('error_reports', error_deleted),
        ('business_metrics', business_deleted),
        ('monitoring_alerts', alert_deleted),
        ('system_health_metrics', health_deleted);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to trigger alert based on rules
CREATE OR REPLACE FUNCTION check_alert_rules()
RETURNS INTEGER AS $$
DECLARE
    rule_record RECORD;
    metric_value NUMERIC;
    alert_triggered BOOLEAN;
    alerts_created INTEGER := 0;
BEGIN
    -- Loop through all enabled alert rules
    FOR rule_record IN 
        SELECT * FROM alert_rules WHERE enabled = true
    LOOP
        alert_triggered := false;
        
        -- Get recent metric value based on rule type
        IF rule_record.type = 'performance' THEN
            SELECT AVG(metric_value) INTO metric_value
            FROM performance_metrics 
            WHERE metric_name = rule_record.metric_name
            AND timestamp >= NOW() - (rule_record.time_window_minutes || ' minutes')::INTERVAL;
        ELSIF rule_record.type = 'error' THEN
            SELECT COUNT(*) INTO metric_value
            FROM error_reports 
            WHERE timestamp >= NOW() - (rule_record.time_window_minutes || ' minutes')::INTERVAL;
        ELSIF rule_record.type = 'business' THEN
            SELECT AVG(event_value) INTO metric_value
            FROM business_metrics 
            WHERE event_name = rule_record.metric_name
            AND timestamp >= NOW() - (rule_record.time_window_minutes || ' minutes')::INTERVAL;
        END IF;
        
        -- Check if alert condition is met
        IF metric_value IS NOT NULL THEN
            CASE rule_record.condition
                WHEN '>' THEN alert_triggered := metric_value > rule_record.threshold;
                WHEN '<' THEN alert_triggered := metric_value < rule_record.threshold;
                WHEN '>=' THEN alert_triggered := metric_value >= rule_record.threshold;
                WHEN '<=' THEN alert_triggered := metric_value <= rule_record.threshold;
                WHEN '=' THEN alert_triggered := metric_value = rule_record.threshold;
                WHEN '!=' THEN alert_triggered := metric_value != rule_record.threshold;
            END CASE;
            
            -- Create alert if triggered
            IF alert_triggered THEN
                INSERT INTO monitoring_alerts (
                    id,
                    type,
                    severity,
                    message,
                    data,
                    timestamp
                ) VALUES (
                    'alert_' || NOW()::TEXT || '_' || substr(md5(random()::text), 1, 8),
                    rule_record.type,
                    rule_record.severity,
                    rule_record.name || ': ' || rule_record.metric_name || ' ' || rule_record.condition || ' ' || rule_record.threshold || ' (current: ' || metric_value || ')',
                    jsonb_build_object(
                        'rule_id', rule_record.id,
                        'metric_name', rule_record.metric_name,
                        'current_value', metric_value,
                        'threshold', rule_record.threshold,
                        'condition', rule_record.condition
                    ),
                    NOW()
                );
                
                alerts_created := alerts_created + 1;
            END IF;
        END IF;
    END LOOP;
    
    RETURN alerts_created;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to update the updated_at timestamp for alert_rules
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_alert_rules_updated_at
    BEFORE UPDATE ON alert_rules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default alert rules
INSERT INTO alert_rules (name, description, type, metric_name, condition, threshold, time_window_minutes, severity) VALUES
('High Response Time', 'Alert when average response time exceeds 3 seconds', 'performance', 'response_time', '>', 3000, 5, 'warning'),
('Poor LCP', 'Alert when Largest Contentful Paint exceeds 2.5 seconds', 'performance', 'web_vitals_lcp', '>', 2500, 10, 'warning'),
('Poor FID', 'Alert when First Input Delay exceeds 100ms', 'performance', 'web_vitals_fid', '>', 100, 10, 'warning'),
('Poor CLS', 'Alert when Cumulative Layout Shift exceeds 0.1', 'performance', 'web_vitals_cls', '>', 0.1, 10, 'warning'),
('High Error Rate', 'Alert when error count exceeds 10 in 5 minutes', 'error', 'error_count', '>', 10, 5, 'critical'),
('Critical Errors', 'Alert on any critical error', 'error', 'critical_error', '>', 0, 1, 'critical')
ON CONFLICT DO NOTHING;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
