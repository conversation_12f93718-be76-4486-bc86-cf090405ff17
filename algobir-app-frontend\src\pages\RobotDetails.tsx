import {
  Box, <PERSON>ing, <PERSON>, Spinner, Alert, AlertIcon,
  Card, CardHeader, CardBody, Image, Badge, Button,
  Flex, VStack, HStack, Stat, StatLabel, StatNumber,
  useColorModeValue, Avatar, Icon, SimpleGrid,
  Table, Thead, Tbody, Tr, Th, Td, TableContainer
} from '@chakra-ui/react';
import { 
  FaMoneyBillWave, FaPercentage, FaChartLine, FaUser,
  FaArrowLeft, FaChartArea, FaClock
} from 'react-icons/fa';
import { 
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip,
  ResponsiveContainer
} from 'recharts';
import { useParams, useNavigate } from 'react-router-dom';
import { useRobotDetails } from '../hooks/useRobotDetails';

const formatCurrency = (value: number): string => {
  if (value === null || value === undefined || isNaN(Number(value))) {
    return '₺0,00';
  }
  return new Intl.NumberFormat('tr-TR', { 
    style: 'currency', 
    currency: 'TRY',
    minimumFractionDigits: 2 
  }).format(Number(value));
};

const formatPercent = (value: number): string => {
  if (value === null || value === undefined || isNaN(Number(value))) {
    return '0,00%';
  }
  return `${Number(value).toFixed(2)}%`;
};

const RobotDetails = () => {
  const { robotId } = useParams<{ robotId: string }>();
  const navigate = useNavigate();
  const { 
    robotDetails, 
    weeklyStatistics, 
    isLoading, 
    error
  } = useRobotDetails(robotId || '');

  // Horizon UI tema renkleri
  const textColor = useColorModeValue('navy.700', 'white');
  const textColorSecondary = useColorModeValue('secondaryGray.600', 'secondaryGray.400');
  const brandColor = useColorModeValue('brand.500', 'brand.400');
  const cardBg = useColorModeValue('white', 'navy.800');
  const cardShadow = useColorModeValue('0px 18px 40px rgba(112, 144, 176, 0.12)', 'none');
  const iconBoxBg = useColorModeValue('secondaryGray.300', 'whiteAlpha.100');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');

  if (isLoading) {
    return (
      <Box pt={{ base: '130px', md: '80px', xl: '80px' }} textAlign="center" py={10}>
        <Spinner thickness="4px" speed="0.65s" emptyColor="gray.200" color={brandColor} size="xl" />
        <Text mt={4} fontSize="md" color={textColorSecondary}>Robot detayları yükleniyor...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Box pt={{ base: '130px', md: '80px', xl: '80px' }}>
        <Alert status="error" borderRadius="16px">
          <AlertIcon />
          {error}
        </Alert>
        <Button mt={4} onClick={() => navigate('/marketplace')} leftIcon={<Icon as={FaArrowLeft} />}>
          Marketplace'e Dön
        </Button>
      </Box>
    );
  }

  if (!robotDetails) {
    return (
      <Box pt={{ base: '130px', md: '80px', xl: '80px' }} textAlign="center">
        <Text fontSize="lg" color={textColorSecondary}>Robot bulunamadı</Text>
        <Button mt={4} onClick={() => navigate('/marketplace')} leftIcon={<Icon as={FaArrowLeft} />}>
          Marketplace'e Dön
        </Button>
      </Box>
    );
  }

  return (
    <Box pt={{ base: '130px', md: '80px', xl: '80px' }}>
      <VStack spacing={6} align="stretch">
        {/* Başlık ve Geri Dön Butonu */}
        <Flex justify="space-between" align="center">
          <Button onClick={() => navigate('/marketplace')} leftIcon={<Icon as={FaArrowLeft} />} variant="ghost">
            Marketplace'e Dön
          </Button>
        </Flex>

        {/* Robot Temel Bilgileri */}
        <Card bg={cardBg} boxShadow={cardShadow} borderRadius="20px">
          <CardHeader>
            <Flex direction={{ base: 'column', md: 'row' }} align="start" gap={6}>
              {/* Robot Resmi */}
              <Box flexShrink={0}>
                {robotDetails.image_url ? (
                  <Image 
                    src={robotDetails.image_url} 
                    alt={robotDetails.name}
                    w="150px" 
                    h="150px" 
                    objectFit="cover" 
                    borderRadius="16px"
                    border="2px solid"
                    borderColor={borderColor}
                  />
                ) : (
                  <Box 
                    w="150px" 
                    h="150px" 
                    bg={iconBoxBg} 
                    borderRadius="16px"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    border="2px solid"
                    borderColor={borderColor}
                  >
                    <Icon as={FaChartLine} color={brandColor} boxSize={12} />
                  </Box>
                )}
              </Box>

              {/* Robot Detayları */}
              <VStack align="start" flex={1} spacing={4}>
                <VStack align="start" spacing={2}>
                  <Heading size="lg" color={textColor} fontWeight="700">
                    {robotDetails.name}
                  </Heading>
                  <HStack>
                    <Badge colorScheme="green" fontSize="sm">v{robotDetails.version}</Badge>
                    {robotDetails.strategy_type && (
                      <Badge colorScheme="blue" fontSize="sm">{robotDetails.strategy_type}</Badge>
                    )}
                    <Badge colorScheme="purple" fontSize="sm">
                      {formatCurrency(robotDetails.price)}/{robotDetails.subscription_period} gün
                    </Badge>
                  </HStack>
                  <Text fontSize="md" color={textColorSecondary} maxW="600px">
                    {robotDetails.description}
                  </Text>
                </VStack>

                {/* Satıcı Bilgileri */}
                <Box p={4} borderRadius="12px" border="1px solid" borderColor={borderColor} bg={cardBg}>
                  <HStack spacing={3}>
                    <Avatar 
                      src={robotDetails.seller.avatar_url} 
                      name={robotDetails.seller.username}
                      size="md"
                    />
                    <VStack align="start" spacing={1}>
                      <Text fontWeight="600" color={textColor}>
                        {robotDetails.seller.username}
                      </Text>
                      {robotDetails.seller.full_name && (
                        <Text fontSize="sm" color={textColorSecondary}>
                          {robotDetails.seller.full_name}
                        </Text>
                      )}
                      {robotDetails.seller.bio && (
                        <Text fontSize="sm" color={textColorSecondary}>
                          {robotDetails.seller.bio}
                        </Text>
                      )}
                    </VStack>
                  </HStack>
                </Box>

                {/* Robot Ayarları */}
                <SimpleGrid columns={{ base: 1, md: 3 }} gap={4} w="full">
                  <Box p={3} borderRadius="8px" bg={iconBoxBg}>
                    <Text fontSize="xs" color={textColorSecondary} mb={1}>Pozisyon Başına Yatırım</Text>
                    <Text fontWeight="600" color={textColor}>
                      {formatCurrency(robotDetails.investment_amount_per_position)}
                    </Text>
                  </Box>
                  <Box p={3} borderRadius="8px" bg={iconBoxBg}>
                    <Text fontSize="xs" color={textColorSecondary} mb={1}>Abonelik Periyodu</Text>
                    <Text fontWeight="600" color={textColor}>
                      {robotDetails.subscription_period} gün
                    </Text>
                  </Box>
                  <Box p={3} borderRadius="8px" bg={iconBoxBg}>
                    <Text fontSize="xs" color={textColorSecondary} mb={1}>Oluşturulma Tarihi</Text>
                    <Text fontWeight="600" color={textColor}>
                      {new Date(robotDetails.created_at).toLocaleDateString('tr-TR')}
                    </Text>
                  </Box>
                </SimpleGrid>
              </VStack>
            </Flex>
          </CardHeader>
        </Card>

        {/* İstatistikler Bölümü */}
        {weeklyStatistics && weeklyStatistics.show_statistics ? (
          <VStack spacing={6} align="stretch">
            {/* Özet İstatistikler */}
            {weeklyStatistics.summary && (
              <Card bg={cardBg} boxShadow={cardShadow} borderRadius="20px">
                <CardHeader>
                  <HStack>
                    <Icon as={FaChartArea} color={brandColor} />
                    <Heading size="md" color={textColor} fontWeight="700">
                      Son 7 Günlük Performans Özeti
                    </Heading>
                    <Badge colorScheme="green">
                      {formatCurrency(weeklyStatistics.simulated_investment_per_trade || 10000)} / işlem
                    </Badge>
                  </HStack>
                </CardHeader>
                <CardBody>
                  <SimpleGrid columns={{ base: 1, md: 2, lg: 5 }} gap="20px">
                    <Box p="20px" borderRadius="16px" border="1px solid" borderColor={borderColor} bg={cardBg}>
                      <Flex w="56px" h="56px" bg={iconBoxBg} borderRadius="50%" align="center" justify="center" mb={3}>
                        <Icon as={FaChartLine} color={brandColor} boxSize={6} />
                      </Flex>
                      <Stat>
                        <StatLabel color={textColorSecondary} fontSize="sm">Toplam İşlem</StatLabel>
                        <StatNumber color={textColor} fontSize="xl" fontWeight="700">
                          {weeklyStatistics.summary.total_trades}
                        </StatNumber>
                      </Stat>
                    </Box>

                    <Box p="20px" borderRadius="16px" border="1px solid" borderColor={borderColor} bg={cardBg}>
                      <Flex w="56px" h="56px" bg={iconBoxBg} borderRadius="50%" align="center" justify="center" mb={3}>
                        <Icon as={FaMoneyBillWave} color={brandColor} boxSize={6} />
                      </Flex>
                      <Stat>
                        <StatLabel color={textColorSecondary} fontSize="sm">Toplam P&L</StatLabel>
                        <StatNumber 
                          color={weeklyStatistics.summary.total_pnl >= 0 ? 'green.500' : 'red.500'} 
                          fontSize="xl" 
                          fontWeight="700"
                        >
                          {formatCurrency(weeklyStatistics.summary.total_pnl)}
                        </StatNumber>
                      </Stat>
                    </Box>

                    <Box p="20px" borderRadius="16px" border="1px solid" borderColor={borderColor} bg={cardBg}>
                      <Flex w="56px" h="56px" bg={iconBoxBg} borderRadius="50%" align="center" justify="center" mb={3}>
                        <Icon as={FaPercentage} color={brandColor} boxSize={6} />
                      </Flex>
                      <Stat>
                        <StatLabel color={textColorSecondary} fontSize="sm">Kazanç Oranı</StatLabel>
                        <StatNumber 
                          color={weeklyStatistics.summary.overall_win_rate >= 50 ? 'green.500' : 'red.500'} 
                          fontSize="xl" 
                          fontWeight="700"
                        >
                          {formatPercent(weeklyStatistics.summary.overall_win_rate)}
                        </StatNumber>
                      </Stat>
                    </Box>

                    <Box p="20px" borderRadius="16px" border="1px solid" borderColor={borderColor} bg={cardBg}>
                      <Flex w="56px" h="56px" bg={iconBoxBg} borderRadius="50%" align="center" justify="center" mb={3}>
                        <Icon as={FaChartArea} color={brandColor} boxSize={6} />
                      </Flex>
                      <Stat>
                        <StatLabel color={textColorSecondary} fontSize="sm">ROI</StatLabel>
                        <StatNumber 
                          color={weeklyStatistics.summary.roi_percentage >= 0 ? 'green.500' : 'red.500'} 
                          fontSize="xl" 
                          fontWeight="700"
                        >
                          {formatPercent(weeklyStatistics.summary.roi_percentage)}
                        </StatNumber>
                      </Stat>
                    </Box>

                    <Box p="20px" borderRadius="16px" border="1px solid" borderColor={borderColor} bg={cardBg}>
                      <Flex w="56px" h="56px" bg={iconBoxBg} borderRadius="50%" align="center" justify="center" mb={3}>
                        <Icon as={FaClock} color={brandColor} boxSize={6} />
                      </Flex>
                      <Stat>
                        <StatLabel color={textColorSecondary} fontSize="sm">Toplam Yatırım</StatLabel>
                        <StatNumber color={textColor} fontSize="xl" fontWeight="700">
                          {formatCurrency(weeklyStatistics.summary.total_investment)}
                        </StatNumber>
                      </Stat>
                    </Box>
                  </SimpleGrid>
                </CardBody>
              </Card>
            )}

            {/* Günlük Performans Grafiği */}
            {weeklyStatistics.daily_breakdown && weeklyStatistics.daily_breakdown.length > 0 && (
              <Card bg={cardBg} boxShadow={cardShadow} borderRadius="20px">
                <CardHeader>
                  <Heading size="md" color={textColor} fontWeight="700">
                    Günlük P&L Performansı
                  </Heading>
                </CardHeader>
                <CardBody>
                  <Box height="350px">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={weeklyStatistics.daily_breakdown}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="date" 
                          tickFormatter={(value) => new Date(value).toLocaleDateString('tr-TR')}
                        />
                        <YAxis tickFormatter={(value) => formatCurrency(value)} />
                        <Tooltip 
                          labelFormatter={(value) => new Date(value).toLocaleDateString('tr-TR')}
                          formatter={(value: number, name: string) => {
                            switch(name) {
                              case 'cumulative_pnl': return [formatCurrency(value), 'Kümülatif P&L'];
                              case 'daily_pnl': return [formatCurrency(value), 'Günlük P&L'];
                              default: return [value, name];
                            }
                          }}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="cumulative_pnl" 
                          stroke="#4F46E5" 
                          strokeWidth={3}
                          dot={{ fill: '#4F46E5', r: 4 }}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="daily_pnl" 
                          stroke="#06B6D4" 
                          strokeWidth={2}
                          strokeDasharray="5 5"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </CardBody>
              </Card>
            )}

            {/* Günlük Detay Tablosu */}
            {weeklyStatistics.daily_breakdown && weeklyStatistics.daily_breakdown.length > 0 && (
              <Card bg={cardBg} boxShadow={cardShadow} borderRadius="20px">
                <CardHeader>
                  <Heading size="md" color={textColor} fontWeight="700">
                    Günlük Detay Breakdown
                  </Heading>
                </CardHeader>
                <CardBody>
                  <TableContainer>
                    <Table variant="simple" size="sm">
                      <Thead>
                        <Tr>
                          <Th color={textColorSecondary}>Tarih</Th>
                          <Th color={textColorSecondary} isNumeric>İşlem Sayısı</Th>
                          <Th color={textColorSecondary} isNumeric>Günlük P&L</Th>
                          <Th color={textColorSecondary} isNumeric>Kümülatif P&L</Th>
                          <Th color={textColorSecondary} isNumeric>Kazanç Oranı</Th>
                          <Th color={textColorSecondary} isNumeric>Yatırım</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {weeklyStatistics.daily_breakdown.map((day, index) => (
                          <Tr key={index}>
                            <Td fontWeight="600">
                              {new Date(day.date).toLocaleDateString('tr-TR')}
                            </Td>
                            <Td isNumeric>
                              <Badge colorScheme="blue">{day.trade_count}</Badge>
                            </Td>
                            <Td isNumeric>
                              <Text 
                                fontWeight="600" 
                                color={day.daily_pnl >= 0 ? 'green.500' : 'red.500'}
                              >
                                {formatCurrency(day.daily_pnl)}
                              </Text>
                            </Td>
                            <Td isNumeric>
                              <Text 
                                fontWeight="600" 
                                color={day.cumulative_pnl >= 0 ? 'green.500' : 'red.500'}
                              >
                                {formatCurrency(day.cumulative_pnl)}
                              </Text>
                            </Td>
                            <Td isNumeric>
                              <Text 
                                fontWeight="600" 
                                color={day.daily_win_rate >= 50 ? 'green.500' : 'red.500'}
                              >
                                {formatPercent(day.daily_win_rate)}
                              </Text>
                            </Td>
                            <Td isNumeric>
                              <Text fontWeight="500">
                                {formatCurrency(day.daily_investment)}
                              </Text>
                            </Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </TableContainer>
                </CardBody>
              </Card>
            )}
          </VStack>
        ) : (
          // İstatistikler kapalıysa
          <Card bg={cardBg} boxShadow={cardShadow} borderRadius="20px">
            <CardBody textAlign="center" py={10}>
              <Icon as={FaUser} color={textColorSecondary} boxSize={12} mb={4} />
              <Text fontSize="lg" color={textColorSecondary}>
                {weeklyStatistics?.message || 'Robot sahibi istatistiklerin gösterilmesine izin vermemiş'}
              </Text>
            </CardBody>
          </Card>
        )}
      </VStack>
    </Box>
  );
};

export default RobotDetails; 