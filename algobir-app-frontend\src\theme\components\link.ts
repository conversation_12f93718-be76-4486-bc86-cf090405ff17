import { mode } from '@chakra-ui/theme-tools';

export const LinkComponent = {
  components: {
    Link: {
      baseStyle: (props: any) => ({
        color: mode('brand.500', 'brand.400')(props),
        fontWeight: '500',
        transition: 'all .25s ease',
        _hover: {
          textDecoration: 'none',
          color: mode('brand.600', 'brand.300')(props),
        },
        _focus: {
          boxShadow: 'none',
        },
      }),
    },
  },
}; 