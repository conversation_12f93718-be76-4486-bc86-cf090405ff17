// Enhanced Symbol Analysis Data Structures
// This file defines comprehensive interfaces for advanced symbol analysis functionality

export interface SymbolMetrics {
  symbol: string;
  
  // Basic Performance Metrics
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number; // Percentage
  
  // Financial Metrics
  totalPnl: number;
  totalInvestment: number;
  roi: number; // Percentage
  averageTradeSize: number;
  averagePnl: number;
  
  // Risk Metrics
  volatility: number;
  sharpeRatio: number;
  maxDrawdown: number;
  maxDrawdownPercent: number;
  
  // Trade Quality Metrics
  bestTrade: number;
  worstTrade: number;
  averageWin: number;
  averageLoss: number;
  profitFactor: number;
  
  // Consistency Metrics
  consecutiveWins: number;
  consecutiveLosses: number;
  winStreak: number;
  lossStreak: number;
  
  // Time-based Metrics
  averageHoldingTime: number; // in hours
  firstTradeDate: string;
  lastTradeDate: string;
  tradingDays: number;
  
  // Frequency Metrics
  tradesPerDay: number;
  tradesPerWeek: number;
  tradesPerMonth: number;
}

export interface SymbolTimePerformance {
  symbol: string;
  
  // Hourly Performance (0-23)
  hourlyPerformance: {
    hour: number;
    pnl: number;
    trades: number;
    winRate: number;
    avgTradeSize: number;
  }[];
  
  // Daily Performance (0-6, Sunday-Saturday)
  dailyPerformance: {
    dayOfWeek: number;
    dayName: string;
    pnl: number;
    trades: number;
    winRate: number;
    avgTradeSize: number;
  }[];
  
  // Monthly Performance
  monthlyPerformance: {
    month: string; // YYYY-MM format
    pnl: number;
    trades: number;
    winRate: number;
    roi: number;
    investment: number;
  }[];
  
  // Weekly Performance
  weeklyPerformance: {
    week: string; // YYYY-WW format
    pnl: number;
    trades: number;
    winRate: number;
    roi: number;
  }[];
}

export interface SymbolTradingFrequency {
  symbol: string;
  
  // Time-based frequency analysis
  dailyFrequency: {
    date: string; // YYYY-MM-DD
    tradeCount: number;
    volume: number;
    pnl: number;
  }[];
  
  weeklyFrequency: {
    week: string; // YYYY-WW
    tradeCount: number;
    volume: number;
    pnl: number;
  }[];
  
  monthlyFrequency: {
    month: string; // YYYY-MM
    tradeCount: number;
    volume: number;
    pnl: number;
  }[];
  
  // Frequency statistics
  avgTradesPerDay: number;
  maxTradesPerDay: number;
  minTradesPerDay: number;
  tradingDaysCount: number;
  totalTradingDays: number;
  tradingFrequencyPercent: number; // (tradingDays / totalDays) * 100
}

export interface SymbolCorrelation {
  symbol1: string;
  symbol2: string;
  correlation: number; // -1 to 1
  pValueSignificance: number;
  tradeCountOverlap: number;
  timeOverlapPercent: number;
}

export interface SymbolRiskReturn {
  symbol: string;
  expectedReturn: number; // Annualized
  volatility: number; // Annualized
  sharpeRatio: number;
  sortino: number;
  maxDrawdown: number;
  valueAtRisk95: number;
  valueAtRisk99: number;
  beta: number; // vs portfolio
  alpha: number; // vs portfolio
  informationRatio: number;
  trackingError: number;
}

export interface SymbolBenchmarkComparison {
  symbol: string;
  benchmarkName: string;
  symbolReturn: number;
  benchmarkReturn: number;
  outperformance: number;
  correlation: number;
  beta: number;
  alpha: number;
  informationRatio: number;
  trackingError: number;
}

export interface SymbolAlert {
  id: string;
  symbol: string;
  alertType: 'performance' | 'risk' | 'volume' | 'pattern';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  value: number;
  threshold: number;
  createdAt: string;
  isActive: boolean;
}

export interface SymbolInsight {
  id: string;
  symbol: string;
  insightType: 'opportunity' | 'warning' | 'trend' | 'anomaly';
  title: string;
  description: string;
  confidence: number; // 0-100
  actionRecommendation?: string;
  supportingData: any;
  createdAt: string;
}

export interface SymbolAnalysisFilters {
  symbols: string[];
  dateRange: {
    start: string;
    end: string;
  };
  performanceRange: {
    minPnl?: number;
    maxPnl?: number;
    minROI?: number;
    maxROI?: number;
  };
  tradeCountRange: {
    min?: number;
    max?: number;
  };
  riskLevel: 'all' | 'low' | 'medium' | 'high';
  sortBy: 'pnl' | 'roi' | 'winRate' | 'trades' | 'volatility' | 'sharpe';
  sortOrder: 'asc' | 'desc';
  showOnlyProfitable: boolean;
  minTradeCount: number;
}

export interface EnhancedSymbolAnalysisData {
  // Core symbol metrics
  symbolMetrics: SymbolMetrics[];
  
  // Time-based analysis
  symbolTimePerformance: SymbolTimePerformance[];
  
  // Trading frequency analysis
  symbolTradingFrequency: SymbolTradingFrequency[];
  
  // Risk-return analysis
  symbolRiskReturn: SymbolRiskReturn[];
  
  // Correlation analysis
  correlationMatrix: SymbolCorrelation[];
  
  // Benchmark comparisons
  benchmarkComparisons: SymbolBenchmarkComparison[];
  
  // Alerts and insights
  alerts: SymbolAlert[];
  insights: SymbolInsight[];
  
  // Summary statistics
  summary: {
    totalSymbols: number;
    profitableSymbols: number;
    totalTrades: number;
    totalPnl: number;
    totalInvestment: number;
    overallROI: number;
    overallWinRate: number;
    bestPerformingSymbol: string;
    worstPerformingSymbol: string;
    mostTradedSymbol: string;
    highestRiskSymbol: string;
    bestRiskAdjustedSymbol: string;
  };
  
  // Metadata
  lastUpdated: string;
  dataQuality: {
    completeness: number; // 0-100
    accuracy: number; // 0-100
    freshness: number; // hours since last update
  };
}

// Chart data interfaces for different visualizations
export interface SymbolBarChartData {
  symbol: string;
  value: number;
  metric: string;
  color?: string;
  trades?: number;
  winRate?: number;
}

export interface SymbolScatterPlotData {
  symbol: string;
  x: number; // Risk or other metric
  y: number; // Return or other metric
  size: number; // Volume or trade count
  color?: string;
  quadrant?: 'high-risk-high-return' | 'high-risk-low-return' | 'low-risk-high-return' | 'low-risk-low-return';
}

export interface SymbolHeatmapData {
  symbol: string;
  period: string; // hour, day, month
  value: number;
  intensity: number; // 0-1 for color intensity
  trades: number;
}

export interface SymbolLineChartData {
  date: string;
  symbol: string;
  value: number;
  cumulativeValue: number;
  trades: number;
  movingAverage?: number;
}

// Export utility type for symbol analysis component props
export interface SymbolAnalysisComponentProps {
  data: EnhancedSymbolAnalysisData;
  filters: SymbolAnalysisFilters;
  onFiltersChange: (filters: Partial<SymbolAnalysisFilters>) => void;
  onSymbolSelect?: (symbol: string) => void;
  onSymbolCompare?: (symbols: string[]) => void;
  loading?: boolean;
  error?: string | null;
}
