import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dalOverlay,
  <PERSON>dalContent,
  <PERSON>dal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Select,
  Button,
  VStack,
  FormErrorMessage,
  useColorModeValue,
  useToast,
  NumberInput,
  NumberInputField,
  Spinner,
  Text,
  RadioGroup,
  Radio,
  Stack,
  Flex
} from '@chakra-ui/react';
import { supabase } from '../../supabaseClient';

// Robot seçimi için tip tanımı
interface SelectableRobot {
  robot_id: string;
  robot_name: string;
  source_type: string;
}

// Form değerleri için tip tanımı
interface FormValues {
  symbol: string;
  orderSide: string;
  price: string;
  quantity: string;
  systemType: string;
  robotId: string;
}

// Props için tip tanımı
interface ManualTradeFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const ManualTradeForm: React.FC<ManualTradeFormProps> = ({ isOpen, onClose, onSuccess }) => {
  // Renk değişkenleri
  const textColor = useColorModeValue('navy.700', 'white');
  const placeholderColor = useColorModeValue('secondaryGray.600', 'whiteAlpha.600');
  const inputBg = useColorModeValue('secondaryGray.300', 'navy.900');

  // State değişkenleri
  const [formValues, setFormValues] = useState<FormValues>({
    symbol: '',
    orderSide: 'BUY',
    price: '',
    quantity: '',
    systemType: 'SOLO',
    robotId: ''
  });
  const [errors, setErrors] = useState<Partial<FormValues>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectableRobots, setSelectableRobots] = useState<SelectableRobot[]>([]);
  const [isLoadingRobots, setIsLoadingRobots] = useState(false);
  const toast = useToast();

  // Robot listesini çek
  useEffect(() => {
    if (isOpen && formValues.systemType === 'BRO') {
      fetchSelectableRobots();
    }
  }, [isOpen, formValues.systemType]);

  // Robotları çeken fonksiyon
  const fetchSelectableRobots = async () => {
    setIsLoadingRobots(true);
    try {
      const { data, error } = await supabase.rpc('get_user_selectable_robots');
      
      if (error) {
        console.error('Robot listesi alınamadı:', error);
        toast({
          title: 'Hata',
          description: 'Robot listesi yüklenirken bir hata oluştu.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
      
      setSelectableRobots(data || []);
    } catch (err) {
      console.error('Robot listesi çekme hatası:', err);
    } finally {
      setIsLoadingRobots(false);
    }
  };

  // Form değerlerini değiştiren fonksiyon
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
    
    // Hata mesajını temizle
    if (errors[name as keyof FormValues]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  // Sayısal değerler için özel değiştirme fonksiyonu
  const handleNumberChange = (name: string, value: string) => {
    setFormValues(prev => ({ ...prev, [name]: value }));
    
    // Hata mesajını temizle
    if (errors[name as keyof FormValues]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  // Radio buton değişikliği için fonksiyon
  const handleRadioChange = (name: string, value: string) => {
    setFormValues(prev => ({ ...prev, [name]: value }));
    
    // Bro-Robot seçildiğinde robot listesini çek
    if (name === 'systemType' && value === 'BRO') {
      fetchSelectableRobots();
    }
    
    // Hata mesajını temizle
    if (errors[name as keyof FormValues]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  // Form doğrulama
  const validateForm = (): boolean => {
    const newErrors: Partial<FormValues> = {};
    
    if (!formValues.symbol.trim()) {
      newErrors.symbol = 'Sembol gereklidir';
    }
    
    if (!formValues.price || parseFloat(formValues.price) <= 0) {
      newErrors.price = 'Geçerli bir fiyat giriniz';
    }
    
    if (!formValues.quantity || parseInt(formValues.quantity) <= 0) {
      newErrors.quantity = 'Geçerli bir miktar giriniz';
    }
    
    if (formValues.systemType === 'BRO' && !formValues.robotId) {
      newErrors.robotId = 'Robot seçimi gereklidir';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Formu gönderen fonksiyon
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Artık RPC fonksiyonu SETOF trades döndürüyor, data'yı yakalayalım
      const { data, error } = await supabase.rpc('add_manual_trade', {
        p_symbol: formValues.symbol.trim().toUpperCase(),
        p_order_side: formValues.orderSide,
        p_price: parseFloat(formValues.price),
        p_calculated_quantity: parseFloat(formValues.quantity), // NUMERIC tipine uygun olarak parseFloat kullanıyoruz
        p_investment_amount: parseFloat(formValues.price) * parseFloat(formValues.quantity),
        p_status: 'filled', // Varsayılan olarak filled durumu
        p_position_status: formValues.orderSide === 'BUY' ? 'Açık' : 'Kapandı',
        p_raw_signal_data: JSON.stringify({
          source: formValues.systemType === 'SOLO' ? 'Solo-Robot' : 'Bro-Robot',
          manual: true,
          robot_id: formValues.systemType === 'BRO' ? formValues.robotId : null
        })
      });
      
      if (error) {
        console.error('İşlem eklenirken hata oluştu:', error);
        toast({
          title: 'İşlem Eklenemedi',
          description: error.message || 'Bir hata oluştu.',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
        return;
      }
      
      // Eklenen işlem bilgisini logla
      if (data && data.length > 0) {
        console.log('Eklenen işlem:', data[0]);
      }
      
      toast({
        title: 'İşlem Eklendi',
        description: `${formValues.symbol} için manuel ${formValues.orderSide === 'BUY' ? 'alım' : 'satım'} işlemi başarıyla eklendi.`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
      // Formu sıfırla
      setFormValues({
        symbol: '',
        orderSide: 'BUY',
        price: '',
        quantity: '',
        systemType: 'SOLO',
        robotId: ''
      });
      
      // Başarı callback'ini çağır ve modalı kapat
      onSuccess();
      onClose();
      
    } catch (err: any) {
      console.error('İşlem ekleme hatası:', err);
      toast({
        title: 'İşlem Eklenemedi',
        description: err.message || 'Beklenmeyen bir hata oluştu.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered size="lg">
      <ModalOverlay backdropFilter="blur(10px)" />
      <ModalContent borderRadius="16px" mx={4} boxShadow="xl">
        <ModalHeader color={textColor} fontWeight="700">Manuel İşlem Ekle</ModalHeader>
        <ModalCloseButton />
        <form onSubmit={handleSubmit}>
          <ModalBody pb={6}>
            <VStack spacing={4} align="stretch">
              {/* Sembol */}
              <FormControl isInvalid={!!errors.symbol}>
                <FormLabel htmlFor="symbol" fontWeight="600">Sembol</FormLabel>
                <Input
                  id="symbol"
                  name="symbol"
                  placeholder="Örn: BTCUSDT"
                  value={formValues.symbol}
                  onChange={handleChange}
                  fontSize="sm"
                  fontWeight="500"
                  variant="filled"
                  bg={inputBg}
                  _placeholder={{ color: placeholderColor }}
                  borderRadius="12px"
                  autoComplete="off"
                  _focus={{
                    borderColor: 'brand.500',
                    bg: inputBg
                  }}
                />
                {errors.symbol && <FormErrorMessage>{errors.symbol}</FormErrorMessage>}
              </FormControl>
              
              {/* İşlem Yönü */}
              <FormControl>
                <FormLabel htmlFor="orderSide" fontWeight="600">Kategori</FormLabel>
                <RadioGroup 
                  id="orderSide" 
                  name="orderSide" 
                  value={formValues.orderSide}
                  onChange={(value) => handleRadioChange('orderSide', value)}
                >
                  <Stack direction="row" spacing={6}>
                    <Radio value="BUY" colorScheme="green">Alım</Radio>
                    <Radio value="SELL" colorScheme="blue">Satım</Radio>
                  </Stack>
                </RadioGroup>
              </FormControl>
              
              {/* Fiyat */}
              <FormControl isInvalid={!!errors.price}>
                <FormLabel htmlFor="price" fontWeight="600">Fiyat</FormLabel>
                <NumberInput
                  id="price"
                  min={0}
                  precision={2}
                  value={formValues.price}
                  onChange={(value) => handleNumberChange('price', value)}
                >
                  <NumberInputField
                    name="price"
                    placeholder="0.00"
                    fontSize="sm"
                    fontWeight="500"
                    bg={inputBg}
                    _placeholder={{ color: placeholderColor }}
                    borderRadius="12px"
                    _focus={{
                      borderColor: 'brand.500',
                      bg: inputBg
                    }}
                  />
                </NumberInput>
                {errors.price && <FormErrorMessage>{errors.price}</FormErrorMessage>}
              </FormControl>
              
              {/* Miktar */}
              <FormControl isInvalid={!!errors.quantity}>
                <FormLabel htmlFor="quantity" fontWeight="600">Miktar</FormLabel>
                <NumberInput
                  id="quantity"
                  min={1}
                  precision={0}
                  value={formValues.quantity}
                  onChange={(value) => handleNumberChange('quantity', value)}
                >
                  <NumberInputField
                    name="quantity"
                    placeholder="0"
                    fontSize="sm"
                    fontWeight="500"
                    bg={inputBg}
                    _placeholder={{ color: placeholderColor }}
                    borderRadius="12px"
                    _focus={{
                      borderColor: 'brand.500',
                      bg: inputBg
                    }}
                  />
                </NumberInput>
                {errors.quantity && <FormErrorMessage>{errors.quantity}</FormErrorMessage>}
              </FormControl>
              
              {/* Sistem Tipi */}
              <FormControl>
                <FormLabel htmlFor="systemType" fontWeight="600">Sistem Adı</FormLabel>
                <RadioGroup 
                  id="systemType" 
                  name="systemType" 
                  value={formValues.systemType}
                  onChange={(value) => handleRadioChange('systemType', value)}
                >
                  <Stack direction="row" spacing={6}>
                    <Radio value="SOLO" colorScheme="blue">Solo-Robot</Radio>
                    <Radio value="BRO" colorScheme="purple">Bro-Robot</Radio>
                  </Stack>
                </RadioGroup>
              </FormControl>
              
              {/* Robot Seçimi (sadece Bro-Robot seçiliyse) */}
              {formValues.systemType === 'BRO' && (
                <FormControl isInvalid={!!errors.robotId}>
                  <FormLabel htmlFor="robotId" fontWeight="600">Robot</FormLabel>
                  {isLoadingRobots ? (
                    <Flex align="center" justify="center" py={2}>
                      <Spinner size="sm" mr={2} />
                      <Text fontSize="sm">Robotlar yükleniyor...</Text>
                    </Flex>
                  ) : selectableRobots.length === 0 ? (
                    <Text fontSize="sm" color="orange.500">
                      Seçilebilir robot bulunamadı. Bir robota abone olun veya kendi robotunuzu oluşturun.
                    </Text>
                  ) : (
                    <Select
                      id="robotId"
                      name="robotId"
                      placeholder="Robot seçin"
                      value={formValues.robotId}
                      onChange={handleChange}
                      fontSize="sm"
                      fontWeight="500"
                      variant="filled"
                      bg={inputBg}
                      _placeholder={{ color: placeholderColor }}
                      borderRadius="12px"
                      _focus={{
                        borderColor: 'brand.500',
                        bg: inputBg
                      }}
                    >
                      {selectableRobots.map((robot) => (
                        <option key={`${robot.robot_id}-${robot.source_type}`} value={robot.robot_id}>
                          {robot.robot_name} ({robot.source_type})
                        </option>
                      ))}
                    </Select>
                  )}
                  {errors.robotId && <FormErrorMessage>{errors.robotId}</FormErrorMessage>}
                </FormControl>
              )}
            </VStack>
          </ModalBody>
          <ModalFooter gap={3}>
            <Button 
              onClick={onClose} 
              variant="outline" 
              colorScheme="gray" 
              fontWeight="500"
              borderRadius="12px"
              isDisabled={isSubmitting}
            >
              İptal
            </Button>
            <Button 
              type="submit" 
              colorScheme="brand" 
              isLoading={isSubmitting}
              loadingText="Kaydediliyor"
              fontWeight="500"
              borderRadius="12px"
            >
              Kaydet
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

export default ManualTradeForm; 