export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          avatar_url: string | null
          full_name: string | null
          id: string
          updated_at: string | null
          username: string | null
          username_change_count: number
          website: string | null
        }
        Insert: {
          avatar_url?: string | null
          full_name?: string | null
          id: string
          updated_at?: string | null
          username?: string | null
          username_change_count?: number
          website?: string | null
        }
        Update: {
          avatar_url?: string | null
          full_name?: string | null
          id?: string
          updated_at?: string | null
          username?: string | null
          username_change_count?: number
          website?: string | null
        }
        Relationships: []
      }
      robots: {
        Row: {
          created_at: string | null
          deleted_at: string | null
          description: string | null
          id: string
          image_url: string | null
          investment_amount_per_position: number | null
          is_deleted: boolean
          is_emergency_stopped: boolean
          is_public: boolean | null
          name: string
          price: number
          seller_id: string | null
          status: string
          strategy_type: string | null
          subscription_period: number
          updated_at: string | null
          version: string | null
        }
        Insert: {
          created_at?: string | null
          deleted_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          investment_amount_per_position?: number | null
          is_deleted?: boolean
          is_emergency_stopped?: boolean
          is_public?: boolean | null
          name: string
          price?: number
          seller_id?: string | null
          status?: string
          strategy_type?: string | null
          subscription_period?: number
          updated_at?: string | null
          version?: string | null
        }
        Update: {
          created_at?: string | null
          deleted_at?: string | null
          description?: string | null
          id?: string
          image_url?: string | null
          investment_amount_per_position?: number | null
          is_deleted?: boolean
          is_emergency_stopped?: boolean
          is_public?: boolean | null
          name?: string
          price?: number
          seller_id?: string | null
          status?: string
          strategy_type?: string | null
          subscription_period?: number
          updated_at?: string | null
          version?: string | null
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          created_at: string | null
          ends_at: string | null
          id: string
          is_active: boolean | null
          is_deleted: boolean
          robot_id: string | null
          started_at: string | null
          updated_at: string | null
          user_configuration_overrides: Json | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          ends_at?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean
          robot_id?: string | null
          started_at?: string | null
          updated_at?: string | null
          user_configuration_overrides?: Json | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          ends_at?: string | null
          id?: string
          is_active?: boolean | null
          is_deleted?: boolean
          robot_id?: string | null
          started_at?: string | null
          updated_at?: string | null
          user_configuration_overrides?: Json | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_robot_id_fkey"
            columns: ["robot_id"]
            isOneToOne: false
            referencedRelation: "robots"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_robot_id_fkey"
            columns: ["robot_id"]
            isOneToOne: false
            referencedRelation: "robots_with_seller_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      trades: {
        Row: {
          calculated_quantity: number | null
          closing_trade_id: number | null
          forwarded_at: string | null
          forwarding_status: string | null
          id: number
          is_deleted: boolean | null
          name: string | null
          order_side: string | null
          order_type: string | null
          pnl: number | null
          position_id: string | null
          position_status: string | null
          price: number | null
          received_at: string | null
          robot_id: string | null
          signal_type: string | null
          symbol: string | null
          system_name: string | null
          time_in_force: string | null
          trade_category: string | null
          user_id: string | null
          webhook_id: string | null
        }
        Insert: {
          calculated_quantity?: number | null
          closing_trade_id?: number | null
          forwarded_at?: string | null
          forwarding_status?: string | null
          id?: number
          is_deleted?: boolean | null
          name?: string | null
          order_side?: string | null
          order_type?: string | null
          pnl?: number | null
          position_id?: string | null
          position_status?: string | null
          price?: number | null
          received_at?: string | null
          robot_id?: string | null
          signal_type?: string | null
          symbol?: string | null
          system_name?: string | null
          time_in_force?: string | null
          trade_category?: string | null
          user_id?: string | null
          webhook_id?: string | null
        }
        Update: {
          calculated_quantity?: number | null
          closing_trade_id?: number | null
          forwarded_at?: string | null
          forwarding_status?: string | null
          id?: number
          is_deleted?: boolean | null
          name?: string | null
          order_side?: string | null
          order_type?: string | null
          pnl?: number | null
          position_id?: string | null
          position_status?: string | null
          price?: number | null
          received_at?: string | null
          robot_id?: string | null
          signal_type?: string | null
          symbol?: string | null
          system_name?: string | null
          time_in_force?: string | null
          trade_category?: string | null
          user_id?: string | null
          webhook_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "trades_closing_trade_id_fkey"
            columns: ["closing_trade_id"]
            isOneToOne: false
            referencedRelation: "trades"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "trades_closing_trade_id_fkey"
            columns: ["closing_trade_id"]
            isOneToOne: false
            referencedRelation: "trades_with_standard_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      user_settings: {
        Row: {
          api_key_set: boolean
          created_at: string | null
          custom_webhook_url: string | null
          id: string
          investment_amount: number | null
          is_active: boolean | null
          is_superuser: boolean | null
          token_set: boolean
          total_investment_amount: number | null
          updated_at: string | null
          webhook_id: string
        }
        Insert: {
          api_key_set?: boolean
          created_at?: string | null
          custom_webhook_url?: string | null
          id: string
          investment_amount?: number | null
          is_active?: boolean | null
          is_superuser?: boolean | null
          token_set?: boolean
          total_investment_amount?: number | null
          updated_at?: string | null
          webhook_id?: string
        }
        Update: {
          api_key_set?: boolean
          created_at?: string | null
          custom_webhook_url?: string | null
          id?: string
          investment_amount?: number | null
          is_active?: boolean | null
          is_superuser?: boolean | null
          token_set?: boolean
          total_investment_amount?: number | null
          updated_at?: string | null
          webhook_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      robots_with_seller_profile: {
        Row: {
          created_at: string | null
          deleted_at: string | null
          description: string | null
          id: string | null
          image_url: string | null
          investment_amount_per_position: number | null
          is_emergency_stopped: boolean | null
          is_public: boolean | null
          name: string | null
          price: number | null
          seller_avatar_url: string | null
          seller_full_name: string | null
          seller_id: string | null
          seller_username: string | null
          status: string | null
          strategy_type: string | null
          subscription_period: number | null
          updated_at: string | null
          version: string | null
        }
        Relationships: []
      }
      trades_with_standard_categories: {
        Row: {
          calculated_quantity: number | null
          closing_trade_id: number | null
          forwarded_at: string | null
          forwarding_status: string | null
          id: number | null
          is_deleted: boolean | null
          name: string | null
          order_side: string | null
          order_type: string | null
          pnl: number | null
          position_id: string | null
          position_status: string | null
          price: number | null
          received_at: string | null
          signal_category_simple: string | null
          signal_type: string | null
          standard_category: string | null
          symbol: string | null
          system_name: string | null
          time_in_force: string | null
          trade_category: string | null
          user_id: string | null
          webhook_id: string | null
        }
        Insert: {
          calculated_quantity?: number | null
          closing_trade_id?: number | null
          forwarded_at?: string | null
          forwarding_status?: string | null
          id?: number | null
          is_deleted?: boolean | null
          name?: string | null
          order_side?: string | null
          order_type?: string | null
          pnl?: number | null
          position_id?: string | null
          position_status?: string | null
          price?: number | null
          received_at?: string | null
          signal_category_simple?: never
          signal_type?: string | null
          standard_category?: never
          symbol?: string | null
          system_name?: string | null
          time_in_force?: string | null
          trade_category?: string | null
          user_id?: string | null
          webhook_id?: string | null
        }
        Update: {
          calculated_quantity?: number | null
          closing_trade_id?: number | null
          forwarded_at?: string | null
          forwarding_status?: string | null
          id?: number | null
          is_deleted?: boolean | null
          name?: string | null
          order_side?: string | null
          order_type?: string | null
          pnl?: number | null
          position_id?: string | null
          position_status?: string | null
          price?: number | null
          received_at?: string | null
          signal_category_simple?: never
          signal_type?: string | null
          standard_category?: never
          symbol?: string | null
          system_name?: string | null
          time_in_force?: string | null
          trade_category?: string | null
          user_id?: string | null
          webhook_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "trades_closing_trade_id_fkey"
            columns: ["closing_trade_id"]
            isOneToOne: false
            referencedRelation: "trades"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "trades_closing_trade_id_fkey"
            columns: ["closing_trade_id"]
            isOneToOne: false
            referencedRelation: "trades_with_standard_categories"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      calculate_and_update_pnl_for_position: {
        Args: { p_position_id_text: string }
        Returns: {
          calculated_pnl: number
          buy_trade_updated: boolean
          sell_trade_updated: boolean
          error_message: string
        }[]
      }
      fetch_user_subscriptions_detailed: {
        Args: { p_user_id: string }
        Returns: {
          subscription_id: string
          robot_id: string
          started_at: string
          ends_at: string
          is_active: boolean
          robot_name: string
          robot_description: string
          robot_image_url: string
          robot_strategy_type: string
        }[]
      }
      find_open_buy_trade: {
        Args: { p_user_id: string; p_symbol: string }
        Returns: {
          calculated_quantity: number | null
          closing_trade_id: number | null
          forwarded_at: string | null
          forwarding_status: string | null
          id: number
          is_deleted: boolean | null
          name: string | null
          order_side: string | null
          order_type: string | null
          pnl: number | null
          position_id: string | null
          position_status: string | null
          price: number | null
          received_at: string | null
          robot_id: string | null
          signal_type: string | null
          symbol: string | null
          system_name: string | null
          time_in_force: string | null
          trade_category: string | null
          user_id: string | null
          webhook_id: string | null
        }
      }
      get_basic_pnl_stats: {
        Args: { user_id_param: string }
        Returns: {
          total_pnl: number
          winning_trades_count: number
          losing_trades_count: number
          win_rate: number
        }[]
      }
      get_cumulative_pnl_series: {
        Args: { p_user_id: string }
        Returns: {
          date: string
          value: number
        }[]
      }
      get_dashboard_balance_stats: {
        Args: { p_user_id: string }
        Returns: {
          total_open_position_cost: number
          total_closed_pnl: number
        }[]
      }
      get_open_positions_cost: {
        Args: { p_user_id: string }
        Returns: number
      }
      get_portfolio_growth_series: {
        Args:
          | { p_user_id: string }
          | { p_user_id: string; p_initial_investment: number }
        Returns: {
          date_point: string
          portfolio_value: number
        }[]
      }
      get_portfolio_summary: {
        Args: { p_user_id: string }
        Returns: {
          total_investment: number
          total_pnl: number
          open_positions_cost: number
          total_assets: number
          available_balance: number
        }[]
      }
      get_robot_subscribers: {
        Args: { p_robot_id: string }
        Returns: {
          subscription_id: string
          user_id: string
          subscribed_at: string
          subscription_is_active: boolean
          username: string
          full_name: string
        }[]
      }
      get_subscribers_for_relay: {
        Args: { target_robot_id: string }
        Returns: {
          ret_user_id: string
          ret_custom_webhook_url: string
          ret_api_key_set: boolean
          ret_token_set: boolean
          ret_username: string
          ret_full_name: string
        }[]
      }
      get_symbol_performance_stats: {
        Args: { p_user_id: string }
        Returns: {
          symbol: string
          total_trades: number
          buy_trades: number
          sell_trades: number
          pnl: number
          win_rate: number
          profit_factor: number
          avg_win: number
          avg_loss: number
        }[]
      }
      get_total_pnl: {
        Args: { p_user_id: string }
        Returns: number
      }
      get_trades_with_pnl: {
        Args: { p_user_id: string }
        Returns: {
          calculated_quantity: number | null
          closing_trade_id: number | null
          forwarded_at: string | null
          forwarding_status: string | null
          id: number
          is_deleted: boolean | null
          name: string | null
          order_side: string | null
          order_type: string | null
          pnl: number | null
          position_id: string | null
          position_status: string | null
          price: number | null
          received_at: string | null
          robot_id: string | null
          signal_type: string | null
          symbol: string | null
          system_name: string | null
          time_in_force: string | null
          trade_category: string | null
          user_id: string | null
          webhook_id: string | null
        }[]
      }
      get_user_emails_by_ids: {
        Args: { user_ids: string[] }
        Returns: {
          id: string
          email: string
        }[]
      }
      get_user_portfolio_summary: {
        Args: { p_user_id: string }
        Returns: Record<string, unknown>
      }
      is_admin: {
        Args: { user_id_to_check: string }
        Returns: boolean
      }
      list_all_users_admin: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          email: string
          full_name: string
          username: string
          created_at: string
          is_superuser: boolean
          api_key_set: boolean
          token_set: boolean
          custom_webhook_url: string
          investment_amount: number
        }[]
      }
      set_robot_emergency_status: {
        Args:
          | { p_requesting_user_id: string; p_is_stopped: boolean }
          | {
              p_robot_id: string
              p_requesting_user_id: string
              p_is_stopped: boolean
            }
        Returns: Json
      }
      soft_delete_robot: {
        Args: { p_robot_id: string; p_requesting_user_id: string }
        Returns: Json
      }
      soft_delete_subscription: {
        Args:
          | { p_subscription_id: string }
          | { p_subscription_id_to_delete: string; p_user_id: string }
        Returns: undefined
      }
      stage_trade_with_calculated_quantity: {
        Args: {
          p_robot_id: string
          p_current_price: number
          p_signal_symbol: string
          p_signal_side: string
          p_raw_signal_data?: Json
        }
        Returns: Json
      }
      update_profile_username: {
        Args: { new_username: string }
        Returns: string
      }
      update_robot_details: {
        Args:
          | {
              p_robot_id: string
              p_name: string
              p_description: string
              p_avatar_url: string
              p_status: string
              p_price: number
              p_subscription_period: number
              p_strategy_type: string
              p_version: string
              p_seller_id: string
            }
          | {
              p_robot_id: string
              p_seller_id: string
              p_name: string
              p_description: string
              p_image_url: string
              p_strategy_type: string
              p_version: string
              p_is_public: boolean
            }
        Returns: undefined
      }
      update_subscription_active_status: {
        Args:
          | {
              p_subscription_id: string
              p_is_active: boolean
              p_user_id: string
            }
          | { subscription_id_param: string; is_active_param: boolean }
        Returns: undefined
      }
      update_user_settings_admin: {
        Args:
          | { target_identifier: string; settings_data: Json }
          | { target_user_id: string; settings_data: Json }
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      portfolio_point: {
        entry_date: string | null
        portfolio_value: number | null
      }
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
