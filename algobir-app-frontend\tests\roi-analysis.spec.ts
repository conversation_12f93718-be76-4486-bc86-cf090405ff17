import { test, expect } from '@playwright/test';

test.describe('ROI Analysis Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the statistics page
    await page.goto('/statistics');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Navigate to ROI Analysis section
    await page.click('text=ROI Analizi');
    await page.waitForSelector('[data-testid="roi-dashboard"]', { timeout: 10000 });
  });

  test('should display ROI dashboard with basic metrics', async ({ page }) => {
    // Check if main ROI metrics are displayed
    await expect(page.locator('text=Toplam ROI')).toBeVisible();
    await expect(page.locator('text=Yıllık ROI')).toBeVisible();
    await expect(page.locator('text=Toplam Yatırım')).toBeVisible();
    await expect(page.locator('text=Toplam Getiri')).toBeVisible();
    
    // Check if ROI values are displayed with proper formatting
    await expect(page.locator('text=₺').first()).toBeVisible();
    await expect(page.locator('text=%').first()).toBeVisible();
  });

  test('should toggle advanced metrics', async ({ page }) => {
    // Find and click the advanced metrics toggle
    const advancedToggle = page.locator('input[id="advanced-metrics"]');
    await advancedToggle.click();
    
    // Check if advanced metrics appear
    await expect(page.locator('text=Volatilite')).toBeVisible();
    await expect(page.locator('text=Sharpe Oranı')).toBeVisible();
    await expect(page.locator('text=Maksimum Düşüş')).toBeVisible();
    
    // Toggle off and check if they disappear
    await advancedToggle.click();
    await expect(page.locator('text=Volatilite')).not.toBeVisible();
  });

  test('should switch between timeframes', async ({ page }) => {
    // Test monthly timeframe (default)
    await expect(page.locator('select').first()).toHaveValue('monthly');
    
    // Switch to quarterly
    await page.selectOption('select', 'quarterly');
    await expect(page.locator('text=Çeyreklik ROI Trendi')).toBeVisible();
    
    // Switch to yearly
    await page.selectOption('select', 'yearly');
    await expect(page.locator('text=Yıllık ROI Trendi')).toBeVisible();
  });

  test('should switch between view modes', async ({ page }) => {
    // Test percentage mode (default)
    const percentageButton = page.locator('button:has-text("%")');
    const absoluteButton = page.locator('button:has-text("₺")');
    
    await expect(percentageButton).toHaveAttribute('data-active', 'true');
    
    // Switch to absolute mode
    await absoluteButton.click();
    await expect(absoluteButton).toHaveAttribute('data-active', 'true');
    
    // Switch back to percentage
    await percentageButton.click();
    await expect(percentageButton).toHaveAttribute('data-active', 'true');
  });

  test('should navigate between analysis tabs', async ({ page }) => {
    // Check default tab (Trend Analysis)
    await expect(page.locator('text=Trend Analizi')).toBeVisible();
    
    // Click on Comparison tab
    await page.click('text=Karşılaştırma');
    await expect(page.locator('text=Karşılaştırma Türü')).toBeVisible();
    
    // Click on Distribution tab
    await page.click('text=Dağılım');
    await expect(page.locator('text=Yatırım Dağılımı')).toBeVisible();
    
    // Check Risk Analysis tab (if advanced metrics are enabled)
    await page.locator('input[id="advanced-metrics"]').click();
    await page.click('text=Risk Analizi');
    await expect(page.locator('text=Risk Metrikleri')).toBeVisible();
  });

  test('should display comparison modes correctly', async ({ page }) => {
    // Navigate to comparison tab
    await page.click('text=Karşılaştırma');
    
    // Test Robot comparison (default)
    await expect(page.locator('button:has-text("Robot Bazlı")')).toHaveClass(/.*active.*/);
    await expect(page.locator('text=Robot ROI Karşılaştırması')).toBeVisible();
    
    // Switch to Symbol comparison
    await page.click('text=Sembol Bazlı');
    await expect(page.locator('text=Sembol Bazlı ROI Analizi')).toBeVisible();
    
    // Switch to Time comparison
    await page.click('text=Zaman Bazlı');
    await expect(page.locator('text=Zaman Bazlı ROI Karşılaştırması')).toBeVisible();
    await expect(page.locator('text=Performans Sıralaması')).toBeVisible();
  });

  test('should display real-time indicators when live updates are active', async ({ page }) => {
    // Check for live update indicators
    const liveIndicator = page.locator('text=CANLI');
    if (await liveIndicator.isVisible()) {
      // Check for pulsing animation
      await expect(liveIndicator).toBeVisible();
      
      // Check for real-time metrics
      await expect(page.locator('text=Açık Pozisyon')).toBeVisible();
      await expect(page.locator('text=Günlük P&L')).toBeVisible();
    }
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Simulate network error by intercepting requests
    await page.route('**/api/statistics/**', route => {
      route.abort();
    });
    
    // Reload page to trigger error
    await page.reload();
    
    // Check for error message
    await expect(page.locator('text=ROI Analizi Hatası')).toBeVisible();
    await expect(page.locator('button:has-text("Yeniden Dene")')).toBeVisible();
  });

  test('should display loading states', async ({ page }) => {
    // Simulate slow network by delaying requests
    await page.route('**/api/statistics/**', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      await route.continue();
    });
    
    // Reload page to trigger loading
    await page.reload();
    
    // Check for loading spinner
    await expect(page.locator('text=ROI Analizi Yükleniyor...')).toBeVisible();
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible();
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check if mobile layout is applied
    await expect(page.locator('[data-testid="roi-dashboard"]')).toBeVisible();
    
    // Check if tabs are scrollable on mobile
    const tabList = page.locator('[role="tablist"]');
    await expect(tabList).toHaveCSS('overflow-x', 'auto');
    
    // Check if controls stack vertically on mobile
    const controls = page.locator('[data-testid="roi-controls"]');
    await expect(controls).toBeVisible();
  });

  test('should filter ROI data correctly', async ({ page }) => {
    // Navigate to filters (assuming they're in a sidebar or header)
    await page.click('[data-testid="filter-toggle"]');
    
    // Test ROI range filter
    await page.fill('input[placeholder="0"]', '10'); // Min ROI
    await page.fill('input[placeholder="∞"]', '50'); // Max ROI
    
    // Apply filter and check results
    await page.click('button:has-text("Filtrele")');
    
    // Verify filtered results
    const roiValues = await page.locator('[data-testid="roi-value"]').allTextContents();
    roiValues.forEach(value => {
      const numericValue = parseFloat(value.replace('%', ''));
      expect(numericValue).toBeGreaterThanOrEqual(10);
      expect(numericValue).toBeLessThanOrEqual(50);
    });
  });

  test('should export ROI analysis data', async ({ page }) => {
    // Look for export button
    const exportButton = page.locator('button:has-text("Dışa Aktar")');
    if (await exportButton.isVisible()) {
      // Start download
      const downloadPromise = page.waitForEvent('download');
      await exportButton.click();
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toContain('roi-analysis');
      expect(download.suggestedFilename()).toMatch(/\.(pdf|xlsx|csv)$/);
    }
  });

  test('should maintain state when switching between sections', async ({ page }) => {
    // Set advanced metrics on
    await page.locator('input[id="advanced-metrics"]').click();
    
    // Set quarterly timeframe
    await page.selectOption('select', 'quarterly');
    
    // Navigate to another section and back
    await page.click('text=Performans');
    await page.click('text=ROI Analizi');
    
    // Verify state is maintained
    await expect(page.locator('input[id="advanced-metrics"]')).toBeChecked();
    await expect(page.locator('select').first()).toHaveValue('quarterly');
  });

  test('should display performance insights correctly', async ({ page }) => {
    // Check for performance insights section
    await expect(page.locator('text=Performans İçgörüleri')).toBeVisible();
    
    // Check for best performer
    await expect(page.locator('text=🏆 En İyi Performans')).toBeVisible();
    
    // Check for portfolio diversity
    await expect(page.locator('text=📊 Portföy Çeşitliliği')).toBeVisible();
    
    // Check for risk assessment (if advanced metrics enabled)
    await page.locator('input[id="advanced-metrics"]').click();
    await expect(page.locator('text=🛡️ Risk Değerlendirmesi')).toBeVisible();
  });

  test('should handle empty data states', async ({ page }) => {
    // Mock empty data response
    await page.route('**/api/statistics/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          roiAnalysis: {
            totalInvestment: 0,
            totalReturns: 0,
            totalROI: 0,
            roiByRobot: [],
            roiBySymbol: []
          }
        })
      });
    });
    
    await page.reload();
    
    // Check for empty state message
    await expect(page.locator('text=ROI Analizi Yapılamıyor')).toBeVisible();
    await expect(page.locator('text=yeterli yatırım verisi bulunmuyor')).toBeVisible();
  });

  test('should validate Turkish language support', async ({ page }) => {
    // Check for Turkish text elements
    await expect(page.locator('text=ROI ve Karlılık Analizi')).toBeVisible();
    await expect(page.locator('text=Gelişmiş yatırım getirisi')).toBeVisible();
    await expect(page.locator('text=Zaman Aralığı')).toBeVisible();
    await expect(page.locator('text=Görünüm Modu')).toBeVisible();
    
    // Check for proper Turkish formatting
    await expect(page.locator('text=₺')).toBeVisible(); // Turkish Lira symbol
    
    // Check for Turkish month names in charts (if visible)
    const turkishMonths = ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran'];
    for (const month of turkishMonths) {
      if (await page.locator(`text=${month}`).isVisible()) {
        await expect(page.locator(`text=${month}`)).toBeVisible();
        break;
      }
    }
  });
});
