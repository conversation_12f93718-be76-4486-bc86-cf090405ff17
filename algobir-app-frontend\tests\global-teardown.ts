import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...');
  
  try {
    // Clean up test data
    await cleanupTestData();
    
    // Clean up test files
    await cleanupTestFiles();
    
    // Generate test report summary
    await generateTestSummary();
    
    console.log('✅ Global teardown completed successfully');
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error to avoid failing the test suite
  }
}

async function cleanupTestData() {
  console.log('🗑️ Cleaning up test data...');
  
  // Clean up any test data that was created during tests
  // This could include:
  // - Removing test user accounts
  // - Clearing test database entries
  // - Resetting application state
  
  console.log('✅ Test data cleanup completed');
}

async function cleanupTestFiles() {
  console.log('📁 Cleaning up test files...');
  
  // Clean up any temporary files created during tests
  // This could include:
  // - Downloaded files
  // - Screenshots from failed tests (if not needed)
  // - Temporary data files
  
  console.log('✅ Test files cleanup completed');
}

async function generateTestSummary() {
  console.log('📊 Generating test summary...');
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    // Read test results if available
    const resultsPath = path.join(process.cwd(), 'test-results', 'results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      const summary = {
        timestamp: new Date().toISOString(),
        totalTests: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
        suites: results.suites?.map((suite: any) => ({
          title: suite.title,
          tests: suite.tests?.length || 0,
          passed: suite.tests?.filter((t: any) => t.outcome === 'passed').length || 0,
          failed: suite.tests?.filter((t: any) => t.outcome === 'failed').length || 0
        })) || []
      };
      
      // Write summary to file
      const summaryPath = path.join(process.cwd(), 'test-results', 'summary.json');
      fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
      
      // Log summary to console
      console.log('📈 Test Summary:');
      console.log(`   Total Tests: ${summary.totalTests}`);
      console.log(`   Passed: ${summary.passed}`);
      console.log(`   Failed: ${summary.failed}`);
      console.log(`   Skipped: ${summary.skipped}`);
      console.log(`   Duration: ${Math.round(summary.duration / 1000)}s`);
      
      if (summary.failed > 0) {
        console.log('❌ Some tests failed. Check the HTML report for details.');
      } else {
        console.log('✅ All tests passed!');
      }
    }
    
  } catch (error) {
    console.error('Failed to generate test summary:', error);
  }
  
  console.log('✅ Test summary generation completed');
}

export default globalTeardown;
