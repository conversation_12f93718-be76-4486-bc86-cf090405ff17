/**
 * Comprehensive search utilities with fuzzy matching and relevance scoring
 */

interface CacheEntry {
  results: SearchResult[];
  timestamp: number;
}

// Simple cache for search results
const searchCache = new Map<string, CacheEntry>();
const CACHE_SIZE_LIMIT = 100;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

export interface SearchableItem {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  keywords?: string[];
  type: 'page' | 'robot' | 'trade' | 'user' | 'documentation' | 'help';
  url?: string;
  badge?: string;
  metadata?: any;
  searchableText?: string; // Combined text for searching
  preview?: {
    description: string;
    features?: string[];
    category?: string;
    estimatedContent?: string;
  };
}

export interface SearchResult extends SearchableItem {
  relevanceScore: number;
  matchedFields: string[];
  highlightedTitle?: string;
  highlightedSubtitle?: string;
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,     // deletion
        matrix[j - 1][i] + 1,     // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }
  
  return matrix[str2.length][str1.length];
}

/**
 * Calculate fuzzy match score (0-1, higher is better)
 */
function fuzzyMatchScore(query: string, target: string): number {
  if (!query || !target) return 0;
  
  const queryLower = query.toLowerCase();
  const targetLower = target.toLowerCase();
  
  // Exact match gets highest score
  if (targetLower === queryLower) return 1.0;
  
  // Starts with query gets high score
  if (targetLower.startsWith(queryLower)) return 0.9;
  
  // Contains query gets medium score
  if (targetLower.includes(queryLower)) return 0.7;
  
  // Fuzzy matching using Levenshtein distance
  const distance = levenshteinDistance(queryLower, targetLower);
  const maxLength = Math.max(queryLower.length, targetLower.length);
  const similarity = 1 - (distance / maxLength);
  
  // Only consider it a match if similarity is above threshold
  return similarity > 0.6 ? similarity * 0.5 : 0;
}

/**
 * Calculate relevance score for a search item
 */
function calculateRelevanceScore(query: string, item: SearchableItem): { score: number; matchedFields: string[] } {
  const matchedFields: string[] = [];
  let totalScore = 0;
  
  // Title match (highest weight)
  const titleScore = fuzzyMatchScore(query, item.title);
  if (titleScore > 0) {
    totalScore += titleScore * 3;
    matchedFields.push('title');
  }
  
  // Subtitle match (medium weight)
  if (item.subtitle) {
    const subtitleScore = fuzzyMatchScore(query, item.subtitle);
    if (subtitleScore > 0) {
      totalScore += subtitleScore * 2;
      matchedFields.push('subtitle');
    }
  }
  
  // Description match (medium weight)
  if (item.description) {
    const descriptionScore = fuzzyMatchScore(query, item.description);
    if (descriptionScore > 0) {
      totalScore += descriptionScore * 1.5;
      matchedFields.push('description');
    }
  }
  
  // Keywords match (high weight)
  if (item.keywords) {
    for (const keyword of item.keywords) {
      const keywordScore = fuzzyMatchScore(query, keyword);
      if (keywordScore > 0) {
        totalScore += keywordScore * 2.5;
        matchedFields.push('keywords');
        break; // Only count keywords once
      }
    }
  }
  
  // Type-based boost
  const typeBoosts = {
    page: 1.2,
    robot: 1.1,
    user: 1.0,
    trade: 0.9,
    documentation: 1.1,
    help: 1.0
  };
  
  totalScore *= typeBoosts[item.type] || 1.0;
  
  return { score: totalScore, matchedFields };
}

/**
 * Highlight matching text in a string
 */
function highlightText(text: string, query: string): string {
  if (!query || !text) return text;
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

/**
 * Main search function with fuzzy matching and relevance scoring
 */
export function performAdvancedSearch(
  query: string,
  items: SearchableItem[],
  options: {
    maxResults?: number;
    minScore?: number;
    groupByType?: boolean;
  } = {}
): SearchResult[] {
  const { maxResults = 20, minScore = 0.1, groupByType = false } = options;

  if (!query.trim()) return [];

  // Create cache key
  const cacheKey = `${query.toLowerCase()}-${maxResults}-${minScore}-${groupByType}-${items.length}`;

  // Check cache first
  const cached = searchCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.results;
  }

  const results: SearchResult[] = [];

  for (const item of items) {
    const { score, matchedFields } = calculateRelevanceScore(query, item);

    if (score >= minScore) {
      results.push({
        ...item,
        relevanceScore: score,
        matchedFields,
        highlightedTitle: highlightText(item.title, query),
        highlightedSubtitle: item.subtitle ? highlightText(item.subtitle, query) : undefined
      });
    }
  }
  
  // Sort by relevance score (descending)
  results.sort((a, b) => b.relevanceScore - a.relevanceScore);
  
  if (groupByType) {
    // Group by type while maintaining overall relevance order
    const grouped: { [key: string]: SearchResult[] } = {};
    
    for (const result of results) {
      if (!grouped[result.type]) {
        grouped[result.type] = [];
      }
      grouped[result.type].push(result);
    }
    
    // Flatten back while respecting type groups
    const typeOrder = ['page', 'robot', 'user', 'trade', 'documentation', 'help'];
    const finalResults: SearchResult[] = [];
    
    for (const type of typeOrder) {
      if (grouped[type]) {
        finalResults.push(...grouped[type].slice(0, Math.ceil(maxResults / typeOrder.length)));
      }
    }
    
    const finalResults_sliced = finalResults.slice(0, maxResults);

    // Cache the results
    if (searchCache.size >= CACHE_SIZE_LIMIT) {
      // Remove oldest entries
      const oldestKey = searchCache.keys().next().value;
      if (oldestKey) {
        searchCache.delete(oldestKey);
      }
    }
    searchCache.set(cacheKey, { results: finalResults_sliced, timestamp: Date.now() });

    return finalResults_sliced;
  }

  const finalResults = results.slice(0, maxResults);

  // Cache the results
  if (searchCache.size >= CACHE_SIZE_LIMIT) {
    // Remove oldest entries
    const oldestKey = searchCache.keys().next().value;
    if (oldestKey) {
      searchCache.delete(oldestKey);
    }
  }
  searchCache.set(cacheKey, { results: finalResults, timestamp: Date.now() });

  return finalResults;
}

/**
 * Debounce function for search input
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Create searchable text from an item
 */
export function createSearchableText(item: Partial<SearchableItem>): string {
  const parts = [
    item.title,
    item.subtitle,
    item.description,
    ...(item.keywords || [])
  ].filter(Boolean);

  return parts.join(' ').toLowerCase();
}

/**
 * Clear expired cache entries
 */
export function clearExpiredCache(): void {
  const now = Date.now();
  for (const [key, entry] of searchCache.entries()) {
    if (now - entry.timestamp > CACHE_TTL) {
      searchCache.delete(key);
    }
  }
}

/**
 * Clear all cache entries
 */
export function clearSearchCache(): void {
  searchCache.clear();
}

/**
 * Generate preview data for search results
 */
export function generatePreviewData(item: SearchableItem): SearchableItem {
  const preview = getPreviewByType(item.type, item.id, item.title);

  return {
    ...item,
    preview
  };
}

/**
 * Get preview information based on item type and ID
 */
function getPreviewByType(type: string, id: string, title: string) {
  switch (type) {
    case 'page':
      return getPagePreview(id, title);
    case 'robot':
      return getRobotPreview(id, title);
    case 'user':
      return getUserPreview(id, title);
    case 'documentation':
      return getDocumentationPreview(id, title);
    default:
      return {
        description: `${title} sayfasını görüntüleyeceksiniz`,
        category: 'Genel İçerik'
      };
  }
}

/**
 * Get page-specific preview information
 */
function getPagePreview(pageId: string, title: string) {
  const pagePreviewData: Record<string, any> = {
    'dashboard': {
      description: 'Ana kontrol paneli ve genel bakış sayfası',
      features: ['Toplam yatırım özeti', 'Son işlemler', 'Performans grafikleri', 'Hızlı erişim menüleri'],
      category: 'Ana Sayfa',
      estimatedContent: 'Hesap özetiniz, son işlemleriniz ve performans metrikleri'
    },
    'trades': {
      description: 'Tüm alım satım işlemlerinizi görüntüleyin ve yönetin',
      features: ['İşlem geçmişi', 'Filtreleme seçenekleri', 'Excel dışa aktarım', 'Manuel işlem ekleme'],
      category: 'İşlem Yönetimi',
      estimatedContent: 'Geçmiş işlemleriniz, kar/zarar analizi ve detaylı işlem bilgileri'
    },
    'open-positions': {
      description: 'Açık pozisyonlarınızı takip edin ve yönetin',
      features: ['Açık işlemler listesi', 'Pozisyon kapatma', 'Gerçek zamanlı P&L', 'Risk yönetimi'],
      category: 'Pozisyon Takibi',
      estimatedContent: 'Aktif pozisyonlarınız ve anlık kar/zarar durumu'
    },
    'marketplace': {
      description: 'Robot pazarında robotları keşfedin ve abone olun',
      features: ['Robot arama', 'Performans karşılaştırması', 'Abonelik yönetimi', 'Yapımcı profilleri'],
      category: 'Robot Pazarı',
      estimatedContent: 'Mevcut robotlar, performans istatistikleri ve abonelik seçenekleri'
    },
    'statistics': {
      description: 'Detaylı performans analizi ve istatistikler',
      features: ['Kar/zarar analizi', 'Performans grafikleri', 'Risk metrikleri', 'Trend analizi'],
      category: 'Analiz ve Raporlama',
      estimatedContent: 'Kapsamlı trading performansınız ve detaylı analiz raporları'
    },
    'management': {
      description: 'Hesap ayarları ve API yönetimi',
      features: ['API anahtarı yönetimi', 'Güvenlik ayarları', 'Bildirim tercihleri', 'Hesap bilgileri'],
      category: 'Hesap Yönetimi',
      estimatedContent: 'Hesap ayarlarınız, güvenlik seçenekleri ve API konfigürasyonu'
    }
  };

  return pagePreviewData[pageId] || {
    description: `${title} sayfasını görüntüleyeceksiniz`,
    category: 'Sayfa İçeriği',
    estimatedContent: 'Sayfa içeriği ve ilgili bilgiler'
  };
}

/**
 * Get robot-specific preview information
 */
function getRobotPreview(_robotId: string, title: string) {
  return {
    description: `${title} robotunun detaylarını ve performansını görüntüleyeceksiniz`,
    features: ['Robot stratejisi', 'Performans geçmişi', 'Abonelik bilgileri', 'Risk profili'],
    category: 'Robot Detayları',
    estimatedContent: 'Robot performansı, strateji açıklaması ve abonelik seçenekleri'
  };
}

/**
 * Get user-specific preview information
 */
function getUserPreview(_userId: string, title: string) {
  return {
    description: `${title} kullanıcısının profilini görüntüleyeceksiniz`,
    features: ['Kullanıcı bilgileri', 'Paylaşılan robotlar', 'Performans geçmişi', 'İletişim seçenekleri'],
    category: 'Kullanıcı Profili',
    estimatedContent: 'Kullanıcı profili, robotları ve genel bilgiler'
  };
}

/**
 * Get documentation-specific preview information
 */
function getDocumentationPreview(_docId: string, title: string) {
  return {
    description: `${title} konusunda yardım ve dokümantasyon`,
    features: ['Adım adım rehber', 'Örnekler', 'SSS', 'Video eğitimler'],
    category: 'Yardım ve Dokümantasyon',
    estimatedContent: 'Detaylı açıklamalar, örnekler ve kullanım kılavuzu'
  };
}
