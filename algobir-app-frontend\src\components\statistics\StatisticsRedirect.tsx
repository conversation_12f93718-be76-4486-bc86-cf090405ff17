import React from 'react';
import { useSearchParams, Navigate } from 'react-router-dom';

/**
 * StatisticsRedirect Component
 *
 * Handles backward compatibility by redirecting old query parameter URLs
 * to the new nested route structure.
 *
 * Old format: /statistics?tab=overview
 * New format: /statistics/overview
 */
const StatisticsRedirect: React.FC = () => {
  const [searchParams] = useSearchParams();
  const tab = searchParams.get('tab');

  // Map old tab parameter values to new route paths
  const tabRouteMap: Record<string, string> = {
    'overview': 'overview',
    'roi-analysis': 'roi-analysis',
    'solo-robot': 'solo-robot',
    'bro-robots': 'bro-robots',
    'robot-comparison': 'robot-comparison',
    'performance': 'performance',
    'risk-analysis': 'risk-analysis',
    'time-analysis': 'time-analysis',
    'symbol-analysis': 'symbol-analysis',
    'reports': 'reports'
  };

  if (tab) {
    const newRoute = tabRouteMap[tab];
    if (newRoute) {
      // Redirect to new nested route structure
      return <Navigate to={`/statistics/${newRoute}`} replace />;
    } else {
      // If tab parameter is invalid, redirect to overview
      return <Navigate to="/statistics/overview" replace />;
    }
  } else {
    // If no tab parameter, redirect to overview (default)
    return <Navigate to="/statistics/overview" replace />;
  }
};

export default StatisticsRedirect;
