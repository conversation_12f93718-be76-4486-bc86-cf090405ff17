import React from 'react';
import {
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  Icon
} from '@chakra-ui/react';
import { FaRocket } from 'react-icons/fa';
import OrderTransmissionSpeedChart from './OrderTransmissionSpeedChart';
import OrderTransmissionMetricsDashboard from './OrderTransmissionMetricsDashboard';

interface OrderTransmissionMonitoringWrapperProps {
  timeRangeHours?: number;
  autoRefresh?: boolean;
}

const OrderTransmissionMonitoringWrapper: React.FC<OrderTransmissionMonitoringWrapperProps> = ({
  timeRangeHours = 24,
  autoRefresh = true
}) => {
  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  return (
    <VStack spacing={6} align="stretch">
      {/* Order Transmission Speed Chart */}
      <Card
        bg={cardBg}
        borderRadius="2xl"
        boxShadow="xl"
        border="1px solid"
        borderColor={borderColor}
        overflow="hidden"
      >
        <CardHeader pb={4}>
          <HStack spacing={4}>
            <Icon as={FaRocket} color="orange.500" boxSize={6} />
            <VStack align="start" spacing={1}>
              <Heading size="md" color={textColor}>
                Emir İletim Hızı - Canlı Grafik
              </Heading>
              <Text fontSize="sm" color={mutedColor}>
                TradingView sinyallerinden webhook iletimi performansı
              </Text>
            </VStack>
          </HStack>
        </CardHeader>
        <CardBody pt={0}>
          <OrderTransmissionSpeedChart
            timeRangeHours={timeRangeHours}
            height={400}
            showControls={true}
            autoRefresh={autoRefresh}
          />
        </CardBody>
      </Card>

      {/* Order Transmission Performance Dashboard */}
      <Card
        bg={cardBg}
        borderRadius="2xl"
        boxShadow="xl"
        border="1px solid"
        borderColor={borderColor}
        overflow="hidden"
      >
        <CardHeader pb={4}>
          <HStack spacing={4}>
            <Icon as={FaRocket} color="purple.500" boxSize={6} />
            <VStack align="start" spacing={1}>
              <Heading size="md" color={textColor}>
                Emir İletim Hızı - Performans Metrikleri
              </Heading>
              <Text fontSize="sm" color={mutedColor}>
                Detaylı performans analizi ve istatistikler
              </Text>
            </VStack>
          </HStack>
        </CardHeader>
        <CardBody pt={0}>
          <OrderTransmissionMetricsDashboard
            timeRangeHours={timeRangeHours}
          />
        </CardBody>
      </Card>
    </VStack>
  );
};

export default OrderTransmissionMonitoringWrapper;
