/**
 * Accessibility Utilities
 * 
 * Comprehensive accessibility tools for WCAG 2.1 AA compliance
 * Includes keyboard navigation, screen reader support, and focus management
 */

/**
 * ARIA live region types
 */
export type AriaLiveType = 'polite' | 'assertive' | 'off';

/**
 * Focus trap configuration
 */
interface FocusTrapConfig {
  initialFocus?: HTMLElement | string;
  returnFocus?: HTMLElement;
  escapeDeactivates?: boolean;
  clickOutsideDeactivates?: boolean;
}

/**
 * Keyboard navigation utilities
 */
export class KeyboardNavigation {
  /**
   * Check if element is focusable
   */
  static isFocusable(element: HTMLElement): boolean {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ];

    return focusableSelectors.some(selector => element.matches(selector)) ||
           (element.tabIndex >= 0 && !element.hasAttribute('disabled'));
  }

  /**
   * Get all focusable elements within a container
   */
  static getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ');

    return Array.from(container.querySelectorAll(focusableSelectors))
      .filter(el => this.isFocusable(el as HTMLElement)) as HTMLElement[];
  }

  /**
   * Handle arrow key navigation in a list
   */
  static handleArrowNavigation(
    event: KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    orientation: 'horizontal' | 'vertical' = 'vertical'
  ): number {
    const isVertical = orientation === 'vertical';
    const nextKey = isVertical ? 'ArrowDown' : 'ArrowRight';
    const prevKey = isVertical ? 'ArrowUp' : 'ArrowLeft';

    let newIndex = currentIndex;

    switch (event.key) {
      case nextKey:
        event.preventDefault();
        newIndex = (currentIndex + 1) % items.length;
        break;
      case prevKey:
        event.preventDefault();
        newIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
        break;
      case 'Home':
        event.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        event.preventDefault();
        newIndex = items.length - 1;
        break;
    }

    if (newIndex !== currentIndex && items[newIndex]) {
      items[newIndex].focus();
    }

    return newIndex;
  }

  /**
   * Create roving tabindex for a group of elements
   */
  static createRovingTabindex(elements: HTMLElement[], initialIndex = 0): () => void {
    elements.forEach((el, index) => {
      el.tabIndex = index === initialIndex ? 0 : -1;
    });

    const handleKeyDown = (event: KeyboardEvent) => {
      const currentIndex = elements.findIndex(el => el === event.target);
      if (currentIndex === -1) return;

      const newIndex = this.handleArrowNavigation(event, elements, currentIndex);
      
      // Update tabindex
      elements.forEach((el, index) => {
        el.tabIndex = index === newIndex ? 0 : -1;
      });
    };

    elements.forEach(el => {
      el.addEventListener('keydown', handleKeyDown);
    });

    // Return cleanup function
    return () => {
      elements.forEach(el => {
        el.removeEventListener('keydown', handleKeyDown);
      });
    };
  }
}

/**
 * Focus management utilities
 */
export class FocusManager {
  private static focusStack: HTMLElement[] = [];

  /**
   * Save current focus and set new focus
   */
  static saveFocus(newFocus?: HTMLElement): void {
    const currentFocus = document.activeElement as HTMLElement;
    if (currentFocus && currentFocus !== document.body) {
      this.focusStack.push(currentFocus);
    }

    if (newFocus) {
      newFocus.focus();
    }
  }

  /**
   * Restore previously saved focus
   */
  static restoreFocus(): void {
    const previousFocus = this.focusStack.pop();
    if (previousFocus && document.contains(previousFocus)) {
      previousFocus.focus();
    }
  }

  /**
   * Create a focus trap within a container
   */
  static createFocusTrap(container: HTMLElement, config: FocusTrapConfig = {}): () => void {
    const focusableElements = KeyboardNavigation.getFocusableElements(container);
    
    if (focusableElements.length === 0) {
      console.warn('No focusable elements found in focus trap container');
      return () => {};
    }

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    // Set initial focus
    if (config.initialFocus) {
      const initialElement = typeof config.initialFocus === 'string' 
        ? container.querySelector(config.initialFocus) as HTMLElement
        : config.initialFocus;
      
      if (initialElement) {
        initialElement.focus();
      } else {
        firstElement.focus();
      }
    } else {
      firstElement.focus();
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Tab') {
        if (event.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
          }
        } else {
          // Tab
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
          }
        }
      } else if (event.key === 'Escape' && config.escapeDeactivates !== false) {
        event.preventDefault();
        if (config.returnFocus) {
          config.returnFocus.focus();
        }
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (config.clickOutsideDeactivates && !container.contains(event.target as Node)) {
        if (config.returnFocus) {
          config.returnFocus.focus();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    if (config.clickOutsideDeactivates) {
      document.addEventListener('click', handleClickOutside);
    }

    // Return cleanup function
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      if (config.clickOutsideDeactivates) {
        document.removeEventListener('click', handleClickOutside);
      }
    };
  }
}

/**
 * Screen reader utilities
 */
export class ScreenReaderUtils {
  private static liveRegion: HTMLElement | null = null;

  /**
   * Initialize live region for announcements
   */
  static initializeLiveRegion(): void {
    if (this.liveRegion) return;

    this.liveRegion = document.createElement('div');
    this.liveRegion.setAttribute('aria-live', 'polite');
    this.liveRegion.setAttribute('aria-atomic', 'true');
    this.liveRegion.style.position = 'absolute';
    this.liveRegion.style.left = '-10000px';
    this.liveRegion.style.width = '1px';
    this.liveRegion.style.height = '1px';
    this.liveRegion.style.overflow = 'hidden';
    
    document.body.appendChild(this.liveRegion);
  }

  /**
   * Announce message to screen readers
   */
  static announce(message: string, priority: AriaLiveType = 'polite'): void {
    if (!this.liveRegion) {
      this.initializeLiveRegion();
    }

    if (this.liveRegion) {
      this.liveRegion.setAttribute('aria-live', priority);
      this.liveRegion.textContent = message;

      // Clear after announcement to allow repeated announcements
      setTimeout(() => {
        if (this.liveRegion) {
          this.liveRegion.textContent = '';
        }
      }, 1000);
    }
  }

  /**
   * Generate accessible description for complex UI elements
   */
  static generateDescription(element: HTMLElement): string {
    const descriptions: string[] = [];

    // Element type
    const role = element.getAttribute('role') || element.tagName.toLowerCase();
    descriptions.push(role);

    // Label or text content
    const label = element.getAttribute('aria-label') || 
                  element.getAttribute('aria-labelledby') ||
                  element.textContent?.trim();
    if (label) {
      descriptions.push(label);
    }

    // State information
    const expanded = element.getAttribute('aria-expanded');
    if (expanded === 'true') descriptions.push('expanded');
    if (expanded === 'false') descriptions.push('collapsed');

    const selected = element.getAttribute('aria-selected');
    if (selected === 'true') descriptions.push('selected');

    const checked = element.getAttribute('aria-checked') || 
                   (element as HTMLInputElement).checked;
    if (checked === true || checked === 'true') descriptions.push('checked');
    if (checked === false || checked === 'false') descriptions.push('unchecked');

    const disabled = element.hasAttribute('disabled') || 
                    element.getAttribute('aria-disabled') === 'true';
    if (disabled) descriptions.push('disabled');

    return descriptions.join(', ');
  }
}

/**
 * Color contrast utilities
 */
export class ColorContrastUtils {
  /**
   * Calculate relative luminance of a color
   */
  static getRelativeLuminance(r: number, g: number, b: number): number {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  /**
   * Calculate contrast ratio between two colors
   */
  static getContrastRatio(color1: [number, number, number], color2: [number, number, number]): number {
    const l1 = this.getRelativeLuminance(...color1);
    const l2 = this.getRelativeLuminance(...color2);
    
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Check if color combination meets WCAG contrast requirements
   */
  static meetsContrastRequirement(
    foreground: [number, number, number],
    background: [number, number, number],
    level: 'AA' | 'AAA' = 'AA',
    size: 'normal' | 'large' = 'normal'
  ): boolean {
    const ratio = this.getContrastRatio(foreground, background);
    
    const requirements = {
      'AA': { normal: 4.5, large: 3 },
      'AAA': { normal: 7, large: 4.5 }
    };
    
    return ratio >= requirements[level][size];
  }
}

/**
 * Semantic HTML utilities
 */
export class SemanticHTMLUtils {
  /**
   * Generate appropriate ARIA attributes for common UI patterns
   */
  static getMenuAttributes(isOpen: boolean) {
    return {
      role: 'menu',
      'aria-expanded': isOpen.toString(),
      'aria-hidden': (!isOpen).toString()
    };
  }

  static getMenuItemAttributes(index: number, total: number) {
    return {
      role: 'menuitem',
      'aria-setsize': total.toString(),
      'aria-posinset': (index + 1).toString()
    };
  }

  static getTabAttributes(isSelected: boolean, controls: string) {
    return {
      role: 'tab',
      'aria-selected': isSelected.toString(),
      'aria-controls': controls,
      tabIndex: isSelected ? 0 : -1
    };
  }

  static getTabPanelAttributes(labelledBy: string) {
    return {
      role: 'tabpanel',
      'aria-labelledby': labelledBy
    };
  }

  static getDialogAttributes(labelledBy?: string, describedBy?: string) {
    return {
      role: 'dialog',
      'aria-modal': 'true',
      ...(labelledBy && { 'aria-labelledby': labelledBy }),
      ...(describedBy && { 'aria-describedby': describedBy })
    };
  }
}

/**
 * Initialize accessibility utilities
 */
export function initializeAccessibility(): void {
  // Initialize screen reader live region
  ScreenReaderUtils.initializeLiveRegion();

  // Add skip link if not present
  if (!document.querySelector('[data-skip-link]')) {
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Ana içeriğe geç';
    skipLink.setAttribute('data-skip-link', 'true');
    skipLink.style.position = 'absolute';
    skipLink.style.left = '-10000px';
    skipLink.style.top = 'auto';
    skipLink.style.width = '1px';
    skipLink.style.height = '1px';
    skipLink.style.overflow = 'hidden';
    
    skipLink.addEventListener('focus', () => {
      skipLink.style.left = '6px';
      skipLink.style.top = '7px';
      skipLink.style.width = 'auto';
      skipLink.style.height = 'auto';
      skipLink.style.padding = '8px';
      skipLink.style.background = '#000';
      skipLink.style.color = '#fff';
      skipLink.style.textDecoration = 'none';
      skipLink.style.borderRadius = '4px';
      skipLink.style.zIndex = '9999';
    });
    
    skipLink.addEventListener('blur', () => {
      skipLink.style.left = '-10000px';
      skipLink.style.top = 'auto';
      skipLink.style.width = '1px';
      skipLink.style.height = '1px';
      skipLink.style.padding = '0';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
  }

  console.log('🎯 Accessibility utilities initialized');
}
