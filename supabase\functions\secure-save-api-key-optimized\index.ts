/**
 * Optimized Secure API Key Storage Function
 * Phase 2.4: Supabase Edge Functions Performance Optimization
 * Enhanced with performance monitoring, caching, and error handling
 */

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { getCorsHeaders } from '../_shared/cors.ts';
import { 
  createPerformanceMonitor, 
  cache, 
  dbOptimizer, 
  responseOptimizer,
  rateLimiter,
  memoryManager
} from '../_shared/performance-optimizer.ts';

// Environment variables with validation
const ENV_VARS = {
  supabaseUrl: Deno.env.get('SUPABASE_URL'),
  serviceRoleKey: Deno.env.get('SUPABASE_SERVICE_ROLE_KEY'),
  projectRefId: Deno.env.get('PROJECT_REF_ID'),
  managementApiToken: Deno.env.get('MANAGEMENT_API_TOKEN')
};

// Validate environment variables on startup
function validateEnvironment(): string[] {
  const missing: string[] = [];
  Object.entries(ENV_VARS).forEach(([key, value]) => {
    if (!value) missing.push(key);
  });
  return missing;
}

// Optimized Vault operations with retry logic and caching
class VaultManager {
  private static retryAttempts = 3;
  private static retryDelay = 1000; // 1 second

  static async upsertSecret(
    secretName: string, 
    secretValue: string,
    monitor: any
  ): Promise<{ success: boolean; error?: any }> {
    const cacheKey = `vault_status_${secretName}`;
    
    // Check if we recently failed for this secret
    const recentFailure = cache.get(cacheKey);
    if (recentFailure && recentFailure.failed) {
      return { success: false, error: { message: 'Recent failure cached' } };
    }

    monitor.startDbQuery();
    
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const response = await fetch(
          `https://api.supabase.com/v1/projects/${ENV_VARS.projectRefId}/secrets`,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${ENV_VARS.managementApiToken}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify([{ name: secretName, value: secretValue }])
          }
        );

        monitor.endDbQuery();

        if (response.ok) {
          // Cache success for 5 minutes
          cache.set(cacheKey, { success: true }, 300000);
          return { success: true };
        }

        const errorBody = await response.text();
        
        // Don't retry on client errors (4xx)
        if (response.status >= 400 && response.status < 500) {
          cache.set(cacheKey, { failed: true }, 60000); // Cache failure for 1 minute
          return { 
            success: false, 
            error: { status: response.status, message: errorBody } 
          };
        }

        // Retry on server errors (5xx)
        if (attempt < this.retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
          continue;
        }

        return { 
          success: false, 
          error: { status: response.status, message: errorBody } 
        };

      } catch (error) {
        monitor.endDbQuery();
        
        if (attempt < this.retryAttempts) {
          await new Promise(resolve => setTimeout(resolve, this.retryDelay * attempt));
          continue;
        }

        cache.set(cacheKey, { failed: true }, 60000);
        return { 
          success: false, 
          error: { message: error instanceof Error ? error.message : String(error) } 
        };
      }
    }

    return { success: false, error: { message: 'Max retry attempts exceeded' } };
  }
}

// Optimized user validation with caching
async function validateUser(jwt: string, supabase: any, monitor: any): Promise<{ valid: boolean; userId?: string; error?: string }> {
  const cacheKey = `user_validation_${jwt.slice(-10)}`; // Use last 10 chars as cache key
  
  // Check cache first
  const cached = cache.get(cacheKey);
  if (cached) {
    monitor.recordCacheHit();
    return cached;
  }

  monitor.recordCacheMiss();
  
  try {
    const result = await dbOptimizer.executeQuery(
      supabase,
      `user_auth_${jwt.slice(-10)}`,
      async () => {
        const { data, error } = await supabase.auth.getUser(jwt);
        return { data, error };
      },
      60000, // 1 minute cache
      monitor
    );

    if (result.error || !result.data.user) {
      const response = { valid: false, error: result.error?.message || 'Invalid token' };
      cache.set(cacheKey, response, 30000); // Cache failures for 30 seconds
      return response;
    }

    const response = { valid: true, userId: result.data.user.id };
    cache.set(cacheKey, response, 300000); // Cache success for 5 minutes
    return response;

  } catch (error) {
    const response = { valid: false, error: error instanceof Error ? error.message : 'Validation failed' };
    cache.set(cacheKey, response, 30000);
    return response;
  }
}

serve(async (req) => {
  const monitor = createPerformanceMonitor();
  const corsHeaders = getCorsHeaders(req.headers);
  
  // Set request size for monitoring
  const requestBody = await req.text();
  monitor.setRequestSize(new TextEncoder().encode(requestBody).length);

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return responseOptimizer.createJsonResponse('ok', 200, corsHeaders, monitor);
  }

  // Method validation
  if (req.method !== 'POST') {
    return responseOptimizer.createErrorResponse(
      'Method Not Allowed. Only POST is accepted.',
      405,
      corsHeaders,
      monitor
    );
  }

  // Rate limiting
  const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
  const rateLimit = rateLimiter.checkLimit(clientIP, 30, 60000); // 30 requests per minute
  
  if (!rateLimit.allowed) {
    const rateLimitHeaders = new Headers(corsHeaders);
    rateLimitHeaders.set('Retry-After', '60');
    rateLimitHeaders.set('X-RateLimit-Remaining', rateLimit.remaining.toString());
    
    return responseOptimizer.createErrorResponse(
      'Rate limit exceeded. Maximum 30 requests per minute.',
      429,
      rateLimitHeaders,
      monitor
    );
  }

  // Environment validation
  const missingEnvVars = validateEnvironment();
  if (missingEnvVars.length > 0) {
    return responseOptimizer.createErrorResponse(
      `Missing environment variables: ${missingEnvVars.join(', ')}`,
      500,
      corsHeaders,
      monitor
    );
  }

  try {
    // Memory check
    const memoryStatus = memoryManager.checkMemoryUsage();
    if (memoryStatus.needsCleanup) {
      console.warn('[secure-save-api-key] High memory usage detected, cleanup performed');
    }

    // Initialize Supabase client
    const supabase = createClient(ENV_VARS.supabaseUrl!, ENV_VARS.serviceRoleKey!);

    // User authentication
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return responseOptimizer.createErrorResponse(
        'Missing or invalid Authorization header',
        401,
        corsHeaders,
        monitor
      );
    }

    const jwt = authHeader.replace('Bearer ', '');
    const userValidation = await validateUser(jwt, supabase, monitor);
    
    if (!userValidation.valid) {
      return responseOptimizer.createErrorResponse(
        userValidation.error || 'Authentication failed',
        401,
        corsHeaders,
        monitor
      );
    }

    const userId = userValidation.userId!;

    // Parse and validate request body
    if (!requestBody.trim()) {
      return responseOptimizer.createErrorResponse(
        'Request body cannot be empty',
        400,
        corsHeaders,
        monitor
      );
    }

    let body: any;
    try {
      body = JSON.parse(requestBody);
    } catch {
      return responseOptimizer.createErrorResponse(
        'Invalid JSON in request body',
        400,
        corsHeaders,
        monitor
      );
    }

    const apiKeyPresent = 'apiKey' in body;
    const tokenPresent = 'token' in body;

    if (!apiKeyPresent && !tokenPresent) {
      return responseOptimizer.createErrorResponse(
        'No API key or token field provided',
        400,
        corsHeaders,
        monitor
      );
    }

    // Process API key and token operations
    const updates: any = {};
    const vaultOps: any[] = [];
    let vaultSuccess = true;
    const vaultErrors: string[] = [];

    // Process API Key
    if (apiKeyPresent) {
      const secretName = `user_${userId}_api_key`;
      const apiKeyValue = body.apiKey;
      
      if (apiKeyValue === null || apiKeyValue === '') {
        updates.api_key_set = false;
        updates.encrypted_api_key = null;
        
        const result = await VaultManager.upsertSecret(secretName, '', monitor);
        vaultOps.push({ type: 'api_key', action: 'clear', result });
        
        if (!result.success) {
          vaultSuccess = false;
          vaultErrors.push(`API Key Clear: ${result.error?.message}`);
        }
      } else {
        updates.api_key_set = true;
        updates.encrypted_api_key = apiKeyValue;
        
        const result = await VaultManager.upsertSecret(secretName, apiKeyValue, monitor);
        vaultOps.push({ type: 'api_key', action: 'save', result });
        
        if (!result.success) {
          vaultSuccess = false;
          vaultErrors.push(`API Key Save: ${result.error?.message}`);
        }
      }
    }

    // Process Token (similar logic)
    if (tokenPresent) {
      const secretName = `user_${userId}_token`;
      const tokenValue = body.token;
      
      if (tokenValue === null || tokenValue === '') {
        updates.token_set = false;
        updates.encrypted_token = null;
        
        const result = await VaultManager.upsertSecret(secretName, '', monitor);
        vaultOps.push({ type: 'token', action: 'clear', result });
        
        if (!result.success) {
          vaultSuccess = false;
          vaultErrors.push(`Token Clear: ${result.error?.message}`);
        }
      } else {
        updates.token_set = true;
        updates.encrypted_token = tokenValue;
        
        const result = await VaultManager.upsertSecret(secretName, tokenValue, monitor);
        vaultOps.push({ type: 'token', action: 'save', result });
        
        if (!result.success) {
          vaultSuccess = false;
          vaultErrors.push(`Token Save: ${result.error?.message}`);
        }
      }
    }

    // Update database with optimized query
    let dbSuccess = true;
    let dbError = null;

    if (Object.keys(updates).length > 0) {
      const dbResult = await dbOptimizer.executeQuery(
        supabase,
        `user_settings_update_${userId}`,
        async () => {
          const { error } = await supabase
            .from('user_settings')
            .update(updates)
            .eq('id', userId);
          return { data: true, error };
        },
        0, // Don't cache updates
        monitor
      );

      if (dbResult.error) {
        dbSuccess = false;
        dbError = dbResult.error;
      }
    }

    // Prepare response
    const summary = {
      api_key: apiKeyPresent ? (body.apiKey === null || body.apiKey === '' ? 'cleared' : 'saved') : 'unchanged',
      token: tokenPresent ? (body.token === null || body.token === '' ? 'cleared' : 'saved') : 'unchanged',
      vault_success: vaultSuccess,
      db_success: dbSuccess
    };

    const responseData = {
      success: true,
      message: dbSuccess ? 'Secrets processed successfully' : 'Secrets processed, but database update failed',
      summary,
      warning: !vaultSuccess ? `Vault operations failed: ${vaultErrors.join('; ')}` : 
               !dbSuccess ? `Database update failed: ${dbError?.message}` : null,
      performance: monitor.getCurrentMetrics()
    };

    return responseOptimizer.createJsonResponse(responseData, 200, corsHeaders, monitor);

  } catch (error) {
    console.error('[secure-save-api-key-optimized] Unexpected error:', error);
    
    const statusCode = error.message?.includes('Authorization') ? 401 :
                      error.message?.includes('JSON') ? 400 : 500;

    return responseOptimizer.createErrorResponse(
      statusCode === 500 ? 'Internal server error' : error.message,
      statusCode,
      corsHeaders,
      monitor
    );
  }
});
