import React from 'react';
import {
  Box,
  Flex,
  Text,
  Icon,
  useColorModeValue,
  Skeleton,
  Badge,
  VStack,
  HStack
} from '@chakra-ui/react';
import { keyframes } from '@emotion/react';
import { IconType } from 'react-icons';
import Card from '../card/Card';

// Animation keyframes
const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const pulse = keyframes`
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

interface TradingStatCardProps {
  title: string;
  value: string | number;
  icon: IconType;
  colorScheme?: string;
  isLoading?: boolean;
  isError?: boolean;
  errorMessage?: string;
  helpText?: string;
  trend?: {
    value: string;
    isPositive: boolean;
  };
  badge?: {
    text: string;
    colorScheme: string;
  };
  onClick?: () => void;
  isClickable?: boolean;
}

const TradingStatCard: React.FC<TradingStatCardProps> = ({
  title,
  value,
  icon,
  colorScheme = 'brand',
  isLoading = false,
  isError = false,
  errorMessage = 'Veri yüklenemedi',
  helpText,
  trend,
  badge,
  onClick,
  isClickable = false
}) => {
  // Theme colors
  const cardBg = useColorModeValue('white', 'navy.800');
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  const textColorSecondary = useColorModeValue('secondaryGray.600', 'secondaryGray.300');
  const iconColor = useColorModeValue(`${colorScheme}.500`, `${colorScheme}.400`);
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');
  const hoverBg = useColorModeValue('gray.50', 'whiteAlpha.50');
  
  // Responsive font sizes
  const titleFontSize = { base: 'sm', md: 'md' };
  const valueFontSize = { base: 'xl', md: '2xl', lg: '3xl' };
  const iconSize = { base: '24px', md: '28px', lg: '32px' };

  const cardContent = (
    <Card
      bg={cardBg}
      border="1px solid"
      borderColor={borderColor}
      borderRadius="20px"
      p={{ base: '16px', md: '20px' }}
      h="100%"
      minH={{ base: '120px', md: '140px' }}
      position="relative"
      cursor={isClickable ? 'pointer' : 'default'}
      animation={`${fadeInUp} 0.6s ease-out`}
      transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
      data-testid="trading-stat-card"
      _hover={isClickable ? {
        transform: 'translateY(-4px) scale(1.02)',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        bg: hoverBg,
        borderColor: iconColor,
        '& .card-icon': {
          animation: `${pulse} 1s infinite`,
        }
      } : {
        '&:hover': {
          transform: 'translateY(-1px)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        }
      }}
      _active={isClickable ? {
        transform: 'translateY(-2px) scale(1.01)'
      } : {}}
      onClick={onClick}
      sx={{
        '&:focus-visible': {
          outline: '2px solid',
          outlineColor: iconColor,
          outlineOffset: '2px',
        }
      }}
    >
      <Flex direction="column" h="100%" justify="space-between">
        {/* Header with icon and badge */}
        <Flex justify="space-between" align="flex-start" mb={3}>
          <Icon
            as={icon}
            w={iconSize}
            h={iconSize}
            color={iconColor}
            flexShrink={0}
            className="card-icon"
            transition="all 0.3s ease"
          />
          {badge && (
            <Badge
              colorScheme={badge.colorScheme}
              variant="subtle"
              fontSize="xs"
              px={2}
              py={1}
              borderRadius="md"
              animation={`${fadeInUp} 0.8s ease-out`}
            >
              {badge.text}
            </Badge>
          )}
        </Flex>

        {/* Main content */}
        <VStack align="flex-start" spacing={1} flex={1}>
          <Text
            fontSize={titleFontSize}
            color={textColorSecondary}
            fontWeight="600"
            lineHeight="1.2"
            noOfLines={2}
          >
            {title}
          </Text>
          
          <Skeleton
            isLoaded={!isLoading}
            borderRadius="md"
            startColor="gray.100"
            endColor="gray.300"
            sx={{
              '&[data-loaded="false"]': {
                background: `linear-gradient(90deg, #f0f0f0 0px, #e0e0e0 40px, #f0f0f0 80px)`,
                backgroundSize: '200px',
                animation: `${shimmer} 1.5s infinite linear`,
              }
            }}
          >
            {isError ? (
              <Text
                fontSize="sm"
                color="red.500"
                fontWeight="500"
                lineHeight="1.2"
                noOfLines={2}
                animation={`${fadeInUp} 0.5s ease-out`}
              >
                {errorMessage}
              </Text>
            ) : (
              <Text
                fontSize={valueFontSize}
                color={textColor}
                fontWeight="700"
                lineHeight="1.1"
                noOfLines={1}
                wordBreak="break-all"
                animation={`${fadeInUp} 1s ease-out`}
              >
                {value}
              </Text>
            )}
          </Skeleton>
        </VStack>

        {/* Footer with trend or help text */}
        {(trend || helpText) && (
          <Box mt={2}>
            {trend && (
              <HStack spacing={1}>
                <Text
                  fontSize="xs"
                  color={trend.isPositive ? 'green.500' : 'red.500'}
                  fontWeight="600"
                >
                  {trend.value}
                </Text>
                <Text
                  fontSize="xs"
                  color={textColorSecondary}
                  fontWeight="400"
                >
                  önceki döneme göre
                </Text>
              </HStack>
            )}
            {helpText && !trend && (
              <Text
                fontSize="xs"
                color={textColorSecondary}
                fontWeight="400"
                noOfLines={1}
              >
                {helpText}
              </Text>
            )}
          </Box>
        )}
      </Flex>
    </Card>
  );

  return cardContent;
};

export default TradingStatCard;
