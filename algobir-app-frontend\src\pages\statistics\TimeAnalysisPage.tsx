import React, { useState, useMemo, useCallback, useEffect } from 'react';
import {
  VStack,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  HStack,
  Box,
  Select,
  Button,
  ButtonGroup,
  Badge,
  Flex,

  Tooltip,
  IconButton,
  useColorModeValue,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress
} from '@chakra-ui/react';
import {
  FiCalendar,
  FiClock,
  FiTrendingUp,
  FiTrendingDown,
  FiBarChart2,
  FiFilter,
  FiRefreshCw,
  FiDownload
} from 'react-icons/fi';
import { useEnhancedStatistics } from '../../hooks/useEnhancedStatistics';
import { useStatisticsExport } from '../../hooks/useStatisticsExport';
import AdvancedLineChart from '../../components/statistics/charts/AdvancedLineChart';
import InteractivePieChart from '../../components/statistics/charts/InteractivePieChart';
import HeatmapChart from '../../components/statistics/charts/HeatmapChart';
import StatisticsExport from '../../components/statistics/StatisticsExport';

const TimeAnalysisPage: React.FC = () => {
  const { stats, loading, error, refetch } = useEnhancedStatistics();
  const { isExporting } = useStatisticsExport();
  const [timeFilter, setTimeFilter] = useState<'all' | '1m' | '3m' | '6m' | '1y'>('3m');
  const [viewMode, setViewMode] = useState<'pnl' | 'trades' | 'winrate'>('pnl');
  const [selectedPeriod, setSelectedPeriod] = useState<'hourly' | 'daily' | 'weekly' | 'monthly'>('daily');
  const [selectedRobots, setSelectedRobots] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [dateRange, setDateRange] = useState<{ start: Date | null; end: Date | null }>({
    start: null,
    end: null
  });
  const [isDataLoaded, setIsDataLoaded] = useState(false);

  // Performance optimization: Debounced filter updates
  const [filterUpdateTimeout] = useState<NodeJS.Timeout | null>(null);

  // Memoized filter update function


  // Track data loading state
  useEffect(() => {
    if (!loading) {
      // Set data loaded to true when loading is complete, regardless of whether we have real data
      // If no real data is available, we'll use sample data
      setIsDataLoaded(true);
    }
  }, [loading]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (filterUpdateTimeout) {
        clearTimeout(filterUpdateTimeout);
      }
    };
  }, [filterUpdateTimeout]);

  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');




  // Enhanced formatting functions with comprehensive null/undefined handling
  const formatCurrency = (value: number | null | undefined): string => {
    if (value === null || value === undefined || isNaN(Number(value))) {
      return '₺0.00';
    }
    const numValue = Number(value);
    return `₺${numValue.toFixed(2)}`;
  };

  const formatPercentage = (value: number | null | undefined): string => {
    if (value === null || value === undefined || isNaN(Number(value))) {
      return '0.00%';
    }
    const numValue = Number(value);
    return `${numValue.toFixed(2)}%`;
  };



  // Generate sample data for demonstration when real data is not available
  const generateSampleData = useCallback(() => {
    // Sample hourly performance data (24 hours)
    const sampleHourlyPerformance = Array.from({ length: 24 }, (_, hour) => {
      const basePerformance = Math.sin((hour - 10) * Math.PI / 12) * 500; // Peak around 10-14 hours
      const randomVariation = (Math.random() - 0.5) * 200;
      const pnl = basePerformance + randomVariation;
      const trades = Math.floor(Math.random() * 15) + 1;
      const winRate = 45 + Math.random() * 30; // 45-75% win rate

      return {
        hour,
        pnl: Math.round(pnl * 100) / 100,
        trades,
        winRate: Math.round(winRate * 100) / 100
      };
    });

    // Sample daily performance data (last 90 days)
    const sampleDailyPerformance = Array.from({ length: 90 }, (_, index) => {
      const date = new Date();
      date.setDate(date.getDate() - (89 - index));
      const trend = index * 2; // Upward trend
      const volatility = (Math.random() - 0.5) * 800;
      const pnl = trend + volatility;
      const trades = Math.floor(Math.random() * 25) + 5;


      return {
        day: date.toISOString().split('T')[0],
        pnl: Math.round(pnl * 100) / 100,
        trades
      };
    });

    // Sample weekly performance data (7 days)

    const sampleWeeklyPerformance = Array.from({ length: 7 }, (_, dayOfWeek) => {
      // Weekdays typically perform better
      const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5;
      const basePnl = isWeekday ? 300 + Math.random() * 400 : 100 + Math.random() * 200;
      const trades = isWeekday ? Math.floor(Math.random() * 20) + 10 : Math.floor(Math.random() * 8) + 2;


      return {
        week: `Week ${dayOfWeek + 1}`,
        pnl: Math.round(basePnl * 100) / 100,
        trades
      };
    });

    // Sample monthly ROI data (last 12 months)
    const sampleMonthlyROI = Array.from({ length: 12 }, (_, index) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (11 - index));
      const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      const roi = 2 + Math.random() * 8; // 2-10% monthly ROI
      const investment = 10000 + Math.random() * 5000;
      const returns = (investment * roi) / 100;

      return {
        month,
        roi: Math.round(roi * 100) / 100,
        investment: Math.round(investment * 100) / 100,
        returns: Math.round(returns * 100) / 100
      };
    });

    // Sample robot stats
    const sampleSoloRobotStats = {
      robotId: 'solo',
      robotName: 'Solo Robot',
      robotType: 'solo' as const,
      totalTrades: 245,
      winningTrades: 147,
      losingTrades: 98,
      winRate: 60.0,
      totalPnl: 12450.75,
      totalInvestment: 50000,
      roi: 24.9,
      annualizedROI: 29.8,
      profitFactor: 1.85,
      maxDrawdown: 8.5,
      sharpeRatio: 1.42,
      averageWin: 185.50,
      averageLoss: -95.25,
      largestWin: 850.00,
      largestLoss: -420.00,
      consecutiveWins: 8,
      consecutiveLosses: 4,
      averageHoldingTime: 2.5,
      totalCommissions: 245.00,
      timePerformance: [],
      averageTradeSize: 2040.82,
      bestTrade: 850.00,
      worstTrade: -420.00,
      monthlyPerformance: [],
      symbolPerformance: []
    };

    const sampleBroRobotStats = [
      {
        robotId: 'bro-1',
        robotName: 'Trend Takipçisi',
        robotType: 'bro' as const,
        totalTrades: 189,
        winningTrades: 121,
        losingTrades: 68,
        winRate: 64.0,
        totalPnl: 8920.50,
        totalInvestment: 30000,
        roi: 29.7,
        annualizedROI: 35.6,
        profitFactor: 1.92,
        maxDrawdown: 6.8,
        sharpeRatio: 1.58,
        averageWin: 195.75,
        averageLoss: -88.50,
        largestWin: 920.00,
        largestLoss: -380.00,
        consecutiveWins: 9,
        consecutiveLosses: 3,
        averageHoldingTime: 1.8,
        totalCommissions: 189.00,
        timePerformance: [],
        averageTradeSize: 1890.50,
        bestTrade: 920.00,
        worstTrade: -380.00,
        monthlyPerformance: [],
        symbolPerformance: []
      },
      {
        robotId: 'bro-2',
        robotName: 'Scalping Master',
        robotType: 'bro' as const,
        totalTrades: 456,
        winningTrades: 267,
        losingTrades: 189,
        winRate: 58.6,
        totalPnl: 15670.25,
        totalInvestment: 45000,
        roi: 34.8,
        annualizedROI: 41.8,
        profitFactor: 1.76,
        maxDrawdown: 9.2,
        sharpeRatio: 1.35,
        averageWin: 165.25,
        averageLoss: -102.75,
        largestWin: 780.00,
        largestLoss: -450.00,
        consecutiveWins: 7,
        consecutiveLosses: 5,
        averageHoldingTime: 0.8,
        totalCommissions: 456.00,
        timePerformance: [],
        averageTradeSize: 1567.25,
        bestTrade: 780.00,
        worstTrade: -450.00,
        monthlyPerformance: [],
        symbolPerformance: []
      }
    ];

    return {
      hourlyPerformance: sampleHourlyPerformance,
      dailyPerformance: sampleDailyPerformance,
      weeklyPerformance: sampleWeeklyPerformance,
      roiAnalysis: {
        monthlyROI: sampleMonthlyROI,
        totalROI: 28.5,
        annualizedROI: 35.2,
        totalInvestment: 125000,
        totalReturns: 35640.50
      },
      soloRobotStats: sampleSoloRobotStats,
      broRobotStats: sampleBroRobotStats
    };
  }, []);

  // Available robots for filtering with proper null checking
  const availableRobots = useMemo(() => {
    const robots = [];

    // Use real data if available, otherwise use sample data
    let soloRobotStats = stats?.soloRobotStats;
    let broRobotStats = stats?.broRobotStats || [];

    if (!soloRobotStats && broRobotStats.length === 0) {
      const sampleData = generateSampleData();
      soloRobotStats = sampleData.soloRobotStats;
      broRobotStats = sampleData.broRobotStats;
    }

    // Safely check for solo robot stats
    if (soloRobotStats) {
      robots.push({
        id: soloRobotStats.robotId,
        name: soloRobotStats.robotName,
        type: 'solo'
      });
    }

    // Safely check for bro robot stats
    if (broRobotStats && Array.isArray(broRobotStats)) {
      broRobotStats.forEach(robot => {
        robots.push({
          id: robot.robotId,
          name: robot.robotName,
          type: 'bro'
        });
      });
    }

    return robots;
  }, [stats?.soloRobotStats, stats?.broRobotStats, generateSampleData]);



  // Enhanced data preparation with memoization and comprehensive error handling
  const hourlyHeatmapData = useMemo(() => {
    try {
      if (!isDataLoaded) return [];

      // Use real data if available, otherwise use sample data
      let hourlyData = stats?.hourlyPerformance || [];
      if (hourlyData.length === 0) {
        const sampleData = generateSampleData();
        hourlyData = sampleData.hourlyPerformance;
      }

      // Validate hourly data structure
      if (!Array.isArray(hourlyData)) {
        console.warn('Invalid hourly data structure, using empty array');
        return [];
      }

      // Create a more comprehensive heatmap with multiple metrics
      const heatmapData: any[] = [];

      // Add current view mode data with error handling
      hourlyData.forEach((item: any) => {
        try {
          // Validate item structure
          if (!item || typeof item.hour !== 'number') {
            console.warn('Invalid hourly data item:', item);
            return;
          }

          const hour = Number(item.hour) || 0;
          const pnl = Number(item.pnl) || 0;
          const trades = Number(item.trades) || 0;
          const winRate = Number(item.winRate) || 0;

          heatmapData.push({
            x: hour,
            y: viewMode === 'pnl' ? 'Kar/Zarar' : viewMode === 'trades' ? 'İşlem Sayısı' : 'Kazanma Oranı',
            value: viewMode === 'pnl' ? pnl : viewMode === 'trades' ? trades : winRate,
            label: viewMode === 'pnl'
              ? `Saat ${hour}: ${formatCurrency(pnl)}`
              : viewMode === 'trades'
              ? `Saat ${hour}: ${trades} işlem`
              : `Saat ${hour}: ${formatPercentage(winRate)}`,
            rawData: item // Keep original data for reference
          });
        } catch (itemError) {
          console.error('Error processing hourly data item:', itemError, item);
        }
      });

      return heatmapData;
    } catch (error) {
      console.error('Error preparing hourly heatmap data:', error);
      return [];
    }
  }, [stats?.hourlyPerformance, viewMode, isDataLoaded, generateSampleData]);

  const dailyPerformanceData = useMemo(() => {
    if (!isDataLoaded) return [];

    // Use real data if available, otherwise use sample data
    let dailyData = stats?.dailyPerformance || [];
    if (dailyData.length === 0) {
      const sampleData = generateSampleData();
      dailyData = sampleData.dailyPerformance;
    }

    return dailyData.map((item: any) => ({
      date: item.day,
      value: viewMode === 'pnl' ? item.pnl : item.trades || 0,
      pnl: item.pnl,
      trades: item.trades || 0
    }));
  }, [stats?.dailyPerformance, viewMode, isDataLoaded, generateSampleData]);

  const monthlyTrendsData = useMemo(() => {
    if (!isDataLoaded) return [];

    // Use real data if available, otherwise use sample data
    let monthlyData = stats?.roiAnalysis?.monthlyROI || [];
    if (monthlyData.length === 0) {
      const sampleData = generateSampleData();
      monthlyData = sampleData.roiAnalysis.monthlyROI;
    }

    return monthlyData.map((item: any) => ({
      date: item.month,
      value: item.roi,
      investment: item.investment,
      returns: item.returns
    }));
  }, [stats?.roiAnalysis?.monthlyROI, isDataLoaded, generateSampleData]);

  // Weekly performance analysis with error handling
  const weeklyPerformanceData = useMemo(() => {
    if (!isDataLoaded) return [];

    // Use real data if available, otherwise use sample data
    let weeklyData = stats?.weeklyPerformance || [];
    if (weeklyData.length === 0) {
      const sampleData = generateSampleData();
      weeklyData = sampleData.weeklyPerformance;
    }

    const weekdays = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];
    return weekdays.map((day, index) => {
      const dayData = weeklyData[index] || { pnl: 0, trades: 0 };
      return {
        date: day,
        name: day,
        value: viewMode === 'pnl' ? (dayData.pnl || 0) : (dayData.trades || 0),
        pnl: dayData.pnl || 0,
        trades: dayData.trades || 0,
        color: index >= 1 && index <= 5 ? '#4299E1' : '#ED8936' // Weekdays vs weekends
      };
    });
  }, [stats?.weeklyPerformance, viewMode, isDataLoaded, generateSampleData]);

  // Enhanced time-based statistics with comprehensive error handling
  const timeStats = useMemo(() => {
    try {
      const bestHour = hourlyHeatmapData.length > 0 ?
        hourlyHeatmapData.reduce((best, current) =>
          (current?.value || 0) > (best?.value || 0) ? current : best) : null;
      const worstHour = hourlyHeatmapData.length > 0 ?
        hourlyHeatmapData.reduce((worst, current) =>
          (current?.value || 0) < (worst?.value || 0) ? current : worst) : null;

    // Best/worst day of the week (e.g., Monday, Tuesday, etc.)
    const bestDayOfWeek = weeklyPerformanceData.length > 0 ?
      weeklyPerformanceData.reduce((best, current) => current.pnl > best.pnl ? current : best) : null;
    const worstDayOfWeek = weeklyPerformanceData.length > 0 ?
      weeklyPerformanceData.reduce((worst, current) => current.pnl < worst.pnl ? current : worst) : null;

    // Best/worst specific trading day (specific date)
    const bestTradingDay = dailyPerformanceData.length > 0 ?
      dailyPerformanceData.reduce((best, current) => current.pnl > best.pnl ? current : best) : null;
    const worstTradingDay = dailyPerformanceData.length > 0 ?
      dailyPerformanceData.reduce((worst, current) => current.pnl < worst.pnl ? current : worst) : null;

    // Calculate transaction statistics using consistent data source
    // Use the same dailyData that we use for other calculations
    let transactionDailyData = stats?.dailyPerformance || [];
    if (transactionDailyData.length === 0) {
      const sampleData = generateSampleData();
      transactionDailyData = sampleData.dailyPerformance;
    }

    // Active trading days: only count days with actual trades
    const activeTradingDays = transactionDailyData.filter((day: any) => (day.trades || 0) > 0).length;
    const profitableDays = transactionDailyData.filter((day: any) => (day.pnl || 0) > 0).length;

    // Calculate total trades and profitable trades for win rate
    const totalTradesForStats = transactionDailyData.reduce((sum: number, day: any) => sum + (Number(day.trades) || 0), 0);

    // Calculate win rate - try to get from stats first, fallback to daily approximation
    let actualWinRate = 0;
    if (stats && stats.winRate !== undefined) {
      actualWinRate = stats.winRate;
    } else {
      // Fallback: use profitable days rate as approximation
      actualWinRate = activeTradingDays > 0 ? (profitableDays / activeTradingDays) * 100 : 0;
    }

    // Additional transaction metrics
    const totalPnlFromDays = transactionDailyData.reduce((sum: number, day: any) => sum + (Number(day.pnl) || 0), 0);
    const avgPnlPerTrade = totalTradesForStats > 0 ? totalPnlFromDays / totalTradesForStats : 0;

    // Calculate average performance by time periods
    // Use the original data sources to ensure we always get PnL values regardless of viewMode
    let hourlyData = stats?.hourlyPerformance || [];
    if (hourlyData.length === 0) {
      const sampleData = generateSampleData();
      hourlyData = sampleData.hourlyPerformance;
    }

    let dailyData = stats?.dailyPerformance || [];
    if (dailyData.length === 0) {
      const sampleData = generateSampleData();
      dailyData = sampleData.dailyPerformance;
    }

    // Calculate averages from the raw data (always PnL-based) with robust error handling
    const avgHourlyPnl = hourlyData.length > 0 ?
      Math.round((hourlyData.reduce((sum: number, hour: any) => sum + (Number(hour.pnl) || 0), 0) / hourlyData.length) * 100) / 100 : 0;

    const avgDailyPnl = dailyData.length > 0 ?
      Math.round((dailyData.reduce((sum: number, day: any) => sum + (Number(day.pnl) || 0), 0) / dailyData.length) * 100) / 100 : 0;

    // Additional average calculations for comprehensive analysis
    const avgHourlyTrades = hourlyData.length > 0 ?
      Math.round((hourlyData.reduce((sum: number, hour: any) => sum + (Number(hour.trades) || 0), 0) / hourlyData.length) * 10) / 10 : 0;

    const avgDailyTrades = dailyData.length > 0 ?
      Math.round((dailyData.reduce((sum: number, day: any) => sum + (Number(day.trades) || 0), 0) / dailyData.length) * 10) / 10 : 0;

    // Calculate average win rate with proper percentage handling
    const avgWinRate = dailyData.length > 0 ?
      Math.round((dailyData.reduce((sum: number, day: any) => sum + (Number(day.winRate) || 0), 0) / dailyData.length) * 100) / 100 : 0;

    return {
      bestHour: bestHour ? `${bestHour.x}:00` : 'N/A',
      worstHour: worstHour ? `${worstHour.x}:00` : 'N/A',
      bestHourValue: bestHour?.value || 0,
      worstHourValue: worstHour?.value || 0,
      bestDay: bestDayOfWeek?.name || 'N/A',
      worstDay: worstDayOfWeek?.name || 'N/A',
      bestDayValue: bestDayOfWeek?.pnl || 0,
      worstDayValue: worstDayOfWeek?.pnl || 0,
      // Additional metrics for specific trading days
      bestTradingDate: bestTradingDay?.date ? new Date(bestTradingDay.date).toLocaleDateString('tr-TR') : 'N/A',
      bestTradingDateValue: bestTradingDay?.pnl || 0,
      worstTradingDate: worstTradingDay?.date ? new Date(worstTradingDay.date).toLocaleDateString('tr-TR') : 'N/A',
      worstTradingDateValue: worstTradingDay?.pnl || 0,
      totalTradingDays: activeTradingDays,
      profitableDays,
      profitableRate: actualWinRate,
      avgPnlPerTrade: Math.round(avgPnlPerTrade * 100) / 100,
      avgHourlyPnl,
      avgDailyPnl,
      avgHourlyTrades,
      avgDailyTrades,
      avgWinRate,
      // Calculate total trades using the same data source as other transaction statistics
      totalTrades: totalTradesForStats,
      avgTradesPerDay: avgDailyTrades // Use the calculated average for consistency
    };
    } catch (error) {
      console.error('Error calculating time statistics:', error);
      // Return safe default values
      return {
        bestHour: 'N/A',
        worstHour: 'N/A',
        bestHourValue: 0,
        worstHourValue: 0,
        bestDay: 'N/A',
        worstDay: 'N/A',
        bestDayValue: 0,
        worstDayValue: 0,
        bestTradingDate: 'N/A',
        bestTradingDateValue: 0,
        worstTradingDate: 'N/A',
        worstTradingDateValue: 0,
        totalTradingDays: 0,
        profitableDays: 0,
        profitableRate: 0,
        avgPnlPerTrade: 0,
        avgHourlyPnl: 0,
        avgDailyPnl: 0,
        avgHourlyTrades: 0,
        avgDailyTrades: 0,
        avgWinRate: 0,
        totalTrades: 0,
        avgTradesPerDay: 0
      };
    }
  }, [hourlyHeatmapData, weeklyPerformanceData, dailyPerformanceData]);

  // Enhanced trading session analysis with comprehensive error handling
  const tradingSessionData = useMemo(() => {
    try {
      if (!isDataLoaded || hourlyHeatmapData.length === 0) {
        return {
          sessions: [],
          bestSession: null,
          worstSession: null
        };
      }

      const sessions = [
        { name: 'Sabah Seansı', timeRange: '09:00-12:00', start: 9, end: 12, value: 0, trades: 0, winRate: 0, color: '#4299E1' },
        { name: 'Öğle Seansı', timeRange: '12:00-15:00', start: 12, end: 15, value: 0, trades: 0, winRate: 0, color: '#48BB78' },
        { name: 'Akşam Seansı', timeRange: '15:00-18:00', start: 15, end: 18, value: 0, trades: 0, winRate: 0, color: '#ED8936' },
        { name: 'Gece Seansı', timeRange: '18:00-21:00', start: 18, end: 21, value: 0, trades: 0, winRate: 0, color: '#9F7AEA' }
      ];

      // Get the source hourly data (real or sample)
      let sourceHourlyData = stats?.hourlyPerformance || [];
      if (sourceHourlyData.length === 0) {
        const sampleData = generateSampleData();
        sourceHourlyData = sampleData.hourlyPerformance;
      }

      // Calculate session performance from hourly data
      hourlyHeatmapData.forEach(hour => {
        sessions.forEach(session => {
          if (hour.x >= session.start && hour.x < session.end) {
            // Add the current view mode value
            session.value += hour.value || 0;
            // Add trades and win rate calculations from source data
            const hourData = sourceHourlyData.find((h: any) => h.hour === hour.x);
            if (hourData) {
              session.trades += hourData.trades || 0;
            }
          }
        });
      });

      // Calculate average win rate for sessions and find best/worst sessions
      sessions.forEach(session => {
        const hoursInSession = session.end - session.start;
        session.winRate = hoursInSession > 0 ? session.winRate / hoursInSession : 0;
      });

      // Find best and worst performing sessions
      const bestSession = sessions.reduce((best, current) =>
        current.value > best.value ? current : best, sessions[0]);
      const worstSession = sessions.reduce((worst, current) =>
        current.value < worst.value ? current : worst, sessions[0]);

      return {
        sessions,
        bestSession,
        worstSession
      };
    } catch (error) {
      console.error('Error calculating session data:', error);
      return {
        sessions: [],
        bestSession: null,
        worstSession: null
      };
    }
  }, [hourlyHeatmapData, viewMode, stats?.hourlyPerformance, isDataLoaded, generateSampleData]);

  // Seasonal analysis with error handling and performance optimization
  const seasonalData = useMemo(() => {
    if (!isDataLoaded || monthlyTrendsData.length === 0) return [];

    const seasons = [
      { name: 'İlkbahar', months: [2, 3, 4], value: 0, color: '#48BB78' },
      { name: 'Yaz', months: [5, 6, 7], value: 0, color: '#ED8936' },
      { name: 'Sonbahar', months: [8, 9, 10], value: 0, color: '#D69E2E' },
      { name: 'Kış', months: [11, 0, 1], value: 0, color: '#4299E1' }
    ];

    try {
      monthlyTrendsData.forEach(month => {
        if (month.date) {
          const monthIndex = new Date(month.date).getMonth();
          seasons.forEach(season => {
            if (season.months.includes(monthIndex)) {
              season.value += month.value || 0;
            }
          });
        }
      });
    } catch (error) {
      console.error('Error calculating seasonal data:', error);
      return [];
    }

    return seasons;
  }, [monthlyTrendsData, isDataLoaded]);

  // Enhanced loading state with skeleton placeholders
  if (loading || !isDataLoaded) {
    return (
      <VStack spacing={6} align="stretch">
        <Flex justify="space-between" align="center">
          <VStack align="start" spacing={1}>
            <Text fontSize="2xl" fontWeight="bold" color={textColor}>
              Zaman Analizi
            </Text>
            <Text fontSize="sm" color="gray.500">
              {loading ? 'Veriler yükleniyor...' : 'Veriler hazırlanıyor...'}
            </Text>
          </VStack>
          <Progress size="lg" isIndeterminate colorScheme="blue" w="200px" />
        </Flex>

        {/* Skeleton placeholders for better UX */}
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} bg={bgColor} borderColor={borderColor} borderWidth="1px">
              <CardBody>
                <VStack align="start" spacing={3}>
                  <HStack>
                    <Box w={6} h={6} bg="gray.200" borderRadius="md" />
                    <Box w="100px" h={4} bg="gray.200" borderRadius="md" />
                  </HStack>
                  <Box w="80px" h={8} bg="gray.300" borderRadius="md" />
                  <Box w="120px" h={3} bg="gray.200" borderRadius="md" />
                </VStack>
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>

        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
          {[1, 2].map((i) => (
            <Card key={i} bg={bgColor} borderColor={borderColor} borderWidth="1px">
              <CardBody>
                <VStack spacing={4}>
                  <Box w="200px" h={6} bg="gray.200" borderRadius="md" />
                  <Box w="full" h="300px" bg="gray.100" borderRadius="md" />
                </VStack>
              </CardBody>
            </Card>
          ))}
        </SimpleGrid>
      </VStack>
    );
  }

  // Enhanced error state with retry options
  if (error || !stats) {
    return (
      <VStack spacing={6} align="stretch">
        <Card bg={bgColor} borderColor="red.200" borderWidth="2px">
          <CardBody>
            <VStack spacing={4} textAlign="center">
              <Box fontSize="4xl">⚠️</Box>
              <Text color="red.500" fontSize="lg" fontWeight="semibold">
                Veri Yükleme Hatası
              </Text>
              <Text color="gray.600" fontSize="md">
                Zaman analizi verileri yüklenirken bir hata oluştu. Lütfen tekrar deneyin.
              </Text>
              <HStack spacing={3}>
                <Button
                  leftIcon={<FiRefreshCw />}
                  onClick={refetch}
                  colorScheme="blue"
                  isLoading={loading}
                >
                  Tekrar Dene
                </Button>
                <Button
                  variant="outline"
                  onClick={() => window.location.reload()}
                >
                  Sayfayı Yenile
                </Button>
              </HStack>
              {error && (
                <Text fontSize="sm" color="gray.500" fontFamily="mono">
                  Hata detayı: {error.toString()}
                </Text>
              )}
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    );
  }

  return (
    <VStack spacing={6} align="stretch">
      {/* Header with Controls */}
      <Flex align="center" justify="space-between" wrap="wrap" gap={4}>
        <VStack align="start" spacing={1}>
          <Text fontSize="2xl" fontWeight="bold" color={textColor}>
            Zaman Analizi
          </Text>
          <Text fontSize="sm" color="gray.500">
            Detaylı zaman bazlı performans analizi ve trendler
          </Text>
        </VStack>

        <HStack spacing={3} wrap="wrap">
          {/* View Mode Selector */}
          <ButtonGroup size="sm" isAttached variant="outline">
            <Button
              colorScheme={viewMode === 'pnl' ? 'blue' : 'gray'}
              onClick={() => setViewMode('pnl')}
              leftIcon={<FiTrendingUp />}
            >
              Kar/Zarar
            </Button>
            <Button
              colorScheme={viewMode === 'trades' ? 'blue' : 'gray'}
              onClick={() => setViewMode('trades')}
              leftIcon={<FiBarChart2 />}
            >
              İşlem Sayısı
            </Button>
            <Button
              colorScheme={viewMode === 'winrate' ? 'blue' : 'gray'}
              onClick={() => setViewMode('winrate')}
              leftIcon={<FiTrendingUp />}
            >
              Kazanma Oranı
            </Button>
          </ButtonGroup>

          {/* Time Filter */}
          <Select
            size="sm"
            value={timeFilter}
            onChange={(e) => setTimeFilter(e.target.value as any)}
            w="120px"
          >
            <option value="1m">1 Ay</option>
            <option value="3m">3 Ay</option>
            <option value="6m">6 Ay</option>
            <option value="1y">1 Yıl</option>
            <option value="all">Tümü</option>
          </Select>

          {/* Action Buttons */}
          <Tooltip label="Filtreleri göster/gizle">
            <IconButton
              aria-label="Filtreler"
              icon={<FiFilter />}
              size="sm"
              variant={showFilters ? "solid" : "outline"}
              colorScheme={showFilters ? "blue" : "gray"}
              onClick={() => setShowFilters(!showFilters)}
            />
          </Tooltip>

          <Tooltip label="Verileri yenile">
            <IconButton
              aria-label="Yenile"
              icon={<FiRefreshCw />}
              size="sm"
              variant="outline"
              onClick={refetch}
              isLoading={loading}
            />
          </Tooltip>

          <StatisticsExport
            stats={stats}
            trigger={
              <Tooltip label="Zaman analizi raporunu indir">
                <IconButton
                  aria-label="İndir"
                  icon={<FiDownload />}
                  size="sm"
                  variant="outline"
                  colorScheme="blue"
                  isLoading={isExporting}
                />
              </Tooltip>
            }
          />
        </HStack>
      </Flex>

      {/* Data Status Indicator */}
      {(() => {
        const hasRealData = stats && (
          (stats.hourlyPerformance && stats.hourlyPerformance.length > 0) ||
          (stats.dailyPerformance && stats.dailyPerformance.length > 0) ||
          (stats.weeklyPerformance && stats.weeklyPerformance.length > 0)
        );

        return (
          <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
            <CardBody py={3}>
              <VStack spacing={3} align="stretch">
                <HStack justify="space-between" align="center">
                  <HStack spacing={3}>
                    <Box
                      w={3}
                      h={3}
                      borderRadius="full"
                      bg={hasRealData ? 'green.400' : 'orange.400'}
                    />
                    <Text fontSize="sm" color={textColor}>
                      {hasRealData
                        ? 'Gerçek veriler kullanılıyor'
                        : 'Demo veriler gösteriliyor - Gerçek işlem verileriniz henüz mevcut değil'
                      }
                    </Text>
                  </HStack>
                  <HStack spacing={2}>
                    <Text fontSize="xs" color="gray.500">
                      Son güncelleme: {new Date().toLocaleTimeString('tr-TR')}
                    </Text>
                    <Button
                      size="xs"
                      variant="ghost"
                      leftIcon={<FiRefreshCw />}
                      onClick={refetch}
                      isLoading={loading}
                    >
                      Yenile
                    </Button>
                  </HStack>
                </HStack>


              </VStack>
            </CardBody>
          </Card>
        );
      })()}

      {/* Advanced Filters Panel */}
      {showFilters && (
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack spacing={4} align="stretch">
              <Flex justify="space-between" align="center">
                <Text fontSize="lg" fontWeight="bold" color={textColor}>
                  Gelişmiş Filtreler
                </Text>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    setSelectedRobots([]);
                    setDateRange({ start: null, end: null });
                    setTimeFilter('3m');
                  }}
                >
                  Filtreleri Temizle
                </Button>
              </Flex>

              <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                {/* Robot Selection */}
                <VStack align="start" spacing={2}>
                  <Text fontSize="sm" fontWeight="semibold" color={textColor}>
                    Robot Seçimi
                  </Text>
                  <VStack align="start" spacing={1} w="full">
                    {availableRobots.map((robot) => (
                      <HStack key={robot.id} w="full">
                        <input
                          type="checkbox"
                          checked={selectedRobots.includes(robot.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedRobots([...selectedRobots, robot.id]);
                            } else {
                              setSelectedRobots(selectedRobots.filter(id => id !== robot.id));
                            }
                          }}
                        />
                        <Text fontSize="sm">
                          {robot.name}
                          <Badge ml={2} size="sm" colorScheme={robot.type === 'solo' ? 'blue' : 'green'}>
                            {robot.type === 'solo' ? 'Solo' : 'Bro'}
                          </Badge>
                        </Text>
                      </HStack>
                    ))}
                  </VStack>
                </VStack>

                {/* Date Range Selection */}
                <VStack align="start" spacing={2}>
                  <Text fontSize="sm" fontWeight="semibold" color={textColor}>
                    Tarih Aralığı
                  </Text>
                  <VStack spacing={2} w="full">
                    <input
                      type="date"
                      value={dateRange.start ? dateRange.start.toISOString().split('T')[0] : ''}
                      onChange={(e) => setDateRange(prev => ({
                        ...prev,
                        start: e.target.value ? new Date(e.target.value) : null
                      }))}
                      style={{
                        padding: '8px',
                        borderRadius: '6px',
                        border: '1px solid #e2e8f0',
                        width: '100%'
                      }}
                    />
                    <input
                      type="date"
                      value={dateRange.end ? dateRange.end.toISOString().split('T')[0] : ''}
                      onChange={(e) => setDateRange(prev => ({
                        ...prev,
                        end: e.target.value ? new Date(e.target.value) : null
                      }))}
                      style={{
                        padding: '8px',
                        borderRadius: '6px',
                        border: '1px solid #e2e8f0',
                        width: '100%'
                      }}
                    />
                  </VStack>
                </VStack>

                {/* Quick Time Filters */}
                <VStack align="start" spacing={2}>
                  <Text fontSize="sm" fontWeight="semibold" color={textColor}>
                    Hızlı Filtreler
                  </Text>
                  <ButtonGroup size="sm" isAttached variant="outline" w="full">
                    <Button
                      flex={1}
                      colorScheme={timeFilter === '1m' ? 'blue' : 'gray'}
                      onClick={() => setTimeFilter('1m')}
                    >
                      1A
                    </Button>
                    <Button
                      flex={1}
                      colorScheme={timeFilter === '3m' ? 'blue' : 'gray'}
                      onClick={() => setTimeFilter('3m')}
                    >
                      3A
                    </Button>
                    <Button
                      flex={1}
                      colorScheme={timeFilter === '6m' ? 'blue' : 'gray'}
                      onClick={() => setTimeFilter('6m')}
                    >
                      6A
                    </Button>
                    <Button
                      flex={1}
                      colorScheme={timeFilter === '1y' ? 'blue' : 'gray'}
                      onClick={() => setTimeFilter('1y')}
                    >
                      1Y
                    </Button>
                  </ButtonGroup>
                </VStack>
              </SimpleGrid>

              {/* Active Filters Display */}
              {(selectedRobots.length > 0 || dateRange.start || dateRange.end || timeFilter !== '3m') && (
                <Box>
                  <Text fontSize="sm" fontWeight="semibold" color={textColor} mb={2}>
                    Aktif Filtreler:
                  </Text>
                  <HStack spacing={2} flexWrap="wrap">
                    {selectedRobots.length > 0 && (
                      <Badge colorScheme="blue">
                        {selectedRobots.length} Robot Seçili
                      </Badge>
                    )}
                    {dateRange.start && (
                      <Badge colorScheme="green">
                        Başlangıç: {dateRange.start.toLocaleDateString('tr-TR')}
                      </Badge>
                    )}
                    {dateRange.end && (
                      <Badge colorScheme="green">
                        Bitiş: {dateRange.end.toLocaleDateString('tr-TR')}
                      </Badge>
                    )}
                    {timeFilter !== '3m' && (
                      <Badge colorScheme="purple">
                        {timeFilter === '1m' ? '1 Ay' :
                         timeFilter === '6m' ? '6 Ay' :
                         timeFilter === '1y' ? '1 Yıl' : 'Tümü'}
                      </Badge>
                    )}
                  </HStack>
                </Box>
              )}
            </VStack>
          </CardBody>
        </Card>
      )}

      {/* Enhanced Time Statistics Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Stat>
              <StatLabel color="gray.500" fontSize="sm">
                <HStack>
                  <FiClock />
                  <Text>En İyi Saat</Text>
                </HStack>
              </StatLabel>
              <StatNumber color="green.500" fontSize="2xl">
                {timeStats.bestHour}
              </StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                {formatCurrency(timeStats.bestHourValue)}
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Stat>
              <StatLabel color="gray.500" fontSize="sm">
                <HStack>
                  <FiTrendingDown />
                  <Text>En Kötü Saat</Text>
                </HStack>
              </StatLabel>
              <StatNumber color="red.500" fontSize="2xl">
                {timeStats.worstHour}
              </StatNumber>
              <StatHelpText>
                <StatArrow type="decrease" />
                {formatCurrency(timeStats.worstHourValue)}
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Stat>
              <StatLabel color="gray.500" fontSize="sm">
                <HStack>
                  <FiCalendar />
                  <Text>En İyi Gün</Text>
                </HStack>
              </StatLabel>
              <StatNumber color="green.500" fontSize="2xl">
                {timeStats.bestDay}
              </StatNumber>
              <StatHelpText>
                <StatArrow type="increase" />
                {formatCurrency(timeStats.bestDayValue)}
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Stat>
              <StatLabel color="gray.500" fontSize="sm">
                <HStack>
                  <FiBarChart2 />
                  <Text>Karlı Günler</Text>
                </HStack>
              </StatLabel>
              <StatNumber color="blue.500" fontSize="2xl">
                {timeStats.profitableDays}
              </StatNumber>
              <StatHelpText>
                <Badge colorScheme={timeStats.profitableRate > 50 ? 'green' : 'orange'}>
                  {formatPercentage(timeStats.profitableRate)} başarı
                </Badge>
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Additional Performance Metrics */}
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="start" spacing={3}>
              <Text fontSize="md" fontWeight="semibold" color={textColor}>
                Ortalama Performans
              </Text>
              <Divider />
              <VStack align="start" spacing={2} w="full">
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">Günlük Ortalama:</Text>
                  <Text fontSize="sm" fontWeight="medium">{formatCurrency(timeStats.avgDailyPnl)}</Text>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">Saatlik Ortalama:</Text>
                  <Text fontSize="sm" fontWeight="medium">{formatCurrency(timeStats.avgHourlyPnl)}</Text>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">Günlük İşlem:</Text>
                  <Text fontSize="sm" fontWeight="medium">{timeStats.avgTradesPerDay.toFixed(1)}</Text>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">Saatlik İşlem:</Text>
                  <Text fontSize="sm" fontWeight="medium">{timeStats.avgHourlyTrades.toFixed(1)}</Text>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">Ortalama Başarı:</Text>
                  <Text fontSize="sm" fontWeight="medium">{formatPercentage(timeStats.avgWinRate)}</Text>
                </Flex>
              </VStack>
            </VStack>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="start" spacing={3}>
              <Text fontSize="md" fontWeight="semibold" color={textColor}>
                İşlem İstatistikleri
              </Text>
              <Divider />
              <VStack align="start" spacing={2} w="full">
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">Toplam İşlem:</Text>
                  <Text fontSize="sm" fontWeight="medium">{timeStats.totalTrades}</Text>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">Aktif Günler:</Text>
                  <Text fontSize="sm" fontWeight="medium">{timeStats.totalTradingDays}</Text>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">Başarı Oranı:</Text>
                  <Badge colorScheme={timeStats.profitableRate > 50 ? 'green' : 'orange'}>
                    {formatPercentage(timeStats.profitableRate)}
                  </Badge>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">İşlem Başına Ort.:</Text>
                  <Text fontSize="sm" fontWeight="medium" color={timeStats.avgPnlPerTrade >= 0 ? 'green.500' : 'red.500'}>
                    {formatCurrency(timeStats.avgPnlPerTrade)}
                  </Text>
                </Flex>
              </VStack>
            </VStack>
          </CardBody>
        </Card>

        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="start" spacing={3}>
              <Text fontSize="md" fontWeight="semibold" color={textColor}>
                Zaman Dağılımı
              </Text>
              <Divider />
              <VStack align="start" spacing={2} w="full">
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">En Aktif Saat:</Text>
                  <Text fontSize="sm" fontWeight="medium">{timeStats.bestHour}</Text>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">En Aktif Gün:</Text>
                  <Text fontSize="sm" fontWeight="medium">{timeStats.bestDay}</Text>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">En İyi Tarih:</Text>
                  <Text fontSize="sm" fontWeight="medium">{timeStats.bestTradingDate}</Text>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">En Kötü Saat:</Text>
                  <Text fontSize="sm" fontWeight="medium" color="red.500">{timeStats.worstHour}</Text>
                </Flex>
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">Aktif Saat Sayısı:</Text>
                  <Text fontSize="sm" fontWeight="medium">
                    {hourlyHeatmapData.filter(h => h.value > 0).length}/24
                  </Text>
                </Flex>
                {tradingSessionData.bestSession && (
                  <Flex justify="space-between" w="full">
                    <Text fontSize="sm" color="gray.500">En İyi Seans:</Text>
                    <Text fontSize="sm" fontWeight="medium" color="green.500">
                      {tradingSessionData.bestSession.name}
                    </Text>
                  </Flex>
                )}
                <Flex justify="space-between" w="full">
                  <Text fontSize="sm" color="gray.500">Görüntüleme:</Text>
                  <Badge colorScheme="blue" variant="subtle">
                    {viewMode === 'pnl' ? 'Kar/Zarar' : viewMode === 'trades' ? 'İşlem Sayısı' : 'Kazanma Oranı'}
                  </Badge>
                </Flex>
              </VStack>
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Hourly Performance Heatmap */}
      <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
        <CardBody>
          <VStack align="stretch" spacing={4}>
            <Flex justify="space-between" align="center">
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                Saatlik Performans Haritası
              </Text>
              <Badge colorScheme="blue" variant="outline">
                {viewMode === 'pnl' ? 'Kar/Zarar' : viewMode === 'trades' ? 'İşlem Sayısı' : 'Kazanma Oranı'}
              </Badge>
            </Flex>
            {hourlyHeatmapData.length > 0 ? (
              <Box position="relative">
                {loading && (
                  <Box
                    position="absolute"
                    top={0}
                    left={0}
                    right={0}
                    bottom={0}
                    bg="rgba(255,255,255,0.8)"
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    zIndex={10}
                    borderRadius="md"
                  >
                    <VStack spacing={2}>
                      <Progress size="sm" isIndeterminate colorScheme="blue" w="100px" />
                      <Text fontSize="sm" color="gray.600">Güncelleniyor...</Text>
                    </VStack>
                  </Box>
                )}
                <HeatmapChart
                  data={hourlyHeatmapData}
                  title=""
                  xAxisLabel="Saat"
                  yAxisLabel="Performans"
                  colorScheme={viewMode === 'pnl' ? 'blue' : viewMode === 'trades' ? 'green' : 'purple'}
                  formatValue={(value) =>
                    viewMode === 'pnl' ? formatCurrency(value) :
                    viewMode === 'trades' ? `${value} işlem` :
                    formatPercentage(value)
                  }
                  height={250}
                />
              </Box>
            ) : (
              <Box p={8} textAlign="center" h="250px" display="flex" alignItems="center" justifyContent="center">
                <VStack spacing={3}>
                  <Text color="gray.500" fontSize="lg">Saatlik performans verisi bulunamadı</Text>
                  <Text color="gray.400" fontSize="sm">
                    Veriler yüklendiğinde burada saatlik performans haritası görüntülenecek
                  </Text>
                  <Button size="sm" variant="outline" onClick={refetch} leftIcon={<FiRefreshCw />}>
                    Verileri Yenile
                  </Button>
                </VStack>
              </Box>
            )}
          </VStack>
        </CardBody>
      </Card>

      {/* Enhanced Charts Grid */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* Daily Performance Trend */}
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="stretch" spacing={4}>
              <Flex justify="space-between" align="center">
                <Text fontSize="lg" fontWeight="bold" color={textColor}>
                  {selectedPeriod === 'daily' ? 'Günlük' : selectedPeriod === 'weekly' ? 'Haftalık' : 'Aylık'} Performans Trendi
                </Text>
                <ButtonGroup size="xs" isAttached variant="outline">
                  <Button
                    colorScheme={selectedPeriod === 'daily' ? 'blue' : 'gray'}
                    onClick={() => setSelectedPeriod('daily')}
                  >
                    Günlük
                  </Button>
                  <Button
                    colorScheme={selectedPeriod === 'weekly' ? 'blue' : 'gray'}
                    onClick={() => setSelectedPeriod('weekly')}
                  >
                    Haftalık
                  </Button>
                  <Button
                    colorScheme={selectedPeriod === 'monthly' ? 'blue' : 'gray'}
                    onClick={() => setSelectedPeriod('monthly')}
                  >
                    Aylık
                  </Button>
                </ButtonGroup>
              </Flex>
              {(selectedPeriod === 'daily' ? dailyPerformanceData :
                selectedPeriod === 'weekly' ? weeklyPerformanceData :
                monthlyTrendsData).length > 0 ? (
                <AdvancedLineChart
                  data={selectedPeriod === 'daily' ? dailyPerformanceData :
                       selectedPeriod === 'weekly' ? weeklyPerformanceData :
                       monthlyTrendsData}
                  title=""
                  dataKey="value"
                  formatValue={viewMode === 'pnl' ? formatCurrency :
                             viewMode === 'trades' ? (v) => `${v} işlem` :
                             formatPercentage}
                  height={350}
                  showBrush={true}
                  showMovingAverage={selectedPeriod === 'daily'}
                  color={viewMode === 'pnl' ? '#4299E1' : viewMode === 'trades' ? '#48BB78' : '#9F7AEA'}
                />
              ) : (
                <Box p={8} textAlign="center" h="350px" display="flex" alignItems="center" justifyContent="center">
                  <VStack spacing={2}>
                    <Text color="gray.500">
                      {selectedPeriod === 'daily' ? 'Günlük' : selectedPeriod === 'weekly' ? 'Haftalık' : 'Aylık'} performans verisi bulunamadı
                    </Text>
                    <Button size="sm" variant="outline" onClick={refetch}>
                      Verileri Yenile
                    </Button>
                  </VStack>
                </Box>
              )}
            </VStack>
          </CardBody>
        </Card>

        {/* Weekly Performance Analysis */}
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="stretch" spacing={4}>
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                Haftalık Performans Dağılımı
              </Text>
              {weeklyPerformanceData.length > 0 ? (
                <InteractivePieChart
                  data={weeklyPerformanceData}
                  title=""
                  height={350}
                  formatValue={viewMode === 'pnl' ? formatCurrency :
                             viewMode === 'trades' ? (v) => `${v} işlem` :
                             formatPercentage}
                  showPercentages={true}
                  showValues={true}
                  viewMode="donut"
                />
              ) : (
                <Box p={8} textAlign="center" h="350px" display="flex" alignItems="center" justifyContent="center">
                  <Text color="gray.500">Haftalık performans verisi bulunamadı</Text>
                </Box>
              )}
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Trading Session and Seasonal Analysis */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* Enhanced Trading Session Analysis */}
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="stretch" spacing={4}>
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                Seans Analizi
              </Text>
              {tradingSessionData.sessions && tradingSessionData.sessions.length > 0 ? (
                <VStack spacing={4}>
                  <InteractivePieChart
                    data={tradingSessionData.sessions}
                    title=""
                    height={300}
                    formatValue={viewMode === 'pnl' ? formatCurrency :
                               viewMode === 'trades' ? (v) => `${v} işlem` :
                               formatPercentage}
                    showPercentages={true}
                    showValues={true}
                  />
                  {/* Session Details */}
                  <VStack spacing={2} w="full">
                    {tradingSessionData.sessions.map((session, index) => (
                      <Flex key={index} justify="space-between" align="center" w="full" p={2} bg="gray.50" borderRadius="md">
                        <HStack>
                          <Box w={3} h={3} bg={session.color} borderRadius="full" />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="medium">{session.name}</Text>
                            <Text fontSize="xs" color="gray.500">{session.timeRange}</Text>
                          </VStack>
                        </HStack>
                        <VStack align="end" spacing={0}>
                          <Text fontSize="sm" fontWeight="bold">
                            {viewMode === 'pnl' ? formatCurrency(session.value) :
                             viewMode === 'trades' ? `${session.trades} işlem` :
                             formatPercentage(session.winRate)}
                          </Text>
                          {viewMode === 'pnl' && (
                            <Text fontSize="xs" color="gray.500">
                              {session.trades} işlem
                            </Text>
                          )}
                        </VStack>
                      </Flex>
                    ))}
                  </VStack>
                </VStack>
              ) : (
                <Box p={8} textAlign="center">
                  <Text color="gray.500">Seans analizi verisi bulunamadı</Text>
                </Box>
              )}
            </VStack>
          </CardBody>
        </Card>

        {/* Seasonal Analysis */}
        <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <VStack align="stretch" spacing={4}>
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                Mevsimsel Analiz
              </Text>
              {seasonalData.length > 0 ? (
                <VStack spacing={4}>
                  <InteractivePieChart
                    data={seasonalData}
                    title=""
                    height={300}
                    formatValue={(value) => `${value.toFixed(2)}%`}
                    showPercentages={true}
                    showValues={true}
                    viewMode="donut"
                  />
                  {/* Seasonal Performance Summary */}
                  <VStack spacing={2} w="full">
                    {seasonalData.map((season, index) => (
                      <Flex key={index} justify="space-between" align="center" w="full" p={2} bg="gray.50" borderRadius="md">
                        <HStack>
                          <Box w={3} h={3} bg={season.color} borderRadius="full" />
                          <Text fontSize="sm" fontWeight="medium">{season.name}</Text>
                        </HStack>
                        <Text fontSize="sm" fontWeight="bold">
                          {formatPercentage(season.value)}
                        </Text>
                      </Flex>
                    ))}
                  </VStack>
                </VStack>
              ) : (
                <Box p={8} textAlign="center">
                  <Text color="gray.500">Mevsimsel analiz verisi bulunamadı</Text>
                </Box>
              )}
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Monthly ROI Trends */}
      <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
        <CardBody>
          <VStack align="stretch" spacing={4}>
            <Flex justify="space-between" align="center">
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                Aylık ROI Trend Analizi
              </Text>
              <Badge colorScheme="green" variant="outline">
                Yatırım Getirisi
              </Badge>
            </Flex>
            {monthlyTrendsData.length > 0 ? (
              <AdvancedLineChart
                data={monthlyTrendsData}
                title=""
                dataKey="value"
                formatValue={(value) => `${value.toFixed(2)}%`}
                height={400}
                showBrush={true}
                showMovingAverage={true}
                showTrendLine={true}
                color="#48BB78"
              />
            ) : (
              <Box p={8} textAlign="center" h="400px" display="flex" alignItems="center" justifyContent="center">
                <VStack spacing={3}>
                  <Text color="gray.500" fontSize="lg">Aylık ROI trend verisi bulunamadı</Text>
                  <Text color="gray.400" fontSize="sm">
                    Veriler yüklendiğinde burada aylık yatırım getirisi trendleri görüntülenecek
                  </Text>
                  <Button size="sm" variant="outline" onClick={refetch} leftIcon={<FiRefreshCw />}>
                    Verileri Yenile
                  </Button>
                </VStack>
              </Box>
            )}
          </VStack>
        </CardBody>
      </Card>
    </VStack>
  );
};

export default TimeAnalysisPage;
