import React from 'react';
import { Card as ChakraCard, CardProps, useColorModeValue } from '@chakra-ui/react';

interface CustomCardProps extends Omit<CardProps, 'wordWrap'> {
  children: React.ReactNode;
  wordWrap?: 'normal' | 'break-word' | 'anywhere';
}

const Card: React.FC<CustomCardProps> = ({ 
  children, 
  wordWrap = 'break-word',
  ...props 
}) => {
  const cardBg = useColorModeValue('white', 'navy.800');
  const cardBorder = useColorModeValue('transparent', 'rgba(255, 255, 255, 0.15)');
  const cardShadow = useColorModeValue(
    '14px 17px 40px 4px rgba(112, 144, 176, 0.18)',
    'unset'
  );

  return (
    <ChakraCard
      bg={cardBg}
      border="1px solid"
      borderColor={cardBorder}
      borderRadius="20px"
      boxShadow={cardShadow}
      p="20px"
      position="relative"
      sx={{
        wordWrap: wordWrap,
      }}
      backgroundClip="border-box"
      {...props}
    >
      {children}
    </ChakraCard>
  );
};

export default Card; 