import { Page, expect } from '@playwright/test';

/**
 * Test kullanıcısı bilgileri
 */
export const TEST_USER = {
  email: '<EMAIL>',
  password: '159753',
  name: '<PERSON><PERSON>'
};

/**
 * Test robot bilgileri
 */
export const TEST_ROBOT = {
  id: 'e0f65ea5-d690-49e6-b553-c2b65c5f6919',
  name: 'Test Robot'
};

/**
 * Kullanıcı girişi yapar
 */
export async function loginUser(page: Page, email: string = TEST_USER.email, password: string = TEST_USER.password) {
  await page.goto('/login');
  
  // Email ve şifre alanlarını doldur
  await page.fill('input[type="email"]', email);
  await page.fill('input[type="password"]', password);
  
  // Giriş yap butonuna tıkla
  await page.click('button[type="submit"]');
  
  // Dashboard'a yönlendirilmeyi bekle
  await page.waitForURL('/');
  
  // Ana navbar'ın yüklenmesini bekle
  await expect(page.locator('nav[aria-label="Ana navigasyon"]')).toBeVisible();
}

/**
 * Admin kullanıcısı olarak giriş yapar
 */
export async function loginAsAdmin(page: Page) {
  await loginUser(page);
  
  // Admin paneline git
  await page.goto('/admin');
  
  // Admin panelinin yüklenmesini bekle
  await expect(page.locator('h1').first()).toContainText('Admin');
}

/**
 * Bildirim dropdown'ını açar
 */
export async function openNotificationDropdown(page: Page) {
  const notificationButton = page.locator('button[aria-label="Bildirimler"]');
  await expect(notificationButton).toBeVisible();
  await notificationButton.click();
  
  // Bildirim dropdown'ının açılmasını bekle
  await expect(page.locator('[data-testid="notification-dropdown-menu"]')).toBeVisible();
}

/**
 * Bildirim sayfasına gider
 */
export async function goToNotifications(page: Page) {
  await page.goto('/notifications');
  await expect(page.locator('h1').first()).toContainText('Bildirimler');
}

/**
 * Admin bildirim sayfasına gider
 */
export async function goToAdminNotifications(page: Page) {
  await page.goto('/admin/notifications');
  await expect(page.locator('h1:has-text("Bildirim Yönetimi")')).toBeVisible();
}

/**
 * Test bildirimi oluşturur
 */
export async function createTestNotification(page: Page, title: string = 'Test Bildirimi', message: string = 'Bu bir test bildirimidir.') {
  // RPC test butonuna tıkla
  const rpcTestButton = page.locator('button:has-text("RPC Test")');
  if (await rpcTestButton.isVisible()) {
    await rpcTestButton.click();
    
    // Toast mesajının görünmesini bekle
    await expect(page.locator('.chakra-toast')).toBeVisible();
  }
}

/**
 * Admin duyuru oluşturur
 */
export async function createAdminAnnouncement(page: Page, title: string, message: string, targetAudience: string = 'all') {
  await page.goto('/admin/notifications/create');
  
  // Form alanlarını doldur
  await page.fill('input[placeholder*="Başlık"]', title);
  await page.fill('textarea[placeholder*="Mesaj"]', message);
  
  // Hedef kitle seç
  await page.selectOption('select', targetAudience);
  
  // Gönder butonuna tıkla
  await page.click('button:has-text("Önizleme & Gönder")');
}

/**
 * Robot sinyali simüle eder
 */
export async function simulateRobotSignal(page: Page, signalType: 'BUY' | 'SELL', symbol: string = 'TCELL', price: number = 18.50) {
  // Management sayfasına git
  await page.goto('/management');
  
  // Test butonunu bul ve tıkla
  const testButton = page.locator(`button:has-text("${signalType} Test")`);
  if (await testButton.isVisible()) {
    await testButton.click();
    
    // Toast mesajının görünmesini bekle
    await expect(page.locator('.chakra-toast')).toBeVisible();
  }
}

/**
 * Sayfanın yüklenmesini bekler
 */
export async function waitForPageLoad(page: Page) {
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(1000); // Ek güvenlik için
}

/**
 * Toast mesajını kontrol eder
 */
export async function expectToast(page: Page, message: string, type: 'success' | 'error' | 'info' = 'success') {
  const toast = page.locator('.chakra-toast');
  await expect(toast).toBeVisible();
  await expect(toast).toContainText(message);
}

/**
 * Bildirim sayısını kontrol eder
 */
export async function expectNotificationCount(page: Page, count: number) {
  if (count > 0) {
    const badge = page.locator('button[aria-label="Bildirimler"] .chakra-badge');
    await expect(badge).toBeVisible();
    await expect(badge).toContainText(count.toString());
  } else {
    const badge = page.locator('button[aria-label="Bildirimler"] .chakra-badge');
    await expect(badge).not.toBeVisible();
  }
}

/**
 * Bildirim listesinde belirli bir bildirimi arar
 */
export async function findNotificationByTitle(page: Page, title: string) {
  const notification = page.locator(`[data-testid="notification-item"]:has-text("${title}")`);
  return notification;
}

/**
 * Debug modunu kapatır
 */
export async function disableDebugMode(page: Page) {
  // Debug elementlerini gizle
  await page.addStyleTag({
    content: `
      [data-testid="debug-info"],
      .debug-section {
        display: none !important;
      }
    `
  });
}
