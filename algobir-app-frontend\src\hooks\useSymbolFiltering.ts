import { useState, useMemo, useCallback } from 'react';
import { SymbolAnalysisFilters, SymbolMetrics } from '../types/symbolAnalysis';

// Default filter state
const defaultFilters: SymbolAnalysisFilters = {
  symbols: [],
  dateRange: {
    start: '',
    end: ''
  },
  performanceRange: {},
  tradeCountRange: {},
  riskLevel: 'all',
  sortBy: 'pnl',
  sortOrder: 'desc',
  showOnlyProfitable: false,
  minTradeCount: 1
};

export const useSymbolFiltering = (data: SymbolMetrics[]) => {
  const [filters, setFilters] = useState<SymbolAnalysisFilters>(defaultFilters);

  // Apply filters to data
  const filteredData = useMemo(() => {
    if (!data || data.length === 0) return [];

    let filtered = [...data];

    // Symbol filter
    if (filters.symbols.length > 0) {
      filtered = filtered.filter(item => filters.symbols.includes(item.symbol));
    }

    // Date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(item => {
        const firstTradeDate = new Date(item.firstTradeDate);
        const lastTradeDate = new Date(item.lastTradeDate);
        
        let passesFilter = true;
        
        if (filters.dateRange.start) {
          const startDate = new Date(filters.dateRange.start);
          passesFilter = passesFilter && lastTradeDate >= startDate;
        }
        
        if (filters.dateRange.end) {
          const endDate = new Date(filters.dateRange.end);
          passesFilter = passesFilter && firstTradeDate <= endDate;
        }
        
        return passesFilter;
      });
    }

    // Performance range filters
    if (filters.performanceRange.minPnl !== undefined) {
      filtered = filtered.filter(item => item.totalPnl >= filters.performanceRange.minPnl!);
    }
    if (filters.performanceRange.maxPnl !== undefined) {
      filtered = filtered.filter(item => item.totalPnl <= filters.performanceRange.maxPnl!);
    }
    if (filters.performanceRange.minROI !== undefined) {
      filtered = filtered.filter(item => item.roi >= filters.performanceRange.minROI!);
    }
    if (filters.performanceRange.maxROI !== undefined) {
      filtered = filtered.filter(item => item.roi <= filters.performanceRange.maxROI!);
    }

    // Trade count range filters
    if (filters.tradeCountRange.min !== undefined) {
      filtered = filtered.filter(item => item.totalTrades >= filters.tradeCountRange.min!);
    }
    if (filters.tradeCountRange.max !== undefined) {
      filtered = filtered.filter(item => item.totalTrades <= filters.tradeCountRange.max!);
    }

    // Risk level filter
    if (filters.riskLevel !== 'all') {
      filtered = filtered.filter(item => {
        const riskScore = calculateRiskScore(item);
        switch (filters.riskLevel) {
          case 'low':
            return riskScore <= 3;
          case 'medium':
            return riskScore > 3 && riskScore <= 7;
          case 'high':
            return riskScore > 7;
          default:
            return true;
        }
      });
    }

    // Show only profitable filter
    if (filters.showOnlyProfitable) {
      filtered = filtered.filter(item => item.totalPnl > 0);
    }

    // Minimum trade count filter
    if (filters.minTradeCount > 1) {
      filtered = filtered.filter(item => item.totalTrades >= filters.minTradeCount);
    }

    // Sort the filtered data
    filtered.sort((a, b) => {
      let aValue: number;
      let bValue: number;

      switch (filters.sortBy) {
        case 'pnl':
          aValue = a.totalPnl;
          bValue = b.totalPnl;
          break;
        case 'roi':
          aValue = a.roi;
          bValue = b.roi;
          break;
        case 'winRate':
          aValue = a.winRate;
          bValue = b.winRate;
          break;
        case 'trades':
          aValue = a.totalTrades;
          bValue = b.totalTrades;
          break;
        case 'volatility':
          aValue = a.volatility;
          bValue = b.volatility;
          break;
        case 'sharpe':
          aValue = a.sharpeRatio;
          bValue = b.sharpeRatio;
          break;
        default:
          aValue = a.totalPnl;
          bValue = b.totalPnl;
      }

      return filters.sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
    });

    return filtered;
  }, [data, filters]);

  // Calculate risk score (0-10 scale)
  const calculateRiskScore = useCallback((symbol: SymbolMetrics): number => {
    // Normalize volatility (0-10 scale)
    const volatilityScore = Math.min(symbol.volatility / 10, 10);
    
    // Normalize max drawdown (0-10 scale)
    const drawdownScore = Math.min(symbol.maxDrawdownPercent / 10, 10);
    
    // Sharpe ratio score (inverted - lower Sharpe = higher risk)
    const sharpeScore = symbol.sharpeRatio < 0 ? 10 : Math.max(0, 10 - symbol.sharpeRatio * 2);
    
    // Win rate score (inverted - lower win rate = higher risk)
    const winRateScore = Math.max(0, 10 - (symbol.winRate / 10));
    
    // Average the scores
    return (volatilityScore + drawdownScore + sharpeScore + winRateScore) / 4;
  }, []);

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<SymbolAnalysisFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  // Get filter summary
  const filterSummary = useMemo(() => {
    const total = data.length;
    const filtered = filteredData.length;
    const percentage = total > 0 ? (filtered / total) * 100 : 0;

    return {
      total,
      filtered,
      percentage,
      hidden: total - filtered
    };
  }, [data.length, filteredData.length]);

  // Get available filter options
  const filterOptions = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        symbols: [],
        dateRange: { min: '', max: '' },
        pnlRange: { min: 0, max: 0 },
        roiRange: { min: 0, max: 0 },
        tradeCountRange: { min: 0, max: 0 },
        riskLevels: ['low', 'medium', 'high']
      };
    }

    const symbols = [...new Set(data.map(d => d.symbol))].sort();
    const dates = data.flatMap(d => [d.firstTradeDate, d.lastTradeDate]).filter(Boolean);
    const pnlValues = data.map(d => d.totalPnl);
    const roiValues = data.map(d => d.roi);
    const tradeValues = data.map(d => d.totalTrades);

    return {
      symbols,
      dateRange: {
        min: dates.length > 0 ? new Date(Math.min(...dates.map(d => new Date(d).getTime()))).toISOString().split('T')[0] : '',
        max: dates.length > 0 ? new Date(Math.max(...dates.map(d => new Date(d).getTime()))).toISOString().split('T')[0] : ''
      },
      pnlRange: {
        min: Math.min(...pnlValues),
        max: Math.max(...pnlValues)
      },
      roiRange: {
        min: Math.min(...roiValues),
        max: Math.max(...roiValues)
      },
      tradeCountRange: {
        min: Math.min(...tradeValues),
        max: Math.max(...tradeValues)
      },
      riskLevels: ['low', 'medium', 'high'] as const
    };
  }, [data]);

  // Quick filter presets
  const applyQuickFilter = useCallback((preset: string) => {
    switch (preset) {
      case 'profitable':
        updateFilters({ showOnlyProfitable: true, sortBy: 'pnl', sortOrder: 'desc' });
        break;
      case 'highVolume':
        updateFilters({ 
          minTradeCount: Math.max(10, Math.floor(filterOptions.tradeCountRange.max * 0.3)),
          sortBy: 'trades',
          sortOrder: 'desc'
        });
        break;
      case 'lowRisk':
        updateFilters({ riskLevel: 'low', sortBy: 'sharpe', sortOrder: 'desc' });
        break;
      case 'highPerformance':
        updateFilters({ 
          showOnlyProfitable: true,
          performanceRange: { minROI: 10 },
          sortBy: 'roi',
          sortOrder: 'desc'
        });
        break;
      case 'recent':
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        updateFilters({
          dateRange: {
            start: thirtyDaysAgo.toISOString().split('T')[0],
            end: new Date().toISOString().split('T')[0]
          }
        });
        break;
      default:
        break;
    }
  }, [updateFilters, filterOptions]);

  return {
    filters,
    filteredData,
    updateFilters,
    resetFilters,
    filterSummary,
    filterOptions,
    applyQuickFilter,
    calculateRiskScore
  };
};
