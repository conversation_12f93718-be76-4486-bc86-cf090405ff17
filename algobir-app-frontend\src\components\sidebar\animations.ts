// Animation constants and utilities for the sidebar system
// Inspired by Supabase dashboard's smooth transitions

// Easing functions - cubic-bezier curves for natural motion
export const EASING = {
  // Standard easing for most transitions
  standard: 'cubic-bezier(0.4, 0, 0.2, 1)',
  
  // Emphasized easing for important state changes
  emphasized: 'cubic-bezier(0.2, 0, 0, 1)',
  
  // Decelerated easing for entering elements
  decelerated: 'cubic-bezier(0, 0, 0.2, 1)',
  
  // Accelerated easing for exiting elements
  accelerated: 'cubic-bezier(0.4, 0, 1, 1)',
  
  // Bounce effect for interactive elements
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  
  // Smooth easing for hover effects
  smooth: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
} as const;

// Duration constants in milliseconds
export const DURATION = {
  // Fast transitions for micro-interactions
  fast: 150,
  
  // Standard duration for most transitions
  standard: 300,
  
  // Slower transitions for complex state changes
  slow: 500,
  
  // Very slow for dramatic effects
  verySlow: 700
} as const;

// Sidebar-specific animation configurations
export const SIDEBAR_ANIMATIONS = {
  // Main sidebar width transition
  width: {
    duration: `${DURATION.standard}ms`,
    easing: EASING.standard,
    property: 'width'
  },
  
  // Sidebar content fade transition
  content: {
    duration: `${DURATION.fast}ms`,
    easing: EASING.decelerated,
    property: 'opacity, transform'
  },
  
  // Submenu panel slide transition
  submenu: {
    duration: `${DURATION.standard}ms`,
    easing: EASING.emphasized,
    property: 'transform, opacity'
  },
  
  // Icon hover effects
  iconHover: {
    duration: `${DURATION.fast}ms`,
    easing: EASING.bounce,
    property: 'transform, box-shadow'
  },
  
  // Menu item hover effects
  menuHover: {
    duration: `${DURATION.fast}ms`,
    easing: EASING.smooth,
    property: 'background-color, transform, box-shadow'
  }
} as const;

// Layout animation configurations
export const LAYOUT_ANIMATIONS = {
  // Content area adjustment
  content: {
    duration: `${DURATION.standard}ms`,
    easing: EASING.standard,
    property: 'margin-left, width'
  },
  
  // Navbar positioning
  navbar: {
    duration: `${DURATION.standard}ms`,
    easing: EASING.standard,
    property: 'left'
  }
} as const;

// Utility functions for creating transition strings
export const createTransition = (
  properties: string[],
  duration: number = DURATION.standard,
  easing: string = EASING.standard,
  delay: number = 0
): string => {
  return properties
    .map(prop => `${prop} ${duration}ms ${easing}${delay ? ` ${delay}ms` : ''}`)
    .join(', ');
};

// Pre-built transition combinations
export const TRANSITIONS = {
  // Sidebar width and shadow
  sidebar: createTransition(
    ['width', 'box-shadow'],
    DURATION.standard,
    EASING.standard
  ),
  
  // Content layout adjustment
  layout: createTransition(
    ['margin-left', 'width'],
    DURATION.standard,
    EASING.standard
  ),
  
  // Interactive element hover
  hover: createTransition(
    ['background-color', 'transform', 'box-shadow'],
    DURATION.fast,
    EASING.smooth
  ),
  
  // Icon scale and color
  icon: createTransition(
    ['transform', 'color'],
    DURATION.fast,
    EASING.bounce
  ),
  
  // Submenu slide in/out
  submenu: createTransition(
    ['transform', 'opacity'],
    DURATION.standard,
    EASING.emphasized
  )
} as const;

// Animation keyframes for complex animations
export const KEYFRAMES = {
  // Fade in from left
  fadeInLeft: {
    from: {
      opacity: 0,
      transform: 'translateX(-20px)'
    },
    to: {
      opacity: 1,
      transform: 'translateX(0)'
    }
  },
  
  // Fade out to left
  fadeOutLeft: {
    from: {
      opacity: 1,
      transform: 'translateX(0)'
    },
    to: {
      opacity: 0,
      transform: 'translateX(-20px)'
    }
  },
  
  // Scale bounce
  scaleBounce: {
    '0%': { transform: 'scale(1)' },
    '50%': { transform: 'scale(1.05)' },
    '100%': { transform: 'scale(1)' }
  },
  
  // Pulse effect
  pulse: {
    '0%': { opacity: 1 },
    '50%': { opacity: 0.7 },
    '100%': { opacity: 1 }
  }
} as const;

// Performance optimization utilities
export const PERFORMANCE = {
  // Optimized will-change for better performance - only essential properties
  willChange: 'transform, width',

  // CSS properties for hardware acceleration (use in sx prop)
  backfaceVisibility: 'hidden' as const,
  perspective: 1000,

  // Reduce motion for accessibility
  prefersReducedMotion: '@media (prefers-reduced-motion: reduce)'
} as const;

// Responsive animation adjustments
export const RESPONSIVE_ANIMATIONS = {
  mobile: {
    duration: DURATION.fast, // Faster on mobile for better performance
    easing: EASING.standard
  },
  
  desktop: {
    duration: DURATION.standard,
    easing: EASING.emphasized
  }
} as const;
