import { 
  <PERSON>, 
  Heading, 
  Tabs, 
  TabList, 
  Tab, 
  Flex, 
  Divider, 
  useColorModeValue, 
  Icon, 
  HStack, 
  Tag, 
  Text,
  Card,
  CardBody
} from '@chakra-ui/react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { FiShield, FiUsers, FiActivity, FiBarChart } from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import Navbar, { NAVBAR_HEIGHT } from '../../components/navbar/NavbarAdmin';

// Sabit sidebar genişliği
const SIDEBAR_WIDTH = "285px";

const AdminLayout = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  
  // Horizon UI renk değerleri
  const mainBg = useColorModeValue('secondaryGray.300', 'navy.900');
  const cardBg = useColorModeValue('white', 'navy.800');
  const textColor = useColorModeValue('navy.700', 'white');
  const secondaryText = useColorModeValue('secondaryGray.600', 'secondaryGray.600');
  const accentColor = useColorModeValue('brand.500', 'brand.400');
  const borderColor = useColorModeValue('gray.200', 'navy.600');
  const tabSelectedBg = useColorModeValue('brand.500', 'brand.400');
  
  // Aktif tab indeksini ayarla
  const getActiveTabIndex = () => {
    const path = location.pathname;
    if (path === "/admin") return 0;
    if (path === "/admin/users") return 1;
    if (path === "/admin/trades") return 2;
    return 0;
  };

  // Tab değişikliğinde ilgili route'a yönlendir
  const handleTabChange = (index: number) => {
    switch (index) {
      case 0:
        navigate("/admin");
        break;
      case 1:
        navigate("/admin/users");
        break;
      case 2:
        navigate("/admin/trades");
        break;
      default:
        navigate("/admin");
    }
  };

  // Email bilgisini güvenli bir şekilde göster
  const userEmailDisplay = user ? (user.email || 'Belirtilmemiş') : 'Kullanıcı bilgisi yok';

  // Get active route details for Navbar
  const getActiveRoute = () => {
    let activeRouteName = 'Admin Paneli';
    const path = location.pathname;
    if (path === "/admin/users") return "Kullanıcılar";
    if (path === "/admin/trades") return "Tüm İşlemler";
    return activeRouteName;
  };

  return (
    <Box>
      <Box
        marginLeft={{ base: '0px', xl: SIDEBAR_WIDTH }}
        width={{ base: '100%', xl: `calc(100% - ${SIDEBAR_WIDTH})` }}
        minHeight='100vh'
        position='relative'
        transition='margin-left 0.2s ease-in-out, width 0.2s ease-in-out'
        bg={mainBg}
      >
        <Navbar
          brandText={getActiveRoute()}
          secondary={false}
        />

        <Box
          as="main"
          pt={`calc(${NAVBAR_HEIGHT} + 20px)`}
          height={`calc(100vh - ${NAVBAR_HEIGHT})`}
          overflowY='auto'
          px={{ base: "20px", md: "30px" }}
          pb="20px"
        >
          {/* Header Card */}
          <Card 
            mb={6}
            variant="elevated"
            bg={cardBg}
            borderRadius="20px"
            boxShadow="0px 3.5px 5.5px rgba(0, 0, 0, 0.02)"
          >
            <CardBody p={6}>
              <Flex 
                direction="column" 
                gap={4}
              >
                <HStack spacing={4} align="center">
                  <Box
                    p={3}
                    borderRadius="16px"
                    bg={useColorModeValue('brand.50', 'brand.900')}
                    color={accentColor}
                  >
                    <Icon as={FiShield} boxSize={6} />
                  </Box>
                  <Box flex="1">
                    <Heading 
                      as="h1" 
                      size="xl" 
                      color={textColor}
                      fontWeight="700"
                      letterSpacing="-0.025em"
                      mb={1}
                    >
                      Admin Paneli
                    </Heading>
                    <Text color={secondaryText} fontSize="md">
                      Sistem yönetimi ve kullanıcı kontrolü
                    </Text>
                  </Box>
                  <Tag 
                    size="lg" 
                    variant="solid" 
                    bg={accentColor}
                    color="white"
                    borderRadius="full"
                    px={4}
                    py={2}
                    fontWeight="600"
                  >
                    Süper Kullanıcı
                  </Tag>
                </HStack>
                
                <Divider borderColor={borderColor} />
                
                <HStack spacing={2}>
                  <Text color={secondaryText} fontSize="sm" fontWeight="500">
                    Giriş yapan admin:
                  </Text>
                  <Text color={textColor} fontSize="sm" fontWeight="600">
                    {userEmailDisplay}
                  </Text>
                </HStack>
              </Flex>
            </CardBody>
          </Card>
          
          {/* Navigation Tabs */}
          <Card 
            mb={6}
            variant="elevated"
            bg={cardBg}
            borderRadius="20px"
            boxShadow="0px 3.5px 5.5px rgba(0, 0, 0, 0.02)"
          >
            <CardBody p={0}>
              <Tabs 
                index={getActiveTabIndex()} 
                onChange={handleTabChange}
                variant="unstyled"
                size="lg"
              >
                <TabList p={2} gap={1}>
                  <Tab 
                    gap={3}
                    borderRadius="16px"
                    px={6}
                    py={3}
                    color={secondaryText}
                    fontWeight="500"
                    transition="all 0.2s ease"
                    _selected={{
                      bg: tabSelectedBg,
                      color: 'white',
                      fontWeight: '600',
                      transform: 'scale(1.02)'
                    }}
                    _hover={{
                      bg: useColorModeValue('gray.100', 'whiteAlpha.100'),
                      transform: 'scale(1.02)'
                    }}
                  >
                    <Icon as={FiBarChart} boxSize={5} /> 
                    <Text>Özet</Text>
                  </Tab>
                  <Tab 
                    gap={3}
                    borderRadius="16px"
                    px={6}
                    py={3}
                    color={secondaryText}
                    fontWeight="500"
                    transition="all 0.2s ease"
                    _selected={{
                      bg: tabSelectedBg,
                      color: 'white',
                      fontWeight: '600',
                      transform: 'scale(1.02)'
                    }}
                    _hover={{
                      bg: useColorModeValue('gray.100', 'whiteAlpha.100'),
                      transform: 'scale(1.02)'
                    }}
                  >
                    <Icon as={FiUsers} boxSize={5} />
                    <Text>Kullanıcılar</Text>
                  </Tab>
                  <Tab 
                    gap={3}
                    borderRadius="16px"
                    px={6}
                    py={3}
                    color={secondaryText}
                    fontWeight="500"
                    transition="all 0.2s ease"
                    _selected={{
                      bg: tabSelectedBg,
                      color: 'white',
                      fontWeight: '600',
                      transform: 'scale(1.02)'
                    }}
                    _hover={{
                      bg: useColorModeValue('gray.100', 'whiteAlpha.100'),
                      transform: 'scale(1.02)'
                    }}
                  >
                    <Icon as={FiActivity} boxSize={5} />
                    <Text>Tüm İşlemler</Text>
                  </Tab>
                </TabList>
              </Tabs>
            </CardBody>
          </Card>

          {/* Content Card */}
          <Card 
            variant="elevated"
            bg={cardBg}
            borderRadius="20px"
            boxShadow="0px 3.5px 5.5px rgba(0, 0, 0, 0.02)"
            minH="60vh"
          >
            <CardBody p={6}>
              <Outlet />
            </CardBody>
          </Card>
        </Box>
      </Box>
    </Box>
  );
};

export default AdminLayout; 