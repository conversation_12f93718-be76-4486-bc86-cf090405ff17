<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submenu Scrolling Demo</title>
    <style>
        body {
            margin: 0;
            font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            background: #f7fafc;
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 70px;
            background: white;
            box-shadow: 14px 17px 40px 4px rgba(112, 144, 176, 0.08);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 0;
            position: relative;
            z-index: 1000;
        }

        .sidebar-item {
            width: 40px;
            height: 40px;
            background: #f7fafc;
            border-radius: 10px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #718096;
        }

        .sidebar-item:hover {
            background: #e2e8f0;
            transform: scale(1.05);
        }

        .submenu-panel {
            position: fixed;
            left: 70px;
            top: 0;
            width: 260px;
            height: 100vh;
            background: white;
            border-right: 1px solid #e2e8f0;
            box-shadow: 14px 17px 40px 4px rgba(112, 144, 176, 0.08);
            z-index: 1050;
            display: none;
            flex-direction: column;
        }

        .submenu-panel.show {
            display: flex;
        }

        .submenu-header {
            padding: 25px 20px 20px;
            border-bottom: 1px solid #e2e8f0;
            flex-shrink: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .submenu-title {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
        }

        .close-button {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: #718096;
            padding: 4px;
            border-radius: 4px;
        }

        .close-button:hover {
            background: #f7fafc;
        }

        .submenu-content {
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        .scrollable-area {
            height: 100%;
            overflow-y: auto;
            padding: 10px 20px 20px;
            scrollbar-width: thin;
            scrollbar-color: rgba(112, 144, 176, 0.3) transparent;
        }

        .scrollable-area::-webkit-scrollbar {
            width: 6px;
        }

        .scrollable-area::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.02);
            border-radius: 3px;
            margin: 2px;
        }

        .scrollable-area::-webkit-scrollbar-thumb {
            background: rgba(112, 144, 176, 0.3);
            border-radius: 15px;
            transition: all 0.2s ease;
        }

        .scrollable-area::-webkit-scrollbar-thumb:hover {
            background: rgba(112, 144, 176, 0.5);
        }

        .menu-item {
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .menu-item:hover {
            background: #f7fafc;
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .menu-item-icon {
            color: #2d3748;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .menu-item-content {
            flex: 1;
        }

        .menu-item-title {
            font-size: 14px;
            font-weight: 600;
            color: #2d3748;
            line-height: 1.2;
            margin-bottom: 4px;
        }

        .menu-item-description {
            font-size: 12px;
            color: #718096;
            line-height: 1.3;
        }

        .demo-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 2000;
        }

        .demo-button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 4px;
            font-size: 14px;
        }

        .demo-button:hover {
            background: #3182ce;
        }

        .scroll-indicator {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(66, 153, 225, 0.1);
            color: #4299e1;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .submenu-content:hover .scroll-indicator {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="sidebar-item" onclick="toggleSubmenu()" title="Settings Menu">
            ⚙️
        </div>
        <div class="sidebar-item" title="Dashboard">
            📊
        </div>
        <div class="sidebar-item" title="Users">
            👥
        </div>
        <div class="sidebar-item" title="Reports">
            📈
        </div>
    </div>

    <div class="submenu-panel" id="submenuPanel">
        <div class="submenu-header">
            <div class="submenu-title">Settings Menu</div>
            <button class="close-button" onclick="closeSubmenu()">✕</button>
        </div>
        <div class="submenu-content">
            <div class="scroll-indicator">Scroll for more</div>
            <div class="scrollable-area" id="scrollableArea">
                <!-- Menu items will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <div class="demo-controls">
        <h3 style="margin-top: 0;">Scrolling Demo Controls</h3>
        <button class="demo-button" onclick="addMoreItems()">Add More Items</button>
        <button class="demo-button" onclick="removeItems()">Remove Items</button>
        <button class="demo-button" onclick="scrollToTop()">Scroll to Top</button>
        <button class="demo-button" onclick="scrollToBottom()">Scroll to Bottom</button>
        <p style="font-size: 12px; color: #718096; margin-bottom: 0;">
            Click the settings icon (⚙️) to open the submenu.<br>
            Use mouse wheel or scrollbar to scroll through items.
        </p>
    </div>

    <script>
        const menuItems = [
            { icon: '👤', title: 'Profile Settings', description: 'Manage your personal profile information and preferences' },
            { icon: '🔧', title: 'Account Settings', description: 'Configure account security, billing, and general settings' },
            { icon: '📧', title: 'Email Management', description: 'Manage email notifications, templates, and communication settings' },
            { icon: '📱', title: 'Phone Settings', description: 'Configure phone numbers, SMS notifications, and call preferences' },
            { icon: '📅', title: 'Calendar Integration', description: 'Sync with external calendars and manage scheduling preferences' },
            { icon: '📁', title: 'File Management', description: 'Upload, organize, and manage your documents and media files' },
            { icon: '🗂️', title: 'Folder Organization', description: 'Create and manage folder structures for better file organization' },
            { icon: '🔔', title: 'Notification Settings', description: 'Control how and when you receive notifications' },
            { icon: '🎨', title: 'Theme Preferences', description: 'Customize the appearance and color scheme of your interface' },
            { icon: '🔒', title: 'Privacy Controls', description: 'Manage your privacy settings and data sharing preferences' },
            { icon: '🌐', title: 'Language Settings', description: 'Change the language and regional settings for your account' },
            { icon: '⚡', title: 'Performance Options', description: 'Optimize performance settings for better user experience' },
            { icon: '🔄', title: 'Sync Settings', description: 'Configure data synchronization across your devices' },
            { icon: '📊', title: 'Analytics Dashboard', description: 'View detailed analytics and usage statistics' },
            { icon: '🛡️', title: 'Security Center', description: 'Monitor security events and manage security settings' }
        ];

        let currentItemCount = 8;

        function renderMenuItems() {
            const scrollableArea = document.getElementById('scrollableArea');
            scrollableArea.innerHTML = '';
            
            for (let i = 0; i < currentItemCount && i < menuItems.length; i++) {
                const item = menuItems[i];
                const menuItem = document.createElement('div');
                menuItem.className = 'menu-item';
                menuItem.innerHTML = `
                    <div class="menu-item-icon">${item.icon}</div>
                    <div class="menu-item-content">
                        <div class="menu-item-title">${item.title}</div>
                        <div class="menu-item-description">${item.description}</div>
                    </div>
                `;
                scrollableArea.appendChild(menuItem);
            }
        }

        function toggleSubmenu() {
            const panel = document.getElementById('submenuPanel');
            panel.classList.toggle('show');
            if (panel.classList.contains('show')) {
                renderMenuItems();
            }
        }

        function closeSubmenu() {
            document.getElementById('submenuPanel').classList.remove('show');
        }

        function addMoreItems() {
            if (currentItemCount < menuItems.length) {
                currentItemCount = Math.min(currentItemCount + 3, menuItems.length);
                renderMenuItems();
            }
        }

        function removeItems() {
            if (currentItemCount > 3) {
                currentItemCount = Math.max(currentItemCount - 3, 3);
                renderMenuItems();
            }
        }

        function scrollToTop() {
            document.getElementById('scrollableArea').scrollTo({ top: 0, behavior: 'smooth' });
        }

        function scrollToBottom() {
            const scrollableArea = document.getElementById('scrollableArea');
            scrollableArea.scrollTo({ top: scrollableArea.scrollHeight, behavior: 'smooth' });
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(event) {
            const panel = document.getElementById('submenuPanel');
            if (panel.classList.contains('show')) {
                if (event.key === 'Escape') {
                    closeSubmenu();
                }
            }
        });

        // Initialize with some items
        renderMenuItems();
    </script>
</body>
</html>
