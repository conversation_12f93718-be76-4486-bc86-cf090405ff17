/**
 * Optimized Signal Relay Function (BRO-ROBOT Flow Part 2)
 * Phase 2.4: Supabase Edge Functions Performance Optimization
 * Enhanced with batch processing, connection pooling, and performance monitoring
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { getCorsHeaders } from '../_shared/cors.ts';
import { 
  createPerformanceMonitor, 
  cache, 
  dbOptimizer, 
  responseOptimizer,
  memoryManager,
  connectionPool
} from '../_shared/performance-optimizer.ts';

// Environment setup
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

// Optimized signal parsing with caching
class SignalParser {
  private static readonly CACHE_TTL = 600000; // 10 minutes

  static parseSignalName(signalName: string, robotName: string): any {
    const cacheKey = `signal_parse_${signalName}_${robotName}`;
    
    const cached = cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    const parts = signalName.trim().split(/\s+/);
    let baseSystemName = robotName || 'Unknown';
    let signalType = 'UNKNOWN';
    let orderSide = 'BUY';
    let symbol;

    if (parts.length >= 2) {
      baseSystemName = parts[0];
      signalType = parts[1].toUpperCase();
      
      // Determine order side
      if (['SELL', 'CLOSE', 'EXIT', 'SİLME', 'PTCTSELL'].includes(signalType)) {
        orderSide = 'SELL';
      } else {
        orderSide = 'BUY';
      }
    } else if (parts.length === 1) {
      baseSystemName = parts[0];
      signalType = 'BUY';
      orderSide = 'BUY';
    }

    // Symbol detection
    for (const part of parts) {
      // Crypto pairs
      if (part.match(/^[A-Z]{3,10}USDT?$/i) || part.match(/^[A-Z]{3,10}BTC$/i)) {
        symbol = part.toUpperCase();
        break;
      }
      // Traditional symbols
      if (part.match(/^[A-Z]{2,5}$/i) && 
          !['BUY', 'SELL', 'OPEN', 'CLOSE', 'EXIT', 'AL', 'SİLME', 'PTCTSELL'].includes(part.toUpperCase())) {
        symbol = part.toUpperCase();
        break;
      }
    }

    const result = { baseSystemName, signalType, orderSide, symbol };
    cache.set(cacheKey, result, this.CACHE_TTL);
    return result;
  }
}

// Optimized subscriber management with batch operations
class SubscriberManager {
  private static readonly CACHE_TTL = 300000; // 5 minutes

  static async getActiveSubscribers(robotId: string, supabase: any, monitor: any): Promise<any[]> {
    const cacheKey = `subscribers_${robotId}`;
    
    const cached = cache.get(cacheKey);
    if (cached) {
      monitor.recordCacheHit();
      return cached;
    }

    monitor.recordCacheMiss();

    try {
      const result = await dbOptimizer.executeQuery(
        supabase,
        `active_subscribers_${robotId}`,
        async () => {
          const { data, error } = await supabase
            .from('subscriptions')
            .select(`
              user_id,
              user_settings!inner(
                id,
                investment_amount,
                is_active,
                encrypted_api_key,
                encrypted_token,
                custom_webhook_url
              )
            `)
            .eq('robot_id', robotId)
            .eq('is_active', true)
            .eq('user_settings.is_active', true)
            .gte('expires_at', new Date().toISOString());
          
          return { data, error };
        },
        this.CACHE_TTL,
        monitor
      );

      if (result.error) {
        console.error('[SubscriberManager] Error fetching subscribers:', result.error);
        return [];
      }

      const subscribers = result.data || [];
      cache.set(cacheKey, subscribers, this.CACHE_TTL);
      return subscribers;

    } catch (error) {
      console.error('[SubscriberManager] Exception fetching subscribers:', error);
      return [];
    }
  }

  static async batchCreateTrades(trades: any[], supabase: any, monitor: any): Promise<{ success: boolean; tradeIds: string[]; errors: any[] }> {
    if (trades.length === 0) {
      return { success: true, tradeIds: [], errors: [] };
    }

    try {
      monitor.startDbQuery();
      
      const { data, error } = await supabase
        .from('trades')
        .insert(trades)
        .select('id');

      monitor.endDbQuery();

      if (error) {
        console.error('[SubscriberManager] Batch trade creation error:', error);
        return { success: false, tradeIds: [], errors: [error] };
      }

      const tradeIds = data?.map((trade: any) => trade.id) || [];
      return { success: true, tradeIds, errors: [] };

    } catch (error) {
      monitor.endDbQuery();
      console.error('[SubscriberManager] Batch trade creation exception:', error);
      return { success: false, tradeIds: [], errors: [error] };
    }
  }

  static async batchCreateNotifications(notifications: any[], supabase: any, monitor: any): Promise<void> {
    if (notifications.length === 0) return;

    try {
      monitor.startDbQuery();
      
      const { error } = await supabase
        .from('notifications')
        .insert(notifications);

      monitor.endDbQuery();

      if (error) {
        console.error('[SubscriberManager] Batch notification creation error:', error);
      }

    } catch (error) {
      monitor.endDbQuery();
      console.error('[SubscriberManager] Batch notification creation exception:', error);
    }
  }
}

// Optimized order submission with parallel processing
class OrderSubmissionManager {
  static async batchSubmitOrders(tradeIds: string[], supabase: any, monitor: any): Promise<void> {
    if (tradeIds.length === 0) return;

    const batchSize = 5; // Process 5 orders at a time to avoid overwhelming
    const batches = [];
    
    for (let i = 0; i < tradeIds.length; i += batchSize) {
      batches.push(tradeIds.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const promises = batch.map(tradeId => this.submitSingleOrder(tradeId, supabase, monitor));
      
      try {
        await Promise.allSettled(promises);
      } catch (error) {
        console.error('[OrderSubmissionManager] Batch submission error:', error);
      }
    }
  }

  private static async submitSingleOrder(tradeId: string, supabase: any, monitor: any): Promise<void> {
    try {
      monitor.startDbQuery();
      
      const { error } = await supabase.functions.invoke(
        'osmanli-yatirim-emir-iletim-bro-robot',
        { body: { trade_id: tradeId } }
      );

      monitor.endDbQuery();

      if (error) {
        console.error(`[OrderSubmissionManager] Order submission failed for trade ${tradeId}:`, error);
      }

    } catch (error) {
      monitor.endDbQuery();
      console.error(`[OrderSubmissionManager] Order submission exception for trade ${tradeId}:`, error);
    }
  }
}

// Currency formatting utility
function formatTurkishCurrency(amount: number): string {
  return new Intl.NumberFormat('tr-TR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}

Deno.serve(async (req) => {
  const monitor = createPerformanceMonitor();
  const corsHeaders = getCorsHeaders(req.headers);

  if (req.method === 'OPTIONS') {
    return responseOptimizer.createJsonResponse('ok', 200, corsHeaders, monitor);
  }

  try {
    // Memory management
    const memoryStatus = memoryManager.checkMemoryUsage();
    if (memoryStatus.needsCleanup) {
      console.warn('[signal-relay-function] High memory usage, cleanup performed');
    }

    // Initialize Supabase client with connection pooling
    const supabase = connectionPool.getPool(
      'supabase_relay',
      () => createClient(supabaseUrl, supabaseServiceKey)
    );

    // Parse request
    const requestBody = await req.text();
    monitor.setRequestSize(new TextEncoder().encode(requestBody).length);

    let requestData: any;
    try {
      requestData = JSON.parse(requestBody);
    } catch {
      return responseOptimizer.createErrorResponse(
        'Invalid JSON in request body',
        400,
        corsHeaders,
        monitor
      );
    }

    const { robot_id, signal_data } = requestData;

    if (!robot_id || !signal_data) {
      return responseOptimizer.createErrorResponse(
        'Missing robot_id or signal_data',
        400,
        corsHeaders,
        monitor
      );
    }

    // Get robot information
    const robotResult = await dbOptimizer.executeQuery(
      supabase,
      `robot_info_${robot_id}`,
      async () => {
        const { data, error } = await supabase
          .from('robots')
          .select('id, name, seller_id')
          .eq('id', robot_id)
          .single();
        return { data, error };
      },
      300000, // 5 minutes cache
      monitor
    );

    if (robotResult.error || !robotResult.data) {
      return responseOptimizer.createErrorResponse(
        'Robot not found',
        404,
        corsHeaders,
        monitor
      );
    }

    const robot = robotResult.data;

    // Parse signal
    const parsedSignal = SignalParser.parseSignalName(signal_data.name || '', robot.name);
    
    if (!parsedSignal.symbol) {
      return responseOptimizer.createErrorResponse(
        'Could not extract symbol from signal',
        400,
        corsHeaders,
        monitor
      );
    }

    // Get active subscribers
    const subscribers = await SubscriberManager.getActiveSubscribers(robot_id, supabase, monitor);
    
    if (subscribers.length === 0) {
      return responseOptimizer.createJsonResponse(
        {
          success: true,
          message: 'No active subscribers found',
          robot_id,
          subscriber_count: 0
        },
        200,
        corsHeaders,
        monitor
      );
    }

    // Prepare trades and notifications for batch processing
    const trades: any[] = [];
    const notifications: any[] = [];
    const price = parseFloat(signal_data.price) || 1.0;

    for (const subscriber of subscribers) {
      const userSettings = subscriber.user_settings;
      const investmentAmount = userSettings.investment_amount || 100.00;
      const calculatedQuantity = Math.floor(investmentAmount / price);

      // Create trade record
      const tradeData = {
        user_id: subscriber.user_id,
        robot_id: robot_id,
        symbol: parsedSignal.symbol,
        order_side: parsedSignal.orderSide,
        signal_type: parsedSignal.signalType,
        trade_category: 'Bro-Robot',
        position_status: parsedSignal.orderSide === 'BUY' ? 'Açık' : 'Kapalı',
        system_name: `Bro-Robot (${robot.name})`,
        price: price,
        calculated_quantity: calculatedQuantity,
        investment_amount: investmentAmount,
        signal_name: signal_data.name,
        category: 'bro-robot',
        forwarded: false,
        status: 'pending',
        raw_signal_data: signal_data,
        name: signal_data.name
      };

      trades.push(tradeData);

      // Prepare notification
      const totalAmount = calculatedQuantity * price;
      const formattedPrice = formatTurkishCurrency(price);
      const formattedTotalAmount = formatTurkishCurrency(totalAmount);

      const notificationTitle = parsedSignal.orderSide === 'BUY' ? 'Al Bildirimi' : 'Sat Bildirimi';
      const notificationMessage = parsedSignal.orderSide === 'BUY' ?
        `Al Bildirimi: ${parsedSignal.symbol} hissesinden ${formattedPrice} TL fiyatla ${calculatedQuantity} adet alındı. Toplam tutar ₺${formattedTotalAmount}'dır. ${robot.name} robotu ile işlem açıldı.` :
        `Sat Bildirimi: ${parsedSignal.symbol} hissesinden ${formattedPrice} TL fiyatla ${calculatedQuantity} adet satıldı. Toplam tutar ₺${formattedTotalAmount}'dır. ${robot.name} robotu ile işlem kapandı.`;

      notifications.push({
        user_id: subscriber.user_id,
        title: notificationTitle,
        message: notificationMessage,
        type: parsedSignal.orderSide === 'BUY' ? 'trade_opened' : 'trade_closed',
        severity: 'success',
        metadata: {
          robot_id: robot_id,
          robot_name: robot.name,
          symbol: parsedSignal.symbol,
          order_side: parsedSignal.orderSide,
          quantity: calculatedQuantity,
          price: price,
          total_amount: totalAmount,
          source: 'bro-robot'
        },
        action_url: '/trades',
        action_label: 'İşlemleri Görüntüle',
        is_read: false
      });
    }

    // Batch create trades
    const tradeResult = await SubscriberManager.batchCreateTrades(trades, supabase, monitor);
    
    if (!tradeResult.success) {
      return responseOptimizer.createErrorResponse(
        'Failed to create trades for subscribers',
        500,
        corsHeaders,
        monitor
      );
    }

    // Batch create notifications (fire and forget)
    SubscriberManager.batchCreateNotifications(notifications, supabase, monitor);

    // Batch submit orders (fire and forget)
    OrderSubmissionManager.batchSubmitOrders(tradeResult.tradeIds, supabase, monitor);

    // Success response
    const responseData = {
      success: true,
      message: 'Signal processed and relayed to subscribers',
      robot_id,
      robot_name: robot.name,
      signal_type: parsedSignal.signalType,
      symbol: parsedSignal.symbol,
      order_side: parsedSignal.orderSide,
      subscriber_count: subscribers.length,
      trades_created: tradeResult.tradeIds.length,
      processing_time_ms: monitor.getCurrentMetrics().duration,
      performance: {
        cache_hits: monitor.getCurrentMetrics().cacheHits,
        cache_misses: monitor.getCurrentMetrics().cacheMisses,
        db_queries: monitor.getCurrentMetrics().dbQueryCount
      }
    };

    return responseOptimizer.createJsonResponse(responseData, 200, corsHeaders, monitor);

  } catch (error) {
    console.error('[signal-relay-function-optimized] Critical error:', error);
    
    return responseOptimizer.createErrorResponse(
      `Signal relay failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500,
      corsHeaders,
      monitor
    );
  }
});
