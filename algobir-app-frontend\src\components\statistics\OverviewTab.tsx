import React from 'react';
import { SimpleGrid, Box, Heading } from '@chakra-ui/react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';
import { StatsData } from '../../hooks/useAdvancedStatisticsData';
import MiniStatistics from '../card/MiniStatistics';

interface Props { stats: StatsData }

const OverviewTab: React.FC<Props> = ({ stats }) => {
  return (
    <Box>
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing="20px" mb="20px">
        <MiniStatistics name="Toplam Kâr/Zarar" value={`₺${stats.totalPnl.toFixed(2)}`} />
        <MiniStatistics name="Kâr Faktörü" value={stats.profitFactor.toFixed(2)} />
        <MiniStatistics name="Kazanma Oranı" value={`${stats.winRate.toFixed(2)}%`} />
        <MiniStatistics name="Maksimum Düşüş" value={`-₺${stats.maxDrawdown.toFixed(2)}`} />
      </SimpleGrid>
      <Box h="400px" mt={8}>
        <Heading size="md" mb={4}>Kümülatif Kâr/Zarar</Heading>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={stats.cumulativePnlData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip formatter={(value: number) => `₺${value.toFixed(2)}`} />
            <Legend />
            <Line type="monotone" dataKey="value" name="Kümülatif K/Z" stroke="#8884d8" strokeWidth={2} dot={false} />
          </LineChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
};

export default OverviewTab; 