import React, { useState } from 'react';
import {
  VStack,
  Text,
  SimpleGrid,
  Card,

  HStack,
  Box,
  Button,
  Progress,
  useColorModeValue
} from '@chakra-ui/react';
import { useEnhancedStatistics } from '../../hooks/useEnhancedStatistics';
import { useStatisticsExport } from '../../hooks/useStatisticsExport';

const ReportsPage: React.FC = () => {
  const { stats, loading, error } = useEnhancedStatistics();
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  // Import the export hook
  const { exportStatistics } = useStatisticsExport();

  if (loading) {
    return (
      <Box p={8} textAlign="center">
        <Text>Yükleniyor...</Text>
      </Box>
    );
  }

  if (error || !stats) {
    return (
      <Box p={8} textAlign="center">
        <Text color="red.500">Veri yüklenirken hata o<PERSON></Text>
      </Box>
    );
  }

  const handleExport = async (format: 'excel' | 'csv' | 'json' | 'pdf') => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      const options = {
        format,
        includeCharts: true,
        includeSummary: true,
        includeRobotDetails: true,
        includeTradeHistory: true
      };

      await exportStatistics(stats, options);

      // Simulate progress for user feedback
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 100;
          }
          return prev + 10;
        });
      }, 100);

      setTimeout(() => {
        setExportProgress(100);
        setTimeout(() => {
          setIsExporting(false);
          setExportProgress(0);
        }, 1000);
      }, 2000);

    } catch (error) {
      console.error('Export error:', error);
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  // Report templates
  const reportTemplates = [
    {
      title: 'Genel Performans Raporu',
      description: 'Tüm trading performansınızın kapsamlı analizi',
      icon: '📊',
      color: 'blue',
      includes: ['Genel istatistikler', 'ROI analizi', 'Risk metrikleri', 'Grafik görselleri']
    },
    {
      title: 'Robot Karşılaştırma Raporu',
      description: 'Solo ve Bro-Robot performanslarının detaylı karşılaştırması',
      icon: '🤖',
      color: 'green',
      includes: ['Robot performansları', 'Karşılaştırmalı analiz', 'Verimlilik metrikleri']
    },
    {
      title: 'Risk Analizi Raporu',
      description: 'Detaylı risk değerlendirmesi ve öneriler',
      icon: '⚠️',
      color: 'orange',
      includes: ['Risk metrikleri', 'Drawdown analizi', 'Volatilite ölçümleri']
    },
    {
      title: 'Zaman Bazlı Analiz',
      description: 'Saatlik, günlük ve aylık performans analizi',
      icon: '⏰',
      color: 'purple',
      includes: ['Zaman analizi', 'Trend grafikleri', 'Seans performansları']
    }
  ];

  // Export formats
  const exportFormats = [
    {
      format: 'pdf' as const,
      title: 'PDF Raporu',
      description: 'Profesyonel PDF formatında detaylı rapor',
      icon: '📄',
      color: 'red'
    },
    {
      format: 'excel' as const,
      title: 'Excel Dosyası',
      description: 'Analiz için Excel formatında veri',
      icon: '📊',
      color: 'green'
    },
    {
      format: 'csv' as const,
      title: 'CSV Dosyası',
      description: 'Ham veri için CSV formatı',
      icon: '📋',
      color: 'blue'
    },
    {
      format: 'json' as const,
      title: 'JSON Verisi',
      description: 'Geliştiriciler için JSON formatı',
      icon: '💾',
      color: 'purple'
    }
  ];

  return (
    <VStack spacing={6} align="stretch">
      <Text fontSize="2xl" fontWeight="bold">Raporlar ve Dışa Aktarma</Text>

      {/* Report Templates */}
      <Card p={6} borderRadius="16px" boxShadow="md">
        <VStack align="stretch" spacing={4}>
          <Text fontSize="lg" fontWeight="bold">Rapor Şablonları</Text>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
            {reportTemplates.map((template, index) => (
              <Box key={index} p={4} borderRadius="12px" bg={useColorModeValue('gray.50', 'gray.700')}>
                <VStack align="start" spacing={3}>
                  <HStack>
                    <Text fontSize="2xl">{template.icon}</Text>
                    <VStack align="start" spacing={0}>
                      <Text fontWeight="bold" fontSize="md">{template.title}</Text>
                      <Text fontSize="sm" color="gray.500">{template.description}</Text>
                    </VStack>
                  </HStack>
                  <VStack align="start" spacing={1}>
                    <Text fontSize="xs" fontWeight="medium" color="gray.600">İçerik:</Text>
                    {template.includes.map((item, idx) => (
                      <Text key={idx} fontSize="xs" color="gray.500">• {item}</Text>
                    ))}
                  </VStack>
                </VStack>
              </Box>
            ))}
          </SimpleGrid>
        </VStack>
      </Card>

      {/* Export Formats */}
      <Card p={6} borderRadius="16px" boxShadow="md">
        <VStack align="stretch" spacing={4}>
          <Text fontSize="lg" fontWeight="bold">Dışa Aktarma Formatları</Text>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={4}>
            {exportFormats.map((format, index) => (
              <Box key={index} p={4} borderRadius="12px" border="1px" borderColor="gray.200">
                <VStack spacing={3}>
                  <Text fontSize="3xl">{format.icon}</Text>
                  <VStack spacing={1}>
                    <Text fontWeight="bold" fontSize="md">{format.title}</Text>
                    <Text fontSize="sm" color="gray.500" textAlign="center">
                      {format.description}
                    </Text>
                  </VStack>
                  <Button
                    colorScheme={format.color}
                    size="sm"
                    width="full"
                    isLoading={isExporting}
                    loadingText="Dışa aktarılıyor..."
                    onClick={() => handleExport(format.format)}
                  >
                    İndir
                  </Button>
                </VStack>
              </Box>
            ))}
          </SimpleGrid>
        </VStack>
      </Card>

      {/* Export Progress */}
      {isExporting && (
        <Card p={6} borderRadius="16px" boxShadow="md">
          <VStack spacing={4}>
            <Text fontSize="lg" fontWeight="bold">Rapor Hazırlanıyor...</Text>
            <Box width="100%">
              <Progress value={exportProgress} colorScheme="blue" size="lg" borderRadius="md" />
            </Box>
            <Text fontSize="sm" color="gray.500">
              {exportProgress < 50 ? 'Veriler toplanıyor...' :
               exportProgress < 80 ? 'Rapor oluşturuluyor...' :
               exportProgress < 100 ? 'Son işlemler yapılıyor...' : 'Tamamlandı!'}
            </Text>
          </VStack>
        </Card>
      )}

      {/* Report History */}
      <Card p={6} borderRadius="16px" boxShadow="md">
        <VStack align="stretch" spacing={4}>
          <Text fontSize="lg" fontWeight="bold">Rapor Geçmişi</Text>
          <Box p={8} textAlign="center" bg={useColorModeValue('gray.50', 'gray.700')} borderRadius="12px">
            <VStack spacing={2}>
              <Text fontSize="4xl">📋</Text>
              <Text fontSize="md" fontWeight="medium">Henüz rapor oluşturulmamış</Text>
              <Text fontSize="sm" color="gray.500">
                Yukarıdaki formatlardan birini seçerek ilk raporunuzu oluşturun
              </Text>
            </VStack>
          </Box>
        </VStack>
      </Card>

      {/* Scheduled Reports */}
      <Card p={6} borderRadius="16px" boxShadow="md">
        <VStack align="stretch" spacing={4}>
          <HStack justify="space-between">
            <Text fontSize="lg" fontWeight="bold">Zamanlanmış Raporlar</Text>
            <Button size="sm" colorScheme="blue" variant="outline">
              Yeni Zamanlama
            </Button>
          </HStack>
          <Box p={8} textAlign="center" bg={useColorModeValue('gray.50', 'gray.700')} borderRadius="12px">
            <VStack spacing={2}>
              <Text fontSize="4xl">⏰</Text>
              <Text fontSize="md" fontWeight="medium">Zamanlanmış rapor bulunmuyor</Text>
              <Text fontSize="sm" color="gray.500">
                Otomatik rapor oluşturma için zamanlama ayarlayabilirsiniz
              </Text>
            </VStack>
          </Box>
        </VStack>
      </Card>
    </VStack>
  );
};

export default ReportsPage;
