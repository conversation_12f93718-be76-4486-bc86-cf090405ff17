/**
 * Standardized hook types and utilities for better TypeScript support
 */

/**
 * Standard loading states
 */
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

/**
 * Base hook state interface
 */
export interface BaseHookState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: number | null;
}

/**
 * Extended hook state with additional metadata
 */
export interface ExtendedHookState<T> extends BaseHookState<T> {
  isStale: boolean;
  isCached: boolean;
  retryCount: number;
  loadingState: LoadingState;
}

/**
 * Hook configuration interface
 */
export interface HookConfig {
  enableCache?: boolean;
  cacheTimeout?: number;
  retryAttempts?: number;
  showErrorToast?: boolean;
  errorContext?: string;
}

/**
 * Pagination state
 */
export interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Pagination controls
 */
export interface PaginationControls {
  goToPage: (page: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  setPageSize: (size: number) => void;
  reset: () => void;
}

/**
 * Combined pagination interface
 */
export interface PaginatedHookReturn<T> extends BaseHookState<T[]> {
  pagination: PaginationState;
  controls: PaginationControls;
}

/**
 * Filter state interface
 */
export interface FilterState<T = Record<string, any>> {
  filters: T;
  activeFilters: (keyof T)[];
  hasActiveFilters: boolean;
}

/**
 * Filter controls interface
 */
export interface FilterControls<T = Record<string, any>> {
  setFilter: <K extends keyof T>(key: K, value: T[K]) => void;
  removeFilter: (key: keyof T) => void;
  clearFilters: () => void;
  resetFilters: () => void;
}

/**
 * Combined filter interface
 */
export interface FilteredHookReturn<T, F = Record<string, any>> extends BaseHookState<T[]> {
  filters: FilterState<F>;
  filterControls: FilterControls<F>;
}

/**
 * Search state interface
 */
export interface SearchState {
  query: string;
  isSearching: boolean;
  searchResults: any[] | null;
  hasSearched: boolean;
}

/**
 * Search controls interface
 */
export interface SearchControls {
  search: (query: string) => Promise<void>;
  clearSearch: () => void;
  setQuery: (query: string) => void;
}

/**
 * Combined search interface
 */
export interface SearchableHookReturn<T> extends BaseHookState<T[]> {
  search: SearchState;
  searchControls: SearchControls;
}

/**
 * CRUD operations interface
 */
export interface CrudOperations<T, CreateData = Partial<T>, UpdateData = Partial<T>> {
  create: (data: CreateData) => Promise<T>;
  update: (id: string | number, data: UpdateData) => Promise<T>;
  delete: (id: string | number) => Promise<void>;
  refresh: () => Promise<void>;
}

/**
 * CRUD hook return interface
 */
export interface CrudHookReturn<T, CreateData = Partial<T>, UpdateData = Partial<T>> 
  extends BaseHookState<T[]> {
  operations: CrudOperations<T, CreateData, UpdateData>;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  operationError: string | null;
}

/**
 * Real-time subscription state
 */
export interface SubscriptionState {
  isConnected: boolean;
  lastUpdate: number | null;
  subscriptionError: string | null;
}

/**
 * Real-time hook return interface
 */
export interface RealtimeHookReturn<T> extends BaseHookState<T[]> {
  subscription: SubscriptionState;
  subscribe: () => void;
  unsubscribe: () => void;
}

/**
 * Utility types for better type inference
 */

/**
 * Extract data type from hook return
 */
export type HookData<T> = T extends BaseHookState<infer U> ? U : never;

/**
 * Make hook state properties optional
 */
export type PartialHookState<T> = Partial<BaseHookState<T>>;

/**
 * Hook state with required data
 */
export type LoadedHookState<T> = BaseHookState<T> & { data: T };

/**
 * Type guard for loaded state
 */
export function isLoaded<T>(state: BaseHookState<T>): state is LoadedHookState<T> {
  return state.data !== null && !state.loading && state.error === null;
}

/**
 * Type guard for loading state
 */
export function isLoading<T>(state: BaseHookState<T>): boolean {
  return state.loading;
}

/**
 * Type guard for error state
 */
export function hasError<T>(state: BaseHookState<T>): boolean {
  return state.error !== null;
}

/**
 * Utility function to create initial hook state
 */
export function createInitialHookState<T>(): BaseHookState<T> {
  return {
    data: null,
    loading: false,
    error: null,
    lastUpdated: null
  };
}

/**
 * Utility function to create loading state
 */
export function createLoadingState<T>(currentData: T | null = null): BaseHookState<T> {
  return {
    data: currentData,
    loading: true,
    error: null,
    lastUpdated: null
  };
}

/**
 * Utility function to create success state
 */
export function createSuccessState<T>(data: T): BaseHookState<T> {
  return {
    data,
    loading: false,
    error: null,
    lastUpdated: Date.now()
  };
}

/**
 * Utility function to create error state
 */
export function createErrorState<T>(error: string, currentData: T | null = null): BaseHookState<T> {
  return {
    data: currentData,
    loading: false,
    error,
    lastUpdated: null
  };
}
