/**
 * Component Performance Tests
 * Phase 3.1: Automated Testing Pipeline Setup
 * Performance benchmarking for critical components
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { renderWithProviders, measureRenderTime } from '../utils/test-utils';
import { ChakraProvider } from '@chakra-ui/react';
import theme from '../../theme/theme';

// Mock components for performance testing
const SimpleComponent = () => <div>Simple Component</div>;

const ComplexComponent = ({ items }: { items: any[] }) => (
  <div>
    <h1>Complex Component</h1>
    <ul>
      {items.map((item, index) => (
        <li key={index}>
          <div>
            <span>{item.name}</span>
            <span>{item.value}</span>
            <button onClick={() => console.log(item)}>Action</button>
          </div>
        </li>
      ))}
    </ul>
  </div>
);

const DataTable = ({ data }: { data: any[] }) => (
  <table>
    <thead>
      <tr>
        <th>ID</th>
        <th>Name</th>
        <th>Value</th>
        <th>Status</th>
      </tr>
    </thead>
    <tbody>
      {data.map((row, index) => (
        <tr key={index}>
          <td>{row.id}</td>
          <td>{row.name}</td>
          <td>{row.value}</td>
          <td>{row.status}</td>
        </tr>
      ))}
    </tbody>
  </table>
);

// Test data generators
const generateTestData = (count: number) => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    name: `Item ${index + 1}`,
    value: Math.random() * 1000,
    status: index % 2 === 0 ? 'active' : 'inactive'
  }));
};

describe('Component Performance Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Render Performance', () => {
    it('should render simple components quickly', async () => {
      const renderTime = await measureRenderTime(() => {
        render(<SimpleComponent />);
      });

      // Should render within 50ms (more realistic for test environment)
      expect(renderTime).toBeLessThan(50);
    });

    it('should render complex components within acceptable time', async () => {
      const testData = generateTestData(100);
      
      const renderTime = await measureRenderTime(() => {
        render(<ComplexComponent items={testData} />);
      });

      // Should render 100 items within 200ms (more realistic for test environment)
      expect(renderTime).toBeLessThan(200);
    });

    it('should handle large datasets efficiently', async () => {
      const largeDataset = generateTestData(1000);
      
      const renderTime = await measureRenderTime(() => {
        render(<DataTable data={largeDataset} />);
      });

      // Should render 1000 rows within 500ms (more realistic for test environment)
      expect(renderTime).toBeLessThan(500);
    });

    it('should render with Chakra UI providers efficiently', async () => {
      const testData = generateTestData(50);
      
      const renderTime = await measureRenderTime(() => {
        renderWithProviders(<ComplexComponent items={testData} />);
      });

      // Should render with providers within 300ms (more realistic for test environment)
      expect(renderTime).toBeLessThan(300);
    });
  });

  describe('Re-render Performance', () => {
    it('should handle prop changes efficiently', async () => {
      const initialData = generateTestData(50);
      const updatedData = generateTestData(50);
      
      const { rerender } = render(<ComplexComponent items={initialData} />);
      
      const rerenderTime = await measureRenderTime(() => {
        rerender(<ComplexComponent items={updatedData} />);
      });

      // Re-render should be faster than initial render
      expect(rerenderTime).toBeLessThan(100);
    });

    it('should optimize re-renders with React.memo', async () => {
      const MemoizedComponent = React.memo(ComplexComponent);
      const testData = generateTestData(100);
      
      const { rerender } = render(<MemoizedComponent items={testData} />);
      
      // Re-render with same props should be very fast
      const rerenderTime = await measureRenderTime(() => {
        rerender(<MemoizedComponent items={testData} />);
      });

      expect(rerenderTime).toBeLessThan(50);
    });

    it('should handle frequent updates without performance degradation', async () => {
      const testData = generateTestData(20);
      const { rerender } = render(<ComplexComponent items={testData} />);
      
      const startTime = performance.now();
      
      // Perform 10 re-renders
      for (let i = 0; i < 10; i++) {
        const updatedData = generateTestData(20);
        rerender(<ComplexComponent items={updatedData} />);
      }
      
      const totalTime = performance.now() - startTime;
      
      // 10 re-renders should complete within 500ms (more realistic for test environment)
      expect(totalTime).toBeLessThan(500);
    });
  });

  describe('Memory Performance', () => {
    it('should not cause memory leaks with component mounting/unmounting', () => {
      const testData = generateTestData(100);
      
      // Mount and unmount multiple times
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(<ComplexComponent items={testData} />);
        unmount();
      }
      
      // Should not throw or cause issues
      expect(true).toBe(true);
    });

    it('should clean up event listeners properly', () => {
      const mockEventListener = vi.fn();
      
      const ComponentWithEvents = () => {
        React.useEffect(() => {
          window.addEventListener('resize', mockEventListener);
          return () => window.removeEventListener('resize', mockEventListener);
        }, []);
        
        return <div>Component with events</div>;
      };
      
      const { unmount } = render(<ComponentWithEvents />);
      
      // Trigger resize event
      window.dispatchEvent(new Event('resize'));
      expect(mockEventListener).toHaveBeenCalledTimes(1);
      
      // Unmount and trigger again
      unmount();
      window.dispatchEvent(new Event('resize'));
      
      // Should not be called again after unmount
      expect(mockEventListener).toHaveBeenCalledTimes(1);
    });
  });

  describe('Bundle Size Impact', () => {
    it('should not significantly increase bundle size with test utilities', () => {
      // This is more of a build-time check, but we can verify imports
      expect(typeof renderWithProviders).toBe('function');
      expect(typeof measureRenderTime).toBe('function');
      
      // Test utilities should be tree-shakeable in production
      expect(process.env.NODE_ENV).toBe('test');
    });
  });

  describe('Accessibility Performance', () => {
    it('should maintain performance with accessibility features', async () => {
      const AccessibleComponent = () => (
        <div>
          <h1 id="title">Accessible Component</h1>
          <nav aria-labelledby="title">
            <ul>
              {Array.from({ length: 50 }, (_, i) => (
                <li key={i}>
                  <a href={`#item-${i}`} aria-describedby={`desc-${i}`}>
                    Item {i}
                  </a>
                  <span id={`desc-${i}`}>Description for item {i}</span>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      );
      
      const renderTime = await measureRenderTime(() => {
        render(<AccessibleComponent />);
      });

      // Should render with accessibility attributes within reasonable time
      expect(renderTime).toBeLessThan(300);
    });
  });

  describe('Animation Performance', () => {
    it('should handle CSS transitions efficiently', async () => {
      const AnimatedComponent = ({ visible }: { visible: boolean }) => (
        <div 
          style={{
            opacity: visible ? 1 : 0,
            transition: 'opacity 0.3s ease-in-out',
            transform: visible ? 'translateY(0)' : 'translateY(-10px)'
          }}
        >
          Animated Content
        </div>
      );
      
      const { rerender } = render(<AnimatedComponent visible={false} />);
      
      const animationTime = await measureRenderTime(() => {
        rerender(<AnimatedComponent visible={true} />);
      });

      // Animation state change should be fast
      expect(animationTime).toBeLessThan(100);
    });
  });

  describe('Data Processing Performance', () => {
    it('should handle data transformations efficiently', async () => {
      const rawData = generateTestData(1000);
      
      const DataProcessor = ({ data }: { data: any[] }) => {
        const processedData = React.useMemo(() => {
          return data
            .filter(item => item.status === 'active')
            .sort((a, b) => b.value - a.value)
            .slice(0, 100);
        }, [data]);
        
        return (
          <ul>
            {processedData.map(item => (
              <li key={item.id}>{item.name}: {item.value}</li>
            ))}
          </ul>
        );
      };
      
      const renderTime = await measureRenderTime(() => {
        render(<DataProcessor data={rawData} />);
      });

      // Should process and render 1000 items within 300ms (more realistic for test environment)
      expect(renderTime).toBeLessThan(300);
    });

    it('should optimize with React.useMemo for expensive calculations', async () => {
      let calculationCount = 0;
      
      const ExpensiveComponent = ({ data }: { data: any[] }) => {
        const expensiveValue = React.useMemo(() => {
          calculationCount++;
          return data.reduce((sum, item) => sum + item.value, 0);
        }, [data]);
        
        return <div>Total: {expensiveValue}</div>;
      };
      
      const testData = generateTestData(100);
      const { rerender } = render(<ExpensiveComponent data={testData} />);
      
      // Re-render with same data
      rerender(<ExpensiveComponent data={testData} />);
      
      // Calculation should only happen once due to memoization
      expect(calculationCount).toBe(1);
    });
  });

  describe('Network Performance Simulation', () => {
    it('should handle loading states efficiently', async () => {
      const LoadingComponent = ({ loading }: { loading: boolean }) => (
        <div>
          {loading ? (
            <div data-testid="loading">
              {Array.from({ length: 10 }, (_, i) => (
                <div key={i} className="skeleton-item">Loading...</div>
              ))}
            </div>
          ) : (
            <div data-testid="content">Content loaded</div>
          )}
        </div>
      );
      
      const { rerender } = render(<LoadingComponent loading={true} />);
      
      expect(screen.getByTestId('loading')).toBeInTheDocument();
      
      const loadingToContentTime = await measureRenderTime(() => {
        rerender(<LoadingComponent loading={false} />);
      });

      expect(screen.getByTestId('content')).toBeInTheDocument();
      expect(loadingToContentTime).toBeLessThan(100);
    });
  });
});
