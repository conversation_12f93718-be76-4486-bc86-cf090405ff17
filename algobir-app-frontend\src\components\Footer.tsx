import { Flex, Link, List, ListItem, Text, useColorModeValue } from '@chakra-ui/react';

export default function Footer() {
  const textColor = useColorModeValue('gray.400', 'white');
  const footerBg = useColorModeValue('white', 'navy.800');
  const borderColor = useColorModeValue('gray.200', 'navy.600');
  
  return (
    <Flex
      as="footer"
      zIndex='3'
      flexDirection={{
        base: 'column',
        xl: 'row'
      }}
      alignItems={{
        base: 'center',
        xl: 'start'
      }}
      justifyContent='space-between'
      px={{ base: '30px', md: '50px' }}
      py='30px'
      width="100%"
      bg={footerBg}
      borderTop="1px solid"
      borderColor={borderColor}
      mt="auto"
    >
      <Text
        color={textColor}
        textAlign={{
          base: 'center',
          xl: 'start'
        }}
        mb={{ base: '20px', xl: '0px' }}
      >
        {' '}
        &copy; {new Date().getFullYear()}
        <Text as='span' fontWeight='500' ms='4px'>
          Algobir. Tüm Hakları Saklıdır.
        </Text>
      </Text>
      <List display='flex'>
        <ListItem
          me={{
            base: '20px',
            md: '44px'
          }}
        >
          <Link fontWeight='500' color={textColor} href='mailto:<EMAIL>'>
            Destek
          </Link>
        </ListItem>
        <ListItem
          me={{
            base: '20px',
            md: '44px'
          }}
        >
          <Link fontWeight='500' color={textColor} href='/guide'>
            Rehber
          </Link>
        </ListItem>
        <ListItem
          me={{
            base: '20px',
            md: '44px'
          }}
        >
          <Link fontWeight='500' color={textColor} href='/terms'>
            Kullanım Şartları
          </Link>
        </ListItem>
        <ListItem>
          <Link fontWeight='500' color={textColor} href='/blog'>
            Blog
          </Link>
        </ListItem>
      </List>
    </Flex>
  );
} 