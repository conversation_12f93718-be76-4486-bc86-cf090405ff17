/**
 * Advanced Build Optimizer
 * Phase 3.4: Build & Deployment Process Optimization
 * Comprehensive build optimization with analysis, compression, and performance monitoring
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');
const zlib = require('zlib');
const { promisify } = require('util');

// Configuration
const BUILD_CONFIG = {
  distDir: 'dist',
  analysisDir: 'build-analysis',
  compressionFormats: ['gzip', 'brotli'],
  sizeThresholds: {
    warning: 500 * 1024, // 500KB
    error: 1024 * 1024,  // 1MB
  },
  performanceTargets: {
    totalBundleSize: 2 * 1024 * 1024, // 2MB
    chunkCount: 20,
    duplicateCodeThreshold: 0.05 // 5%
  }
};

// Utility functions
const formatBytes = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatPercentage = (value) => {
  return (value * 100).toFixed(2) + '%';
};

const getFileSize = async (filePath) => {
  try {
    const stats = await fs.stat(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
};

const compressFile = async (filePath, format) => {
  try {
    const data = await fs.readFile(filePath);
    let compressed;
    
    if (format === 'gzip') {
      compressed = await promisify(zlib.gzip)(data);
    } else if (format === 'brotli') {
      compressed = await promisify(zlib.brotliCompress)(data);
    } else {
      throw new Error(`Unsupported compression format: ${format}`);
    }
    
    return compressed.length;
  } catch (error) {
    console.warn(`Failed to compress ${filePath} with ${format}:`, error.message);
    return 0;
  }
};

// Build analysis functions
class BuildAnalyzer {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      buildTime: 0,
      totalSize: 0,
      compressedSizes: {},
      chunks: [],
      assets: [],
      warnings: [],
      errors: [],
      performance: {}
    };
  }

  async analyzeBuild() {
    console.log('🔍 Starting build analysis...');
    
    const startTime = Date.now();
    
    try {
      // Analyze build directory
      await this.analyzeDirectory(BUILD_CONFIG.distDir);
      
      // Calculate compression ratios
      await this.analyzeCompression();
      
      // Analyze bundle composition
      await this.analyzeBundleComposition();
      
      // Check performance targets
      this.checkPerformanceTargets();
      
      // Generate recommendations
      this.generateRecommendations();
      
      this.results.buildTime = Date.now() - startTime;
      
      console.log('✅ Build analysis completed');
      return this.results;
      
    } catch (error) {
      console.error('❌ Build analysis failed:', error);
      this.results.errors.push({
        type: 'analysis_error',
        message: error.message,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  async analyzeDirectory(dirPath) {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          await this.analyzeDirectory(fullPath);
        } else {
          await this.analyzeFile(fullPath);
        }
      }
    } catch (error) {
      console.warn(`Failed to analyze directory ${dirPath}:`, error.message);
    }
  }

  async analyzeFile(filePath) {
    const size = await getFileSize(filePath);
    const relativePath = path.relative(BUILD_CONFIG.distDir, filePath);
    const ext = path.extname(filePath).toLowerCase();
    
    this.results.totalSize += size;
    
    const fileInfo = {
      path: relativePath,
      size,
      formattedSize: formatBytes(size),
      type: this.getFileType(ext),
      extension: ext
    };
    
    // Categorize files
    if (this.isChunkFile(relativePath)) {
      this.results.chunks.push(fileInfo);
    } else {
      this.results.assets.push(fileInfo);
    }
    
    // Check size thresholds
    if (size > BUILD_CONFIG.sizeThresholds.error) {
      this.results.errors.push({
        type: 'file_size_error',
        message: `File ${relativePath} exceeds size limit: ${formatBytes(size)}`,
        file: relativePath,
        size,
        threshold: BUILD_CONFIG.sizeThresholds.error
      });
    } else if (size > BUILD_CONFIG.sizeThresholds.warning) {
      this.results.warnings.push({
        type: 'file_size_warning',
        message: `File ${relativePath} is large: ${formatBytes(size)}`,
        file: relativePath,
        size,
        threshold: BUILD_CONFIG.sizeThresholds.warning
      });
    }
  }

  async analyzeCompression() {
    console.log('📦 Analyzing compression ratios...');
    
    for (const format of BUILD_CONFIG.compressionFormats) {
      let totalCompressed = 0;
      
      // Compress JavaScript files
      const jsFiles = [...this.results.chunks, ...this.results.assets]
        .filter(file => file.extension === '.js');
      
      for (const file of jsFiles) {
        const filePath = path.join(BUILD_CONFIG.distDir, file.path);
        const compressedSize = await compressFile(filePath, format);
        totalCompressed += compressedSize;
      }
      
      // Compress CSS files
      const cssFiles = this.results.assets.filter(file => file.extension === '.css');
      
      for (const file of cssFiles) {
        const filePath = path.join(BUILD_CONFIG.distDir, file.path);
        const compressedSize = await compressFile(filePath, format);
        totalCompressed += compressedSize;
      }
      
      this.results.compressedSizes[format] = {
        size: totalCompressed,
        formattedSize: formatBytes(totalCompressed),
        ratio: totalCompressed / this.results.totalSize,
        savings: this.results.totalSize - totalCompressed
      };
    }
  }

  async analyzeBundleComposition() {
    console.log('🧩 Analyzing bundle composition...');
    
    // Sort chunks by size
    this.results.chunks.sort((a, b) => b.size - a.size);
    
    // Calculate chunk statistics
    const chunkSizes = this.results.chunks.map(chunk => chunk.size);
    const totalChunkSize = chunkSizes.reduce((sum, size) => sum + size, 0);
    
    this.results.performance.chunks = {
      count: this.results.chunks.length,
      totalSize: totalChunkSize,
      averageSize: totalChunkSize / this.results.chunks.length,
      largestChunk: Math.max(...chunkSizes),
      smallestChunk: Math.min(...chunkSizes)
    };
    
    // Analyze asset distribution
    const assetsByType = this.results.assets.reduce((acc, asset) => {
      const type = asset.type;
      if (!acc[type]) {
        acc[type] = { count: 0, totalSize: 0 };
      }
      acc[type].count++;
      acc[type].totalSize += asset.size;
      return acc;
    }, {});
    
    this.results.performance.assets = assetsByType;
  }

  checkPerformanceTargets() {
    console.log('🎯 Checking performance targets...');
    
    const targets = BUILD_CONFIG.performanceTargets;
    const performance = this.results.performance;
    
    // Check total bundle size
    if (this.results.totalSize > targets.totalBundleSize) {
      this.results.warnings.push({
        type: 'bundle_size_warning',
        message: `Total bundle size ${formatBytes(this.results.totalSize)} exceeds target ${formatBytes(targets.totalBundleSize)}`,
        current: this.results.totalSize,
        target: targets.totalBundleSize
      });
    }
    
    // Check chunk count
    if (performance.chunks.count > targets.chunkCount) {
      this.results.warnings.push({
        type: 'chunk_count_warning',
        message: `Chunk count ${performance.chunks.count} exceeds target ${targets.chunkCount}`,
        current: performance.chunks.count,
        target: targets.chunkCount
      });
    }
    
    // Performance score calculation
    let score = 100;
    
    if (this.results.totalSize > targets.totalBundleSize) {
      score -= 20;
    }
    
    if (performance.chunks.count > targets.chunkCount) {
      score -= 10;
    }
    
    if (this.results.errors.length > 0) {
      score -= this.results.errors.length * 15;
    }
    
    if (this.results.warnings.length > 0) {
      score -= this.results.warnings.length * 5;
    }
    
    this.results.performance.score = Math.max(0, score);
  }

  generateRecommendations() {
    const recommendations = [];
    
    // Bundle size recommendations
    if (this.results.totalSize > BUILD_CONFIG.performanceTargets.totalBundleSize) {
      recommendations.push({
        type: 'optimization',
        priority: 'high',
        title: 'Reduce Bundle Size',
        description: 'Consider code splitting, tree shaking, or removing unused dependencies',
        impact: 'High performance improvement'
      });
    }
    
    // Large chunk recommendations
    const largeChunks = this.results.chunks.filter(chunk => 
      chunk.size > BUILD_CONFIG.sizeThresholds.warning
    );
    
    if (largeChunks.length > 0) {
      recommendations.push({
        type: 'optimization',
        priority: 'medium',
        title: 'Split Large Chunks',
        description: `${largeChunks.length} chunks are larger than ${formatBytes(BUILD_CONFIG.sizeThresholds.warning)}`,
        impact: 'Improved loading performance'
      });
    }
    
    // Compression recommendations
    const gzipRatio = this.results.compressedSizes.gzip?.ratio || 1;
    if (gzipRatio > 0.7) {
      recommendations.push({
        type: 'optimization',
        priority: 'low',
        title: 'Improve Compression',
        description: 'Consider optimizing assets for better compression ratios',
        impact: 'Reduced transfer size'
      });
    }
    
    this.results.recommendations = recommendations;
  }

  getFileType(extension) {
    const typeMap = {
      '.js': 'javascript',
      '.css': 'stylesheet',
      '.html': 'html',
      '.png': 'image',
      '.jpg': 'image',
      '.jpeg': 'image',
      '.svg': 'image',
      '.gif': 'image',
      '.webp': 'image',
      '.woff': 'font',
      '.woff2': 'font',
      '.ttf': 'font',
      '.eot': 'font',
      '.json': 'data',
      '.ico': 'icon'
    };
    
    return typeMap[extension] || 'other';
  }

  isChunkFile(filePath) {
    return filePath.includes('/js/') && filePath.endsWith('.js');
  }
}

// Build optimization functions
class BuildOptimizer {
  constructor() {
    this.analyzer = new BuildAnalyzer();
  }

  async optimize() {
    console.log('🚀 Starting build optimization...');
    
    try {
      // Clean previous build
      await this.cleanBuild();
      
      // Run optimized build
      await this.runBuild();
      
      // Analyze build results
      const analysis = await this.analyzer.analyzeBuild();
      
      // Generate reports
      await this.generateReports(analysis);
      
      // Apply post-build optimizations
      await this.postBuildOptimizations();
      
      console.log('✅ Build optimization completed successfully');
      return analysis;
      
    } catch (error) {
      console.error('❌ Build optimization failed:', error);
      throw error;
    }
  }

  async cleanBuild() {
    console.log('🧹 Cleaning previous build...');
    
    try {
      await fs.rm(BUILD_CONFIG.distDir, { recursive: true, force: true });
      await fs.rm(BUILD_CONFIG.analysisDir, { recursive: true, force: true });
      console.log('✅ Build directory cleaned');
    } catch (error) {
      console.warn('⚠️ Failed to clean build directory:', error.message);
    }
  }

  async runBuild() {
    console.log('🔨 Running optimized build...');
    
    try {
      // Set environment variables for optimization
      process.env.NODE_ENV = 'production';
      process.env.ANALYZE_BUNDLE = 'true';
      
      // Run Vite build with optimization config
      execSync('npm run build:optimized', { 
        stdio: 'inherit',
        env: { ...process.env }
      });
      
      console.log('✅ Build completed');
    } catch (error) {
      console.error('❌ Build failed:', error.message);
      throw error;
    }
  }

  async generateReports(analysis) {
    console.log('📊 Generating build reports...');
    
    try {
      // Create analysis directory
      await fs.mkdir(BUILD_CONFIG.analysisDir, { recursive: true });
      
      // Generate JSON report
      await fs.writeFile(
        path.join(BUILD_CONFIG.analysisDir, 'build-analysis.json'),
        JSON.stringify(analysis, null, 2)
      );
      
      // Generate HTML report
      const htmlReport = this.generateHtmlReport(analysis);
      await fs.writeFile(
        path.join(BUILD_CONFIG.analysisDir, 'build-report.html'),
        htmlReport
      );
      
      // Generate summary report
      const summaryReport = this.generateSummaryReport(analysis);
      await fs.writeFile(
        path.join(BUILD_CONFIG.analysisDir, 'build-summary.md'),
        summaryReport
      );
      
      console.log('✅ Reports generated');
    } catch (error) {
      console.error('❌ Failed to generate reports:', error);
    }
  }

  async postBuildOptimizations() {
    console.log('⚡ Applying post-build optimizations...');
    
    try {
      // Create compressed versions of assets
      await this.createCompressedAssets();
      
      // Generate service worker (if PWA enabled)
      if (process.env.VITE_ENABLE_PWA === 'true') {
        await this.optimizeServiceWorker();
      }
      
      console.log('✅ Post-build optimizations completed');
    } catch (error) {
      console.warn('⚠️ Post-build optimizations failed:', error.message);
    }
  }

  async createCompressedAssets() {
    console.log('📦 Creating compressed assets...');
    
    const compressibleExtensions = ['.js', '.css', '.html', '.json', '.svg'];
    
    const compressFile = async (filePath) => {
      const data = await fs.readFile(filePath);
      
      // Create gzip version
      const gzipped = await promisify(zlib.gzip)(data, { level: 9 });
      await fs.writeFile(filePath + '.gz', gzipped);
      
      // Create brotli version
      const brotlied = await promisify(zlib.brotliCompress)(data, {
        params: {
          [zlib.constants.BROTLI_PARAM_QUALITY]: 11
        }
      });
      await fs.writeFile(filePath + '.br', brotlied);
    };
    
    const processDirectory = async (dirPath) => {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          await processDirectory(fullPath);
        } else if (compressibleExtensions.includes(path.extname(entry.name))) {
          await compressFile(fullPath);
        }
      }
    };
    
    await processDirectory(BUILD_CONFIG.distDir);
  }

  async optimizeServiceWorker() {
    console.log('⚙️ Optimizing service worker...');
    
    const swPath = path.join(BUILD_CONFIG.distDir, 'sw.js');
    
    try {
      const swExists = await fs.access(swPath).then(() => true).catch(() => false);
      
      if (swExists) {
        // Add custom optimizations to service worker
        let swContent = await fs.readFile(swPath, 'utf8');
        
        // Add performance optimizations
        const optimizations = `
// Performance optimizations
self.addEventListener('fetch', (event) => {
  if (event.request.destination === 'image') {
    event.respondWith(
      caches.match(event.request).then((response) => {
        return response || fetch(event.request);
      })
    );
  }
});
`;
        
        swContent += optimizations;
        await fs.writeFile(swPath, swContent);
      }
    } catch (error) {
      console.warn('Failed to optimize service worker:', error.message);
    }
  }

  generateHtmlReport(analysis) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Build Analysis Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 40px; }
        .score { font-size: 48px; font-weight: bold; color: ${analysis.performance.score >= 80 ? '#22c55e' : analysis.performance.score >= 60 ? '#f59e0b' : '#ef4444'}; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 40px; }
        .metric { background: #f8fafc; padding: 20px; border-radius: 6px; }
        .metric-title { font-weight: 600; color: #374151; margin-bottom: 8px; }
        .metric-value { font-size: 24px; font-weight: bold; color: #111827; }
        .section { margin-bottom: 40px; }
        .section-title { font-size: 24px; font-weight: bold; margin-bottom: 20px; color: #111827; }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; }
        .table th { background: #f9fafb; font-weight: 600; }
        .warning { background: #fef3c7; color: #92400e; padding: 12px; border-radius: 6px; margin: 8px 0; }
        .error { background: #fee2e2; color: #991b1b; padding: 12px; border-radius: 6px; margin: 8px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Build Analysis Report</h1>
            <div class="score">${analysis.performance.score}/100</div>
            <p>Generated on ${new Date(analysis.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="metrics">
            <div class="metric">
                <div class="metric-title">Total Bundle Size</div>
                <div class="metric-value">${formatBytes(analysis.totalSize)}</div>
            </div>
            <div class="metric">
                <div class="metric-title">Gzip Compressed</div>
                <div class="metric-value">${analysis.compressedSizes.gzip?.formattedSize || 'N/A'}</div>
            </div>
            <div class="metric">
                <div class="metric-title">Chunks</div>
                <div class="metric-value">${analysis.chunks.length}</div>
            </div>
            <div class="metric">
                <div class="metric-title">Build Time</div>
                <div class="metric-value">${(analysis.buildTime / 1000).toFixed(2)}s</div>
            </div>
        </div>
        
        ${analysis.errors.length > 0 ? `
        <div class="section">
            <div class="section-title">Errors</div>
            ${analysis.errors.map(error => `<div class="error">${error.message}</div>`).join('')}
        </div>
        ` : ''}
        
        ${analysis.warnings.length > 0 ? `
        <div class="section">
            <div class="section-title">Warnings</div>
            ${analysis.warnings.map(warning => `<div class="warning">${warning.message}</div>`).join('')}
        </div>
        ` : ''}
        
        <div class="section">
            <div class="section-title">Largest Chunks</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>File</th>
                        <th>Size</th>
                        <th>Type</th>
                    </tr>
                </thead>
                <tbody>
                    ${analysis.chunks.slice(0, 10).map(chunk => `
                    <tr>
                        <td>${chunk.path}</td>
                        <td>${chunk.formattedSize}</td>
                        <td>${chunk.type}</td>
                    </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
    `;
  }

  generateSummaryReport(analysis) {
    return `# Build Analysis Summary

**Generated:** ${new Date(analysis.timestamp).toLocaleString()}
**Performance Score:** ${analysis.performance.score}/100

## Bundle Statistics

- **Total Size:** ${formatBytes(analysis.totalSize)}
- **Gzip Compressed:** ${analysis.compressedSizes.gzip?.formattedSize || 'N/A'} (${formatPercentage(analysis.compressedSizes.gzip?.ratio || 0)} of original)
- **Brotli Compressed:** ${analysis.compressedSizes.brotli?.formattedSize || 'N/A'} (${formatPercentage(analysis.compressedSizes.brotli?.ratio || 0)} of original)
- **Chunks:** ${analysis.chunks.length}
- **Assets:** ${analysis.assets.length}
- **Build Time:** ${(analysis.buildTime / 1000).toFixed(2)}s

## Issues

### Errors (${analysis.errors.length})
${analysis.errors.map(error => `- ${error.message}`).join('\n')}

### Warnings (${analysis.warnings.length})
${analysis.warnings.map(warning => `- ${warning.message}`).join('\n')}

## Recommendations

${analysis.recommendations?.map(rec => `
### ${rec.title} (${rec.priority} priority)
${rec.description}
*Impact: ${rec.impact}*
`).join('\n') || 'No recommendations at this time.'}

## Largest Chunks

${analysis.chunks.slice(0, 5).map(chunk => `- ${chunk.path}: ${chunk.formattedSize}`).join('\n')}
`;
  }
}

// Main execution
if (require.main === module) {
  const optimizer = new BuildOptimizer();
  
  optimizer.optimize()
    .then((analysis) => {
      console.log('\n📊 Build Analysis Summary:');
      console.log(`Performance Score: ${analysis.performance.score}/100`);
      console.log(`Total Size: ${formatBytes(analysis.totalSize)}`);
      console.log(`Chunks: ${analysis.chunks.length}`);
      console.log(`Build Time: ${(analysis.buildTime / 1000).toFixed(2)}s`);
      
      if (analysis.errors.length > 0) {
        console.log(`\n❌ Errors: ${analysis.errors.length}`);
        process.exit(1);
      }
      
      if (analysis.warnings.length > 0) {
        console.log(`\n⚠️ Warnings: ${analysis.warnings.length}`);
      }
      
      console.log('\n✅ Build optimization completed successfully!');
    })
    .catch((error) => {
      console.error('\n❌ Build optimization failed:', error.message);
      process.exit(1);
    });
}

module.exports = { BuildOptimizer, BuildAnalyzer };
