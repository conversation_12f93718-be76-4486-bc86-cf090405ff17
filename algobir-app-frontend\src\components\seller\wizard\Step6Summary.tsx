import React from 'react';
import {
  Box,
  VStack,
  Heading,
  Text,
  HStack,
  Icon,
  Badge,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Divider,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';
import { FaCheck, FaRobot, FaChartLine, FaStore, FaDollarSign, FaClock, FaUsers, FaImage } from 'react-icons/fa';
import { RobotFormData } from '../RobotSetupWizard';

const strategyTypeLabels: Record<string, string> = {
  'trend_following': 'Trend <PERSON>',
  'mean_reversion': 'Ortal<PERSON><PERSON> Dönüş',
  'breakout': '<PERSON>ır<PERSON>lım',
  'momentum': 'Momentum',
  'swing_trading': 'Salınım Ticareti',
  'scalping': 'Scalping',
  'other': 'Diğer',
};

export const Step6Summary: React.FC = () => {
  const { getValues } = useFormContext<RobotFormData>();
  const formData = getValues();

  const calculateMonthlyRevenue = () => {
    if (!formData.price || !formData.subscription_period) return 0;
    return (formData.price / formData.subscription_period) * 30;
  };

  return (
    <Box>
      <VStack spacing={6} align="stretch">
        <Box textAlign="center" mb={4}>
          <HStack justify="center" mb={3}>
            <Icon as={FaCheck} boxSize={8} color="green.500" />
          </HStack>
          <Heading size="lg" mb={2}>Robot Özeti ve Oluştur</Heading>
          <Text color="gray.600">
            Robot ayarlarınızı ve TradingView JSON formatlarınızı gözden geçirin. Her şey doğruysa "Robot Oluştur" butonuna tıklayabilirsiniz.
          </Text>
        </Box>

        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
          {/* Basic Info Card */}
          <Card>
            <CardHeader pb={2}>
              <HStack>
                <Icon as={FaRobot} color="brand.500" />
                <Heading size="md">Temel Bilgiler</Heading>
              </HStack>
            </CardHeader>
            <CardBody pt={2}>
              <VStack align="start" spacing={3}>
                <Box>
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">Robot Adı</Text>
                  <Text fontSize="lg" fontWeight="bold">{formData.name}</Text>
                </Box>
                
                {formData.description && (
                  <Box>
                    <Text fontSize="sm" color="gray.600" fontWeight="medium">Açıklama</Text>
                    <Text fontSize="sm">{formData.description}</Text>
                  </Box>
                )}
                
                <HStack justify="space-between" w="100%">
                  <Box>
                    <Text fontSize="sm" color="gray.600" fontWeight="medium">Versiyon</Text>
                    <Badge colorScheme="blue">{formData.version || '1.0'}</Badge>
                  </Box>
                  {formData.image_url && (
                    <Box>
                      <Text fontSize="sm" color="gray.600" fontWeight="medium">Görsel</Text>
                      <HStack>
                        <Icon as={FaImage} color="green.500" />
                        <Text fontSize="sm" color="green.600">Ayarlandı</Text>
                      </HStack>
                    </Box>
                  )}
                </HStack>
              </VStack>
            </CardBody>
          </Card>

          {/* Trading Settings Card */}
          <Card>
            <CardHeader pb={2}>
              <HStack>
                <Icon as={FaChartLine} color="brand.500" />
                <Heading size="md">Ticaret Ayarları</Heading>
              </HStack>
            </CardHeader>
            <CardBody pt={2}>
              <VStack align="start" spacing={3}>
                <Box>
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">Strateji Türü</Text>
                  <Badge colorScheme="teal" size="lg">
                    {strategyTypeLabels[formData.strategy_type] || formData.strategy_type}
                  </Badge>
                </Box>
                
                <Box>
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">Pozisyon Başına Yatırım</Text>
                  <HStack>
                    <Icon as={FaDollarSign} color="green.500" />
                                          <Text fontSize="xl" fontWeight="bold" color="green.600">
                        ₺{formData.investment_amount_per_position}
                      </Text>
                  </HStack>
                </Box>
              </VStack>
            </CardBody>
          </Card>

          {/* Marketplace Settings Card */}
          <Card>
            <CardHeader pb={2}>
              <HStack>
                <Icon as={FaStore} color="brand.500" />
                <Heading size="md">Pazar Yeri Ayarları</Heading>
              </HStack>
            </CardHeader>
            <CardBody pt={2}>
              <VStack align="start" spacing={3}>
                <HStack justify="space-between" w="100%">
                  <Text fontSize="sm" color="gray.600" fontWeight="medium">Görünürlük</Text>
                  <Badge colorScheme={formData.is_public ? "green" : "purple"}>
                    {formData.is_public ? "Herkese Açık" : "Özel"}
                  </Badge>
                </HStack>
                
                {formData.is_public && (
                  <>
                    <Box>
                      <Text fontSize="sm" color="gray.600" fontWeight="medium">Abonelik Ücreti</Text>
                      <HStack>
                        <Icon as={FaDollarSign} color="green.500" />
                        <Text fontSize="xl" fontWeight="bold" color="green.600">
                          ₺{formData.price}
                        </Text>
                      </HStack>
                    </Box>
                    
                    <Box>
                      <Text fontSize="sm" color="gray.600" fontWeight="medium">Abonelik Süresi</Text>
                      <HStack>
                        <Icon as={FaClock} color="blue.500" />
                        <Text fontWeight="bold">{formData.subscription_period} gün</Text>
                      </HStack>
                    </Box>
                  </>
                )}
              </VStack>
            </CardBody>
          </Card>

          {/* Revenue Projection Card */}
          {formData.is_public && formData.price && formData.subscription_period && (
            <Card>
              <CardHeader pb={2}>
                <HStack>
                  <Icon as={FaUsers} color="brand.500" />
                  <Heading size="md">Gelir Projeksiyonu</Heading>
                </HStack>
              </CardHeader>
              <CardBody pt={2}>
                <VStack spacing={4}>
                  <Box textAlign="center" w="100%">
                    <Text fontSize="sm" color="gray.600" fontWeight="medium">Abone Başına Aylık Gelir</Text>
                    <Text fontSize="2xl" fontWeight="bold" color="green.600">
                      ₺{calculateMonthlyRevenue().toFixed(2)}
                    </Text>
                  </Box>
                  
                  <Divider />
                  
                  <SimpleGrid columns={2} spacing={4} w="100%">
                    <Box textAlign="center">
                      <Text fontSize="lg" fontWeight="bold" color="green.600">
                        ₺{(calculateMonthlyRevenue() * 5).toFixed(0)}
                      </Text>
                      <Text fontSize="xs" color="gray.600">5 abone ile</Text>
                    </Box>
                    <Box textAlign="center">
                      <Text fontSize="lg" fontWeight="bold" color="green.600">
                        ₺{(calculateMonthlyRevenue() * 10).toFixed(0)}
                      </Text>
                      <Text fontSize="xs" color="gray.600">10 abone ile</Text>
                    </Box>
                  </SimpleGrid>
                </VStack>
              </CardBody>
            </Card>
          )}
        </SimpleGrid>

        <Alert status="success" borderRadius="md">
          <AlertIcon />
          <AlertDescription>
            Robotunuz oluşturulduktan sonra webhook URL'inizi alacak ve sinyallerinizi göndermeye başlayabileceksiniz.
            {formData.is_public && " Ayrıca robotunuz pazar yerinde görünecek ve kullanıcılar abone olabilecek."}
          </AlertDescription>
        </Alert>
      </VStack>
    </Box>
  );
}; 