import { mode } from '@chakra-ui/theme-tools';

export const ButtonComponent = {
  components: {
    Button: {
      baseStyle: {
        borderRadius: '16px',
        boxShadow: '45px 76px 113px 7px rgba(112, 144, 176, 0.08)',
        transition: '.25s all ease',
        fontWeight: 'bold',
        boxSizing: 'border-box',
        _focus: {
          boxShadow: 'none'
        },
        _active: {
          boxShadow: 'none'
        }
      },
      variants: {
        solid: (props: any) => ({
          bg: props.colorScheme === 'brand' ? 'brand.500' : undefined,
          color: 'white',
          _hover: {
            bg: props.colorScheme === 'brand' ? 'brand.600' : undefined,
          },
        }),
        brand: (props: any) => ({
          bg: mode('brand.500', 'brand.400')(props),
          color: 'white',
          _focus: {
            bg: mode('brand.500', 'brand.400')(props)
          },
          _active: {
            bg: mode('brand.500', 'brand.400')(props)
          },
          _hover: {
            bg: mode('brand.600', 'brand.400')(props)
          }
        }),
        darkBrand: (props: any) => ({
          bg: mode('brand.900', 'brand.400')(props),
          color: 'white',
          _focus: {
            bg: mode('brand.900', 'brand.400')(props)
          },
          _active: {
            bg: mode('brand.900', 'brand.400')(props)
          },
          _hover: {
            bg: mode('brand.800', 'brand.400')(props)
          }
        }),
        lightBrand: (props: any) => ({
          bg: mode('#E3F2F9', 'whiteAlpha.100')(props),
          color: mode('brand.500', 'white')(props),
          _focus: {
            bg: mode('#E3F2F9', 'whiteAlpha.100')(props)
          },
          _active: {
            bg: mode('secondaryGray.300', 'whiteAlpha.100')(props)
          },
          _hover: {
            bg: mode('secondaryGray.400', 'whiteAlpha.200')(props)
          }
        }),
        light: (props: any) => ({
          bg: mode('secondaryGray.300', 'whiteAlpha.100')(props),
          color: mode('secondaryGray.900', 'white')(props),
          _focus: {
            bg: mode('secondaryGray.300', 'whiteAlpha.100')(props)
          },
          _active: {
            bg: mode('secondaryGray.300', 'whiteAlpha.100')(props)
          },
          _hover: {
            bg: mode('secondaryGray.400', 'whiteAlpha.200')(props)
          }
        }),
        action: (props: any) => ({
          fontWeight: '500',
          borderRadius: '50px',
          bg: mode('secondaryGray.300', 'brand.400')(props),
          color: mode('brand.500', 'white')(props),
          _focus: {
            bg: mode('secondaryGray.300', 'brand.400')(props)
          },
          _active: { bg: mode('secondaryGray.300', 'brand.400')(props) },
          _hover: {
            bg: mode('secondaryGray.200', 'brand.400')(props)
          }
        }),
        outline: () => ({
          borderRadius: '16px'
        })
      },
    },
  },
}; 