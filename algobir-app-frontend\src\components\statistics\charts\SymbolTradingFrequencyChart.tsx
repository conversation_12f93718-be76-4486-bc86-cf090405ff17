import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonGroup,
  Select,
  useColorModeValue,

  Badge,
  Flex,
  Spacer,

  Switch,
  FormControl,
  FormLabel,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb
} from '@chakra-ui/react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Legend,
  Brush
} from 'recharts';
import { SymbolTradingFrequency } from '../../../types/symbolAnalysis';
import { format, parseISO } from 'date-fns';
import { tr } from 'date-fns/locale';

interface SymbolTradingFrequencyChartProps {
  data: SymbolTradingFrequency[];
  height?: number;
  selectedSymbols?: string[];
  onSymbolToggle?: (symbol: string) => void;
  maxSymbols?: number;
}

type TimePeriod = 'daily' | 'weekly' | 'monthly';
type ChartType = 'line' | 'area';
type MetricType = 'tradeCount' | 'volume' | 'pnl';

interface MetricConfig {
  key: MetricType;
  label: string;
  formatValue: (value: number) => string;
  color: string;
}

const metricConfigs: MetricConfig[] = [
  {
    key: 'tradeCount',
    label: 'İşlem Sayısı',
    formatValue: (value: number) => value.toString(),
    color: '#3182CE'
  },
  {
    key: 'volume',
    label: 'İşlem Hacmi',
    formatValue: (value: number) => `₺${value.toFixed(0)}`,
    color: '#38A169'
  },
  {
    key: 'pnl',
    label: 'Kar/Zarar',
    formatValue: (value: number) => `₺${value.toFixed(2)}`,
    color: '#9F7AEA'
  }
];

const SymbolTradingFrequencyChart: React.FC<SymbolTradingFrequencyChartProps> = ({
  data,
  height = 400,
  selectedSymbols = [],
  onSymbolToggle,
  maxSymbols = 10
}) => {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>('daily');
  const [chartType, setChartType] = useState<ChartType>('line');
  const [selectedMetric, setSelectedMetric] = useState<MetricType>('tradeCount');
  const [showTrendLines, setShowTrendLines] = useState(false);
  const [symbolLimit, setSymbolLimit] = useState(5);
  const [showBrush, setShowBrush] = useState(true);

  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const gridColor = useColorModeValue('#f0f0f0', '#2d3748');

  // Color palette for different symbols
  const symbolColors = [
    '#3182CE', '#38A169', '#9F7AEA', '#ED8936', '#E53E3E',
    '#00B5D8', '#D69E2E', '#805AD5', '#DD6B20', '#319795'
  ];

  // Get current metric config
  const currentMetric = metricConfigs.find(m => m.key === selectedMetric) || metricConfigs[0];

  // Process data for chart
  const chartData = useMemo(() => {
    // Filter symbols based on selection or limit
    const symbolsToShow = selectedSymbols.length > 0 
      ? data.filter(item => selectedSymbols.includes(item.symbol))
      : data.slice(0, symbolLimit);

    if (symbolsToShow.length === 0) return [];

    // Get the appropriate frequency data based on time period
    const getFrequencyData = (symbolData: SymbolTradingFrequency) => {
      switch (timePeriod) {
        case 'daily':
          return symbolData.dailyFrequency;
        case 'weekly':
          return symbolData.weeklyFrequency;
        case 'monthly':
          return symbolData.monthlyFrequency;
        default:
          return symbolData.dailyFrequency;
      }
    };

    // Collect all unique dates/periods
    const allPeriods = new Set<string>();
    symbolsToShow.forEach(symbolData => {
      const frequencyData = getFrequencyData(symbolData);
      frequencyData.forEach(item => {
        const period = timePeriod === 'daily' ? (item as any).date :
                      timePeriod === 'weekly' ? (item as any).week :
                      (item as any).month;
        allPeriods.add(period);
      });
    });

    // Sort periods
    const sortedPeriods = Array.from(allPeriods).sort();

    // Create chart data structure
    return sortedPeriods.map(period => {
      const dataPoint: any = { period };
      
      symbolsToShow.forEach((symbolData) => {
        const frequencyData = getFrequencyData(symbolData);
        const periodData = frequencyData.find(item => {
          const itemPeriod = timePeriod === 'daily' ? (item as any).date :
                            timePeriod === 'weekly' ? (item as any).week :
                            (item as any).month;
          return itemPeriod === period;
        });

        dataPoint[symbolData.symbol] = periodData ? periodData[selectedMetric] : 0;
      });

      return dataPoint;
    });
  }, [data, selectedSymbols, symbolLimit, timePeriod, selectedMetric]);

  // Get symbols to display
  const symbolsToDisplay = useMemo(() => {
    return selectedSymbols.length > 0 
      ? data.filter(item => selectedSymbols.includes(item.symbol))
      : data.slice(0, symbolLimit);
  }, [data, selectedSymbols, symbolLimit]);

  // Format date for display
  const formatPeriod = (period: string) => {
    try {
      if (timePeriod === 'daily') {
        return format(parseISO(period), 'dd MMM', { locale: tr });
      } else if (timePeriod === 'weekly') {
        return `Hafta ${period.split('-')[1]}`;
      } else {
        const [year, month] = period.split('-');
        return format(new Date(parseInt(year), parseInt(month) - 1), 'MMM yyyy', { locale: tr });
      }
    } catch {
      return period;
    }
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    return (
      <Box
        bg={bgColor}
        p={4}
        borderRadius="md"
        boxShadow="lg"
        border="1px solid"
        borderColor={borderColor}
        minW="200px"
      >
        <Text fontWeight="bold" mb={2} color={textColor}>
          {formatPeriod(label)}
        </Text>
        <VStack align="start" spacing={1}>
          {payload.map((entry: any, index: number) => (
            <Text key={index} fontSize="sm" color={entry.color}>
              <strong>{entry.dataKey}:</strong> {currentMetric.formatValue(entry.value)}
            </Text>
          ))}
        </VStack>
      </Box>
    );
  };

  // Handle symbol toggle
  const handleSymbolToggle = (symbol: string) => {
    onSymbolToggle?.(symbol);
  };

  return (
    <VStack spacing={4} align="stretch">
      {/* Controls */}
      <Flex wrap="wrap" gap={4} align="center">
        {/* Time Period Selection */}
        <ButtonGroup size="sm" isAttached>
          <Button
            variant={timePeriod === 'daily' ? 'solid' : 'outline'}
            onClick={() => setTimePeriod('daily')}
          >
            Günlük
          </Button>
          <Button
            variant={timePeriod === 'weekly' ? 'solid' : 'outline'}
            onClick={() => setTimePeriod('weekly')}
          >
            Haftalık
          </Button>
          <Button
            variant={timePeriod === 'monthly' ? 'solid' : 'outline'}
            onClick={() => setTimePeriod('monthly')}
          >
            Aylık
          </Button>
        </ButtonGroup>

        {/* Chart Type */}
        <ButtonGroup size="sm" isAttached>
          <Button
            variant={chartType === 'line' ? 'solid' : 'outline'}
            onClick={() => setChartType('line')}
          >
            Çizgi
          </Button>
          <Button
            variant={chartType === 'area' ? 'solid' : 'outline'}
            onClick={() => setChartType('area')}
          >
            Alan
          </Button>
        </ButtonGroup>

        {/* Metric Selection */}
        <HStack>
          <Text fontSize="sm" fontWeight="medium" color={textColor}>
            Metrik:
          </Text>
          <Select
            value={selectedMetric}
            onChange={(e) => setSelectedMetric(e.target.value as MetricType)}
            size="sm"
            w="150px"
          >
            {metricConfigs.map(config => (
              <option key={config.key} value={config.key}>
                {config.label}
              </option>
            ))}
          </Select>
        </HStack>

        <Spacer />

        {/* Options */}
        <HStack>
          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="trend-lines" mb="0" fontSize="sm">
              Trend Çizgileri
            </FormLabel>
            <Switch
              id="trend-lines"
              isChecked={showTrendLines}
              onChange={(e) => setShowTrendLines(e.target.checked)}
              size="sm"
            />
          </FormControl>

          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="brush" mb="0" fontSize="sm">
              Zoom
            </FormLabel>
            <Switch
              id="brush"
              isChecked={showBrush}
              onChange={(e) => setShowBrush(e.target.checked)}
              size="sm"
            />
          </FormControl>
        </HStack>
      </Flex>

      {/* Symbol Limit Slider (when no symbols selected) */}
      {selectedSymbols.length === 0 && (
        <HStack>
          <Text fontSize="sm" color={textColor}>
            Gösterilen Sembol Sayısı:
          </Text>
          <Box w="200px">
            <Slider
              value={symbolLimit}
              onChange={setSymbolLimit}
              min={1}
              max={maxSymbols}
              step={1}
            >
              <SliderTrack>
                <SliderFilledTrack />
              </SliderTrack>
              <SliderThumb />
            </Slider>
          </Box>
          <Text fontSize="sm" color={textColor}>
            {symbolLimit}
          </Text>
        </HStack>
      )}

      {/* Current Settings Info */}
      <HStack>
        <Badge colorScheme="blue" variant="subtle">
          {currentMetric.label}
        </Badge>
        <Badge colorScheme="green" variant="subtle">
          {timePeriod === 'daily' ? 'Günlük' : timePeriod === 'weekly' ? 'Haftalık' : 'Aylık'}
        </Badge>
        <Badge colorScheme="purple" variant="subtle">
          {symbolsToDisplay.length} sembol
        </Badge>
      </HStack>

      {/* Chart */}
      <Box h={`${height}px`} w="100%">
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'line' ? (
            <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
              <XAxis
                dataKey="period"
                tickFormatter={formatPeriod}
                stroke={textColor}
                fontSize={12}
              />
              <YAxis
                tickFormatter={currentMetric.formatValue}
                stroke={textColor}
                fontSize={12}
              />
              <RechartsTooltip content={<CustomTooltip />} />
              <Legend />
              {symbolsToDisplay.map((symbolData, index) => (
                <Line
                  key={symbolData.symbol}
                  type="monotone"
                  dataKey={symbolData.symbol}
                  stroke={symbolColors[index % symbolColors.length]}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
              ))}
              {showBrush && <Brush dataKey="period" height={30} />}
            </LineChart>
          ) : (
            <AreaChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
              <XAxis
                dataKey="period"
                tickFormatter={formatPeriod}
                stroke={textColor}
                fontSize={12}
              />
              <YAxis
                tickFormatter={currentMetric.formatValue}
                stroke={textColor}
                fontSize={12}
              />
              <RechartsTooltip content={<CustomTooltip />} />
              <Legend />
              {symbolsToDisplay.map((symbolData, index) => (
                <Area
                  key={symbolData.symbol}
                  type="monotone"
                  dataKey={symbolData.symbol}
                  stackId="1"
                  stroke={symbolColors[index % symbolColors.length]}
                  fill={symbolColors[index % symbolColors.length]}
                  fillOpacity={0.6}
                />
              ))}
              {showBrush && <Brush dataKey="period" height={30} />}
            </AreaChart>
          )}
        </ResponsiveContainer>
      </Box>

      {/* Symbol Legend with Toggle */}
      {symbolsToDisplay.length > 0 && (
        <Box>
          <Text fontSize="sm" fontWeight="medium" mb={2} color={textColor}>
            Semboller:
          </Text>
          <Flex wrap="wrap" gap={2}>
            {symbolsToDisplay.map((symbolData, index) => (
              <Button
                key={symbolData.symbol}
                size="sm"
                variant={selectedSymbols.includes(symbolData.symbol) ? 'solid' : 'outline'}
                colorScheme="blue"
                onClick={() => handleSymbolToggle(symbolData.symbol)}
                leftIcon={
                  <Box
                    w={3}
                    h={3}
                    borderRadius="full"
                    bg={symbolColors[index % symbolColors.length]}
                  />
                }
              >
                {symbolData.symbol}
              </Button>
            ))}
          </Flex>
        </Box>
      )}

      {/* Summary Stats */}
      <HStack justify="space-between" fontSize="sm" color="gray.500">
        <Text>
          Toplam Veri Noktası: {chartData.length}
        </Text>
        <Text>
          Zaman Aralığı: {chartData.length > 0 ? `${formatPeriod(chartData[0].period)} - ${formatPeriod(chartData[chartData.length - 1].period)}` : 'N/A'}
        </Text>
      </HStack>
    </VStack>
  );
};

export default SymbolTradingFrequencyChart;
