import React, { useState, useMemo } from 'react';
import {
  Box,
  HStack,
  VStack,
  Text,
  Button,
  ButtonGroup,
  useColorModeValue,
  Badge,
  IconButton,
  Tooltip,
  SimpleGrid,
  Progress,

} from '@chakra-ui/react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip as RechartsTooltip,

  Sector
} from 'recharts';
import { DownloadIcon } from '@chakra-ui/icons';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

interface PieDataPoint {
  name: string;
  value: number;
  color?: string;
  percentage?: number;
  [key: string]: any;
}

interface InteractivePieChartProps {
  data: PieDataPoint[];
  title?: string;
  height?: number;
  showLegend?: boolean;
  showPercentages?: boolean;
  showValues?: boolean;
  enableAnimation?: boolean;
  enableHover?: boolean;
  colors?: string[];
  formatValue?: (value: number) => string;
  onSegmentClick?: (data: PieDataPoint) => void;
  centerContent?: React.ReactNode;
  viewMode?: 'pie' | 'donut';
}

const defaultColors = [
  '#3182CE', '#38A169', '#E53E3E', '#D69E2E', '#9F7AEA',
  '#00B5D8', '#DD6B20', '#319795', '#E53E3E', '#805AD5'
];

const InteractivePieChart: React.FC<InteractivePieChartProps> = ({
  data,
  title,
  height = 400,
  showLegend = true,
  enableAnimation = true,
  enableHover = true,
  colors = defaultColors,
  formatValue = (value: number) => `₺${value.toFixed(2)}`,
  onSegmentClick,
  centerContent,
  viewMode: initialViewMode = 'pie'
}) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<'pie' | 'donut'>(initialViewMode);
  const [selectedSegment, setSelectedSegment] = useState<PieDataPoint | null>(null);

  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Calculate percentages and prepare data
  const processedData = useMemo(() => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    return data.map((item, index) => ({
      ...item,
      percentage: total > 0 ? (item.value / total) * 100 : 0,
      color: item.color || colors[index % colors.length]
    }));
  }, [data, colors]);

  // Sort data by value for better visualization
  const sortedData = useMemo(() => {
    return [...processedData].sort((a, b) => b.value - a.value);
  }, [processedData]);

  // Custom active shape for hover effect
  const renderActiveShape = (props: any) => {
    const {
      cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle,
      fill, payload, percent, value
    } = props;

    const RADIAN = Math.PI / 180;
    const sin = Math.sin(-RADIAN * midAngle);
    const cos = Math.cos(-RADIAN * midAngle);
    const sx = cx + (outerRadius + 10) * cos;
    const sy = cy + (outerRadius + 10) * sin;
    const mx = cx + (outerRadius + 30) * cos;
    const my = cy + (outerRadius + 30) * sin;
    const ex = mx + (cos >= 0 ? 1 : -1) * 22;
    const ey = my;
    const textAnchor = cos >= 0 ? 'start' : 'end';

    return (
      <g>
        <text x={cx} y={cy} dy={8} textAnchor="middle" fill={fill} fontSize="12">
          {payload.name}
        </text>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={viewMode === 'donut' ? innerRadius : 0}
          outerRadius={outerRadius + 6}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 6}
          outerRadius={outerRadius + 10}
          fill={fill}
        />
        <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
        <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
        <text 
          x={ex + (cos >= 0 ? 1 : -1) * 12} 
          y={ey} 
          textAnchor={textAnchor} 
          fill="#333"
          fontSize="12"
          fontWeight="bold"
        >
          {`${formatValue(value)}`}
        </text>
        <text 
          x={ex + (cos >= 0 ? 1 : -1) * 12} 
          y={ey} 
          dy={18} 
          textAnchor={textAnchor} 
          fill="#999"
          fontSize="10"
        >
          {`(${(percent * 100).toFixed(1)}%)`}
        </text>
      </g>
    );
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <MotionBox
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          bg={bgColor}
          p={3}
          borderRadius="md"
          boxShadow="lg"
          border="1px"
          borderColor={borderColor}
        >
          <VStack spacing={1} align="start">
            <HStack spacing={2}>
              <Box w={3} h={3} bg={data.color} borderRadius="sm" />
              <Text fontSize="sm" fontWeight="bold">
                {data.name}
              </Text>
            </HStack>
            <Text fontSize="sm">
              Değer: {formatValue(data.value)}
            </Text>
            <Text fontSize="sm">
              Oran: {data.percentage.toFixed(1)}%
            </Text>
          </VStack>
        </MotionBox>
      );
    }
    return null;
  };

  // Handle segment click
  const handleSegmentClick = (data: PieDataPoint) => {
    setSelectedSegment(data);
    onSegmentClick?.(data);
  };

  // Handle mouse enter
  const onPieEnter = (_: any, index: number) => {
    if (enableHover) {
      setActiveIndex(index);
    }
  };

  // Handle mouse leave
  const onPieLeave = () => {
    if (enableHover) {
      setActiveIndex(null);
    }
  };

  const totalValue = processedData.reduce((sum, item) => sum + item.value, 0);

  return (
    <Box bg={bgColor} borderRadius="lg" p={4} border="1px" borderColor={borderColor}>
      {/* Header */}
      <HStack justify="space-between" mb={4}>
        <VStack align="start" spacing={1}>
          {title && (
            <Text fontSize="lg" fontWeight="bold" color={textColor}>
              {title}
            </Text>
          )}
          <HStack spacing={2}>
            <Badge colorScheme="blue" variant="subtle">
              {processedData.length} kategori
            </Badge>
            <Badge colorScheme="green" variant="subtle">
              Toplam: {formatValue(totalValue)}
            </Badge>
          </HStack>
        </VStack>

        <HStack spacing={2}>
          {/* View Mode Toggle */}
          <ButtonGroup size="sm" isAttached variant="outline">
            <Button
              isActive={viewMode === 'pie'}
              onClick={() => setViewMode('pie')}
            >
              Pasta
            </Button>
            <Button
              isActive={viewMode === 'donut'}
              onClick={() => setViewMode('donut')}
            >
              Halka
            </Button>
          </ButtonGroup>

          {/* Export Button */}
          <Tooltip label="Grafiği indir">
            <IconButton
              aria-label="İndir"
              icon={<DownloadIcon />}
              size="sm"
              variant="outline"
            />
          </Tooltip>
        </HStack>
      </HStack>

      <SimpleGrid columns={{ base: 1, lg: showLegend ? 2 : 1 }} spacing={6}>
        {/* Chart */}
        <Box h={`${height}px`} position="relative">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={sortedData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={viewMode === 'donut' ? 120 : 140}
                innerRadius={viewMode === 'donut' ? 60 : 0}
                fill="#8884d8"
                dataKey="value"
                animationBegin={0}
                animationDuration={enableAnimation ? 800 : 0}
                onMouseEnter={onPieEnter}
                onMouseLeave={onPieLeave}
                onClick={handleSegmentClick}
              >
                {sortedData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.color}
                    stroke={activeIndex === index ? '#fff' : 'none'}
                    strokeWidth={activeIndex === index ? 2 : 0}
                    style={{ cursor: 'pointer' }}
                  />
                ))}
              </Pie>
              
              {enableHover && activeIndex !== null && (
                <Pie
                  data={[sortedData[activeIndex]]}
                  cx="50%"
                  cy="50%"
                  activeIndex={0}
                  activeShape={renderActiveShape}
                  outerRadius={viewMode === 'donut' ? 120 : 140}
                  innerRadius={viewMode === 'donut' ? 60 : 0}
                  fill="#8884d8"
                  dataKey="value"
                />
              )}

              <RechartsTooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>

          {/* Center Content for Donut Chart */}
          {viewMode === 'donut' && centerContent && (
            <Box
              position="absolute"
              top="50%"
              left="50%"
              transform="translate(-50%, -50%)"
              textAlign="center"
            >
              {centerContent}
            </Box>
          )}

          {/* Default Center Content */}
          {viewMode === 'donut' && !centerContent && (
            <Box
              position="absolute"
              top="50%"
              left="50%"
              transform="translate(-50%, -50%)"
              textAlign="center"
            >
              <VStack spacing={1}>
                <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                  {formatValue(totalValue)}
                </Text>
                <Text fontSize="sm" color="gray.500">
                  Toplam
                </Text>
              </VStack>
            </Box>
          )}
        </Box>

        {/* Legend and Details */}
        {showLegend && (
          <VStack spacing={4} align="stretch">
            <Text fontSize="md" fontWeight="semibold" color={textColor}>
              Detaylar
            </Text>
            
            <VStack spacing={3} align="stretch" maxH="300px" overflowY="auto">
              {sortedData.map((item, index) => (
                <MotionBox
                  key={item.name}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  p={3}
                  borderRadius="md"
                  border="1px"
                  borderColor={selectedSegment?.name === item.name ? item.color : borderColor}
                  bg={selectedSegment?.name === item.name ? `${item.color}10` : 'transparent'}
                  cursor="pointer"
                  onClick={() => handleSegmentClick(item)}
                  _hover={{ borderColor: item.color, bg: `${item.color}10` }}
                >
                  <VStack spacing={2} align="stretch">
                    <HStack justify="space-between">
                      <HStack spacing={2}>
                        <Box w={3} h={3} bg={item.color} borderRadius="sm" />
                        <Text fontSize="sm" fontWeight="medium">
                          {item.name}
                        </Text>
                      </HStack>
                      <Badge colorScheme="gray" variant="subtle">
                        {item.percentage.toFixed(1)}%
                      </Badge>
                    </HStack>
                    
                    <HStack justify="space-between">
                      <Text fontSize="sm" color="gray.500">
                        {formatValue(item.value)}
                      </Text>
                      <Progress
                        value={item.percentage}
                        size="sm"
                        colorScheme="blue"
                        bg="gray.100"
                        flex={1}
                        ml={4}
                      />
                    </HStack>
                  </VStack>
                </MotionBox>
              ))}
            </VStack>

            {/* Summary Statistics */}
            <Box p={3} bg="gray.50" borderRadius="md">
              <VStack spacing={2} align="stretch">
                <Text fontSize="sm" fontWeight="semibold">
                  Özet İstatistikler
                </Text>
                <HStack justify="space-between">
                  <Text fontSize="xs" color="gray.600">En Büyük:</Text>
                  <Text fontSize="xs" fontWeight="bold">
                    {sortedData[0]?.name} ({sortedData[0]?.percentage.toFixed(1)}%)
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="xs" color="gray.600">En Küçük:</Text>
                  <Text fontSize="xs" fontWeight="bold">
                    {sortedData[sortedData.length - 1]?.name} ({sortedData[sortedData.length - 1]?.percentage.toFixed(1)}%)
                  </Text>
                </HStack>
                <HStack justify="space-between">
                  <Text fontSize="xs" color="gray.600">Ortalama:</Text>
                  <Text fontSize="xs" fontWeight="bold">
                    {(totalValue / sortedData.length).toFixed(0)} ({(100 / sortedData.length).toFixed(1)}%)
                  </Text>
                </HStack>
              </VStack>
            </Box>
          </VStack>
        )}
      </SimpleGrid>
    </Box>
  );
};

export default InteractivePieChart;
