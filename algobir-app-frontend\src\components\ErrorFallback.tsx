import {
  Box,
  Heading,
  Text,
  Button,
  VStack,
  HStack,
  useColorModeValue,
  Icon,
  Collapse,
  useDisclosure,
  Code,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Divider
} from '@chakra-ui/react';
import { FiAlertTriangle, FiRefreshCw, FiChevronDown, FiChevronUp, FiHome } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';

interface ErrorFallbackProps {
  error?: Error | null;
  resetErrorBoundary?: () => void;
  message?: string;
  showDetails?: boolean;
  showHomeButton?: boolean;
  variant?: 'default' | 'minimal' | 'detailed';
}

/**
 * Gelişmiş hata durumu bileşeni - UX optimized
 * Accessibility, user-friendly error handling ve recovery options
 */
const ErrorFallback = ({
  error,
  resetErrorBoundary,
  message = 'Üzgünüz, bir hata o<PERSON>ştu.',
  showDetails = true,
  showHomeButton = true,
  variant = 'default'
}: ErrorFallbackProps) => {
  const navigate = useNavigate();
  const { isOpen: showErrorDetails, onToggle } = useDisclosure();

  // Color mode values
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('red.200', 'red.600');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const errorTextColor = useColorModeValue('red.600', 'red.300');
  const codeColor = useColorModeValue('gray.800', 'gray.100');
  const codeBg = useColorModeValue('gray.50', 'gray.700');

  const handleGoHome = () => {
    navigate('/');
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  // Minimal variant
  if (variant === 'minimal') {
    return (
      <Alert status="error" borderRadius="md" role="alert">
        <AlertIcon />
        <Box>
          <AlertTitle>Hata!</AlertTitle>
          <AlertDescription>{message}</AlertDescription>
        </Box>
        {resetErrorBoundary && (
          <Button
            size="sm"
            colorScheme="red"
            variant="outline"
            onClick={resetErrorBoundary}
            ml="auto"
            leftIcon={<Icon as={FiRefreshCw} />}
          >
            Yeniden Dene
          </Button>
        )}
      </Alert>
    );
  }

  // Detailed variant
  if (variant === 'detailed') {
    return (
      <Box
        p={8}
        bg={bgColor}
        border="2px"
        borderColor={borderColor}
        borderRadius="xl"
        my={6}
        boxShadow="lg"
        role="alert"
        aria-live="assertive"
      >
        <VStack spacing={6} align="stretch">
          {/* Header */}
          <HStack spacing={4}>
            <Icon as={FiAlertTriangle} color="red.500" boxSize={8} />
            <Box>
              <Heading as="h2" size="lg" color={errorTextColor} mb={2}>
                Bir Sorun Oluştu
              </Heading>
              <Text color={textColor} fontSize="md">
                {message}
              </Text>
            </Box>
          </HStack>

          <Divider />

          {/* Error details */}
          {error && showDetails && (
            <Box>
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggle}
                leftIcon={<Icon as={showErrorDetails ? FiChevronUp : FiChevronDown} />}
                color={textColor}
                _hover={{ bg: useColorModeValue('gray.100', 'gray.700') }}
              >
                {showErrorDetails ? 'Detayları Gizle' : 'Hata Detaylarını Göster'}
              </Button>

              <Collapse in={showErrorDetails} animateOpacity>
                <Box mt={4} p={4} bg={codeBg} borderRadius="md" border="1px" borderColor={borderColor}>
                  <Text fontSize="sm" fontWeight="semibold" mb={2} color={textColor}>
                    Hata Mesajı:
                  </Text>
                  <Code
                    p={3}
                    display="block"
                    whiteSpace="pre-wrap"
                    fontSize="sm"
                    bg="transparent"
                    color={codeColor}
                  >
                    {error.message}
                  </Code>

                  {error.stack && (
                    <>
                      <Text fontSize="sm" fontWeight="semibold" mt={4} mb={2} color={textColor}>
                        Stack Trace:
                      </Text>
                      <Code
                        p={3}
                        display="block"
                        whiteSpace="pre-wrap"
                        fontSize="xs"
                        bg="transparent"
                        color={codeColor}
                        maxH="200px"
                        overflowY="auto"
                      >
                        {error.stack}
                      </Code>
                    </>
                  )}
                </Box>
              </Collapse>
            </Box>
          )}

          {/* Action buttons */}
          <HStack spacing={3} justify="center">
            {resetErrorBoundary && (
              <Button
                colorScheme="red"
                onClick={resetErrorBoundary}
                leftIcon={<Icon as={FiRefreshCw} />}
                size="md"
              >
                Yeniden Dene
              </Button>
            )}

            <Button
              variant="outline"
              onClick={handleRefresh}
              leftIcon={<Icon as={FiRefreshCw} />}
              size="md"
            >
              Sayfayı Yenile
            </Button>

            {showHomeButton && (
              <Button
                variant="ghost"
                onClick={handleGoHome}
                leftIcon={<Icon as={FiHome} />}
                size="md"
              >
                Ana Sayfaya Dön
              </Button>
            )}
          </HStack>

          {/* Help text */}
          <Text fontSize="sm" color={textColor} textAlign="center" opacity={0.8}>
            Sorun devam ederse, lütfen destek ekibi ile iletişime geçin.
          </Text>
        </VStack>
      </Box>
    );
  }

  // Default variant
  return (
    <Box
      p={6}
      bg={bgColor}
      border="1px"
      borderColor={borderColor}
      borderRadius="lg"
      my={4}
      boxShadow="md"
      role="alert"
      aria-live="assertive"
    >
      <VStack spacing={5} align="stretch">
        <HStack spacing={3}>
          <Icon as={FiAlertTriangle} color="red.500" boxSize={6} />
          <Heading as="h3" size="md" color={errorTextColor}>
            {message}
          </Heading>
        </HStack>

        {error && (
          <Text color={textColor} fontSize="sm">
            {error.message}
          </Text>
        )}

        <HStack spacing={3}>
          {resetErrorBoundary && (
            <Button
              colorScheme="red"
              onClick={resetErrorBoundary}
              size="sm"
              leftIcon={<Icon as={FiRefreshCw} />}
            >
              Yeniden Dene
            </Button>
          )}

          <Button
            variant="outline"
            onClick={handleRefresh}
            size="sm"
            leftIcon={<Icon as={FiRefreshCw} />}
          >
            Sayfayı Yenile
          </Button>
        </HStack>
      </VStack>
    </Box>
  );
};

/**
 * API isteklerinde oluşacak hatalar için özelleştirilmiş hata mesajları
 */
export const getErrorMessage = (error: unknown): string => {
  // Supabase hata mesajları
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as { code?: string; message?: string };
    
    if (errorObj.code === 'PGRST301') {
      return 'Kaynak bulunamadı.';
    }
    
    if (errorObj.code?.startsWith('23')) {
      return 'Veri tabanı hatası oluştu.';
    }
    
    // Ağ hataları
    if (errorObj.message?.includes('network')) {
      return 'İnternet bağlantınızı kontrol edin.';
    }
    
    // Eğer mesaj varsa döndür
    if (errorObj.message) {
      return errorObj.message;
    }
  }
  
  // Varsayılan mesaj
  return 'Beklenmeyen bir hata oluştu.';
};

export default ErrorFallback; 