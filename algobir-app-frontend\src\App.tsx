import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { Box, ChakraProvider, Spinner, Flex } from '@chakra-ui/react';
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/react';
import { lazy, Suspense, useEffect } from 'react';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';
import { useAuth } from './context/AuthContext';
import { SidebarProvider } from './context/SidebarContext';
import theme from './theme';
import ErrorBoundary from './components/ErrorBoundary';
import { preloadCommonRoutes, preloadRelatedRoutes } from './utils/routePreloader';
import { initializePerformanceOptimizations } from './utils/performanceOptimizer';
import { initializeAccessibility } from './utils/accessibilityUtils';

// Performance optimized lazy loading with chunk names and preloading
const Dashboard = lazy(() => import(/* webpackChunkName: "dashboard" */ './pages/Dashboard'));
const Trades = lazy(() => import(/* webpackChunkName: "trades" */ './pages/Trades'));
const Statistics = lazy(() => import(/* webpackChunkName: "statistics" */ './pages/Statistics'));
const Management = lazy(() => import(/* webpackChunkName: "management" */ './pages/Management'));
const Guide = lazy(() => import(/* webpackChunkName: "guide" */ './pages/guide/GuidePage'));
const SoloRobotGuide = lazy(() => import(/* webpackChunkName: "solo-guide" */ './pages/guide/SoloRobotGuide'));
const BroRobotGuide = lazy(() => import(/* webpackChunkName: "bro-guide" */ './pages/guide/BroRobotGuide'));
const FAQPage = lazy(() => import(/* webpackChunkName: "faq" */ './pages/guide/FAQPage'));
const Profile = lazy(() => import(/* webpackChunkName: "profile" */ './pages/Profile'));
const AuthPage = lazy(() => import(/* webpackChunkName: "auth" */ './pages/AuthPage'));
const OpenPositions = lazy(() => import(/* webpackChunkName: "open-positions" */ './pages/OpenPositions'));
const Marketplace = lazy(() => import(/* webpackChunkName: "marketplace" */ './pages/marketplace/MarketplacePage'));
const RobotDetails = lazy(() => import(/* webpackChunkName: "robot-details" */ './pages/RobotDetails'));
const RobotManagementPage = lazy(() => import(/* webpackChunkName: "robot-management" */ './pages/seller/RobotManagementPage'));
const UserPage = lazy(() => import(/* webpackChunkName: "user-page" */ './pages/user/UserPage'));

// Bildirim sayfaları
const Notifications = lazy(() => import(/* webpackChunkName: "notifications" */ './pages/Notifications'));

// Admin sayfaları - lazy load (daha az kullanılan sayfalar)
const UserManagement = lazy(() => import(/* webpackChunkName: "admin-users" */ './pages/admin/UserManagement'));
const AdminDashboard = lazy(() => import(/* webpackChunkName: "admin-dashboard" */ './pages/admin/AdminDashboard'));
const AllTradesView = lazy(() => import(/* webpackChunkName: "admin-trades" */ './pages/admin/AllTradesView'));
const NotificationManagement = lazy(() => import(/* webpackChunkName: "admin-notifications" */ './pages/admin/NotificationManagement'));
const CreateAnnouncement = lazy(() => import(/* webpackChunkName: "admin-create-announcement" */ './pages/admin/CreateAnnouncement'));
const AdminStatistics = lazy(() => import(/* webpackChunkName: "admin-statistics" */ './pages/admin/AdminStatistics'));
const AdminStatus = lazy(() => import(/* webpackChunkName: "admin-status" */ './pages/admin/AdminStatus'));

// Statistics sub-pages
const StatisticsRedirect = lazy(() => import(/* webpackChunkName: "statistics-redirect" */ './components/statistics/StatisticsRedirect'));
const OverviewPage = lazy(() => import(/* webpackChunkName: "statistics-overview" */ './pages/statistics/OverviewPage'));
const ROIAnalysisPage = lazy(() => import(/* webpackChunkName: "statistics-roi" */ './pages/statistics/ROIAnalysisPage'));
const PerformancePage = lazy(() => import(/* webpackChunkName: "statistics-performance" */ './pages/statistics/PerformancePage'));
const SoloRobotPage = lazy(() => import(/* webpackChunkName: "statistics-solo" */ './pages/statistics/SoloRobotPage'));
const BroRobotsPage = lazy(() => import(/* webpackChunkName: "statistics-bro" */ './pages/statistics/BroRobotsPage'));
const RobotComparisonPage = lazy(() => import(/* webpackChunkName: "statistics-comparison" */ './pages/statistics/RobotComparisonPage'));
const RiskAnalysisPage = lazy(() => import(/* webpackChunkName: "statistics-risk" */ './pages/statistics/RiskAnalysisPage'));
const TimeAnalysisPage = lazy(() => import(/* webpackChunkName: "statistics-time" */ './pages/statistics/TimeAnalysisPage'));
const SymbolAnalysisPage = lazy(() => import(/* webpackChunkName: "statistics-symbol" */ './pages/statistics/SymbolAnalysisPage'));
const ReportsPage = lazy(() => import(/* webpackChunkName: "statistics-reports" */ './pages/statistics/ReportsPage'));

// Loading component
const PageLoader = () => (
  <Flex justify="center" align="center" minH="60vh">
    <Spinner size="lg" color="brand.500" thickness="4px" />
  </Flex>
);

function App() {
  const { loading } = useAuth();
  const location = useLocation();

  // Initialize optimizations: Performance monitoring, accessibility, and route preloading
  useEffect(() => {
    if (!loading) {
      // Initialize performance optimizations
      initializePerformanceOptimizations();

      // Initialize accessibility features
      initializeAccessibility();

      // Preload common routes
      preloadCommonRoutes();

      // Preload related routes based on current location
      preloadRelatedRoutes(location.pathname);
    }
  }, [loading, location.pathname]);

  if (loading) {
    // Yükleme sırasında bir yükleme göstergesi gösterebiliriz
    return (
      <ChakraProvider theme={theme}>
        <Box p={4}>Yükleniyor...</Box>
      </ChakraProvider>
    );
  }

  return (
    <ChakraProvider theme={theme}>
      <SidebarProvider>
        <ErrorBoundary>
          <Box minH="100vh">
            <Suspense fallback={<PageLoader />}>
              <Routes location={location} key={location.pathname}>
                {/* Genel Rotalar */}
                <Route path="/login" element={<AuthPage />} />
                
                {/* Layout ile sarılacak rotalar */}
                <Route path="/" element={<Layout />}>
                  {/* Public Routes (Authentication gerektirmeyen) */}
                  <Route path="user/:username" element={<UserPage />} />
                  <Route path="marketplace/robot/:robotId" element={<RobotDetails />} />
                  
                  {/* Protected Routes */}
                  <Route index element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
                  <Route path="open-positions" element={<ProtectedRoute><OpenPositions /></ProtectedRoute>} />
                  <Route path="trades" element={<ProtectedRoute><Trades /></ProtectedRoute>} />
                  <Route path="statistics" element={<ProtectedRoute><Statistics /></ProtectedRoute>}>
                    <Route index element={<StatisticsRedirect />} />
                    <Route path="overview" element={<OverviewPage />} />
                    <Route path="roi-analysis" element={<ROIAnalysisPage />} />
                    <Route path="performance" element={<PerformancePage />} />
                    <Route path="solo-robot" element={<SoloRobotPage />} />
                    <Route path="bro-robots" element={<BroRobotsPage />} />
                    <Route path="robot-comparison" element={<RobotComparisonPage />} />
                    <Route path="risk-analysis" element={<RiskAnalysisPage />} />
                    <Route path="time-analysis" element={<TimeAnalysisPage />} />
                    <Route path="symbol-analysis" element={<SymbolAnalysisPage />} />
                    <Route path="reports" element={<ReportsPage />} />
                  </Route>
                  <Route path="marketplace" element={<ProtectedRoute><Marketplace /></ProtectedRoute>} />
                  <Route path="management" element={<ProtectedRoute><Management /></ProtectedRoute>} />
                  <Route path="guide" element={<ProtectedRoute><Guide /></ProtectedRoute>} />
                  <Route path="guide/solo-robot" element={<ProtectedRoute><SoloRobotGuide /></ProtectedRoute>} />
                  <Route path="guide/bro-robot" element={<ProtectedRoute><BroRobotGuide /></ProtectedRoute>} />
                  <Route path="guide/faq" element={<ProtectedRoute><FAQPage /></ProtectedRoute>} />
                  <Route path="profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
                  <Route path="seller/robots" element={<ProtectedRoute><RobotManagementPage /></ProtectedRoute>} />
                  <Route path="notifications" element={<ProtectedRoute><Notifications /></ProtectedRoute>} />
                </Route>

                {/* Admin Rotaları */}
                <Route path="/admin" element={<AdminRoute><Layout /></AdminRoute>}>
                  <Route index element={<AdminDashboard />} />
                  <Route path="users" element={<UserManagement />} />
                  <Route path="trades" element={<AllTradesView />} />
                  <Route path="notifications" element={<NotificationManagement />} />
                  <Route path="notifications/create" element={<CreateAnnouncement />} />
                  <Route path="statistics" element={<AdminStatistics />} />
                  <Route path="status" element={<AdminStatus />} />
                </Route>

                {/* Bulunamayan sayfalar için yönlendirme */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Suspense>
            
            {/* Vercel Analytics ve Speed Insights bileşenleri */}
            <Analytics />
            <SpeedInsights />
          </Box>
        </ErrorBoundary>
      </SidebarProvider>
    </ChakraProvider>
  );
}

export default App; 