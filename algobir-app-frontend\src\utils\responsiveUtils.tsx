/**
 * Advanced Responsive Utilities
 * Comprehensive responsive design helpers with mobile-first approach
 */

import React from 'react';
import { useMediaQuery } from '@chakra-ui/react';
import { useEffect, useState } from 'react';

// Enhanced breakpoint definitions
export const BREAKPOINTS = {
  xs: '320px',    // Extra small phones
  sm: '480px',    // Small phones
  md: '768px',    // Tablets
  lg: '992px',    // Small desktops
  xl: '1280px',   // Large desktops
  '2xl': '1536px', // Extra large screens
  '3xl': '1920px', // Ultra wide screens
} as const;

// Device type definitions
export type DeviceType = 'mobile' | 'tablet' | 'desktop' | 'ultrawide';
export type ScreenSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';

// Responsive value type
export type ResponsiveValue<T> = T | {
  base?: T;
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
  '3xl'?: T;
};

// Touch target sizes (following accessibility guidelines)
export const TOUCH_TARGETS = {
  minimum: '44px',    // WCAG minimum
  comfortable: '48px', // Recommended
  large: '56px',      // Large touch targets
} as const;

// Spacing scales for different screen sizes
export const RESPONSIVE_SPACING = {
  xs: { base: 2, container: 4 },
  sm: { base: 3, container: 6 },
  md: { base: 4, container: 8 },
  lg: { base: 6, container: 12 },
  xl: { base: 8, container: 16 },
  '2xl': { base: 10, container: 20 },
} as const;

// Typography scales
export const RESPONSIVE_TYPOGRAPHY = {
  heading: {
    h1: { base: '2xl', md: '3xl', lg: '4xl', xl: '5xl' },
    h2: { base: 'xl', md: '2xl', lg: '3xl', xl: '4xl' },
    h3: { base: 'lg', md: 'xl', lg: '2xl', xl: '3xl' },
    h4: { base: 'md', md: 'lg', lg: 'xl', xl: '2xl' },
    h5: { base: 'sm', md: 'md', lg: 'lg', xl: 'xl' },
    h6: { base: 'xs', md: 'sm', lg: 'md', xl: 'lg' },
  },
  body: {
    large: { base: 'md', md: 'lg', lg: 'xl' },
    normal: { base: 'sm', md: 'md', lg: 'lg' },
    small: { base: 'xs', md: 'sm', lg: 'md' },
  }
} as const;

// Grid configurations
export const RESPONSIVE_GRIDS = {
  cards: {
    base: 1,
    sm: 2,
    md: 2,
    lg: 3,
    xl: 4,
    '2xl': 5
  },
  dashboard: {
    base: 1,
    md: 2,
    lg: 3,
    xl: 4
  },
  marketplace: {
    base: 1,
    sm: 2,
    lg: 3,
    xl: 4,
    '2xl': 5
  },
  statistics: {
    base: 1,
    md: 2,
    xl: 3
  }
} as const;

// Custom hooks for responsive behavior
export const useDeviceType = (): DeviceType => {
  const [isMobile] = useMediaQuery(`(max-width: ${BREAKPOINTS.md})`);
  const [isTablet] = useMediaQuery(`(min-width: ${BREAKPOINTS.md}) and (max-width: ${BREAKPOINTS.lg})`);
  const [isUltrawide] = useMediaQuery(`(min-width: ${BREAKPOINTS['3xl']})`);

  if (isMobile) return 'mobile';
  if (isTablet) return 'tablet';
  if (isUltrawide) return 'ultrawide';
  return 'desktop';
};

export const useScreenSize = (): ScreenSize => {
  const [isXs] = useMediaQuery(`(max-width: ${BREAKPOINTS.sm})`);
  const [isSm] = useMediaQuery(`(min-width: ${BREAKPOINTS.sm}) and (max-width: ${BREAKPOINTS.md})`);
  const [isMd] = useMediaQuery(`(min-width: ${BREAKPOINTS.md}) and (max-width: ${BREAKPOINTS.lg})`);
  const [isLg] = useMediaQuery(`(min-width: ${BREAKPOINTS.lg}) and (max-width: ${BREAKPOINTS.xl})`);
  const [isXl] = useMediaQuery(`(min-width: ${BREAKPOINTS.xl}) and (max-width: ${BREAKPOINTS['2xl']})`);
  const [is2xl] = useMediaQuery(`(min-width: ${BREAKPOINTS['2xl']}) and (max-width: ${BREAKPOINTS['3xl']})`);
  const [is3xl] = useMediaQuery(`(min-width: ${BREAKPOINTS['3xl']})`);

  if (isXs) return 'xs';
  if (isSm) return 'sm';
  if (isMd) return 'md';
  if (isLg) return 'lg';
  if (isXl) return 'xl';
  if (is2xl) return '2xl';
  if (is3xl) return '3xl';
  return 'lg'; // fallback
};

export const useIsMobile = (): boolean => {
  const [isMobile] = useMediaQuery(`(max-width: ${BREAKPOINTS.md})`);
  return isMobile;
};

export const useIsTablet = (): boolean => {
  const [isTablet] = useMediaQuery(`(min-width: ${BREAKPOINTS.md}) and (max-width: ${BREAKPOINTS.lg})`);
  return isTablet;
};

export const useIsDesktop = (): boolean => {
  const [isDesktop] = useMediaQuery(`(min-width: ${BREAKPOINTS.lg})`);
  return isDesktop;
};

// Touch device detection
export const useIsTouchDevice = (): boolean => {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    const checkTouchDevice = () => {
      return 'ontouchstart' in window || 
             navigator.maxTouchPoints > 0 || 
             (window as any).DocumentTouch && document instanceof (window as any).DocumentTouch;
    };

    setIsTouchDevice(checkTouchDevice());
  }, []);

  return isTouchDevice;
};

// Orientation detection
export const useOrientation = (): 'portrait' | 'landscape' => {
  const [isLandscape] = useMediaQuery('(orientation: landscape)');
  return isLandscape ? 'landscape' : 'portrait';
};

// Viewport dimensions
export const useViewportSize = () => {
  const [size, setSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });

  useEffect(() => {
    const handleResize = () => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return size;
};

// Responsive value resolver
export const getResponsiveValue = <T extends any>(
  value: ResponsiveValue<T>,
  screenSize: ScreenSize
): T => {
  if (typeof value !== 'object' || value === null) {
    return value as T;
  }

  const responsiveValue = value as Record<string, T>;
  
  // Priority order for fallbacks
  const fallbackOrder: ScreenSize[] = ['3xl', '2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = fallbackOrder.indexOf(screenSize);
  
  // Check current size first
  if (responsiveValue[screenSize] !== undefined) {
    return responsiveValue[screenSize];
  }
  
  // Check base value
  if (responsiveValue.base !== undefined) {
    return responsiveValue.base;
  }
  
  // Fallback to smaller sizes
  for (let i = currentIndex + 1; i < fallbackOrder.length; i++) {
    const fallbackSize = fallbackOrder[i];
    if (responsiveValue[fallbackSize] !== undefined) {
      return responsiveValue[fallbackSize];
    }
  }
  
  // Fallback to larger sizes
  for (let i = currentIndex - 1; i >= 0; i--) {
    const fallbackSize = fallbackOrder[i];
    if (responsiveValue[fallbackSize] !== undefined) {
      return responsiveValue[fallbackSize];
    }
  }
  
  // Return first available value
  const firstValue = Object.values(responsiveValue)[0];
  return firstValue as T;
};

// Container width utilities
export const getContainerWidth = (variant: 'page' | 'section' | 'card' | 'full' = 'page') => {
  const configs = {
    page: { base: '100%', sm: 'container.sm', md: 'container.md', lg: 'container.lg', xl: 'container.xl' },
    section: { base: '100%', md: 'container.md', lg: 'container.lg' },
    card: { base: '100%', sm: '400px', md: '500px', lg: '600px' },
    full: '100%'
  };
  
  return configs[variant];
};

// Spacing utilities
export const getResponsiveSpacing = (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md') => {
  return RESPONSIVE_SPACING[size];
};

// Grid utilities
export const getResponsiveGrid = (type: keyof typeof RESPONSIVE_GRIDS) => {
  return RESPONSIVE_GRIDS[type];
};

// Typography utilities
export const getResponsiveTypography = (
  type: 'heading' | 'body',
  variant: string
) => {
  if (type === 'heading' && variant in RESPONSIVE_TYPOGRAPHY.heading) {
    return RESPONSIVE_TYPOGRAPHY.heading[variant as keyof typeof RESPONSIVE_TYPOGRAPHY.heading];
  }
  
  if (type === 'body' && variant in RESPONSIVE_TYPOGRAPHY.body) {
    return RESPONSIVE_TYPOGRAPHY.body[variant as keyof typeof RESPONSIVE_TYPOGRAPHY.body];
  }
  
  return { base: 'md' };
};

// Safe area utilities (for mobile devices with notches)
export const useSafeArea = () => {
  const [safeArea, setSafeArea] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  useEffect(() => {
    const updateSafeArea = () => {
      const style = getComputedStyle(document.documentElement);
      setSafeArea({
        top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
        right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
        bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
        left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0'),
      });
    };

    updateSafeArea();
    window.addEventListener('resize', updateSafeArea);
    window.addEventListener('orientationchange', updateSafeArea);

    return () => {
      window.removeEventListener('resize', updateSafeArea);
      window.removeEventListener('orientationchange', updateSafeArea);
    };
  }, []);

  return safeArea;
};

// Performance utilities
export const useReducedMotion = (): boolean => {
  const [prefersReducedMotion] = useMediaQuery('(prefers-reduced-motion: reduce)');
  return prefersReducedMotion;
};

export const useHighContrast = (): boolean => {
  const [prefersHighContrast] = useMediaQuery('(prefers-contrast: high)');
  return prefersHighContrast;
};

// Responsive component wrapper
export const withResponsive = <P extends object = {}>(
  Component: React.ComponentType<P>
) => {
  return (props: P) => {
    const deviceType = useDeviceType();
    const screenSize = useScreenSize();
    const isTouchDevice = useIsTouchDevice();
    const orientation = useOrientation();
    const reducedMotion = useReducedMotion();

    const responsiveProps = {
      ...props,
      deviceType,
      screenSize,
      isTouchDevice,
      orientation,
      reducedMotion,
    };

    return <Component {...responsiveProps} />;
  };
};

export default {
  BREAKPOINTS,
  TOUCH_TARGETS,
  RESPONSIVE_SPACING,
  RESPONSIVE_TYPOGRAPHY,
  RESPONSIVE_GRIDS,
  useDeviceType,
  useScreenSize,
  useIsMobile,
  useIsTablet,
  useIsDesktop,
  useIsTouchDevice,
  useOrientation,
  useViewportSize,
  getResponsiveValue,
  getContainerWidth,
  getResponsiveSpacing,
  getResponsiveGrid,
  getResponsiveTypography,
  useSafeArea,
  useReducedMotion,
  useHighContrast,
  withResponsive,
};
