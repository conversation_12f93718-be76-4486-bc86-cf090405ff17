---
type: "manual"
priority: "high"
scope: ["user-profile", "forms", "validation", "privacy", "avatar-upload"]
last_updated: "2025-01-30"
created: "2025-01-30"
parent_rules: ["core/PROJECT_CORE.mdc", "frontend/MAIN_APP.mdc", "security/GUIDELINES.mdc"]
---

# User Profile Management Feature

## 🎯 Feature Overview

User profile management allows users to:
- Edit personal information (username, full name, bio)
- Upload and manage avatar images
- Control privacy settings for public visibility
- Manage account security settings

## 📋 Current Implementation Status

### Existing Components
```typescript
// Profile.tsx - Main profile editing page
interface ProfileFormData {
  username: string;
  full_name: string;
  avatar_url: string;
  bio: string;                    // ✅ ALREADY EXISTS - Textarea implementation
  display_full_name_publicly: boolean;
  display_bio_publicly: boolean;
  display_robots_publicly: boolean;
  url_slug: string;
}

// UserPage.tsx - Public profile display
- Shows user information publicly
- Displays bio if user allows public visibility
- Responsive design implemented
```

### Database Schema
```sql
-- profiles table structure
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  username TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  bio TEXT,                      -- ✅ ALREADY EXISTS
  display_full_name_publicly BOOLEAN DEFAULT false,
  display_bio_publicly BOOLEAN DEFAULT false,
  display_robots_publicly BOOLEAN DEFAULT true,
  url_slug TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔧 Form Implementation Rules

### React Hook Form Integration
```typescript
// Form validation schema (following core rules)
import { useForm } from 'react-hook-form';
import { sanitizeString, validateForm, commonSchemas } from '../utils/security';

const profileValidationSchema = {
  username: {
    required: true,
    minLength: 3,
    maxLength: 30,
    pattern: /^[a-zA-Z0-9_-]+$/,
    message: 'Username must be 3-30 characters, alphanumeric with _ or -'
  },
  full_name: {
    required: false,
    maxLength: 100,
    message: 'Full name must be less than 100 characters'
  },
  bio: {
    required: false,
    maxLength: 500,                // ✅ Bio character limit
    message: 'Bio must be less than 500 characters'
  }
};

// Form implementation with security
const { register, handleSubmit, formState: { errors } } = useForm({
  defaultValues: profileData,
  mode: 'onChange'
});

const onSubmit = async (data: ProfileFormData) => {
  // Sanitize inputs (following security guidelines)
  const sanitizedData = {
    username: sanitizeString(data.username),
    full_name: sanitizeString(data.full_name),
    bio: sanitizeString(data.bio),    // ✅ Bio sanitization
    avatar_url: data.avatar_url
  };
  
  // Validate
  const { isValid, errors } = validateForm(sanitizedData, profileValidationSchema);
  if (!isValid) return;
  
  // Update profile
  await updateProfile(sanitizedData);
};
```

### Bio Textarea Implementation (ALREADY EXISTS)
```typescript
// Current bio implementation in Profile.tsx (lines 663-700)
<FormControl>
  <FormLabel 
    fontSize={fontSize}
    fontWeight="600" 
    color={textColor}
    mb={{ base: '6px', md: '8px' }}
  >
    Biyografi (İsteğe bağlı)
  </FormLabel>
  <Textarea
    value={formData.bio}
    onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
    placeholder="Kendiniz hakkında kısa bir açıklama yazın..."
    fontSize={fontSize}
    fontWeight="500"
    borderRadius="16px"
    minH="100px"                   // ✅ Responsive height
    border="1px solid"
    borderColor={borderColor}
    bg={inputBg}
    resize="vertical"              // ✅ User can resize
    maxLength={500}                // ✅ Character limit
    _focus={{
      borderColor: brandColor,
      boxShadow: `0 0 0 1px ${brandColor}`,
    }}
    _placeholder={{ 
      color: "secondaryGray.600", 
      fontWeight: "400",
      fontSize: fontSize
    }}
  />
  <FormHelperText color={textColorSecondary} fontSize="xs">
    Maksimum 500 karakter
  </FormHelperText>
</FormControl>
```

## 🛡️ Security Implementation

### Input Validation & Sanitization
```typescript
// Bio-specific security measures
const validateBio = (bio: string): string[] => {
  const errors: string[] = [];
  
  if (bio.length > 500) {
    errors.push('Bio 500 karakterden uzun olamaz');
  }
  
  // Check for malicious content
  if (bio.includes('<script>') || bio.includes('javascript:')) {
    errors.push('Bio güvenlik nedeniyle reddedildi');
  }
  
  return errors;
};

// Sanitization before save
const sanitizeBio = (bio: string): string => {
  return sanitizeString(bio)
    .replace(/[<>]/g, '')           // Remove HTML tags
    .replace(/javascript:/gi, '')   // Remove javascript: protocol
    .trim()
    .slice(0, 500);                // Enforce length limit
};
```

### Privacy Controls
```typescript
// Bio visibility control (ALREADY IMPLEMENTED)
<FormControl display="flex" alignItems="center" justifyContent="space-between">
  <Box>
    <FormLabel fontSize={fontSize} fontWeight="600" color={textColor} mb="0">
      Biyografiyi Herkese Açık Göster
    </FormLabel>
    <Text fontSize="xs" color={textColorSecondary}>
      Diğer kullanıcılar biyografinizi görebilir
    </Text>
  </Box>
  <Switch
    colorScheme="brand"
    isChecked={formData.display_bio_publicly}
    onChange={(e) => setFormData(prev => ({ 
      ...prev, 
      display_bio_publicly: e.target.checked 
    }))}
  />
</FormControl>
```

## 📱 Responsive Design Rules

### Mobile-First Bio Textarea
```typescript
// Responsive bio textarea (ALREADY IMPLEMENTED)
<Textarea
  minH={{ base: "80px", md: "100px", lg: "120px" }}  // Responsive height
  fontSize={{ base: "sm", md: "md" }}                // Responsive font
  p={{ base: 3, md: 4 }}                            // Responsive padding
  borderRadius={{ base: "12px", md: "16px" }}       // Responsive border radius
  resize="vertical"                                   // Allow vertical resize only
  _focus={{
    borderColor: brandColor,
    boxShadow: { 
      base: `0 0 0 1px ${brandColor}`, 
      md: `0 0 0 2px ${brandColor}` 
    }
  }}
/>
```

### Touch Optimization
```typescript
// Touch-friendly bio editing (ENHANCEMENT NEEDED)
import { useTouchInteractions } from '../hooks/useTouchInteractions';

const bioTextareaRef = useRef<HTMLTextAreaElement>(null);

// Touch interactions for better mobile UX
const { } = useTouchInteractions(
  bioTextareaRef,
  { enableTap: true, enableLongPress: true },
  {
    onTap: () => bioTextareaRef.current?.focus(),
    onLongPress: () => {
      // Show formatting options or character count
      showBioHelp();
    }
  }
);
```

## 🎨 UX Enhancement Rules

### Character Counter (ENHANCEMENT NEEDED)
```typescript
// Bio character counter component
const BioCharacterCounter: React.FC<{ value: string; maxLength: number }> = ({ 
  value, 
  maxLength 
}) => {
  const remaining = maxLength - value.length;
  const isNearLimit = remaining < 50;
  const isOverLimit = remaining < 0;
  
  return (
    <Text 
      fontSize="xs" 
      color={isOverLimit ? 'red.500' : isNearLimit ? 'orange.500' : 'gray.500'}
      textAlign="right"
    >
      {remaining} karakter kaldı
    </Text>
  );
};

// Usage in bio textarea
<FormControl>
  <FormLabel>Biyografi</FormLabel>
  <Textarea
    value={formData.bio}
    onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
    maxLength={500}
  />
  <Flex justify="space-between" align="center" mt={1}>
    <FormHelperText>Kendinizi tanıtın</FormHelperText>
    <BioCharacterCounter value={formData.bio} maxLength={500} />
  </Flex>
</FormControl>
```

### Auto-Save Feature (ENHANCEMENT NEEDED)
```typescript
// Auto-save bio changes
import { useDebounce } from '../hooks/useDebounce';

const debouncedBio = useDebounce(formData.bio, 2000); // 2 second delay

useEffect(() => {
  if (debouncedBio && debouncedBio !== profile?.bio) {
    // Auto-save bio
    saveBioDraft(debouncedBio);
  }
}, [debouncedBio]);
```

## 📊 Performance Considerations

### Bio Loading Optimization
```typescript
// Lazy load bio content for public profiles
const BioDisplay: React.FC<{ userId: string; isPublic: boolean }> = ({ 
  userId, 
  isPublic 
}) => {
  const [bio, setBio] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    if (isPublic) {
      // Only load bio if it's set to public
      loadUserBio(userId).then(setBio).finally(() => setIsLoading(false));
    } else {
      setIsLoading(false);
    }
  }, [userId, isPublic]);
  
  if (!isPublic) return null;
  if (isLoading) return <LoadingState type="skeleton" height="60px" />;
  if (!bio) return null;
  
  return (
    <Text color={textColor} fontSize={{ base: 'sm', md: 'md' }} mt={2} maxW="500px">
      {bio}
    </Text>
  );
};
```

## 🔄 Database Operations

### Bio Update Query Optimization
```typescript
// Optimized bio update (only update if changed)
const updateBio = async (newBio: string) => {
  const sanitizedBio = sanitizeBio(newBio);
  
  // Only update if bio actually changed
  if (sanitizedBio === profile?.bio) return;
  
  const { error } = await supabase
    .from('profiles')
    .update({ 
      bio: sanitizedBio || null,
      updated_at: new Date().toISOString()
    })
    .eq('id', user?.id);
    
  if (error) throw error;
  
  // Update local state
  setProfile(prev => prev ? { ...prev, bio: sanitizedBio } : null);
};
```

## 📝 Implementation Status

### ✅ Already Implemented
- Bio textarea in Profile.tsx (lines 663-724) - ENHANCED
- Bio display in UserPage.tsx (lines 252-261)
- Bio privacy controls (display_bio_publicly)
- Database schema with bio field
- Basic validation and sanitization
- Responsive design
- Character limit (500 chars)

### ✅ Recently Enhanced (2025-01-30)
- **Real-time character counter** - IMPLEMENTED
  - Visual feedback with color coding (orange at 450+, red at 500)
  - Dynamic character count display
  - Border color changes based on character count
- **Auto-save functionality** - IMPLEMENTED
  - 2-second debounced auto-save
  - Visual status indicator (saving/saved)
  - Automatic sanitization before save
  - Local state synchronization
- **Enhanced input validation** - IMPLEMENTED
  - Real-time HTML tag removal
  - Character limit enforcement
  - Input sanitization following security guidelines

### 🔄 Future Enhancement Opportunities
- Enhanced touch interactions
- Bio formatting options (bold, italic)
- Bio templates or suggestions
- Emoji picker integration
- Markdown support for bio formatting

## Update Requirements

### When to Update This File
- Bio feature enhancements
- New privacy controls added
- Security improvements for text input
- UX improvements for bio editing
- Performance optimizations
- Database schema changes affecting profiles

### Related Files to Update
- Update `frontend/MAIN_APP.mdc` for new bio components
- Update `security/GUIDELINES.mdc` for bio security rules
- Update `frontend/UX_OPTIMIZATION.mdc` for bio UX improvements
- Update `backend/SUPABASE_EDGE.mdc` for bio-related database operations
