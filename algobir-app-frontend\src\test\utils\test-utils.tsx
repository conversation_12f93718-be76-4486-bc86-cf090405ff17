/**
 * Test Utilities for React Components
 * Phase 3.1: Automated Testing Pipeline Setup
 * Comprehensive testing utilities for component testing
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { Chakra<PERSON>rovider } from '@chakra-ui/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';
import theme from '../../theme/theme';
import { AuthProvider } from '../../context/AuthContext';
import { createMockSupabaseClient, createMockUser, createMockSession } from '../setup';

// Mock Supabase client for testing
const mockSupabaseClient = createMockSupabaseClient();

// Mock AuthContext
const MockAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const mockAuthValue = {
    user: createMockUser(),
    session: createMockSession(),
    loading: false,
    signIn: vi.fn().mockResolvedValue({ data: { user: createMockUser(), session: createMockSession() }, error: null }),
    signOut: vi.fn().mockResolvedValue({ error: null }),
    signUp: vi.fn().mockResolvedValue({ data: { user: createMockUser(), session: createMockSession() }, error: null }),
    resetPassword: vi.fn().mockResolvedValue({ error: null }),
    updateProfile: vi.fn().mockResolvedValue({ error: null }),
    supabase: mockSupabaseClient
  };

  return (
    <AuthProvider value={mockAuthValue}>
      {children}
    </AuthProvider>
  );
};

// Custom render function with all providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  // Router options
  initialEntries?: string[];
  
  // Auth options
  user?: any;
  session?: any;
  loading?: boolean;
  
  // Query client options
  queryClient?: QueryClient;
  
  // Theme options
  theme?: any;
  
  // Skip specific providers
  skipAuth?: boolean;
  skipRouter?: boolean;
  skipQuery?: boolean;
  skipChakra?: boolean;
}

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      gcTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
});

export const renderWithProviders = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult => {
  const {
    initialEntries = ['/'],
    user = createMockUser(),
    session = createMockSession(),
    loading = false,
    queryClient = createTestQueryClient(),
    theme: customTheme = theme,
    skipAuth = false,
    skipRouter = false,
    skipQuery = false,
    skipChakra = false,
    ...renderOptions
  } = options;

  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    let component = children;

    // Wrap with React Query Provider
    if (!skipQuery) {
      component = (
        <QueryClientProvider client={queryClient}>
          {component}
        </QueryClientProvider>
      );
    }

    // Wrap with Auth Provider
    if (!skipAuth) {
      const mockAuthValue = {
        user,
        session,
        loading,
        signIn: vi.fn().mockResolvedValue({ data: { user, session }, error: null }),
        signOut: vi.fn().mockResolvedValue({ error: null }),
        signUp: vi.fn().mockResolvedValue({ data: { user, session }, error: null }),
        resetPassword: vi.fn().mockResolvedValue({ error: null }),
        updateProfile: vi.fn().mockResolvedValue({ error: null }),
        supabase: mockSupabaseClient
      };

      component = (
        <AuthProvider value={mockAuthValue}>
          {component}
        </AuthProvider>
      );
    }

    // Wrap with Router
    if (!skipRouter) {
      component = (
        <BrowserRouter>
          {component}
        </BrowserRouter>
      );
    }

    // Wrap with Chakra UI Provider
    if (!skipChakra) {
      component = (
        <ChakraProvider theme={customTheme}>
          {component}
        </ChakraProvider>
      );
    }

    return <>{component}</>;
  };

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Utility for testing hooks
export const createHookWrapper = (options: CustomRenderOptions = {}) => {
  const {
    queryClient = createTestQueryClient(),
    skipAuth = false,
    skipRouter = false,
    skipQuery = false,
    skipChakra = false,
  } = options;

  return ({ children }: { children: React.ReactNode }) => {
    let component = children;

    if (!skipQuery) {
      component = (
        <QueryClientProvider client={queryClient}>
          {component}
        </QueryClientProvider>
      );
    }

    if (!skipAuth) {
      component = (
        <MockAuthProvider>
          {component}
        </MockAuthProvider>
      );
    }

    if (!skipRouter) {
      component = (
        <BrowserRouter>
          {component}
        </BrowserRouter>
      );
    }

    if (!skipChakra) {
      component = (
        <ChakraProvider theme={theme}>
          {component}
        </ChakraProvider>
      );
    }

    return <>{component}</>;
  };
};

// Mock data generators for testing
export const createMockTrade = (overrides = {}) => ({
  id: 'test-trade-id',
  user_id: 'test-user-id',
  robot_id: 'test-robot-id',
  symbol: 'BTCUSDT',
  order_side: 'BUY',
  quantity: 0.001,
  price: 45000.00,
  status: 'completed',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

export const createMockRobot = (overrides = {}) => ({
  id: 'test-robot-id',
  name: 'Test Robot',
  description: 'A test trading robot',
  seller_id: 'test-user-id',
  is_active: true,
  performance_metrics: {
    total_trades: 100,
    winning_trades: 75,
    win_rate: 75.0,
    total_profit: 1250.50
  },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

export const createMockNotification = (overrides = {}) => ({
  id: 'test-notification-id',
  user_id: 'test-user-id',
  title: 'Test Notification',
  message: 'This is a test notification',
  type: 'info',
  is_read: false,
  metadata: {},
  created_at: new Date().toISOString(),
  ...overrides
});

// Event simulation utilities
export const simulateKeyPress = (element: Element, key: string, options = {}) => {
  const keyboardEvent = new KeyboardEvent('keydown', {
    key,
    code: key,
    bubbles: true,
    ...options
  });
  element.dispatchEvent(keyboardEvent);
};

export const simulateMouseEvent = (element: Element, eventType: string, options = {}) => {
  const mouseEvent = new MouseEvent(eventType, {
    bubbles: true,
    cancelable: true,
    ...options
  });
  element.dispatchEvent(mouseEvent);
};

// Accessibility testing utilities
export const getByRole = (container: HTMLElement, role: string, options = {}) => {
  return container.querySelector(`[role="${role}"]`) as HTMLElement;
};

export const getByAriaLabel = (container: HTMLElement, label: string) => {
  return container.querySelector(`[aria-label="${label}"]`) as HTMLElement;
};

export const hasAriaAttribute = (element: Element, attribute: string, value?: string) => {
  const attrValue = element.getAttribute(attribute);
  return value ? attrValue === value : attrValue !== null;
};

// Performance testing utilities
export const measureRenderTime = async (renderFn: () => void) => {
  const start = performance.now();
  renderFn();
  const end = performance.now();
  return end - start;
};

// Error boundary testing utility
export const ErrorBoundaryTestComponent = ({ shouldThrow = false, children }: { shouldThrow?: boolean; children: React.ReactNode }) => {
  if (shouldThrow) {
    throw new Error('Test error for error boundary');
  }
  return <>{children}</>;
};

// Custom matchers for better assertions
export const customMatchers = {
  toBeAccessible: (element: HTMLElement) => {
    const hasRole = element.hasAttribute('role');
    const hasAriaLabel = element.hasAttribute('aria-label') || element.hasAttribute('aria-labelledby');
    const hasTabIndex = element.hasAttribute('tabindex');
    
    return {
      pass: hasRole || hasAriaLabel || hasTabIndex,
      message: () => `Expected element to have accessibility attributes (role, aria-label, or tabindex)`
    };
  },
  
  toHaveValidContrast: (element: HTMLElement) => {
    // This is a simplified contrast check - in real tests you'd use a proper contrast checker
    const styles = window.getComputedStyle(element);
    const backgroundColor = styles.backgroundColor;
    const color = styles.color;
    
    return {
      pass: backgroundColor !== color, // Simplified check
      message: () => `Expected element to have sufficient color contrast`
    };
  }
};

// Export everything for easy importing
export * from '@testing-library/react';
export { vi } from 'vitest';
export { default as userEvent } from '@testing-library/user-event';

// Export mock utilities
export { createMockSupabaseClient, createMockUser, createMockSession };
