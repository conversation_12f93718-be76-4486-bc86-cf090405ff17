import React, { useMemo } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  HStack,
  VStack,
  Badge,
  Icon,
  useColorModeValue,
  Tooltip,
  Button,
  ButtonGroup
} from '@chakra-ui/react';
import {
  AreaChart,
  Area,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  ReferenceLine,
  Brush,

} from 'recharts';
import {
  FiTrendingUp,
  FiTrendingDown,
  FiActivity,
  FiZoomIn
} from 'react-icons/fi';

interface CumulativePnLChartProps {
  data: Array<{
    month: string;
    pnl: number;
    trades: number;
    roi: number;
  }>;
  chartType?: 'area' | 'line';
  showBrush?: boolean;
  showReferenceLine?: boolean;
  height?: string;
  title?: string;
}

const CumulativePnLChart: React.FC<CumulativePnLChartProps> = ({
  data,
  chartType = 'area',
  showBrush = true,
  showReferenceLine = true,
  height = '400px',
  title = '<PERSON>ü<PERSON>ü<PERSON>if <PERSON>/<PERSON>'
}) => {
  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');
  const gridColor = useColorModeValue('#f0f0f0', '#2d3748');
  
  // Chart colors
  const primaryColor = useColorModeValue('#3182CE', '#63B3ED');
  const successColor = useColorModeValue('#38A169', '#68D391');
  const dangerColor = useColorModeValue('#E53E3E', '#FC8181');

  // Enhanced formatting functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(value / 100);
  };

  // Calculate cumulative data
  const cumulativeData = useMemo(() => {
    let cumulativePnL = 0;
    let cumulativeTrades = 0;
    
    return data.map((item, index) => {
      cumulativePnL += item.pnl;
      cumulativeTrades += item.trades;
      
      return {
        ...item,
        cumulativePnL,
        cumulativeTrades,
        period: index + 1,
        monthLabel: item.month,
        isProfit: item.pnl > 0,
        profitMargin: cumulativePnL > 0 ? ((item.pnl / cumulativePnL) * 100) : 0
      };
    });
  }, [data]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalPnL = cumulativeData[cumulativeData.length - 1]?.cumulativePnL || 0;
    const totalTrades = cumulativeData[cumulativeData.length - 1]?.cumulativeTrades || 0;
    const profitableMonths = cumulativeData.filter(d => d.pnl > 0).length;
    const lossMonths = cumulativeData.filter(d => d.pnl < 0).length;
    const winRate = totalTrades > 0 ? (profitableMonths / cumulativeData.length) * 100 : 0;
    
    // Calculate maximum drawdown
    let peak = 0;
    let maxDrawdown = 0;
    cumulativeData.forEach(point => {
      if (point.cumulativePnL > peak) {
        peak = point.cumulativePnL;
      }
      const drawdown = peak - point.cumulativePnL;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    });

    return {
      totalPnL,
      totalTrades,
      profitableMonths,
      lossMonths,
      winRate,
      maxDrawdown,
      averageMonthlyReturn: totalPnL / Math.max(1, cumulativeData.length)
    };
  }, [cumulativeData]);

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          bg={cardBg}
          p={4}
          borderRadius="lg"
          shadow="lg"
          border="1px"
          borderColor={borderColor}
        >
          <VStack spacing={2} align="start">
            <Text fontWeight="bold" color={textColor}>
              {label}
            </Text>
            <HStack spacing={4}>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">Aylık P&L</Text>
                <Text 
                  fontWeight="bold" 
                  color={data.pnl > 0 ? 'green.500' : 'red.500'}
                >
                  {formatCurrency(data.pnl)}
                </Text>
              </VStack>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">Kümülatif P&L</Text>
                <Text 
                  fontWeight="bold" 
                  color={data.cumulativePnL > 0 ? 'green.500' : 'red.500'}
                >
                  {formatCurrency(data.cumulativePnL)}
                </Text>
              </VStack>
            </HStack>
            <HStack spacing={4}>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">İşlem Sayısı</Text>
                <Text fontWeight="bold" color={primaryColor}>
                  {data.trades}
                </Text>
              </VStack>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">ROI</Text>
                <Text 
                  fontWeight="bold" 
                  color={data.roi > 0 ? 'green.500' : 'red.500'}
                >
                  {formatPercent(data.roi)}
                </Text>
              </VStack>
            </HStack>
          </VStack>
        </Box>
      );
    }
    return null;
  };

  return (
    <Card bg={cardBg} borderRadius="xl" shadow="sm">
      <CardHeader pb={2}>
        <HStack justify="space-between" flexWrap="wrap">
          <VStack align="start" spacing={1}>
            <Heading size="md" color={textColor}>
              {title}
            </Heading>
            <HStack spacing={4} flexWrap="wrap">
              <Badge 
                colorScheme={stats.totalPnL > 0 ? 'green' : 'red'} 
                variant="subtle"
                px={3}
                py={1}
              >
                <Icon as={stats.totalPnL > 0 ? FiTrendingUp : FiTrendingDown} mr={1} />
                {formatCurrency(stats.totalPnL)} Toplam
              </Badge>
              <Badge colorScheme="blue" variant="subtle" px={3} py={1}>
                <Icon as={FiActivity} mr={1} />
                {stats.totalTrades} İşlem
              </Badge>
              <Badge 
                colorScheme={stats.winRate > 50 ? 'green' : 'orange'} 
                variant="subtle"
                px={3}
                py={1}
              >
                {stats.winRate.toFixed(1)}% Kazanma
              </Badge>
            </HStack>
          </VStack>

          <ButtonGroup size="sm" isAttached variant="outline">
            <Tooltip label="Yakınlaştır">
              <Button>
                <Icon as={FiZoomIn} />
              </Button>
            </Tooltip>
          </ButtonGroup>
        </HStack>
      </CardHeader>

      <CardBody pt={0}>
        <Box h={height}>
          <ResponsiveContainer width="100%" height="100%">
            {chartType === 'area' ? (
              <AreaChart data={cumulativeData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                <defs>
                  <linearGradient id="cumulativePnLGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop 
                      offset="5%" 
                      stopColor={stats.totalPnL > 0 ? successColor : dangerColor} 
                      stopOpacity={0.8}
                    />
                    <stop 
                      offset="95%" 
                      stopColor={stats.totalPnL > 0 ? successColor : dangerColor} 
                      stopOpacity={0.1}
                    />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
                <XAxis 
                  dataKey="monthLabel" 
                  tick={{ fontSize: 12, fill: textColor }}
                  axisLine={{ stroke: borderColor }}
                />
                <YAxis 
                  tick={{ fontSize: 12, fill: textColor }}
                  axisLine={{ stroke: borderColor }}
                  tickFormatter={formatCurrency}
                />
                <RechartsTooltip content={<CustomTooltip />} />
                
                {showReferenceLine && (
                  <ReferenceLine 
                    y={0} 
                    stroke={borderColor} 
                    strokeDasharray="5 5"
                    label={{ value: "Başabaş", position: "insideTopRight" }}
                  />
                )}
                
                <Area
                  type="monotone"
                  dataKey="cumulativePnL"
                  stroke={stats.totalPnL > 0 ? successColor : dangerColor}
                  fillOpacity={1}
                  fill="url(#cumulativePnLGradient)"
                  strokeWidth={2}
                />
                
                {showBrush && (
                  <Brush 
                    dataKey="monthLabel" 
                    height={30} 
                    stroke={primaryColor}
                    fill={`${primaryColor}20`}
                  />
                )}
              </AreaChart>
            ) : (
              <LineChart data={cumulativeData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
                <XAxis 
                  dataKey="monthLabel" 
                  tick={{ fontSize: 12, fill: textColor }}
                  axisLine={{ stroke: borderColor }}
                />
                <YAxis 
                  tick={{ fontSize: 12, fill: textColor }}
                  axisLine={{ stroke: borderColor }}
                  tickFormatter={formatCurrency}
                />
                <RechartsTooltip content={<CustomTooltip />} />
                
                {showReferenceLine && (
                  <ReferenceLine 
                    y={0} 
                    stroke={borderColor} 
                    strokeDasharray="5 5"
                    label={{ value: "Başabaş", position: "insideTopRight" }}
                  />
                )}
                
                <Line
                  type="monotone"
                  dataKey="cumulativePnL"
                  stroke={stats.totalPnL > 0 ? successColor : dangerColor}
                  strokeWidth={3}
                  dot={{ fill: stats.totalPnL > 0 ? successColor : dangerColor, strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: stats.totalPnL > 0 ? successColor : dangerColor, strokeWidth: 2 }}
                />
                
                {showBrush && (
                  <Brush 
                    dataKey="monthLabel" 
                    height={30} 
                    stroke={primaryColor}
                    fill={`${primaryColor}20`}
                  />
                )}
              </LineChart>
            )}
          </ResponsiveContainer>
        </Box>

        {/* Statistics Summary */}
        <HStack justify="space-around" mt={4} pt={4} borderTop="1px" borderColor={borderColor}>
          <VStack spacing={1}>
            <Text fontSize="sm" color="gray.500">Ortalama Aylık</Text>
            <Text 
              fontWeight="bold" 
              color={stats.averageMonthlyReturn > 0 ? 'green.500' : 'red.500'}
            >
              {formatCurrency(stats.averageMonthlyReturn)}
            </Text>
          </VStack>
          <VStack spacing={1}>
            <Text fontSize="sm" color="gray.500">Max Düşüş</Text>
            <Text fontWeight="bold" color="red.500">
              {formatCurrency(stats.maxDrawdown)}
            </Text>
          </VStack>
          <VStack spacing={1}>
            <Text fontSize="sm" color="gray.500">Karlı Aylar</Text>
            <Text fontWeight="bold" color="green.500">
              {stats.profitableMonths}/{cumulativeData.length}
            </Text>
          </VStack>
        </HStack>
      </CardBody>
    </Card>
  );
};

export default CumulativePnLChart;
