$url = "https://fllklckmycxcgwhboiji.supabase.co/functions/v1/seller-signal-endpoint/e0f65ea5-d690-49e6-b553-c2b65c5f6919"
$body = @{
    signal_name = "TEST ALIM"
    symbol = "BTCUSDT"
    price = 50000
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri $url -Method POST -Body $body -ContentType "application/json" -ErrorAction Stop
    Write-Host "Response:"
    Write-Host ($response | ConvertTo-Json -Depth 10)
} catch {
    Write-Host "Error: $($_.Exception.Message)"
    if ($_.ErrorDetails) {
        Write-Host "Details: $($_.ErrorDetails.Message)"
    }
} 