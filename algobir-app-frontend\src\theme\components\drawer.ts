import { mode } from '@chakra-ui/theme-tools';

export const DrawerComponent = {
  components: {
    Drawer: {
      baseStyle: (props: any) => ({
        dialog: {
          bg: mode('white', 'navy.800')(props),
          borderRadius: '20px',
          border: '1px solid',
          borderColor: mode('transparent', 'rgba(255, 255, 255, 0.15)')(props),
          boxShadow: mode(
            '14px 17px 40px 4px rgba(112, 144, 176, 0.18)',
            'unset'
          )(props),
        },
        header: {
          color: mode('secondaryGray.900', 'white')(props),
          fontWeight: '700',
          fontSize: 'xl',
          borderBottom: '1px solid',
          borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          pb: '16px',
        },
        body: {
          color: mode('secondaryGray.900', 'white')(props),
          pt: '20px',
        },
        footer: {
          borderTop: '1px solid',
          borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          pt: '16px',
        },
      }),
    },
  },
}; 