import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.8'
import { getCorsHeaders } from '../_shared/cors.ts'
import { submitOrderWithRetry } from '../_shared/http-client.ts'

interface TradePayload {
  trade_id: number;
}

serve(async (req) => {
  const corsHeaders = getCorsHeaders(req.headers);

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { trade_id }: TradePayload = await req.json();
    if (!trade_id) {
      throw new Error('trade_id is required.');
    }

    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { data: tradeData, error: tradeError } = await supabaseAdmin
      .from('trades')
      .select('*, user_id')
      .eq('id', trade_id)
      .single();

    if (tradeError || !tradeData) {
      throw new Error(`Trade with ID ${trade_id} not found. Error: ${tradeError?.message}`);
    }

    const userId = tradeData.user_id;

    const { data: userSettings, error: userSettingsError } = await supabaseAdmin
      .from('user_settings')
      .select('custom_webhook_url')
      .eq('id', userId)
      .single();

    if (userSettingsError || !userSettings || !userSettings.custom_webhook_url) {
      throw new Error(`User settings or custom_webhook_url not found for user ${userId}. Error: ${userSettingsError?.message}`);
    }

    // Get API keys from user_settings table
    const { data: apiKeyData, error: apiKeyError } = await supabaseAdmin
      .from('user_settings')
      .select('encrypted_api_key, encrypted_token')
      .eq('id', userId)
      .single();

    if (apiKeyError || !apiKeyData?.encrypted_api_key || !apiKeyData?.encrypted_token) {
      throw new Error(`API keys not found in user_settings for user ${userId}. Error: ${apiKeyError?.message}`);
    }

    const apiKey = apiKeyData.encrypted_api_key;
    const token = apiKeyData.encrypted_token;
    
    const orderPayload = {
      name: tradeData.name,
      symbol: tradeData.symbol,
      orderSide: tradeData.order_side,
      orderType: "mktbest",
      price: String(tradeData.price),
      quantity: String(tradeData.calculated_quantity),
      timeInForce: "day",
      apiKey: apiKey,
      timenow: tradeData.created_at,
      token: token
    };

    // OPTIMIZATION: Use optimized HTTP client with connection pooling and retries
    try {
      const response = await submitOrderWithRetry(userSettings.custom_webhook_url, orderPayload, {
        timeout: 15000, // 15 second timeout for order submission
        retries: 2, // Retry twice for failed orders
        keepAlive: true
      });

      console.log(`Successfully forwarded order for trade ${trade_id} to user ${userId}'s webhook.`);
    } catch (error) {
      // Enhanced error handling for HTTP client errors
      let errorMessage = `Failed to send order to brokerage for user ${userId}`;

      if (error.message.includes('timeout')) {
        errorMessage = `Order submission timeout for user ${userId}`;
      } else if (error.message.includes('429')) {
        errorMessage = `Rate limit exceeded for user ${userId}'s webhook URL. This is likely due to webhook.site limits or similar service restrictions.`;
      } else if (error.message.includes('503')) {
        errorMessage = `Webhook service temporarily unavailable for user ${userId}.`;
      } else if (error.message.includes('502')) {
        errorMessage = `Webhook service connection error for user ${userId}.`;
      } else if (error.message.includes('HTTP 5')) {
        errorMessage = `Webhook service internal error for user ${userId}.`;
      } else if (error.message.includes('HTTP 4')) {
        errorMessage = `Webhook service rejected the request for user ${userId}.`;
      } else {
        errorMessage = `${errorMessage}: ${error.message}`;
      }

      console.error(`[ORDER-SUBMISSION] ${errorMessage}`);
      throw new Error(errorMessage);
    }

    return new Response(JSON.stringify({ success: true, message: "Order forwarded successfully." }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error(error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
}) 