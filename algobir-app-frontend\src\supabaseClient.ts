// src/supabaseClient.ts
import { createClient } from '@supabase/supabase-js'

// Bu değerleri kendi Supabase projenizin public/anon anahtarı ile değiştirin
// DİKKAT: Bu 'anon' anahtar o<PERSON>dı<PERSON>, asla 'service_role' anahtarı kullanmayın!
const supabaseUrl = 'https://fllklckmycxcgwhboiji.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZsbGtsY2tteWN4Y2d3aGJvaWppIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUwNTU4NjcsImV4cCI6MjA2MDYzMTg2N30.wmJx9E5tc9xHUIbya2ONUP-XsuFxRHQtSbnSsLH_2rM';

// Supabase client oluştur
// Bu client-side kullanım için public/anon anahtarı ile oluşturulmuştur
// Tüm güvenlik kontrolleri Supabase RLS (Row Level Security) politikaları tarafından sağlanır
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,  // Oturumu hatırla
    autoRefreshToken: true, // Token'ları otomatik yenile
    detectSessionInUrl: true, // URL'deki oturum bilgisini algıla
  },
  global: {
    headers: {
      'x-application-name': 'algobir-app-frontend',
    },
  },
});