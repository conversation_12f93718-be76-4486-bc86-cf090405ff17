import { test, expect } from '@playwright/test';

test.describe('Admin Order Transmission Monitoring', () => {
  test.beforeEach(async ({ page }) => {
    // Mock the Supabase client to avoid real API calls
    await page.addInitScript(() => {
      // Mock Supabase
      window.mockSupabase = {
        auth: {
          getUser: () => Promise.resolve({
            data: {
              user: {
                id: 'test-admin-user',
                email: '<EMAIL>'
              }
            },
            error: null
          })
        },
        from: () => ({
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({
                data: { is_superuser: true },
                error: null
              })
            })
          })
        }),
        rpc: (functionName: string) => {
          if (functionName === 'get_order_transmission_metrics') {
            return Promise.resolve({
              data: [
                {
                  id: '1',
                  trade_id: 123,
                  signal_type: 'BUY',
                  symbol: 'BTCUSDT',
                  order_side: 'BUY',
                  json_parsing_time_ms: 5.2,
                  transformation_time_ms: 15.8,
                  webhook_delivery_time_ms: 45.3,
                  total_processing_time_ms: 66.3,
                  signal_source: 'solo-robot',
                  processing_status: 'success',
                  created_at: new Date().toISOString(),
                  signal_received_at: new Date().toISOString(),
                  processing_completed_at: new Date().toISOString()
                }
              ],
              error: null
            });
          }
          if (functionName === 'get_order_transmission_stats') {
            return Promise.resolve({
              data: [{
                total_signals: 100,
                avg_json_parsing_time_ms: 5.5,
                avg_transformation_time_ms: 18.2,
                avg_webhook_delivery_time_ms: 42.1,
                avg_total_processing_time_ms: 65.8,
                p95_total_processing_time_ms: 120.5,
                p99_total_processing_time_ms: 180.2,
                success_rate: 98.5,
                signals_per_minute: 2.3,
                fastest_processing_time_ms: 25.1,
                slowest_processing_time_ms: 250.8,
                solo_robot_count: 60,
                bro_robot_count: 40
              }],
              error: null
            });
          }
          return Promise.resolve({ data: [], error: null });
        },
        channel: () => ({
          on: () => ({ subscribe: () => Promise.resolve() }),
          subscribe: () => Promise.resolve()
        }),
        removeChannel: () => {}
      };
    });

    // Navigate to admin status page
    await page.goto('/admin/status');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display order transmission monitoring section', async ({ page }) => {
    // Check if the monitoring section is visible
    await expect(page.locator('text=Emir İletim Hızı Monitörü')).toBeVisible();
    
    // Check if the description is present
    await expect(page.locator('text=TradingView sinyallerinden webhook iletimi performansı')).toBeVisible();
  });

  test('should show monitoring tabs', async ({ page }) => {
    // Check if tabs are present
    await expect(page.locator('text=Canlı Grafik')).toBeVisible();
    await expect(page.locator('text=Performans Metrikleri')).toBeVisible();
  });

  test('should display live chart by default', async ({ page }) => {
    // The first tab should be active by default
    const chartTab = page.locator('text=Canlı Grafik');
    await expect(chartTab).toBeVisible();
    
    // Check if chart container is present
    await expect(page.locator('[data-testid="order-transmission-chart"]').or(page.locator('.recharts-wrapper'))).toBeVisible();
  });

  test('should switch to performance metrics tab', async ({ page }) => {
    // Click on performance metrics tab
    await page.locator('text=Performans Metrikleri').click();
    
    // Wait for content to load
    await page.waitForTimeout(1000);
    
    // Check if performance metrics are displayed
    await expect(page.locator('text=Toplam Sinyal')).toBeVisible();
    await expect(page.locator('text=Başarı Oranı')).toBeVisible();
    await expect(page.locator('text=Ortalama Hız')).toBeVisible();
  });

  test('should display performance statistics', async ({ page }) => {
    // Switch to performance metrics tab
    await page.locator('text=Performans Metrikleri').click();
    await page.waitForTimeout(1000);
    
    // Check for key performance indicators
    await expect(page.locator('text=100')).toBeVisible(); // Total signals
    await expect(page.locator('text=%98.5')).toBeVisible(); // Success rate
    await expect(page.locator('text=65.8ms')).toBeVisible(); // Average processing time
  });

  test('should show processing time breakdown', async ({ page }) => {
    // Switch to performance metrics tab
    await page.locator('text=Performans Metrikleri').click();
    await page.waitForTimeout(1000);
    
    // Check for processing breakdown components
    await expect(page.locator('text=JSON Ayrıştırma')).toBeVisible();
    await expect(page.locator('text=Veri Dönüştürme')).toBeVisible();
    await expect(page.locator('text=Webhook İletimi')).toBeVisible();
  });

  test('should display performance percentiles', async ({ page }) => {
    // Switch to performance metrics tab
    await page.locator('text=Performans Metrikleri').click();
    await page.waitForTimeout(1000);
    
    // Check for percentile information
    await expect(page.locator('text=95. Yüzdelik')).toBeVisible();
    await expect(page.locator('text=99. Yüzdelik')).toBeVisible();
    await expect(page.locator('text=En Hızlı')).toBeVisible();
    await expect(page.locator('text=En Yavaş')).toBeVisible();
  });

  test('should show connection status indicator', async ({ page }) => {
    // Check for connection status elements
    await expect(page.locator('text=Canlı Bağlantı').or(page.locator('text=Bağlantı Kesildi'))).toBeVisible();
  });

  test('should display chart controls when available', async ({ page }) => {
    // Check if chart type controls are present
    const chartControls = page.locator('text=Çizgi').or(page.locator('text=Alan')).or(page.locator('text=Çubuk'));
    
    // At least one chart type should be visible
    await expect(chartControls.first()).toBeVisible();
  });

  test('should show time range selector', async ({ page }) => {
    // Look for time range options
    const timeRangeSelector = page.locator('select').filter({ hasText: 'Son' });
    
    if (await timeRangeSelector.count() > 0) {
      await expect(timeRangeSelector.first()).toBeVisible();
    }
  });

  test('should integrate with existing admin status page layout', async ({ page }) => {
    // Check if other admin status elements are still present
    await expect(page.locator('text=Sistem Durumu').or(page.locator('text=System Status'))).toBeVisible();
    
    // Check if the monitoring section is properly positioned
    const monitoringSection = page.locator('text=Emir İletim Hızı Monitörü').locator('..');
    await expect(monitoringSection).toBeVisible();
  });

  test('should handle refresh functionality', async ({ page }) => {
    // Look for refresh button
    const refreshButton = page.locator('button:has-text("Yenile")').or(page.locator('button:has-text("Refresh")'));
    
    if (await refreshButton.count() > 0) {
      await refreshButton.first().click();
      
      // Wait for potential loading state
      await page.waitForTimeout(500);
      
      // Content should still be visible after refresh
      await expect(page.locator('text=Emir İletim Hızı Monitörü')).toBeVisible();
    }
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check if monitoring section is still visible and accessible
    await expect(page.locator('text=Emir İletim Hızı Monitörü')).toBeVisible();
    
    // Tabs should be accessible on mobile
    await expect(page.locator('text=Canlı Grafik')).toBeVisible();
    await expect(page.locator('text=Performans Metrikleri')).toBeVisible();
  });

  test('should maintain Turkish language conventions', async ({ page }) => {
    // Check for Turkish text elements
    await expect(page.locator('text=Emir İletim Hızı Monitörü')).toBeVisible();
    await expect(page.locator('text=TradingView sinyallerinden webhook iletimi performansı')).toBeVisible();
    await expect(page.locator('text=Canlı Grafik')).toBeVisible();
    await expect(page.locator('text=Performans Metrikleri')).toBeVisible();
    
    // Check for Turkish performance metrics labels
    if (await page.locator('text=Performans Metrikleri').isVisible()) {
      await page.locator('text=Performans Metrikleri').click();
      await page.waitForTimeout(1000);
      
      await expect(page.locator('text=Toplam Sinyal')).toBeVisible();
      await expect(page.locator('text=Başarı Oranı')).toBeVisible();
      await expect(page.locator('text=Ortalama Hız')).toBeVisible();
    }
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Mock error response
    await page.addInitScript(() => {
      window.mockSupabase.rpc = () => Promise.resolve({
        data: null,
        error: { message: 'Database connection failed' }
      });
    });
    
    // Reload page to trigger error
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Should show error message or fallback content
    const errorIndicators = page.locator('text=hata').or(page.locator('text=error')).or(page.locator('text=yüklenemedi'));
    
    // At least some error indication should be present
    if (await errorIndicators.count() > 0) {
      await expect(errorIndicators.first()).toBeVisible();
    }
  });

  test('should show loading states appropriately', async ({ page }) => {
    // Look for loading indicators
    const loadingIndicators = page.locator('text=yükleniyor').or(page.locator('text=loading')).or(page.locator('[data-testid="spinner"]'));
    
    // Loading states might be brief, so we check if they exist
    if (await loadingIndicators.count() > 0) {
      // Loading indicator should eventually disappear
      await expect(loadingIndicators.first()).toBeHidden({ timeout: 10000 });
    }
  });
});
