import { test, expect } from '@playwright/test';
import { 
  loginAsAdmin, 
  goToAdminNotifications,
  createAdminAnnouncement,
  waitForPageLoad,
  expectToast
} from './utils/test-helpers';

test.describe('Admin Bildirim Paneli', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsAdmin(page);
    await waitForPageLoad(page);
  });

  test('Admin bildirim paneli yükleniyor', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // Sayfa başlığını kontrol et
    await expect(page.locator('h1')).toContainText('Bildirim Yönetimi');
    
    // İstatistik kartlarının görünür olduğunu kontrol et
    await expect(page.locator('text=Toplam Gönderilen')).toBeVisible();
    await expect(page.locator('text=Solo Kullanıcılar')).toBeVisible();
    await expect(page.locator('text=Bro Satıcılar')).toBeVisible();
    await expect(page.locator('text=Bro Aboneler')).toBeVisible();
  });

  test('İstatistik kartları doğru değerleri gösteriyor', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // İstatistik değerlerinin sayısal olduğunu kontrol et
    const stats = page.locator('[data-testid="stat-value"]');
    const count = await stats.count();
    
    for (let i = 0; i < count; i++) {
      const statValue = await stats.nth(i).textContent();
      expect(statValue).toMatch(/^\d+$/); // Sadece sayı olmalı
    }
  });

  test('Hızlı duyuru linkleri çalışıyor', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // Hızlı duyuru linklerini test et
    const quickLinks = [
      'Tüm Kullanıcılara',
      'Solo Kullanıcılara', 
      'Bro Satıcılara',
      'Bro Abonelere'
    ];
    
    for (const linkText of quickLinks) {
      const link = page.locator(`a:has-text("${linkText}")`);
      await expect(link).toBeVisible();
      
      // Link'in doğru URL'ye gittiğini kontrol et
      const href = await link.getAttribute('href');
      expect(href).toContain('/admin/notifications/create');
    }
  });

  test('Son gönderilen bildirimler tablosu görüntüleniyor', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // Tablo başlığını kontrol et
    await expect(page.locator('text=Son Gönderilen Bildirimler')).toBeVisible();
    
    // Tablo header'larını kontrol et
    await expect(page.locator('text=Bildirim')).toBeVisible();
    await expect(page.locator('text=Hedef Kitle')).toBeVisible();
    await expect(page.locator('text=Alıcı Sayısı')).toBeVisible();
    await expect(page.locator('text=Tarih')).toBeVisible();
    await expect(page.locator('text=Gönderen')).toBeVisible();
  });

  test('Duyuru oluştur sayfası açılıyor', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // Duyuru oluştur butonuna tıkla
    const createButton = page.locator('a:has-text("Duyuru Oluştur")');
    await expect(createButton).toBeVisible();
    await createButton.click();
    
    // Duyuru oluşturma sayfasına yönlendirildiğini kontrol et
    await expect(page).toHaveURL('/admin/notifications/create');
    await expect(page.locator('h2')).toContainText('Duyuru Oluştur');
  });

  test('Yenile butonu çalışıyor', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // Yenile butonunu bul ve tıkla
    const refreshButton = page.locator('button:has-text("Yenile")');
    await expect(refreshButton).toBeVisible();
    await refreshButton.click();
    
    // Sayfanın yeniden yüklendiğini kontrol et
    await waitForPageLoad(page);
    await expect(page.locator('h1')).toContainText('Bildirim Yönetimi');
  });

  test('Bildirim geçmişi filtreleme', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // Filtre alanları varsa test et
    const filterInput = page.locator('input[placeholder*="filtre"], input[placeholder*="ara"]');
    if (await filterInput.isVisible()) {
      await filterInput.fill('test');
      await waitForPageLoad(page);
      
      // Filtrelenmiş sonuçları kontrol et
      const tableRows = page.locator('tbody tr');
      const count = await tableRows.count();
      
      if (count > 0) {
        const firstRow = tableRows.first();
        const text = await firstRow.textContent();
        expect(text?.toLowerCase()).toContain('test');
      }
    }
  });

  test('Bildirim detayları modal açılıyor', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // İlk bildirim satırını bul
    const firstRow = page.locator('tbody tr').first();
    if (await firstRow.isVisible()) {
      // Detay butonunu bul ve tıkla
      const detailButton = firstRow.locator('button:has-text("Detay"), button[aria-label*="detay"]');
      if (await detailButton.isVisible()) {
        await detailButton.click();
        
        // Modal'ın açıldığını kontrol et
        const modal = page.locator('[role="dialog"], .chakra-modal');
        await expect(modal).toBeVisible();
      }
    }
  });

  test('Pagination çalışıyor', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // Pagination butonlarını kontrol et
    const nextButton = page.locator('button:has-text("Sonraki"), button[aria-label*="next"]');
    const prevButton = page.locator('button:has-text("Önceki"), button[aria-label*="prev"]');
    
    if (await nextButton.isVisible()) {
      await nextButton.click();
      await waitForPageLoad(page);
      
      // Sayfa değiştiğini kontrol et
      await expect(prevButton).toBeVisible();
    }
  });

  test('Bildirim silme işlevi', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // İlk bildirim satırını bul
    const firstRow = page.locator('tbody tr').first();
    if (await firstRow.isVisible()) {
      // Silme butonunu bul
      const deleteButton = firstRow.locator('button:has-text("Sil"), button[aria-label*="sil"]');
      if (await deleteButton.isVisible()) {
        await deleteButton.click();
        
        // Onay dialogunu bekle
        const confirmDialog = page.locator('[role="alertdialog"], .chakra-alert-dialog');
        if (await confirmDialog.isVisible()) {
          const confirmButton = confirmDialog.locator('button:has-text("Sil"), button:has-text("Onayla")');
          await confirmButton.click();
          
          // Toast mesajını kontrol et
          await expectToast(page, 'silindi', 'success');
        }
      }
    }
  });

  test('Toplu bildirim gönderme', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // Toplu işlem alanı varsa test et
    const bulkActionSelect = page.locator('select[name*="bulk"], select[aria-label*="toplu"]');
    if (await bulkActionSelect.isVisible()) {
      await bulkActionSelect.selectOption('send_notification');
      
      // Toplu gönderim butonunu bul
      const bulkSendButton = page.locator('button:has-text("Toplu Gönder")');
      if (await bulkSendButton.isVisible()) {
        await bulkSendButton.click();
        
        // Onay dialogunu kontrol et
        const confirmDialog = page.locator('[role="dialog"]');
        await expect(confirmDialog).toBeVisible();
      }
    }
  });

  test('Export/İndirme işlevi', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // Export butonunu bul
    const exportButton = page.locator('button:has-text("İndir"), button:has-text("Export")');
    if (await exportButton.isVisible()) {
      // Download event'ini dinle
      const downloadPromise = page.waitForEvent('download');
      await exportButton.click();
      
      // Download'ın başladığını kontrol et
      const download = await downloadPromise;
      expect(download.suggestedFilename()).toMatch(/\.(csv|xlsx|pdf)$/);
    }
  });

  test('Bildirim istatistikleri grafiği', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // Grafik alanını kontrol et
    const chartContainer = page.locator('[data-testid="notification-chart"], .recharts-wrapper');
    if (await chartContainer.isVisible()) {
      // Grafik elementlerinin yüklendiğini kontrol et
      await expect(chartContainer.locator('svg')).toBeVisible();
    }
  });

  test('Real-time bildirim güncellemeleri', async ({ page }) => {
    await goToAdminNotifications(page);
    
    // İlk bildirim sayısını al
    const initialCount = await page.locator('tbody tr').count();
    
    // Yeni bir duyuru oluştur
    await page.goto('/admin/notifications/create');
    await createAdminAnnouncement(page, 'Test Duyuru', 'Bu bir test duyurusudur.');
    
    // Admin paneline geri dön
    await goToAdminNotifications(page);
    
    // Yeni bildirim sayısını kontrol et
    const newCount = await page.locator('tbody tr').count();
    expect(newCount).toBeGreaterThanOrEqual(initialCount);
  });
});
