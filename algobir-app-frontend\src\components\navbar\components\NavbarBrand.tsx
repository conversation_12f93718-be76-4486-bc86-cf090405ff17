import React from 'react';
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Flex,
  Text,
  VStack,
  IconButton,
  Icon,
  useColorModeValue,
  useBreakpointValue,
} from '@chakra-ui/react';
import { FiMenu, FiSidebar } from 'react-icons/fi';
import { useSidebar } from '../../../context/SidebarContext';
import { SidebarResponsive } from '../../sidebar/Sidebar';

interface NavbarBrandProps {
  brandText: string;
}

/**
 * NavbarBrand - Handles brand display, breadcrumb, and sidebar controls
 * Separated from main navbar for better maintainability and single responsibility
 */
const NavbarBrand: React.FC<NavbarBrandProps> = ({ brandText }) => {
  const { toggleSidebarState, isCollapsed } = useSidebar();
  
  // Responsive values
  const showBreadcrumb = useBreakpointValue({ base: false, md: true });
  
  // Color values
  const mainText = useColorModeValue('text-primary', 'white');
  const breadcrumbColor = useColorModeValue('text-secondary', 'gray.400');
  const iconColor = useColorModeValue('gray.600', 'gray.300');
  const buttonHover = useColorModeValue('gray.100', 'gray.700');
  const buttonActive = useColorModeValue('gray.200', 'gray.600');

  return (
    <Flex
      alignItems="center"
      flex="1"
      minW="0" // Prevents overflow
      gap={{ base: '2', md: '3' }}
    >
      {/* Mobile Sidebar Toggle */}
      <Box flexShrink="0">
        <SidebarResponsive />
      </Box>

      {/* Desktop Sidebar Toggle - Enhanced responsive display */}
      <IconButton
        aria-label={isCollapsed ? "Sidebar'ı genişlet" : "Sidebar'ı daralt"}
        icon={<Icon as={isCollapsed ? FiMenu : FiSidebar} />}
        variant="ghost"
        size={{ base: 'sm', md: 'md' }}
        borderRadius="full"
        color={iconColor}
        display={{ base: 'none', lg: 'flex' }}
        onClick={toggleSidebarState}
        _hover={{ bg: buttonHover }}
        _active={{ bg: buttonActive }}
        _focus={{
          boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
          outline: 'none',
        }}
        title={isCollapsed ? "Sidebar'ı genişlet" : "Sidebar'ı daralt"}
      />
      
      <VStack 
        align="flex-start" 
        spacing="0"
        flex="1"
        minW="0" // Prevents text overflow
      >
        {/* Breadcrumb - sadece desktop'ta göster */}
        {showBreadcrumb && (
          <Breadcrumb 
            fontSize="sm"
            color={breadcrumbColor}
            aria-label="Breadcrumb navigation"
          >
            <BreadcrumbItem>
              <BreadcrumbLink 
                href="#" 
                color={breadcrumbColor}
                _hover={{ color: mainText }}
                transition="color 0.2s"
                _focus={{
                  outline: '2px solid',
                  outlineColor: 'brand.500',
                  outlineOffset: '2px',
                }}
              >
                Sayfalar
              </BreadcrumbLink>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink 
                href="#" 
                color={breadcrumbColor}
                _hover={{ color: mainText }}
                transition="color 0.2s"
                _focus={{
                  outline: '2px solid',
                  outlineColor: 'brand.500',
                  outlineOffset: '2px',
                }}
                aria-current="page"
              >
                {brandText}
              </BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
        )}
        
        {/* Başlık - Responsive font sizes */}
        <Text
          as="h1"
          color={mainText}
          fontWeight="bold"
          fontSize={{ 
            base: 'lg',    // 18px on mobile
            sm: 'xl',      // 20px on small screens
            md: '2xl',     // 24px on tablets
            lg: '3xl'      // 30px on desktop
          }}
          lineHeight="1.2"
          noOfLines={1}
          letterSpacing="-0.025em"
          title={brandText} // Tooltip for truncated text
        >
          {brandText}
        </Text>
      </VStack>
    </Flex>
  );
};

export default NavbarBrand;
