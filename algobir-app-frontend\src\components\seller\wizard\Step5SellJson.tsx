import React from 'react';
import {
  Box,
  VStack,
  Heading,
  Text,
  HStack,
  Icon,
  Code,
  Card,
  CardBody,
  CardHeader,
  Button,
  useClipboard,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';
import { FaMoneyBillWave, FaCopy, FaCheck } from 'react-icons/fa';
import { RobotFormData } from '../RobotSetupWizard';

export const Step5SellJson: React.FC = () => {
  const { getValues } = useFormContext<RobotFormData>();
  const robotName = getValues('name') || 'Robot';
  
  const sellJsonString = JSON.stringify({
    name: `${robotName} TP`,
    symbol: "{{ticker}}",
    orderSide: "sell",
    orderType: "mktbest",
    price: "{{close}}",
    quantity: "1",
    timeInForce: "day"
  }, null, 2);

  const { onCopy, hasCopied } = useClipboard(sellJsonString);

  return (
    <Box>
      <VStack spacing={6} align="stretch">
        <Box textAlign="center" mb={4}>
          <HStack justify="center" mb={3}>
            <Icon as={FaMoneyBillWave} boxSize={8} color="red.500" />
          </HStack>
          <Heading size="lg" mb={2}>Satım Sinyali JSON</Heading>
          <Text color="gray.600">
            TradingView alarmlarınızda kullanacağınız satım sinyali JSON formatı hazırlandı.
          </Text>
        </Box>

        <Alert status="warning" borderRadius="md">
          <AlertIcon />
          <AlertDescription>
            Bu JSON kodunu TradingView'da kar alma (Take Profit) alarmlarınızın "Mesaj" bölümüne kopyalayın. 
            Robot satım sinyali gönderdiğinde bu format kullanılacaktır.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <HStack justify="space-between">
              <Heading size="md">Satım Sinyali Formatı</Heading>
              <Button
                leftIcon={hasCopied ? <FaCheck /> : <FaCopy />}
                colorScheme={hasCopied ? "green" : "red"}
                variant="outline"
                onClick={onCopy}
                size="sm"
              >
                {hasCopied ? "Kopyalandı!" : "Kopyala"}
              </Button>
            </HStack>
          </CardHeader>
          <CardBody>
            <Box
              p={4}
              bg="red.50"
              borderRadius="md"
              border="1px solid"
              borderColor="red.200"
            >
              <Code 
                display="block" 
                whiteSpace="pre" 
                fontFamily="mono"
                fontSize="sm"
                bg="transparent"
                p={0}
              >
                {sellJsonString}
              </Code>
            </Box>
          </CardBody>
        </Card>

        <VStack spacing={3} align="stretch">
          <Heading size="sm">JSON Alanları Açıklaması:</Heading>
          <Box p={4} bg="red.50" borderRadius="md">
            <VStack spacing={2} align="stretch">
              <HStack>
                <Text fontWeight="bold" color="red.600">name:</Text>
                <Text fontSize="sm">Sinyalin adı (Robot adınız + "TP" = Take Profit)</Text>
              </HStack>
              <HStack>
                <Text fontWeight="bold" color="red.600">symbol:</Text>
                <Text fontSize="sm">TradingView'dan gelen sembol ({"{{"+ "ticker" + "}}"})  </Text>
              </HStack>
              <HStack>
                <Text fontWeight="bold" color="red.600">orderSide:</Text>
                <Text fontSize="sm">İşlem yönü (satım için "sell")</Text>
              </HStack>
              <HStack>
                <Text fontWeight="bold" color="red.600">orderType:</Text>
                <Text fontSize="sm">Emir tipi (piyasa emri için "mktbest")</Text>
              </HStack>
              <HStack>
                <Text fontWeight="bold" color="red.600">price:</Text>
                <Text fontSize="sm">TradingView'dan gelen kapanış fiyatı ({"{{"+ "close" + "}}"})  </Text>
              </HStack>
              <HStack>
                <Text fontWeight="bold" color="red.600">quantity:</Text>
                <Text fontSize="sm">İşlem miktarı</Text>
              </HStack>
              <HStack>
                <Text fontWeight="bold" color="red.600">timeInForce:</Text>
                <Text fontSize="sm">Emrin geçerlilik süresi ("day" = gün sonuna kadar)</Text>
              </HStack>
            </VStack>
          </Box>
        </VStack>

        <Alert status="success" borderRadius="md">
          <AlertIcon />
          <AlertDescription>
            <strong>İpucu:</strong> Hem alım hem de satım JSON'larınızı hazırladıktan sonra, 
            TradingView stratejinizde uygun koşullarda bu mesajları göndererek robotunuzu tamamen otomatize edebilirsiniz.
          </AlertDescription>
        </Alert>
      </VStack>
    </Box>
  );
}; 