/**
 * Health Check Utilities
 * Phase 3.3: Monitoring & Alerting Systems
 * System health monitoring and diagnostics
 */

import { supabase } from '../supabaseClient';

export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  checks: {
    database: HealthCheck;
    authentication: HealthCheck;
    api: HealthCheck;
    frontend: HealthCheck;
  };
  metadata: {
    version: string;
    environment: string;
    uptime: number;
  };
}

export interface HealthCheck {
  status: 'pass' | 'warn' | 'fail';
  responseTime: number;
  message?: string;
  details?: Record<string, any>;
}

class HealthCheckService {
  private startTime = Date.now();

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(): Promise<HealthCheckResult> {
    const timestamp = new Date().toISOString();
    
    const [database, authentication, api, frontend] = await Promise.allSettled([
      this.checkDatabase(),
      this.checkAuthentication(),
      this.checkAPI(),
      this.checkFrontend()
    ]);

    const checks = {
      database: this.getCheckResult(database),
      authentication: this.getCheckResult(authentication),
      api: this.getCheckResult(api),
      frontend: this.getCheckResult(frontend)
    };

    const overallStatus = this.determineOverallStatus(checks);

    return {
      status: overallStatus,
      timestamp,
      checks,
      metadata: {
        version: import.meta.env.VITE_APP_VERSION || '1.0.0',
        environment: import.meta.env.NODE_ENV || 'development',
        uptime: Date.now() - this.startTime
      }
    };
  }

  /**
   * Check database connectivity and performance
   */
  private async checkDatabase(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // Test basic database connectivity
      const { error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);

      const responseTime = performance.now() - startTime;

      if (error) {
        return {
          status: 'fail',
          responseTime,
          message: `Database error: ${error.message}`,
          details: { error: error.code }
        };
      }

      // Check response time
      if (responseTime > 2000) {
        return {
          status: 'warn',
          responseTime,
          message: 'Database response time is slow',
          details: { threshold: 2000 }
        };
      }

      return {
        status: 'pass',
        responseTime,
        message: 'Database is healthy'
      };
    } catch (error) {
      return {
        status: 'fail',
        responseTime: performance.now() - startTime,
        message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Check authentication service
   */
  private async checkAuthentication(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // Test auth service availability
      const { error } = await supabase.auth.getSession();
      const responseTime = performance.now() - startTime;

      if (error) {
        return {
          status: 'fail',
          responseTime,
          message: `Authentication error: ${error.message}`
        };
      }

      return {
        status: 'pass',
        responseTime,
        message: 'Authentication service is healthy'
      };
    } catch (error) {
      return {
        status: 'fail',
        responseTime: performance.now() - startTime,
        message: `Authentication check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Check API endpoints
   */
  private async checkAPI(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // Test API availability with a simple query
      const { error } = await supabase
        .from('robots')
        .select('id')
        .limit(1);

      const responseTime = performance.now() - startTime;

      if (error) {
        return {
          status: 'fail',
          responseTime,
          message: `API error: ${error.message}`
        };
      }

      return {
        status: 'pass',
        responseTime,
        message: 'API is healthy'
      };
    } catch (error) {
      return {
        status: 'fail',
        responseTime: performance.now() - startTime,
        message: `API check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Check frontend application health
   */
  private async checkFrontend(): Promise<HealthCheck> {
    const startTime = performance.now();
    
    try {
      // Check if critical DOM elements are present
      const hasRoot = document.getElementById('root') !== null;
      const hasReactApp = document.querySelector('[data-reactroot]') !== null || 
                         document.querySelector('#root > div') !== null;

      const responseTime = performance.now() - startTime;

      if (!hasRoot) {
        return {
          status: 'fail',
          responseTime,
          message: 'Root element not found'
        };
      }

      if (!hasReactApp) {
        return {
          status: 'warn',
          responseTime,
          message: 'React app may not be fully loaded'
        };
      }

      // Check for JavaScript errors
      const hasErrors = window.onerror !== null || 
                       (window as any).__errorCount > 0;

      if (hasErrors) {
        return {
          status: 'warn',
          responseTime,
          message: 'JavaScript errors detected'
        };
      }

      return {
        status: 'pass',
        responseTime,
        message: 'Frontend is healthy'
      };
    } catch (error) {
      return {
        status: 'fail',
        responseTime: performance.now() - startTime,
        message: `Frontend check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Extract check result from Promise.allSettled result
   */
  private getCheckResult(result: PromiseSettledResult<HealthCheck>): HealthCheck {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      return {
        status: 'fail',
        responseTime: 0,
        message: `Health check failed: ${result.reason}`
      };
    }
  }

  /**
   * Determine overall system status based on individual checks
   */
  private determineOverallStatus(checks: HealthCheckResult['checks']): HealthCheckResult['status'] {
    const statuses = Object.values(checks).map(check => check.status);
    
    if (statuses.includes('fail')) {
      return 'unhealthy';
    }
    
    if (statuses.includes('warn')) {
      return 'degraded';
    }
    
    return 'healthy';
  }

  /**
   * Get simple health status for quick checks
   */
  async getSimpleHealth(): Promise<{ status: string; timestamp: string }> {
    try {
      const result = await this.performHealthCheck();
      return {
        status: result.status,
        timestamp: result.timestamp
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Export singleton instance
export const healthCheckService = new HealthCheckService();

// Export utility functions
export const performHealthCheck = () => healthCheckService.performHealthCheck();
export const getSimpleHealth = () => healthCheckService.getSimpleHealth();
