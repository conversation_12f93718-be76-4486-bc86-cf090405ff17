/**
 * Test Setup Verification
 * Phase 3.1: Automated Testing Pipeline Setup
 * Verifies that the testing environment is properly configured
 */

import { describe, it, expect, vi } from 'vitest';

describe('Test Setup Verification', () => {
  it('should have vitest globals available', () => {
    expect(describe).toBeDefined();
    expect(it).toBeDefined();
    expect(expect).toBeDefined();
    expect(vi).toBeDefined();
  });

  it('should have mocked console methods', () => {
    expect(console.warn).toBeDefined();
    expect(console.error).toBeDefined();
  });

  it('should have mocked window.matchMedia', () => {
    expect(window.matchMedia).toBeDefined();
    
    const mediaQuery = window.matchMedia('(max-width: 768px)');
    expect(mediaQuery).toHaveProperty('matches');
    expect(mediaQuery).toHaveProperty('media');
  });

  it('should have mocked localStorage', () => {
    expect(window.localStorage).toBeDefined();
    expect(window.localStorage.getItem).toBeDefined();
    expect(window.localStorage.setItem).toBeDefined();
  });

  it('should have mocked fetch', () => {
    expect(global.fetch).toBeDefined();
  });

  it('should have performance API mocked', () => {
    expect(global.performance).toBeDefined();
    expect(global.performance.now).toBeDefined();
  });

  it('should have crypto API mocked', () => {
    expect(global.crypto).toBeDefined();
    expect(global.crypto.randomUUID).toBeDefined();
  });
});
