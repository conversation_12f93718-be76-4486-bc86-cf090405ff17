/**
 * Monitoring Dashboard API Edge Function
 * Phase 3.3: Monitoring & Alerting Systems
 * Provides aggregated monitoring data for dashboards and alerts
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Types
interface DashboardQuery {
  type: 'overview' | 'performance' | 'errors' | 'business' | 'alerts';
  timeRange: '1h' | '24h' | '7d' | '30d';
  filters?: Record<string, any>;
}

interface MetricAggregation {
  metric: string;
  value: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  timestamp: string;
}

interface AlertSummary {
  total: number;
  critical: number;
  warning: number;
  info: number;
  recent: any[];
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
};

// Time range utilities
const getTimeRangeFilter = (timeRange: string) => {
  const now = new Date();
  const ranges = {
    '1h': new Date(now.getTime() - 60 * 60 * 1000),
    '24h': new Date(now.getTime() - 24 * 60 * 60 * 1000),
    '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
    '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  };
  
  return ranges[timeRange as keyof typeof ranges] || ranges['24h'];
};

// Overview dashboard data
const getOverviewData = async (timeRange: string) => {
  const startTime = getTimeRangeFilter(timeRange);
  const startTimeISO = startTime.toISOString();

  // Get performance metrics summary
  const { data: perfMetrics } = await supabase
    .from('performance_metrics')
    .select('metric_name, metric_value, timestamp')
    .gte('timestamp', startTimeISO)
    .order('timestamp', { ascending: false });

  // Get error summary
  const { data: errors } = await supabase
    .from('error_reports')
    .select('severity, timestamp')
    .gte('timestamp', startTimeISO);

  // Get business metrics summary
  const { data: businessMetrics } = await supabase
    .from('business_metrics')
    .select('event_name, event_value, timestamp')
    .gte('timestamp', startTimeISO);

  // Get active alerts
  const { data: alerts } = await supabase
    .from('monitoring_alerts')
    .select('*')
    .eq('acknowledged', false)
    .order('timestamp', { ascending: false })
    .limit(10);

  // Calculate key metrics
  const totalPageViews = businessMetrics?.filter(m => m.event_name === 'page_view').length || 0;
  const totalErrors = errors?.length || 0;
  const criticalErrors = errors?.filter(e => e.severity === 'critical').length || 0;
  
  // Calculate average response time
  const responseTimes = perfMetrics?.filter(m => m.metric_name.includes('response_time')) || [];
  const avgResponseTime = responseTimes.length > 0 
    ? responseTimes.reduce((sum, m) => sum + m.metric_value, 0) / responseTimes.length 
    : 0;

  // Calculate Web Vitals averages
  const lcpMetrics = perfMetrics?.filter(m => m.metric_name === 'web_vitals_lcp') || [];
  const avgLCP = lcpMetrics.length > 0 
    ? lcpMetrics.reduce((sum, m) => sum + m.metric_value, 0) / lcpMetrics.length 
    : 0;

  const fidMetrics = perfMetrics?.filter(m => m.metric_name === 'web_vitals_fid') || [];
  const avgFID = fidMetrics.length > 0 
    ? fidMetrics.reduce((sum, m) => sum + m.metric_value, 0) / fidMetrics.length 
    : 0;

  const clsMetrics = perfMetrics?.filter(m => m.metric_name === 'web_vitals_cls') || [];
  const avgCLS = clsMetrics.length > 0 
    ? clsMetrics.reduce((sum, m) => sum + m.metric_value, 0) / clsMetrics.length 
    : 0;

  return {
    summary: {
      pageViews: totalPageViews,
      errors: totalErrors,
      criticalErrors,
      avgResponseTime: Math.round(avgResponseTime),
      activeAlerts: alerts?.length || 0
    },
    webVitals: {
      lcp: Math.round(avgLCP),
      fid: Math.round(avgFID),
      cls: Math.round(avgCLS * 1000) / 1000 // Round to 3 decimal places
    },
    recentAlerts: alerts || [],
    timestamp: new Date().toISOString()
  };
};

// Performance dashboard data
const getPerformanceData = async (timeRange: string) => {
  const startTime = getTimeRangeFilter(timeRange);
  const startTimeISO = startTime.toISOString();

  // Get performance metrics
  const { data: metrics } = await supabase
    .from('performance_metrics')
    .select('*')
    .gte('timestamp', startTimeISO)
    .order('timestamp', { ascending: true });

  if (!metrics) return { metrics: [], aggregations: [] };

  // Group metrics by name and calculate aggregations
  const metricGroups = metrics.reduce((groups, metric) => {
    if (!groups[metric.metric_name]) {
      groups[metric.metric_name] = [];
    }
    groups[metric.metric_name].push(metric);
    return groups;
  }, {} as Record<string, any[]>);

  const aggregations = Object.entries(metricGroups).map(([name, values]) => {
    const avg = values.reduce((sum, v) => sum + v.metric_value, 0) / values.length;
    const min = Math.min(...values.map(v => v.metric_value));
    const max = Math.max(...values.map(v => v.metric_value));
    const p95 = calculatePercentile(values.map(v => v.metric_value), 95);

    return {
      metric: name,
      average: Math.round(avg * 100) / 100,
      min: Math.round(min * 100) / 100,
      max: Math.round(max * 100) / 100,
      p95: Math.round(p95 * 100) / 100,
      count: values.length
    };
  });

  return {
    metrics: metrics.slice(-100), // Return last 100 metrics for chart
    aggregations,
    timestamp: new Date().toISOString()
  };
};

// Error dashboard data
const getErrorData = async (timeRange: string) => {
  const startTime = getTimeRangeFilter(timeRange);
  const startTimeISO = startTime.toISOString();

  // Get error reports
  const { data: errors } = await supabase
    .from('error_reports')
    .select('*')
    .gte('timestamp', startTimeISO)
    .order('timestamp', { ascending: false });

  if (!errors) return { errors: [], summary: {} };

  // Calculate error summary
  const summary = {
    total: errors.length,
    critical: errors.filter(e => e.severity === 'critical').length,
    high: errors.filter(e => e.severity === 'high').length,
    medium: errors.filter(e => e.severity === 'medium').length,
    low: errors.filter(e => e.severity === 'low').length
  };

  // Group errors by message for frequency analysis
  const errorFrequency = errors.reduce((freq, error) => {
    const key = error.message.substring(0, 100); // Truncate for grouping
    freq[key] = (freq[key] || 0) + 1;
    return freq;
  }, {} as Record<string, number>);

  const topErrors = Object.entries(errorFrequency)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([message, count]) => ({ message, count }));

  return {
    errors: errors.slice(0, 50), // Return recent 50 errors
    summary,
    topErrors,
    timestamp: new Date().toISOString()
  };
};

// Business metrics dashboard data
const getBusinessData = async (timeRange: string) => {
  const startTime = getTimeRangeFilter(timeRange);
  const startTimeISO = startTime.toISOString();

  // Get business metrics
  const { data: metrics } = await supabase
    .from('business_metrics')
    .select('*')
    .gte('timestamp', startTimeISO)
    .order('timestamp', { ascending: true });

  if (!metrics) return { metrics: [], summary: {} };

  // Calculate event summary
  const eventSummary = metrics.reduce((summary, metric) => {
    if (!summary[metric.event_name]) {
      summary[metric.event_name] = { count: 0, totalValue: 0 };
    }
    summary[metric.event_name].count += 1;
    summary[metric.event_name].totalValue += metric.event_value;
    return summary;
  }, {} as Record<string, { count: number; totalValue: number }>);

  const topEvents = Object.entries(eventSummary)
    .sort(([,a], [,b]) => b.count - a.count)
    .slice(0, 10)
    .map(([event, data]) => ({
      event,
      count: data.count,
      totalValue: data.totalValue,
      avgValue: Math.round((data.totalValue / data.count) * 100) / 100
    }));

  return {
    metrics: metrics.slice(-100), // Return last 100 metrics
    topEvents,
    totalEvents: metrics.length,
    timestamp: new Date().toISOString()
  };
};

// Alerts dashboard data
const getAlertsData = async (timeRange: string) => {
  const startTime = getTimeRangeFilter(timeRange);
  const startTimeISO = startTime.toISOString();

  // Get alerts
  const { data: alerts } = await supabase
    .from('monitoring_alerts')
    .select('*')
    .gte('timestamp', startTimeISO)
    .order('timestamp', { ascending: false });

  if (!alerts) return { alerts: [], summary: {} };

  const summary = {
    total: alerts.length,
    critical: alerts.filter(a => a.severity === 'critical').length,
    warning: alerts.filter(a => a.severity === 'warning').length,
    info: alerts.filter(a => a.severity === 'info').length,
    acknowledged: alerts.filter(a => a.acknowledged).length,
    unacknowledged: alerts.filter(a => !a.acknowledged).length
  };

  return {
    alerts,
    summary,
    timestamp: new Date().toISOString()
  };
};

// Utility function to calculate percentile
const calculatePercentile = (values: number[], percentile: number): number => {
  const sorted = values.sort((a, b) => a - b);
  const index = Math.ceil((percentile / 100) * sorted.length) - 1;
  return sorted[index] || 0;
};

// Main handler
serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method !== 'GET' && req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    let query: DashboardQuery;

    if (req.method === 'GET') {
      const url = new URL(req.url);
      query = {
        type: (url.searchParams.get('type') as any) || 'overview',
        timeRange: (url.searchParams.get('timeRange') as any) || '24h',
        filters: {}
      };
    } else {
      query = await req.json();
    }

    // Validate query
    if (!['overview', 'performance', 'errors', 'business', 'alerts'].includes(query.type)) {
      return new Response(
        JSON.stringify({ error: 'Invalid dashboard type' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    if (!['1h', '24h', '7d', '30d'].includes(query.timeRange)) {
      return new Response(
        JSON.stringify({ error: 'Invalid time range' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    let data;

    // Get data based on type
    switch (query.type) {
      case 'overview':
        data = await getOverviewData(query.timeRange);
        break;
      case 'performance':
        data = await getPerformanceData(query.timeRange);
        break;
      case 'errors':
        data = await getErrorData(query.timeRange);
        break;
      case 'business':
        data = await getBusinessData(query.timeRange);
        break;
      case 'alerts':
        data = await getAlertsData(query.timeRange);
        break;
    }

    return new Response(
      JSON.stringify({
        success: true,
        type: query.type,
        timeRange: query.timeRange,
        data,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Monitoring dashboard error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
