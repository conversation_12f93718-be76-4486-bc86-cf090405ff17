import { useToast, UseToastOptions } from '@chakra-ui/react';
import { createContext, useContext, ReactNode } from 'react';

// Bildirimlerin tipi için tip tanımı
type ToastType = 'success' | 'error' | 'warning' | 'info';

// Toast yardımcı fonksiyonları için tip tanımı
interface ToastHelperContextType {
  showToast: (
    title: string,
    description?: string,
    type?: ToastType,
    duration?: number
  ) => void;
  showSuccessToast: (title: string, description?: string) => void;
  showErrorToast: (title: string, description?: string) => void;
  showWarningToast: (title: string, description?: string) => void;
  showInfoToast: (title: string, description?: string) => void;
}

// Toast context oluşturma
const ToastHelperContext = createContext<ToastHelperContextType | null>(null);

// Context provider bileşeni
export const ToastHelperProvider = ({ children }: { children: ReactNode }) => {
  const toast = useToast();

  // Genel toast gösterme fonksiyonu
  const showToast = (
    title: string,
    description: string = '',
    type: ToastType = 'info',
    duration: number = 3000
  ) => {
    const options: UseToastOptions = {
      title,
      description,
      status: type,
      duration,
      isClosable: true,
      position: 'top-right',
      variant: 'solid',
    };

    toast(options);
  };

  // Başarı bildirimi
  const showSuccessToast = (title: string, description: string = '') => {
    showToast(title, description, 'success');
  };

  // Hata bildirimi
  const showErrorToast = (title: string, description: string = '') => {
    showToast(title, description, 'error', 5000); // Hata bildirimlerini daha uzun göster
  };

  // Uyarı bildirimi
  const showWarningToast = (title: string, description: string = '') => {
    showToast(title, description, 'warning', 4000);
  };

  // Bilgi bildirimi
  const showInfoToast = (title: string, description: string = '') => {
    showToast(title, description, 'info');
  };

  // Context değerleri
  const value = {
    showToast,
    showSuccessToast,
    showErrorToast,
    showWarningToast,
    showInfoToast,
  };

  return (
    <ToastHelperContext.Provider value={value}>
      {children}
    </ToastHelperContext.Provider>
  );
};

// Toast yardımcı hook
export const useToastHelper = () => {
  const context = useContext(ToastHelperContext);
  
  if (!context) {
    throw new Error('useToastHelper hook must be used within a ToastHelperProvider');
  }
  
  return context;
};

// Direkt kullanım için yardımcı fonksiyonlar
export const createToastHelpers = () => {
  const toast = useToast();

  return {
    showSuccessToast: (title: string, description: string = '') => {
      toast({
        title,
        description,
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top-right',
      });
    },
    
    showErrorToast: (title: string, description: string = '') => {
      toast({
        title,
        description,
        status: 'error',
        duration: 5000,
        isClosable: true,
        position: 'top-right',
      });
    },
    
    showWarningToast: (title: string, description: string = '') => {
      toast({
        title,
        description,
        status: 'warning',
        duration: 4000,
        isClosable: true,
        position: 'top-right',
      });
    },
    
    showInfoToast: (title: string, description: string = '') => {
      toast({
        title,
        description,
        status: 'info',
        duration: 3000,
        isClosable: true,
        position: 'top-right',
      });
    }
  };
}; 