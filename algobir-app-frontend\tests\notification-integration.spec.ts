import { test, expect } from '@playwright/test';
import { 
  loginUser, 
  goToNotifications, 
  waitForPageLoad,
  disableDebugMode,
  TEST_USER
} from './utils/test-helpers';

test.describe('Bildirim Sistemi Entegrasyon Testleri', () => {
  test.beforeEach(async ({ page }) => {
    await loginUser(page);
    await waitForPageLoad(page);
  });

  test('Tam bildirim akışı: Oluşturma → Görüntüleme → Etkileşim', async ({ page }) => {
    // 1. Başlangıç durumunu kaydet
    await goToNotifications(page);
    const initialCount = await page.locator('[data-testid="notification-item"]').count();
    console.log(`📊 Başlangıç bildirim sayısı: ${initialCount}`);
    
    // 2. Test bildirimi oluştur
    const rpcTestButton = page.locator('button:has-text("RPC Test")');
    if (await rpcTestButton.isVisible()) {
      await rpcTestButton.click();
      
      // Toast mesajının görünmesini bekle
      await expect(page.locator('.chakra-toast')).toBeVisible();
      console.log('✅ Test bildirimi oluşturuldu');
      
      // 3. Sayfayı yenile ve yeni bildirimi kontrol et
      await page.reload();
      await waitForPageLoad(page);
      
      const newCount = await page.locator('[data-testid="notification-item"]').count();
      expect(newCount).toBeGreaterThan(initialCount);
      console.log(`📊 Yeni bildirim sayısı: ${newCount}`);
      
      // 4. Yeni bildirimin içeriğini kontrol et
      const firstNotification = page.locator('[data-testid="notification-item"]').first();
      await expect(firstNotification.locator('[data-testid="notification-title"]')).toBeVisible();
      await expect(firstNotification.locator('[data-testid="notification-message"]')).toBeVisible();
      
      const title = await firstNotification.locator('[data-testid="notification-title"]').textContent();
      console.log(`✅ Yeni bildirim başlığı: ${title}`);
    }
  });

  test('Navbar bildirim sayısı ve dropdown senkronizasyonu', async ({ page }) => {
    // Ana sayfaya git
    await page.goto('/');
    await waitForPageLoad(page);
    
    // Navbar'daki bildirim sayısını al
    const notificationButton = page.locator('button[aria-label="Bildirimler"]');
    await expect(notificationButton).toBeVisible();
    
    let navbarCount = 0;
    const badge = notificationButton.locator('.chakra-badge');
    if (await badge.isVisible()) {
      const badgeText = await badge.textContent();
      navbarCount = parseInt(badgeText || '0');
    }
    
    // Dropdown'ı aç
    await notificationButton.click();
    await expect(page.locator('[data-testid="notification-dropdown-menu"]')).toBeVisible();
    
    // Dropdown'daki bildirim sayısını say
    const dropdownNotifications = page.locator('[data-testid="notification-dropdown-menu"] [data-testid="notification-item"]');
    const dropdownCount = await dropdownNotifications.count();
    
    // Bildirimler sayfasına git
    await page.goto('/notifications');
    await waitForPageLoad(page);
    
    // Bildirimler sayfasındaki sayıyı al
    const pageNotifications = page.locator('[data-testid="notification-item"]');
    const pageCount = await pageNotifications.count();
    
    console.log(`📊 Navbar badge: ${navbarCount}, Dropdown: ${dropdownCount}, Sayfa: ${pageCount}`);
    
    // Sayıların tutarlı olduğunu kontrol et
    if (navbarCount > 0) {
      expect(dropdownCount).toBeGreaterThan(0);
    }
    expect(pageCount).toBeGreaterThanOrEqual(dropdownCount);
  });

  test('Bildirim türleri ve formatları doğruluğu', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    const notifications = page.locator('[data-testid="notification-item"]');
    const count = await notifications.count();
    
    let tradeNotificationFound = false;
    let systemNotificationFound = false;
    
    for (let i = 0; i < Math.min(count, 10); i++) {
      const notification = notifications.nth(i);
      const title = await notification.locator('[data-testid="notification-title"]').textContent();
      const message = await notification.locator('[data-testid="notification-message"]').textContent();
      
      // Trade bildirimi kontrolü
      if (title?.includes('Al Bildirimi') || title?.includes('Sat Bildirimi')) {
        tradeNotificationFound = true;
        
        // Türkçe format kontrolü
        expect(message).toMatch(/hissesinden/);
        expect(message).toMatch(/TL fiyatla/);
        expect(message).toMatch(/adet/);
        expect(message).toMatch(/Toplam tutar/);
        expect(message).toMatch(/₺[\d,]+/);
        
        console.log(`✅ Trade bildirimi formatı doğru: ${title}`);
      }
      
      // Sistem bildirimi kontrolü
      if (title?.includes('Test') || title?.includes('Sistem')) {
        systemNotificationFound = true;
        console.log(`✅ Sistem bildirimi bulundu: ${title}`);
      }
    }
    
    if (tradeNotificationFound) {
      console.log('✅ En az bir trade bildirimi bulundu ve formatı doğru');
    }
    
    if (systemNotificationFound) {
      console.log('✅ En az bir sistem bildirimi bulundu');
    }
  });

  test('Bildirim etkileşimleri ve navigasyon', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Aksiyon linki olan bildirimi bul
    const notificationWithAction = page.locator('[data-testid="notification-item"]').filter({
      has: page.locator('a')
    }).first();
    
    if (await notificationWithAction.isVisible()) {
      const actionLink = notificationWithAction.locator('a').first();
      const href = await actionLink.getAttribute('href');
      const linkText = await actionLink.textContent();
      
      console.log(`🔗 Aksiyon linki bulundu: "${linkText}" → ${href}`);
      
      if (href) {
        // Link'e tıkla
        await actionLink.click();
        await waitForPageLoad(page);
        
        // URL'nin değiştiğini kontrol et
        expect(page.url()).toContain(href);
        console.log(`✅ Navigasyon başarılı: ${href}`);
        
        // Hedef sayfanın yüklendiğini kontrol et
        await expect(page.locator('body')).toBeVisible();
        
        // Geri dön
        await page.goBack();
        await waitForPageLoad(page);
      }
    }
  });

  test('Bildirim silme işlevi', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    const initialCount = await page.locator('[data-testid="notification-item"]').count();
    
    if (initialCount > 0) {
      // İlk bildirimi bul
      const firstNotification = page.locator('[data-testid="notification-item"]').first();
      const title = await firstNotification.locator('[data-testid="notification-title"]').textContent();
      
      // Silme butonunu bul
      const deleteButton = firstNotification.locator('button[aria-label*="sil"]');
      
      if (await deleteButton.isVisible()) {
        console.log(`🗑️ Bildirim siliniyor: ${title}`);
        
        await deleteButton.click();
        
        // Onay dialogu varsa onayla
        const confirmButton = page.locator('button:has-text("Sil")');
        if (await confirmButton.isVisible()) {
          await confirmButton.click();
        }
        
        await waitForPageLoad(page);
        
        // Bildirim sayısının azaldığını kontrol et
        const newCount = await page.locator('[data-testid="notification-item"]').count();
        expect(newCount).toBeLessThanOrEqual(initialCount);
        
        console.log(`✅ Bildirim silindi. Yeni sayı: ${newCount}`);
      } else {
        console.log('ℹ️ Silme butonu bulunamadı');
      }
    } else {
      console.log('ℹ️ Silinecek bildirim yok');
    }
  });

  test('Bildirim arama ve filtreleme', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    const totalCount = await page.locator('[data-testid="notification-item"]').count();
    
    // Arama alanını bul
    const searchInput = page.locator('input[placeholder*="ara"]');
    
    if (await searchInput.isVisible()) {
      // "Test" kelimesi ile ara
      await searchInput.fill('Test');
      await waitForPageLoad(page);
      
      const filteredCount = await page.locator('[data-testid="notification-item"]').count();
      
      console.log(`🔍 Arama sonucu: ${filteredCount}/${totalCount} bildirim`);
      
      if (filteredCount > 0) {
        // Sonuçların "Test" kelimesini içerdiğini kontrol et
        const firstResult = page.locator('[data-testid="notification-item"]').first();
        const text = await firstResult.textContent();
        expect(text?.toLowerCase()).toContain('test');
        
        console.log('✅ Arama filtreleme çalışıyor');
      }
      
      // Aramayı temizle
      await searchInput.clear();
      await waitForPageLoad(page);
      
      const clearedCount = await page.locator('[data-testid="notification-item"]').count();
      expect(clearedCount).toBeGreaterThanOrEqual(filteredCount);
    } else {
      console.log('ℹ️ Arama alanı bulunamadı');
    }
  });

  test('Bildirim yükleme performansı', async ({ page }) => {
    const startTime = Date.now();
    
    await goToNotifications(page);
    await waitForPageLoad(page);
    
    const loadTime = Date.now() - startTime;
    
    // Sayfa 5 saniyeden kısa sürede yüklenmeli
    expect(loadTime).toBeLessThan(5000);
    
    // Bildirimler yüklenmiş olmalı
    const notificationCount = await page.locator('[data-testid="notification-item"]').count();
    expect(notificationCount).toBeGreaterThanOrEqual(0);
    
    console.log(`⚡ Bildirimler sayfası ${loadTime}ms'de yüklendi (${notificationCount} bildirim)`);
  });

  test('Daha fazla yükle işlevi', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    const initialCount = await page.locator('[data-testid="notification-item"]').count();
    
    // Daha fazla yükle butonunu bul
    const loadMoreButton = page.locator('button:has-text("Daha Fazla Yükle")');
    
    if (await loadMoreButton.isVisible()) {
      console.log(`📄 Daha fazla yükle butonu bulundu. Mevcut: ${initialCount} bildirim`);
      
      await loadMoreButton.click();
      await waitForPageLoad(page);
      
      const newCount = await page.locator('[data-testid="notification-item"]').count();
      expect(newCount).toBeGreaterThanOrEqual(initialCount);
      
      console.log(`✅ Daha fazla yüklendi: ${newCount} bildirim`);
    } else {
      console.log('ℹ️ Daha fazla yükle butonu yok (tüm bildirimler yüklenmiş)');
    }
  });

  test('Bildirim real-time güncellemesi simülasyonu', async ({ page }) => {
    await goToNotifications(page);
    const initialCount = await page.locator('[data-testid="notification-item"]').count();
    
    // Yeni sekmede test bildirimi oluştur
    const newPage = await page.context().newPage();
    await loginUser(newPage);
    await newPage.goto('/notifications');
    
    // RPC test butonuna tıkla
    const rpcTestButton = newPage.locator('button:has-text("RPC Test")');
    if (await rpcTestButton.isVisible()) {
      await rpcTestButton.click();
      await expect(newPage.locator('.chakra-toast')).toBeVisible();
      console.log('✅ Yeni sekmede test bildirimi oluşturuldu');
    }
    
    await newPage.close();
    
    // Ana sayfada güncellemeyi kontrol et
    await page.reload();
    await waitForPageLoad(page);
    
    const newCount = await page.locator('[data-testid="notification-item"]').count();
    
    if (newCount > initialCount) {
      console.log(`✅ Real-time güncelleme çalışıyor: ${initialCount} → ${newCount}`);
    } else {
      console.log(`ℹ️ Bildirim sayısı değişmedi: ${newCount}`);
    }
  });
});
