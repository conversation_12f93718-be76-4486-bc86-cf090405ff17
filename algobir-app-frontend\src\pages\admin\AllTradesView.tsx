import { useState, useEffect, useRef } from 'react';
import {
  Box,
  Heading,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Alert<PERSON>con,
  Badge,
  Stack,
  Input,
  InputGroup,
  InputLeftElement,
  Button,
  HStack,
  Select,
  useToast,
  Flex,
  Icon,
  IconButton
} from '@chakra-ui/react';
import { FiSearch, FiDownload, FiRefreshCw } from 'react-icons/fi';
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { supabase } from '../../supabaseClient';
import * as XLSX from 'xlsx';

// İşlem tipi 
interface Trade {
  id: number;
  user_id: string;
  received_at: string;
  signal_name: string;
  symbol: string;
  category: string;
  price: number | null;
  calculated_quantity: number | null;
  forwarded: boolean;
  order_side: string;
  name?: string; 
  system_name: string | null;
  signal_type: string | null;
  trade_category: string | null;
  pnl?: number | null;
  user_email?: string;
}

const PAGE_SIZE = 20;

const AllTradesView = () => {
  const [trades, setTrades] = useState<Trade[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [symbolFilter, setSymbolFilter] = useState('');
  const [userFilter, setUserFilter] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalTrades, setTotalTrades] = useState(0);
  const [refreshKey, setRefreshKey] = useState(0);
  const toast = useToast();

  // Realtime kanal referansı
  const channelRef = useRef<any>(null);

  // İşlemleri yükle
  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setLoading(true);
        setError(null);

        // Toplam işlem sayısını öğren (filtreleme dahil)
        const countQuery = supabase
          .from('trades')
          .select('id', { count: 'exact', head: true });
        
        // Filtreleri uygula
        if (symbolFilter) {
          countQuery.ilike('symbol', `%${symbolFilter}%`);
        }
        if (userFilter) {
          countQuery.ilike('user_id', `%${userFilter}%`);
        }
        if (categoryFilter) {
          countQuery.eq('trade_category', categoryFilter);
        }
        
        const { count, error: countError } = await countQuery;
        
        if (countError) {
          throw countError;
        }
        
        setTotalTrades(count || 0);
        
        // İşlemleri getir
        let query = supabase
          .from('trades')
          .select('*')
          .order('received_at', { ascending: false })
          .range((currentPage - 1) * PAGE_SIZE, currentPage * PAGE_SIZE - 1);
        
        // Filtreleri uygula
        if (symbolFilter) {
          query = query.ilike('symbol', `%${symbolFilter}%`);
        }
        if (userFilter) {
          query = query.ilike('user_id', `%${userFilter}%`);
        }
        if (categoryFilter) {
          query = query.eq('trade_category', categoryFilter);
        }
        
        const { data, error: tradesError } = await query;
        
        if (tradesError) {
          throw tradesError;
        }

        // Kullanıcı e-postalarını al
        if (data && data.length > 0) {
          const userIds = [...new Set(data.map(trade => trade.user_id).filter(id => id))];
          
          if (userIds.length > 0) {
            const { data: usersData, error: usersError } = await supabase.rpc(
              'get_user_emails_by_ids',
              { user_ids: userIds }
            );
            
            if (usersError) {
              console.warn('Auth kullanıcılarına erişilemedi (RPC):', usersError);
            }
            
            // E-postaları işlemlere ekle
            const userEmailMap = new Map<string, string>();
            if (usersData) {
              usersData.forEach((user: { id: string; email: string }) => {
                userEmailMap.set(user.id, user.email);
              });
            }
            
            let tradesWithEmail = data.map(trade => ({
              ...trade,
              user_email: userEmailMap.get(trade.user_id) || '-'
            }));
            
            setTrades(tradesWithEmail);
          } else {
            setTrades(data.map(trade => ({ ...trade, user_email: '-' })));
          }
        } else {
          setTrades(data || []);
        }
      } catch (err: any) {
        console.error('İşlemler yüklenirken hata oluştu:', err);
        setError(err.message || 'İşlemler yüklenirken bir hata oluştu.');
      } finally {
        setLoading(false);
      }
    };

    fetchTrades();

    // Realtime dinleyicisi
    const channel = supabase
      .channel('all_trades_channel')
      .on('postgres_changes', { 
        event: 'INSERT',
        schema: 'public',
        table: 'trades'
      }, () => {
        // Yeni işlem eklendiğinde sayfayı yenile
        if (currentPage === 1) {
          fetchTrades();
        } else {
          toast({
            title: 'Yeni işlem',
            description: 'Yeni işlemler eklendi. Son işlemleri görmek için sayfayı yenileyin.',
            status: 'info',
            duration: 5000,
            isClosable: true,
          });
        }
      })
      .subscribe();
    
    channelRef.current = channel;
    
    return () => {
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
      }
    };
  }, [currentPage, symbolFilter, userFilter, categoryFilter, refreshKey, toast]);

  // Sayfa değiştirme işlevleri
  const goToNextPage = () => {
    const totalPages = Math.ceil(totalTrades / PAGE_SIZE);
    if (currentPage < totalPages) {
      setCurrentPage(prevPage => prevPage + 1);
    }
  };
  
  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prevPage => prevPage - 1);
    }
  };
  
  const goToFirstPage = () => {
    setCurrentPage(1);
  };
  
  const goToLastPage = () => {
    const totalPages = Math.ceil(totalTrades / PAGE_SIZE);
    setCurrentPage(totalPages);
  };

  // Excel'e aktarma işlevleri
  const handleExportExcel = async () => {
    try {
      // Bütün kayıtları al (sayfalamadan bağımsız)
      const { data, error } = await supabase
        .from('trades')
        .select('*')
        .order('received_at', { ascending: false });
      
      if (error) {
        throw error;
      }
      
      // Kullanıcı e-postalarını al
      if (data && data.length > 0) {
        const userIds = [...new Set(data.map(trade => trade.user_id).filter(id => id))];
        
        if (userIds.length > 0) {
          const { data: usersData, error: usersError } = await supabase.rpc(
            'get_user_emails_by_ids',
            { user_ids: userIds }
          );
          
          if (usersError) {
            console.warn('Auth kullanıcılarına erişilemedi (RPC):', usersError);
          }
          
          // E-postaları işlemlere ekle
          const userEmailMap = new Map<string, string>();
          if (usersData) {
            usersData.forEach((user: { id: string; email: string }) => {
              userEmailMap.set(user.id, user.email);
            });
          }
          
          let tradesWithEmail = data.map(trade => ({
            ...trade,
            user_email: userEmailMap.get(trade.user_id) || '-'
          }));
          
          // Verileri Excel uyumlu bir formata dönüştür
          const formattedData = tradesWithEmail.map(trade => {
            return {
              'İşlem ID': trade.id,
              'Kullanıcı': trade.user_email,
              'Sembol': trade.symbol,
              'İşlem Tipi': trade.trade_category || '-',
              'Sinyal Tipi': trade.signal_type || '-',
              'Sistem': trade.system_name || '-',
              'Fiyat': trade.price || 0,
              'Miktar': trade.calculated_quantity ? Math.floor(trade.calculated_quantity) : 0,
              'Toplam': (trade.price || 0) * (trade.calculated_quantity || 0),
              'Kar/Zarar': trade.pnl || '-',
              'Durum': trade.position_status || '-',
              'Tarih': new Date(trade.received_at).toLocaleString('tr-TR')
            };
          });
          
          // Excel dosyasını oluştur
          const worksheet = XLSX.utils.json_to_sheet(formattedData);
          const workbook = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(workbook, worksheet, "İşlemler");
          
          // Excel'i indir
          XLSX.writeFile(workbook, "Tüm_İşlemler.xlsx");
          
          toast({
            title: 'Excel Dışa Aktarma',
            description: `${formattedData.length} işlem başarıyla Excel'e aktarıldı.`,
            status: 'success',
            duration: 3000,
            isClosable: true,
          });
        } else {
          toast({
            title: 'Dışa Aktarma',
            description: 'Aktarılacak kullanıcı ID bulunamadı.',
            status: 'warning',
            duration: 3000,
            isClosable: true,
          });
        }
      } else {
        toast({
          title: 'Dışa Aktarma',
          description: 'Aktarılacak işlem bulunamadı.',
          status: 'warning',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err: any) {
      console.error('Excel dışa aktarımı başarısız:', err);
      toast({
        title: 'Dışa Aktarım Hatası',
        description: err.message || 'Excel dosyası oluşturulamadı.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Yenileme fonksiyonu
  const handleRefresh = () => {
    setRefreshKey(old => old + 1);
  };

  // Yardımcı formatlama fonksiyonları
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('tr-TR');
    } catch (err) {
      return dateString || '-';
    }
  };

  const getCategoryColor = (category: string | null | undefined): string => {
    if (!category) return 'gray';
    
    const lowerCategory = category.toLowerCase();
    if (lowerCategory.includes('alım')) return 'green';
    if (lowerCategory.includes('satım')) {
      if (lowerCategory.includes('tp')) return 'blue';
      if (lowerCategory.includes('stop')) return 'red';
      return 'purple'; // Diğer satım tipleri
    }
    return 'gray'; // Varsayılan
  };

  const formatNumber = (value: number | null, minimumFractionDigits = 2): string => {
    if (value === null || value === undefined) return '-';
    return value.toLocaleString('tr-TR', { minimumFractionDigits });
  };

  // Tablo başlıklarının değeri
  const tableHeaders = [
    { key: 'received_at', label: 'Tarih' },
    { key: 'user_email', label: 'Kullanıcı' },
    { key: 'symbol', label: 'Sembol' },
    { key: 'trade_category', label: 'İşlem Tipi' },
    { key: 'price', label: 'Fiyat' },
    { key: 'calculated_quantity', label: 'Miktar' },
    { key: 'total', label: 'Toplam' },
    { key: 'pnl', label: 'Kar/Zarar' },
  ];

  // Toplam sayfa sayısı
  const totalPages = Math.ceil(totalTrades / PAGE_SIZE);

  return (
    <Stack spacing={6}>
      <Box>
        <Heading as="h2" size="lg" mb={2}>
          Tüm Kullanıcı İşlemleri
        </Heading>
        <Text color="gray.600">
          Sistemdeki tüm kullanıcıların işlemlerini görüntüleyin ve filtreyin
        </Text>
      </Box>
      
      {/* Filtreler ve Butonlar */}
      <Stack spacing={4} direction={{ base: 'column', md: 'row' }} wrap="wrap" mb={6}>
        <InputGroup maxW="200px">
          <InputLeftElement pointerEvents="none">
            <Icon as={FiSearch} color="gray.400" />
          </InputLeftElement>
          <Input
            placeholder="Sembol ara..."
            value={symbolFilter}
            onChange={(e) => {
              setSymbolFilter(e.target.value);
              setCurrentPage(1);
            }}
            borderRadius="md"
            bg="white"
          />
        </InputGroup>
        
        <InputGroup maxW="250px">
          <InputLeftElement pointerEvents="none">
            <Icon as={FiSearch} color="gray.400" />
          </InputLeftElement>
          <Input
            placeholder="Kullanıcı ID veya E-posta ara..."
            value={userFilter}
            onChange={(e) => {
              setUserFilter(e.target.value);
              setCurrentPage(1);
            }}
            borderRadius="md"
            bg="white"
          />
        </InputGroup>
        
        <Select
          placeholder="Kategori Seç"
          value={categoryFilter}
          onChange={(e) => {
            setCategoryFilter(e.target.value);
            setCurrentPage(1);
          }}
          maxW="180px"
          borderRadius="md"
          bg="white"
        >
          <option value="">Tümü</option>
          <option value="Alım">Alım</option>
          <option value="Satım">Satım</option>
        </Select>
        
        <Button
          leftIcon={<Icon as={FiDownload} />}
          variant="solid"
          colorScheme="green"
          onClick={handleExportExcel}
          ml="auto"
        >
          Excel'e Aktar
        </Button>
        
        <Button
          leftIcon={<Icon as={FiRefreshCw} />}
          variant="outline"
          colorScheme="blue"
          onClick={handleRefresh}
        >
          Yenile
        </Button>
      </Stack>
      
      {/* Hata Durumu */}
      {error && (
        <Alert status="error" mb={4}>
          <AlertIcon />
          {error}
        </Alert>
      )}
      
      {/* Yükleniyor Durumu */}
      {loading ? (
        <Flex justify="center" py={8}>
          <Spinner size="xl" />
        </Flex>
      ) : (
        <>
          {/* İşlem Sayısı Bilgisi */}
          <Text fontSize="sm" color="gray.600" mb={2}>
            Toplam {totalTrades} işlem içinden {trades.length} işlem gösteriliyor
            (Sayfa {currentPage}/{totalPages || 1})
          </Text>
          
          {/* İşlemler Tablosu */}
          <Box overflowX="auto">
            <Table variant="simple" size="sm" bg="white" borderRadius="md" boxShadow="sm">
              <Thead bg="gray.50">
                <Tr>
                  {tableHeaders.map((header) => (
                    <Th key={header.key}>{header.label}</Th>
                  ))}
                </Tr>
              </Thead>
              <Tbody>
                {trades.length === 0 ? (
                  <Tr>
                    <Td colSpan={tableHeaders.length} textAlign="center" py={4}>
                      İşlem bulunamadı
                    </Td>
                  </Tr>
                ) : (
                  trades.map((trade) => (
                    <Tr key={trade.id}>
                      <Td>{formatDate(trade.received_at)}</Td>
                      <Td>{trade.user_email || '-'}</Td>
                      <Td fontWeight="bold">{trade.symbol}</Td>
                      <Td>
                        <Badge colorScheme={getCategoryColor(trade.trade_category)}>
                          {trade.trade_category || '-'}
                        </Badge>
                      </Td>
                      <Td isNumeric>{formatNumber(trade.price)}</Td>
                      <Td isNumeric>{formatNumber(trade.calculated_quantity ? Math.floor(trade.calculated_quantity) : 0, 0)}</Td>
                      <Td isNumeric>
                        {trade.price && trade.calculated_quantity 
                          ? formatNumber(trade.price * trade.calculated_quantity)
                          : '-'}
                      </Td>
                      <Td isNumeric>
                        {trade.pnl !== null && trade.pnl !== undefined ? (
                          <Text color={Number(trade.pnl) >= 0 ? 'green.500' : 'red.500'} fontWeight="bold">
                            {formatNumber(Number(trade.pnl))}
                          </Text>
                        ) : '-'}
                      </Td>
                    </Tr>
                  ))
                )}
              </Tbody>
            </Table>
          </Box>
          
          {/* Sayfalama */}
          {totalPages > 1 && (
            <Flex justify="center" mt={4}>
              <HStack>
                <IconButton
                  aria-label="İlk sayfa"
                  icon={<ChevronLeftIcon />}
                  onClick={goToFirstPage}
                  isDisabled={currentPage === 1}
                  size="sm"
                />
                <IconButton
                  aria-label="Önceki sayfa"
                  icon={<ChevronLeftIcon />}
                  onClick={goToPrevPage}
                  isDisabled={currentPage === 1}
                  size="sm"
                />
                <Text mx={2}>
                  Sayfa {currentPage} / {totalPages}
                </Text>
                <IconButton
                  aria-label="Sonraki sayfa"
                  icon={<ChevronRightIcon />}
                  onClick={goToNextPage}
                  isDisabled={currentPage === totalPages}
                  size="sm"
                />
                <IconButton
                  aria-label="Son sayfa"
                  icon={<ChevronRightIcon />}
                  onClick={goToLastPage}
                  isDisabled={currentPage === totalPages}
                  size="sm"
                />
              </HStack>
            </Flex>
          )}
        </>
      )}
    </Stack>
  );
};

export default AllTradesView; 