import { mode } from '@chakra-ui/theme-tools';

export const InputComponent = {
  components: {
    Input: {
      baseStyle: {
        field: {
          fontWeight: 400,
          borderRadius: '16px',
        },
      },
      variants: {
        outline: (props: any) => ({
          field: {
            bg: mode('transparent', 'navy.800')(props),
            border: '1px solid',
            borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
            color: mode('secondaryGray.900', 'white')(props),
            borderRadius: '16px',
            fontSize: 'sm',
            p: '20px',
            _hover: {
              borderColor: 'brand.500',
            },
            _focus: {
              borderColor: 'brand.500',
              boxShadow: `0 0 0 1px var(--chakra-colors-brand-500)`,
            },
            _placeholder: { 
              color: 'secondaryGray.400' 
            },
          },
        }),
        main: (props: any) => ({
          field: {
            bg: mode('transparent', 'navy.800')(props),
            border: '1px solid',
            color: mode('secondaryGray.900', 'white')(props),
            borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
            borderRadius: '16px',
            fontSize: 'sm',
            p: '20px',
            _placeholder: { color: 'secondaryGray.400' }
          }
        }),
        auth: (props: any) => ({
          field: {
            fontWeight: '500',
            color: mode('navy.700', 'white')(props),
            bg: mode('transparent', 'transparent')(props),
            border: '1px solid',
            borderColor: mode('secondaryGray.100', 'rgba(135, 140, 189, 0.3)')(props),
            borderRadius: '16px',
            _placeholder: { color: 'secondaryGray.600', fontWeight: '400' }
          }
        }),
        search: () => ({
          field: {
            border: 'none',
            py: '11px',
            borderRadius: 'inherit',
            _placeholder: { color: 'secondaryGray.600' }
          }
        })
      },
    },
    Textarea: {
      baseStyle: {
        fontWeight: 400,
        borderRadius: '16px',
      },
      variants: {
        outline: (props: any) => ({
          bg: mode('transparent', 'navy.800')(props),
          border: '1px solid',
          borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          color: mode('secondaryGray.900', 'white')(props),
          borderRadius: '16px',
          fontSize: 'sm',
          p: '20px',
          _hover: {
            borderColor: 'brand.500',
          },
          _focus: {
            borderColor: 'brand.500',
            boxShadow: `0 0 0 1px var(--chakra-colors-brand-500)`,
          },
          _placeholder: { 
            color: 'secondaryGray.400' 
          },
        }),
        main: (props: any) => ({
          bg: mode('transparent', 'navy.800')(props),
          border: '1px solid',
          color: mode('secondaryGray.900', 'white')(props),
          borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          borderRadius: '16px',
          fontSize: 'sm',
          p: '20px',
          _placeholder: { color: 'secondaryGray.400' }
        }),
      },
    },
    NumberInput: {
      baseStyle: {
        field: {
          fontWeight: 400,
          borderRadius: '16px',
        },
      },
      variants: {
        outline: (props: any) => ({
          field: {
            bg: mode('transparent', 'navy.800')(props),
            border: '1px solid',
            borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
            borderRadius: '16px',
            _hover: {
              borderColor: 'brand.500',
            },
            _focus: {
              borderColor: 'brand.500',
              boxShadow: `0 0 0 1px var(--chakra-colors-brand-500)`,
            },
            _placeholder: { 
              color: 'secondaryGray.600' 
            },
          },
        }),
        main: () => ({
          field: {
            bg: 'transparent',
            border: '1px solid',
            borderColor: 'secondaryGray.100',
            borderRadius: '16px',
            _placeholder: { color: 'secondaryGray.600' }
          }
        }),
      },
    },
    Select: {
      baseStyle: {
        field: {
          fontWeight: 400,
          borderRadius: '16px',
        },
      },
      variants: {
        outline: (props: any) => ({
          field: {
            bg: mode('transparent', 'navy.800')(props),
            border: '1px solid',
            borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
            borderRadius: '16px',
            _hover: {
              borderColor: 'brand.500',
            },
            _focus: {
              borderColor: 'brand.500',
              boxShadow: `0 0 0 1px var(--chakra-colors-brand-500)`,
            },
            _placeholder: { 
              color: 'secondaryGray.600' 
            },
          },
          icon: {
            color: 'secondaryGray.600'
          },
        }),
        main: (props: any) => ({
          field: {
            bg: mode('transparent', 'navy.800')(props),
            border: '1px solid',
            color: 'secondaryGray.600',
            borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
            borderRadius: '16px',
            _placeholder: { color: 'secondaryGray.600' }
          },
          icon: {
            color: 'secondaryGray.600'
          }
        }),
      },
    },
  },
}; 