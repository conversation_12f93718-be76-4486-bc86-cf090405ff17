/**
 * Advanced Rate Limiter for Supabase Edge Functions
 * Multiple algorithms and comprehensive protection
 */

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (request: Request) => string;
  onLimitReached?: (request: Request, identifier: string) => void;
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
  blocked: boolean;
}

interface TokenBucketEntry {
  tokens: number;
  lastRefill: number;
}

interface SlidingWindowEntry {
  requests: number[];
}

// Different rate limiting strategies
export enum RateLimitStrategy {
  FIXED_WINDOW = 'fixed_window',
  SLIDING_WINDOW = 'sliding_window',
  TOKEN_BUCKET = 'token_bucket',
  ADAPTIVE = 'adaptive'
}

// Predefined rate limit configurations
export const RateLimitPresets = {
  // API endpoints
  API_STRICT: { windowMs: 60000, maxRequests: 10 }, // 10 req/min
  API_MODERATE: { windowMs: 60000, maxRequests: 30 }, // 30 req/min
  API_LENIENT: { windowMs: 60000, maxRequests: 100 }, // 100 req/min
  
  // Webhook endpoints
  WEBHOOK_TRADING: { windowMs: 60000, maxRequests: 60 }, // 60 req/min
  WEBHOOK_SIGNALS: { windowMs: 60000, maxRequests: 120 }, // 120 req/min
  
  // Authentication
  AUTH_LOGIN: { windowMs: 900000, maxRequests: 5 }, // 5 attempts per 15 min
  AUTH_REGISTER: { windowMs: 3600000, maxRequests: 3 }, // 3 attempts per hour
  
  // Admin operations
  ADMIN_OPERATIONS: { windowMs: 60000, maxRequests: 20 }, // 20 req/min
  
  // Public endpoints
  PUBLIC_READ: { windowMs: 60000, maxRequests: 200 }, // 200 req/min
} as const;

class RateLimiter {
  private fixedWindowStore = new Map<string, RateLimitEntry>();
  private tokenBucketStore = new Map<string, TokenBucketEntry>();
  private slidingWindowStore = new Map<string, SlidingWindowEntry>();
  private suspiciousIPs = new Set<string>();
  private blockedIPs = new Map<string, number>(); // IP -> unblock timestamp
  
  // Fixed window rate limiting
  checkFixedWindow(identifier: string, config: RateLimitConfig): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
  } {
    const now = Date.now();
    const entry = this.fixedWindowStore.get(identifier);
    
    if (!entry || now > entry.resetTime) {
      // New window
      const newEntry: RateLimitEntry = {
        count: 1,
        resetTime: now + config.windowMs,
        firstRequest: now,
        blocked: false
      };
      this.fixedWindowStore.set(identifier, newEntry);
      
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: newEntry.resetTime
      };
    }
    
    if (entry.count >= config.maxRequests) {
      // Mark as suspicious if hitting limits frequently
      if (!entry.blocked) {
        this.markSuspicious(identifier);
        entry.blocked = true;
      }
      
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime
      };
    }
    
    entry.count++;
    return {
      allowed: true,
      remaining: config.maxRequests - entry.count,
      resetTime: entry.resetTime
    };
  }
  
  // Token bucket rate limiting
  checkTokenBucket(identifier: string, config: RateLimitConfig): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
  } {
    const now = Date.now();
    const refillRate = config.maxRequests / (config.windowMs / 1000); // tokens per second
    const bucketSize = config.maxRequests;
    
    let entry = this.tokenBucketStore.get(identifier);
    
    if (!entry) {
      entry = {
        tokens: bucketSize,
        lastRefill: now
      };
      this.tokenBucketStore.set(identifier, entry);
    }
    
    // Refill tokens
    const timePassed = (now - entry.lastRefill) / 1000;
    const tokensToAdd = Math.floor(timePassed * refillRate);
    
    if (tokensToAdd > 0) {
      entry.tokens = Math.min(bucketSize, entry.tokens + tokensToAdd);
      entry.lastRefill = now;
    }
    
    if (entry.tokens < 1) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: now + ((1 - entry.tokens) / refillRate) * 1000
      };
    }
    
    entry.tokens--;
    return {
      allowed: true,
      remaining: Math.floor(entry.tokens),
      resetTime: now + (1 / refillRate) * 1000
    };
  }
  
  // Sliding window rate limiting
  checkSlidingWindow(identifier: string, config: RateLimitConfig): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
  } {
    const now = Date.now();
    const windowStart = now - config.windowMs;
    
    let entry = this.slidingWindowStore.get(identifier);
    
    if (!entry) {
      entry = { requests: [] };
      this.slidingWindowStore.set(identifier, entry);
    }
    
    // Remove old requests
    entry.requests = entry.requests.filter(time => time > windowStart);
    
    if (entry.requests.length >= config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.requests[0] + config.windowMs
      };
    }
    
    entry.requests.push(now);
    return {
      allowed: true,
      remaining: config.maxRequests - entry.requests.length,
      resetTime: now + config.windowMs
    };
  }
  
  // Adaptive rate limiting based on system load and user behavior
  checkAdaptive(identifier: string, config: RateLimitConfig): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
  } {
    // Check if IP is blocked
    const blockUntil = this.blockedIPs.get(identifier);
    if (blockUntil && Date.now() < blockUntil) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: blockUntil
      };
    }
    
    // Adjust limits based on suspicious activity
    let adjustedConfig = { ...config };
    
    if (this.suspiciousIPs.has(identifier)) {
      // Reduce limits for suspicious IPs
      adjustedConfig.maxRequests = Math.floor(config.maxRequests * 0.5);
      adjustedConfig.windowMs = config.windowMs * 2;
    }
    
    // Use sliding window for adaptive strategy
    return this.checkSlidingWindow(identifier, adjustedConfig);
  }
  
  // Main rate limiting method
  check(
    identifier: string, 
    config: RateLimitConfig, 
    strategy: RateLimitStrategy = RateLimitStrategy.FIXED_WINDOW
  ): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
    strategy: RateLimitStrategy;
  } {
    let result;
    
    switch (strategy) {
      case RateLimitStrategy.TOKEN_BUCKET:
        result = this.checkTokenBucket(identifier, config);
        break;
      case RateLimitStrategy.SLIDING_WINDOW:
        result = this.checkSlidingWindow(identifier, config);
        break;
      case RateLimitStrategy.ADAPTIVE:
        result = this.checkAdaptive(identifier, config);
        break;
      default:
        result = this.checkFixedWindow(identifier, config);
    }
    
    // Log rate limit violations
    if (!result.allowed) {
      console.warn(`[RateLimit] ${strategy} limit exceeded for ${identifier}`);
      
      if (config.onLimitReached) {
        // Note: We can't pass the actual request here, but we can log the identifier
        console.log(`[RateLimit] Limit reached callback triggered for ${identifier}`);
      }
    }
    
    return { ...result, strategy };
  }
  
  // Mark IP as suspicious
  private markSuspicious(identifier: string): void {
    this.suspiciousIPs.add(identifier);
    console.warn(`[RateLimit] Marked ${identifier} as suspicious`);
    
    // Auto-remove from suspicious list after 1 hour
    setTimeout(() => {
      this.suspiciousIPs.delete(identifier);
    }, 3600000);
  }
  
  // Block IP temporarily
  blockIP(identifier: string, durationMs: number = 3600000): void {
    const unblockTime = Date.now() + durationMs;
    this.blockedIPs.set(identifier, unblockTime);
    console.warn(`[RateLimit] Blocked ${identifier} until ${new Date(unblockTime).toISOString()}`);
    
    // Auto-cleanup
    setTimeout(() => {
      this.blockedIPs.delete(identifier);
    }, durationMs);
  }
  
  // Get identifier from request
  getIdentifier(request: Request, keyGenerator?: (req: Request) => string): string {
    if (keyGenerator) {
      return keyGenerator(request);
    }
    
    // Default: use IP address
    return request.headers.get('x-forwarded-for') || 
           request.headers.get('x-real-ip') || 
           request.headers.get('cf-connecting-ip') || 
           'unknown';
  }
  
  // Cleanup old entries
  cleanup(): void {
    const now = Date.now();
    
    // Cleanup fixed window store
    for (const [key, entry] of this.fixedWindowStore.entries()) {
      if (now > entry.resetTime) {
        this.fixedWindowStore.delete(key);
      }
    }
    
    // Cleanup sliding window store
    for (const [key, entry] of this.slidingWindowStore.entries()) {
      entry.requests = entry.requests.filter(time => time > now - 3600000); // Keep 1 hour
      if (entry.requests.length === 0) {
        this.slidingWindowStore.delete(key);
      }
    }
    
    // Cleanup blocked IPs
    for (const [ip, unblockTime] of this.blockedIPs.entries()) {
      if (now > unblockTime) {
        this.blockedIPs.delete(ip);
      }
    }
    
    console.log(`[RateLimit] Cleanup completed. Active entries: ${this.fixedWindowStore.size}`);
  }
  
  // Get statistics
  getStats(): {
    activeEntries: number;
    suspiciousIPs: number;
    blockedIPs: number;
  } {
    return {
      activeEntries: this.fixedWindowStore.size + this.slidingWindowStore.size,
      suspiciousIPs: this.suspiciousIPs.size,
      blockedIPs: this.blockedIPs.size
    };
  }
}

// Global rate limiter instance
const globalRateLimiter = new RateLimiter();

// Middleware function
export function withRateLimit(
  config: RateLimitConfig,
  strategy: RateLimitStrategy = RateLimitStrategy.FIXED_WINDOW
) {
  return (handler: (request: Request) => Promise<Response>) => {
    return async (request: Request): Promise<Response> => {
      const identifier = globalRateLimiter.getIdentifier(request, config.keyGenerator);
      const result = globalRateLimiter.check(identifier, config, strategy);
      
      if (!result.allowed) {
        const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000);
        
        return new Response(JSON.stringify({
          error: 'Rate limit exceeded',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter,
          limit: config.maxRequests,
          window: config.windowMs / 1000,
          strategy: result.strategy
        }), {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': retryAfter.toString(),
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': result.remaining.toString(),
            'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString(),
            'X-RateLimit-Strategy': result.strategy
          }
        });
      }
      
      // Add rate limit headers to successful responses
      const response = await handler(request);
      
      // Clone response to add headers
      const newResponse = new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers
      });
      
      newResponse.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
      newResponse.headers.set('X-RateLimit-Remaining', result.remaining.toString());
      newResponse.headers.set('X-RateLimit-Reset', Math.ceil(result.resetTime / 1000).toString());
      newResponse.headers.set('X-RateLimit-Strategy', result.strategy);
      
      return newResponse;
    };
  };
}

// Cleanup interval (run every 5 minutes)
setInterval(() => {
  globalRateLimiter.cleanup();
}, 300000);

export { globalRateLimiter, RateLimiter };
export default globalRateLimiter;
