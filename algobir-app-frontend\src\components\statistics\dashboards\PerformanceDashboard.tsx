import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  Button,
  ButtonGroup,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  Icon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,

  Switch,
  FormControl,
  FormLabel
} from '@chakra-ui/react';
import {
  ResponsiveContainer,

  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,

} from 'recharts';
import {
  FiTrendingUp,

  FiTarget,
  FiDollarSign,
  FiPercent,
  FiBarChart,
  FiPieChart,
  FiActivity,

  FiAlertTriangle
} from 'react-icons/fi';
import { EnhancedStatsData } from '../../../hooks/useEnhancedStatistics';
import AdvancedLineChart from '../charts/AdvancedLineChart';
import Interactive<PERSON>ie<PERSON>hart from '../charts/InteractivePieChart';

interface PerformanceDashboardProps {
  stats: EnhancedStatsData;
}

const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({ stats }) => {
  const [timeframe, setTimeframe] = useState<'daily' | 'weekly' | 'monthly'>('monthly');
  const [viewMode, setViewMode] = useState<'absolute' | 'percentage'>('absolute');
  const [showComparison, setShowComparison] = useState(true);
  
  const cardBg = useColorModeValue('white', 'gray.700');
  const accentColor = useColorModeValue('#4299E1', '#63B3ED');
  
  // Format functions
  const formatCurrency = (value: number) => `₺${value.toFixed(2)}`;
  const formatPercent = (value: number) => `${value.toFixed(2)}%`;

  // Calculate advanced performance metrics
  const performanceMetrics = useMemo(() => {
    const totalTrades = stats.totalTrades || 0;
    const winningTrades = stats.winningTrades || 0;

    const totalPnl = stats.totalPnl || 0;

    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
    const profitFactor = stats.profitFactor || 0;
    const maxDrawdown = stats.maxDrawdown || 0;
    const sharpeRatio = stats.sharpeRatio || 0;

    return [
      {
        title: 'Toplam ROI',
        value: formatPercent(stats.roiAnalysis?.totalROI || 0),
        icon: FiTrendingUp,
        color: (stats.roiAnalysis?.totalROI || 0) > 0 ? 'green' : 'red',
        change: stats.roiAnalysis?.totalROI || 0,
        description: 'Toplam yatırım getirisi'
      },
      {
        title: 'Kazanma Oranı',
        value: formatPercent(winRate),
        icon: FiTarget,
        color: winRate >= 60 ? 'green' : winRate >= 40 ? 'yellow' : 'red',
        change: winRate,
        description: 'Başarılı işlem oranı'
      },
      {
        title: 'Kâr Faktörü',
        value: profitFactor.toFixed(2),
        icon: FiBarChart,
        color: profitFactor > 1.5 ? 'green' : profitFactor > 1 ? 'yellow' : 'red',
        change: profitFactor,
        description: 'Brüt kâr / Brüt zarar'
      },
      {
        title: 'Sharpe Oranı',
        value: sharpeRatio.toFixed(2),
        icon: FiActivity,
        color: sharpeRatio > 1 ? 'green' : sharpeRatio > 0.5 ? 'yellow' : 'red',
        change: sharpeRatio,
        description: 'Risk ayarlı getiri'
      },
      {
        title: 'Maksimum Düşüş',
        value: formatPercent(Math.abs(maxDrawdown)),
        icon: FiAlertTriangle,
        color: Math.abs(maxDrawdown) < 10 ? 'green' : Math.abs(maxDrawdown) < 20 ? 'yellow' : 'red',
        change: maxDrawdown,
        description: 'En büyük kayıp oranı'
      },
      {
        title: 'Toplam P&L',
        value: formatCurrency(totalPnl),
        icon: FiDollarSign,
        color: totalPnl > 0 ? 'green' : 'red',
        change: totalPnl,
        description: 'Net kar/zarar'
      },
      {
        title: 'Ortalama İşlem',
        value: formatCurrency(totalTrades > 0 ? totalPnl / totalTrades : 0),
        icon: FiPercent,
        color: totalPnl > 0 ? 'green' : 'red',
        change: totalTrades > 0 ? totalPnl / totalTrades : 0,
        description: 'İşlem başına ortalama'
      },
      {
        title: 'Toplam İşlem',
        value: totalTrades.toString(),
        icon: FiPieChart,
        color: 'blue',
        change: 0,
        description: 'Toplam işlem sayısı'
      }
    ];
  }, [stats]);

  // Prepare profit/loss distribution data
  const profitLossData = useMemo(() => [
    { name: 'Kazançlı İşlemler', value: stats.winningTrades || 0, color: '#38A169' },
    { name: 'Zararlı İşlemler', value: stats.losingTrades || 0, color: '#E53E3E' }
  ], [stats.winningTrades, stats.losingTrades]);

  // Prepare win rate breakdown data
  const winRateData = useMemo(() => {
    const winRate = stats.totalTrades > 0 ? ((stats.winningTrades || 0) / stats.totalTrades) * 100 : 0;
    const lossRate = 100 - winRate;
    
    return [
      { name: 'Kazanma', value: winRate, color: '#38A169' },
      { name: 'Kayıp', value: lossRate, color: '#E53E3E' }
    ];
  }, [stats.totalTrades, stats.winningTrades]);

  // Prepare monthly performance data
  const monthlyPerformanceData = useMemo(() => {
    return (stats.roiAnalysis?.monthlyROI || []).map(item => ({
      date: item.month,
      value: viewMode === 'percentage' ? item.roi : item.returns,
      investment: item.investment,
      returns: item.returns,
      roi: item.roi
    }));
  }, [stats.roiAnalysis?.monthlyROI, viewMode]);

  // Robot comparison data
  const robotComparisonData = useMemo(() => {
    const robotData = [];
    
    // Add solo robot if exists
    if (stats.soloRobotStats) {
      robotData.push({
        name: 'Solo Robot',
        roi: stats.soloRobotStats.roi,
        totalPnl: stats.soloRobotStats.totalPnl,
        winRate: stats.soloRobotStats.winRate,
        trades: stats.soloRobotStats.totalTrades,
        color: '#4299E1'
      });
    }

    // Add bro robots
    if (stats.broRobotStats && stats.broRobotStats.length > 0) {
      stats.broRobotStats.forEach((robot, index) => {
        robotData.push({
          name: robot.robotName.length > 15 ? robot.robotName.slice(0, 15) + '...' : robot.robotName,
          roi: robot.roi,
          totalPnl: robot.totalPnl,
          winRate: robot.winRate,
          trades: robot.totalTrades,
          color: `hsl(${(index + 1) * 60}, 70%, 50%)`
        });
      });
    }

    return robotData;
  }, [stats.soloRobotStats, stats.broRobotStats]);

  return (
    <VStack spacing={6} align="stretch">
      <HStack justify="space-between" align="center">
        <Heading size="lg">Performans Analizi</Heading>
        <HStack spacing={4}>
          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="comparison-toggle" mb="0" fontSize="sm">
              Robot Karşılaştırması
            </FormLabel>
            <Switch
              id="comparison-toggle"
              isChecked={showComparison}
              onChange={(e) => setShowComparison(e.target.checked)}
            />
          </FormControl>
          <ButtonGroup size="sm" isAttached variant="outline">
            <Button
              isActive={viewMode === 'absolute'}
              onClick={() => setViewMode('absolute')}
            >
              Mutlak
            </Button>
            <Button
              isActive={viewMode === 'percentage'}
              onClick={() => setViewMode('percentage')}
            >
              Yüzde
            </Button>
          </ButtonGroup>
        </HStack>
      </HStack>

      {/* Performance Metrics Grid */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
        {performanceMetrics.map((metric, index) => (
          <Card key={index} bg={cardBg} borderRadius="16px" boxShadow="md">
            <CardBody>
              <Stat>
                <HStack spacing={3} mb={2}>
                  <Icon as={metric.icon} color={`${metric.color}.500`} boxSize={5} />
                  <StatLabel fontSize="sm" fontWeight="medium">{metric.title}</StatLabel>
                </HStack>
                <StatNumber 
                  fontSize="2xl" 
                  color={`${metric.color}.500`}
                  mb={1}
                >
                  {metric.value}
                </StatNumber>
                <StatHelpText fontSize="xs" mb={2}>
                  {metric.change !== 0 && (
                    <StatArrow type={metric.change > 0 ? 'increase' : 'decrease'} />
                  )}
                  {metric.description}
                </StatHelpText>
                {/* Performance indicator */}
                <Progress
                  value={Math.abs(metric.change)}
                  max={metric.title === 'Kazanma Oranı' ? 100 : undefined}
                  colorScheme={metric.color}
                  size="sm"
                  borderRadius="md"
                />
              </Stat>
            </CardBody>
          </Card>
        ))}
      </SimpleGrid>

      {/* Charts Section */}
      <Tabs variant="enclosed" colorScheme="blue">
        <TabList>
          <Tab>Genel Performans</Tab>
          <Tab>Kar/Zarar Analizi</Tab>
          {showComparison && <Tab>Robot Karşılaştırması</Tab>}
          <Tab>Trend Analizi</Tab>
        </TabList>

        <TabPanels>
          {/* General Performance Tab */}
          <TabPanel p={0} pt={6}>
            <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
              {/* Profit/Loss Distribution */}
              <Card bg={cardBg} borderRadius="16px" boxShadow="md">
                <CardHeader>
                  <Heading size="md">İşlem Dağılımı</Heading>
                </CardHeader>
                <CardBody>
                  <InteractivePieChart
                    data={profitLossData}
                    title=""
                    height={300}
                    formatValue={(value) => `${value} işlem`}
                    showPercentages={true}
                  />
                </CardBody>
              </Card>

              {/* Win Rate Analysis */}
              <Card bg={cardBg} borderRadius="16px" boxShadow="md">
                <CardHeader>
                  <Heading size="md">Kazanma/Kayıp Oranı</Heading>
                </CardHeader>
                <CardBody>
                  <InteractivePieChart
                    data={winRateData}
                    title=""
                    height={300}
                    formatValue={(value) => `${value.toFixed(1)}%`}
                    showPercentages={true}
                    viewMode="donut"
                  />
                </CardBody>
              </Card>
            </SimpleGrid>
          </TabPanel>

          {/* P&L Analysis Tab */}
          <TabPanel p={0} pt={6}>
            <Card bg={cardBg} borderRadius="16px" boxShadow="md">
              <CardHeader>
                <HStack justify="space-between">
                  <Heading size="md">Aylık Performans Trendi</Heading>
                  <ButtonGroup size="sm" isAttached variant="outline">
                    <Button
                      isActive={timeframe === 'daily'}
                      onClick={() => setTimeframe('daily')}
                    >
                      Günlük
                    </Button>
                    <Button
                      isActive={timeframe === 'weekly'}
                      onClick={() => setTimeframe('weekly')}
                    >
                      Haftalık
                    </Button>
                    <Button
                      isActive={timeframe === 'monthly'}
                      onClick={() => setTimeframe('monthly')}
                    >
                      Aylık
                    </Button>
                  </ButtonGroup>
                </HStack>
              </CardHeader>
              <CardBody>
                <AdvancedLineChart
                  data={monthlyPerformanceData}
                  title=""
                  dataKey="value"
                  formatValue={viewMode === 'percentage' ? formatPercent : formatCurrency}
                  height={400}
                  showBrush={true}
                  showMovingAverage={true}
                  color={accentColor}
                />
              </CardBody>
            </Card>
          </TabPanel>

          {/* Robot Comparison Tab */}
          {showComparison && (
            <TabPanel p={0} pt={6}>
              <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                {/* ROI Comparison */}
                <Card bg={cardBg} borderRadius="16px" boxShadow="md">
                  <CardHeader>
                    <Heading size="md">Robot ROI Karşılaştırması</Heading>
                  </CardHeader>
                  <CardBody>
                    <Box h="350px">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={robotComparisonData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <RechartsTooltip 
                            formatter={(value: number) => [formatPercent(value), 'ROI']}
                          />
                          <Bar dataKey="roi" fill={accentColor} />
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardBody>
                </Card>

                {/* Win Rate Comparison */}
                <Card bg={cardBg} borderRadius="16px" boxShadow="md">
                  <CardHeader>
                    <Heading size="md">Robot Kazanma Oranı</Heading>
                  </CardHeader>
                  <CardBody>
                    <Box h="350px">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={robotComparisonData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <RechartsTooltip 
                            formatter={(value: number) => [formatPercent(value), 'Kazanma Oranı']}
                          />
                          <Bar dataKey="winRate" fill="#48BB78" />
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardBody>
                </Card>
              </SimpleGrid>
            </TabPanel>
          )}

          {/* Trend Analysis Tab */}
          <TabPanel p={0} pt={6}>
            <VStack spacing={6}>
              {/* Performance Benchmarks */}
              <Card bg={cardBg} borderRadius="16px" boxShadow="md" w="100%">
                <CardHeader>
                  <Heading size="md">Performans Kıyaslamaları</Heading>
                </CardHeader>
                <CardBody>
                  <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
                    <VStack spacing={2}>
                      <Text fontSize="sm" color="gray.500">Mükemmel (&gt;20%)</Text>
                      <Progress value={Math.min((stats.roiAnalysis?.totalROI || 0), 20) / 20 * 100} colorScheme="green" size="lg" w="100%" />
                    </VStack>
                    <VStack spacing={2}>
                      <Text fontSize="sm" color="gray.500">İyi (10-20%)</Text>
                      <Progress value={Math.min(Math.max((stats.roiAnalysis?.totalROI || 0) - 10, 0), 10) / 10 * 100} colorScheme="blue" size="lg" w="100%" />
                    </VStack>
                    <VStack spacing={2}>
                      <Text fontSize="sm" color="gray.500">Orta (5-10%)</Text>
                      <Progress value={Math.min(Math.max((stats.roiAnalysis?.totalROI || 0) - 5, 0), 5) / 5 * 100} colorScheme="yellow" size="lg" w="100%" />
                    </VStack>
                  </SimpleGrid>
                </CardBody>
              </Card>
            </VStack>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </VStack>
  );
};

export default PerformanceDashboard;
