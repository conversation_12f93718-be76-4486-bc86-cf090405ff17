import React, { useEffect, useState } from 'react';
import {
  Box,
  Flex,
  Text,
  Button,
  Icon,
  HStack,
  VStack,
  Progress,
  useColorModeValue,
  Portal,
  Slide,
  IconButton,
  Badge
} from '@chakra-ui/react';
import {
  FiArrowRight,
  FiX,
  FiClock
} from 'react-icons/fi';
import { SearchResult } from '../../utils/searchUtils';

interface NavigationPreviewProps {
  isOpen: boolean;
  result: SearchResult | null;
  onNavigate: () => void;
  onClose: () => void;
  autoNavigateDelay?: number; // in milliseconds
}

const NavigationPreview: React.FC<NavigationPreviewProps> = ({
  isOpen,
  result,
  onNavigate,
  onClose,
  autoNavigateDelay = 3000
}) => {
  const [timeLeft, setTimeLeft] = useState(autoNavigateDelay);
  const [isCountingDown, setIsCountingDown] = useState(false);

  // Color mode values
  const bg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');
  const shadow = useColorModeValue(
    '0 10px 30px rgba(0, 0, 0, 0.15)',
    '0 10px 30px rgba(0, 0, 0, 0.4)'
  );

  // Start countdown when preview opens
  useEffect(() => {
    if (isOpen && result) {
      setTimeLeft(autoNavigateDelay);
      setIsCountingDown(true);

      const interval = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 100) {
            clearInterval(interval);
            setIsCountingDown(false);
            onNavigate();
            return 0;
          }
          return prev - 100;
        });
      }, 100);

      return () => {
        clearInterval(interval);
        setIsCountingDown(false);
      };
    }
  }, [isOpen, result, autoNavigateDelay, onNavigate]);

  // Reset state when closed
  useEffect(() => {
    if (!isOpen) {
      setTimeLeft(autoNavigateDelay);
      setIsCountingDown(false);
    }
  }, [isOpen, autoNavigateDelay]);

  // Keyboard support
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Enter':
          e.preventDefault();
          onNavigate();
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
        case ' ': // Space bar
          e.preventDefault();
          onNavigate();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onNavigate, onClose]);

  if (!result) return null;

  const progressValue = ((autoNavigateDelay - timeLeft) / autoNavigateDelay) * 100;

  // Get type-specific information
  const getTypeInfo = (type: string) => {
    switch (type) {
      case 'page':
        return {
          color: 'blue',
          label: 'Sayfa',
          description: 'Bu sayfaya yönlendirileceksiniz'
        };
      case 'robot':
        return {
          color: 'purple',
          label: 'Robot',
          description: 'Robot detaylarını görüntüleyeceksiniz'
        };
      case 'user':
        return {
          color: 'green',
          label: 'Kullanıcı',
          description: 'Kullanıcı profilini görüntüleyeceksiniz'
        };
      case 'documentation':
        return {
          color: 'orange',
          label: 'Dokümantasyon',
          description: 'Yardım sayfasını görüntüleyeceksiniz'
        };
      default:
        return {
          color: 'gray',
          label: 'İçerik',
          description: 'İçeriği görüntüleyeceksiniz'
        };
    }
  };

  const typeInfo = getTypeInfo(result.type);

  return (
    <Portal>
      <Slide direction="bottom" in={isOpen} style={{ zIndex: 9999 }}>
        <Box
          position="fixed"
          bottom="20px"
          left="50%"
          transform="translateX(-50%)"
          w={{ base: '90%', md: '500px' }}
          maxW="500px"
          bg={bg}
          borderRadius="xl"
          border="1px solid"
          borderColor={borderColor}
          boxShadow={shadow}
          p="4"
          mx="auto"
          role="dialog"
          aria-labelledby="preview-title"
          aria-describedby="preview-description"
          tabIndex={-1}
        >
          {/* Progress bar */}
          {isCountingDown && (
            <Progress
              value={progressValue}
              size="xs"
              colorScheme="blue"
              borderRadius="full"
              mb="3"
              bg={useColorModeValue('gray.100', 'gray.700')}
            />
          )}

          <Flex justify="space-between" align="flex-start" mb="3">
            <VStack align="flex-start" spacing="1" flex="1">
              <HStack spacing="2" wrap="wrap">
                <Badge colorScheme={typeInfo.color} variant="subtle" fontSize="xs">
                  {typeInfo.label}
                </Badge>
                {result.preview?.category && (
                  <Badge colorScheme="gray" variant="outline" fontSize="xs">
                    {result.preview.category}
                  </Badge>
                )}
                <Text fontSize="xs" color={secondaryTextColor}>
                  {Math.ceil(timeLeft / 1000)}s içinde yönlendirilecek
                </Text>
              </HStack>
              
              <Text
                id="preview-title"
                fontSize="md"
                fontWeight="semibold"
                color={textColor}
                noOfLines={1}
              >
                {result.title}
              </Text>
              
              {result.subtitle && (
                <Text fontSize="sm" color={secondaryTextColor} noOfLines={2}>
                  {result.subtitle}
                </Text>
              )}

              {result.preview?.description && (
                <Text
                  id="preview-description"
                  fontSize="xs"
                  color={secondaryTextColor}
                  noOfLines={2}
                >
                  {result.preview.description}
                </Text>
              )}

              {result.preview?.features && result.preview.features.length > 0 && (
                <Text fontSize="xs" color={secondaryTextColor} fontStyle="italic">
                  İçerik: {result.preview.features.slice(0, 2).join(', ')}
                  {result.preview.features.length > 2 && '...'}
                </Text>
              )}
            </VStack>

            <IconButton
              aria-label="Kapat"
              icon={<Icon as={FiX} />}
              size="sm"
              variant="ghost"
              onClick={onClose}
              color={secondaryTextColor}
              _hover={{ color: textColor }}
            />
          </Flex>

          <VStack spacing="2" align="stretch">
            {/* Keyboard shortcuts hint */}
            <Text fontSize="xs" color={secondaryTextColor} textAlign="center" opacity="0.8">
              Enter veya Space: Git • Esc: İptal
            </Text>

            <HStack spacing="2" justify="flex-end">
              <Button
                size="sm"
                variant="outline"
                leftIcon={<Icon as={FiClock} />}
                onClick={onClose}
                borderColor={borderColor}
                color={secondaryTextColor}
                _hover={{
                  borderColor: textColor,
                  color: textColor
                }}
              >
                İptal
              </Button>

              <Button
                size="sm"
                colorScheme="blue"
                leftIcon={<Icon as={FiArrowRight} />}
                onClick={onNavigate}
                _hover={{ transform: 'translateX(2px)' }}
                transition="all 0.2s"
              >
                Şimdi Git
              </Button>
            </HStack>
          </VStack>
        </Box>
      </Slide>
    </Portal>
  );
};

export default NavigationPreview;
