# Algobir Trading Platform - Staging Environment Configuration
# Phase 3.2: Deployment Automation & CI/CD Enhancement

# Application Environment
NODE_ENV=staging
VITE_APP_ENV=staging
VITE_APP_VERSION=1.0.0-staging
VITE_APP_BUILD_TIME=__BUILD_TIME__

# Supabase Configuration (Staging)
VITE_SUPABASE_URL=https://fllklckmycxcgwhboiji.supabase.co
VITE_SUPABASE_ANON_KEY=__STAGING_SUPABASE_ANON_KEY__
VITE_SUPABASE_SERVICE_ROLE_KEY=__STAGING_SUPABASE_SERVICE_ROLE_KEY__

# API Configuration
VITE_API_BASE_URL=https://fllklckmycxcgwhboiji.supabase.co
VITE_API_TIMEOUT=30000
VITE_API_RETRY_ATTEMPTS=3

# Feature Flags (Staging)
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_A_B_TESTING=true
VITE_PERFORMANCE_SAMPLE_RATE=0.1
VITE_ERROR_SAMPLE_RATE=1.0

# Security Configuration
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true
VITE_ENABLE_SECURE_COOKIES=true

# Performance Configuration
VITE_ENABLE_SERVICE_WORKER=true
VITE_ENABLE_CODE_SPLITTING=true
VITE_ENABLE_LAZY_LOADING=true
VITE_BUNDLE_ANALYZER=false

# Monitoring & Logging
VITE_LOG_LEVEL=debug
VITE_ENABLE_CONSOLE_LOGS=true
VITE_ENABLE_NETWORK_LOGGING=true
VITE_ENABLE_PERFORMANCE_LOGS=true

# Trading Configuration (Staging)
VITE_TRADING_MODE=paper
VITE_MAX_CONCURRENT_TRADES=10
VITE_ORDER_TIMEOUT=30000
VITE_ENABLE_REAL_TIME_UPDATES=true

# WebSocket Configuration
VITE_WS_RECONNECT_ATTEMPTS=5
VITE_WS_RECONNECT_DELAY=1000
VITE_WS_HEARTBEAT_INTERVAL=30000

# Cache Configuration
VITE_CACHE_TTL=300000
VITE_ENABLE_OFFLINE_MODE=true
VITE_CACHE_STRATEGY=stale-while-revalidate

# Development Tools (Staging)
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_REDUX_DEVTOOLS=true
VITE_ENABLE_WHYRENDER=false

# Error Handling
VITE_ERROR_BOUNDARY_FALLBACK=true
VITE_ENABLE_ERROR_OVERLAY=true
VITE_SENTRY_DSN=__STAGING_SENTRY_DSN__

# Deployment Configuration
VITE_DEPLOYMENT_URL=https://algobir-staging.vercel.app
VITE_DEPLOYMENT_BRANCH=develop
VITE_DEPLOYMENT_COMMIT=__GIT_COMMIT_SHA__

# Third-party Integrations (Staging)
VITE_GOOGLE_ANALYTICS_ID=__STAGING_GA_ID__
VITE_HOTJAR_ID=__STAGING_HOTJAR_ID__
VITE_INTERCOM_APP_ID=__STAGING_INTERCOM_ID__

# Rate Limiting
VITE_API_RATE_LIMIT=1000
VITE_API_RATE_WINDOW=3600000

# File Upload Configuration
VITE_MAX_FILE_SIZE=5242880
VITE_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Notification Configuration
VITE_ENABLE_PUSH_NOTIFICATIONS=true
VITE_ENABLE_EMAIL_NOTIFICATIONS=true
VITE_NOTIFICATION_SOUND=true

# Accessibility Configuration
VITE_ENABLE_HIGH_CONTRAST=true
VITE_ENABLE_REDUCED_MOTION=true
VITE_ENABLE_SCREEN_READER=true

# Internationalization
VITE_DEFAULT_LOCALE=en
VITE_SUPPORTED_LOCALES=en,tr,de,fr,es
VITE_ENABLE_RTL=false

# Build Configuration
VITE_BUILD_SOURCEMAP=true
VITE_BUILD_MINIFY=true
VITE_BUILD_ANALYZE=false
VITE_BUILD_REPORT=true
