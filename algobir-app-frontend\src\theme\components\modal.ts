import { mode } from '@chakra-ui/theme-tools';

export const ModalComponent = {
  components: {
    Modal: {
      baseStyle: (props: any) => ({
        dialog: {
          bg: mode('white', 'navy.800')(props),
          borderRadius: '20px',
          border: '1px solid',
          borderColor: mode('transparent', 'rgba(255, 255, 255, 0.15)')(props),
          boxShadow: mode(
            '14px 17px 40px 4px rgba(112, 144, 176, 0.18)',
            'unset'
          )(props),
          mx: '16px',
          my: '16px',
        },
        header: {
          color: mode('secondaryGray.900', 'white')(props),
          fontWeight: '700',
          fontSize: 'xl',
          borderBottom: '1px solid',
          borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          pb: '16px',
          px: '20px',
          pt: '20px',
        },
        body: {
          color: mode('secondaryGray.900', 'white')(props),
          px: '20px',
          py: '20px',
        },
        footer: {
          borderTop: '1px solid',
          borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          pt: '16px',
          px: '20px',
          pb: '20px',
        },
        closeButton: {
          color: mode('secondaryGray.600', 'white')(props),
          _hover: {
            color: mode('secondaryGray.900', 'white')(props),
            bg: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          },
        },
      }),
    },
  },
}; 