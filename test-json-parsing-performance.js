// Test script to verify JSON parsing performance measurement
// This simulates what happens in the Edge Functions

function testJSONParsingPerformance() {
  console.log('Testing JSON parsing performance measurement...');
  
  // Test data similar to what TradingView sends
  const testData = {
    name: "TEST_SIGNAL",
    symbol: "BTCUSDT", 
    action: "BUY",
    quantity: 100,
    price: 50000,
    timestamp: new Date().toISOString(),
    strategy: "test-strategy",
    exchange: "binance"
  };
  
  const requestBody = JSON.stringify(testData);
  console.log('Request body size:', requestBody.length, 'bytes');
  
  // Test 1: Simple JSON parsing timing
  console.log('\n=== Test 1: Simple JSON Parsing ===');
  const startTime1 = performance.now();
  const parsed1 = JSON.parse(requestBody);
  const endTime1 = performance.now();
  const duration1 = endTime1 - startTime1;
  console.log('JSON parsing time:', duration1.toFixed(6), 'ms');
  console.log('Parsed successfully:', !!parsed1.name);
  
  // Test 2: Multiple iterations to see variation
  console.log('\n=== Test 2: Multiple Iterations ===');
  const iterations = 100;
  let totalTime = 0;
  let minTime = Infinity;
  let maxTime = 0;
  
  for (let i = 0; i < iterations; i++) {
    const start = performance.now();
    JSON.parse(requestBody);
    const end = performance.now();
    const duration = end - start;
    
    totalTime += duration;
    minTime = Math.min(minTime, duration);
    maxTime = Math.max(maxTime, duration);
  }
  
  const avgTime = totalTime / iterations;
  console.log('Average time over', iterations, 'iterations:', avgTime.toFixed(6), 'ms');
  console.log('Min time:', minTime.toFixed(6), 'ms');
  console.log('Max time:', maxTime.toFixed(6), 'ms');
  
  // Test 3: Larger JSON to see if size matters
  console.log('\n=== Test 3: Larger JSON ===');
  const largeData = {
    ...testData,
    metadata: {
      indicators: Array.from({length: 100}, (_, i) => ({
        name: `indicator_${i}`,
        value: Math.random() * 100,
        timestamp: new Date().toISOString()
      })),
      history: Array.from({length: 50}, (_, i) => ({
        price: 50000 + Math.random() * 1000,
        volume: Math.random() * 1000000,
        timestamp: new Date(Date.now() - i * 60000).toISOString()
      }))
    }
  };
  
  const largeRequestBody = JSON.stringify(largeData);
  console.log('Large request body size:', largeRequestBody.length, 'bytes');
  
  const startTime3 = performance.now();
  const parsed3 = JSON.parse(largeRequestBody);
  const endTime3 = performance.now();
  const duration3 = endTime3 - startTime3;
  console.log('Large JSON parsing time:', duration3.toFixed(6), 'ms');
  console.log('Parsed successfully:', !!parsed3.name);
  
  // Test 4: Edge Function simulation
  console.log('\n=== Test 4: Edge Function Simulation ===');
  const performanceMetrics = {
    signalReceivedAt: new Date(),
    jsonParsingStartTime: 0,
    jsonParsingEndTime: 0,
    totalStartTime: performance.now()
  };
  
  // Simulate the exact code from Edge Function
  performanceMetrics.jsonParsingStartTime = performance.now();
  let signalData;
  try {
    signalData = JSON.parse(requestBody);
    performanceMetrics.jsonParsingEndTime = performance.now();
    console.log('Edge Function simulation - JSON parsing time:', 
      (performanceMetrics.jsonParsingEndTime - performanceMetrics.jsonParsingStartTime).toFixed(6), 'ms');
  } catch (parseError) {
    performanceMetrics.jsonParsingEndTime = performance.now();
    console.error('Parse error:', parseError);
  }
  
  // Test 5: High precision timing
  console.log('\n=== Test 5: High Precision Timing ===');
  const hrStart = process.hrtime.bigint();
  JSON.parse(requestBody);
  const hrEnd = process.hrtime.bigint();
  const hrDuration = Number(hrEnd - hrStart) / 1000000; // Convert nanoseconds to milliseconds
  console.log('High precision timing:', hrDuration.toFixed(6), 'ms');
  
  console.log('\n=== Summary ===');
  console.log('JSON parsing is extremely fast, often sub-millisecond');
  console.log('performance.now() precision might not capture very fast operations');
  console.log('This explains why we see 0.000ms in the database');
}

// Run the test
testJSONParsingPerformance();
