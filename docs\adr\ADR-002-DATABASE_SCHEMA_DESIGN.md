# ADR-002: Database Schema Design

## Status
**ACCEPTED** - 2025-01-30

## Context

The Algobir trading platform requires a robust database schema that can efficiently handle:
- User management and authentication
- Trading robot definitions and configurations
- Real-time trading signals and execution
- Subscription management for Bro-Robot services
- Performance metrics and analytics
- System monitoring and error tracking
- Scalable data access patterns

## Decision

We have designed a PostgreSQL database schema with the following core principles:

### Schema Design Principles
1. **Normalization**: Proper normalization to reduce data redundancy
2. **Performance**: Optimized indexes and query patterns
3. **Security**: Row Level Security (RLS) for data isolation
4. **Scalability**: Partitioning strategies for large tables
5. **Auditability**: Comprehensive audit trails and timestamps
6. **Flexibility**: JSONB fields for extensible metadata

### Core Tables Structure

#### User Management
```sql
-- User profiles with authentication integration
profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  username TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  bio TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User settings and configurations
user_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  webhook_id TEXT UNIQUE,
  api_keys JSONB DEFAULT '{}',
  permissions JSONB DEFAULT '{}',
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Trading System
```sql
-- Trading robots (Solo and Bro types)
robots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  seller_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  robot_type TEXT CHECK (robot_type IN ('solo', 'bro')) NOT NULL,
  performance_metrics JSONB DEFAULT '{}',
  configuration JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Trading signals and executions
trades (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  robot_id UUID REFERENCES robots(id) ON DELETE SET NULL,
  symbol TEXT NOT NULL,
  order_side TEXT CHECK (order_side IN ('buy', 'sell')) NOT NULL,
  quantity DECIMAL(20,8) NOT NULL,
  price DECIMAL(20,8) NOT NULL,
  executed_at TIMESTAMPTZ DEFAULT NOW(),
  status TEXT CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Subscription management for Bro-Robots
subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  robot_id UUID REFERENCES robots(id) ON DELETE CASCADE,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT true,
  subscription_type TEXT DEFAULT 'monthly',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Platform Features
```sql
-- User notifications system
notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT CHECK (type IN ('info', 'success', 'warning', 'error')) NOT NULL,
  metadata JSONB DEFAULT '{}',
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- System-wide announcements
announcements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  type TEXT CHECK (type IN ('maintenance', 'feature', 'security', 'general')) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  starts_at TIMESTAMPTZ DEFAULT NOW(),
  ends_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Monitoring and Analytics
```sql
-- Performance metrics collection
performance_metrics (
  id TEXT PRIMARY KEY,
  session_id TEXT NOT NULL,
  metric_name TEXT NOT NULL,
  metric_value NUMERIC NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL,
  url TEXT NOT NULL,
  tags JSONB DEFAULT '{}',
  metadata JSONB DEFAULT '{}'
);

-- Error reporting and tracking
error_reports (
  id TEXT PRIMARY KEY,
  session_id TEXT NOT NULL,
  error_message TEXT NOT NULL,
  stack_trace TEXT,
  severity TEXT CHECK (severity IN ('low', 'medium', 'high', 'critical')) NOT NULL,
  context JSONB DEFAULT '{}',
  timestamp TIMESTAMPTZ NOT NULL,
  url TEXT NOT NULL,
  user_agent TEXT,
  resolved_at TIMESTAMPTZ
);

-- Business metrics and KPIs
business_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name TEXT NOT NULL,
  metric_value NUMERIC NOT NULL,
  dimensions JSONB DEFAULT '{}',
  timestamp TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Row Level Security (RLS) Policies

#### User Data Protection
```sql
-- Users can only access their own profile data
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Users can only access their own settings
CREATE POLICY "Users can manage own settings" ON user_settings
  FOR ALL USING (auth.uid() = user_id);
```

#### Trading Data Security
```sql
-- Users can view their own trades
CREATE POLICY "Users can view own trades" ON trades
  FOR SELECT USING (auth.uid() = user_id);

-- Users can view trades from subscribed robots
CREATE POLICY "Users can view subscribed robot trades" ON trades
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM subscriptions 
      WHERE user_id = auth.uid() 
      AND robot_id = trades.robot_id 
      AND is_active = true
    )
  );

-- Robot owners can view all trades for their robots
CREATE POLICY "Robot owners can view robot trades" ON trades
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM robots 
      WHERE id = trades.robot_id 
      AND seller_id = auth.uid()
    )
  );
```

#### Robot Access Control
```sql
-- Users can view active robots and their own robots
CREATE POLICY "Users can view available robots" ON robots
  FOR SELECT USING (is_active = true OR seller_id = auth.uid());

-- Users can only manage their own robots
CREATE POLICY "Users can manage own robots" ON robots
  FOR ALL USING (auth.uid() = seller_id);
```

#### Monitoring Data Access
```sql
-- Service role can insert monitoring data
CREATE POLICY "Service role can insert monitoring data" ON performance_metrics
  FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- Authenticated users can view aggregated monitoring data
CREATE POLICY "Users can view monitoring data" ON performance_metrics
  FOR SELECT USING (auth.role() = 'authenticated');

-- Admin users can view all error reports
CREATE POLICY "Admins can view error reports" ON error_reports
  FOR SELECT USING (
    auth.jwt() ->> 'role' = 'admin' OR 
    auth.jwt() ->> 'role' = 'superuser'
  );
```

### Indexing Strategy

#### Performance Indexes
```sql
-- User-related indexes
CREATE INDEX idx_profiles_username ON profiles(username);
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX idx_user_settings_webhook_id ON user_settings(webhook_id);

-- Trading-related indexes
CREATE INDEX idx_robots_seller_id ON robots(seller_id);
CREATE INDEX idx_robots_type_active ON robots(robot_type, is_active);
CREATE INDEX idx_trades_user_id_timestamp ON trades(user_id, executed_at DESC);
CREATE INDEX idx_trades_robot_id_timestamp ON trades(robot_id, executed_at DESC);
CREATE INDEX idx_trades_symbol_timestamp ON trades(symbol, executed_at DESC);

-- Subscription indexes
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_robot_id ON subscriptions(robot_id);
CREATE INDEX idx_subscriptions_active ON subscriptions(is_active, expires_at);

-- Monitoring indexes
CREATE INDEX idx_performance_metrics_timestamp ON performance_metrics(timestamp DESC);
CREATE INDEX idx_performance_metrics_session ON performance_metrics(session_id);
CREATE INDEX idx_error_reports_timestamp ON error_reports(timestamp DESC);
CREATE INDEX idx_error_reports_severity ON error_reports(severity);
```

#### JSONB Indexes
```sql
-- JSONB field indexes for performance
CREATE INDEX idx_robots_performance_metrics ON robots USING GIN (performance_metrics);
CREATE INDEX idx_user_settings_permissions ON user_settings USING GIN (permissions);
CREATE INDEX idx_trades_metadata ON trades USING GIN (metadata);
CREATE INDEX idx_performance_metrics_tags ON performance_metrics USING GIN (tags);
```

### Data Partitioning Strategy

#### Time-based Partitioning
```sql
-- Partition trades table by month for better performance
CREATE TABLE trades_y2025m01 PARTITION OF trades
  FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE trades_y2025m02 PARTITION OF trades
  FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- Partition performance metrics by day
CREATE TABLE performance_metrics_y2025m01 PARTITION OF performance_metrics
  FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

## Rationale

### Design Decisions

#### PostgreSQL Choice
- **ACID Compliance**: Ensures data consistency for financial transactions
- **JSON Support**: Native JSONB support for flexible metadata storage
- **Performance**: Excellent query optimization and indexing capabilities
- **Scalability**: Horizontal scaling options with read replicas
- **Ecosystem**: Rich ecosystem of tools and extensions

#### UUID Primary Keys
- **Global Uniqueness**: No collision risk across distributed systems
- **Security**: Non-sequential IDs prevent enumeration attacks
- **Scalability**: Better distribution in sharded environments
- **Integration**: Compatible with Supabase Auth system

#### JSONB for Metadata
- **Flexibility**: Schema evolution without migrations
- **Performance**: Indexed queries on JSON fields
- **Developer Experience**: Easy to work with in application code
- **Future-Proofing**: Accommodates changing requirements

#### Row Level Security
- **Security by Default**: Database-level access control
- **Performance**: Efficient policy evaluation
- **Compliance**: Meets data protection requirements
- **Simplicity**: Reduces application-level security logic

### Performance Considerations

#### Query Optimization
- **Composite Indexes**: Multi-column indexes for common query patterns
- **Partial Indexes**: Indexes on filtered subsets of data
- **Expression Indexes**: Indexes on computed values
- **JSONB Indexes**: GIN indexes for JSON field queries

#### Scalability Strategies
- **Partitioning**: Time-based partitioning for large tables
- **Read Replicas**: Separate read and write workloads
- **Connection Pooling**: Efficient connection management
- **Query Caching**: Application-level query result caching

## Consequences

### Positive Consequences
- **Data Integrity**: Strong consistency guarantees for financial data
- **Security**: Comprehensive access control at database level
- **Performance**: Optimized for common query patterns
- **Flexibility**: JSONB fields allow schema evolution
- **Scalability**: Partitioning and indexing strategies support growth
- **Auditability**: Complete audit trails for compliance

### Negative Consequences
- **Complexity**: RLS policies add complexity to queries
- **Storage Overhead**: UUID keys consume more space than integers
- **Migration Complexity**: Schema changes require careful planning
- **JSONB Limitations**: Some queries on JSON fields can be slower

### Mitigation Strategies
- **Documentation**: Comprehensive schema documentation and examples
- **Testing**: Extensive testing of RLS policies and performance
- **Monitoring**: Query performance monitoring and optimization
- **Tooling**: Database migration and management tools

## Implementation Notes

### Migration Strategy
1. **Core Tables**: Implement user management and basic trading tables
2. **Security Policies**: Add RLS policies for data protection
3. **Indexes**: Create performance indexes based on query patterns
4. **Monitoring Tables**: Add performance and error tracking tables
5. **Partitioning**: Implement partitioning for high-volume tables

### Data Seeding
- **Test Data**: Comprehensive test data for development
- **Reference Data**: Trading symbols, currencies, and configurations
- **Admin Users**: Initial admin accounts and permissions

### Backup and Recovery
- **Automated Backups**: Daily automated backups with point-in-time recovery
- **Disaster Recovery**: Cross-region backup replication
- **Testing**: Regular backup restoration testing

## References
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Supabase Database Guide](https://supabase.com/docs/guides/database)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [PostgreSQL Performance Tuning](https://wiki.postgresql.org/wiki/Performance_Optimization)

## Related ADRs
- ADR-001: Technology Stack Selection
- ADR-003: Authentication and Authorization Strategy
- ADR-004: Real-time Data Architecture
- ADR-006: Data Privacy and Compliance Strategy

---

**Author**: Algobir Development Team  
**Date**: 2025-01-30  
**Status**: Accepted  
**Reviewers**: Database Architect, Security Engineer, Senior Developers
