import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../supabaseClient';

type AdminAuthState = {
  isAdmin: boolean;
  loading: boolean;
  error: Error | null;
};

export const useAdminAuth = () => {
  const { user } = useAuth();
  const [state, setState] = useState<AdminAuthState>({
    isAdmin: false,
    loading: true,
    error: null,
  });

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        setState({ isAdmin: false, loading: false, error: null });
        return;
      }

      try {
        setState(prev => ({ ...prev, loading: true, error: null }));
        
        const { data, error } = await supabase
          .from('user_settings')
          .select('is_superuser')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Admin yetkisi kontrol edilirken hata oluştu:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          });
          
          if (error.code === '406' || error.code === 'PGRST116') {
            throw new Error(`Kullanıcı yetkileri okunamadı: ${error.message}. Sayfa erişimi kısıtlanabilir.`);
          }
          
          throw error;
        }

        setState({
          isAdmin: data?.is_superuser || false,
          loading: false,
          error: null,
        });
      } catch (error) {
        console.error('Admin yetkisi kontrol edilirken hata oluştu:', error);
        setState({
          isAdmin: false,
          loading: false,
          error: error instanceof Error ? error : new Error('Bilinmeyen hata'),
        });
      }
    };

    if (user) {
      setState(prev => ({ ...prev, loading: true }));
      checkAdminStatus();
    }
  }, [user]);

  return state;
};

export default useAdminAuth; 