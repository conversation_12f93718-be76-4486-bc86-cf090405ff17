import React from 'react';
import {
  Box,
  Flex,
  Text,
  Button,
  useDisclosure,
  Icon,
} from '@chakra-ui/react';
import { FiPlayCircle } from 'react-icons/fi';
import { InitialSetupWizard } from '../wizards/InitialSetupWizard';
import { useAuth } from '../../context/AuthContext';

export const InitialSetupBar: React.FC = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { refetchUserData } = useAuth();

  const handleSetupComplete = async () => {
    await refetchUserData(); // Refresh user data to hide the setup bar
    onClose();
  };

  return (
    <>
      <Box
        bgGradient="linear(to-r, brand.500, brand.400)"
        color="white"
        p={4}
        borderRadius="lg"
        mb={6}
        boxShadow="lg"
      >
        <Flex align="center" justify="space-between">
          <Flex align="center">
            <Icon as={FiPlayCircle} w={8} h={8} mr={4} />
            <Box>
              <Text fontWeight="bold" fontSize="lg">Algobir'e Hoşgeldiniz!</Text>
              <Text fontSize="sm">2 aşamalı kurulum ile tüm özellikleri etkinleştirin.</Text>
            </Box>
          </Flex>
          <Button 
            colorScheme="whiteAlpha" 
            variant="solid"
            bg="whiteAlpha.200"
            _hover={{ 
              bg: "white", 
              color: "brand.500",
              transform: "translateY(-1px)",
              boxShadow: "0 4px 12px rgba(0,0,0,0.15)"
            }}
            _active={{
              bg: "whiteAlpha.300",
              transform: "translateY(0px)"
            }}
            transition="all 0.2s"
            fontWeight="semibold"
            px={6}
            onClick={onOpen}
          > 
            Başla
          </Button>
        </Flex>
      </Box>

      <InitialSetupWizard
        isOpen={isOpen}
        onClose={onClose}
        onComplete={handleSetupComplete}
      />
    </>
  );
}; 