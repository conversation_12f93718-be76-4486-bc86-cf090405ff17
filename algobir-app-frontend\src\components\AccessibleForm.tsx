import React, { useId, useState, useCallback } from 'react';
import {
  FormControl,
  FormLabel,
  FormErrorMessage,
  FormHelperText,
  Input,
  Textarea,
  Select,
  Checkbox,
  Radio,
  RadioGroup,
  Stack,
  Box,
  Text,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  InputProps,
  TextareaProps,
  SelectProps,
  CheckboxProps
} from '@chakra-ui/react';
import { ScreenReaderUtils } from '../utils/accessibilityUtils';

/**
 * Accessible form field props
 */
interface AccessibleFieldProps {
  label: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  describedBy?: string;
}

/**
 * Accessible Input Field
 */
interface AccessibleInputProps extends InputProps, AccessibleFieldProps {}

export const AccessibleInput: React.FC<AccessibleInputProps> = ({
  label,
  error,
  helperText,
  required = false,
  describedBy,
  ...inputProps
}) => {
  const fieldId = useId();
  const errorId = useId();
  const helperId = useId();

  const ariaDescribedBy = [
    error ? errorId : null,
    helperText ? helperId : null,
    describedBy
  ].filter(Boolean).join(' ') || undefined;

  return (
    <FormControl isInvalid={!!error} isRequired={required}>
      <FormLabel htmlFor={fieldId} fontSize="sm" fontWeight="medium">
        {label}
        {required && (
          <Text as="span" color="red.500" ml={1} aria-label="gerekli alan">
            *
          </Text>
        )}
      </FormLabel>
      
      <Input
        id={fieldId}
        aria-describedby={ariaDescribedBy}
        aria-invalid={!!error}
        {...inputProps}
      />
      
      {helperText && (
        <FormHelperText id={helperId} fontSize="sm">
          {helperText}
        </FormHelperText>
      )}
      
      {error && (
        <FormErrorMessage id={errorId} fontSize="sm">
          <Text as="span" role="alert" aria-live="polite">
            {error}
          </Text>
        </FormErrorMessage>
      )}
    </FormControl>
  );
};

/**
 * Accessible Textarea Field
 */
interface AccessibleTextareaProps extends TextareaProps, AccessibleFieldProps {
  maxLength?: number;
  showCharacterCount?: boolean;
}

export const AccessibleTextarea: React.FC<AccessibleTextareaProps> = ({
  label,
  error,
  helperText,
  required = false,
  describedBy,
  maxLength,
  showCharacterCount = false,
  value,
  onChange,
  ...textareaProps
}) => {
  const fieldId = useId();
  const errorId = useId();
  const helperId = useId();
  const countId = useId();

  const [charCount, setCharCount] = useState(
    typeof value === 'string' ? value.length : 0
  );

  const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCharCount(e.target.value.length);
    onChange?.(e);
  }, [onChange]);

  const ariaDescribedBy = [
    error ? errorId : null,
    helperText ? helperId : null,
    showCharacterCount ? countId : null,
    describedBy
  ].filter(Boolean).join(' ') || undefined;

  return (
    <FormControl isInvalid={!!error} isRequired={required}>
      <FormLabel htmlFor={fieldId} fontSize="sm" fontWeight="medium">
        {label}
        {required && (
          <Text as="span" color="red.500" ml={1} aria-label="gerekli alan">
            *
          </Text>
        )}
      </FormLabel>
      
      <Textarea
        id={fieldId}
        aria-describedby={ariaDescribedBy}
        aria-invalid={!!error}
        maxLength={maxLength}
        value={value}
        onChange={handleChange}
        {...textareaProps}
      />
      
      {showCharacterCount && maxLength && (
        <Text
          id={countId}
          fontSize="xs"
          color="gray.500"
          textAlign="right"
          mt={1}
          aria-live="polite"
        >
          {charCount}/{maxLength} karakter
        </Text>
      )}
      
      {helperText && (
        <FormHelperText id={helperId} fontSize="sm">
          {helperText}
        </FormHelperText>
      )}
      
      {error && (
        <FormErrorMessage id={errorId} fontSize="sm">
          <Text as="span" role="alert" aria-live="polite">
            {error}
          </Text>
        </FormErrorMessage>
      )}
    </FormControl>
  );
};

/**
 * Accessible Select Field
 */
interface AccessibleSelectProps extends SelectProps, AccessibleFieldProps {
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  placeholder?: string;
}

export const AccessibleSelect: React.FC<AccessibleSelectProps> = ({
  label,
  error,
  helperText,
  required = false,
  describedBy,
  options,
  placeholder,
  ...selectProps
}) => {
  const fieldId = useId();
  const errorId = useId();
  const helperId = useId();

  const ariaDescribedBy = [
    error ? errorId : null,
    helperText ? helperId : null,
    describedBy
  ].filter(Boolean).join(' ') || undefined;

  return (
    <FormControl isInvalid={!!error} isRequired={required}>
      <FormLabel htmlFor={fieldId} fontSize="sm" fontWeight="medium">
        {label}
        {required && (
          <Text as="span" color="red.500" ml={1} aria-label="gerekli alan">
            *
          </Text>
        )}
      </FormLabel>
      
      <Select
        id={fieldId}
        aria-describedby={ariaDescribedBy}
        aria-invalid={!!error}
        placeholder={placeholder}
        {...selectProps}
      >
        {options.map((option) => (
          <option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </Select>
      
      {helperText && (
        <FormHelperText id={helperId} fontSize="sm">
          {helperText}
        </FormHelperText>
      )}
      
      {error && (
        <FormErrorMessage id={errorId} fontSize="sm">
          <Text as="span" role="alert" aria-live="polite">
            {error}
          </Text>
        </FormErrorMessage>
      )}
    </FormControl>
  );
};

/**
 * Accessible Checkbox Field
 */
interface AccessibleCheckboxProps extends CheckboxProps, AccessibleFieldProps {}

export const AccessibleCheckbox: React.FC<AccessibleCheckboxProps> = ({
  label,
  error,
  helperText,
  required = false,
  describedBy,
  children,
  ...checkboxProps
}) => {
  const fieldId = useId();
  const errorId = useId();
  const helperId = useId();

  const ariaDescribedBy = [
    error ? errorId : null,
    helperText ? helperId : null,
    describedBy
  ].filter(Boolean).join(' ') || undefined;

  return (
    <FormControl isInvalid={!!error} isRequired={required}>
      <Checkbox
        id={fieldId}
        aria-describedby={ariaDescribedBy}
        aria-invalid={!!error ? true : undefined}
        {...checkboxProps}
      >
        {label || children}
        {required && (
          <Text as="span" color="red.500" ml={1} aria-label="gerekli alan">
            *
          </Text>
        )}
      </Checkbox>
      
      {helperText && (
        <FormHelperText id={helperId} fontSize="sm" mt={2}>
          {helperText}
        </FormHelperText>
      )}
      
      {error && (
        <FormErrorMessage id={errorId} fontSize="sm" mt={2}>
          <Text as="span" role="alert" aria-live="polite">
            {error}
          </Text>
        </FormErrorMessage>
      )}
    </FormControl>
  );
};

/**
 * Accessible Radio Group Field
 */
interface AccessibleRadioGroupProps {
  label: string;
  error?: string;
  helperText?: string;
  required?: boolean;
  describedBy?: string;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  value?: string;
  onChange?: (value: string) => void;
  orientation?: 'horizontal' | 'vertical';
}

export const AccessibleRadioGroup: React.FC<AccessibleRadioGroupProps> = ({
  label,
  error,
  helperText,
  required = false,
  describedBy,
  options,
  value,
  onChange,
  orientation = 'vertical'
}) => {

  const errorId = useId();
  const helperId = useId();

  const ariaDescribedBy = [
    error ? errorId : null,
    helperText ? helperId : null,
    describedBy
  ].filter(Boolean).join(' ') || undefined;

  return (
    <FormControl isInvalid={!!error} isRequired={required}>
      <FormLabel fontSize="sm" fontWeight="medium">
        {label}
        {required && (
          <Text as="span" color="red.500" ml={1} aria-label="gerekli alan">
            *
          </Text>
        )}
      </FormLabel>
      
      <RadioGroup
        value={value}
        onChange={onChange}
        aria-describedby={ariaDescribedBy}
        aria-invalid={!!error}
      >
        <Stack direction={orientation === 'horizontal' ? 'row' : 'column'} spacing={3}>
          {options.map((option) => (
            <Radio
              key={option.value}
              value={option.value}
              isDisabled={option.disabled}
            >
              {option.label}
            </Radio>
          ))}
        </Stack>
      </RadioGroup>
      
      {helperText && (
        <FormHelperText id={helperId} fontSize="sm" mt={2}>
          {helperText}
        </FormHelperText>
      )}
      
      {error && (
        <FormErrorMessage id={errorId} fontSize="sm" mt={2}>
          <Text as="span" role="alert" aria-live="polite">
            {error}
          </Text>
        </FormErrorMessage>
      )}
    </FormControl>
  );
};

/**
 * Accessible Form Container
 */
interface AccessibleFormProps {
  children: React.ReactNode;
  onSubmit?: (e: React.FormEvent) => void;
  title?: string;
  description?: string;
  errors?: string[];
  successMessage?: string;
  isLoading?: boolean;
}

export const AccessibleForm: React.FC<AccessibleFormProps> = ({
  children,
  onSubmit,
  title,
  description,
  errors = [],
  successMessage,
  isLoading = false
}) => {
  const formId = useId();
  const titleId = useId();
  const descriptionId = useId();
  const errorsId = useId();
  const successId = useId();

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    
    // Announce form submission to screen readers
    if (isLoading) {
      ScreenReaderUtils.announce('Form gönderiliyor...', 'polite');
    }
    
    onSubmit?.(e);
  }, [onSubmit, isLoading]);

  const ariaDescribedBy = [
    description ? descriptionId : null,
    errors.length > 0 ? errorsId : null,
    successMessage ? successId : null
  ].filter(Boolean).join(' ') || undefined;

  return (
    <Box as="form" id={formId} onSubmit={handleSubmit} noValidate>
      {title && (
        <Text
          id={titleId}
          as="h2"
          fontSize="xl"
          fontWeight="bold"
          mb={4}
          role="heading"
          aria-level={2}
        >
          {title}
        </Text>
      )}
      
      {description && (
        <Text id={descriptionId} color="gray.600" mb={6}>
          {description}
        </Text>
      )}
      
      {errors.length > 0 && (
        <Alert status="error" mb={6} id={errorsId} role="alert">
          <AlertIcon />
          <Box>
            <AlertTitle>Form Hataları:</AlertTitle>
            <AlertDescription>
              <ul>
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Box>
        </Alert>
      )}
      
      {successMessage && (
        <Alert status="success" mb={6} id={successId} role="alert">
          <AlertIcon />
          <AlertDescription>{successMessage}</AlertDescription>
        </Alert>
      )}
      
      <Stack spacing={6} aria-describedby={ariaDescribedBy}>
        {children}
      </Stack>
    </Box>
  );
};
