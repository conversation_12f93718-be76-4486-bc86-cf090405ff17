/**
 * Monitoring Page Component
 * Phase 3.3: Monitoring & Alerting Systems
 * Main monitoring dashboard page with comprehensive system monitoring
 */

import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  useColorModeValue,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Icon
} from '@chakra-ui/react';
import { FiHome, FiActivity, FiChevronRight } from 'react-icons/fi';
import { Link as RouterLink } from 'react-router-dom';

// Components
import MonitoringDashboard from '../components/monitoring/MonitoringDashboard';
import { useMonitoring } from '../hooks/useMonitoring';

const MonitoringPage: React.FC = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  
  // Initialize monitoring for this page
  const { isEnabled, isInitialized, trackUserAction } = useMonitoring({
    enabled: true,
    trackPageViews: true,
    trackUserInteractions: true,
    trackPerformance: true,
    trackErrors: true
  });

  // Track page access
  React.useEffect(() => {
    trackUserAction('page_access', 'monitoring_dashboard', {
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    });
  }, [trackUserAction]);

  return (
    <Box minH="100vh" bg={bgColor}>
      <Container maxW="full" p={0}>
        {/* Header */}
        <Box bg={cardBg} borderBottom="1px" borderColor="gray.200" px={6} py={4}>
          <VStack align="start" spacing={3}>
            {/* Breadcrumb */}
            <Breadcrumb spacing="8px" separator={<Icon as={FiChevronRight} color="gray.500" />}>
              <BreadcrumbItem>
                <BreadcrumbLink as={RouterLink} to="/">
                  <HStack spacing={2}>
                    <Icon as={FiHome} />
                    <Text>Dashboard</Text>
                  </HStack>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink>
                  <HStack spacing={2}>
                    <Icon as={FiActivity} />
                    <Text>Monitoring</Text>
                  </HStack>
                </BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>

            {/* Page Title */}
            <HStack justify="space-between" width="100%">
              <VStack align="start" spacing={1}>
                <Heading size="lg" color="gray.800">
                  System Monitoring
                </Heading>
                <Text color="gray.600" fontSize="sm">
                  Real-time performance metrics, error tracking, and system health monitoring
                </Text>
              </VStack>
              
              <HStack spacing={3}>
                <Badge 
                  colorScheme={isEnabled ? 'green' : 'red'} 
                  variant="subtle"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  {isEnabled ? 'Monitoring Active' : 'Monitoring Disabled'}
                </Badge>
                
                <Badge 
                  colorScheme={isInitialized ? 'blue' : 'gray'} 
                  variant="outline"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  {isInitialized ? 'Initialized' : 'Initializing'}
                </Badge>
              </HStack>
            </HStack>
          </VStack>
        </Box>

        {/* Main Content */}
        <Box p={0}>
          {isEnabled ? (
            <MonitoringDashboard 
              timeRange="24h"
              autoRefresh={true}
              refreshInterval={30000}
            />
          ) : (
            <Box p={8} textAlign="center">
              <VStack spacing={4}>
                <Icon as={FiActivity} size="48px" color="gray.400" />
                <Heading size="md" color="gray.600">
                  Monitoring Disabled
                </Heading>
                <Text color="gray.500" maxW="md">
                  System monitoring is currently disabled. Enable monitoring in your environment 
                  configuration to view performance metrics, error reports, and system health data.
                </Text>
                <Box bg="gray.100" p={4} borderRadius="md" fontFamily="mono" fontSize="sm">
                  <Text>VITE_ENABLE_PERFORMANCE_MONITORING=true</Text>
                  <Text>VITE_ENABLE_ERROR_REPORTING=true</Text>
                </Box>
              </VStack>
            </Box>
          )}
        </Box>
      </Container>
    </Box>
  );
};

export default MonitoringPage;
