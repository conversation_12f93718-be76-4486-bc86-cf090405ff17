import React, { useEffect, useRef } from 'react';
import mermaid from 'mermaid';

// Initialize Mermaid. It's important to set startOnLoad to false
// as we will be controlling the rendering manually.
mermaid.initialize({
  startOnLoad: false,
  theme: 'base', // You can set a default theme: 'default', 'dark', 'forest', 'neutral'
  securityLevel: 'loose',
});

interface MermaidProps {
  chart: string; // This prop will contain the Mermaid diagram syntax
}

const Mermaid: React.FC<MermaidProps> = ({ chart }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Only proceed if the container exists and a chart definition is provided
    if (containerRef.current && chart) {
      // Use a unique ID for each render to avoid conflicts
      const uniqueId = `mermaid-graph-${Math.random().toString(36).substring(2, 9)}`;
      
      try {
        // Modern mermaid API - render returns a promise
        mermaid.render(uniqueId, chart).then((result) => {
          // Modern API returns an object with svg property
          if (containerRef.current && result.svg) {
            containerRef.current.innerHTML = result.svg;
          }
        }).catch((error) => {
          console.error("Failed to render Mermaid diagram:", error);
          if (containerRef.current) {
            containerRef.current.innerHTML = "Error rendering diagram.";
          }
        });
      } catch (error) {
        console.error("Failed to render Mermaid diagram:", error);
        if (containerRef.current) {
          containerRef.current.innerHTML = "Error rendering diagram.";
        }
      }
    }
  }, [chart]); // This effect will re-run only when the 'chart' prop changes.

  // This div is the stable container for the Mermaid SVG.
  return <div className="mermaid-container" ref={containerRef} />;
};

export default Mermaid; 