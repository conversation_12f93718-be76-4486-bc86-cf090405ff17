import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  SimpleGrid,
  Badge,
  Icon,
  Progress,
  Tooltip,
  Divider
} from '@chakra-ui/react';
import {
  FaDatabase,
  FaShieldAlt,
  FaCloud,
  FaGlobe,
  FaServer,
  FaCheck,
  FaExclamationTriangle,
  FaTimes,
  FaCog,
  FaWifi,

} from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { ServiceStatus } from '../../hooks/useSystemMonitoring';

const MotionBox = motion(Box);

interface RealTimeServiceStatusProps {
  services: ServiceStatus[];
  loading?: boolean;
  lastUpdate?: Date;
  isConnected?: boolean;
}

const RealTimeServiceStatus: React.FC<RealTimeServiceStatusProps> = ({
  services,
  loading = false,
  lastUpdate,
  isConnected = true
}) => {
  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');
  const healthyBg = useColorModeValue('green.50', 'green.900');
  const warningBg = useColorModeValue('yellow.50', 'yellow.900');
  const errorBg = useColorModeValue('red.50', 'red.900');

  // Helper functions
  const getStatusColor = (status: 'healthy' | 'warning' | 'error') => {
    switch (status) {
      case 'healthy': return 'green';
      case 'warning': return 'yellow';
      case 'error': return 'red';
      default: return 'gray';
    }
  };

  const getStatusIcon = (status: 'healthy' | 'warning' | 'error') => {
    switch (status) {
      case 'healthy': return FaCheck;
      case 'warning': return FaExclamationTriangle;
      case 'error': return FaTimes;
      default: return FaCog;
    }
  };

  const getStatusText = (status: 'healthy' | 'warning' | 'error') => {
    switch (status) {
      case 'healthy': return 'Sağlıklı';
      case 'warning': return 'Uyarı';
      case 'error': return 'Hata';
      default: return 'Bilinmiyor';
    }
  };

  const getServiceIcon = (serviceName: string) => {
    if (serviceName.includes('Veritabanı')) return FaDatabase;
    if (serviceName.includes('Kimlik')) return FaShieldAlt;
    if (serviceName.includes('Edge') || serviceName.includes('Webhook') || serviceName.includes('Robot')) return FaCloud;
    if (serviceName.includes('API')) return FaGlobe;
    return FaServer;
  };

  const getServiceBg = (status: 'healthy' | 'warning' | 'error') => {
    switch (status) {
      case 'healthy': return healthyBg;
      case 'warning': return warningBg;
      case 'error': return errorBg;
      default: return cardBg;
    }
  };

  // Calculate overall health
  const healthyServices = services.filter(s => s.status === 'healthy').length;
  const totalServices = services.length;
  const overallHealth = totalServices > 0 ? (healthyServices / totalServices) * 100 : 0;

  return (
    <Card
      bg={cardBg}
      borderRadius="xl"
      boxShadow="lg"
      border="1px solid"
      borderColor={borderColor}
      overflow="hidden"
    >
      <CardHeader pb={2}>
        <HStack justify="space-between" align="center">
          <VStack align="start" spacing={1}>
            <Heading size="md" color={textColor}>
              Servis Durumu
            </Heading>
            <HStack spacing={2}>
              <Icon
                as={isConnected ? FaWifi : FaWifi}
                color={isConnected ? 'green.500' : 'red.500'}
                boxSize={3}
              />
              <Text fontSize="xs" color={mutedColor}>
                {loading ? 'Kontrol ediliyor...' : 
                 lastUpdate ? `Son kontrol: ${lastUpdate.toLocaleTimeString('tr-TR')}` : 
                 'Veri yok'}
              </Text>
            </HStack>
          </VStack>

          <VStack align="end" spacing={1}>
            <Badge
              colorScheme={overallHealth >= 80 ? 'green' : overallHealth >= 60 ? 'yellow' : 'red'}
              variant="solid"
              borderRadius="full"
              px={3}
              py={1}
            >
              {healthyServices}/{totalServices} Sağlıklı
            </Badge>
            <Text fontSize="xs" color={mutedColor}>
              %{overallHealth.toFixed(1)} Sistem Sağlığı
            </Text>
          </VStack>
        </HStack>
      </CardHeader>

      <CardBody pt={0}>
        <VStack spacing={4} align="stretch">
          {/* Overall Health Progress */}
          <Box>
            <HStack justify="space-between" mb={2}>
              <Text fontSize="sm" fontWeight="medium" color={textColor}>
                Genel Sistem Sağlığı
              </Text>
              <Text fontSize="sm" color={mutedColor}>
                %{overallHealth.toFixed(1)}
              </Text>
            </HStack>
            <Progress
              value={overallHealth}
              colorScheme={overallHealth >= 80 ? 'green' : overallHealth >= 60 ? 'yellow' : 'red'}
              size="md"
              borderRadius="full"
              bg={useColorModeValue('gray.100', 'gray.700')}
            />
          </Box>

          <Divider />

          {/* Services Grid */}
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
            <AnimatePresence>
              {services.map((service, index) => (
                <MotionBox
                  key={service.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card
                    bg={getServiceBg(service.status)}
                    borderRadius="lg"
                    border="1px solid"
                    borderColor={`${getStatusColor(service.status)}.200`}
                    overflow="hidden"
                    _hover={{ transform: 'translateY(-2px)', transition: 'all 0.2s' }}
                  >
                    <CardBody p={4}>
                      <VStack spacing={3} align="stretch">
                        {/* Service Header */}
                        <HStack justify="space-between" align="start">
                          <HStack spacing={3}>
                            <Icon
                              as={getServiceIcon(service.name)}
                              color={`${getStatusColor(service.status)}.500`}
                              boxSize={5}
                            />
                            <VStack align="start" spacing={0}>
                              <Text fontSize="sm" fontWeight="bold" color={textColor}>
                                {service.name}
                              </Text>
                              <Text fontSize="xs" color={mutedColor}>
                                {service.description}
                              </Text>
                            </VStack>
                          </HStack>

                          <Badge
                            colorScheme={getStatusColor(service.status)}
                            variant="solid"
                            borderRadius="full"
                            display="flex"
                            alignItems="center"
                            gap={1}
                          >
                            <Icon as={getStatusIcon(service.status)} boxSize={2} />
                            {getStatusText(service.status)}
                          </Badge>
                        </HStack>

                        {/* Service Metrics */}
                        <SimpleGrid columns={3} spacing={2}>
                          <Tooltip label="Sistem çalışma süresi">
                            <VStack spacing={0}>
                              <Text fontSize="xs" color={mutedColor}>Uptime</Text>
                              <Text fontSize="sm" fontWeight="bold" color={textColor}>
                                %{service.uptime.toFixed(1)}
                              </Text>
                            </VStack>
                          </Tooltip>

                          <Tooltip label="Yanıt süresi">
                            <VStack spacing={0}>
                              <Text fontSize="xs" color={mutedColor}>Yanıt</Text>
                              <Text fontSize="sm" fontWeight="bold" color={textColor}>
                                {service.responseTime}ms
                              </Text>
                            </VStack>
                          </Tooltip>

                          <Tooltip label="Son kontrol zamanı">
                            <VStack spacing={0}>
                              <Text fontSize="xs" color={mutedColor}>Son Kontrol</Text>
                              <Text fontSize="sm" fontWeight="bold" color={textColor}>
                                {service.lastChecked.toLocaleTimeString('tr-TR', { 
                                  hour: '2-digit', 
                                  minute: '2-digit' 
                                })}
                              </Text>
                            </VStack>
                          </Tooltip>
                        </SimpleGrid>

                        {/* Uptime Progress Bar */}
                        <Box>
                          <Progress
                            value={service.uptime}
                            colorScheme={getStatusColor(service.status)}
                            size="sm"
                            borderRadius="full"
                          />
                        </Box>

                        {/* Additional Details */}
                        {service.details && (
                          <Box
                            bg={useColorModeValue('gray.50', 'gray.700')}
                            p={2}
                            borderRadius="md"
                          >
                            <Text fontSize="xs" color={mutedColor}>
                              {service.details}
                            </Text>
                          </Box>
                        )}

                        {/* Endpoint Info */}
                        {service.endpoint && (
                          <HStack spacing={2}>
                            <Icon as={FaGlobe} color={mutedColor} boxSize={3} />
                            <Text fontSize="xs" color={mutedColor} isTruncated>
                              {service.endpoint}
                            </Text>
                          </HStack>
                        )}
                      </VStack>
                    </CardBody>
                  </Card>
                </MotionBox>
              ))}
            </AnimatePresence>
          </SimpleGrid>

          {/* Summary Stats */}
          <Box
            bg={useColorModeValue('gray.50', 'gray.700')}
            p={4}
            borderRadius="lg"
          >
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
              <VStack spacing={1}>
                <Text fontSize="xs" color={mutedColor}>Sağlıklı Servisler</Text>
                <Text fontSize="lg" fontWeight="bold" color="green.500">
                  {services.filter(s => s.status === 'healthy').length}
                </Text>
              </VStack>

              <VStack spacing={1}>
                <Text fontSize="xs" color={mutedColor}>Uyarı Durumunda</Text>
                <Text fontSize="lg" fontWeight="bold" color="yellow.500">
                  {services.filter(s => s.status === 'warning').length}
                </Text>
              </VStack>

              <VStack spacing={1}>
                <Text fontSize="xs" color={mutedColor}>Hatalı Servisler</Text>
                <Text fontSize="lg" fontWeight="bold" color="red.500">
                  {services.filter(s => s.status === 'error').length}
                </Text>
              </VStack>

              <VStack spacing={1}>
                <Text fontSize="xs" color={mutedColor}>Ortalama Yanıt</Text>
                <Text fontSize="lg" fontWeight="bold" color={textColor}>
                  {Math.round(services.reduce((sum, s) => sum + s.responseTime, 0) / services.length)}ms
                </Text>
              </VStack>
            </SimpleGrid>
          </Box>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default RealTimeServiceStatus;
