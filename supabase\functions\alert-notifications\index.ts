/**
 * Alert Notifications Edge Function
 * Phase 3.3: Monitoring & Alerting Systems
 * Sends notifications for monitoring alerts via multiple channels
 */

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Types
interface AlertNotification {
  id: string;
  type: string;
  severity: 'info' | 'warning' | 'critical';
  message: string;
  data: any;
  timestamp: string;
  channels: NotificationChannel[];
}

interface NotificationChannel {
  type: 'email' | 'slack' | 'webhook' | 'sms';
  config: Record<string, any>;
  enabled: boolean;
}

interface NotificationRequest {
  alertId?: string;
  alert?: AlertNotification;
  channels?: string[];
  immediate?: boolean;
}

// Configuration
const NOTIFICATION_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000,
  batchSize: 10,
  rateLimits: {
    email: { count: 100, window: 3600 }, // 100 emails per hour
    slack: { count: 1000, window: 3600 }, // 1000 slack messages per hour
    sms: { count: 50, window: 3600 }, // 50 SMS per hour
    webhook: { count: 500, window: 3600 } // 500 webhook calls per hour
  }
};

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Rate limiting store (in-memory for simplicity)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Utility functions
const generateId = () => `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const checkRateLimit = (channel: string, type: string): boolean => {
  const key = `${channel}_${type}`;
  const limit = NOTIFICATION_CONFIG.rateLimits[type as keyof typeof NOTIFICATION_CONFIG.rateLimits];
  const now = Date.now();
  
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + (limit.window * 1000) });
    return true;
  }
  
  if (current.count >= limit.count) {
    return false;
  }
  
  current.count++;
  return true;
};

// Email notification
const sendEmailNotification = async (alert: AlertNotification, config: any) => {
  // This would integrate with your email service (SendGrid, AWS SES, etc.)
  console.log('Sending email notification:', {
    to: config.recipients,
    subject: `[${alert.severity.toUpperCase()}] ${alert.type} Alert`,
    body: alert.message,
    alert: alert.id
  });

  // Simulate email sending
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return { success: true, channel: 'email', messageId: generateId() };
};

// Slack notification
const sendSlackNotification = async (alert: AlertNotification, config: any) => {
  if (!config.webhookUrl) {
    throw new Error('Slack webhook URL not configured');
  }

  const color = {
    info: '#36a64f',
    warning: '#ff9500',
    critical: '#ff0000'
  }[alert.severity];

  const payload = {
    text: `Alert: ${alert.message}`,
    attachments: [
      {
        color,
        fields: [
          {
            title: 'Alert Type',
            value: alert.type,
            short: true
          },
          {
            title: 'Severity',
            value: alert.severity.toUpperCase(),
            short: true
          },
          {
            title: 'Timestamp',
            value: new Date(alert.timestamp).toLocaleString(),
            short: true
          },
          {
            title: 'Alert ID',
            value: alert.id,
            short: true
          }
        ]
      }
    ]
  };

  const response = await fetch(config.webhookUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    throw new Error(`Slack API error: ${response.status} ${response.statusText}`);
  }

  return { success: true, channel: 'slack', messageId: generateId() };
};

// Webhook notification
const sendWebhookNotification = async (alert: AlertNotification, config: any) => {
  if (!config.url) {
    throw new Error('Webhook URL not configured');
  }

  const payload = {
    alert: {
      id: alert.id,
      type: alert.type,
      severity: alert.severity,
      message: alert.message,
      timestamp: alert.timestamp,
      data: alert.data
    },
    timestamp: new Date().toISOString()
  };

  const response = await fetch(config.url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'Algobir-Monitoring/1.0',
      ...(config.headers || {})
    },
    body: JSON.stringify(payload)
  });

  if (!response.ok) {
    throw new Error(`Webhook error: ${response.status} ${response.statusText}`);
  }

  return { success: true, channel: 'webhook', messageId: generateId() };
};

// SMS notification (placeholder)
const sendSMSNotification = async (alert: AlertNotification, config: any) => {
  // This would integrate with SMS service (Twilio, AWS SNS, etc.)
  console.log('Sending SMS notification:', {
    to: config.phoneNumbers,
    message: `[${alert.severity.toUpperCase()}] ${alert.message}`,
    alert: alert.id
  });

  // Simulate SMS sending
  await new Promise(resolve => setTimeout(resolve, 200));
  
  return { success: true, channel: 'sms', messageId: generateId() };
};

// Send notification with retry logic
const sendNotificationWithRetry = async (
  alert: AlertNotification, 
  channel: NotificationChannel, 
  retryCount = 0
): Promise<any> => {
  try {
    // Check rate limit
    if (!checkRateLimit(channel.type, channel.type)) {
      throw new Error(`Rate limit exceeded for ${channel.type}`);
    }

    let result;
    
    switch (channel.type) {
      case 'email':
        result = await sendEmailNotification(alert, channel.config);
        break;
      case 'slack':
        result = await sendSlackNotification(alert, channel.config);
        break;
      case 'webhook':
        result = await sendWebhookNotification(alert, channel.config);
        break;
      case 'sms':
        result = await sendSMSNotification(alert, channel.config);
        break;
      default:
        throw new Error(`Unsupported notification channel: ${channel.type}`);
    }

    return result;
  } catch (error) {
    if (retryCount < NOTIFICATION_CONFIG.maxRetries) {
      console.warn(`Notification failed, retrying (${retryCount + 1}/${NOTIFICATION_CONFIG.maxRetries}):`, error.message);
      
      await new Promise(resolve => 
        setTimeout(resolve, NOTIFICATION_CONFIG.retryDelay * Math.pow(2, retryCount))
      );
      
      return sendNotificationWithRetry(alert, channel, retryCount + 1);
    }
    
    throw error;
  }
};

// Get notification channels for alert
const getNotificationChannels = async (alert: AlertNotification): Promise<NotificationChannel[]> => {
  // This would typically come from user preferences or configuration
  // For now, return default channels based on severity
  
  const defaultChannels: NotificationChannel[] = [];
  
  // Always send to webhook for logging
  defaultChannels.push({
    type: 'webhook',
    config: {
      url: Deno.env.get('MONITORING_WEBHOOK_URL') || 'https://httpbin.org/post'
    },
    enabled: true
  });

  // Send Slack notifications for warnings and critical alerts
  if (alert.severity !== 'info' && Deno.env.get('SLACK_WEBHOOK_URL')) {
    defaultChannels.push({
      type: 'slack',
      config: {
        webhookUrl: Deno.env.get('SLACK_WEBHOOK_URL')
      },
      enabled: true
    });
  }

  // Send email for critical alerts
  if (alert.severity === 'critical' && Deno.env.get('ALERT_EMAIL_RECIPIENTS')) {
    defaultChannels.push({
      type: 'email',
      config: {
        recipients: Deno.env.get('ALERT_EMAIL_RECIPIENTS')?.split(',') || []
      },
      enabled: true
    });
  }

  return defaultChannels.filter(channel => channel.enabled);
};

// Process notification request
const processNotificationRequest = async (request: NotificationRequest) => {
  let alert: AlertNotification;

  if (request.alertId) {
    // Fetch alert from database
    const { data, error } = await supabase
      .from('monitoring_alerts')
      .select('*')
      .eq('id', request.alertId)
      .single();

    if (error || !data) {
      throw new Error(`Alert not found: ${request.alertId}`);
    }

    alert = {
      id: data.id,
      type: data.type,
      severity: data.severity,
      message: data.message,
      data: data.data,
      timestamp: data.timestamp,
      channels: []
    };
  } else if (request.alert) {
    alert = request.alert;
  } else {
    throw new Error('Either alertId or alert must be provided');
  }

  // Get notification channels
  const channels = await getNotificationChannels(alert);
  
  if (channels.length === 0) {
    console.warn('No notification channels configured for alert:', alert.id);
    return { sent: 0, failed: 0, results: [] };
  }

  // Send notifications
  const results = [];
  let sent = 0;
  let failed = 0;

  for (const channel of channels) {
    try {
      const result = await sendNotificationWithRetry(alert, channel);
      results.push({ channel: channel.type, success: true, ...result });
      sent++;
    } catch (error) {
      console.error(`Failed to send ${channel.type} notification:`, error);
      results.push({ 
        channel: channel.type, 
        success: false, 
        error: error.message 
      });
      failed++;
    }
  }

  // Log notification attempt
  await supabase
    .from('notification_logs')
    .insert({
      id: generateId(),
      alert_id: alert.id,
      channels_attempted: channels.length,
      channels_successful: sent,
      channels_failed: failed,
      results: results,
      timestamp: new Date().toISOString()
    })
    .catch(console.error);

  return { sent, failed, results };
};

// Main handler
serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    const request: NotificationRequest = await req.json();

    // Validate request
    if (!request.alertId && !request.alert) {
      return new Response(
        JSON.stringify({ error: 'Either alertId or alert must be provided' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const result = await processNotificationRequest(request);

    return new Response(
      JSON.stringify({
        success: true,
        ...result,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Alert notification error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
