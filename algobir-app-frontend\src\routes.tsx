import React from 'react';
import { RouteObject, Navigate } from 'react-router-dom';
import { Icon } from '@chakra-ui/react';
import { FiHome, FiTrendingUp, FiBarChart2, FiSettings, FiBook, FiUser, FiShield, FiBell, FiPieChart, FiFileText, FiCreditCard, FiKey, FiUsers, FiDatabase, FiMonitor, FiTarget, FiBarChart, FiClock, FiGitBranch } from 'react-icons/fi';
import { RiRobotFill, RiExchangeFundsFill } from 'react-icons/ri';
import { lazy } from 'react';
import Layout from './components/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';

// Lazy loaded components
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Trades = lazy(() => import('./pages/Trades'));
const Statistics = lazy(() => import('./pages/Statistics'));
const Management = lazy(() => import('./pages/Management'));
const Guide = lazy(() => import('./pages/guide/GuidePage'));
const SoloRobotGuide = lazy(() => import('./pages/guide/SoloRobotGuide'));
const BroRobotGuide = lazy(() => import('./pages/guide/BroRobotGuide'));
const FAQPage = lazy(() => import('./pages/guide/FAQPage'));
const Profile = lazy(() => import('./pages/Profile'));
const AuthPage = lazy(() => import('./pages/AuthPage'));
const OpenPositions = lazy(() => import('./pages/OpenPositions'));
const Marketplace = lazy(() => import('./pages/marketplace/MarketplacePage'));
const RobotDetails = lazy(() => import('./pages/RobotDetails'));
const RobotManagementPage = lazy(() => import('./pages/seller/RobotManagementPage'));
const UserPage = lazy(() => import('./pages/user/UserPage'));
const NotificationsPage = lazy(() => import('./pages/Notifications'));

// Statistics sub-pages for nested routing
const OverviewPage = lazy(() => import('./pages/statistics/OverviewPage'));
const ROIAnalysisPage = lazy(() => import('./pages/statistics/ROIAnalysisPage'));
const SoloRobotPage = lazy(() => import('./pages/statistics/SoloRobotPage'));
const BroRobotsPage = lazy(() => import('./pages/statistics/BroRobotsPage'));
const RobotComparisonPage = lazy(() => import('./pages/statistics/RobotComparisonPage'));
const PerformancePage = lazy(() => import('./pages/statistics/PerformancePage'));
const RiskAnalysisPage = lazy(() => import('./pages/statistics/RiskAnalysisPage'));
const TimeAnalysisPage = lazy(() => import('./pages/statistics/TimeAnalysisPage'));
const SymbolAnalysisPage = lazy(() => import('./pages/statistics/SymbolAnalysisPage'));
const ReportsPage = lazy(() => import('./pages/statistics/ReportsPage'));
const StatisticsRedirect = lazy(() => import('./components/statistics/StatisticsRedirect'));
const TestAuth = lazy(() => import('./pages/TestAuth'));
const SystemMonitoringValidation = lazy(() => import('./pages/test/SystemMonitoringValidation'));

// Admin pages
const UserManagement = lazy(() => import('./pages/admin/UserManagement'));
const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard'));
const AllTradesView = lazy(() => import('./pages/admin/AllTradesView'));
const NotificationManagement = lazy(() => import('./pages/admin/NotificationManagement'));
const CreateAnnouncement = lazy(() => import('./pages/admin/CreateAnnouncement'));
const AdminStatistics = lazy(() => import('./pages/admin/AdminStatistics'));
const AdminStatus = lazy(() => import('./pages/admin/AdminStatus'));

// Define routes configuration for createBrowserRouter
export const routes: RouteObject[] = [
  // Auth route (standalone)
  {
    path: '/login',
    element: <AuthPage />,
  },
  
  // Main application routes with Layout
  {
    path: '/',
    element: <Layout />,
    children: [
      // Public routes (no authentication required)
      {
        path: 'user/:username',
        element: <UserPage />,
      },
      {
        path: 'marketplace/robot/:robotId',
        element: <RobotDetails />,
      },
      
      // Protected routes
      {
        index: true,
        element: <ProtectedRoute><Dashboard /></ProtectedRoute>,
      },
      {
        path: 'open-positions',
        element: <ProtectedRoute><OpenPositions /></ProtectedRoute>,
      },
      {
        path: 'trades',
        element: <ProtectedRoute><Trades /></ProtectedRoute>,
      },
      {
        path: 'statistics',
        element: <ProtectedRoute><Statistics /></ProtectedRoute>,
        children: [
          {
            index: true,
            element: <StatisticsRedirect />,
          },
          {
            path: 'overview',
            element: <OverviewPage />,
          },
          {
            path: 'roi-analysis',
            element: <ROIAnalysisPage />,
          },
          {
            path: 'solo-robot',
            element: <SoloRobotPage />,
          },
          {
            path: 'bro-robots',
            element: <BroRobotsPage />,
          },
          {
            path: 'robot-comparison',
            element: <RobotComparisonPage />,
          },
          {
            path: 'performance',
            element: <PerformancePage />,
          },
          {
            path: 'risk-analysis',
            element: <RiskAnalysisPage />,
          },
          {
            path: 'time-analysis',
            element: <TimeAnalysisPage />,
          },
          {
            path: 'symbol-analysis',
            element: <SymbolAnalysisPage />,
          },
          {
            path: 'reports',
            element: <ReportsPage />,
          },
        ],
      },
      {
        path: 'marketplace',
        element: <ProtectedRoute><Marketplace /></ProtectedRoute>,
      },
      {
        path: 'management',
        element: <ProtectedRoute><Management /></ProtectedRoute>,
      },
      {
        path: 'guide',
        element: <ProtectedRoute><Guide /></ProtectedRoute>,
      },
      {
        path: 'guide/solo-robot',
        element: <ProtectedRoute><SoloRobotGuide /></ProtectedRoute>,
      },
      {
        path: 'guide/bro-robot',
        element: <ProtectedRoute><BroRobotGuide /></ProtectedRoute>,
      },
      {
        path: 'guide/faq',
        element: <ProtectedRoute><FAQPage /></ProtectedRoute>,
      },
      {
        path: 'profile',
        element: <ProtectedRoute><Profile /></ProtectedRoute>,
      },
      {
        path: 'seller/robots',
        element: <ProtectedRoute><RobotManagementPage /></ProtectedRoute>,
      },
      {
        path: 'notifications',
        element: <ProtectedRoute><NotificationsPage /></ProtectedRoute>,
      },
      {
        path: 'test-auth',
        element: <TestAuth />,
      },
      {
        path: 'test/system-monitoring',
        element: <SystemMonitoringValidation />,
      },
    ],
  },

  // Admin routes
  {
    path: '/admin',
    element: <AdminRoute><Layout /></AdminRoute>,
    children: [
      {
        index: true,
        element: <AdminDashboard />,
      },
      {
        path: 'users',
        element: <UserManagement />,
      },
      {
        path: 'trades',
        element: <AllTradesView />,
      },
      {
        path: 'notifications',
        element: <NotificationManagement />,
      },
      {
        path: 'notifications/create',
        element: <CreateAnnouncement />,
      },
      {
        path: 'statistics',
        element: <AdminStatistics />,
      },
      {
        path: 'status',
        element: <AdminStatus />,
      },
    ],
  },
  
  // Catch-all redirect
  {
    path: '*',
    element: <Navigate to="/" />,
  },
];

// Enhanced interface for sidebar compatibility with submenu support
export interface RouteType {
  name: string;
  layout: string;
  path: string;
  icon: React.ReactElement;
  secondary?: boolean;
  children?: SubmenuRouteType[];
  hasSubmenu?: boolean;
}

// Submenu route interface
export interface SubmenuRouteType {
  id: string;
  name: string;
  path: string;
  icon?: React.ReactElement;
  description?: string;
}

// Legacy routes array for sidebar navigation
export const sidebarRoutes: RouteType[] = [
  {
    name: 'Dashboard',
    layout: '',
    path: '/',
    icon: <Icon as={FiHome} width="20px" height="20px" color="inherit" />,
  },
  {
    name: 'Açık İşlemler',
    layout: '',
    path: '/open-positions',
    icon: <Icon as={RiExchangeFundsFill} width="20px" height="20px" color="inherit" />,
  },
  {
    name: 'İşlemler',
    layout: '',
    path: '/trades',
    icon: <Icon as={FiTrendingUp} width="20px" height="20px" color="inherit" />,
  },
  {
    name: 'İstatistikler',
    layout: '',
    path: '/statistics',
    icon: <Icon as={FiBarChart2} width="20px" height="20px" color="inherit" />,
    hasSubmenu: true,
    children: [
      {
        id: 'overview',
        name: 'Genel Bakış',
        path: '/statistics/overview',
        icon: <Icon as={FiTrendingUp} width="16px" height="16px" color="inherit" />,
        description: 'Performans özeti ve temel metrikler'
      },
      {
        id: 'roi-analysis',
        name: 'ROI Analizi',
        path: '/statistics/roi-analysis',
        icon: <Icon as={FiTarget} width="16px" height="16px" color="inherit" />,
        description: 'Yatırım getirisi ve karlılık analizi'
      },
      {
        id: 'performance',
        name: 'Performans Analizi',
        path: '/statistics/performance',
        icon: <Icon as={FiBarChart} width="16px" height="16px" color="inherit" />,
        description: 'Detaylı performans metrikleri'
      },
      {
        id: 'solo-robot',
        name: 'Solo-Robot Analizi',
        path: '/statistics/solo-robot',
        icon: <Icon as={FiUser} width="16px" height="16px" color="inherit" />,
        description: 'Solo-Robot performans analizi'
      },
      {
        id: 'bro-robots',
        name: 'Bro-Robot Analizi',
        path: '/statistics/bro-robots',
        icon: <Icon as={FiUsers} width="16px" height="16px" color="inherit" />,
        description: 'Bro-Robot performans analizi'
      },
      {
        id: 'robot-comparison',
        name: 'Robot Karşılaştırması',
        path: '/statistics/robot-comparison',
        icon: <Icon as={FiGitBranch} width="16px" height="16px" color="inherit" />,
        description: 'Robot performans karşılaştırması'
      },
      {
        id: 'risk-analysis',
        name: 'Risk Analizi',
        path: '/statistics/risk-analysis',
        icon: <Icon as={FiShield} width="16px" height="16px" color="inherit" />,
        description: 'Risk metrikleri ve güvenlik analizi'
      },
      {
        id: 'time-analysis',
        name: 'Zaman Analizi',
        path: '/statistics/time-analysis',
        icon: <Icon as={FiClock} width="16px" height="16px" color="inherit" />,
        description: 'Zamansal performans analizi'
      },
      {
        id: 'symbol-analysis',
        name: 'Sembol Analizi',
        path: '/statistics/symbol-analysis',
        icon: <Icon as={FiPieChart} width="16px" height="16px" color="inherit" />,
        description: 'Sembol bazlı performans analizi'
      },
      {
        id: 'reports',
        name: 'Raporlar',
        path: '/statistics/reports',
        icon: <Icon as={FiFileText} width="16px" height="16px" color="inherit" />,
        description: 'Detaylı raporlar ve dışa aktarma'
      }
    ]
  },
  {
    name: 'Robot Pazarı',
    layout: '',
    path: '/marketplace',
    icon: <Icon as={RiRobotFill} width="20px" height="20px" color="inherit" />,
  },
  {
    name: 'Robotlarım',
    layout: '',
    path: '/seller/robots',
    icon: <Icon as={RiRobotFill} width="20px" height="20px" color="inherit" />,
  },
  {
    name: 'Yönetim',
    layout: '',
    path: '/management',
    icon: <Icon as={FiSettings} width="20px" height="20px" color="inherit" />,
    hasSubmenu: true,
    children: [
      {
        id: 'subscription',
        name: 'Abonelik Yönetimi',
        path: '/management?tab=subscription',
        icon: <Icon as={FiCreditCard} width="16px" height="16px" color="inherit" />,
        description: 'Robot abonelikleri ve ödemeler'
      },
      {
        id: 'api-keys',
        name: 'API Anahtarları',
        path: '/management?tab=api-keys',
        icon: <Icon as={FiKey} width="16px" height="16px" color="inherit" />,
        description: 'Borsa API anahtarı yönetimi'
      }
    ]
  },
  {
    name: 'Rehber',
    layout: '',
    path: '/guide',
    icon: <Icon as={FiBook} width="20px" height="20px" color="inherit" />,
  },
  {
    name: 'Profil',
    layout: '',
    path: '/profile',
    icon: <Icon as={FiUser} width="20px" height="20px" color="inherit" />,
  },
  {
    name: 'Bildirimler',
    layout: '',
    path: '/notifications',
    icon: <Icon as={FiBell} width="20px" height="20px" color="inherit" />,
  },
  // Admin paneli, koşullu olarak gösterilecek
  {
    name: 'Admin Paneli',
    layout: '',
    path: '/admin',
    icon: <Icon as={FiShield} width="20px" height="20px" color="inherit" />,
    secondary: true,
    hasSubmenu: true,
    children: [
      {
        id: 'admin-dashboard',
        name: 'Ana Sayfa',
        path: '/admin',
        icon: <Icon as={FiHome} width="16px" height="16px" color="inherit" />,
        description: 'Admin ana sayfası'
      },
      {
        id: 'admin-users',
        name: 'Kullanıcı Yönetimi',
        path: '/admin/users',
        icon: <Icon as={FiUsers} width="16px" height="16px" color="inherit" />,
        description: 'Kullanıcı hesapları ve yetkileri'
      },
      {
        id: 'admin-trades',
        name: 'İşlem Yönetimi',
        path: '/admin/trades',
        icon: <Icon as={FiTrendingUp} width="16px" height="16px" color="inherit" />,
        description: 'Tüm kullanıcı işlemleri'
      },
      {
        id: 'admin-notifications',
        name: 'Bildirim Yönetimi',
        path: '/admin/notifications',
        icon: <Icon as={FiBell} width="16px" height="16px" color="inherit" />,
        description: 'Sistem bildirimleri ve duyurular'
      },
      {
        id: 'admin-statistics',
        name: 'Sistem İstatistikleri',
        path: '/admin/statistics',
        icon: <Icon as={FiDatabase} width="16px" height="16px" color="inherit" />,
        description: 'Platform geneli istatistikler'
      },
      {
        id: 'admin-status',
        name: 'Sistem Durumu',
        path: '/admin/status',
        icon: <Icon as={FiMonitor} width="16px" height="16px" color="inherit" />,
        description: 'Sistem sağlığı ve performans'
      }
    ]
  },
];

export default sidebarRoutes; 