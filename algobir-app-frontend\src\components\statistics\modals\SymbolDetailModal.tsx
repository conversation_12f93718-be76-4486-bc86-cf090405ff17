import React, { useMemo } from 'react';
import {
  <PERSON>dal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  Flex,
  Text,
  Box,
  SimpleGrid,
  Card,
  CardBody,
  Badge,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  useColorModeValue,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Alert,
  AlertIcon,
  Tooltip,
  IconButton
} from '@chakra-ui/react';
import { DownloadIcon, StarIcon } from '@chakra-ui/icons';
import { 
  SymbolMetrics, 
  SymbolTimePerformance, 
  SymbolTradingFrequency 
} from '../../../types/symbolAnalysis';
import { AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer } from 'recharts';

interface SymbolDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  symbol: string;
  symbolData?: SymbolMetrics;
  timePerformance?: SymbolTimePerformance;
  tradingFrequency?: SymbolTradingFrequency;
  onAddToWatchlist?: (symbol: string) => void;
  onExportData?: (symbol: string) => void;
}

const SymbolDetailModal: React.FC<SymbolDetailModalProps> = ({
  isOpen,
  onClose,
  symbol,
  symbolData,
  timePerformance,

  onAddToWatchlist,
  onExportData
}) => {
  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');


  // Calculate additional metrics
  const additionalMetrics = useMemo(() => {
    if (!symbolData) return null;

    const riskScore = calculateRiskScore(symbolData);
    const performanceGrade = getPerformanceGrade(symbolData);
    const recommendation = getRecommendation(symbolData);

    return {
      riskScore,
      performanceGrade,
      recommendation
    };
  }, [symbolData]);

  // Calculate risk score (0-10)
  const calculateRiskScore = (data: SymbolMetrics): number => {
    const volatilityScore = Math.min(data.volatility / 10, 10);
    const drawdownScore = Math.min(data.maxDrawdownPercent / 10, 10);
    const sharpeScore = data.sharpeRatio < 0 ? 10 : Math.max(0, 10 - data.sharpeRatio * 2);
    const winRateScore = Math.max(0, 10 - (data.winRate / 10));
    
    return (volatilityScore + drawdownScore + sharpeScore + winRateScore) / 4;
  };

  // Get performance grade
  const getPerformanceGrade = (data: SymbolMetrics): { grade: string; color: string } => {
    const score = (data.roi / 100) + (data.winRate / 100) + Math.min(data.sharpeRatio / 2, 1);
    
    if (score >= 2) return { grade: 'A+', color: 'green' };
    if (score >= 1.5) return { grade: 'A', color: 'green' };
    if (score >= 1) return { grade: 'B+', color: 'blue' };
    if (score >= 0.5) return { grade: 'B', color: 'blue' };
    if (score >= 0) return { grade: 'C', color: 'orange' };
    return { grade: 'D', color: 'red' };
  };

  // Get recommendation
  const getRecommendation = (data: SymbolMetrics): { text: string; color: string; action: string } => {
    const riskScore = calculateRiskScore(data);
    const isProfit = data.totalPnl > 0;
    const highWinRate = data.winRate > 60;
    const goodSharpe = data.sharpeRatio > 1;

    if (isProfit && highWinRate && goodSharpe && riskScore < 5) {
      return { text: 'Güçlü Alım', color: 'green', action: 'BUY' };
    } else if (isProfit && (highWinRate || goodSharpe)) {
      return { text: 'Alım', color: 'blue', action: 'BUY' };
    } else if (!isProfit && riskScore > 7) {
      return { text: 'Satım', color: 'red', action: 'SELL' };
    } else if (riskScore > 6) {
      return { text: 'Dikkatli İzle', color: 'orange', action: 'WATCH' };
    } else {
      return { text: 'Nötr', color: 'gray', action: 'HOLD' };
    }
  };

  // Format currency
  const formatCurrency = (value: number) => `₺${value.toFixed(2)}`;
  const formatPercentage = (value: number) => `${value.toFixed(2)}%`;

  // Prepare chart data
  const hourlyChartData = timePerformance?.hourlyPerformance.map(item => ({
    hour: `${item.hour}:00`,
    pnl: item.pnl,
    trades: item.trades,
    winRate: item.winRate
  })) || [];

  const dailyChartData = timePerformance?.dailyPerformance.map(item => ({
    day: item.dayName,
    pnl: item.pnl,
    trades: item.trades,
    winRate: item.winRate
  })) || [];



  if (!symbolData) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Sembol Detayları</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Alert status="warning">
              <AlertIcon />
              <Text>Seçilen sembol için detaylı veri bulunamadı.</Text>
            </Alert>
          </ModalBody>
          <ModalFooter>
            <Button onClick={onClose}>Kapat</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} size={{ base: "full", md: "6xl" }}>
      <ModalOverlay />
      <ModalContent
        maxW={{ base: "100vw", md: "90vw" }}
        maxH={{ base: "100vh", md: "90vh" }}
        m={{ base: 0, md: 4 }}
        borderRadius={{ base: 0, md: "md" }}
      >
        <ModalHeader>
          <VStack spacing={3} align="stretch">
            <Flex
              direction={{ base: "column", md: "row" }}
              justify="space-between"
              align={{ base: "start", md: "center" }}
              gap={3}
            >
              <HStack wrap="wrap" spacing={3}>
                <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold">{symbol}</Text>
                {additionalMetrics && (
                  <Badge
                    colorScheme={additionalMetrics.performanceGrade.color}
                    variant="solid"
                    fontSize={{ base: "sm", md: "md" }}
                    px={3}
                    py={1}
                  >
                    {additionalMetrics.performanceGrade.grade}
                  </Badge>
                )}
              </HStack>
              <HStack spacing={2}>
                <Tooltip label="İzleme listesine ekle">
                  <IconButton
                    aria-label="Add to watchlist"
                    icon={<StarIcon />}
                    size="sm"
                    variant="outline"
                    onClick={() => onAddToWatchlist?.(symbol)}
                  />
                </Tooltip>
                <Tooltip label="Verileri dışa aktar">
                  <IconButton
                    aria-label="Export data"
                    icon={<DownloadIcon />}
                    size="sm"
                    variant="outline"
                    onClick={() => onExportData?.(symbol)}
                  />
                </Tooltip>
              </HStack>
            </Flex>
          </VStack>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody overflowY="auto">
          <VStack spacing={6} align="stretch">
            {/* Key Metrics Overview */}
            <SimpleGrid columns={{ base: 1, sm: 2, md: 3, lg: 6 }} spacing={{ base: 3, md: 4 }}>
              <Stat>
                <StatLabel>Toplam P&L</StatLabel>
                <StatNumber color={symbolData.totalPnl > 0 ? 'green.500' : 'red.500'}>
                  {formatCurrency(symbolData.totalPnl)}
                </StatNumber>
                <StatHelpText>
                  <StatArrow type={symbolData.totalPnl > 0 ? 'increase' : 'decrease'} />
                  {symbolData.totalTrades} işlem
                </StatHelpText>
              </Stat>

              <Stat>
                <StatLabel>ROI</StatLabel>
                <StatNumber color={symbolData.roi > 0 ? 'green.500' : 'red.500'}>
                  {formatPercentage(symbolData.roi)}
                </StatNumber>
                <StatHelpText>Yatırım getirisi</StatHelpText>
              </Stat>

              <Stat>
                <StatLabel>Kazanma Oranı</StatLabel>
                <StatNumber>{formatPercentage(symbolData.winRate)}</StatNumber>
                <StatHelpText>
                  {symbolData.winningTrades}/{symbolData.totalTrades}
                </StatHelpText>
              </Stat>

              <Stat>
                <StatLabel>Sharpe Oranı</StatLabel>
                <StatNumber color={symbolData.sharpeRatio > 1 ? 'green.500' : symbolData.sharpeRatio > 0 ? 'blue.500' : 'red.500'}>
                  {symbolData.sharpeRatio.toFixed(3)}
                </StatNumber>
                <StatHelpText>Risk ayarlı getiri</StatHelpText>
              </Stat>

              <Stat>
                <StatLabel>Volatilite</StatLabel>
                <StatNumber>{symbolData.volatility.toFixed(2)}</StatNumber>
                <StatHelpText>Fiyat oynaklığı</StatHelpText>
              </Stat>

              <Stat>
                <StatLabel>Max Drawdown</StatLabel>
                <StatNumber color="red.500">
                  {formatPercentage(symbolData.maxDrawdownPercent)}
                </StatNumber>
                <StatHelpText>{formatCurrency(symbolData.maxDrawdown)}</StatHelpText>
              </Stat>
            </SimpleGrid>

            {/* Risk Assessment & Recommendation */}
            {additionalMetrics && (
              <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4}>
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <VStack align="start" spacing={3}>
                      <Text fontSize="md" fontWeight="bold">Risk Değerlendirmesi</Text>
                      <Box w="100%">
                        <HStack justify="space-between" mb={2}>
                          <Text fontSize="sm">Risk Skoru</Text>
                          <Text fontSize="sm" fontWeight="bold">
                            {additionalMetrics.riskScore.toFixed(1)}/10
                          </Text>
                        </HStack>
                        <Progress 
                          value={additionalMetrics.riskScore * 10} 
                          colorScheme={
                            additionalMetrics.riskScore < 3 ? 'green' :
                            additionalMetrics.riskScore < 7 ? 'yellow' : 'red'
                          }
                          size="lg"
                        />
                        <Text fontSize="xs" color="gray.500" mt={1}>
                          {additionalMetrics.riskScore < 3 ? 'Düşük Risk' :
                           additionalMetrics.riskScore < 7 ? 'Orta Risk' : 'Yüksek Risk'}
                        </Text>
                      </Box>
                    </VStack>
                  </CardBody>
                </Card>

                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <VStack align="start" spacing={3}>
                      <Text fontSize="md" fontWeight="bold">Performans Notu</Text>
                      <HStack>
                        <Badge 
                          colorScheme={additionalMetrics.performanceGrade.color} 
                          variant="solid"
                          fontSize="2xl"
                          px={4}
                          py={2}
                        >
                          {additionalMetrics.performanceGrade.grade}
                        </Badge>
                        <VStack align="start" spacing={0}>
                          <Text fontSize="sm">Genel Performans</Text>
                          <Text fontSize="xs" color="gray.500">
                            ROI, kazanma oranı ve Sharpe bazlı
                          </Text>
                        </VStack>
                      </HStack>
                    </VStack>
                  </CardBody>
                </Card>

                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardBody>
                    <VStack align="start" spacing={3}>
                      <Text fontSize="md" fontWeight="bold">Yatırım Önerisi</Text>
                      <HStack>
                        <Badge 
                          colorScheme={additionalMetrics.recommendation.color} 
                          variant="solid"
                          fontSize="lg"
                          px={3}
                          py={1}
                        >
                          {additionalMetrics.recommendation.action}
                        </Badge>
                        <VStack align="start" spacing={0}>
                          <Text fontSize="sm" fontWeight="medium">
                            {additionalMetrics.recommendation.text}
                          </Text>
                          <Text fontSize="xs" color="gray.500">
                            Risk ve performans analizi
                          </Text>
                        </VStack>
                      </HStack>
                    </VStack>
                  </CardBody>
                </Card>
              </SimpleGrid>
            )}

            {/* Detailed Analysis Tabs */}
            <Tabs variant="enclosed" colorScheme="blue">
              <TabList>
                <Tab>Zaman Analizi</Tab>
                <Tab>Trading Detayları</Tab>
                <Tab>Risk Metrikleri</Tab>
              </TabList>

              <TabPanels>
                {/* Time Analysis Tab */}
                <TabPanel px={0}>
                  <VStack spacing={6} align="stretch">
                    {/* Hourly Performance */}
                    <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                      <CardBody>
                        <Text fontSize="md" fontWeight="bold" mb={4}>Saatlik Performans</Text>
                        <Box h="250px">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={hourlyChartData}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="hour" fontSize={12} />
                              <YAxis fontSize={12} />
                              <RechartsTooltip 
                                formatter={(value, name) => [
                                  name === 'pnl' ? formatCurrency(value as number) : value,
                                  name === 'pnl' ? 'P&L' : name === 'trades' ? 'İşlem' : 'Kazanma %'
                                ]}
                              />
                              <Bar dataKey="pnl" fill="#3182CE" />
                            </BarChart>
                          </ResponsiveContainer>
                        </Box>
                      </CardBody>
                    </Card>

                    {/* Daily Performance */}
                    <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                      <CardBody>
                        <Text fontSize="md" fontWeight="bold" mb={4}>Günlük Performans</Text>
                        <Box h="250px">
                          <ResponsiveContainer width="100%" height="100%">
                            <AreaChart data={dailyChartData}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="day" fontSize={12} />
                              <YAxis fontSize={12} />
                              <RechartsTooltip 
                                formatter={(value, name) => [
                                  name === 'pnl' ? formatCurrency(value as number) : value,
                                  name === 'pnl' ? 'P&L' : name === 'trades' ? 'İşlem' : 'Kazanma %'
                                ]}
                              />
                              <Area type="monotone" dataKey="pnl" stroke="#38A169" fill="#38A169" fillOpacity={0.6} />
                            </AreaChart>
                          </ResponsiveContainer>
                        </Box>
                      </CardBody>
                    </Card>
                  </VStack>
                </TabPanel>

                {/* Trading Details Tab */}
                <TabPanel px={0}>
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                    <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                      <CardBody>
                        <Text fontSize="md" fontWeight="bold" mb={4}>Trading İstatistikleri</Text>
                        <VStack align="stretch" spacing={3}>
                          <HStack justify="space-between">
                            <Text fontSize="sm">Ortalama İşlem Boyutu</Text>
                            <Text fontSize="sm" fontWeight="bold">{formatCurrency(symbolData.averageTradeSize)}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">Ortalama P&L</Text>
                            <Text fontSize="sm" fontWeight="bold" color={symbolData.averagePnl > 0 ? 'green.500' : 'red.500'}>
                              {formatCurrency(symbolData.averagePnl)}
                            </Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">En İyi İşlem</Text>
                            <Text fontSize="sm" fontWeight="bold" color="green.500">{formatCurrency(symbolData.bestTrade)}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">En Kötü İşlem</Text>
                            <Text fontSize="sm" fontWeight="bold" color="red.500">{formatCurrency(symbolData.worstTrade)}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">Kar Faktörü</Text>
                            <Text fontSize="sm" fontWeight="bold">{symbolData.profitFactor.toFixed(2)}</Text>
                          </HStack>
                        </VStack>
                      </CardBody>
                    </Card>

                    <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                      <CardBody>
                        <Text fontSize="md" fontWeight="bold" mb={4}>Seri İstatistikleri</Text>
                        <VStack align="stretch" spacing={3}>
                          <HStack justify="space-between">
                            <Text fontSize="sm">En Uzun Kazanma Serisi</Text>
                            <Text fontSize="sm" fontWeight="bold" color="green.500">{symbolData.winStreak}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">En Uzun Kayıp Serisi</Text>
                            <Text fontSize="sm" fontWeight="bold" color="red.500">{symbolData.lossStreak}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">Trading Günleri</Text>
                            <Text fontSize="sm" fontWeight="bold">{symbolData.tradingDays}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">Günlük Ortalama İşlem</Text>
                            <Text fontSize="sm" fontWeight="bold">{symbolData.tradesPerDay.toFixed(1)}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">İlk İşlem</Text>
                            <Text fontSize="sm">{new Date(symbolData.firstTradeDate).toLocaleDateString('tr-TR')}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">Son İşlem</Text>
                            <Text fontSize="sm">{new Date(symbolData.lastTradeDate).toLocaleDateString('tr-TR')}</Text>
                          </HStack>
                        </VStack>
                      </CardBody>
                    </Card>
                  </SimpleGrid>
                </TabPanel>

                {/* Risk Metrics Tab */}
                <TabPanel px={0}>
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                    <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                      <CardBody>
                        <Text fontSize="md" fontWeight="bold" mb={4}>Risk Metrikleri</Text>
                        <VStack align="stretch" spacing={4}>
                          <Box>
                            <HStack justify="space-between" mb={2}>
                              <Text fontSize="sm">Volatilite</Text>
                              <Text fontSize="sm" fontWeight="bold">{symbolData.volatility.toFixed(2)}</Text>
                            </HStack>
                            <Progress value={Math.min(symbolData.volatility * 2, 100)} colorScheme="orange" size="sm" />
                          </Box>
                          
                          <Box>
                            <HStack justify="space-between" mb={2}>
                              <Text fontSize="sm">Max Drawdown %</Text>
                              <Text fontSize="sm" fontWeight="bold" color="red.500">
                                {formatPercentage(symbolData.maxDrawdownPercent)}
                              </Text>
                            </HStack>
                            <Progress value={symbolData.maxDrawdownPercent} colorScheme="red" size="sm" />
                          </Box>

                          <Box>
                            <HStack justify="space-between" mb={2}>
                              <Text fontSize="sm">Sharpe Oranı</Text>
                              <Text fontSize="sm" fontWeight="bold" color={symbolData.sharpeRatio > 1 ? 'green.500' : 'orange.500'}>
                                {symbolData.sharpeRatio.toFixed(3)}
                              </Text>
                            </HStack>
                            <Progress 
                              value={Math.min(Math.max(symbolData.sharpeRatio * 25, 0), 100)} 
                              colorScheme={symbolData.sharpeRatio > 1 ? 'green' : 'orange'} 
                              size="sm" 
                            />
                          </Box>
                        </VStack>
                      </CardBody>
                    </Card>

                    <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                      <CardBody>
                        <Text fontSize="md" fontWeight="bold" mb={4}>Performans Dağılımı</Text>
                        <VStack align="stretch" spacing={3}>
                          <HStack justify="space-between">
                            <Text fontSize="sm">Ortalama Kazanç</Text>
                            <Text fontSize="sm" fontWeight="bold" color="green.500">
                              {formatCurrency(symbolData.averageWin)}
                            </Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">Ortalama Kayıp</Text>
                            <Text fontSize="sm" fontWeight="bold" color="red.500">
                              {formatCurrency(symbolData.averageLoss)}
                            </Text>
                          </HStack>
                          <Divider />
                          <HStack justify="space-between">
                            <Text fontSize="sm">Toplam Yatırım</Text>
                            <Text fontSize="sm" fontWeight="bold">{formatCurrency(symbolData.totalInvestment)}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">Kazanan İşlemler</Text>
                            <Text fontSize="sm" fontWeight="bold" color="green.500">{symbolData.winningTrades}</Text>
                          </HStack>
                          <HStack justify="space-between">
                            <Text fontSize="sm">Kaybeden İşlemler</Text>
                            <Text fontSize="sm" fontWeight="bold" color="red.500">{symbolData.losingTrades}</Text>
                          </HStack>
                        </VStack>
                      </CardBody>
                    </Card>
                  </SimpleGrid>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="outline" onClick={() => onExportData?.(symbol)}>
              Verileri Dışa Aktar
            </Button>
            <Button colorScheme="blue" onClick={onClose}>
              Kapat
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SymbolDetailModal;
