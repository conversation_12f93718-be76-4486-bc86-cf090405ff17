---
type: "manual"
priority: "high"
scope: ["frontend", "react", "ui", "components", "routing", "ux", "responsive", "accessibility"]
last_updated: "2025-01-30"
optimizations_applied: ["ux", "security", "performance", "responsive"]
---

# Frontend Application Rules - React + TypeScript + Chakra UI (Optimized)

## 🎨 UI Framework & Design System

### Chakra UI Configuration
```typescript
// Theme Configuration (src/theme/index.ts)
import { extendTheme } from '@chakra-ui/react';
import { mode } from '@chakra-ui/theme-tools';

// Horizon UI integration with custom brand colors
const theme = extendTheme({
  colors: {
    brand: {
      50: '#E6FFFA',
      500: '#38B2AC',
      900: '#234E52',
    },
    navy: {
      50: '#d0dcfb',
      900: '#1b2559',
    }
  },
  fonts: {
    heading: 'DM Sans, sans-serif',
    body: 'DM Sans, sans-serif',
  }
});
```

### Component Architecture (Enhanced with UX Optimizations)
```typescript
// Component Structure Pattern - Optimized
src/components/
├── admin/              # Admin-specific components
├── card/               # Reusable card variations
├── marketplace/        # Marketplace-specific UI
├── navbar/             # Navigation components (mobile-optimized)
│   ├── NavbarAdmin.tsx         # Enhanced responsive navbar
│   ├── MobileSearchDrawer.tsx  # NEW: Mobile search experience
│   └── NotificationDropdown.tsx
├── sidebar/            # Sidebar navigation system (touch-optimized)
├── statistics/         # Statistics dashboards
├── tables/             # Data table components
├── trades/             # Trading-specific UI
├── LoadingState.tsx    # NEW: Enhanced loading states
├── ErrorFallback.tsx   # NEW: Improved error handling
├── ErrorBoundary.tsx   # NEW: Advanced error boundaries
└── ResponsiveContainer.tsx # NEW: Mobile-first containers

// Component Naming Convention
- PascalCase for component files
- Descriptive names (e.g., RobotPerformanceCard.tsx)
- Index files for barrel exports
```

## 🧭 Routing & Navigation

### React Router Configuration
```typescript
// App.tsx - Route Structure
<Routes>
  {/* Public Routes */}
  <Route path="/login" element={<AuthPage />} />
  <Route path="user/:username" element={<UserPage />} />
  
  {/* Protected Routes */}
  <Route path="/" element={<Layout />}>
    <Route index element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
    <Route path="trades" element={<ProtectedRoute><Trades /></ProtectedRoute>} />
    <Route path="marketplace" element={<ProtectedRoute><Marketplace /></ProtectedRoute>} />
    <Route path="statistics" element={<ProtectedRoute><Statistics /></ProtectedRoute>}>
      <Route path="overview" element={<OverviewPage />} />
      <Route path="performance" element={<PerformancePage />} />
      <Route path="solo-robot" element={<SoloRobotPage />} />
      <Route path="bro-robots" element={<BroRobotsPage />} />
    </Route>
  </Route>
  
  {/* Admin Routes */}
  <Route path="/admin" element={<AdminRoute><Layout /></AdminRoute>}>
    <Route index element={<AdminDashboard />} />
    <Route path="users" element={<UserManagement />} />
    <Route path="status" element={<AdminStatus />} />
  </Route>
</Routes>
```

### Sidebar Navigation System
```typescript
// Sidebar Features
- Collapsible main sidebar (icons only when collapsed)
- Auto-expand on left edge hover (50-60px threshold, 100-150ms delay)
- Submenu panels that open adjacent to collapsed sidebar
- Smooth animations with 300ms transitions
- Responsive design with accessibility standards
- Auto-collapse on navigation (800-1000ms delay, 50-80px buffer)

// Implementation Pattern
const { isCollapsed, toggleSidebar, activeSubmenu } = useSidebar();
```

## 🔐 Authentication & State Management

### AuthContext Pattern
```typescript
// src/context/AuthContext.tsx
interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  isAdmin: boolean;
  isSuperuser: boolean;
}

// Usage in components
const { user, isAdmin, signOut } = useAuth();
```

### Custom Hooks Pattern
```typescript
// Business Logic Hooks
src/hooks/
├── useAuth.ts          # Authentication logic
├── useRobots.ts        # Robot management
├── useTrades.ts        # Trading operations
├── useStatistics.ts    # Statistics data
└── useNotifications.ts # Notification system

// Hook Implementation Example
export const useRobots = () => {
  const [robots, setRobots] = useState<Robot[]>([]);
  const [loading, setLoading] = useState(true);
  
  const fetchRobots = useCallback(async () => {
    // Supabase query logic
  }, []);
  
  return { robots, loading, fetchRobots };
};
```

## 📊 Data Visualization & Tables

### Recharts Integration
```typescript
// Chart Components Pattern
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const PerformanceChart = ({ data }: { data: ChartData[] }) => (
  <ResponsiveContainer width="100%" height={300}>
    <LineChart data={data}>
      <CartesianGrid strokeDasharray="3 3" />
      <XAxis dataKey="date" />
      <YAxis />
      <Tooltip />
      <Line type="monotone" dataKey="profit" stroke="#38B2AC" strokeWidth={2} />
    </LineChart>
  </ResponsiveContainer>
);
```

### TanStack React Table
```typescript
// Table Configuration Pattern
const columns = useMemo<ColumnDef<Trade>[]>(() => [
  {
    accessorKey: 'symbol',
    header: 'Sembol',
    cell: ({ row }) => <Text fontWeight="bold">{row.getValue('symbol')}</Text>,
  },
  {
    accessorKey: 'order_side',
    header: 'İşlem',
    cell: ({ row }) => (
      <Badge colorScheme={row.getValue('order_side') === 'BUY' ? 'green' : 'red'}>
        {row.getValue('order_side')}
      </Badge>
    ),
  },
], []);
```

## 🔔 Notification System

### Toast Notifications
```typescript
// Turkish Notification Formats
const notificationTemplates = {
  buySignal: (symbol: string, price: number, quantity: number, robotName: string) => ({
    title: `${symbol} ALIM Sinyali`,
    description: `${robotName} - ${quantity} adet ${symbol} ${price} TL'den alındı. Toplam: ${(price * quantity).toLocaleString('tr-TR')} TL`,
    status: 'success' as const,
  }),
  sellSignal: (symbol: string, price: number, quantity: number, robotName: string) => ({
    title: `${symbol} SATIM Sinyali`,
    description: `${robotName} - ${quantity} adet ${symbol} ${price} TL'den satıldı. Toplam: ${(price * quantity).toLocaleString('tr-TR')} TL`,
    status: 'info' as const,
  }),
};

// Usage
const toast = useToast();
toast(notificationTemplates.buySignal('THYAO', 25.50, 100, 'Tobot Pro'));
```

### Real-time Notifications
```typescript
// Supabase Real-time Integration
useEffect(() => {
  const subscription = supabase
    .channel('notifications')
    .on('postgres_changes', 
      { event: 'INSERT', schema: 'public', table: 'notifications' },
      (payload) => {
        if (payload.new.user_id === user?.id) {
          toast({
            title: payload.new.title,
            description: payload.new.message,
            status: payload.new.severity,
          });
        }
      }
    )
    .subscribe();

  return () => subscription.unsubscribe();
}, [user?.id]);
```

## 🎭 Animation & Performance

### Framer Motion Integration
```typescript
// Page Transitions
const pageVariants = {
  initial: { opacity: 0, y: 20 },
  in: { opacity: 1, y: 0 },
  out: { opacity: 0, y: -20 }
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.3
};

// Component Animation
<motion.div
  initial="initial"
  animate="in"
  exit="out"
  variants={pageVariants}
  transition={pageTransition}
>
  {children}
</motion.div>
```

### Performance Optimizations
```typescript
// Lazy Loading Pattern
const Dashboard = lazy(() => import('./pages/Dashboard'));
const Statistics = lazy(() => import('./pages/Statistics'));

// Memoization
const MemoizedChart = memo(PerformanceChart);
const MemoizedTable = memo(TradesTable);

// Virtual Scrolling for Large Lists
import { FixedSizeList as List } from 'react-window';
```

## 🌐 API Integration

### Supabase Client Configuration
```typescript
// src/supabaseClient.ts
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
  },
  global: {
    headers: {
      'x-application-name': 'algobir-app-frontend',
    },
  },
});
```

### Service Layer Pattern
```typescript
// src/services/robotService.ts
export const robotService = {
  async getRobots(): Promise<Robot[]> {
    const { data, error } = await supabase
      .from('robots')
      .select('*')
      .eq('is_active', true);
    
    if (error) throw error;
    return data;
  },
  
  async createRobot(robot: CreateRobotRequest): Promise<Robot> {
    const { data, error } = await supabase
      .from('robots')
      .insert([robot])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },
};
```

## 🚀 UX Optimizations Applied (2025-01-30)

### Enhanced Loading States
```typescript
// LoadingState.tsx - Advanced loading component
interface LoadingProps {
  text?: string;
  type?: 'spinner' | 'skeleton' | 'progress';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'minimal' | 'detailed';
  progress?: number;
  showProgress?: boolean;
  ariaLabel?: string;
}

// Usage with accessibility and performance
<LoadingState
  type="progress"
  progress={75}
  showProgress={true}
  ariaLabel="Veriler yükleniyor"
  variant="detailed"
/>
```

### Advanced Error Handling
```typescript
// ErrorBoundary.tsx - Enhanced error boundaries
class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  // Features:
  // - Retry mechanism with limits
  // - Error ID generation for tracking
  // - External error service integration
  // - User ID tracking
  // - Enhanced logging
}

// ErrorFallback.tsx - User-friendly error displays
<ErrorFallback
  error={error}
  resetErrorBoundary={resetErrorBoundary}
  variant="detailed"
  showDetails={isDevelopment}
  showHomeButton={true}
/>
```

### Responsive Design System
```typescript
// ResponsiveContainer.tsx - Mobile-first containers
<ResponsiveContainer variant="page" spacing="md">
  <ResponsiveGrid columns={{ base: 1, md: 2, lg: 3 }}>
    {items.map(item => <Card key={item.id} {...item} />)}
  </ResponsiveGrid>
</ResponsiveContainer>

// ResponsiveStack - Adaptive layouts
<ResponsiveStack direction="responsive" spacing={{ base: 4, md: 6 }}>
  <Button>Action 1</Button>
  <Button>Action 2</Button>
</ResponsiveStack>
```

### Mobile Search Experience
```typescript
// MobileSearchDrawer.tsx - Full-screen mobile search
<MobileSearchDrawer
  isOpen={isSearchOpen}
  onClose={() => setIsSearchOpen(false)}
  placeholder="Ara..."
/>

// Features:
// - Touch gesture support (swipe to close)
// - Keyboard navigation
// - Recent searches
// - Optimized touch targets (44px minimum)
// - Voice search ready
```

### Accessibility Enhancements
```typescript
// useAccessibility.ts - Comprehensive a11y support
const { announce, focusElement, toggleHighContrast } = useAccessibility({
  announcePageChanges: true,
  manageFocus: true,
  enableKeyboardShortcuts: true
});

// Built-in keyboard shortcuts:
// Alt + S: Skip to main content
// Alt + H: Toggle high contrast
// Alt + M: Toggle reduced motion
// Ctrl + K: Focus search
// Ctrl + /: Show shortcuts help
```

### Touch Interactions
```typescript
// useTouchInteractions.ts - Mobile gesture support
const { isTouch, touchCount } = useTouchInteractions(
  elementRef,
  { enableSwipe: true, enablePinch: false },
  {
    onSwipe: (gesture) => {
      if (gesture.direction === 'left') handleNext();
      if (gesture.direction === 'right') handlePrevious();
    },
    onTap: (point) => handleTap(point),
    onLongPress: (point) => showContextMenu(point)
  }
);
```

## Update Requirements

## 📝 Recent Feature Implementations (2025-01-30)

### Bio Textarea Enhancement - Profile.tsx
```typescript
// Enhanced bio textarea with real-time features
<Textarea
  value={formData.bio}
  onChange={(e) => {
    const newValue = e.target.value;
    // Enforce character limit and sanitize input
    if (newValue.length <= 500) {
      const sanitizedValue = newValue.replace(/[<>]/g, '');
      setFormData(prev => ({ ...prev, bio: sanitizedValue }));
    }
  }}
  borderColor={
    formData.bio.length > 450 ? 'orange.300' :
    formData.bio.length === 500 ? 'red.300' :
    borderColor
  }
  maxLength={500}
/>

// Real-time character counter
<Text
  fontSize="xs"
  color={
    formData.bio.length === 500 ? 'red.500' :
    formData.bio.length > 450 ? 'orange.500' :
    'gray.500'
  }
>
  {500 - formData.bio.length} karakter kaldı
</Text>

// Auto-save status indicator
{bioAutoSaveStatus !== 'idle' && (
  <Text fontSize="xs" color={bioAutoSaveStatus === 'saving' ? 'blue.500' : 'green.500'}>
    {bioAutoSaveStatus === 'saving' ? 'Kaydediliyor...' : 'Kaydedildi ✓'}
  </Text>
)}
```

### Auto-Save Implementation Pattern
```typescript
// Debounced auto-save with security sanitization
useEffect(() => {
  if (!profile || !isEditing) return;

  if (bioTimeoutRef.current) {
    clearTimeout(bioTimeoutRef.current);
  }

  if (formData.bio !== profile.bio) {
    bioTimeoutRef.current = setTimeout(async () => {
      setBioAutoSaveStatus('saving');

      // Sanitize following security guidelines
      const sanitizedBio = formData.bio
        .replace(/[<>]/g, '')           // Remove HTML tags
        .replace(/javascript:/gi, '')   // Remove javascript: protocol
        .trim()
        .slice(0, 500);                // Enforce length limit

      await supabase.from('profiles').update({
        bio: sanitizedBio || null,
        updated_at: new Date().toISOString()
      }).eq('id', user?.id);

      setBioAutoSaveStatus('saved');
    }, 2000);
  }
}, [formData.bio, profile?.bio, isEditing, user?.id]);
```

## 🚨 KRİTİK PERFORMANS REGRESYONU ÇÖZÜMÜ (2025-01-30)

### Frontend Build System Düzeltmeleri
```yaml
Problem: "TypeScript compilation errors causing build failures"
Etkilenen Dosyalar:
  - algobir-app-frontend/src/utils/queryOptimizer.ts (56 errors)
  - algobir-app-frontend/src/utils/monitoring.ts (5 errors)
  - algobir-app-frontend/src/hooks/useMonitoring.ts (1 error)

Çözüm Stratejisi:
  1. queryOptimizer.ts tamamen yeniden yazıldı (simplified, type-safe)
  2. monitoring.ts TypeScript type casting issues düzeltildi
  3. useMonitoring.ts unused interfaces kaldırıldı
  4. Build process restore edildi
```

### Simplified QueryOptimizer Implementation
```typescript
// algobir-app-frontend/src/utils/queryOptimizer.ts
// Removed complex optimization patterns that caused TypeScript errors
// Maintained essential functionality with type safety

export class QueryOptimizer {
  private static cacheHitRate = new Map<string, { hits: number; misses: number }>();

  static async optimizedSelect<T = any>(
    table: string,
    config: {
      columns?: string;
      filters?: Record<string, any>;
      orderBy?: { column: string; ascending?: boolean }[];
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<QueryResult<T[]>> {
    // Type-safe implementation without complex query optimization
    // Focus on stability over advanced features
  }
}
```

### Frontend Performance Restoration
```typescript
// Build Performance Results
Before Fix:
  - Build Status: ❌ Failed (TypeScript compilation errors)
  - Dev Server: ❌ Extended loading times
  - User Experience: ❌ Application unusable
  - TypeScript Errors: 62 total errors

After Fix:
  - Build Status: ✅ Successful completion
  - Dev Server: ✅ 298ms startup time ⚡
  - User Experience: ✅ Responsive and fast
  - TypeScript Errors: 0 errors
```

### Component Architecture Stability
```typescript
// Maintained component patterns while fixing build issues
src/components/
├── LoadingState.tsx      # ✅ Working (no TypeScript errors)
├── ErrorFallback.tsx     # ✅ Working (no TypeScript errors)
├── ErrorBoundary.tsx     # ✅ Working (no TypeScript errors)
├── ResponsiveContainer.tsx # ✅ Working (no TypeScript errors)
└── MobileSearchDrawer.tsx  # ✅ Working (no TypeScript errors)

// All UX optimizations preserved
- Enhanced loading states
- Advanced error handling
- Responsive design system
- Mobile search experience
- Accessibility enhancements
- Touch interactions
```

### TypeScript Strict Mode Compliance
```typescript
// Fixed common TypeScript issues in frontend utilities
interface QueryResult<T = any> {
  data: T | null;                    // Proper null handling
  error: any;                        // Error type handling
  count?: number;                    // Optional count (not null)
  executionTime: number;             // Performance tracking
  fromCache: boolean;                // Cache status
  queryId: string;                   // Query identification
}

// Type-safe Supabase query results
return {
  data: result.data as T[] | null,   // Explicit type casting
  error: result.error,
  count: result.count || undefined,  // Null to undefined conversion
  executionTime,
  fromCache: false,
  queryId
};
```

### Frontend Development Experience
```typescript
// Improved developer experience with stable build
Development Workflow:
  1. ✅ Fast TypeScript compilation
  2. ✅ Hot Module Replacement (HMR) working
  3. ✅ Error-free development server
  4. ✅ Responsive UI components
  5. ✅ Stable build process

Production Build:
  1. ✅ Successful TypeScript compilation
  2. ✅ Optimized bundle generation
  3. ✅ Asset optimization working
  4. ✅ Performance optimizations active
```

### Lessons Learned - Frontend Stability
```yaml
Technical Lessons:
  - "Complexity vs Stability": Simple, working code > complex, broken code
  - "TypeScript Compliance": All utilities must be TypeScript strict mode compatible
  - "Build Process Validation": Every optimization must pass build tests
  - "Incremental Implementation": Large changes should be implemented incrementally

Development Process:
  - "Error Monitoring": TypeScript errors can completely break the application
  - "Rollback Strategy": Always have a working fallback implementation
  - "Testing Strategy": Build tests are as important as unit tests
  - "Documentation": All changes must be documented for future reference
```

### When to Update This File
- New UI components or patterns added
- Routing structure changes
- State management modifications
- New animation or performance optimizations
- API integration pattern changes
- UX/Accessibility improvements
- Responsive design enhancements
- Security or performance optimizations
- Theme or design system updates
- Feature enhancements like bio textarea improvements
- **Build system issues and resolutions**
- **TypeScript compilation fixes**
- **Performance regression solutions**

### Related Files to Update
- Update `features/*.mdc` when adding new feature components
- Update `performance/OPTIMIZATION.mdc` for performance changes
- Update `security/GUIDELINES.mdc` for authentication changes
- Update `core/PROJECT_CORE.mdc` for major architecture changes
