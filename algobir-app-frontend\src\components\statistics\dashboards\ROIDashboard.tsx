import React, { useState, useMemo } from 'react';
import {
  Box,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  Button,
  ButtonGroup,
  useColorModeValue,
  useBreakpointValue,
  Icon,

  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Alert,
  AlertIcon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  AlertTitle,
  AlertDescription,
  Divider,
  Switch,
  FormControl,
  FormLabel,
  Flex,

  Select,
  Spinner
} from '@chakra-ui/react';
import {

  Line,

  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  ComposedChart,
  PieChart,
  Pie,
  Cell,
  ScatterChart,
  Scatter,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,

  Legend
} from 'recharts';
import {
  FiTrendingUp,
  FiTrendingDown,
  FiDollarSign,
  FiTarget,
  FiCalendar,
  FiPieChart,
  FiBarChart,
  FiActivity,
  FiAward,
  FiAlertTriangle,

  FiShield,
  FiZap
} from 'react-icons/fi';
import { EnhancedStatsData } from '../../../hooks/useEnhancedStatistics';
import AdvancedLineChart from '../charts/AdvancedLineChart';
import InteractivePieChart from '../charts/InteractivePieChart';

interface ROIDashboardProps {
  stats: EnhancedStatsData;
}

const ROIDashboard: React.FC<ROIDashboardProps> = ({ stats }) => {
  const [timeframe, setTimeframe] = useState<'monthly' | 'quarterly' | 'yearly'>('monthly');
  const [viewMode, setViewMode] = useState<'absolute' | 'percentage'>('percentage');
  const [activeTab, setActiveTab] = useState(0);
  const [showAdvancedMetrics, setShowAdvancedMetrics] = useState(false);
  const [comparisonMode, setComparisonMode] = useState<'robot' | 'symbol' | 'time'>('robot');
  const [lastUpdateTime, setLastUpdateTime] = useState<string>(new Date().toISOString());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const cardBg = useColorModeValue('white', 'gray.700');
  const textColor = useColorModeValue('gray.700', 'white');
  const accentColor = useColorModeValue('blue.500', 'blue.300');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const successColor = useColorModeValue('green.500', 'green.300');
  const errorColor = useColorModeValue('red.500', 'red.300');

  const formatCurrency = (value: number) => `₺${value.toFixed(2)}`;
  const formatPercent = (value: number) => `${value.toFixed(2)}%`;
  const formatNumber = (value: number) => value.toFixed(2);
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const { roiAnalysis, realtimeMetrics, liveUpdates, lastUpdateTime: statsUpdateTime } = stats;

  // Update local time when stats update
  React.useEffect(() => {
    if (statsUpdateTime) {
      setLastUpdateTime(statsUpdateTime);
      setError(null); // Clear any previous errors when new data arrives
    }
  }, [statsUpdateTime]);

  // Error handling and retry logic
  const handleRetry = React.useCallback(() => {
    setIsLoading(true);
    setError(null);
    setRetryCount(prev => prev + 1);

    // Simulate retry delay
    setTimeout(() => {
      setIsLoading(false);
      // In a real implementation, this would trigger a data refetch
    }, 1000);
  }, []);

  // Validate data integrity
  const validateData = React.useCallback(() => {
    try {
      if (!stats) {
        throw new Error('İstatistik verileri yüklenemedi');
      }

      if (!roiAnalysis) {
        throw new Error('ROI analiz verileri bulunamadı');
      }

      if (roiAnalysis.totalInvestment < 0) {
        throw new Error('Geçersiz yatırım miktarı tespit edildi');
      }

      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Bilinmeyen bir hata oluştu');
      return false;
    }
  }, [stats, roiAnalysis]);

  // Validate data on component mount and stats change
  React.useEffect(() => {
    validateData();
  }, [validateData]);

  // Enhanced ROI performance indicators with real-time data
  const roiIndicators = [
    {
      label: 'Toplam ROI',
      value: formatPercent(roiAnalysis.totalROI),
      icon: FiTrendingUp,
      color: roiAnalysis.totalROI > 0 ? 'green' : 'red',
      change: roiAnalysis.totalROI,
      description: 'Toplam yatırım getirisi oranı',
      category: 'basic',
      realTimeValue: null, // currentROI not available in realtimeMetrics
      isLive: liveUpdates
    },
    {
      label: 'Yıllık ROI',
      value: formatPercent(roiAnalysis.annualizedROI),
      icon: FiCalendar,
      color: roiAnalysis.annualizedROI > 0 ? 'green' : 'red',
      change: roiAnalysis.annualizedROI,
      description: 'Yıllık bazda hesaplanan getiri',
      category: 'basic',
      realTimeValue: null,
      isLive: false
    },
    {
      label: 'Toplam Yatırım',
      value: formatCurrency(roiAnalysis.totalInvestment),
      icon: FiDollarSign,
      color: 'blue',
      change: 0,
      description: 'Toplam yatırım miktarı',
      category: 'basic',
      realTimeValue: null,
      isLive: false
    },
    {
      label: 'Toplam Getiri',
      value: formatCurrency(roiAnalysis.totalReturns),
      icon: FiTarget,
      color: roiAnalysis.totalReturns > 0 ? 'green' : 'red',
      change: roiAnalysis.totalReturns,
      description: 'Net kar/zarar miktarı',
      category: 'basic',
      realTimeValue: null,
      isLive: false
    },
    {
      label: 'Bileşik Büyüme (CAGR)',
      value: formatPercent(roiAnalysis.compoundGrowthRate),
      icon: FiBarChart,
      color: roiAnalysis.compoundGrowthRate > 0 ? 'green' : 'red',
      change: roiAnalysis.compoundGrowthRate,
      description: 'Bileşik yıllık büyüme oranı',
      category: 'basic',
      realTimeValue: null,
      isLive: false
    },
    {
      label: 'Risk Ayarlı Getiri',
      value: formatPercent(roiAnalysis.riskAdjustedReturn),
      icon: FiShield,
      color: roiAnalysis.riskAdjustedReturn > 0 ? 'green' : 'orange',
      change: roiAnalysis.riskAdjustedReturn,
      description: 'Sharpe oranı bazlı risk ayarlı getiri',
      category: 'advanced',
      realTimeValue: null,
      isLive: false
    },
    {
      label: 'Volatilite',
      value: formatPercent(roiAnalysis.volatility || 0),
      icon: FiActivity,
      color: (roiAnalysis.volatility || 0) < 15 ? 'green' : (roiAnalysis.volatility || 0) < 25 ? 'orange' : 'red',
      change: roiAnalysis.volatility || 0,
      description: 'Getiri volatilitesi (standart sapma)',
      category: 'advanced',
      realTimeValue: null,
      isLive: false
    },
    {
      label: 'Sharpe Oranı',
      value: formatNumber(roiAnalysis.sharpeRatio || 0),
      icon: FiAward,
      color: (roiAnalysis.sharpeRatio || 0) > 1 ? 'green' : (roiAnalysis.sharpeRatio || 0) > 0.5 ? 'orange' : 'red',
      change: roiAnalysis.sharpeRatio || 0,
      description: 'Risk başına getiri oranı',
      category: 'advanced',
      realTimeValue: null,
      isLive: false
    },
    {
      label: 'Maksimum Düşüş',
      value: formatPercent(roiAnalysis.maxDrawdown || 0),
      icon: FiTrendingDown,
      color: (roiAnalysis.maxDrawdown || 0) < 10 ? 'green' : (roiAnalysis.maxDrawdown || 0) < 20 ? 'orange' : 'red',
      change: roiAnalysis.maxDrawdown || 0,
      description: 'En büyük değer kaybı yüzdesi',
      category: 'advanced',
      realTimeValue: null,
      isLive: false
    }
  ];

  // Filter indicators based on advanced metrics toggle
  const displayedIndicators = showAdvancedMetrics
    ? roiIndicators
    : roiIndicators.filter(indicator => indicator.category === 'basic');

  // Prepare chart data based on timeframe
  const chartData = useMemo(() => {
    switch (timeframe) {
      case 'monthly':
        return roiAnalysis.monthlyROI.map(item => ({
          date: item.month,
          roi: item.roi,
          investment: item.investment,
          returns: item.returns,
          value: viewMode === 'percentage' ? item.roi : item.returns
        }));
      case 'quarterly':
        return roiAnalysis.quarterlyROI.map(item => ({
          date: item.quarter,
          roi: item.roi,
          investment: item.investment,
          returns: item.returns,
          value: viewMode === 'percentage' ? item.roi : item.returns
        }));
      case 'yearly':
        return roiAnalysis.yearlyROI.map(item => ({
          date: item.year,
          roi: item.roi,
          investment: item.investment,
          returns: item.returns,
          value: viewMode === 'percentage' ? item.roi : item.returns
        }));
      default:
        return roiAnalysis.monthlyROI.map(item => ({
          date: item.month,
          roi: item.roi,
          investment: item.investment,
          returns: item.returns,
          value: viewMode === 'percentage' ? item.roi : item.returns
        }));
    }
  }, [timeframe, viewMode, roiAnalysis]);

  // Robot ROI comparison data
  const robotROIData = roiAnalysis.roiByRobot.map(robot => ({
    name: robot.robotName.length > 12 ? robot.robotName.slice(0, 12) + '...' : robot.robotName,
    fullName: robot.robotName,
    value: robot.roi,
    investment: robot.investment,
    color: robot.roi > 0 ? '#38A169' : '#E53E3E'
  }));

  // Performance benchmarks
  const benchmarks = [
    { name: 'Mükemmel', min: 20, color: '#38A169' },
    { name: 'İyi', min: 10, color: '#68D391' },
    { name: 'Orta', min: 5, color: '#D69E2E' },
    { name: 'Zayıf', min: 0, color: '#E53E3E' },
    { name: 'Kayıp', min: -Infinity, color: '#C53030' }
  ];

  const getPerformanceBenchmark = (roi: number) => {
    return benchmarks.find(benchmark => roi >= benchmark.min) || benchmarks[benchmarks.length - 1];
  };

  const currentBenchmark = getPerformanceBenchmark(roiAnalysis?.totalROI || 0);

  // Loading State
  if (isLoading) {
    return (
      <VStack spacing={6} align="stretch">
        <Card bg={cardBg}>
          <CardBody>
            <VStack spacing={4} align="center" py={8}>
              <Spinner size="xl" color={accentColor} thickness="4px" data-testid="loading-spinner" />
              <VStack spacing={2}>
                <Text fontSize="lg" fontWeight="bold">ROI Analizi Yükleniyor...</Text>
                <Text fontSize="sm" color="gray.500">
                  Veriler işleniyor, lütfen bekleyiniz
                </Text>
              </VStack>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    );
  }

  // Error State
  if (error) {
    return (
      <Alert status="error" borderRadius="lg">
        <AlertIcon />
        <VStack align="start" spacing={3} flex={1}>
          <VStack align="start" spacing={1}>
            <AlertTitle>ROI Analizi Hatası</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </VStack>
          <HStack spacing={3}>
            <Button
              size="sm"
              colorScheme="red"
              variant="outline"
              onClick={handleRetry}
              isLoading={isLoading}
              loadingText="Yeniden Deniyor..."
            >
              Yeniden Dene
            </Button>
            {retryCount > 0 && (
              <Text fontSize="xs" color="gray.500">
                Deneme sayısı: {retryCount}
              </Text>
            )}
          </HStack>
        </VStack>
      </Alert>
    );
  }

  // No Data State
  if (!roiAnalysis || roiAnalysis.totalInvestment === 0) {
    return (
      <Alert status="info" borderRadius="lg">
        <AlertIcon />
        <VStack align="start" spacing={3} flex={1}>
          <VStack align="start" spacing={1}>
            <AlertTitle>ROI Analizi Yapılamıyor</AlertTitle>
            <AlertDescription>
              ROI analizi için yeterli yatırım verisi bulunmuyor.
              İşlem geçmişiniz oluştuktan sonra detaylı ROI analizini burada görebileceksiniz.
            </AlertDescription>
          </VStack>
          <Button
            size="sm"
            colorScheme="blue"
            variant="outline"
            onClick={handleRetry}
          >
            Verileri Yenile
          </Button>
        </VStack>
      </Alert>
    );
  }

  return (
    <VStack spacing={6} align="stretch" data-testid="roi-dashboard">
      {/* Enhanced Header with Controls */}
      <Card bg={cardBg} borderColor={borderColor}>
        <CardBody>
          <Flex direction={{ base: 'column', lg: 'row' }} gap={4}>
            <Box flex={1}>
              <HStack spacing={4} mb={4}>
                <Icon as={FiTrendingUp} boxSize={8} color={accentColor} />
                <VStack align="start" spacing={0}>
                  <Heading size="lg" color={textColor}>ROI ve Karlılık Analizi</Heading>
                  <Text color="gray.500">Gelişmiş yatırım getirisi ve performans metrikleri</Text>
                </VStack>
              </HStack>

              <HStack spacing={4} flexWrap="wrap">
                <Badge
                  colorScheme={currentBenchmark.name === 'Kayıp' ? 'red' : 'green'}
                  variant="subtle"
                  px={3}
                  py={1}
                >
                  {currentBenchmark.name} Performans
                </Badge>
                <Badge colorScheme="blue" variant="subtle" px={3} py={1}>
                  {formatCurrency(roiAnalysis.totalInvestment)} Yatırım
                </Badge>
                <Badge
                  colorScheme={roiAnalysis.totalReturns > 0 ? 'green' : 'red'}
                  variant="subtle"
                  px={3}
                  py={1}
                >
                  {formatCurrency(roiAnalysis.totalReturns)} Getiri
                </Badge>
                {/* Real-time indicators */}
                <Badge
                  colorScheme={liveUpdates ? 'green' : 'gray'}
                  variant="solid"
                  px={3}
                  py={1}
                >
                  <HStack spacing={1}>
                    <Box
                      w={2}
                      h={2}
                      bg={liveUpdates ? 'green.300' : 'gray.300'}
                      borderRadius="full"
                      animation={liveUpdates ? 'pulse 2s infinite' : 'none'}
                    />
                    <Text fontSize="xs">
                      {liveUpdates ? 'Canlı' : 'Statik'}
                    </Text>
                  </HStack>
                </Badge>
                <Badge colorScheme="purple" variant="subtle" px={3} py={1}>
                  <Text fontSize="xs">
                    Son: {formatTime(lastUpdateTime)}
                  </Text>
                </Badge>
                {realtimeMetrics.openPositions > 0 && (
                  <Badge colorScheme="orange" variant="solid" px={3} py={1}>
                    {realtimeMetrics.openPositions} Açık Pozisyon
                  </Badge>
                )}
                {realtimeMetrics.todaysPnl !== 0 && (
                  <Badge
                    colorScheme={realtimeMetrics.todaysPnl > 0 ? 'green' : 'red'}
                    variant="solid"
                    px={3}
                    py={1}
                  >
                    Bugün: {formatCurrency(realtimeMetrics.todaysPnl)}
                  </Badge>
                )}
                {/* Data Freshness Warning */}
                {(() => {
                  const timeDiff = new Date().getTime() - new Date(lastUpdateTime).getTime();
                  const minutesDiff = Math.floor(timeDiff / (1000 * 60));
                  if (minutesDiff > 5) {
                    return (
                      <Badge colorScheme="orange" variant="subtle" px={3} py={1}>
                        <HStack spacing={1}>
                          <Icon as={FiAlertTriangle} boxSize={3} />
                          <Text fontSize="xs">
                            Eski Veri ({minutesDiff}dk)
                          </Text>
                        </HStack>
                      </Badge>
                    );
                  }
                  return null;
                })()}
              </HStack>
            </Box>

            {/* Controls - Enhanced Responsive */}
            <VStack
              spacing={4}
              align="stretch"
              minW={{ base: "full", lg: "250px" }}
              w={{ base: "full", lg: "auto" }}
              data-testid="roi-controls"
            >
              <FormControl display="flex" alignItems="center" justifyContent={{ base: "space-between", lg: "flex-start" }}>
                <FormLabel htmlFor="advanced-metrics" mb="0" fontSize={{ base: "xs", md: "sm" }}>
                  Gelişmiş Metrikler
                </FormLabel>
                <Switch
                  id="advanced-metrics"
                  isChecked={showAdvancedMetrics}
                  onChange={(e) => setShowAdvancedMetrics(e.target.checked)}
                  colorScheme="blue"
                  size={{ base: "sm", md: "md" }}
                />
              </FormControl>

              <FormControl>
                <FormLabel fontSize={{ base: "xs", md: "sm" }}>Zaman Aralığı</FormLabel>
                <Select
                  value={timeframe}
                  onChange={(e) => setTimeframe(e.target.value as any)}
                  size={{ base: "sm", md: "md" }}
                >
                  <option value="monthly">Aylık</option>
                  <option value="quarterly">Çeyreklik</option>
                  <option value="yearly">Yıllık</option>
                </Select>
              </FormControl>

              <FormControl>
                <FormLabel fontSize={{ base: "xs", md: "sm" }}>Görünüm Modu</FormLabel>
                <ButtonGroup size={{ base: "sm", md: "md" }} isAttached variant="outline" w="full">
                  <Button
                    flex={1}
                    isActive={viewMode === 'percentage'}
                    onClick={() => setViewMode('percentage')}
                    colorScheme={viewMode === 'percentage' ? 'blue' : 'gray'}
                    fontSize={{ base: "xs", md: "sm" }}
                  >
                    %
                  </Button>
                  <Button
                    flex={1}
                    isActive={viewMode === 'absolute'}
                    onClick={() => setViewMode('absolute')}
                    colorScheme={viewMode === 'absolute' ? 'blue' : 'gray'}
                    fontSize={{ base: "xs", md: "sm" }}
                  >
                    ₺
                  </Button>
                </ButtonGroup>
              </FormControl>
            </VStack>
          </Flex>
        </CardBody>
      </Card>

      {/* Enhanced ROI Metrics Grid - Improved Responsive */}
      <SimpleGrid
        columns={{
          base: 1,
          sm: 2,
          md: showAdvancedMetrics ? 2 : 3,
          lg: showAdvancedMetrics ? 3 : 4,
          xl: showAdvancedMetrics ? 4 : 5
        }}
        spacing={{ base: 4, md: 6 }}
      >
        {displayedIndicators.map((indicator, index) => (
          <Card key={index} bg={cardBg} position="relative">
            <CardBody>
              <Stat>
                <HStack spacing={3} mb={2} justify="space-between">
                  <HStack spacing={3}>
                    <Icon as={indicator.icon} color={`${indicator.color}.500`} boxSize={5} />
                    <StatLabel fontSize="sm">{indicator.label}</StatLabel>
                  </HStack>
                  {indicator.isLive && (
                    <Badge size="sm" colorScheme="green" variant="solid">
                      <HStack spacing={1}>
                        <Box
                          w={1.5}
                          h={1.5}
                          bg="green.300"
                          borderRadius="full"
                          animation="pulse 2s infinite"
                        />
                        <Text fontSize="xs">CANLI</Text>
                      </HStack>
                    </Badge>
                  )}
                </HStack>
                <StatNumber
                  fontSize="2xl"
                  color={`${indicator.color}.500`}
                  mb={1}
                  data-testid="metric-value"
                >
                  {indicator.realTimeValue || indicator.value}
                </StatNumber>
                {indicator.realTimeValue && indicator.realTimeValue !== indicator.value && (
                  <Text fontSize="sm" color="gray.500" mb={1}>
                    Önceki: {indicator.value}
                  </Text>
                )}
                <StatHelpText fontSize="xs" mb={2}>
                  {indicator.change !== 0 && (
                    <StatArrow type={indicator.change > 0 ? 'increase' : 'decrease'} />
                  )}
                  {indicator.description}
                </StatHelpText>
                {indicator.label === 'Toplam ROI' && (
                  <Progress
                    value={Math.min(100, Math.max(0, roiAnalysis.totalROI + 50))}
                    colorScheme={roiAnalysis.totalROI > 0 ? 'green' : 'red'}
                    size="sm"
                    borderRadius="md"
                  />
                )}
                {indicator.isLive && (
                  <HStack spacing={1} mt={2}>
                    <Icon as={FiZap} color="green.400" boxSize={3} />
                    <Text fontSize="xs" color="green.500">
                      Canlı güncelleniyor
                    </Text>
                  </HStack>
                )}
              </Stat>
            </CardBody>
          </Card>
        ))}
      </SimpleGrid>

      {/* Enhanced Tabbed Analysis Interface - Mobile Responsive */}
      <Tabs
        index={activeTab}
        onChange={setActiveTab}
        variant="enclosed"
        colorScheme="blue"
        orientation="horizontal"
        isFitted={useBreakpointValue({ base: true, md: false })}
      >
        <TabList
          overflowX={{ base: "auto", md: "visible" }}
          overflowY="hidden"
          flexWrap={{ base: "nowrap", md: "wrap" }}
        >
          <Tab minW={{ base: "120px", md: "auto" }} fontSize={{ base: "xs", md: "sm" }}>
            <Icon as={FiTrendingUp} mr={{ base: 1, md: 2 }} boxSize={{ base: 3, md: 4 }} />
            <Text display={{ base: "none", sm: "block" }}>Trend Analizi</Text>
            <Text display={{ base: "block", sm: "none" }}>Trend</Text>
          </Tab>
          <Tab minW={{ base: "120px", md: "auto" }} fontSize={{ base: "xs", md: "sm" }}>
            <Icon as={FiBarChart} mr={{ base: 1, md: 2 }} boxSize={{ base: 3, md: 4 }} />
            <Text display={{ base: "none", sm: "block" }}>Karşılaştırma</Text>
            <Text display={{ base: "block", sm: "none" }}>Karşılaştır</Text>
          </Tab>
          <Tab minW={{ base: "120px", md: "auto" }} fontSize={{ base: "xs", md: "sm" }}>
            <Icon as={FiPieChart} mr={{ base: 1, md: 2 }} boxSize={{ base: 3, md: 4 }} />
            <Text display={{ base: "none", sm: "block" }}>Dağılım</Text>
            <Text display={{ base: "block", sm: "none" }}>Dağılım</Text>
          </Tab>
          {showAdvancedMetrics && (
            <Tab minW={{ base: "120px", md: "auto" }} fontSize={{ base: "xs", md: "sm" }}>
              <Icon as={FiShield} mr={{ base: 1, md: 2 }} boxSize={{ base: 3, md: 4 }} />
              <Text display={{ base: "none", sm: "block" }}>Risk Analizi</Text>
              <Text display={{ base: "block", sm: "none" }}>Risk</Text>
            </Tab>
          )}
        </TabList>

        <TabPanels>
          {/* Trend Analysis Tab */}
          <TabPanel p={0} pt={6}>
            <VStack spacing={6} align="stretch">
              {/* ROI Trend Chart */}
              <Card bg={cardBg}>
                <CardHeader>
                  <Flex justify="space-between" align="center">
                    <Heading size="md">
                      {timeframe === 'monthly' ? 'Aylık' : timeframe === 'quarterly' ? 'Çeyreklik' : 'Yıllık'} ROI Trendi
                    </Heading>
                    <HStack>
                      <Text fontSize="sm" color="gray.500">Son güncelleme:</Text>
                      <Badge
                        colorScheme={liveUpdates ? 'green' : 'gray'}
                        variant="subtle"
                      >
                        <HStack spacing={1}>
                          <Box
                            w={2}
                            h={2}
                            bg={liveUpdates ? 'green.300' : 'gray.300'}
                            borderRadius="full"
                            animation={liveUpdates ? 'pulse 2s infinite' : 'none'}
                          />
                          <Text fontSize="xs">
                            {liveUpdates ? 'Canlı' : 'Statik'} - {formatTime(lastUpdateTime)}
                          </Text>
                        </HStack>
                      </Badge>
                      {realtimeMetrics.openPositions > 0 && (
                        <Badge colorScheme="orange" variant="solid">
                          {realtimeMetrics.openPositions} Açık
                        </Badge>
                      )}
                    </HStack>
                  </Flex>
                </CardHeader>
                <CardBody>
                  <Box h={{ base: "300px", md: "400px" }}>
                    {chartData && chartData.length > 0 ? (
                      <AdvancedLineChart
                        data={chartData}
                        dataKey="value"
                        color={accentColor}
                        height={useBreakpointValue({ base: 300, md: 400 })}
                        formatValue={viewMode === 'percentage' ? formatPercent : formatCurrency}
                        showBrush={true}
                        showMovingAverage={true}
                        showTrendLine={true}
                      />
                    ) : (
                      <VStack spacing={4} justify="center" h="full">
                        <Icon as={FiTrendingUp} boxSize={12} color="gray.400" />
                        <VStack spacing={1}>
                          <Text fontSize="lg" fontWeight="bold" color="gray.500">
                            Grafik Verisi Bulunamadı
                          </Text>
                          <Text fontSize="sm" color="gray.400">
                            Seçilen zaman aralığında veri bulunmuyor
                          </Text>
                        </VStack>
                        <Button size="sm" variant="outline" onClick={handleRetry}>
                          Yeniden Dene
                        </Button>
                      </VStack>
                    )}
                  </Box>
                </CardBody>
              </Card>

              {/* Performance Benchmark with Real-time Alerts */}
              <Card bg={cardBg}>
                <CardHeader>
                  <Heading size="md">Performans Kıyaslaması</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    {/* Real-time Performance Alert */}
                    {realtimeMetrics.todaysPnl !== 0 && (
                      <Alert
                        status={realtimeMetrics.todaysPnl > 0 ? 'success' : 'warning'}
                        borderRadius="lg"
                        size="sm"
                      >
                        <AlertIcon />
                        <Box>
                          <AlertTitle fontSize="sm">
                            Günlük Performans {realtimeMetrics.todaysPnl > 0 ? 'Pozitif' : 'Negatif'}
                          </AlertTitle>
                          <AlertDescription fontSize="xs">
                            Bugünkü P&L: {formatCurrency(realtimeMetrics.todaysPnl)}
                            {realtimeMetrics.openPositions > 0 && ` (${realtimeMetrics.openPositions} açık pozisyon)`}
                          </AlertDescription>
                        </Box>
                      </Alert>
                    )}

                    <HStack justify="space-between">
                      <Text fontWeight="600">Mevcut Performans Seviyesi</Text>
                      <Badge
                        colorScheme={currentBenchmark.name === 'Kayıp' ? 'red' : 'green'}
                        variant="solid"
                        px={3}
                        py={1}
                      >
                        {currentBenchmark.name}
                      </Badge>
                    </HStack>

                    <Box>
                      <HStack justify="space-between" mb={2}>
                        {benchmarks.slice(0, -1).map((benchmark, index) => (
                          <VStack key={index} spacing={1}>
                            <Box w={3} h={3} bg={benchmark.color} borderRadius="full" />
                            <Text fontSize="xs">{benchmark.name}</Text>
                            <Text fontSize="xs" color="gray.500">{benchmark.min}%+</Text>
                          </VStack>
                        ))}
                      </HStack>
                      <Progress
                        value={Math.min(100, Math.max(0, (roiAnalysis.totalROI + 10) * 2.5))}
                        colorScheme={roiAnalysis.totalROI > 0 ? 'green' : 'red'}
                        size="lg"
                        borderRadius="md"
                      />
                    </Box>

                    {/* Real-time metrics summary */}
                    {liveUpdates && (
                      <HStack spacing={4} pt={2} borderTop="1px" borderColor={borderColor}>
                        <VStack spacing={0}>
                          <Text fontSize="xs" color="gray.500">Açık Pozisyon</Text>
                          <Text fontSize="sm" fontWeight="bold">{realtimeMetrics.openPositions}</Text>
                        </VStack>
                        <VStack spacing={0}>
                          <Text fontSize="xs" color="gray.500">Günlük P&L</Text>
                          <Text
                            fontSize="sm"
                            fontWeight="bold"
                            color={realtimeMetrics.todaysPnl > 0 ? successColor : errorColor}
                          >
                            {formatCurrency(realtimeMetrics.todaysPnl)}
                          </Text>
                        </VStack>
                        <VStack spacing={0}>
                          <Text fontSize="xs" color="gray.500">Son İşlem</Text>
                          <Text fontSize="sm" fontWeight="bold">
                            {'Yok'} {/* lastTradeTime not available in realtimeMetrics */}
                          </Text>
                        </VStack>
                      </HStack>
                    )}
                  </VStack>
                </CardBody>
              </Card>
            </VStack>
          </TabPanel>

          {/* Comparison Analysis Tab */}
          <TabPanel p={0} pt={6}>
            <VStack spacing={6} align="stretch">
              {/* Comparison Mode Selector - Mobile Responsive */}
              <Card bg={cardBg}>
                <CardBody>
                  <FormControl>
                    <FormLabel fontSize={{ base: "sm", md: "md" }}>Karşılaştırma Türü</FormLabel>
                    <ButtonGroup
                      size={{ base: "sm", md: "md" }}
                      isAttached
                      variant="outline"
                      w="full"
                      orientation={useBreakpointValue({ base: "vertical", sm: "horizontal" })}
                    >
                      <Button
                        flex={1}
                        isActive={comparisonMode === 'robot'}
                        onClick={() => setComparisonMode('robot')}
                        colorScheme={comparisonMode === 'robot' ? 'blue' : 'gray'}
                        fontSize={{ base: "xs", md: "sm" }}
                      >
                        <Text display={{ base: "none", sm: "block" }}>Robot Bazlı</Text>
                        <Text display={{ base: "block", sm: "none" }}>Robot</Text>
                      </Button>
                      <Button
                        flex={1}
                        isActive={comparisonMode === 'symbol'}
                        onClick={() => setComparisonMode('symbol')}
                        colorScheme={comparisonMode === 'symbol' ? 'blue' : 'gray'}
                        fontSize={{ base: "xs", md: "sm" }}
                      >
                        <Text display={{ base: "none", sm: "block" }}>Sembol Bazlı</Text>
                        <Text display={{ base: "block", sm: "none" }}>Sembol</Text>
                      </Button>
                      <Button
                        flex={1}
                        isActive={comparisonMode === 'time'}
                        onClick={() => setComparisonMode('time')}
                        colorScheme={comparisonMode === 'time' ? 'blue' : 'gray'}
                        fontSize={{ base: "xs", md: "sm" }}
                      >
                        <Text display={{ base: "none", sm: "block" }}>Zaman Bazlı</Text>
                        <Text display={{ base: "block", sm: "none" }}>Zaman</Text>
                      </Button>
                    </ButtonGroup>
                  </FormControl>
                </CardBody>
              </Card>

              {/* Robot Comparison */}
              {comparisonMode === 'robot' && (
                <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                  <Card bg={cardBg}>
                    <CardHeader>
                      <Heading size="md">Robot ROI Karşılaştırması</Heading>
                    </CardHeader>
                    <CardBody>
                      <Box h={{ base: "250px", md: "350px" }}>
                        {robotROIData && robotROIData.length > 0 ? (
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={robotROIData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis
                                dataKey="name"
                                angle={-45}
                                textAnchor="end"
                                height={80}
                              />
                              <YAxis />
                              <RechartsTooltip
                                formatter={(value: number) => [formatPercent(value), 'ROI']}
                                labelFormatter={(label) => `Robot: ${label}`}
                              />
                              <Bar dataKey="value" fill={accentColor}>
                                {robotROIData.map((entry, index) => (
                                  <Cell key={`cell-${index}`} fill={entry.color} />
                                ))}
                              </Bar>
                            </BarChart>
                          </ResponsiveContainer>
                        ) : (
                          <VStack spacing={4} justify="center" h="full">
                            <Icon as={FiBarChart} boxSize={12} color="gray.400" />
                            <VStack spacing={1}>
                              <Text fontSize="lg" fontWeight="bold" color="gray.500">
                                Robot Verisi Bulunamadı
                              </Text>
                              <Text fontSize="sm" color="gray.400">
                                Karşılaştırılacak robot verisi yok
                              </Text>
                            </VStack>
                          </VStack>
                        )}
                      </Box>
                    </CardBody>
                  </Card>

                  <Card bg={cardBg}>
                    <CardHeader>
                      <Heading size="md">Robot Performans Radarı</Heading>
                    </CardHeader>
                    <CardBody>
                      <Box h="350px">
                        <ResponsiveContainer width="100%" height="100%">
                          <RadarChart data={robotROIData.slice(0, 6)}>
                            <PolarGrid />
                            <PolarAngleAxis dataKey="name" />
                            <PolarRadiusAxis
                              angle={90}
                              domain={[0, Math.max(...robotROIData.map(r => r.value))]}
                            />
                            <Radar
                              name="ROI"
                              dataKey="value"
                              stroke={accentColor}
                              fill={accentColor}
                              fillOpacity={0.3}
                            />
                            <RechartsTooltip formatter={(value: number) => [formatPercent(value), 'ROI']} />
                          </RadarChart>
                        </ResponsiveContainer>
                      </Box>
                    </CardBody>
                  </Card>
                </SimpleGrid>
              )}

              {/* Symbol Comparison */}
              {comparisonMode === 'symbol' && roiAnalysis.roiBySymbol && (
                <Card bg={cardBg}>
                  <CardHeader>
                    <Heading size="md">Sembol Bazlı ROI Analizi</Heading>
                  </CardHeader>
                  <CardBody>
                    <Box h="400px">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={roiAnalysis.roiBySymbol.slice(0, 10)}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="symbol" />
                          <YAxis />
                          <RechartsTooltip
                            formatter={(value: number) => [formatPercent(value), 'ROI']}
                          />
                          <Bar dataKey="roi" fill={accentColor} />
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardBody>
                </Card>
              )}

              {/* Time-based Comparison */}
              {comparisonMode === 'time' && (
                <VStack spacing={6} align="stretch">
                  <Card bg={cardBg}>
                    <CardHeader>
                      <Heading size="md">Zaman Bazlı ROI Karşılaştırması</Heading>
                    </CardHeader>
                    <CardBody>
                      <Box h="400px">
                        <ResponsiveContainer width="100%" height="100%">
                          <ComposedChart data={chartData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis yAxisId="left" />
                            <YAxis yAxisId="right" orientation="right" />
                            <RechartsTooltip />
                            <Legend />
                            <Bar yAxisId="left" dataKey="investment" fill="#8884d8" name="Yatırım" />
                            <Line yAxisId="right" type="monotone" dataKey="roi" stroke="#82ca9d" name="ROI %" />
                          </ComposedChart>
                        </ResponsiveContainer>
                      </Box>
                    </CardBody>
                  </Card>

                  {/* Performance Ranking Table */}
                  <Card bg={cardBg}>
                    <CardHeader>
                      <Heading size="md">Performans Sıralaması</Heading>
                    </CardHeader>
                    <CardBody>
                      <TableContainer>
                        <Table variant="simple" size="sm">
                          <Thead>
                            <Tr>
                              <Th>Sıra</Th>
                              <Th>Robot/Strateji</Th>
                              <Th isNumeric>ROI (%)</Th>
                              <Th isNumeric>Yatırım (₺)</Th>
                              <Th isNumeric>Getiri (₺)</Th>
                              <Th>Performans</Th>
                            </Tr>
                          </Thead>
                          <Tbody>
                            {roiAnalysis.roiByRobot
                              .sort((a, b) => b.roi - a.roi)
                              .slice(0, 10)
                              .map((robot, index) => (
                                <Tr key={robot.robotId}>
                                  <Td>
                                    <Badge
                                      colorScheme={index < 3 ? 'gold' : 'gray'}
                                      variant={index < 3 ? 'solid' : 'subtle'}
                                    >
                                      #{index + 1}
                                    </Badge>
                                  </Td>
                                  <Td fontWeight="semibold">{robot.robotName}</Td>
                                  <Td isNumeric>
                                    <Text color={robot.roi > 0 ? successColor : errorColor}>
                                      {formatPercent(robot.roi)}
                                    </Text>
                                  </Td>
                                  <Td isNumeric>{formatCurrency(robot.investment)}</Td>
                                  <Td isNumeric>
                                    <Text color={robot.roi > 0 ? successColor : errorColor}>
                                      {formatCurrency((robot.investment * robot.roi) / 100)}
                                    </Text>
                                  </Td>
                                  <Td>
                                    <Badge
                                      colorScheme={
                                        robot.roi > 20 ? 'green' :
                                        robot.roi > 10 ? 'blue' :
                                        robot.roi > 5 ? 'orange' :
                                        robot.roi > 0 ? 'yellow' : 'red'
                                      }
                                      variant="subtle"
                                    >
                                      {robot.roi > 20 ? 'Mükemmel' :
                                       robot.roi > 10 ? 'İyi' :
                                       robot.roi > 5 ? 'Orta' :
                                       robot.roi > 0 ? 'Zayıf' : 'Kayıp'}
                                    </Badge>
                                  </Td>
                                </Tr>
                              ))}
                          </Tbody>
                        </Table>
                      </TableContainer>
                    </CardBody>
                  </Card>

                  {/* Side-by-side Comparison Tool */}
                  <Card bg={cardBg}>
                    <CardHeader>
                      <Heading size="md">Yan Yana Karşılaştırma</Heading>
                    </CardHeader>
                    <CardBody>
                      <SimpleGrid columns={{ base: 1, sm: 2, md: 2, lg: 3 }} spacing={{ base: 3, md: 4 }}>
                        {roiAnalysis.roiByRobot.slice(0, 3).map((robot, index) => (
                          <Card key={robot.robotId} variant="outline" bg={cardBg}>
                            <CardBody>
                              <VStack spacing={3} align="stretch">
                                <HStack justify="space-between">
                                  <Text fontWeight="bold" fontSize="sm">{robot.robotName}</Text>
                                  <Badge
                                    colorScheme={robot.roi > 0 ? 'green' : 'red'}
                                    variant="solid"
                                  >
                                    #{index + 1}
                                  </Badge>
                                </HStack>
                                <Divider />
                                <VStack spacing={2} align="stretch">
                                  <HStack justify="space-between">
                                    <Text fontSize="xs" color="gray.500">ROI:</Text>
                                    <Text fontSize="sm" fontWeight="bold" color={robot.roi > 0 ? successColor : errorColor}>
                                      {formatPercent(robot.roi)}
                                    </Text>
                                  </HStack>
                                  <HStack justify="space-between">
                                    <Text fontSize="xs" color="gray.500">Yatırım:</Text>
                                    <Text fontSize="sm">{formatCurrency(robot.investment)}</Text>
                                  </HStack>
                                  <HStack justify="space-between">
                                    <Text fontSize="xs" color="gray.500">Getiri:</Text>
                                    <Text fontSize="sm" color={robot.roi > 0 ? successColor : errorColor}>
                                      {formatCurrency((robot.investment * robot.roi) / 100)}
                                    </Text>
                                  </HStack>
                                </VStack>
                                <Progress
                                  value={Math.min(100, Math.max(0, robot.roi + 50))}
                                  colorScheme={robot.roi > 0 ? 'green' : 'red'}
                                  size="sm"
                                  borderRadius="md"
                                />
                              </VStack>
                            </CardBody>
                          </Card>
                        ))}
                      </SimpleGrid>
                    </CardBody>
                  </Card>
                </VStack>
              )}
            </VStack>
          </TabPanel>

          {/* Distribution Analysis Tab */}
          <TabPanel p={0} pt={6}>
            <VStack spacing={6} align="stretch">
              <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                {/* Investment Distribution */}
                <Card bg={cardBg}>
                  <CardHeader>
                    <Heading size="md">Yatırım Dağılımı</Heading>
                  </CardHeader>
                  <CardBody>
                    <Box h="350px">
                      <InteractivePieChart
                        data={robotROIData.map(robot => ({
                          name: robot.fullName,
                          value: robot.investment,
                          color: robot.color
                        }))}
                        height={350}
                        formatValue={formatCurrency}
                        showLegend={true}
                        viewMode={'donut' as 'donut'}
                        centerContent={
                          <VStack spacing={1}>
                            <Text fontSize="xl" fontWeight="bold">
                              {formatCurrency(roiAnalysis.totalInvestment)}
                            </Text>
                            <Text fontSize="sm" color="gray.500">
                              Toplam Yatırım
                            </Text>
                          </VStack>
                        }
                      />
                    </Box>
                  </CardBody>
                </Card>

                {/* ROI Distribution */}
                <Card bg={cardBg}>
                  <CardHeader>
                    <Heading size="md">ROI Dağılımı</Heading>
                  </CardHeader>
                  <CardBody>
                    <Box h="350px">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={robotROIData}
                            cx="50%"
                            cy="50%"
                            outerRadius={120}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, value }) => `${name}: ${formatPercent(value)}`}
                          >
                            {robotROIData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <RechartsTooltip formatter={(value: number) => [formatPercent(value), 'ROI']} />
                        </PieChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardBody>
                </Card>
              </SimpleGrid>

              {/* Symbol Performance Distribution */}
              {roiAnalysis.roiBySymbol && roiAnalysis.roiBySymbol.length > 0 && (
                <Card bg={cardBg}>
                  <CardHeader>
                    <Heading size="md">Sembol Performans Dağılımı</Heading>
                  </CardHeader>
                  <CardBody>
                    <Box h="300px">
                      <ResponsiveContainer width="100%" height="100%">
                        <ScatterChart data={roiAnalysis.roiBySymbol}>
                          <CartesianGrid />
                          <XAxis
                            type="number"
                            dataKey="trades"
                            name="İşlem Sayısı"
                            label={{ value: 'İşlem Sayısı', position: 'insideBottom', offset: -5 }}
                          />
                          <YAxis
                            type="number"
                            dataKey="roi"
                            name="ROI"
                            label={{ value: 'ROI (%)', angle: -90, position: 'insideLeft' }}
                          />
                          <RechartsTooltip
                            cursor={{ strokeDasharray: '3 3' }}
                            formatter={(value, name) => [
                              name === 'roi' ? formatPercent(value as number) : value,
                              name === 'roi' ? 'ROI' : 'İşlem Sayısı'
                            ]}
                            labelFormatter={(label) => `Sembol: ${label}`}
                          />
                          <Scatter name="Semboller" dataKey="roi" fill={accentColor} />
                        </ScatterChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardBody>
                </Card>
              )}
            </VStack>
          </TabPanel>

          {/* Risk Analysis Tab */}
          {showAdvancedMetrics && (
            <TabPanel p={0} pt={6}>
              <VStack spacing={6} align="stretch">
                <Alert status="info" borderRadius="lg">
                  <AlertIcon />
                  <Box>
                    <AlertTitle>Risk Analizi</AlertTitle>
                    <AlertDescription>
                      Gelişmiş risk metrikleri ve volatilite analizi. Bu veriler yatırım kararlarınızda rehber olarak kullanılabilir.
                    </AlertDescription>
                  </Box>
                </Alert>

                <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
                  {/* Risk Metrics */}
                  <Card bg={cardBg}>
                    <CardHeader>
                      <Heading size="md">Risk Metrikleri</Heading>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={4} align="stretch">
                        <HStack justify="space-between">
                          <Text>Volatilite</Text>
                          <Badge colorScheme={(roiAnalysis.volatility || 0) < 15 ? 'green' : 'orange'}>
                            {formatPercent(roiAnalysis.volatility || 0)}
                          </Badge>
                        </HStack>
                        <HStack justify="space-between">
                          <Text>Sharpe Oranı</Text>
                          <Badge colorScheme={(roiAnalysis.sharpeRatio || 0) > 1 ? 'green' : 'orange'}>
                            {formatNumber(roiAnalysis.sharpeRatio || 0)}
                          </Badge>
                        </HStack>
                        <HStack justify="space-between">
                          <Text>Maksimum Düşüş</Text>
                          <Badge colorScheme={(roiAnalysis.maxDrawdown || 0) < 10 ? 'green' : 'red'}>
                            {formatPercent(roiAnalysis.maxDrawdown || 0)}
                          </Badge>
                        </HStack>
                        <HStack justify="space-between">
                          <Text>VaR (95%)</Text>
                          <Badge colorScheme="purple">
                            {formatPercent(roiAnalysis.valueAtRisk95 || 0)}
                          </Badge>
                        </HStack>
                      </VStack>
                    </CardBody>
                  </Card>

                  {/* Risk-Return Scatter */}
                  <Card bg={cardBg}>
                    <CardHeader>
                      <Heading size="md">Risk-Getiri Analizi</Heading>
                    </CardHeader>
                    <CardBody>
                      <Box h="300px">
                        <ResponsiveContainer width="100%" height="100%">
                          <ScatterChart data={robotROIData}>
                            <CartesianGrid />
                            <XAxis
                              type="number"
                              dataKey="investment"
                              name="Yatırım"
                              label={{ value: 'Yatırım (₺)', position: 'insideBottom', offset: -5 }}
                            />
                            <YAxis
                              type="number"
                              dataKey="value"
                              name="ROI"
                              label={{ value: 'ROI (%)', angle: -90, position: 'insideLeft' }}
                            />
                            <RechartsTooltip
                              formatter={(value, name) => [
                                name === 'value' ? formatPercent(value as number) : formatCurrency(value as number),
                                name === 'value' ? 'ROI' : 'Yatırım'
                              ]}
                            />
                            <Scatter name="Robotlar" dataKey="value" fill={accentColor} />
                          </ScatterChart>
                        </ResponsiveContainer>
                      </Box>
                    </CardBody>
                  </Card>
                </SimpleGrid>
              </VStack>
            </TabPanel>
          )}
        </TabPanels>
      </Tabs>

      {/* Enhanced Summary and Comparison Section */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* ROI Summary Table */}
        <Card bg={cardBg}>
          <CardHeader>
            <Flex justify="space-between" align="center">
              <Heading size="md">ROI Özet Tablosu</Heading>
              <Badge colorScheme="blue" variant="subtle">
                {roiAnalysis.roiByRobot.length} Robot
              </Badge>
            </Flex>
          </CardHeader>
          <CardBody>
            <TableContainer>
              <Table size="sm">
                <Thead>
                  <Tr>
                    <Th>Sıra</Th>
                    <Th>Robot</Th>
                    <Th isNumeric>ROI (%)</Th>
                    <Th isNumeric>Yatırım</Th>
                    <Th>Durum</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {roiAnalysis.roiByRobot
                    .sort((a, b) => b.roi - a.roi)
                    .slice(0, 8)
                    .map((robot, index) => {
                      const benchmark = getPerformanceBenchmark(robot.roi);
                      return (
                        <Tr key={robot.robotId}>
                          <Td>
                            <Badge
                              colorScheme={index < 3 ? 'gold' : 'gray'}
                              variant={index < 3 ? 'solid' : 'subtle'}
                              size="sm"
                            >
                              #{index + 1}
                            </Badge>
                          </Td>
                          <Td>
                            <Text fontSize="sm" fontWeight="medium">
                              {robot.robotName}
                            </Text>
                          </Td>
                          <Td isNumeric>
                            <Text
                              fontSize="sm"
                              fontWeight="bold"
                              color={robot.roi > 0 ? successColor : errorColor}
                            >
                              {formatPercent(robot.roi)}
                            </Text>
                          </Td>
                          <Td isNumeric>
                            <Text fontSize="sm">
                              {formatCurrency(robot.investment)}
                            </Text>
                          </Td>
                          <Td>
                            <Badge
                              size="sm"
                              colorScheme={
                                robot.roi > 10 ? 'green' :
                                robot.roi > 0 ? 'blue' : 'red'
                              }
                              variant="subtle"
                            >
                              {benchmark.name}
                            </Badge>
                          </Td>
                        </Tr>
                      );
                    })}
                </Tbody>
              </Table>
            </TableContainer>
          </CardBody>
        </Card>

        {/* Performance Insights and Comparison */}
        <Card bg={cardBg}>
          <CardHeader>
            <Heading size="md">Performans İçgörüleri</Heading>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              {/* Best Performer */}
              <Box p={4} bg={useColorModeValue('green.50', 'green.900')} borderRadius="lg">
                <HStack justify="space-between" mb={2}>
                  <Text fontSize="sm" fontWeight="bold" color="green.600">
                    🏆 En İyi Performans
                  </Text>
                  <Icon as={FiAward} color="green.500" />
                </HStack>
                <Text fontSize="lg" fontWeight="bold">
                  {roiAnalysis.roiByRobot.sort((a, b) => b.roi - a.roi)[0]?.robotName || 'Veri Yok'}
                </Text>
                <Text fontSize="sm" color="green.600">
                  {formatPercent(roiAnalysis.roiByRobot.sort((a, b) => b.roi - a.roi)[0]?.roi || 0)} ROI
                </Text>
              </Box>

              {/* Portfolio Diversity */}
              <Box p={4} bg={useColorModeValue('blue.50', 'blue.900')} borderRadius="lg">
                <HStack justify="space-between" mb={2}>
                  <Text fontSize="sm" fontWeight="bold" color="blue.600">
                    📊 Portföy Çeşitliliği
                  </Text>
                  <Icon as={FiPieChart} color="blue.500" />
                </HStack>
                <Text fontSize="lg" fontWeight="bold">
                  {roiAnalysis.roiBySymbol?.length || 0} Farklı Sembol
                </Text>
                <Text fontSize="sm" color="blue.600">
                  {roiAnalysis.roiByRobot.length} Aktif Robot
                </Text>
              </Box>

              {/* Risk Assessment */}
              {showAdvancedMetrics && (
                <Box p={4} bg={useColorModeValue('purple.50', 'purple.900')} borderRadius="lg">
                  <HStack justify="space-between" mb={2}>
                    <Text fontSize="sm" fontWeight="bold" color="purple.600">
                      🛡️ Risk Değerlendirmesi
                    </Text>
                    <Icon as={FiShield} color="purple.500" />
                  </HStack>
                  <Text fontSize="lg" fontWeight="bold">
                    {(roiAnalysis.volatility || 0) < 15 ? 'Düşük' :
                     (roiAnalysis.volatility || 0) < 25 ? 'Orta' : 'Yüksek'} Risk
                  </Text>
                  <Text fontSize="sm" color="purple.600">
                    {formatPercent(roiAnalysis.volatility || 0)} Volatilite
                  </Text>
                </Box>
              )}

              {/* Real-time Status */}
              {liveUpdates && (
                <Box p={4} bg={useColorModeValue('orange.50', 'orange.900')} borderRadius="lg">
                  <HStack justify="space-between" mb={2}>
                    <Text fontSize="sm" fontWeight="bold" color="orange.600">
                      ⚡ Canlı Durum
                    </Text>
                    <Icon as={FiZap} color="orange.500" />
                  </HStack>
                  <Text fontSize="lg" fontWeight="bold">
                    {realtimeMetrics.openPositions} Açık Pozisyon
                  </Text>
                  <Text fontSize="sm" color="orange.600">
                    Bugün: {formatCurrency(realtimeMetrics.todaysPnl)}
                  </Text>
                </Box>
              )}
            </VStack>
          </CardBody>
        </Card>
      </SimpleGrid>
    </VStack>
  );
};

export default ROIDashboard;
