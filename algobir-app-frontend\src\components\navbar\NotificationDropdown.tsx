import React, { useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  MenuList,
  MenuItem,
  MenuDivider,
  IconButton,
  Badge,
  VStack,
  HStack,
  Text,
  Box,
  Spinner,
  Button,
  useColorModeValue,
  Portal,
  Flex,
  Icon,
  useToast,
  Tag,
  TagLabel
} from '@chakra-ui/react';
import { 
  FiBell, 
  FiCheckCircle, 
  FiExternalLink,
  FiInfo,
  FiAlertTriangle,
  FiAlertCircle,
  FiTrendingUp,
  FiActivity,
  FiUsers
} from 'react-icons/fi';
import { useNotifications } from '../../hooks/useNotifications';
import { Notification } from '../../types/notification';
import { formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Link as RouterLink } from 'react-router-dom';

interface NotificationDropdownProps {
  size?: 'sm' | 'md' | 'lg';
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({ size = 'md' }) => {
  const toast = useToast();
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    fetchNotifications
  } = useNotifications();

  // Debug bilgileri
  useEffect(() => {
    console.log('📊 NotificationDropdown State:', {
      notificationsCount: notifications.length,
      unreadCount,
      loading,
      error,
      notificationTypes: notifications.map((n: Notification) => n.type),
      tradeNotifications: notifications.filter((n: Notification) => n.type === 'trade_opened' || n.type === 'trade_closed').length,
      allNotifications: notifications
    });
  }, [notifications, unreadCount, loading, error]);

  // Color mode values
  const iconColor = useColorModeValue('gray.600', 'gray.300');
  const menuBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');
  const buttonHover = useColorModeValue('gray.100', 'gray.700');
  const unreadBg = useColorModeValue('blue.50', 'blue.900');
  const shadow = useColorModeValue(
    '0 4px 12px rgba(0, 0, 0, 0.15)',
    '0 4px 12px rgba(0, 0, 0, 0.3)'
  );

  // Bildirim ikonunu al
  const getNotificationIcon = (type: string, severity: string) => {
    switch (type) {
      case 'trade_opened':
      case 'trade_closed':
        return FiTrendingUp;
      case 'admin_announcement':
        return severity === 'error' ? FiAlertCircle : 
               severity === 'warning' ? FiAlertTriangle : FiInfo;
      case 'robot_status':
        return FiActivity;
      case 'subscription_update':
        return FiUsers;
      default:
        return FiInfo;
    }
  };

  // Şiddete göre renk al
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'success':
        return 'green';
      case 'warning':
        return 'orange';
      case 'error':
        return 'red';
      default:
        return 'blue';
    }
  };

  // Bildirim tipine göre kategori adı
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'trade_opened':
        return 'Pozisyon Açıldı';
      case 'trade_closed':
        return 'Pozisyon Kapandı';
      case 'admin_announcement':
        return 'Admin Duyurusu';
      case 'robot_status':
        return 'Robot Durumu';
      case 'subscription_update':
        return 'Abonelik';
      default:
        return 'Sistem';
    }
  };

  // Bildirimi işle (okundu işaretle ve aksiyonu çalıştır)
  const handleNotificationClick = async (notification: Notification) => {
    if (!notification.is_read) {
      await markAsRead(notification.id);
    }

    // Eğer aksiyon URL'i varsa yönlendir
    if (notification.action_url) {
      // External link kontrolü
      if (notification.action_url.startsWith('http')) {
        window.open(notification.action_url, '_blank');
      }
      // Diğer durumlar için router navigation gerekli
    }
  };

  // Tümünü okundu işaretle
  const handleMarkAllAsRead = async () => {
    try {
      const result = await markAllAsRead();
      toast({
        title: 'Bildirimler Güncellendi',
        description: `${result} bildirim okundu olarak işaretlendi`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Hata',
        description: 'Bildirimler güncellenirken bir hata oluştu',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <Menu placement="bottom-end">
      <MenuButton
        as={IconButton}
        aria-label="Bildirimler"
        icon={
          <Box position="relative">
            <Icon as={FiBell} />
            {unreadCount > 0 && (
              <Badge
                colorScheme="red"
                position="absolute"
                top="-8px"
                right="-8px"
                borderRadius="full"
                fontSize="xs"
                minW="18px"
                h="18px"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </Box>
        }
        variant="ghost"
        size={size}
        borderRadius="full"
        color={iconColor}
        _hover={{ bg: buttonHover }}
        _active={{ bg: buttonHover }}
        _focus={{
          boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
          outline: 'none',
        }}
      />

      <Portal>
        <MenuList
          data-testid="notification-dropdown-menu"
          bg={menuBg}
          borderColor={borderColor}
          boxShadow={shadow}
          borderRadius="xl"
          p="0"
          minW="380px"
          maxW="400px"
          zIndex={9999}
          maxH="500px"
          overflowY="auto"
        >
          {/* Header */}
          <Box p="4" borderBottom="1px solid" borderColor={borderColor}>
            <Flex justify="space-between" align="center">
              <Text fontSize="lg" fontWeight="semibold" color={textColor} data-testid="notification-dropdown-header">
                Bildirimler
              </Text>
              {unreadCount > 0 && (
                <Button
                  data-testid="mark-all-read-button"
                  size="sm"
                  variant="ghost"
                  colorScheme="blue"
                  onClick={handleMarkAllAsRead}
                  leftIcon={<Icon as={FiCheckCircle} />}
                >
                  Tümünü Okundu İşaretle
                </Button>
              )}
            </Flex>
          </Box>

          {/* Content */}
          {loading && (
            <Box p="6" textAlign="center" data-testid="notification-loading">
              <Spinner size="md" color="blue.500" />
              <Text mt="2" color={secondaryTextColor}>
                Bildirimler yükleniyor...
              </Text>
            </Box>
          )}

          {error && (
            <Box p="4" textAlign="center">
              <Text color="red.500" fontSize="sm">
                {error}
              </Text>
              <Button
                size="sm"
                mt="2"
                onClick={() => fetchNotifications()}
                variant="outline"
              >
                Tekrar Dene
              </Button>
            </Box>
          )}

          {!loading && !error && notifications.length === 0 && (
            <Box p="6" textAlign="center">
              <Icon as={FiBell} size="32" color={secondaryTextColor} mb="2" />
              <Text color={secondaryTextColor} fontSize="sm">
                Henüz bildirim yok
              </Text>
            </Box>
          )}

          {!loading && !error && notifications.length > 0 && (
            <VStack spacing="0" align="stretch">
              {notifications.slice(0, 10).map((notification, index) => {
                const IconComponent = getNotificationIcon(notification.type, notification.severity);
                const severityColor = getSeverityColor(notification.severity);
                
                return (
                  <Box key={notification.id}>
                    <MenuItem
                      as="div"
                      data-testid="notification-dropdown-item"
                      p="4"
                      bg={!notification.is_read ? unreadBg : 'transparent'}
                      _hover={{ bg: buttonHover }}
                      cursor="pointer"
                      onClick={() => handleNotificationClick(notification)}
                      transition="all 0.2s"
                    >
                      <HStack align="start" spacing="3" w="100%">
                        {/* Icon */}
                        <Icon
                          as={IconComponent}
                          color={`${severityColor}.500`}
                          fontSize="lg"
                          mt="1"
                          flexShrink="0"
                        />

                        {/* Content */}
                        <VStack align="start" spacing="1" flex="1">
                          <HStack w="100%" justify="space-between" align="start">
                            <Text
                              fontSize="sm"
                              fontWeight={!notification.is_read ? "semibold" : "medium"}
                              color={textColor}
                              lineHeight="1.3"
                              noOfLines={2}
                            >
                              {notification.title}
                            </Text>
                            
                            {!notification.is_read && (
                              <Box
                                w="8px"
                                h="8px"
                                borderRadius="full"
                                bg="blue.500"
                                flexShrink="0"
                                mt="1"
                              />
                            )}
                          </HStack>

                          <Text
                            fontSize="xs"
                            color={secondaryTextColor}
                            lineHeight="1.3"
                            noOfLines={2}
                          >
                            {notification.message}
                          </Text>

                          <HStack spacing="2" mt="1">
                            <Tag size="sm" colorScheme={severityColor} variant="subtle">
                              <TagLabel fontSize="xs">
                                {getTypeLabel(notification.type)}
                              </TagLabel>
                            </Tag>

                            <Text fontSize="xs" color={secondaryTextColor}>
                              {formatDistanceToNow(new Date(notification.created_at), {
                                addSuffix: true,
                                locale: tr
                              })}
                            </Text>
                          </HStack>

                          {notification.action_url && notification.action_label && (
                            <Button
                              size="xs"
                              variant="outline"
                              colorScheme="blue"
                              mt="2"
                              rightIcon={<Icon as={FiExternalLink} />}
                            >
                              {notification.action_label}
                            </Button>
                          )}
                        </VStack>
                      </HStack>
                    </MenuItem>
                    
                    {index < notifications.length - 1 && (
                      <MenuDivider m="0" borderColor={borderColor} />
                    )}
                  </Box>
                );
              })}
            </VStack>
          )}

          {/* Footer */}
          {notifications.length > 0 && (
            <>
              <MenuDivider />
              <Box p="3">
                <Button
                  as={RouterLink}
                  to="/notifications"
                  data-testid="view-all-notifications-link"
                  size="sm"
                  variant="ghost"
                  w="100%"
                  justifyContent="center"
                >
                  Tüm Bildirimleri Görüntüle
                </Button>
              </Box>
            </>
          )}
        </MenuList>
      </Portal>
    </Menu>
  );
};

export default NotificationDropdown; 