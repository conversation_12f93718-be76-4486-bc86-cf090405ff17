export type StatsData = {
  total_profit_loss: number | null;
  win_rate: number | null;
  total_trades: number | null;
  winning_trades: number | null;
  losing_trades: number | null;
};

export type StatsError = {
  message: string;
};

export type BasicPnLStats = {
  total_pnl: number | string;
  winning_trades_count: number;
  losing_trades_count: number;
  win_rate: number | string;
};

export type PnLStats = {
  totalPnl: number;
  winRate: number;
  winningTrades: number;
  losingTrades: number;
};

export type Stats = {
  totalTrades: number;
  tradesToday: number;
  tradesThisWeek: number;
  buyTrades: number;
  tpTrades: number;
  stopTrades: number;
};

// Yeni performans analitikleri türleri
export interface UserPerformanceAnalytics {
  total_net_pnl: number;
  total_investment: number;
  total_net_pnl_percentage: number;
  total_trades: number;
  winning_trades_count: number;
  losing_trades_count: number;
  win_rate: number;
  profit_factor: number;
  average_pnl_per_trade: number;
  average_winning_trade_pnl: number;
  average_losing_trade_pnl: number;
  largest_winning_trade_pnl: number;
  largest_losing_trade_pnl: number;
  average_trade_duration_seconds: number;
  max_drawdown_percentage: number;
  longest_winning_streak: number;
  longest_losing_streak: number;
  // Yeni P&L dağılım verisi
  pnl_distribution: PnLDistributionPoint[];
}

// P&L dağılım noktası
export interface PnLDistributionPoint {
  pnl: number;
}

// Enhanced PnL data point with new fields
export interface PnlDataPoint {
  date: string;
  period_pnl: number;
  cumulative_pnl: number;
  moving_average: number;
  trade_count: number;
  period_investment: number;
  cumulative_investment: number;
  win_rate: number;
  avg_trade_size: number;
  cumulative_roi: number;
}

export interface PnlOverTimeSeriesData {
  period_type: string;
  moving_average_period: number;
  data_points: PnlDataPoint[];
}

export interface SymbolPerformanceData {
  symbol: string;
  net_pnl: number;
  total_trades_count: number;
  win_rate: number;
  winning_trades: number;
  losing_trades: number;
  average_pnl_per_trade: number;
  best_trade: number;
  worst_trade: number;
  total_invested: number;
  total_wins: number;
  total_losses: number;
}

export interface SymbolPerformanceBreakdown {
  symbols: SymbolPerformanceData[];
}

export interface UserSubscription {
  subscription_id: string;
  robot_id: string;
  robot_name: string;
  robot_description: string;
  is_active: boolean;
  started_at: string;
  expires_at: string | null;
}

export interface UserActiveSubscriptions {
  subscriptions: UserSubscription[];
}

// Yeni selectable robot türü
export interface SelectableRobot {
  robot_id: string;
  robot_name: string;
  robot_description: string;
  seller_username: string;
  started_at: string;
  expires_at: string | null;
}

export interface SelectableRobotsData {
  robots: SelectableRobot[];
}

export type AnalyticsContextType = 'all_trades' | 'solo_robot' | 'bro_robot_specific';

export interface AnalyticsContext {
  type: AnalyticsContextType;
  robotId?: string;
  robotName?: string;
}

// P&L Histogram için veri yapısı
export interface PnLHistogramBin {
  range: string;
  count: number;
  percentage: number;
  minValue: number;
  maxValue: number;
}

export interface PnLHistogramData {
  bins: PnLHistogramBin[];
  totalTrades: number;
}

// Trade volume over time için
export interface TradeVolumeDataPoint {
  date: string;
  trade_count: number;
  total_volume: number;
}

// Risk Reward Analysis
export interface RiskBucket {
  risk_bucket: string;
  trade_count: number;
  avg_pnl: number;
  total_pnl: number;
  avg_trade_size: number;
  min_risk_pct: number;
  max_risk_pct: number;
}

export interface RiskRewardAnalysis {
  risk_buckets: RiskBucket[];
}

// Weekly Performance Analysis
export interface WeeklyPerformanceData {
  day_name: string;
  day_number: number;
  trade_count: number;
  avg_pnl: number;
  total_pnl: number;
  win_rate: number;
  pnl_volatility: number | null;
}

export interface WeeklyPerformanceAnalysis {
  weekly_performance: WeeklyPerformanceData[];
}

// Drawdown Analysis
export interface DrawdownDataPoint {
  date: string;
  running_pnl: number;
  peak_pnl: number;
  drawdown: number;
  drawdown_percentage: number;
}

export interface DrawdownAnalysis {
  drawdown_series: DrawdownDataPoint[];
  max_drawdown: number;
  max_drawdown_duration: number;
}

// Enhanced P&L Histogram with better binning
export interface EnhancedPnLBin {
  label: string;
  min_value: number;
  max_value: number;
  count: number;
  percentage: number;
  total_pnl: number;
  avg_pnl: number;
}

export interface EnhancedPnLHistogram {
  bins: EnhancedPnLBin[];
  total_trades: number;
  positive_trades: number;
  negative_trades: number;
  zero_trades: number;
}

// Robot detayları için tipler
export interface RobotDetails {
  id: string;
  name: string;
  description: string;
  image_url?: string;
  strategy_type?: string;
  version: string;
  is_public: boolean;
  price: number;
  subscription_period: number;
  investment_amount_per_position: number;
  show_statistics_publicly: boolean;
  created_at: string;
  seller: {
    id: string;
    username: string;
    full_name?: string;
    avatar_url?: string;
    bio?: string;
    display_robots_publicly: boolean;
  };
}

export interface RobotWeeklyStatistics {
  show_statistics: boolean;
  message?: string;
  period?: string;
  simulated_investment_per_trade?: number;
  summary?: {
    total_trades: number;
    total_pnl: number;
    total_investment: number;
    overall_win_rate: number;
    roi_percentage: number;
  };
  daily_breakdown?: {
    date: string;
    trade_count: number;
    daily_pnl: number;
    daily_investment: number;
    cumulative_pnl: number;
    cumulative_investment: number;
    daily_win_rate: number;
    trades: {
      symbol: string;
      side: string;
      pnl: number;
      investment: number;
    }[];
  }[];
}

// Varsa diğer gerekli tipler buraya eklenebilir
export type GenericType = unknown; // Geçici placeholder, 'any' yerine daha güvenli 'unknown' kullanımı 