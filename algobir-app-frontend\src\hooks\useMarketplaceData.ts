import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '../supabaseClient';
import { Robot } from '../types/robot';
import { useAuth } from '../context/AuthContext';

interface UseMarketplaceDataReturn {
  robots: Robot[];
  trendingRobots: Robot[];
  recentRobots: Robot[];
  userSubscriptions: string[];
  isLoading: boolean;
  error: any | null;
  refetch: () => Promise<void>;
}

interface FilterOptions {
  searchTerm?: string;
}

interface SubscriptionWithDetails {
  subscription_id: string;
  robot_id: string;
  is_active: boolean;
}

export function useMarketplaceData(filterOptions: FilterOptions = {}): UseMarketplaceDataReturn & { userSubscriptionsDetailed: SubscriptionWithDetails[] } {
  const { user } = useAuth();
  const [robots, setRobots] = useState<Robot[]>([]);
  const [trendingRobots, setTrendingRobots] = useState<Robot[]>([]);
  const [recentRobots, setRecentRobots] = useState<Robot[]>([]);
  const [userSubscriptions, setUserSubscriptions] = useState<string[]>([]);
  const [userSubscriptionsDetailed, setUserSubscriptionsDetailed] = useState<SubscriptionWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<any | null>(null);

  const { searchTerm } = filterOptions;

  // Performance optimization: Cache for robots data (1 minute cache)
  const robotsCache = useRef<{ data: Robot[]; timestamp: number; searchTerm?: string } | null>(null);
  const subscriptionsCache = useRef<{ data: SubscriptionWithDetails[]; timestamp: number; userId: string } | null>(null);
  const CACHE_DURATION = 60000; // 1 minute

  // Kullanıcının aboneliklerini getir
  const fetchUserSubscriptions = useCallback(async (userId: string) => {
    try {
      // Check cache first
      const now = Date.now();
      if (subscriptionsCache.current && 
          subscriptionsCache.current.userId === userId &&
          (now - subscriptionsCache.current.timestamp < CACHE_DURATION)) {
        console.log('Using cached subscriptions data');
        const cachedData = subscriptionsCache.current.data;
        setUserSubscriptions(cachedData.map(sub => sub.robot_id));
        setUserSubscriptionsDetailed(cachedData);
        return;
      }

      const { data, error } = await supabase
        .from('subscriptions')
        .select('subscription_id: id, robot_id, is_active')
        .eq('user_id', userId)
        .eq('is_deleted', false);
      
      if (error) throw error;
      
      if (data) {
        // Cache the data
        subscriptionsCache.current = {
          data,
          timestamp: now,
          userId
        };
        
        setUserSubscriptions(data.map(sub => sub.robot_id));
        setUserSubscriptionsDetailed(data);
      }
    } catch (err: any) {
      console.error('Abonelikler çekilirken hata oluştu:', err);
      // Abonelikler alınamazsa ana hata state'ini güncellemiyoruz
      // sadece logla bırakıyoruz ki kullanıcı hala robotları görebisin
    }
  }, []);

  // Robotları getir
  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Check cache first (if no search term or same search term)
      const now = Date.now();
      if (robotsCache.current && 
          robotsCache.current.searchTerm === searchTerm &&
          (now - robotsCache.current.timestamp < CACHE_DURATION)) {
        console.log('Using cached robots data');
        const cachedData = robotsCache.current.data;
        setRobots(cachedData);
        
        // Process trending and recent from cache
        const sortedByPerformance = [...cachedData];
        sortedByPerformance.sort((a, b) => {
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        setTrendingRobots(sortedByPerformance.slice(0, 6));
        
        const recent = [...cachedData];
        recent.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        setRecentRobots(recent.slice(0, 6));
        setIsLoading(false);
        return;
      }

      // Tüm robotları çek (public ve silinmemiş olanlar) - Arama ve filtreleri entegre et
      let query = supabase
        .from('v_marketplace_robots')
        .select(`
          id, name, description, image_url, strategy_type, is_public, created_at, updated_at, price,
          seller_id, seller_username, seller_full_name, seller_avatar_url, seller_url_slug, subscription_period
        `);

      // Arama terimi filtresi
      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
      }

      // Sıralama: oluşturma tarihine göre sırala
      query = query
        .eq('is_public', true)
        .filter('deleted_at', 'is', null)
        .order('created_at', { ascending: false });
      
      const { data, error: fetchError } = await query;
      
      if (fetchError) throw fetchError;
      
      const robotsData = data || [];
      setRobots(robotsData);
      
      // Cache the data
      robotsCache.current = {
        data: robotsData,
        timestamp: now,
        searchTerm
      };
      
      if (data) {
        // Trend robotlar - şimdilik en son güncellenenler olarak gösterelim
        // Her bir robotun monthly_gain değerine göre sıralama yaparak "performans trendini" gösterebiliriz
        const sortedByPerformance = [...data] as Robot[];
        // monthly_gain yok, sadece created_at ile sırala
        sortedByPerformance.sort((a, b) => {
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        setTrendingRobots(sortedByPerformance.slice(0, 6));
        
        // Yeni eklenenler (oluşturma tarihine göre)
        const recent = [...data] as Robot[];
        recent.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
        setRecentRobots(recent.slice(0, 6));
      }
    } catch (err: any) {
      console.error('Robotlar çekilirken hata oluştu:', err);
      setError(err);
      // Hata durumunda boş diziler ayarla
      setRobots([]);
      setTrendingRobots([]);
      setRecentRobots([]);
    } finally {
      setIsLoading(false);
    }
  }, [searchTerm]);

  // Component mount olduğunda robotları getir
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Kullanıcı değiştiğinde kullanıcının aboneliklerini getir
  useEffect(() => {
    if (user?.id) {
      fetchUserSubscriptions(user.id);
    } else {
      setUserSubscriptions([]);
    }
  }, [user, fetchUserSubscriptions]);

  return { 
    robots, 
    trendingRobots, 
    recentRobots, 
    userSubscriptions, 
    isLoading, 
    error, 
    refetch: fetchData,
    userSubscriptionsDetailed
  };
} 