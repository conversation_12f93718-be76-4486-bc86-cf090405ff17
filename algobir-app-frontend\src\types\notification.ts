export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'trade_opened' | 'trade_closed' | 'admin_announcement' | 'robot_status' | 'system_alert' | 'subscription_update';
  severity: 'info' | 'warning' | 'error' | 'success';
  is_read: boolean;
  metadata?: Record<string, any>;
  action_url?: string;
  action_label?: string;
  created_at: string;
  expires_at?: string;
}

export interface NotificationPreferences {
  user_id: string;
  email_notifications: boolean;
  push_notifications: boolean;
  trade_notifications: boolean;
  robot_notifications: boolean;
  admin_notifications: boolean;
  subscription_notifications: boolean;
  created_at: string;
  updated_at: string;
}

export interface AdminAnnouncement {
  id: string;
  admin_user_id?: string;
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'error' | 'success';
  target_audience: 'all' | 'solo_users' | 'bro_sellers' | 'bro_subscribers' | 'premium_users';
  is_active: boolean;
  expires_at?: string;
  action_url?: string;
  action_label?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface CreateNotificationParams {
  user_id: string;
  title: string;
  message: string;
  type?: string;
  severity?: string;
  metadata?: Record<string, any>;
  action_url?: string;
  action_label?: string;
  expires_at?: string;
}

export interface CreateAnnouncementParams {
  title: string;
  message: string;
  target_audience?: string;
  severity?: string;
  expires_at?: string;
  action_url?: string;
  action_label?: string;
  metadata?: Record<string, any>;
}

export interface NotificationStats {
  unreadCount: number;
  totalCount: number;
} 