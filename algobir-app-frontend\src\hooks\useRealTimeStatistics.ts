import { useEffect, useRef, useState } from 'react';
import { supabase } from '../supabaseClient';
import { useAuth } from '../context/AuthContext';
import { EnhancedStatsData } from './useEnhancedStatistics';



interface UseRealTimeStatisticsProps {
  stats: EnhancedStatsData | null;
  onStatsUpdate: (updatedStats: EnhancedStatsData) => void;
  enabled?: boolean;
  updateInterval?: number; // in milliseconds
}

export const useRealTimeStatistics = ({
  stats,
  onStatsUpdate,
  enabled = true,
  updateInterval = 30000 // 30 seconds default
}: UseRealTimeStatisticsProps) => {
  const { user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const [updateCount, setUpdateCount] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const subscriptionRef = useRef<any>(null);

  // Real-time subscription for trade updates
  useEffect(() => {
    if (!enabled || !user || !stats) return;

    const setupRealtimeSubscription = async () => {
      try {
        // Subscribe to trades table changes
        const tradesSubscription = supabase
          .channel('trades_realtime')
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'trades',
              filter: `user_id=eq.${user.id}`
            },
            (payload) => {
              console.log('Real-time trade update:', payload);
              handleTradeUpdate(payload);
            }
          )
          .subscribe((status) => {
            console.log('Trades subscription status:', status);
            setIsConnected(status === 'SUBSCRIBED');
          });

        subscriptionRef.current = tradesSubscription;

        // Set up periodic updates for calculated metrics
        if (updateInterval > 0) {
          intervalRef.current = setInterval(() => {
            fetchLatestMetrics();
          }, updateInterval);
        }

      } catch (error) {
        console.error('Error setting up real-time subscription:', error);
        setIsConnected(false);
      }
    };

    setupRealtimeSubscription();

    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [enabled, user, stats, updateInterval]);

  // Handle trade updates
  const handleTradeUpdate = async (payload: any) => {
    if (!stats) return;

    try {
      const { eventType, new: newRecord, old: oldRecord } = payload;
      
      // Update stats based on the type of change
      let updatedStats = { ...stats };

      switch (eventType) {
        case 'INSERT':
          if (newRecord.position_status === 'Kapalı' && newRecord.pnl !== null) {
            updatedStats = await recalculateStatsWithNewTrade(updatedStats, newRecord);
          }
          break;
        
        case 'UPDATE':
          if (newRecord.position_status === 'Kapalı' && oldRecord.position_status !== 'Kapalı') {
            updatedStats = await recalculateStatsWithNewTrade(updatedStats, newRecord);
          } else if (oldRecord.position_status === 'Kapalı' && newRecord.position_status !== 'Kapalı') {
            updatedStats = await recalculateStatsWithRemovedTrade(updatedStats, oldRecord);
          }
          break;
        
        case 'DELETE':
          if (oldRecord.position_status === 'Kapalı') {
            updatedStats = await recalculateStatsWithRemovedTrade(updatedStats, oldRecord);
          }
          break;
      }

      // Update real-time metrics
      updatedStats.realtimeMetrics = {
        ...updatedStats.realtimeMetrics,
        currentPnl: updatedStats.totalPnl,
        todaysPnl: await calculateTodaysPnl(),
        openPositions: await getOpenPositionsCount(),
        activeRobots: await getActiveRobotsCount()
      };

      updatedStats.lastUpdateTime = new Date().toISOString();
      updatedStats.liveUpdates = true;

      setLastUpdate(new Date().toISOString());
      setUpdateCount(prev => prev + 1);
      onStatsUpdate(updatedStats);

    } catch (error) {
      console.error('Error handling trade update:', error);
    }
  };

  // Fetch latest metrics periodically
  const fetchLatestMetrics = async () => {
    if (!user || !stats) return;

    try {
      const [todaysPnl, openPositions, activeRobots] = await Promise.all([
        calculateTodaysPnl(),
        getOpenPositionsCount(),
        getActiveRobotsCount()
      ]);

      const updatedStats = {
        ...stats,
        realtimeMetrics: {
          ...stats.realtimeMetrics,
          todaysPnl,
          openPositions,
          activeRobots
        },
        lastUpdateTime: new Date().toISOString()
      };

      setLastUpdate(new Date().toISOString());
      onStatsUpdate(updatedStats);

    } catch (error) {
      console.error('Error fetching latest metrics:', error);
    }
  };

  // Helper functions for calculations
  const recalculateStatsWithNewTrade = async (currentStats: EnhancedStatsData, newTrade: any) => {
    // This would trigger a partial recalculation
    // For now, we'll just update basic metrics
    const updatedStats = { ...currentStats };
    
    if (newTrade.pnl > 0) {
      updatedStats.winningTrades += 1;
    } else {
      updatedStats.losingTrades += 1;
    }
    
    updatedStats.totalTrades += 1;
    updatedStats.totalPnl += newTrade.pnl;
    updatedStats.winRate = (updatedStats.winningTrades / updatedStats.totalTrades) * 100;
    
    return updatedStats;
  };

  const recalculateStatsWithRemovedTrade = async (currentStats: EnhancedStatsData, removedTrade: any) => {
    const updatedStats = { ...currentStats };
    
    if (removedTrade.pnl > 0) {
      updatedStats.winningTrades -= 1;
    } else {
      updatedStats.losingTrades -= 1;
    }
    
    updatedStats.totalTrades -= 1;
    updatedStats.totalPnl -= removedTrade.pnl;
    updatedStats.winRate = updatedStats.totalTrades > 0 ? 
      (updatedStats.winningTrades / updatedStats.totalTrades) * 100 : 0;
    
    return updatedStats;
  };

  const calculateTodaysPnl = async (): Promise<number> => {
    if (!user) return 0;

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const { data, error } = await supabase
      .from('trades')
      .select('pnl')
      .eq('user_id', user.id)
      .eq('position_status', 'Kapalı')
      .gte('pnl_calculated_at', today.toISOString())
      .not('pnl', 'is', null);

    if (error) {
      console.error('Error calculating today\'s P&L:', error);
      return 0;
    }

    return data?.reduce((sum, trade) => sum + (trade.pnl || 0), 0) || 0;
  };

  const getOpenPositionsCount = async (): Promise<number> => {
    if (!user) return 0;

    const { count, error } = await supabase
      .from('trades')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('position_status', 'Açık');

    if (error) {
      console.error('Error getting open positions count:', error);
      return 0;
    }

    return count || 0;
  };

  const getActiveRobotsCount = async (): Promise<number> => {
    if (!user) return 0;

    const { data, error } = await supabase
      .from('trades')
      .select('robot_id')
      .eq('user_id', user.id)
      .eq('position_status', 'Açık')
      .not('robot_id', 'is', null);

    if (error) {
      console.error('Error getting active robots count:', error);
      return 0;
    }

    const uniqueRobots = new Set(data?.map(trade => trade.robot_id));
    return uniqueRobots.size + (data?.some(trade => !trade.robot_id) ? 1 : 0); // +1 for solo if exists
  };

  // Manual refresh function
  const refreshStats = async () => {
    await fetchLatestMetrics();
  };

  return {
    isConnected,
    lastUpdate,
    updateCount,
    refreshStats,
    enabled
  };
};
