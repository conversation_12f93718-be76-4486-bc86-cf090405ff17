import { useRef, useCallback } from 'react';

/**
 * Cache entry structure
 */
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  userId?: string;
  key: string;
}

/**
 * Cache configuration
 */
export interface CacheConfig {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum number of entries
  userSpecific?: boolean; // Whether cache should be user-specific
}

/**
 * Cache operations interface
 */
export interface CacheOperations<T> {
  get: (key: string, userId?: string) => T | null;
  set: (key: string, data: T, userId?: string) => void;
  has: (key: string, userId?: string) => boolean;
  delete: (key: string, userId?: string) => void;
  clear: () => void;
  isStale: (key: string, userId?: string) => boolean;
  getStats: () => { size: number; hitRate: number };
}

/**
 * Advanced caching hook with LRU eviction and user-specific caching
 * 
 * @param config - Cache configuration
 * @returns Cache operations
 */
export function useCache<T>(config: CacheConfig = {}): CacheOperations<T> {
  const {
    ttl = 300000, // 5 minutes default
    maxSize = 100,
    userSpecific = true
  } = config;

  const cache = useRef<Map<string, CacheEntry<T>>>(new Map());
  const accessOrder = useRef<string[]>([]);
  const stats = useRef({ hits: 0, misses: 0 });

  // Generate cache key
  const generateKey = useCallback((key: string, userId?: string): string => {
    return userSpecific && userId ? `${userId}:${key}` : key;
  }, [userSpecific]);

  // Update access order for LRU
  const updateAccessOrder = useCallback((key: string) => {
    const index = accessOrder.current.indexOf(key);
    if (index > -1) {
      accessOrder.current.splice(index, 1);
    }
    accessOrder.current.push(key);
  }, []);

  // Evict least recently used entries
  const evictLRU = useCallback(() => {
    while (cache.current.size >= maxSize && accessOrder.current.length > 0) {
      const oldestKey = accessOrder.current.shift();
      if (oldestKey) {
        cache.current.delete(oldestKey);
      }
    }
  }, [maxSize]);

  // Check if entry is stale
  const isStale = useCallback((key: string, userId?: string): boolean => {
    const cacheKey = generateKey(key, userId);
    const entry = cache.current.get(cacheKey);
    
    if (!entry) return true;
    
    return Date.now() - entry.timestamp > ttl;
  }, [generateKey, ttl]);

  // Get data from cache
  const get = useCallback((key: string, userId?: string): T | null => {
    const cacheKey = generateKey(key, userId);
    const entry = cache.current.get(cacheKey);

    if (!entry) {
      stats.current.misses++;
      return null;
    }

    // Check if entry is stale
    if (Date.now() - entry.timestamp > ttl) {
      cache.current.delete(cacheKey);
      const index = accessOrder.current.indexOf(cacheKey);
      if (index > -1) {
        accessOrder.current.splice(index, 1);
      }
      stats.current.misses++;
      return null;
    }

    // Update access order
    updateAccessOrder(cacheKey);
    stats.current.hits++;
    return entry.data;
  }, [generateKey, ttl, updateAccessOrder]);

  // Set data in cache
  const set = useCallback((key: string, data: T, userId?: string): void => {
    const cacheKey = generateKey(key, userId);
    
    // Evict old entries if necessary
    evictLRU();

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      userId,
      key: cacheKey
    };

    cache.current.set(cacheKey, entry);
    updateAccessOrder(cacheKey);
  }, [generateKey, evictLRU, updateAccessOrder]);

  // Check if key exists in cache
  const has = useCallback((key: string, userId?: string): boolean => {
    const cacheKey = generateKey(key, userId);
    const entry = cache.current.get(cacheKey);
    
    if (!entry) return false;
    
    // Check if stale
    if (Date.now() - entry.timestamp > ttl) {
      cache.current.delete(cacheKey);
      const index = accessOrder.current.indexOf(cacheKey);
      if (index > -1) {
        accessOrder.current.splice(index, 1);
      }
      return false;
    }
    
    return true;
  }, [generateKey, ttl]);

  // Delete specific entry
  const deleteEntry = useCallback((key: string, userId?: string): void => {
    const cacheKey = generateKey(key, userId);
    cache.current.delete(cacheKey);
    
    const index = accessOrder.current.indexOf(cacheKey);
    if (index > -1) {
      accessOrder.current.splice(index, 1);
    }
  }, [generateKey]);

  // Clear all cache
  const clear = useCallback((): void => {
    cache.current.clear();
    accessOrder.current = [];
    stats.current = { hits: 0, misses: 0 };
  }, []);

  // Get cache statistics
  const getStats = useCallback(() => {
    const total = stats.current.hits + stats.current.misses;
    const hitRate = total > 0 ? (stats.current.hits / total) * 100 : 0;
    
    return {
      size: cache.current.size,
      hitRate: Math.round(hitRate * 100) / 100
    };
  }, []);

  return {
    get,
    set,
    has,
    delete: deleteEntry,
    clear,
    isStale,
    getStats
  };
}

/**
 * Global cache instance for sharing data across components
 */
let globalCache: Map<string, any> | null = null;

export function useGlobalCache<T>(namespace: string = 'default'): CacheOperations<T> {
  if (!globalCache) {
    globalCache = new Map();
  }

  const cache = useCache<T>({
    ttl: 600000, // 10 minutes for global cache
    maxSize: 200,
    userSpecific: true
  });

  // Wrap operations with namespace
  return {
    get: (key: string, userId?: string) => cache.get(`${namespace}:${key}`, userId),
    set: (key: string, data: T, userId?: string) => cache.set(`${namespace}:${key}`, data, userId),
    has: (key: string, userId?: string) => cache.has(`${namespace}:${key}`, userId),
    delete: (key: string, userId?: string) => cache.delete(`${namespace}:${key}`, userId),
    clear: cache.clear,
    isStale: (key: string, userId?: string) => cache.isStale(`${namespace}:${key}`, userId),
    getStats: cache.getStats
  };
}
