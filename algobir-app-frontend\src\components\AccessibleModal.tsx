import React, { useEffect, useRef, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  ModalCloseButton,
  Button,
  ButtonGroup,
  Text,
  Box,
  ModalProps,
  useDisclosure
} from '@chakra-ui/react';
import { ScreenReaderUtils } from '../utils/accessibilityUtils';

/**
 * Accessible Modal Props
 */
interface AccessibleModalProps extends Omit<ModalProps, 'children'> {
  title: string;
  description?: string;
  children: React.ReactNode;
  primaryAction?: {
    label: string;
    onClick: () => void;
    isLoading?: boolean;
    colorScheme?: string;
    variant?: string;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    variant?: string;
  };
  closeOnOverlayClick?: boolean;
  closeOnEsc?: boolean;
  returnFocusOnClose?: boolean;
  announceOnOpen?: string;
  announceOnClose?: string;
}

/**
 * Accessible Modal Component
 * 
 * Features:
 * - Proper focus management with focus trap
 * - Screen reader announcements
 * - Keyboard navigation support
 * - ARIA attributes for accessibility
 * - Customizable actions with proper labeling
 */
export const AccessibleModal: React.FC<AccessibleModalProps> = ({
  title,
  description,
  children,
  primaryAction,
  secondaryAction,
  closeOnOverlayClick = true,
  closeOnEsc = true,
  returnFocusOnClose = true,
  announceOnOpen,
  announceOnClose,
  isOpen,
  onClose,
  size = 'md',
  ...modalProps
}) => {
  const initialFocusRef = useRef<HTMLButtonElement>(null);
  const finalFocusRef = useRef<HTMLElement | null>(null);

  // Save focus when modal opens
  useEffect(() => {
    if (isOpen) {
      finalFocusRef.current = document.activeElement as HTMLElement;
      
      // Announce modal opening to screen readers
      const announcement = announceOnOpen || `${title} penceresi açıldı`;
      ScreenReaderUtils.announce(announcement, 'assertive');
    }
  }, [isOpen, title, announceOnOpen]);

  // Handle modal close
  const handleClose = useCallback(() => {
    // Announce modal closing to screen readers
    const announcement = announceOnClose || `${title} penceresi kapatıldı`;
    ScreenReaderUtils.announce(announcement, 'polite');
    
    onClose();
    
    // Return focus if enabled
    if (returnFocusOnClose && finalFocusRef.current) {
      setTimeout(() => {
        finalFocusRef.current?.focus();
      }, 100);
    }
  }, [onClose, title, announceOnClose, returnFocusOnClose]);

  // Handle primary action
  const handlePrimaryAction = useCallback(() => {
    primaryAction?.onClick();
  }, [primaryAction]);

  // Handle secondary action
  const handleSecondaryAction = useCallback(() => {
    secondaryAction?.onClick();
  }, [secondaryAction]);

  // Handle escape key
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && closeOnEsc) {
      event.preventDefault();
      handleClose();
    }
  }, [closeOnEsc, handleClose]);

  // Add escape key listener
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [isOpen, handleKeyDown]);

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size={size}
      closeOnOverlayClick={closeOnOverlayClick}
      closeOnEsc={closeOnEsc}
      returnFocusOnClose={returnFocusOnClose}
      initialFocusRef={initialFocusRef}
      {...modalProps}
    >
      <ModalOverlay bg="blackAlpha.600" />
      <ModalContent
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        aria-describedby={description ? "modal-description" : undefined}
        mx={4}
        my={16}
      >
        <ModalHeader pb={description ? 2 : 4}>
          <Text
            id="modal-title"
            fontSize="lg"
            fontWeight="semibold"
            lineHeight="1.2"
          >
            {title}
          </Text>
          {description && (
            <Text
              id="modal-description"
              fontSize="sm"
              color="gray.600"
              mt={2}
              fontWeight="normal"
            >
              {description}
            </Text>
          )}
        </ModalHeader>

        <ModalCloseButton
          aria-label={`${title} penceresini kapat`}
          size="sm"
          top={4}
          right={4}
        />

        <ModalBody py={4}>
          <Box role="document">
            {children}
          </Box>
        </ModalBody>

        {(primaryAction || secondaryAction) && (
          <ModalFooter pt={4}>
            <ButtonGroup spacing={3} size="sm">
              {secondaryAction && (
                <Button
                  variant={secondaryAction.variant || 'outline'}
                  onClick={handleSecondaryAction}
                >
                  {secondaryAction.label}
                </Button>
              )}
              {primaryAction && (
                <Button
                  ref={initialFocusRef}
                  colorScheme={primaryAction.colorScheme || 'brand'}
                  variant={primaryAction.variant || 'solid'}
                  isLoading={primaryAction.isLoading}
                  onClick={handlePrimaryAction}
                >
                  {primaryAction.label}
                </Button>
              )}
            </ButtonGroup>
          </ModalFooter>
        )}
      </ModalContent>
    </Modal>
  );
};

/**
 * Accessible Confirmation Modal
 */
interface AccessibleConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmLabel?: string;
  cancelLabel?: string;
  isLoading?: boolean;
  variant?: 'danger' | 'warning' | 'info';
}

export const AccessibleConfirmModal: React.FC<AccessibleConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmLabel = 'Onayla',
  cancelLabel = 'İptal',
  isLoading = false,
  variant = 'info'
}) => {
  const colorSchemes = {
    danger: 'red',
    warning: 'orange',
    info: 'brand'
  };

  const handleConfirm = useCallback(() => {
    onConfirm();
  }, [onConfirm]);

  return (
    <AccessibleModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="sm"
      closeOnOverlayClick={false}
      announceOnOpen={`Onay penceresi: ${title}`}
      primaryAction={{
        label: confirmLabel,
        onClick: handleConfirm,
        isLoading,
        colorScheme: colorSchemes[variant]
      }}
      secondaryAction={{
        label: cancelLabel,
        onClick: onClose
      }}
    >
      <Text>{message}</Text>
    </AccessibleModal>
  );
};

/**
 * Hook for accessible modal management
 */
export function useAccessibleModal() {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const openModal = useCallback((announcement?: string) => {
    onOpen();
    if (announcement) {
      ScreenReaderUtils.announce(announcement, 'assertive');
    }
  }, [onOpen]);

  const closeModal = useCallback((announcement?: string) => {
    onClose();
    if (announcement) {
      ScreenReaderUtils.announce(announcement, 'polite');
    }
  }, [onClose]);

  return {
    isOpen,
    onOpen: openModal,
    onClose: closeModal
  };
}

/**
 * Accessible Alert Dialog
 */
interface AccessibleAlertDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  actionLabel?: string;
  variant?: 'error' | 'warning' | 'info' | 'success';
}

export const AccessibleAlertDialog: React.FC<AccessibleAlertDialogProps> = ({
  isOpen,
  onClose,
  title,
  message,
  actionLabel = 'Tamam',
  variant = 'info'
}) => {
  const colorSchemes = {
    error: 'red',
    warning: 'orange',
    info: 'blue',
    success: 'green'
  };

  return (
    <AccessibleModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="sm"
      announceOnOpen={`Uyarı: ${title}`}
      primaryAction={{
        label: actionLabel,
        onClick: onClose,
        colorScheme: colorSchemes[variant]
      }}
    >
      <Text>{message}</Text>
    </AccessibleModal>
  );
};
