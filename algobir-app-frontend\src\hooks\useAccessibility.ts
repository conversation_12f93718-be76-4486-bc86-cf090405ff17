import { useEffect, useRef, useState, useCallback } from 'react';
import { useToast } from '@chakra-ui/react';

interface AccessibilityOptions {
  announcePageChanges?: boolean;
  manageFocus?: boolean;
  enableKeyboardShortcuts?: boolean;
  highContrastMode?: boolean;
  reducedMotion?: boolean;
}

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  callback: () => void;
  description: string;
}

/**
 * Accessibility Hook - UX optimized
 * Provides comprehensive accessibility features and utilities
 */
export const useAccessibility = (options: AccessibilityOptions = {}) => {
  const {
    announcePageChanges = true,
    manageFocus = true,
    enableKeyboardShortcuts = true,
    highContrastMode = false,
    reducedMotion = false
  } = options;

  const toast = useToast();
  const [isHighContrast, setIsHighContrast] = useState(highContrastMode);
  const [isReducedMotion, setIsReducedMotion] = useState(reducedMotion);
  const [shortcuts, setShortcuts] = useState<KeyboardShortcut[]>([]);
  const announcementRef = useRef<HTMLDivElement>(null);

  // Screen reader announcement function
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (announcementRef.current) {
      announcementRef.current.setAttribute('aria-live', priority);
      announcementRef.current.textContent = message;
      
      // Clear after announcement
      setTimeout(() => {
        if (announcementRef.current) {
          announcementRef.current.textContent = '';
        }
      }, 1000);
    }
  }, []);

  // Focus management
  const focusElement = useCallback((selector: string | HTMLElement) => {
    if (!manageFocus) return;

    const element = typeof selector === 'string' 
      ? document.querySelector(selector) as HTMLElement
      : selector;

    if (element) {
      element.focus();
      // Scroll into view if needed
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [manageFocus]);

  // Skip to main content
  const skipToMain = useCallback(() => {
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      focusElement(mainContent);
      announce('Ana içeriğe atlandı');
    }
  }, [focusElement, announce]);

  // Toggle high contrast mode
  const toggleHighContrast = useCallback(() => {
    setIsHighContrast(prev => {
      const newValue = !prev;
      document.documentElement.setAttribute('data-high-contrast', newValue.toString());
      announce(newValue ? 'Yüksek kontrast modu açıldı' : 'Yüksek kontrast modu kapatıldı');
      return newValue;
    });
  }, [announce]);

  // Toggle reduced motion
  const toggleReducedMotion = useCallback(() => {
    setIsReducedMotion(prev => {
      const newValue = !prev;
      document.documentElement.setAttribute('data-reduced-motion', newValue.toString());
      announce(newValue ? 'Azaltılmış hareket modu açıldı' : 'Azaltılmış hareket modu kapatıldı');
      return newValue;
    });
  }, [announce]);

  // Add keyboard shortcut
  const addShortcut = useCallback((shortcut: KeyboardShortcut) => {
    setShortcuts(prev => [...prev, shortcut]);
  }, []);

  // Remove keyboard shortcut
  const removeShortcut = useCallback((key: string) => {
    setShortcuts(prev => prev.filter(s => s.key !== key));
  }, []);

  // Show keyboard shortcuts help
  const showShortcutsHelp = useCallback(() => {
    const shortcutsList = shortcuts.map(s => {
      const keys = [];
      if (s.ctrlKey) keys.push('Ctrl');
      if (s.altKey) keys.push('Alt');
      if (s.shiftKey) keys.push('Shift');
      keys.push(s.key);
      return `${keys.join(' + ')}: ${s.description}`;
    }).join('\n');

    toast({
      title: 'Klavye Kısayolları',
      description: shortcutsList || 'Henüz kısayol tanımlanmamış',
      status: 'info',
      duration: 8000,
      isClosable: true,
      position: 'top'
    });
  }, [shortcuts, toast]);

  // Keyboard event handler
  useEffect(() => {
    if (!enableKeyboardShortcuts) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Check for registered shortcuts
      const matchingShortcut = shortcuts.find(shortcut => 
        shortcut.key.toLowerCase() === event.key.toLowerCase() &&
        !!shortcut.ctrlKey === event.ctrlKey &&
        !!shortcut.altKey === event.altKey &&
        !!shortcut.shiftKey === event.shiftKey
      );

      if (matchingShortcut) {
        event.preventDefault();
        matchingShortcut.callback();
        return;
      }

      // Built-in shortcuts
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case '/':
            event.preventDefault();
            showShortcutsHelp();
            break;
          case 'k':
            event.preventDefault();
            // Focus search if available
            const searchInput = document.querySelector('input[type="search"], input[placeholder*="ara"]') as HTMLElement;
            if (searchInput) {
              focusElement(searchInput);
              announce('Arama kutusuna odaklandı');
            }
            break;
        }
      }

      // Skip link (Alt + S)
      if (event.altKey && event.key.toLowerCase() === 's') {
        event.preventDefault();
        skipToMain();
      }

      // High contrast toggle (Alt + H)
      if (event.altKey && event.key.toLowerCase() === 'h') {
        event.preventDefault();
        toggleHighContrast();
      }

      // Reduced motion toggle (Alt + M)
      if (event.altKey && event.key.toLowerCase() === 'm') {
        event.preventDefault();
        toggleReducedMotion();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts, enableKeyboardShortcuts, showShortcutsHelp, focusElement, announce, skipToMain, toggleHighContrast, toggleReducedMotion]);

  // Detect system preferences
  useEffect(() => {
    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setIsReducedMotion(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setIsReducedMotion(e.matches);
      document.documentElement.setAttribute('data-reduced-motion', e.matches.toString());
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Page change announcements
  useEffect(() => {
    if (!announcePageChanges) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.target === document.head) {
          announce(`Sayfa değişti: ${document.title}`);
        }
      });
    });

    // Watch for title changes
    const titleElement = document.querySelector('title');
    if (titleElement) {
      observer.observe(titleElement, { childList: true, subtree: true });
    }

    return () => observer.disconnect();
  }, [announcePageChanges, announce]);

  // Create announcement element
  useEffect(() => {
    if (!announcementRef.current) {
      const element = document.createElement('div');
      element.setAttribute('aria-live', 'polite');
      element.setAttribute('aria-atomic', 'true');
      element.style.position = 'absolute';
      element.style.left = '-10000px';
      element.style.width = '1px';
      element.style.height = '1px';
      element.style.overflow = 'hidden';
      document.body.appendChild(element);
      announcementRef.current = element;
    }

    return () => {
      if (announcementRef.current && document.body.contains(announcementRef.current)) {
        document.body.removeChild(announcementRef.current);
      }
    };
  }, []);

  return {
    // State
    isHighContrast,
    isReducedMotion,
    shortcuts,

    // Functions
    announce,
    focusElement,
    skipToMain,
    toggleHighContrast,
    toggleReducedMotion,
    addShortcut,
    removeShortcut,
    showShortcutsHelp,

    // Utilities
    getAriaLabel: (text: string, context?: string) => 
      context ? `${text}, ${context}` : text,
    
    getAriaDescription: (description: string) => description,
    
    // ARIA helpers
    ariaProps: {
      'aria-live': 'polite' as const,
      'aria-atomic': true,
      'role': 'status' as const
    }
  };
};

export default useAccessibility;
