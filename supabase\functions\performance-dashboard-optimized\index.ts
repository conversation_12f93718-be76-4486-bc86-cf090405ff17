/**
 * Optimized Performance Dashboard Function
 * Phase 2.4: Supabase Edge Functions Performance Optimization
 * Real-time performance metrics and monitoring dashboard
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { getCorsHeaders } from '../_shared/cors.ts';
import { 
  createPerformanceMonitor, 
  cache, 
  dbOptimizer, 
  responseOptimizer,
  memoryManager,
  connectionPool
} from '../_shared/performance-optimizer.ts';

// Environment setup
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

// Performance metrics aggregator
class PerformanceAggregator {
  private static readonly CACHE_TTL = 60000; // 1 minute cache

  static async getSystemMetrics(supabase: any, monitor: any): Promise<any> {
    const cacheKey = 'system_metrics';
    
    const cached = cache.get(cacheKey);
    if (cached) {
      monitor.recordCacheHit();
      return cached;
    }

    monitor.recordCacheMiss();

    try {
      // Get Edge Function performance metrics
      const edgeFunctionMetrics = await this.getEdgeFunctionMetrics(supabase, monitor);
      
      // Get database performance metrics
      const databaseMetrics = await this.getDatabaseMetrics(supabase, monitor);
      
      // Get cache performance metrics
      const cacheMetrics = this.getCacheMetrics();
      
      // Get memory usage
      const memoryMetrics = memoryManager.checkMemoryUsage();

      const systemMetrics = {
        timestamp: new Date().toISOString(),
        edge_functions: edgeFunctionMetrics,
        database: databaseMetrics,
        cache: cacheMetrics,
        memory: memoryMetrics,
        uptime: performance.now()
      };

      cache.set(cacheKey, systemMetrics, this.CACHE_TTL);
      return systemMetrics;

    } catch (error) {
      console.error('[PerformanceAggregator] Error getting system metrics:', error);
      return {
        timestamp: new Date().toISOString(),
        error: 'Failed to collect system metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private static async getEdgeFunctionMetrics(supabase: any, monitor: any): Promise<any> {
    try {
      const result = await dbOptimizer.executeQuery(
        supabase,
        'edge_function_metrics',
        async () => {
          const { data, error } = await supabase
            .from('order_transmission_metrics')
            .select(`
              signal_source,
              processing_status,
              avg(total_processing_time_ms) as avg_processing_time,
              avg(json_parsing_time_ms) as avg_parsing_time,
              avg(transformation_time_ms) as avg_transformation_time,
              avg(webhook_delivery_time_ms) as avg_webhook_time,
              count(*) as total_requests,
              sum(case when processing_status = 'success' then 1 else 0 end) as successful_requests
            `)
            .gte('created_at', new Date(Date.now() - 3600000).toISOString()) // Last hour
            .group('signal_source, processing_status');
          
          return { data, error };
        },
        30000, // 30 seconds cache
        monitor
      );

      if (result.error) {
        return { error: 'Failed to fetch Edge Function metrics' };
      }

      // Process the data
      const metrics = result.data || [];
      const summary = {
        total_requests: metrics.reduce((sum: number, m: any) => sum + (m.total_requests || 0), 0),
        successful_requests: metrics.reduce((sum: number, m: any) => sum + (m.successful_requests || 0), 0),
        avg_processing_time: metrics.length > 0 
          ? metrics.reduce((sum: number, m: any) => sum + (m.avg_processing_time || 0), 0) / metrics.length 
          : 0,
        success_rate: 0,
        by_source: {}
      };

      summary.success_rate = summary.total_requests > 0 
        ? (summary.successful_requests / summary.total_requests) * 100 
        : 0;

      // Group by source
      metrics.forEach((m: any) => {
        if (!summary.by_source[m.signal_source]) {
          summary.by_source[m.signal_source] = {
            total_requests: 0,
            successful_requests: 0,
            avg_processing_time: 0
          };
        }
        
        summary.by_source[m.signal_source].total_requests += m.total_requests || 0;
        summary.by_source[m.signal_source].successful_requests += m.successful_requests || 0;
        summary.by_source[m.signal_source].avg_processing_time = m.avg_processing_time || 0;
      });

      return summary;

    } catch (error) {
      return { error: 'Exception in Edge Function metrics collection' };
    }
  }

  private static async getDatabaseMetrics(supabase: any, monitor: any): Promise<any> {
    try {
      const queries = [
        // Active connections
        dbOptimizer.executeQuery(
          supabase,
          'db_connections',
          async () => {
            const { data, error } = await supabase.rpc('get_db_connections');
            return { data, error };
          },
          30000,
          monitor
        ),
        
        // Recent trades count
        dbOptimizer.executeQuery(
          supabase,
          'recent_trades_count',
          async () => {
            const { data, error } = await supabase
              .from('trades')
              .select('count', { count: 'exact', head: true })
              .gte('created_at', new Date(Date.now() - 3600000).toISOString());
            return { data, error };
          },
          60000,
          monitor
        ),

        // Active users count
        dbOptimizer.executeQuery(
          supabase,
          'active_users_count',
          async () => {
            const { data, error } = await supabase
              .from('user_settings')
              .select('count', { count: 'exact', head: true })
              .eq('is_active', true);
            return { data, error };
          },
          300000,
          monitor
        )
      ];

      const results = await dbOptimizer.executeBatch(queries, monitor);
      
      return {
        connections: results[0].data || 0,
        recent_trades: results[1].data || 0,
        active_users: results[2].data || 0,
        query_performance: {
          total_queries: monitor.getCurrentMetrics().dbQueryCount,
          total_query_time: monitor.getCurrentMetrics().dbQueryTime
        }
      };

    } catch (error) {
      return { error: 'Exception in database metrics collection' };
    }
  }

  private static getCacheMetrics(): any {
    return cache.getStats();
  }
}

// Real-time alerts manager
class AlertsManager {
  private static readonly ALERT_THRESHOLDS = {
    high_processing_time: 5000, // 5 seconds
    low_success_rate: 95, // 95%
    high_memory_usage: 80 * 1024 * 1024, // 80MB
    high_error_rate: 5 // 5%
  };

  static checkAlerts(metrics: any): any[] {
    const alerts: any[] = [];
    const timestamp = new Date().toISOString();

    // Check processing time
    if (metrics.edge_functions?.avg_processing_time > this.ALERT_THRESHOLDS.high_processing_time) {
      alerts.push({
        type: 'performance',
        severity: 'warning',
        message: `High average processing time: ${metrics.edge_functions.avg_processing_time.toFixed(2)}ms`,
        threshold: this.ALERT_THRESHOLDS.high_processing_time,
        current_value: metrics.edge_functions.avg_processing_time,
        timestamp
      });
    }

    // Check success rate
    if (metrics.edge_functions?.success_rate < this.ALERT_THRESHOLDS.low_success_rate) {
      alerts.push({
        type: 'reliability',
        severity: 'error',
        message: `Low success rate: ${metrics.edge_functions.success_rate.toFixed(2)}%`,
        threshold: this.ALERT_THRESHOLDS.low_success_rate,
        current_value: metrics.edge_functions.success_rate,
        timestamp
      });
    }

    // Check memory usage
    if (metrics.memory?.usage > this.ALERT_THRESHOLDS.high_memory_usage) {
      alerts.push({
        type: 'resource',
        severity: 'warning',
        message: `High memory usage: ${(metrics.memory.usage / 1024 / 1024).toFixed(2)}MB`,
        threshold: this.ALERT_THRESHOLDS.high_memory_usage / 1024 / 1024,
        current_value: metrics.memory.usage / 1024 / 1024,
        timestamp
      });
    }

    return alerts;
  }
}

// Health check utilities
class HealthChecker {
  static async performHealthCheck(supabase: any, monitor: any): Promise<any> {
    const healthStatus = {
      timestamp: new Date().toISOString(),
      overall_status: 'healthy',
      services: {
        database: { status: 'unknown', response_time: 0 },
        cache: { status: 'unknown', hit_rate: 0 },
        memory: { status: 'unknown', usage_mb: 0 }
      },
      issues: []
    };

    try {
      // Database health check
      const dbStart = performance.now();
      const dbResult = await supabase.from('user_settings').select('count', { count: 'exact', head: true }).limit(1);
      const dbTime = performance.now() - dbStart;
      
      healthStatus.services.database = {
        status: dbResult.error ? 'unhealthy' : 'healthy',
        response_time: dbTime
      };

      if (dbResult.error) {
        healthStatus.issues.push('Database connection issues');
        healthStatus.overall_status = 'degraded';
      }

      // Cache health check
      const cacheStats = cache.getStats();
      const hitRate = cacheStats.totalHits > 0 
        ? (cacheStats.totalHits / (cacheStats.totalHits + cacheStats.size)) * 100 
        : 0;
      
      healthStatus.services.cache = {
        status: cacheStats.size >= 0 ? 'healthy' : 'unhealthy',
        hit_rate: hitRate
      };

      // Memory health check
      const memoryStatus = memoryManager.checkMemoryUsage();
      healthStatus.services.memory = {
        status: memoryStatus.needsCleanup ? 'warning' : 'healthy',
        usage_mb: memoryStatus.usage / 1024 / 1024
      };

      if (memoryStatus.needsCleanup) {
        healthStatus.issues.push('High memory usage detected');
        if (healthStatus.overall_status === 'healthy') {
          healthStatus.overall_status = 'warning';
        }
      }

    } catch (error) {
      healthStatus.overall_status = 'unhealthy';
      healthStatus.issues.push(`Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return healthStatus;
  }
}

Deno.serve(async (req) => {
  const monitor = createPerformanceMonitor();
  const corsHeaders = getCorsHeaders(req.headers);

  if (req.method === 'OPTIONS') {
    return responseOptimizer.createJsonResponse('ok', 200, corsHeaders, monitor);
  }

  try {
    // Initialize Supabase client
    const supabase = connectionPool.getPool(
      'supabase_dashboard',
      () => createClient(supabaseUrl, supabaseServiceKey)
    );

    // Parse URL to determine endpoint
    const url = new URL(req.url);
    const endpoint = url.pathname.split('/').pop();

    switch (endpoint) {
      case 'metrics':
        const metrics = await PerformanceAggregator.getSystemMetrics(supabase, monitor);
        return responseOptimizer.createJsonResponse(metrics, 200, corsHeaders, monitor);

      case 'health':
        const health = await HealthChecker.performHealthCheck(supabase, monitor);
        const statusCode = health.overall_status === 'healthy' ? 200 : 
                          health.overall_status === 'warning' ? 200 : 503;
        return responseOptimizer.createJsonResponse(health, statusCode, corsHeaders, monitor);

      case 'alerts':
        const systemMetrics = await PerformanceAggregator.getSystemMetrics(supabase, monitor);
        const alerts = AlertsManager.checkAlerts(systemMetrics);
        return responseOptimizer.createJsonResponse({ alerts, count: alerts.length }, 200, corsHeaders, monitor);

      case 'cache-stats':
        const cacheStats = cache.getStats();
        return responseOptimizer.createJsonResponse(cacheStats, 200, corsHeaders, monitor);

      default:
        // Default dashboard overview
        const [dashboardMetrics, dashboardHealth, dashboardAlerts] = await Promise.all([
          PerformanceAggregator.getSystemMetrics(supabase, monitor),
          HealthChecker.performHealthCheck(supabase, monitor),
          PerformanceAggregator.getSystemMetrics(supabase, monitor).then(m => AlertsManager.checkAlerts(m))
        ]);

        const dashboard = {
          timestamp: new Date().toISOString(),
          metrics: dashboardMetrics,
          health: dashboardHealth,
          alerts: dashboardAlerts,
          summary: {
            total_alerts: dashboardAlerts.length,
            critical_alerts: dashboardAlerts.filter(a => a.severity === 'error').length,
            overall_health: dashboardHealth.overall_status,
            uptime_ms: performance.now()
          }
        };

        return responseOptimizer.createJsonResponse(dashboard, 200, corsHeaders, monitor);
    }

  } catch (error) {
    console.error('[performance-dashboard-optimized] Error:', error);
    
    return responseOptimizer.createErrorResponse(
      `Dashboard error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500,
      corsHeaders,
      monitor
    );
  }
});
