import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '../supabaseClient';
import LoadingState from '../components/LoadingState';

// Extend the User type to include our custom profile and settings data
export interface AppUser extends User {
  profile?: any;
  settings?: UserSettings | null;
}

export interface UserSettings {
    initial_setup_complete: boolean;
    is_superuser: boolean;
    custom_webhook_url: string;
    api_key_set: boolean;
    token_set: boolean;
}

interface AuthContextType {
  user: AppUser | null;
  session: Session | null;
  loading: boolean;
  initialSetupComplete: boolean;
  refetchUserData: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<AppUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [settingsLoaded, setSettingsLoaded] = useState(false);
  
  // Performance optimization: Cache user settings to prevent re-fetching
  const [userCache, setUserCache] = useState<{ [key: string]: any }>({});
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  const fetchUserData = async (currentUser: User): Promise<void> => {
    try {
      console.log('Fetching user data for:', currentUser.id);
      
      // Performance optimization: Check cache first (2-minute cache)
      const now = Date.now();
      const cacheKey = `user_${currentUser.id}`;
      if (userCache[cacheKey] && (now - lastFetchTime < 120000)) { // 2 minutes cache
        console.log('Using cached user data');
        setUser({ ...currentUser, ...userCache[cacheKey] });
        setSettingsLoaded(true);
        return;
      }
      
      // Set basic user first so app can render
      setUser({ ...currentUser, profile: null, settings: null });
      
      // Try to fetch additional data, but don't block rendering
      const [profileRes, settingsRes] = await Promise.allSettled([
        supabase.from('profiles').select('*').eq('id', currentUser.id).single(),
        supabase.from('user_settings').select('initial_setup_complete, is_superuser, custom_webhook_url, api_key_set, token_set').eq('id', currentUser.id).single()
      ]);

      let profile = null;
      let settings = null;

      if (profileRes.status === 'fulfilled' && !profileRes.value.error) {
        profile = profileRes.value.data;
      } else {
        console.warn("Could not fetch profile data");
      }

      if (settingsRes.status === 'fulfilled' && !settingsRes.value.error) {
        settings = settingsRes.value.data;
      } else {
        console.warn("Could not fetch user settings");
      }

      // Cache the data
      const userData = { profile, settings };
      setUserCache(prev => ({ ...prev, [cacheKey]: userData }));
      setLastFetchTime(now);

      // Update user with additional data
      setUser({ ...currentUser, profile, settings });
      setSettingsLoaded(true);
      
    } catch (error) {
      console.error("Error fetching user data:", error);
      // Keep basic user data even on error
      setUser({ ...currentUser, profile: null, settings: null });
      setSettingsLoaded(true); // Still mark as loaded even on error
    }
  };

  useEffect(() => {
    let mounted = true;
    
    console.log('AuthContext: Starting initialization...');

    // Simple timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (mounted && loading) {
        console.warn('Auth initialization timed out, forcing loading to false');
        setLoading(false);
      }
    }, 10000); // 10 second timeout

    const initAuth = async () => {
      try {
        console.log('AuthContext: Getting session...');
        
        // Get initial session with timeout
        const sessionPromise = supabase.auth.getSession();
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Session timeout')), 5000)
        );
        
        const result = await Promise.race([sessionPromise, timeoutPromise]) as any;
        
        if (!mounted) return;
        
        const initialSession = result?.data?.session;
        console.log('AuthContext: Initial session result:', initialSession ? 'Found' : 'Not found');
        
        setSession(initialSession);
        
        if (initialSession?.user) {
          // Don't await this - let it run in background
          fetchUserData(initialSession.user).catch(console.error);
        } else {
          setUser(null);
          setSettingsLoaded(false);
        }
        
      } catch (error) {
        console.error('AuthContext: Error during initialization:', error);
        if (mounted) {
          setSession(null);
          setUser(null);
          setSettingsLoaded(false);
        }
      } finally {
        if (mounted) {
          console.log('AuthContext: Setting loading to false');
          clearTimeout(timeoutId);
          setLoading(false);
        }
      }
    };

    // Start initialization
    initAuth();

    // Auth state listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        try {
          console.log('AuthContext: Auth state change:', event);
          
          if (!mounted) return;
          
          setSession(newSession);
          
          if (newSession?.user) {
            // Don't await - let it run in background
            fetchUserData(newSession.user).catch(console.error);
          } else {
            setUser(null);
            setSettingsLoaded(false);
          }
        } catch (error) {
          console.error('AuthContext: Error in auth state change:', error);
        }
      }
    );

    return () => {
      console.log('AuthContext: Cleanup');
      mounted = false;
      clearTimeout(timeoutId);
      authListener?.subscription.unsubscribe();
    };
  }, []);
  
  const refetchUserData = () => {
    if (session?.user) {
        console.log('AuthContext: Refetching user data...');
        setSettingsLoaded(false); // Reset to prevent showing wizard during refetch
        fetchUserData(session.user).catch(console.error);
    }
  };

  // Intelligent initial setup logic:
  // - If settings not loaded yet, assume complete (don't show wizard during loading)
  // - If no user, assume complete
  // - If settings loaded, use actual value
  const shouldShowSetupWizard = user && settingsLoaded && user.settings && user.settings.initial_setup_complete === false;

  const value = {
    user,
    session,
    loading,
    initialSetupComplete: !shouldShowSetupWizard,
    refetchUserData,
  };

  console.log('AuthContext: Render state - loading:', loading, 'session:', !!session, 'user:', !!user);

  return (
    <AuthContext.Provider value={value}>
      {loading ? (
        <LoadingState text="Oturum kontrol ediliyor..." isFullPage={true} />
      ) : (
        children
      )}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 