/**
 * Edge Functions Performance Optimizer
 * Advanced performance optimization utilities for Supabase Edge Functions
 * Phase 2.4: Supabase Edge Functions Performance Optimization
 */

// Performance monitoring interface
export interface PerformanceMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryUsage?: number;
  cpuUsage?: number;
  requestSize?: number;
  responseSize?: number;
  dbQueryCount?: number;
  dbQueryTime?: number;
  cacheHits?: number;
  cacheMisses?: number;
}

// Cache interface for Edge Functions
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
}

/**
 * High-performance in-memory cache for Edge Functions
 */
export class EdgeFunctionCache {
  private static cache = new Map<string, CacheEntry<any>>();
  private static maxSize = 1000; // Maximum cache entries
  private static cleanupInterval = 60000; // 1 minute cleanup interval
  private static lastCleanup = Date.now();

  /**
   * Get cached value
   */
  static get<T>(key: string): T | null {
    this.performCleanupIfNeeded();
    
    const entry = this.cache.get(key);
    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Increment hit counter
    entry.hits++;
    return entry.data;
  }

  /**
   * Set cached value
   */
  static set<T>(key: string, data: T, ttl: number = 300000): void { // 5 minutes default
    this.performCleanupIfNeeded();
    
    // Evict oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0
    });
  }

  /**
   * Delete cached value
   */
  static delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all cache
   */
  static clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  static getStats() {
    const entries = Array.from(this.cache.values());
    return {
      size: this.cache.size,
      totalHits: entries.reduce((sum, entry) => sum + entry.hits, 0),
      averageAge: entries.length > 0 
        ? entries.reduce((sum, entry) => sum + (Date.now() - entry.timestamp), 0) / entries.length 
        : 0
    };
  }

  /**
   * Cleanup expired entries
   */
  private static performCleanupIfNeeded(): void {
    const now = Date.now();
    if (now - this.lastCleanup < this.cleanupInterval) {
      return;
    }

    const expiredKeys: string[] = [];
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
    this.lastCleanup = now;
  }
}

/**
 * Performance Monitor for Edge Functions
 */
export class PerformanceMonitor {
  private metrics: PerformanceMetrics;
  private dbQueryStartTime: number = 0;
  private dbQueryCount: number = 0;
  private dbQueryTotalTime: number = 0;

  constructor() {
    this.metrics = {
      startTime: performance.now(),
      dbQueryCount: 0,
      dbQueryTime: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
  }

  /**
   * Start database query timing
   */
  startDbQuery(): void {
    this.dbQueryStartTime = performance.now();
  }

  /**
   * End database query timing
   */
  endDbQuery(): void {
    if (this.dbQueryStartTime > 0) {
      const queryTime = performance.now() - this.dbQueryStartTime;
      this.dbQueryTotalTime += queryTime;
      this.dbQueryCount++;
      this.dbQueryStartTime = 0;
    }
  }

  /**
   * Record cache hit
   */
  recordCacheHit(): void {
    this.metrics.cacheHits = (this.metrics.cacheHits || 0) + 1;
  }

  /**
   * Record cache miss
   */
  recordCacheMiss(): void {
    this.metrics.cacheMisses = (this.metrics.cacheMisses || 0) + 1;
  }

  /**
   * Set request size
   */
  setRequestSize(size: number): void {
    this.metrics.requestSize = size;
  }

  /**
   * Set response size
   */
  setResponseSize(size: number): void {
    this.metrics.responseSize = size;
  }

  /**
   * Finish monitoring and get metrics
   */
  finish(): PerformanceMetrics {
    this.metrics.endTime = performance.now();
    this.metrics.duration = this.metrics.endTime - this.metrics.startTime;
    this.metrics.dbQueryCount = this.dbQueryCount;
    this.metrics.dbQueryTime = this.dbQueryTotalTime;

    // Get memory usage if available
    if ('memory' in performance) {
      this.metrics.memoryUsage = (performance as any).memory?.usedJSHeapSize;
    }

    return this.metrics;
  }

  /**
   * Get current metrics without finishing
   */
  getCurrentMetrics(): PerformanceMetrics {
    return {
      ...this.metrics,
      duration: performance.now() - this.metrics.startTime,
      dbQueryCount: this.dbQueryCount,
      dbQueryTime: this.dbQueryTotalTime
    };
  }
}

/**
 * Database Query Optimizer for Edge Functions
 */
export class EdgeDbOptimizer {
  private static queryCache = new Map<string, any>();
  private static preparedStatements = new Map<string, string>();

  /**
   * Execute optimized database query with caching
   */
  static async executeQuery<T>(
    supabase: any,
    queryKey: string,
    queryFn: () => Promise<{ data: T; error: any }>,
    cacheTtl: number = 60000, // 1 minute default
    monitor?: PerformanceMonitor
  ): Promise<{ data: T; error: any; fromCache: boolean }> {
    // Check cache first
    const cached = EdgeFunctionCache.get<T>(queryKey);
    if (cached) {
      monitor?.recordCacheHit();
      return { data: cached, error: null, fromCache: true };
    }

    monitor?.recordCacheMiss();
    monitor?.startDbQuery();

    try {
      const result = await queryFn();
      monitor?.endDbQuery();

      // Cache successful results
      if (!result.error && result.data) {
        EdgeFunctionCache.set(queryKey, result.data, cacheTtl);
      }

      return { ...result, fromCache: false };
    } catch (error) {
      monitor?.endDbQuery();
      return { data: null as T, error, fromCache: false };
    }
  }

  /**
   * Batch multiple queries for better performance
   */
  static async executeBatch<T>(
    queries: Array<() => Promise<{ data: T; error: any }>>,
    monitor?: PerformanceMonitor
  ): Promise<Array<{ data: T; error: any }>> {
    monitor?.startDbQuery();
    
    try {
      const results = await Promise.all(queries.map(query => query()));
      monitor?.endDbQuery();
      return results;
    } catch (error) {
      monitor?.endDbQuery();
      throw error;
    }
  }

  /**
   * Prepare and cache SQL statements
   */
  static prepareStatement(key: string, sql: string): string {
    if (!this.preparedStatements.has(key)) {
      this.preparedStatements.set(key, sql);
    }
    return this.preparedStatements.get(key)!;
  }
}

/**
 * Response optimization utilities
 */
export class ResponseOptimizer {
  /**
   * Create optimized JSON response with compression hints
   */
  static createJsonResponse(
    data: any,
    status: number = 200,
    headers: HeadersInit = {},
    monitor?: PerformanceMonitor
  ): Response {
    const jsonString = JSON.stringify(data);
    const responseSize = new TextEncoder().encode(jsonString).length;
    
    monitor?.setResponseSize(responseSize);

    const responseHeaders = new Headers(headers);
    responseHeaders.set('Content-Type', 'application/json');
    
    // Add compression hint for large responses
    if (responseSize > 1024) {
      responseHeaders.set('Content-Encoding', 'gzip');
    }

    // Add performance headers
    const metrics = monitor?.getCurrentMetrics();
    if (metrics) {
      responseHeaders.set('X-Processing-Time', `${metrics.duration?.toFixed(2)}ms`);
      responseHeaders.set('X-DB-Queries', `${metrics.dbQueryCount}`);
      responseHeaders.set('X-Cache-Hits', `${metrics.cacheHits}`);
    }

    return new Response(jsonString, {
      status,
      headers: responseHeaders
    });
  }

  /**
   * Create error response with performance metrics
   */
  static createErrorResponse(
    error: string,
    status: number = 500,
    headers: HeadersInit = {},
    monitor?: PerformanceMonitor
  ): Response {
    return this.createJsonResponse(
      { error, timestamp: new Date().toISOString() },
      status,
      headers,
      monitor
    );
  }
}

/**
 * Rate limiting for Edge Functions
 */
export class EdgeRateLimiter {
  private static requests = new Map<string, { count: number; resetTime: number }>();
  private static defaultLimit = 100; // requests per minute
  private static defaultWindow = 60000; // 1 minute

  /**
   * Check if request is within rate limit
   */
  static checkLimit(
    identifier: string,
    limit: number = this.defaultLimit,
    windowMs: number = this.defaultWindow
  ): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    const key = `${identifier}:${Math.floor(now / windowMs)}`;
    
    const current = this.requests.get(key) || { count: 0, resetTime: now + windowMs };
    
    if (now > current.resetTime) {
      current.count = 1;
      current.resetTime = now + windowMs;
    } else {
      current.count++;
    }
    
    this.requests.set(key, current);
    
    // Cleanup old entries
    this.cleanup();
    
    return {
      allowed: current.count <= limit,
      remaining: Math.max(0, limit - current.count),
      resetTime: current.resetTime
    };
  }

  /**
   * Cleanup expired rate limit entries
   */
  private static cleanup(): void {
    const now = Date.now();
    for (const [key, data] of this.requests.entries()) {
      if (now > data.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

/**
 * Memory management utilities
 */
export class MemoryManager {
  private static readonly MAX_MEMORY_USAGE = 50 * 1024 * 1024; // 50MB threshold

  /**
   * Check memory usage and trigger cleanup if needed
   */
  static checkMemoryUsage(): { usage: number; needsCleanup: boolean } {
    const usage = (performance as any).memory?.usedJSHeapSize || 0;
    const needsCleanup = usage > this.MAX_MEMORY_USAGE;

    if (needsCleanup) {
      this.performCleanup();
    }

    return { usage, needsCleanup };
  }

  /**
   * Perform memory cleanup
   */
  private static performCleanup(): void {
    // Clear caches
    EdgeFunctionCache.clear();
    EdgeRateLimiter['cleanup']();

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  }
}

/**
 * Connection pool manager for database connections
 */
export class ConnectionPoolManager {
  private static pools = new Map<string, any>();
  private static readonly MAX_CONNECTIONS = 10;
  private static readonly CONNECTION_TIMEOUT = 30000; // 30 seconds

  /**
   * Get or create connection pool
   */
  static getPool(key: string, createFn: () => any): any {
    if (!this.pools.has(key)) {
      const pool = createFn();
      this.pools.set(key, {
        pool,
        created: Date.now(),
        lastUsed: Date.now()
      });
    }

    const poolData = this.pools.get(key)!;
    poolData.lastUsed = Date.now();
    return poolData.pool;
  }

  /**
   * Cleanup unused connections
   */
  static cleanup(): void {
    const now = Date.now();
    for (const [key, poolData] of this.pools.entries()) {
      if (now - poolData.lastUsed > this.CONNECTION_TIMEOUT) {
        this.pools.delete(key);
      }
    }
  }
}

// Export convenience functions
export const createPerformanceMonitor = () => new PerformanceMonitor();
export const cache = EdgeFunctionCache;
export const dbOptimizer = EdgeDbOptimizer;
export const responseOptimizer = ResponseOptimizer;
export const rateLimiter = EdgeRateLimiter;
export const memoryManager = MemoryManager;
export const connectionPool = ConnectionPoolManager;
