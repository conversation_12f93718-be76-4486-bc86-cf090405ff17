/**
 * Authentication Flow Integration Tests
 * Phase 3.1: Automated Testing Pipeline Setup
 * End-to-end authentication flow testing
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, createMockUser, createMockSession } from '../utils/test-utils';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '../../context/AuthContext';

// Mock components for integration testing
const LoginPage = () => (
  <div>
    <h1>Login Page</h1>
    <form data-testid="login-form">
      <input 
        type="email" 
        placeholder="Email" 
        data-testid="email-input"
      />
      <input 
        type="password" 
        placeholder="Password" 
        data-testid="password-input"
      />
      <button type="submit" data-testid="login-button">
        Sign In
      </button>
    </form>
  </div>
);

const Dashboard = () => (
  <div>
    <h1>Dashboard</h1>
    <p data-testid="welcome-message">Welcome to Algobir!</p>
  </div>
);

const AppRouter = ({ user, loading }: { user: any; loading: boolean }) => {
  if (loading) {
    return <div data-testid="loading">Loading...</div>;
  }

  return user ? <Dashboard /> : <LoginPage />;
};

describe('Authentication Flow Integration', () => {
  const mockUser = createMockUser();
  const mockSession = createMockSession();
  
  let mockSignIn: any;
  let mockSignOut: any;
  let mockAuthState: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockSignIn = vi.fn();
    mockSignOut = vi.fn();
    
    mockAuthState = {
      user: null,
      session: null,
      loading: false,
      signIn: mockSignIn,
      signOut: mockSignOut,
      signUp: vi.fn(),
      resetPassword: vi.fn(),
      updateProfile: vi.fn(),
      supabase: {}
    };
  });

  describe('Login Flow', () => {
    it('should show login page when user is not authenticated', () => {
      renderWithProviders(
        <AppRouter user={null} loading={false} />,
        { skipAuth: true }
      );

      expect(screen.getByText('Login Page')).toBeInTheDocument();
      expect(screen.getByTestId('login-form')).toBeInTheDocument();
    });

    it('should show loading state during authentication', () => {
      renderWithProviders(
        <AppRouter user={null} loading={true} />,
        { skipAuth: true }
      );

      expect(screen.getByTestId('loading')).toBeInTheDocument();
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('should show dashboard when user is authenticated', () => {
      renderWithProviders(
        <AppRouter user={mockUser} loading={false} />,
        { skipAuth: true }
      );

      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByTestId('welcome-message')).toBeInTheDocument();
    });

    it('should handle successful login flow', async () => {
      const user = userEvent.setup();
      
      // Start with unauthenticated state
      mockSignIn.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null
      });

      const { rerender } = renderWithProviders(
        <AppRouter user={null} loading={false} />,
        { skipAuth: true }
      );

      // Should show login page
      expect(screen.getByText('Login Page')).toBeInTheDocument();

      // Fill in login form
      const emailInput = screen.getByTestId('email-input');
      const passwordInput = screen.getByTestId('password-input');
      const loginButton = screen.getByTestId('login-button');

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      // Simulate successful authentication
      rerender(<AppRouter user={mockUser} loading={false} />);

      // Should now show dashboard
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByTestId('welcome-message')).toBeInTheDocument();
    });

    it('should handle login errors gracefully', async () => {
      const user = userEvent.setup();
      
      mockSignIn.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid credentials' }
      });

      renderWithProviders(
        <AppRouter user={null} loading={false} />,
        { skipAuth: true }
      );

      const emailInput = screen.getByTestId('email-input');
      const passwordInput = screen.getByTestId('password-input');
      const loginButton = screen.getByTestId('login-button');

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(loginButton);

      // Should remain on login page
      expect(screen.getByText('Login Page')).toBeInTheDocument();
    });
  });

  describe('Logout Flow', () => {
    it('should handle successful logout', async () => {
      mockSignOut.mockResolvedValue({ error: null });

      const { rerender } = renderWithProviders(
        <AppRouter user={mockUser} loading={false} />,
        { skipAuth: true }
      );

      // Should start with dashboard
      expect(screen.getByText('Dashboard')).toBeInTheDocument();

      // Simulate logout
      rerender(<AppRouter user={null} loading={false} />);

      // Should show login page
      expect(screen.getByText('Login Page')).toBeInTheDocument();
    });

    it('should handle logout errors', async () => {
      mockSignOut.mockResolvedValue({
        error: { message: 'Logout failed' }
      });

      renderWithProviders(
        <AppRouter user={mockUser} loading={false} />,
        { skipAuth: true }
      );

      // Should remain authenticated on error
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });
  });

  describe('Session Management', () => {
    it('should handle session expiration', async () => {
      const { rerender } = renderWithProviders(
        <AppRouter user={mockUser} loading={false} />,
        { skipAuth: true }
      );

      // Start authenticated
      expect(screen.getByText('Dashboard')).toBeInTheDocument();

      // Simulate session expiration
      rerender(<AppRouter user={null} loading={false} />);

      // Should redirect to login
      expect(screen.getByText('Login Page')).toBeInTheDocument();
    });

    it('should handle session refresh', async () => {
      const { rerender } = renderWithProviders(
        <AppRouter user={null} loading={true} />,
        { skipAuth: true }
      );

      // Start with loading
      expect(screen.getByTestId('loading')).toBeInTheDocument();

      // Simulate successful session refresh
      rerender(<AppRouter user={mockUser} loading={false} />);

      // Should show dashboard
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });
  });

  describe('Protected Routes', () => {
    it('should redirect unauthenticated users to login', () => {
      renderWithProviders(
        <AppRouter user={null} loading={false} />,
        { skipAuth: true }
      );

      expect(screen.getByText('Login Page')).toBeInTheDocument();
      expect(screen.queryByText('Dashboard')).not.toBeInTheDocument();
    });

    it('should allow authenticated users to access protected routes', () => {
      renderWithProviders(
        <AppRouter user={mockUser} loading={false} />,
        { skipAuth: true }
      );

      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.queryByText('Login Page')).not.toBeInTheDocument();
    });
  });

  describe('Error Boundaries', () => {
    it('should handle authentication errors gracefully', () => {
      // Mock console.error to avoid noise in tests
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const ErrorComponent = () => {
        throw new Error('Auth error');
      };

      expect(() => {
        renderWithProviders(<ErrorComponent />, { skipAuth: true });
      }).not.toThrow();

      consoleSpy.mockRestore();
    });
  });

  describe('Performance', () => {
    it('should complete authentication flow within acceptable time', async () => {
      const startTime = performance.now();
      
      mockSignIn.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null
      });

      const { rerender } = renderWithProviders(
        <AppRouter user={null} loading={false} />,
        { skipAuth: true }
      );

      // Simulate authentication
      rerender(<AppRouter user={mockUser} loading={false} />);

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within 100ms
      expect(duration).toBeLessThan(100);
    });

    it('should not cause memory leaks during auth state changes', () => {
      const { rerender, unmount } = renderWithProviders(
        <AppRouter user={null} loading={false} />,
        { skipAuth: true }
      );

      // Simulate multiple state changes
      for (let i = 0; i < 10; i++) {
        rerender(<AppRouter user={i % 2 === 0 ? mockUser : null} loading={false} />);
      }

      // Should not throw on unmount
      expect(() => unmount()).not.toThrow();
    });
  });
});
