import React from 'react';
import { 
  Box, 
  Container, 

  BoxProps,
  ContainerProps
} from '@chakra-ui/react';

interface ResponsiveContainerProps extends Omit<ContainerProps, 'maxW'> {
  variant?: 'page' | 'section' | 'card' | 'full';
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}

/**
 * Responsive Container Component - UX optimized
 * Provides consistent spacing and responsive behavior across the app
 */
const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  variant = 'page',
  spacing = 'md',
  children,
  ...props
}) => {
  // Responsive spacing configurations
  const spacingConfig = {
    none: { base: 0, md: 0, lg: 0 },
    sm: { base: 2, md: 3, lg: 4 },
    md: { base: 4, md: 6, lg: 8 },
    lg: { base: 6, md: 8, lg: 12 },
    xl: { base: 8, md: 12, lg: 16 }
  };

  // Container max width configurations
  const maxWidthConfig = {
    page: { base: '100%', sm: 'container.sm', md: 'container.md', lg: 'container.lg', xl: 'container.xl' },
    section: { base: '100%', md: 'container.md', lg: 'container.lg' },
    card: { base: '100%', sm: '400px', md: '500px', lg: '600px' },
    full: '100%'
  };

  const currentSpacing = spacingConfig[spacing];
  const currentMaxWidth = maxWidthConfig[variant];

  if (variant === 'full') {
    return (
      <Box
        w="100%"
        px={currentSpacing}
        py={currentSpacing}
        {...props}
      >
        {children}
      </Box>
    );
  }

  return (
    <Container
      maxW={currentMaxWidth}
      px={currentSpacing}
      py={currentSpacing}
      centerContent={variant === 'card'}
      {...props}
    >
      {children}
    </Container>
  );
};

interface ResponsiveGridProps extends BoxProps {
  columns?: {
    base?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  spacing?: number | string | object;
  children: React.ReactNode;
}

/**
 * Responsive Grid Component
 * Provides consistent grid layouts with mobile-first approach
 */
export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  columns = { base: 1, md: 2, lg: 3 },
  spacing = { base: 4, md: 6 },
  children,
  ...props
}) => {
  return (
    <Box
      display="grid"
      gridTemplateColumns={{
        base: `repeat(${columns.base || 1}, 1fr)`,
        sm: `repeat(${columns.sm || columns.base || 1}, 1fr)`,
        md: `repeat(${columns.md || columns.sm || columns.base || 2}, 1fr)`,
        lg: `repeat(${columns.lg || columns.md || 3}, 1fr)`,
        xl: `repeat(${columns.xl || columns.lg || 4}, 1fr)`,
        '2xl': `repeat(${columns['2xl'] || columns.xl || 5}, 1fr)`,
      }}
      gap={spacing}
      {...props}
    >
      {children}
    </Box>
  );
};

interface ResponsiveStackProps extends BoxProps {
  direction?: 'row' | 'column' | 'responsive';
  spacing?: number | string | object;
  align?: string;
  justify?: string;
  wrap?: boolean;
  children: React.ReactNode;
}

/**
 * Responsive Stack Component
 * Automatically switches between row and column based on screen size
 */
export const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  direction = 'responsive',
  spacing = { base: 4, md: 6 },
  align = 'stretch',
  justify = 'flex-start',
  wrap = true,
  children,
  ...props
}) => {
  const flexDirection = direction === 'responsive'
    ? { base: 'column' as const, md: 'row' as const }
    : direction;

  return (
    <Box
      display="flex"
      flexDirection={flexDirection}
      gap={spacing}
      alignItems={align}
      justifyContent={justify}
      flexWrap={wrap ? 'wrap' : 'nowrap'}
      {...props}
    >
      {children}
    </Box>
  );
};

interface MobileFirstBoxProps extends BoxProps {
  showOn?: 'mobile' | 'tablet' | 'desktop' | 'all';
  hideOn?: 'mobile' | 'tablet' | 'desktop';
  children: React.ReactNode;
}

/**
 * Mobile First Box Component
 * Provides easy show/hide functionality for different screen sizes
 */
export const MobileFirstBox: React.FC<MobileFirstBoxProps> = ({
  showOn = 'all',
  hideOn,
  children,
  ...props
}) => {
  let display = { base: 'block', md: 'block', lg: 'block' };

  if (showOn === 'mobile') {
    display = { base: 'block', md: 'none', lg: 'none' };
  } else if (showOn === 'tablet') {
    display = { base: 'none', md: 'block', lg: 'none' };
  } else if (showOn === 'desktop') {
    display = { base: 'none', md: 'none', lg: 'block' };
  }

  if (hideOn === 'mobile') {
    display = { base: 'none', md: 'block', lg: 'block' };
  } else if (hideOn === 'tablet') {
    display = { base: 'block', md: 'none', lg: 'block' };
  } else if (hideOn === 'desktop') {
    display = { base: 'block', md: 'block', lg: 'none' };
  }

  return (
    <Box display={display} {...props}>
      {children}
    </Box>
  );
};

export default ResponsiveContainer;
