import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Card,
  CardBody,
  Button,
  useToast,
  Icon,
  Badge,
  useColorModeValue,
  Center,
  Divider,
  Link,
  Skeleton,
  Flex,

} from '@chakra-ui/react';
import {
  FiBell,
  FiInfo,
  FiAlertTriangle,
  FiAlertCircle,
  FiTrash2,
  FiTrendingUp,
  FiActivity,
  FiUsers,
  FiExternalLink,
  FiCheckCircle
} from 'react-icons/fi';
import { useNotifications } from '../hooks/useNotifications';
import { useAuth } from '../context/AuthContext';
import { formatDistanceToNow } from 'date-fns';
import { tr } from 'date-fns/locale';

const NotificationsPage: React.FC = () => {
  const toast = useToast();
  const { user } = useAuth();
  const {
    notifications,
    unreadCount,
    loading,
    error,
    // markAsRead, // Commented out unused function
    markAllAsRead,
    deleteNotification,
    fetchNotifications
    // createNotification // Commented out unused function
  } = useNotifications();

  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreNotifications, setHasMoreNotifications] = useState(true);

  // Color mode values
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');

  // İlk yükleme
  useEffect(() => {
    if (user) {
      fetchNotifications(20, 0, false);
    }
  }, [user, fetchNotifications]);







  // Bildirim ikonunu al
  const getNotificationIcon = (type: string, severity: string) => {
    switch (type) {
      case 'trade_opened':
      case 'trade_closed':
        return FiTrendingUp;
      case 'admin_announcement':
        return severity === 'error' ? FiAlertCircle : 
               severity === 'warning' ? FiAlertTriangle : FiInfo;
      case 'robot_status':
        return FiActivity;
      case 'subscription_update':
        return FiUsers;
      default:
        return FiInfo;
    }
  };

  // Şiddete göre renk al
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'success':
        return 'green';
      case 'warning':
        return 'orange';
      case 'error':
        return 'red';
      default:
        return 'blue';
    }
  };

  // Bildirim tipine göre kategori adı
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'trade_opened':
        return 'Pozisyon Açıldı';
      case 'trade_closed':
        return 'Pozisyon Kapandı';
      case 'admin_announcement':
        return 'Admin Duyurusu';
      case 'robot_status':
        return 'Robot Durumu';
      case 'subscription_update':
        return 'Abonelik';
      default:
        return 'Sistem';
    }
  };

  // Bildirimi işle (okundu işaretle ve aksiyonu çalıştır) - UNUSED FOR NOW
  // const handleNotificationClick = async (notification: Notification) => {
  //   if (!notification.is_read) {
  //     await markAsRead(notification.id);
  //   }

  //   // Eğer aksiyon URL'i varsa yönlendir
  //   if (notification.action_url) {
  //     // External link kontrolü
  //     if (notification.action_url.startsWith('http')) {
  //       window.open(notification.action_url, '_blank');
  //     }
  //     // Internal routing için router navigation gerekli
  //   }
  // };

  // Tümünü okundu işaretle
  const handleMarkAllAsRead = async () => {
    try {
      const result = await markAllAsRead();
      toast({
        title: 'Bildirimler Güncellendi',
        description: `${result} bildirim okundu olarak işaretlendi`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Hata',
        description: 'Bildirimler güncellenirken bir hata oluştu',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Bildirimi sil
  const handleDeleteNotification = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Parent click event'ini engelle
    
    try {
      const result = await deleteNotification(notificationId);
      if (result) {
        toast({
          title: 'Bildirim Silindi',
          description: 'Bildirim başarıyla silindi',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err) {
      toast({
        title: 'Hata',
        description: 'Bildirim silinirken bir hata oluştu',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Yeni bildirimler yükle
  const loadMoreNotifications = async () => {
    if (isLoadingMore || !hasMoreNotifications) return;

    setIsLoadingMore(true);
    try {
      const newNotifications = await fetchNotifications(20, notifications.length, false);
      if (!newNotifications || newNotifications.length < 20) {
        setHasMoreNotifications(false);
      }
    } catch (error) {
      // Hata durumunda sessizce devam et
    } finally {
      setIsLoadingMore(false);
    }
  };



  if (loading && notifications.length === 0) {
    return (
      <Box p={6}>
        <VStack spacing={4}>
          <Skeleton height="40px" width="300px" />
          <Skeleton height="20px" width="200px" />
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} height="80px" width="100%" />
          ))}
        </VStack>
      </Box>
    );
  }

  return (
    <Box p={6}>
      <VStack spacing={8} align="stretch">
        {/* Header */}
        <Box>
          <HStack justify="space-between" align="center" mb={2}>
            <HStack>
              <Icon as={FiBell} boxSize={6} color="blue.500" />
              <Heading as="h1" size="xl">
                Bildirimler
              </Heading>
              {unreadCount > 0 && (
                <Badge colorScheme="red" borderRadius="full" px={2}>
                  {unreadCount} Okunmamış
                </Badge>
              )}
            </HStack>
            
            {unreadCount > 0 && (
              <Button
                leftIcon={<Icon as={FiCheckCircle} />}
                colorScheme="blue"
                variant="outline"
                size="sm"
                onClick={handleMarkAllAsRead}
              >
                Tümünü Okundu İşaretle
              </Button>
            )}
          </HStack>
          
          <Text color={secondaryTextColor}>
            Size gönderilen bildirimler ve duyurular
          </Text>
        </Box>



        {/* Error State */}
        {error && (
          <Card bg="red.50" borderColor="red.200" mb={4}>
            <CardBody>
              <HStack>
                <Icon as={FiAlertCircle} color="red.500" />
                <Text color="red.700">{error}</Text>
              </HStack>
            </CardBody>
          </Card>
        )}

        {/* Bildirimler Listesi */}
        <VStack spacing={4} align="stretch" data-testid="notifications-list">

          {/* Empty State */}
          {!loading && notifications.length === 0 && !error && (
            <Center py={20}>
              <VStack spacing={4}>
                <Icon as={FiBell} boxSize={20} color="gray.300" />
                <Heading size="md" color={secondaryTextColor}>
                  Henüz bildirim yok
                </Heading>
                <Text color={secondaryTextColor} textAlign="center">
                  Size gönderilen bildirimler burada görünecek
                </Text>

              </VStack>
            </Center>
          )}



          {/* NOTIFICATIONS LIST - SIMPLIFIED */}
          {notifications.map((notification, index) => (
            <Box key={notification.id || index} p={1}>
              <Card
                data-testid="notification-item"
                bg="white"
                borderColor="gray.200"
                borderWidth="2px"
                shadow="md"
                style={{
                  position: 'relative',
                  zIndex: 1,
                  opacity: 1,
                  display: 'block',
                  minHeight: '120px',
                  width: '100%',
                  backgroundColor: notification.is_read ? '#f8f9fa' : '#fff',
                  border: notification.is_read ? '1px solid #e9ecef' : '2px solid #007bff'
                }}
              >
                <CardBody p={4}>

                  
                  <VStack align="stretch" spacing={3}>
                    <HStack spacing={4} align="flex-start">
                      {/* Icon */}
                      <Icon
                        as={getNotificationIcon(notification.type, notification.severity)}
                        boxSize={6}
                        color={getSeverityColor(notification.severity) + '.500'}
                        flexShrink={0}
                        mt={1}
                      />

                      {/* Content */}
                      <VStack align="flex-start" flex="1" spacing={2}>
                        <HStack justify="space-between" width="100%">
                          <VStack align="flex-start" spacing={1}>
                            <HStack>
                              <Badge
                                colorScheme={getSeverityColor(notification.severity)}
                                size="sm"
                              >
                                {getTypeLabel(notification.type)}
                              </Badge>
                              {!notification.is_read && (
                                <Badge colorScheme="blue" size="sm">
                                  Yeni
                                </Badge>
                              )}
                            </HStack>
                            
                            <Heading size="sm" color={textColor} data-testid="notification-title">
                              {notification.title || 'Başlık yok'}
                            </Heading>
                          </VStack>

                          <HStack spacing={2} flexShrink={0}>
                            <Text fontSize="xs" color={secondaryTextColor} data-testid="notification-date">
                              {notification.created_at ? formatDistanceToNow(new Date(notification.created_at), {
                                addSuffix: true,
                                locale: tr
                              }) : 'Tarih yok'}
                            </Text>
                            
                            <Button
                              size="xs"
                              variant="ghost"
                              colorScheme="red"
                              onClick={(e) => handleDeleteNotification(notification.id, e)}
                              _hover={{ bg: 'red.100' }}
                              aria-label="Bildirimi sil"
                            >
                              <Icon as={FiTrash2} boxSize={3} />
                            </Button>
                          </HStack>
                        </HStack>

                        <Text color={textColor} fontSize="sm" data-testid="notification-message">
                          {notification.message || 'Mesaj yok'}
                        </Text>

                        {/* Action link */}
                        {notification.action_url && notification.action_label && (
                          <>
                            <Divider />
                            <Link
                              href={notification.action_url}
                              isExternal={notification.action_url.startsWith('http')}
                              color="blue.500"
                              fontSize="sm"
                              fontWeight="medium"
                              _hover={{ textDecoration: 'underline' }}
                            >
                              <HStack>
                                <Text>{notification.action_label}</Text>
                                {notification.action_url.startsWith('http') && (
                                  <Icon as={FiExternalLink} boxSize={3} />
                                )}
                              </HStack>
                            </Link>
                          </>
                        )}
                      </VStack>
                    </HStack>
                  </VStack>
                </CardBody>
              </Card>
            </Box>
          ))}

          {/* Load More Button */}
          {hasMoreNotifications && (
            <Flex justify="center" pt={4}>
              <Button
                onClick={loadMoreNotifications}
                isLoading={isLoadingMore}
                loadingText="Yükleniyor..."
                colorScheme="blue"
                variant="outline"
              >
                Daha Fazla Yükle
              </Button>
            </Flex>
          )}
        </VStack>
      </VStack>
    </Box>
  );
};

export default NotificationsPage; 