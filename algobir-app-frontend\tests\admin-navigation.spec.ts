import { test, expect } from '@playwright/test';

test.describe('Admin Navigation Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('http://localhost:3001');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display admin menu for admin users', async ({ page }) => {
    // First, we need to login as an admin user
    // This test assumes there's a way to authenticate as admin
    // You may need to modify this based on your authentication flow
    
    // Look for the admin menu in the sidebar
    const adminMenu = page.locator('[data-testid="sidebar"]').locator('text=Admin Paneli');
    
    // Check if admin menu is visible (this will depend on user permissions)
    const isVisible = await adminMenu.isVisible();
    
    if (isVisible) {
      // If admin menu is visible, test its functionality
      await expect(adminMenu).toBeVisible();
      
      // Click on admin menu to open submenu
      await adminMenu.click();
      
      // Wait for submenu to appear
      await page.waitForTimeout(500);
      
      // Check for submenu items
      const submenuPanel = page.locator('[role="dialog"][aria-label*="Admin Paneli"]');
      await expect(submenuPanel).toBeVisible();
      
      // Verify all required submenu items are present
      const expectedSubmenuItems = [
        'Kullanıcı Yönetimi',
        'İşlem Yönetimi', 
        'Bildirim Yönetimi',
        'Sistem İstatistikleri',
        'Sistem Durumu'
      ];
      
      for (const item of expectedSubmenuItems) {
        const submenuItem = submenuPanel.locator(`text=${item}`);
        await expect(submenuItem).toBeVisible();
      }
    } else {
      console.log('Admin menu not visible - user may not have admin privileges');
    }
  });

  test('should navigate to admin pages correctly', async ({ page }) => {
    // Check if admin menu is available
    const adminMenu = page.locator('[data-testid="sidebar"]').locator('text=Admin Paneli');
    const isVisible = await adminMenu.isVisible();
    
    if (isVisible) {
      // Click admin menu to open submenu
      await adminMenu.click();
      await page.waitForTimeout(500);
      
      const submenuPanel = page.locator('[role="dialog"][aria-label*="Admin Paneli"]');
      
      // Test navigation to User Management
      const userManagementLink = submenuPanel.locator('text=Kullanıcı Yönetimi');
      await userManagementLink.click();
      
      // Wait for navigation
      await page.waitForURL('**/admin/users');
      await expect(page).toHaveURL(/.*\/admin\/users/);
      
      // Go back and test other navigation items
      await page.goBack();
      await page.waitForTimeout(500);
      
      // Test navigation to Notification Management
      await adminMenu.click();
      await page.waitForTimeout(500);
      
      const notificationManagementLink = submenuPanel.locator('text=Bildirim Yönetimi');
      await notificationManagementLink.click();
      
      await page.waitForURL('**/admin/notifications');
      await expect(page).toHaveURL(/.*\/admin\/notifications/);
    }
  });

  test('should have proper admin route protection', async ({ page }) => {
    // Try to access admin routes directly
    const adminRoutes = [
      '/admin',
      '/admin/users',
      '/admin/trades',
      '/admin/notifications',
      '/admin/statistics',
      '/admin/status'
    ];
    
    for (const route of adminRoutes) {
      await page.goto(`http://localhost:3001${route}`);
      
      // Check if user is redirected or shown an error
      // This depends on your authentication implementation
      const currentUrl = page.url();
      
      // If not admin, should be redirected to home or login
      if (!currentUrl.includes('/admin')) {
        console.log(`Route ${route} properly protected - redirected to ${currentUrl}`);
      } else {
        // If admin access is granted, verify page loads correctly
        await page.waitForLoadState('networkidle');
        console.log(`Admin route ${route} accessible`);
      }
    }
  });

  test('should have responsive admin navigation', async ({ page }) => {
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    
    const adminMenu = page.locator('text=Admin Paneli');
    const isVisible = await adminMenu.isVisible();
    
    if (isVisible) {
      // On mobile, admin menu should be in drawer
      const mobileMenuButton = page.locator('[aria-label*="menü"]');
      if (await mobileMenuButton.isVisible()) {
        await mobileMenuButton.click();
        await page.waitForTimeout(500);
        
        // Check if admin menu is in mobile drawer
        const drawer = page.locator('[role="dialog"]');
        const adminMenuInDrawer = drawer.locator('text=Admin Paneli');
        await expect(adminMenuInDrawer).toBeVisible();
      }
    }
    
    // Test desktop view
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(500);
    
    if (isVisible) {
      // On desktop, admin menu should be in sidebar
      const sidebarAdminMenu = page.locator('[data-testid="sidebar"]').locator('text=Admin Paneli');
      await expect(sidebarAdminMenu).toBeVisible();
    }
  });

  test('should handle sidebar collapse/expand with admin menu', async ({ page }) => {
    const adminMenu = page.locator('[data-testid="sidebar"]').locator('text=Admin Paneli');
    const isVisible = await adminMenu.isVisible();
    
    if (isVisible) {
      // Test sidebar collapse functionality
      const sidebar = page.locator('[data-testid="sidebar"]');
      
      // Hover over left edge to expand if collapsed
      await page.mouse.move(30, 300);
      await page.waitForTimeout(200);
      
      // Check if admin menu is still accessible
      await expect(adminMenu).toBeVisible();
      
      // Move mouse away to collapse
      await page.mouse.move(500, 300);
      await page.waitForTimeout(1000);
      
      // Admin menu should still be accessible (as icon or collapsed state)
      const sidebarContent = sidebar.locator('*');
      await expect(sidebarContent.first()).toBeVisible();
    }
  });
});
