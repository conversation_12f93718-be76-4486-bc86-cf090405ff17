import { useState } from 'react';
import { 
  Box, 
  Heading, 
  Text, 
  Card, 
  CardBody, 
  Flex, 
  Button, 
  Grid, 
  GridItem, 
  VStack, 
  HStack 
} from '@chakra-ui/react';
import { FaChartLine, FaRobot, FaShieldAlt } from 'react-icons/fa';
import Login from './auth/Login';
import Signup from './auth/Signup';

const AuthPage = () => {
  const [tabIndex, setTabIndex] = useState(0);

  const handleTabChange = (index: number) => {
    setTabIndex(index);
  };

  return (
    <Box minH="100vh" bg="gray.50">
      <Grid templateColumns={{ base: '1fr', md: '1fr 1fr' }} minH="100vh">
        {/* Sol Sütun - Tanıtım/Görsel Alanı */}
        <GridItem 
          display={{ base: 'none', md: 'flex' }}
          bg="brand.900"
          backgroundImage="linear-gradient(135deg, #04365d 0%, #006ebd 100%)"
          color="white"
          position="relative"
          overflow="hidden"
        >
          <Box p={10} zIndex={1} width="100%">
            <VStack align="start" spacing={8} h="full" justify="center">
              <Heading 
                as="h1" 
                size="2xl" 
                lineHeight="shorter" 
                fontWeight="bold"
                mb={4}
              >
                Algobir App
              </Heading>
              
              <Text fontSize="xl" fontWeight="medium" maxW="md">
                Alım-satım sinyallerinizi otomatik olarak yönetin ve finansal hedeflerinize daha hızlı ulaşın.
              </Text>
              
              <VStack spacing={6} alignItems="flex-start" width="100%" mt={8}>
                <HStack spacing={4}>
                  <Box color="accent.500">
                    <FaChartLine size={24} />
                  </Box>
                  <Box>
                    <Text fontWeight="bold" fontSize="lg">Gerçek Zamanlı İzleme</Text>
                    <Text>Tüm işlemlerinizi anlık olarak takip edin.</Text>
                  </Box>
                </HStack>
                
                <HStack spacing={4}>
                  <Box color="accent.500">
                    <FaRobot size={24} />
                  </Box>
                  <Box>
                    <Text fontWeight="bold" fontSize="lg">Otomatik Yönetim</Text>
                    <Text>Sinyallerinizi tam otomatik olarak yönlendirin.</Text>
                  </Box>
                </HStack>
                
                <HStack spacing={4}>
                  <Box color="accent.500">
                    <FaShieldAlt size={24} />
                  </Box>
                  <Box>
                    <Text fontWeight="bold" fontSize="lg">Güvenilir Altyapı</Text>
                    <Text>Verileriniz güvenle saklanır ve iletilir.</Text>
                  </Box>
                </HStack>
              </VStack>
            </VStack>
          </Box>
          
          {/* Dekoratif arka plan deseni */}
          <Box 
            position="absolute" 
            right="-10%" 
            top="50%" 
            transform="translateY(-50%)" 
            opacity={0.1} 
            width="70%" 
            height="140%" 
            bgImage="url('https://cdn.pixabay.com/photo/2021/02/03/00/10/crypto-5975470_1280.png')"
            bgPosition="center"
            bgSize="contain"
            bgRepeat="no-repeat"
          />
        </GridItem>
        
        {/* Sağ Sütun - Form Alanı */}
        <GridItem 
          display="flex" 
          alignItems="center" 
          justifyContent="center" 
          p={{ base: 4, md: 10 }}
          bg="gray.50"
        >
          <Box w="100%" maxW="md">
            {/* Mobil ekranlar için logo */}
            <Box display={{ base: "block", md: "none" }} mb={10}>
              <Heading 
                as="h1" 
                size="xl" 
                textAlign="center" 
                color="brand.700"
              >
                Algobir App
              </Heading>
              <Text fontSize="md" textAlign="center" color="gray.600">
                Alım-satım sinyallerinizi otomatik olarak yönetin
              </Text>
            </Box>
            
            <Card 
              borderRadius="xl" 
              boxShadow="lg" 
              bg="white"
              borderWidth="1px"
              borderColor="gray.200"
              overflow="hidden"
            >
              <CardBody p={6}>
                <Flex 
                  width="100%" 
                  justifyContent="center" 
                  borderBottom="1px" 
                  borderColor="gray.200"
                  pb={3}
                >
                  <Button 
                    variant={tabIndex === 0 ? "solid" : "ghost"} 
                    mr={2} 
                    colorScheme="brand"
                    onClick={() => handleTabChange(0)}
                  >
                    Giriş
                  </Button>
                  <Button 
                    variant={tabIndex === 1 ? "solid" : "ghost"} 
                    colorScheme="brand"
                    onClick={() => handleTabChange(1)}
                  >
                    Kayıt Ol
                  </Button>
                </Flex>

                <Box width="100%" mt={4}>
                  {tabIndex === 0 && <Login />}
                  {tabIndex === 1 && <Signup />}
                </Box>
              </CardBody>
            </Card>
          </Box>
        </GridItem>
      </Grid>
    </Box>
  );
};

export default AuthPage; 