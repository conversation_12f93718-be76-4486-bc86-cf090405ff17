import React from 'react';
import { 
  Box, 
  Heading, 
  Text, 
  VStack, 
  Container,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Badge
} from '@chakra-ui/react';

const FAQPage: React.FC = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const textColor = useColorModeValue('gray.700', 'gray.200');

  return (
    <Box minH="100vh" bg={bgColor}>
      <Container maxW="1200px" pb={20}>
        <VStack spacing={12} align="stretch">
          <VStack spacing={6} textAlign="center">
            <Badge colorScheme="orange" fontSize="md" px={4} py={2} borderRadius="full">
              ❓ Yardım ve Destek Merkezi
            </Badge>
            <Heading 
              as="h1" 
              size="3xl" 
              bgGradient="linear(to-r, orange.400, red.400)"
              bgClip="text"
              fontWeight="extrabold"
            >
              <PERSON><PERSON><PERSON>lan <PERSON>
            </Heading>
            <Text fontSize="xl" color={textColor} maxW="800px">
              En çok merak edilen konular, yaygın sorunların çözümleri ve teknik detaylar. 
              Aradığınızı bulamazsanız destek ekibimizle iletişime geçin.
            </Text>
          </VStack>

          <Alert status="info" borderRadius="lg">
            <AlertIcon />
            <Box>
              <AlertTitle>🚧 Bu Sayfa Yakında Geliyor!</AlertTitle>
              <AlertDescription>
                Kapsamlı FAQ sayfamız şu anda hazırlanmaktadır. Sorularınız için lütfen:
                <br />
                • Bro-Robot Kılavuzunu inceleyin (satıcılar için)
                <br />
                • Solo-Robot Kılavuzunu inceleyin (bireysel kullanıcılar için)
                <br />
                • Destek ekibimizle iletişime geçin
              </AlertDescription>
            </Box>
          </Alert>
        </VStack>
      </Container>
    </Box>
  );
};

export default FAQPage; 