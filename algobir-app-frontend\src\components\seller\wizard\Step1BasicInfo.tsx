import React from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  FormErrorMessage,
  VStack,
  Heading,
  Text,
  HStack,
  Icon,
} from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';
import { FaRobot, FaFileAlt } from 'react-icons/fa';
import { RobotFormData } from '../RobotSetupWizard';

export const Step1BasicInfo: React.FC = () => {
  const { register, formState: { errors } } = useFormContext<RobotFormData>();

  return (
    <Box>
      <VStack spacing={6} align="stretch">
        <Box textAlign="center" mb={4}>
          <HStack justify="center" mb={3}>
            <Icon as={FaRobot} boxSize={8} color="brand.500" />
          </HStack>
          <Heading size="lg" mb={2}>Robot Temel Bilgileri</Heading>
          <Text color="gray.600">
            Robotunuzun adını ve açıklamasını girin. Bu bilgiler pazar yerinde görünecektir.
          </Text>
        </Box>

        <FormControl isInvalid={!!errors.name} isRequired>
          <FormLabel fontWeight="semibold">
            <HStack>
              <Text>Robot Adı</Text>
            </HStack>
          </FormLabel>
          <Input
            {...register('name', {
              required: 'Robot adı zorunludur',
              minLength: {
                value: 3,
                message: 'Robot adı en az 3 karakter olmalıdır'
              },
              maxLength: {
                value: 100,
                message: 'Robot adı en fazla 100 karakter olabilir'
              }
            })}
            placeholder="Örn: Bitcoin Momentum Trader"
            size="lg"
          />
          <FormErrorMessage>{errors.name?.message}</FormErrorMessage>
        </FormControl>

        <FormControl isInvalid={!!errors.description}>
          <FormLabel fontWeight="semibold">
            <HStack>
              <Icon as={FaFileAlt} />
              <Text>Açıklama</Text>
            </HStack>
          </FormLabel>
          <Textarea
            {...register('description', {
              maxLength: {
                value: 500,
                message: 'Açıklama en fazla 500 karakter olabilir'
              }
            })}
            placeholder="Robotunuzun ne yaptığını, hangi stratejileri kullandığını açıklayın..."
            rows={4}
            resize="vertical"
            size="lg"
          />
          <FormErrorMessage>{errors.description?.message}</FormErrorMessage>
          <Text fontSize="sm" color="gray.500" mt={1}>
            Potansiyel abonelerin robotunuzu daha iyi anlaması için detaylı bir açıklama yazın.
          </Text>
        </FormControl>

        <FormControl>
          <FormLabel fontWeight="semibold">Robot Versiyonu</FormLabel>
          <Input
            {...register('version')}
            placeholder="1.0"
            size="lg"
          />
          <Text fontSize="sm" color="gray.500" mt={1}>
            Robot versiyonu güncellemelerinizi takip etmenize yardımcı olur.
          </Text>
        </FormControl>

        <FormControl>
          <FormLabel fontWeight="semibold">Robot Görseli (URL)</FormLabel>
          <Input
            {...register('image_url')}
            placeholder="https://example.com/robot-image.jpg"
            size="lg"
          />
          <Text fontSize="sm" color="gray.500" mt={1}>
            Robotunuzu temsil eden bir görsel URL'si ekleyebilirsiniz (isteğe bağlı).
          </Text>
        </FormControl>
      </VStack>
    </Box>
  );
}; 