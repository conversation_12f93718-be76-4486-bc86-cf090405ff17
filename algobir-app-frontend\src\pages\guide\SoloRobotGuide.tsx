import React from 'react';
import { 
  Box, 
  Heading, 
  Text, 
  VStack, 
  Code, 
  List, 
  ListItem, 
  ListIcon,
  Container,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Badge,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Button,
  HStack,
  Icon,
  Stack,
  Progress
} from '@chakra-ui/react';
import { 
  MdCheckCircle, 
  MdSecurity, 
  MdTimeline,
  MdWarning,
  MdTrendingUp,
  MdAutoFixHigh,
  MdCode,
  MdSettings,
  MdIntegrationInstructions
} from 'react-icons/md';
import { FaRocket, FaCog, FaChartLine } from 'react-icons/fa';
import { SiTradingview } from 'react-icons/si';

const SoloRobotGuide: React.FC = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const accentColor = useColorModeValue('brand.500', 'brand.300');
  // Removed unused variables to fix TypeScript warnings

    return (
    <Box minH="100vh" bg={bgColor}>
      <Container maxW="1200px" pb={20}>
        <VStack spacing={12} align="stretch">
          {/* Header Section */}
          <VStack spacing={10} textAlign="center">
            <Badge 
              colorScheme="brand" 
              fontSize="xl" 
              px={8} 
              py={4} 
              borderRadius="full"
              bgGradient="linear(to-r, brand.400, blue.400)"
              color="white"
              fontWeight="bold"
              textTransform="none"
            >
              👤 Bireysel Trader Rehberi
            </Badge>
            <VStack spacing={6}>
              <Heading 
                as="h1" 
                size="4xl" 
                bgGradient="linear(to-r, brand.400, blue.400, green.400)"
                bgClip="text"
                fontWeight="black"
                letterSpacing="tight"
                lineHeight="1.1"
              >
                Solo-Robot
                <br />
                Kapsamlı Kılavuzu
              </Heading>
              <Text fontSize="2xl" color={textColor} maxW="900px" lineHeight="1.6">
                TradingView sinyallerinizi <strong>tamamen otomatize</strong> etmenin profesyonel yolu. 
                Kişisel ticaret stratejilerinizi 7/24 çalışan bir sisteme dönüştürün.
              </Text>
              <HStack spacing={6} pt={4}>
                <Badge colorScheme="green" fontSize="lg" px={4} py={2}>
                  ⚡ Kurulum: 15 dakika
                </Badge>
                <Badge colorScheme="blue" fontSize="lg" px={4} py={2}>
                  🚀 Hız: &lt;2 saniye
                </Badge>
                <Badge colorScheme="purple" fontSize="lg" px={4} py={2}>
                  🔒 256-bit Güvenlik
                </Badge>
              </HStack>
            </VStack>
          </VStack>

          {/* Enhanced Stats */}
          <Box bg={cardBg} p={10} borderRadius="2xl" border="3px solid" borderColor="brand.200">
            <VStack spacing={8}>
              <Heading size="lg" color="brand.600" textAlign="center">
                ⚡ Solo-Robot Sistem Performansı
              </Heading>
              <SimpleGrid columns={{ base: 2, md: 4 }} spacing={8} w="full">
                <Stat textAlign="center" bg={useColorModeValue('green.50', 'green.900')} p={6} borderRadius="xl" border="2px solid" borderColor="green.200">
                  <StatLabel fontSize="sm" color="gray.500" fontWeight="bold">Kurulum Süresi</StatLabel>
                  <StatNumber color="green.500" fontSize="3xl" fontWeight="black">15 dk</StatNumber>
                  <StatHelpText fontSize="sm" color="green.600">Tamamen hazır sistem</StatHelpText>
                  <Progress value={95} colorScheme="green" size="sm" mt={2} borderRadius="full" />
                </Stat>
                <Stat textAlign="center" bg={useColorModeValue('blue.50', 'blue.900')} p={6} borderRadius="xl" border="2px solid" borderColor="blue.200">
                  <StatLabel fontSize="sm" color="gray.500" fontWeight="bold">Sinyal Hızı</StatLabel>
                  <StatNumber color="blue.500" fontSize="3xl" fontWeight="black">&lt;2 sn</StatNumber>
                  <StatHelpText fontSize="sm" color="blue.600">TradingView → Borsa</StatHelpText>
                  <Progress value={98} colorScheme="blue" size="sm" mt={2} borderRadius="full" />
                </Stat>
                <Stat textAlign="center" bg={useColorModeValue('purple.50', 'purple.900')} p={6} borderRadius="xl" border="2px solid" borderColor="purple.200">
                  <StatLabel fontSize="sm" color="gray.500" fontWeight="bold">Güvenlik Seviyesi</StatLabel>
                  <StatNumber color="purple.500" fontSize="3xl" fontWeight="black">256-bit</StatNumber>
                  <StatHelpText fontSize="sm" color="purple.600">SSL şifreleme</StatHelpText>
                  <Progress value={100} colorScheme="purple" size="sm" mt={2} borderRadius="full" />
                </Stat>
                <Stat textAlign="center" bg={useColorModeValue('orange.50', 'orange.900')} p={6} borderRadius="xl" border="2px solid" borderColor="orange.200">
                  <StatLabel fontSize="sm" color="gray.500" fontWeight="bold">Desteklenen Borsalar</StatLabel>
                  <StatNumber color="orange.500" fontSize="3xl" fontWeight="black">15+</StatNumber>
                  <StatHelpText fontSize="sm" color="orange.600">Popüler platformlar</StatHelpText>
                  <Progress value={90} colorScheme="orange" size="sm" mt={2} borderRadius="full" />
                </Stat>
              </SimpleGrid>
              
              <Alert status="success" borderRadius="xl" bg={useColorModeValue('green.50', 'green.900')} border="2px solid" borderColor="green.200">
                <AlertIcon />
                <Box flex="1">
                  <AlertTitle fontSize="lg">✅ Hemen Kullanıma Hazır!</AlertTitle>
                  <AlertDescription fontSize="md">
                    Solo-Robot sistemi plug-and-play mantığıyla çalışır. Herhangi bir teknik bilgi 
                    gerektirmez ve 15 dakikada kuruluma hazır hale gelir.
                  </AlertDescription>
                </Box>
              </Alert>
            </VStack>
          </Box>

          {/* Main Content Tabs */}
          <Box bg={cardBg} borderRadius="2xl" border="2px solid" borderColor="brand.100" overflow="hidden">
            <Tabs variant="enclosed" colorScheme="brand" size="lg">
              <TabList bg={useColorModeValue('brand.50', 'brand.900')} border="none">
                <Tab _selected={{ bg: 'brand.500', color: 'white' }} fontWeight="bold" fontSize="lg" py={4}>
                  <Icon as={FaRocket} mr={3} />Hızlı Başlangıç
                </Tab>
                <Tab _selected={{ bg: 'brand.500', color: 'white' }} fontWeight="bold" fontSize="lg" py={4}>
                  <Icon as={SiTradingview} mr={3} />TradingView Entegrasyonu
                </Tab>
                <Tab _selected={{ bg: 'brand.500', color: 'white' }} fontWeight="bold" fontSize="lg" py={4}>
                  <Icon as={FaCog} mr={3} />Gelişmiş Ayarlar
                </Tab>
                <Tab _selected={{ bg: 'brand.500', color: 'white' }} fontWeight="bold" fontSize="lg" py={4}>
                  <Icon as={FaChartLine} mr={3} />İzleme ve Analiz
                </Tab>
              </TabList>

            <TabPanels>
              {/* Hızlı Başlangıç Tab */}
              <TabPanel>
                <VStack spacing={8} align="stretch">
                  <Alert status="info" borderRadius="lg">
                    <AlertIcon />
                    <Box>
                      <AlertTitle>🎯 Solo-Robot Nedir?</AlertTitle>
                      <AlertDescription>
                        Solo-Robot sistemi, TradingView veya diğer kaynaklardan gelen sinyallerinizi 
                        otomatik olarak borsanıza ileten kişisel otomasyon sisteminizdir. 
                        Hiçbir manuel müdahale gerektirmez.
                      </AlertDescription>
                    </Box>
                  </Alert>

                  <Box bg={cardBg} p={8} borderRadius="xl">
                    <Heading size="lg" mb={6} color={accentColor}>
                      <Icon as={MdTimeline} mr={3} />
                      3 Adımda Solo-Robot Kurulumu
                    </Heading>
                    
                    <VStack spacing={8} align="stretch">
                      {[
                        {
                          step: 1,
                          title: "API Anahtarlarını Ayarlayın",
                          description: "Borsanızdan aldığınız API anahtarlarını güvenli olarak sisteme kaydedin. Yalnızca trading yetkisi verin, withdrawal yetkisi vermeyin.",
                          time: "5 dakika",
                          details: [
                            "Borsanızdan API Key ve Secret alın",
                            "Yönetim panelinden güvenli kayıt yapın",
                            "IP kısıtlaması ekleyin (önerilen)"
                          ]
                        },
                        {
                          step: 2,
                          title: "Webhook URL'inizi Kopyalayın",
                          description: "Size özel webhook URL'inizi alın ve TradingView alarm ayarlarınızda kullanmak üzere kopyalayın.",
                          time: "1 dakika",
                          details: [
                            "Yönetim panelinden URL'inizi kopyalayın",
                            "URL'in benzersiz ve güvenli olduğunu doğrulayın",
                            "Test amaçlı bir sinyal gönderin"
                          ]
                        },
                        {
                          step: 3,
                          title: "TradingView Alarmlarını Yapılandırın",
                          description: "TradingView'da alarm oluştururken webhook URL'inizi ve doğru JSON formatını kullanın.",
                          time: "5 dakika",
                          details: [
                            "TradingView'da yeni alarm oluşturun",
                            "Webhook seçeneğini işaretleyin",
                            "JSON formatını doğru şekilde yapılandırın"
                          ]
                        }
                      ].map((item, index) => (
                        <Box key={index} bg={useColorModeValue('gray.50', 'gray.700')} p={6} borderRadius="lg">
                          <HStack spacing={4} align="start" mb={4}>
                            <Box
                              bg="brand.500"
                              color="white"
                              borderRadius="full"
                              w={10}
                              h={10}
                              display="flex"
                              alignItems="center"
                              justifyContent="center"
                              fontWeight="bold"
                              flexShrink={0}
                              fontSize="lg"
                            >
                              {item.step}
                            </Box>
                            <VStack align="start" spacing={2} flex={1}>
                              <Text fontSize="xl" fontWeight="bold" color={textColor}>{item.title}</Text>
                              <Text color="gray.500">{item.description}</Text>
                              <Badge colorScheme="green" size="sm">{item.time}</Badge>
                            </VStack>
                          </HStack>
                          
                          <List spacing={2} pl={14}>
                            {item.details.map((detail, idx) => (
                              <ListItem key={idx}>
                                <ListIcon as={MdCheckCircle} color="green.500" />
                                <Text fontSize="sm" color={textColor}>{detail}</Text>
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      ))}
                    </VStack>
                  </Box>

                  <Alert status="success" borderRadius="lg">
                    <AlertIcon />
                    <Box>
                      <AlertTitle>🚀 Hemen Test Edin!</AlertTitle>
                      <AlertDescription>
                        Kurulum tamamlandıktan sonra küçük bir pozisyonla test işlemi yaparak 
                        sistemin doğru çalıştığını doğrulayın.
                      </AlertDescription>
                    </Box>
                  </Alert>
                </VStack>
              </TabPanel>

              {/* TradingView Entegrasyonu Tab */}
              <TabPanel>
                <VStack spacing={8} align="stretch">
                  <Heading size="lg" color={accentColor}>
                                          <Icon as={SiTradingview} mr={3} />
                    TradingView Entegrasyon Rehberi
                  </Heading>

                  <Accordion allowMultiple>
                    <AccordionItem>
                      <AccordionButton>
                        <Box flex="1" textAlign="left">
                          <Text fontWeight="bold">📋 Alarm Oluşturma Adım Adım</Text>
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                      <AccordionPanel pb={4}>
            <VStack spacing={6} align="stretch">
                          <Text color={textColor}>
                            TradingView'da alarm oluştururken aşağıdaki adımları takip edin:
                </Text>

                          <Stack spacing={4}>
                            <Box>
                              <Text fontWeight="bold" mb={2}>1. Alarm Panelini Açın</Text>
                              <Text fontSize="sm" color="gray.500">
                                Grafik üzerinde sağ tıklayıp "Add Alert" seçeneğini kullanın veya 
                                Alt+A kısayolunu kullanın.
                              </Text>
                            </Box>
                            
                            <Box>
                              <Text fontWeight="bold" mb={2}>2. Koşul Belirleyin</Text>
                              <Text fontSize="sm" color="gray.500">
                                Alarmın tetikleneceği koşulu seçin (fiyat seviyesi, indikatör sinyali vb.)
                </Text>
                            </Box>
                            
                            <Box>
                              <Text fontWeight="bold" mb={2}>3. Webhook Ayarları</Text>
                              <Text fontSize="sm" color="gray.500">
                                "Notifications" sekmesinde "Webhook URL" kutucuğunu işaretleyin ve 
                                size verilen URL'i yapıştırın.
                              </Text>
                            </Box>
                          </Stack>

                          <Alert status="warning" borderRadius="lg">
                            <AlertIcon />
                            <Box>
                              <AlertTitle>⚠️ Önemli Not</AlertTitle>
                              <AlertDescription>
                                TradingView Pro, Pro+ veya Premium hesabınızın olması gerekmektedir. 
                                Webhook özelliği ücretsiz hesaplarda kullanılamaz.
                              </AlertDescription>
                            </Box>
                          </Alert>
                        </VStack>
                      </AccordionPanel>
                    </AccordionItem>

                    <AccordionItem>
                      <AccordionButton>
                        <Box flex="1" textAlign="left">
                          <Text fontWeight="bold">📝 JSON Formatı ve Örnekler</Text>
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                      <AccordionPanel pb={4}>
                        <VStack spacing={6} align="stretch">
                          <Text color={textColor}>
                            Alarm mesajınız JSON formatında olmalıdır. İşte farklı senaryolar için örnekler:
                </Text>

                          <Tabs variant="soft-rounded" colorScheme="brand">
                            <TabList>
                              <Tab>Basit Alış</Tab>
                              <Tab>Basit Satış</Tab>
                              <Tab>Stop Loss/TP</Tab>
                              <Tab>Gelişmiş</Tab>
                            </TabList>

                            <TabPanels>
                              <TabPanel>
                <Code p={4} borderRadius="md" w="full" whiteSpace="pre-wrap">
{`{
  "name": "BTC Long Entry",
  "symbol": "BTCUSDT",
  "order_side": "BUY",
  "price": {{close}}
}`}
                                </Code>
                                <Text fontSize="sm" mt={2} color="gray.500">
                                  {`{{close}}`} TradingView'ın güncel kapanış fiyatını otomatik ekler.
                                </Text>
                              </TabPanel>
                              
                              <TabPanel>
                                <Code p={4} borderRadius="md" w="full" whiteSpace="pre-wrap">
{`{
  "name": "ETH Short Entry",
  "symbol": "ETHUSDT", 
  "order_side": "SELL",
  "price": {{close}}
}`}
                                </Code>
                              </TabPanel>
                              
                              <TabPanel>
                                <Code p={4} borderRadius="md" w="full" whiteSpace="pre-wrap">
{`{
  "name": "BTC Position Close",
  "symbol": "BTCUSDT",
  "order_side": "SELL",
  "price": {{close}},
  "position_type": "close",
  "close_reason": "stop_loss"
}`}
                                </Code>
                              </TabPanel>
                              
                              <TabPanel>
                                <Code p={4} borderRadius="md" w="full" whiteSpace="pre-wrap">
{`{
  "name": "{{ticker}} Momentum Signal",
  "symbol": "{{ticker}}",
  "order_side": "BUY", 
  "price": {{close}},
  "timeframe": "{{interval}}",
  "timestamp": "{{time}}",
  "strategy": "momentum_breakout",
  "rsi": {{rsi}},
  "volume": {{volume}}
}`}
                                </Code>
                                <Text fontSize="sm" mt={2} color="gray.500">
                                  TradingView değişkenlerini kullanarak dinamik sinyaller oluşturabilirsiniz.
                                </Text>
                              </TabPanel>
                            </TabPanels>
                          </Tabs>
                        </VStack>
                      </AccordionPanel>
                    </AccordionItem>

                    <AccordionItem>
                      <AccordionButton>
                        <Box flex="1" textAlign="left">
                          <Text fontWeight="bold">🔧 Yaygın Sorunlar ve Çözümler</Text>
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                      <AccordionPanel pb={4}>
                        <VStack spacing={4} align="stretch">
                          <List spacing={4}>
                            <ListItem>
                              <ListIcon as={MdWarning} color="orange.500" />
                              <Box>
                                <Text fontWeight="bold">Alarm Tetiklenmiyor</Text>
                                <Text fontSize="sm" color="gray.500">
                                  Koşul ayarlarınızı kontrol edin ve "Once Per Bar Close" seçeneğini işaretleyin.
                                </Text>
                              </Box>
                            </ListItem>
                            
                            <ListItem>
                              <ListIcon as={MdWarning} color="orange.500" />
                              <Box>
                                <Text fontWeight="bold">JSON Formatı Hatası</Text>
                                <Text fontSize="sm" color="gray.500">
                                  Süslü parantezlerin doğru olduğundan ve virgüllerin yerinde olduğundan emin olun.
                                </Text>
                              </Box>
                            </ListItem>
                            
                            <ListItem>
                              <ListIcon as={MdWarning} color="orange.500" />
                              <Box>
                                <Text fontWeight="bold">İşlem Gerçekleşmiyor</Text>
                                <Text fontSize="sm" color="gray.500">
                                  API anahtarlarınızın trading yetkisinin olduğunu ve bakiyenizin yeterli olduğunu kontrol edin.
                                </Text>
                              </Box>
                            </ListItem>
                          </List>

                          <Alert status="info" borderRadius="lg">
                            <AlertIcon />
                            <Box>
                              <AlertTitle>💡 Pro İpucu</AlertTitle>
                              <AlertDescription>
                                JSON formatınızı online JSON validator araçlarıyla test edebilirsiniz. 
                                Bu sayede syntax hatalarını önceden tespit edersiniz.
                              </AlertDescription>
                            </Box>
                          </Alert>
                        </VStack>
                      </AccordionPanel>
                    </AccordionItem>
                  </Accordion>
                </VStack>
              </TabPanel>

              {/* Gelişmiş Ayarlar Tab */}
              <TabPanel>
                <VStack spacing={8} align="stretch">
                  <Heading size="lg" color={accentColor}>
                    <Icon as={MdSettings} mr={3} />
                    Gelişmiş Yapılandırma
                  </Heading>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    <Box bg={cardBg} p={6} borderRadius="xl">
                      <Heading size="md" mb={4}>
                        <Icon as={MdSecurity} mr={2} />
                        Güvenlik Ayarları
                      </Heading>
                      
                      <VStack spacing={4} align="stretch">
                        <Box>
                          <Text fontWeight="bold" mb={2}>🔐 API Anahtarı Güvenliği</Text>
                          <List spacing={2} fontSize="sm">
                            <ListItem>
                              <ListIcon as={MdCheckCircle} color="green.500" />
                              Withdrawal yetkisi vermeyin
                            </ListItem>
                            <ListItem>
                              <ListIcon as={MdCheckCircle} color="green.500" />
                              IP kısıtlaması ekleyin
                            </ListItem>
                            <ListItem>
                              <ListIcon as={MdCheckCircle} color="green.500" />
                              Düzenli olarak yenileyin
                            </ListItem>
                          </List>
                        </Box>

                        <Box>
                          <Text fontWeight="bold" mb={2}>🔒 Webhook Güvenliği</Text>
                          <List spacing={2} fontSize="sm">
                            <ListItem>
                              <ListIcon as={MdCheckCircle} color="green.500" />
                              URL'inizi kimseyle paylaşmayın
                            </ListItem>
                            <ListItem>
                              <ListIcon as={MdCheckCircle} color="green.500" />
                              Şüpheli aktivite durumunda yenileyin
                            </ListItem>
                          </List>
                        </Box>
                      </VStack>
                    </Box>

                    <Box bg={cardBg} p={6} borderRadius="xl">
                      <Heading size="md" mb={4}>
                        <Icon as={MdAutoFixHigh} mr={2} />
                        Risk Yönetimi
                      </Heading>
                      
                      <VStack spacing={4} align="stretch">
                        <Box>
                          <Text fontWeight="bold" mb={2}>💰 Pozisyon Boyutu</Text>
                          <Text fontSize="sm" color="gray.500" mb={2}>
                            Sistem, ayarladığınız yatırım tutarına göre otomatik pozisyon hesaplar.
                          </Text>
                          <Code p={2} fontSize="sm">
                            Miktar = Yatırım Tutarı / Sinyal Fiyatı
                          </Code>
                        </Box>

                        <Box>
                          <Text fontWeight="bold" mb={2}>🛡️ Acil Durdurma</Text>
                          <Text fontSize="sm" color="gray.500">
                            Yönetim panelinden sistemi anlık olarak durdurabilirsiniz.
                          </Text>
                        </Box>
                      </VStack>
                    </Box>
                  </SimpleGrid>

                  <Box bg={cardBg} p={8} borderRadius="xl">
                    <Heading size="md" mb={6}>
                      <Icon as={MdCode} mr={2} />
                      Özel JSON Şablonları
                    </Heading>
                    
                    <Text color={textColor} mb={6}>
                      Farklı borsa ve broker'lar için özelleştirilmiş JSON formatları:
                    </Text>

                    <Tabs variant="line" colorScheme="brand">
                      <TabList>
                        <Tab>Binance</Tab>
                        <Tab>ByBit</Tab>
                        <Tab>OKX</Tab>
                        <Tab>Custom</Tab>
                      </TabList>

                      <TabPanels>
                        <TabPanel>
                          <Code p={4} borderRadius="md" w="full" whiteSpace="pre-wrap">
{`{
  "name": "Binance Signal",
  "symbol": "{{ticker}}",
  "side": "{{strategy.order.action}}",
  "type": "MARKET",
  "price": {{close}},
  "quantity": "calculated_auto",
  "timeInForce": "GTC"
}`}
                          </Code>
                        </TabPanel>
                        
                        <TabPanel>
                          <Code p={4} borderRadius="md" w="full" whiteSpace="pre-wrap">
{`{
  "name": "ByBit Signal", 
  "symbol": "{{ticker}}",
  "order_type": "Market",
  "side": "{{strategy.order.action}}",
  "price": {{close}},
  "qty": "calculated_auto"
}`}
                          </Code>
                        </TabPanel>
                        
                        <TabPanel>
                          <Code p={4} borderRadius="md" w="full" whiteSpace="pre-wrap">
{`{
  "name": "OKX Signal",
  "instId": "{{ticker}}",
  "ordType": "market", 
  "side": "{{strategy.order.action}}",
  "px": {{close}},
  "sz": "calculated_auto"
}`}
                </Code>
                        </TabPanel>
                        
                        <TabPanel>
                          <Code p={4} borderRadius="md" w="full" whiteSpace="pre-wrap">
{`{
  "name": "Custom Signal",
  "symbol": "{{ticker}}",
  "action": "{{strategy.order.action}}",
  "price": {{close}},
  "amount": "calculated_auto",
  "timestamp": "{{time}}",
  "custom_field": "your_value"
}`}
                          </Code>
                        </TabPanel>
                      </TabPanels>
                    </Tabs>
                  </Box>
                </VStack>
              </TabPanel>

              {/* İzleme ve Analiz Tab */}
              <TabPanel>
                <VStack spacing={8} align="stretch">
                  <Heading size="lg" color={accentColor}>
                    <Icon as={MdTrendingUp} mr={3} />
                    İzleme ve Performans Analizi
                  </Heading>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
                    <Box bg={cardBg} p={6} borderRadius="xl">
                      <Heading size="md" mb={4}>📊 İşlem Takibi</Heading>
                      <VStack spacing={4} align="stretch">
                        <Text color={textColor}>
                          Sistem otomatik olarak tüm işlemlerinizi kaydeder ve analiz eder:
                        </Text>
                        <List spacing={2}>
                          <ListItem>
                            <ListIcon as={MdCheckCircle} color="green.500" />
                            Gerçek zamanlı P&amp;L hesaplama
                          </ListItem>
                          <ListItem>
                            <ListIcon as={MdCheckCircle} color="green.500" />
                            Açık pozisyon takibi
                          </ListItem>
                          <ListItem>
                            <ListIcon as={MdCheckCircle} color="green.500" />
                            Performans metrikleri
                          </ListItem>
                          <ListItem>
                            <ListIcon as={MdCheckCircle} color="green.500" />
                            İşlem geçmişi
                          </ListItem>
                        </List>
                      </VStack>
                    </Box>

                    <Box bg={cardBg} p={6} borderRadius="xl">
                      <Heading size="md" mb={4}>📈 Raporlama</Heading>
                      <VStack spacing={4} align="stretch">
                        <Text color={textColor}>
                          Detaylı raporlar ve analizler:
                        </Text>
                        <List spacing={2}>
                          <ListItem>
                            <ListIcon as={MdCheckCircle} color="green.500" />
                            Günlük/haftalık/aylık özet
                          </ListItem>
                          <ListItem>
                            <ListIcon as={MdCheckCircle} color="green.500" />
                            Başarı oranı analizi
                          </ListItem>
                    <ListItem>
                        <ListIcon as={MdCheckCircle} color="green.500" />
                            Risk/getiri oranları
                    </ListItem>
                    <ListItem>
                        <ListIcon as={MdCheckCircle} color="green.500" />
                            Sembol bazlı performans
                    </ListItem>
                </List>
            </VStack>
                    </Box>
                  </SimpleGrid>

                  <Box bg={cardBg} p={8} borderRadius="xl">
                    <Heading size="md" mb={6}>
                      <Icon as={MdIntegrationInstructions} mr={2} />
                      Optimizasyon İpuçları
                    </Heading>
                    
                    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                      <Box>
                        <Text fontWeight="bold" color="green.600" mb={2}>✅ En İyi Uygulamalar</Text>
                        <List spacing={2} fontSize="sm">
                          <ListItem>
                            <ListIcon as={MdCheckCircle} color="green.500" />
                            Küçük pozisyonlarla başlayın
                          </ListItem>
                          <ListItem>
                            <ListIcon as={MdCheckCircle} color="green.500" />
                            Stop-loss kullanın
                          </ListItem>
                          <ListItem>
                            <ListIcon as={MdCheckCircle} color="green.500" />
                            Backtesting yapın
                          </ListItem>
                          <ListItem>
                            <ListIcon as={MdCheckCircle} color="green.500" />
                            Düzenli olarak performansı gözden geçirin
                          </ListItem>
                        </List>
                      </Box>
                      
                      <Box>
                        <Text fontWeight="bold" color="red.600" mb={2}>❌ Kaçınılması Gerekenler</Text>
                        <List spacing={2} fontSize="sm">
                          <ListItem>
                            <ListIcon as={MdWarning} color="red.500" />
                            Tüm sermayeyi tek sinyale yatırma
                          </ListItem>
                          <ListItem>
                            <ListIcon as={MdWarning} color="red.500" />
                            Test etmeden canlı başlama
                          </ListItem>
                          <ListItem>
                            <ListIcon as={MdWarning} color="red.500" />
                            API anahtarlarını paylaşma
                          </ListItem>
                          <ListItem>
                            <ListIcon as={MdWarning} color="red.500" />
                            Duygusal müdahaleler
                          </ListItem>
                        </List>
                      </Box>
                    </SimpleGrid>
                  </Box>

                  <Alert status="success" borderRadius="lg">
                    <AlertIcon />
                    <Box>
                      <AlertTitle>🎯 Başarı İçin Altın Kurallar</AlertTitle>
                      <AlertDescription>
                        <strong>1. Sabır:</strong> Sisteminizin çalışması için zaman tanıyın<br/>
                        <strong>2. Disiplin:</strong> Stratejinize sadık kalın<br/>
                        <strong>3. Öğrenme:</strong> Her işlemden ders çıkarın<br/>
                        <strong>4. Risk Yönetimi:</strong> Asla kaybetmeyi göze alamadığınız miktarı riske atmayın
                      </AlertDescription>
                    </Box>
                  </Alert>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
          </Box>

          {/* Bottom CTA */}
          <Box bg={useColorModeValue('brand.50', 'brand.900')} p={8} borderRadius="xl" textAlign="center">
            <VStack spacing={4}>
              <Heading size="lg" color="brand.600">
                🚀 Solo-Robot'unuzu Kurmaya Hazır mısınız?
              </Heading>
              <Text color={textColor}>
                Yönetim paneline giderek API anahtarlarınızı ayarlayın ve otomatik ticaret yolculuğunuza başlayın.
              </Text>
              <Button 
                colorScheme="brand" 
                size="lg" 
                leftIcon={<Icon as={FaRocket} />}
              >
                Yönetim Paneline Git
              </Button>
            </VStack>
          </Box>
        </VStack>
      </Container>
        </Box>
    );
};

export default SoloRobotGuide; 