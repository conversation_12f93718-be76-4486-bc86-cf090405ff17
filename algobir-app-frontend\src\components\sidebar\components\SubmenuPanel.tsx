import React, { useEffect, useRef } from 'react';
import {
  Box,
  Flex,
  VStack,
  Text,
  HStack,
  useColorModeValue,
  IconButton,
  Slide
} from '@chakra-ui/react';
import { NavLink } from 'react-router-dom';
import { FiX } from 'react-icons/fi';
import { Scrollbars } from 'react-custom-scrollbars-2';
import { useSidebar, SubmenuItem } from '../../../context/SidebarContext';
import { TRANSITIONS } from '../animations';

interface SubmenuPanelProps {
  isOpen: boolean;
  items: SubmenuItem[];
  title?: string;
}

// Custom scrollbar components for submenu
const renderSubmenuTrack = ({ ...props }) => {
  const trackStyle = {
    position: 'absolute',
    maxWidth: '100%',
    width: 6,
    transition: 'opacity 200ms ease 0s',
    opacity: 1,
    background: 'rgba(0, 0, 0, 0.02)',
    bottom: 2,
    top: 2,
    borderRadius: 3,
    right: 2
  } as const;
  return <div style={trackStyle} {...props} />;
};

const renderSubmenuThumb = ({ ...props }) => {
  const thumbStyle = {
    borderRadius: 15,
    background: 'rgba(112, 144, 176, 0.3)',
    transition: 'all 0.2s ease',
    cursor: 'pointer',
    '&:hover': {
      background: 'rgba(112, 144, 176, 0.5)'
    }
  } as const;
  return <div style={thumbStyle} {...props} />;
};

const renderSubmenuView = ({ style, ...props }: any) => {
  const viewStyle = {
    ...style,
    marginRight: -17, // Space for scrollbar
    paddingRight: 17,
    // Ensure smooth scrolling
    scrollBehavior: 'smooth'
  } as const;
  return (
    <div style={viewStyle} {...props} />
  );
};

const SubmenuPanel: React.FC<SubmenuPanelProps> = ({ isOpen, items, title }) => {
  const { closeSubmenu, currentSidebarWidth, setSidebarHovered } = useSidebar();
  const panelRef = useRef<HTMLDivElement>(null);
  const firstItemRef = useRef<HTMLAnchorElement>(null);
  const scrollbarsRef = useRef<Scrollbars>(null);

  // Color mode values
  const panelBg = useColorModeValue('white', 'navy.700');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');
  const textColor = useColorModeValue('gray.700', 'white');
  const secondaryTextColor = useColorModeValue('gray.500', 'gray.400');
  const hoverBg = useColorModeValue('gray.50', 'whiteAlpha.100');
  const shadow = useColorModeValue(
    '14px 17px 40px 4px rgba(112, 144, 176, 0.08)', 
    '14px 17px 40px 4px rgba(12, 44, 55, 0.18)'
  );

  // Focus management for accessibility
  useEffect(() => {
    if (isOpen && firstItemRef.current) {
      // Focus first item when submenu opens
      setTimeout(() => {
        firstItemRef.current?.focus();
      }, 150); // Wait for animation to complete
    }
  }, [isOpen]);

  // Enhanced keyboard navigation with scrolling support
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        event.preventDefault();
        closeSubmenu();
      }

      // Handle arrow key navigation with auto-scrolling
      if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
        const focusedElement = document.activeElement as HTMLElement;
        const menuItems = panelRef.current?.querySelectorAll('[role="menuitem"]');

        if (menuItems && focusedElement) {
          const currentIndex = Array.from(menuItems).indexOf(focusedElement);
          let nextIndex = -1;

          if (event.key === 'ArrowDown') {
            nextIndex = currentIndex < menuItems.length - 1 ? currentIndex + 1 : 0;
          } else {
            nextIndex = currentIndex > 0 ? currentIndex - 1 : menuItems.length - 1;
          }

          const nextElement = menuItems[nextIndex] as HTMLElement;
          if (nextElement) {
            event.preventDefault();
            nextElement.focus();

            // Auto-scroll to focused element
            if (scrollbarsRef.current) {
              const scrollContainer = scrollbarsRef.current.getScrollTop();
              const containerHeight = scrollbarsRef.current.getClientHeight();
              const elementTop = nextElement.offsetTop;
              const elementHeight = nextElement.offsetHeight;

              // Check if element is outside visible area
              if (elementTop < scrollContainer) {
                // Scroll up to show element
                scrollbarsRef.current.scrollTop(elementTop - 20);
              } else if (elementTop + elementHeight > scrollContainer + containerHeight) {
                // Scroll down to show element
                scrollbarsRef.current.scrollTop(elementTop + elementHeight - containerHeight + 20);
              }
            }
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, closeSubmenu]);

  if (!isOpen) return null;

  return (
    <Slide direction="right" in={isOpen} style={{ zIndex: 1050 }}>
      <Box
        ref={panelRef}
        position="fixed"
        left={currentSidebarWidth} // Dynamic positioning based on sidebar width
        top="0"
        h="100vh"
        w="260px" // Slightly smaller for better proportions
        bg={panelBg}
        borderRight="1px solid"
        borderColor={borderColor}
        boxShadow={shadow}
        zIndex={1050} // Higher than sidebar but lower than navbar
        role="dialog"
        aria-modal="true"
        aria-label={`${title || 'Menü'} alt menüsü`}
        aria-describedby="submenu-description"
        onMouseEnter={() => setSidebarHovered(true)}
        onMouseLeave={() => setSidebarHovered(false)}
        sx={{
          // Ensure proper layering
          isolation: 'isolate',
        }}
      >
        <Flex direction="column" h="100%">
          {/* Fixed Header with close button */}
          <Flex
            alignItems="center"
            justifyContent="space-between"
            pt="25px"
            px="20px"
            pb="20px"
            borderBottom="1px solid"
            borderColor={borderColor}
            flexShrink={0} // Prevent header from shrinking
          >
            <Text
              fontSize="lg"
              fontWeight="bold"
              color={textColor}
              noOfLines={1}
            >
              {title || 'Menü'}
            </Text>
            <IconButton
              aria-label="Alt menüyü kapat (Escape tuşu)"
              icon={<FiX />}
              variant="ghost"
              size="sm"
              color={textColor}
              _hover={{ bg: hoverBg }}
              onClick={closeSubmenu}
              _focus={{
                boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
                outline: 'none',
              }}
            />
          </Flex>

          {/* Scrollable content area */}
          <Box flex="1" overflow="hidden">
            <Scrollbars
              ref={scrollbarsRef}
              autoHide={true}
              hideTracksWhenNotNeeded={true}
              renderTrackVertical={renderSubmenuTrack}
              renderThumbVertical={renderSubmenuThumb}
              renderView={renderSubmenuView}
              style={{
                height: '100%',
                overflow: 'hidden'
              }}
              // Smooth scrolling configuration
              autoHideTimeout={1000}
              autoHideDuration={200}
              thumbMinSize={30}
              universal={true}
            >
              {/* Submenu items container */}
              <VStack
                spacing={2}
                align="stretch"
                px="20px"
                py="10px"
                pb="20px"
                id="submenu-description"
              >
                {items.map((item, index) => (
                  <NavLink
                    key={item.id}
                    to={item.path}
                    ref={index === 0 ? firstItemRef : undefined}
                  >
                    <Box
                      p="16px"
                      borderRadius="12px"
                      _hover={{
                        bg: hoverBg,
                        transform: 'translateX(4px)',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                      }}
                      _focus={{
                        bg: hoverBg,
                        boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
                        outline: 'none',
                      }}
                      transition={TRANSITIONS.hover}
                      sx={{
                        '@media (prefers-reduced-motion: reduce)': {
                          transition: 'none'
                        }
                      }}
                      cursor="pointer"
                      onClick={closeSubmenu}
                      role="menuitem"
                      tabIndex={0}
                    >
                      <HStack spacing={3} align="flex-start">
                        {item.icon && (
                          <Box
                            color={textColor}
                            mt="2px"
                            flexShrink={0}
                          >
                            {item.icon}
                          </Box>
                        )}
                        <VStack align="flex-start" spacing={1} flex={1}>
                          <Text
                            fontSize="md"
                            fontWeight="semibold"
                            color={textColor}
                            lineHeight="1.2"
                          >
                            {item.name}
                          </Text>
                          {item.description && (
                            <Text
                              fontSize="sm"
                              color={secondaryTextColor}
                              lineHeight="1.3"
                            >
                              {item.description}
                            </Text>
                          )}
                        </VStack>
                      </HStack>
                    </Box>
                  </NavLink>
                ))}
              </VStack>
            </Scrollbars>
          </Box>
        </Flex>
      </Box>
    </Slide>
  );
};

export default SubmenuPanel;
