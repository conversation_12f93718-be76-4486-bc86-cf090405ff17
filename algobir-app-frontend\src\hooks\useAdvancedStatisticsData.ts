import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabaseClient';
import { useAuth } from '../context/AuthContext';
import { Trade } from '../types/trade';

export interface StatsData {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  totalWins: number;
  totalLosses: number;
  grossProfit: number;
  grossLoss: number;
  winRate: number;
  totalPnl: number;
  profitFactor: number;
  avgTradePnl: number;
  avgWinningTrade: number;
  avgLosingTrade: number;
  maxDrawdown: number;
  maxDrawdownPercent: number;
  sharpeRatio: number;
  sortinoRatio: number;
  calmarRatio: number;
  maxConsecutiveWins: number;
  maxConsecutiveLosses: number;
  averageWinStreak: number;
  averageLossStreak: number;
  largestWin: number;
  largestLoss: number;
  pnlBySymbol: { [key: string]: { pnl: number; trades: number; winRate: number } };
  pnlByMonth: { date: string; pnl: number; trades: number }[];
  cumulativePnlData: { date: string; value: number; trades: number }[];
  hourlyPerformance: { hour: number; pnl: number; trades: number }[];
  dailyPerformance: { day: string; pnl: number; trades: number }[];
  weeklyPerformance: { week: string; pnl: number; trades: number }[];
  monthlyPerformance: { month: string; pnl: number; trades: number }[];
  riskMetrics: {
    valueAtRisk: number;
    expectedShortfall: number;
    maxDrawdownDuration: number;
    recoveryFactor: number;
    ulcerIndex: number;
  };
  performanceByRobot: { robotId: string; robotName: string; pnl: number; trades: number; winRate: number }[];
}

export interface FilterOptions {
  robotType: 'all' | 'solo' | 'bro';
  robotId?: string;
  dateRange: 'all' | '7d' | '30d' | '90d' | '1y';
  symbol?: string;
}

export const useAdvancedStatisticsData = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({
    robotType: 'all',
    dateRange: 'all'
  });

  const calculateAdvancedStats = useCallback((trades: Trade[]) => {
    if (trades.length === 0) {
      return {
        totalTrades: 0, winningTrades: 0, losingTrades: 0, totalWins: 0, totalLosses: 0,
        grossProfit: 0, grossLoss: 0, winRate: 0, totalPnl: 0, profitFactor: 0, 
        avgTradePnl: 0, avgWinningTrade: 0, avgLosingTrade: 0, maxDrawdown: 0, 
        maxDrawdownPercent: 0, sharpeRatio: 0, sortinoRatio: 0, calmarRatio: 0, 
        maxConsecutiveWins: 0, maxConsecutiveLosses: 0, averageWinStreak: 0, 
        averageLossStreak: 0, largestWin: 0, largestLoss: 0, pnlBySymbol: {}, 
        pnlByMonth: [], cumulativePnlData: [], hourlyPerformance: [], dailyPerformance: [], 
        weeklyPerformance: [], monthlyPerformance: [], 
        riskMetrics: { valueAtRisk: 0, expectedShortfall: 0, maxDrawdownDuration: 0, recoveryFactor: 0, ulcerIndex: 0 },
        performanceByRobot: []
      };
    }

    const closedTrades = trades
      .filter(t => t.position_status === 'Kapalı' && t.pnl !== null)
      .sort((a, b) => new Date(a.pnl_calculated_at!).getTime() - new Date(b.pnl_calculated_at!).getTime());

    const totalTrades = closedTrades.length;
    const winningTrades = closedTrades.filter(t => t.pnl! > 0);
    const losingTrades = closedTrades.filter(t => t.pnl! <= 0);
    const winRate = totalTrades > 0 ? (winningTrades.length / totalTrades) * 100 : 0;

    const totalPnl = closedTrades.reduce((acc, t) => acc + t.pnl!, 0);
    const totalWinPnl = winningTrades.reduce((acc, t) => acc + t.pnl!, 0);
    const totalLossPnl = Math.abs(losingTrades.reduce((acc, t) => acc + t.pnl!, 0));
    const profitFactor = totalLossPnl > 0 ? totalWinPnl / totalLossPnl : totalWinPnl > 0 ? 999 : 0;

    const avgTradePnl = totalPnl / totalTrades;
    const avgWinningTrade = winningTrades.length > 0 ? totalWinPnl / winningTrades.length : 0;
    const avgLosingTrade = losingTrades.length > 0 ? totalLossPnl / losingTrades.length : 0;

    // Drawdown calculation
    let peak = 0;
    let maxDrawdown = 0;
    let maxDrawdownPercent = 0;
    let cumulativePnl = 0;
    const cumulativePnlData = [];

    for (const trade of closedTrades) {
      cumulativePnl += trade.pnl!;
      if (cumulativePnl > peak) peak = cumulativePnl;
      const drawdown = peak - cumulativePnl;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
        maxDrawdownPercent = peak > 0 ? (drawdown / peak) * 100 : 0;
      }
      cumulativePnlData.push({
        date: trade.pnl_calculated_at!,
        value: cumulativePnl,
        trades: cumulativePnlData.length + 1
      });
    }

    // Consecutive wins/losses
    let currentWinStreak = 0, currentLossStreak = 0;
    let maxConsecutiveWins = 0, maxConsecutiveLosses = 0;
    let winStreaks = [], lossStreaks = [];

    for (const trade of closedTrades) {
      if (trade.pnl! > 0) {
        currentWinStreak++;
        if (currentLossStreak > 0) {
          lossStreaks.push(currentLossStreak);
          currentLossStreak = 0;
        }
      } else {
        currentLossStreak++;
        if (currentWinStreak > 0) {
          winStreaks.push(currentWinStreak);
          currentWinStreak = 0;
        }
      }
      maxConsecutiveWins = Math.max(maxConsecutiveWins, currentWinStreak);
      maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLossStreak);
    }

    const averageWinStreak = winStreaks.length > 0 ? winStreaks.reduce((a, b) => a + b, 0) / winStreaks.length : 0;
    const averageLossStreak = lossStreaks.length > 0 ? lossStreaks.reduce((a, b) => a + b, 0) / lossStreaks.length : 0;

    // Largest win/loss
    const largestWin = Math.max(...closedTrades.map(t => t.pnl!));
    const largestLoss = Math.min(...closedTrades.map(t => t.pnl!));

    // Sharpe ratio (simplified - assuming risk-free rate = 0)
    const returns = closedTrades.map(t => t.pnl!);
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    const stdDev = Math.sqrt(returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length);
    const sharpeRatio = stdDev > 0 ? avgReturn / stdDev : 0;

    // Sortino ratio (downside deviation)
    const negativeReturns = returns.filter(r => r < 0);
    const downsideDeviation = negativeReturns.length > 0 ? 
      Math.sqrt(negativeReturns.reduce((sum, ret) => sum + Math.pow(ret, 2), 0) / negativeReturns.length) : 0;
    const sortinoRatio = downsideDeviation > 0 ? avgReturn / downsideDeviation : 0;

    // Calmar ratio
    const calmarRatio = maxDrawdown > 0 ? (totalPnl / maxDrawdown) : 0;

    // Symbol breakdown
    const pnlBySymbol: { [key: string]: { pnl: number; trades: number; winRate: number } } = {};
    closedTrades.forEach(trade => {
      if (!pnlBySymbol[trade.symbol]) {
        pnlBySymbol[trade.symbol] = { pnl: 0, trades: 0, winRate: 0 };
      }
      pnlBySymbol[trade.symbol].pnl += trade.pnl!;
      pnlBySymbol[trade.symbol].trades += 1;
    });

    Object.keys(pnlBySymbol).forEach(symbol => {
      const symbolTrades = closedTrades.filter(t => t.symbol === symbol);
      const symbolWins = symbolTrades.filter(t => t.pnl! > 0).length;
      pnlBySymbol[symbol].winRate = (symbolWins / symbolTrades.length) * 100;
    });

    // Time-based performance
    const hourlyPerformance = Array.from({ length: 24 }, (_, hour) => ({
      hour,
      pnl: 0,
      trades: 0
    }));

    const dailyPerformance: { [key: string]: { pnl: number; trades: number } } = {};
    const weeklyPerformance: { [key: string]: { pnl: number; trades: number } } = {};
    const monthlyPerformance: { [key: string]: { pnl: number; trades: number } } = {};

    closedTrades.forEach(trade => {
      const date = new Date(trade.pnl_calculated_at!);
      const hour = date.getHours();
      const dayKey = date.toISOString().split('T')[0];
      const weekKey = `${date.getFullYear()}-W${Math.ceil(date.getDate() / 7)}`;
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      hourlyPerformance[hour].pnl += trade.pnl!;
      hourlyPerformance[hour].trades += 1;

      if (!dailyPerformance[dayKey]) dailyPerformance[dayKey] = { pnl: 0, trades: 0 };
      dailyPerformance[dayKey].pnl += trade.pnl!;
      dailyPerformance[dayKey].trades += 1;

      if (!weeklyPerformance[weekKey]) weeklyPerformance[weekKey] = { pnl: 0, trades: 0 };
      weeklyPerformance[weekKey].pnl += trade.pnl!;
      weeklyPerformance[weekKey].trades += 1;

      if (!monthlyPerformance[monthKey]) monthlyPerformance[monthKey] = { pnl: 0, trades: 0 };
      monthlyPerformance[monthKey].pnl += trade.pnl!;
      monthlyPerformance[monthKey].trades += 1;
    });

    // Risk metrics
    const returns5thPercentile = returns.sort((a, b) => a - b)[Math.floor(returns.length * 0.05)];
    const valueAtRisk = Math.abs(returns5thPercentile || 0);
    const expectedShortfall = Math.abs(returns.filter(r => r <= returns5thPercentile).reduce((a, b) => a + b, 0) / returns.filter(r => r <= returns5thPercentile).length || 0);

    // Performance by robot
    const performanceByRobot: { [key: string]: { robotName: string; pnl: number; trades: number; winRate: number } } = {};
    closedTrades.forEach(trade => {
      const robotId = trade.robot_id || 'solo';
      const robotName = trade.robot_id ? 'Bro-Robot' : 'Solo-Robot';
      
      if (!performanceByRobot[robotId]) {
        performanceByRobot[robotId] = { robotName, pnl: 0, trades: 0, winRate: 0 };
      }
      performanceByRobot[robotId].pnl += trade.pnl!;
      performanceByRobot[robotId].trades += 1;
    });

    Object.keys(performanceByRobot).forEach(robotId => {
      const robotTrades = closedTrades.filter(t => (t.robot_id || 'solo') === robotId);
      const robotWins = robotTrades.filter(t => t.pnl! > 0).length;
      performanceByRobot[robotId].winRate = (robotWins / robotTrades.length) * 100;
    });

    return {
      totalTrades,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      totalWins: winningTrades.length,
      totalLosses: losingTrades.length,
      grossProfit: totalWinPnl,
      grossLoss: totalLossPnl,
      winRate,
      totalPnl,
      profitFactor,
      avgTradePnl,
      avgWinningTrade,
      avgLosingTrade,
      maxDrawdown,
      maxDrawdownPercent,
      sharpeRatio,
      sortinoRatio,
      calmarRatio,
      maxConsecutiveWins,
      maxConsecutiveLosses,
      averageWinStreak,
      averageLossStreak,
      largestWin,
      largestLoss,
      pnlBySymbol,
      pnlByMonth: Object.entries(monthlyPerformance).map(([month, data]) => ({
        date: month,
        pnl: data.pnl,
        trades: data.trades
      })),
      cumulativePnlData,
      hourlyPerformance,
      dailyPerformance: Object.entries(dailyPerformance).map(([day, data]) => ({
        day,
        pnl: data.pnl,
        trades: data.trades
      })),
      weeklyPerformance: Object.entries(weeklyPerformance).map(([week, data]) => ({
        week,
        pnl: data.pnl,
        trades: data.trades
      })),
      monthlyPerformance: Object.entries(monthlyPerformance).map(([month, data]) => ({
        month,
        pnl: data.pnl,
        trades: data.trades
      })),
      riskMetrics: {
        valueAtRisk,
        expectedShortfall,
        maxDrawdownDuration: 0, // Placeholder
        recoveryFactor: maxDrawdown > 0 ? totalPnl / maxDrawdown : 0,
        ulcerIndex: 0 // Placeholder
      },
      performanceByRobot: Object.entries(performanceByRobot).map(([robotId, data]) => ({
        robotId,
        robotName: data.robotName,
        pnl: data.pnl,
        trades: data.trades,
        winRate: data.winRate
      }))
    };
  }, []);

  const fetchData = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      let query = supabase
        .from('trades')
        .select('*')
        .eq('user_id', user.id);

      // Apply filters
      if (filters.robotType === 'solo') {
        query = query.is('robot_id', null);
      } else if (filters.robotType === 'bro') {
        query = query.not('robot_id', 'is', null);
      }

      if (filters.robotId) {
        query = query.eq('robot_id', filters.robotId);
      }

      if (filters.symbol) {
        query = query.eq('symbol', filters.symbol);
      }

      if (filters.dateRange !== 'all') {
        const days = filters.dateRange === '7d' ? 7 : 
                    filters.dateRange === '30d' ? 30 : 
                    filters.dateRange === '90d' ? 90 : 365;
        const fromDate = new Date();
        fromDate.setDate(fromDate.getDate() - days);
        query = query.gte('pnl_calculated_at', fromDate.toISOString());
      }

      const { data: trades, error } = await query;

      if (error) {
        setError(error.message);
        return;
      }

      const calculatedStats = calculateAdvancedStats(trades || []);
      setStats(calculatedStats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Bilinmeyen hata');
    } finally {
      setLoading(false);
    }
  }, [user, filters, calculateAdvancedStats]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const updateFilters = useCallback((newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  return {
    stats,
    loading,
    error,
    filters,
    updateFilters,
    refetch: fetchData
  };
}; 