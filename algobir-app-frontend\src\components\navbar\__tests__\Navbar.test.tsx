/**
 * Navbar Component Unit Tests
 * Phase 3.1: Automated Testing Pipeline Setup
 * Comprehensive testing for navigation component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, createMockUser } from '../../../test/utils/test-utils';
import { Navbar } from '../Navbar';

// Mock the useAuth hook
const mockUseAuth = vi.fn();
vi.mock('../../../hooks/useAuth', () => ({
  useAuth: () => mockUseAuth()
}));

// Mock the useNavigate hook
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ pathname: '/' })
  };
});

describe('Navbar Component', () => {
  const mockUser = createMockUser();
  
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseAuth.mockReturnValue({
      user: mockUser,
      session: { access_token: 'mock-token' },
      loading: false,
      signOut: vi.fn().mockResolvedValue({ error: null })
    });
  });

  describe('Rendering', () => {
    it('should render navbar with user information when authenticated', () => {
      renderWithProviders(<Navbar />);
      
      expect(screen.getByText('Test User')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('should render login button when not authenticated', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        session: null,
        loading: false,
        signOut: vi.fn()
      });

      renderWithProviders(<Navbar />);
      
      expect(screen.getByText('Giriş Yap')).toBeInTheDocument();
      expect(screen.queryByText('Test User')).not.toBeInTheDocument();
    });

    it('should show loading state when authentication is loading', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        session: null,
        loading: true,
        signOut: vi.fn()
      });

      renderWithProviders(<Navbar />);
      
      // Should show skeleton or loading indicator
      expect(screen.getByTestId('navbar-loading')).toBeInTheDocument();
    });
  });

  describe('User Menu Interactions', () => {
    it('should open user menu when avatar is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Navbar />);
      
      const avatar = screen.getByRole('button', { name: /kullanıcı menüsü/i });
      await user.click(avatar);
      
      expect(screen.getByText('Profil')).toBeInTheDocument();
      expect(screen.getByText('Ayarlar')).toBeInTheDocument();
      expect(screen.getByText('Çıkış Yap')).toBeInTheDocument();
    });

    it('should close user menu when clicking outside', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Navbar />);
      
      // Open menu
      const avatar = screen.getByRole('button', { name: /kullanıcı menüsü/i });
      await user.click(avatar);
      
      expect(screen.getByText('Profil')).toBeInTheDocument();
      
      // Click outside
      await user.click(document.body);
      
      await waitFor(() => {
        expect(screen.queryByText('Profil')).not.toBeInTheDocument();
      });
    });

    it('should navigate to profile when profile menu item is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Navbar />);
      
      // Open menu
      const avatar = screen.getByRole('button', { name: /kullanıcı menüsü/i });
      await user.click(avatar);
      
      // Click profile
      const profileItem = screen.getByText('Profil');
      await user.click(profileItem);
      
      expect(mockNavigate).toHaveBeenCalledWith('/profile');
    });
  });

  describe('Sign Out Functionality', () => {
    it('should call signOut when logout is clicked', async () => {
      const mockSignOut = vi.fn().mockResolvedValue({ error: null });
      mockUseAuth.mockReturnValue({
        user: mockUser,
        session: { access_token: 'mock-token' },
        loading: false,
        signOut: mockSignOut
      });

      const user = userEvent.setup();
      renderWithProviders(<Navbar />);
      
      // Open menu
      const avatar = screen.getByRole('button', { name: /kullanıcı menüsü/i });
      await user.click(avatar);
      
      // Click logout
      const logoutItem = screen.getByText('Çıkış Yap');
      await user.click(logoutItem);
      
      expect(mockSignOut).toHaveBeenCalled();
    });

    it('should show error toast when signOut fails', async () => {
      const mockSignOut = vi.fn().mockResolvedValue({ 
        error: { message: 'Sign out failed' } 
      });
      mockUseAuth.mockReturnValue({
        user: mockUser,
        session: { access_token: 'mock-token' },
        loading: false,
        signOut: mockSignOut
      });

      const user = userEvent.setup();
      renderWithProviders(<Navbar />);
      
      // Open menu and click logout
      const avatar = screen.getByRole('button', { name: /kullanıcı menüsü/i });
      await user.click(avatar);
      
      const logoutItem = screen.getByText('Çıkış Yap');
      await user.click(logoutItem);
      
      await waitFor(() => {
        expect(screen.getByText(/çıkış yapılırken hata/i)).toBeInTheDocument();
      });
    });
  });

  describe('Responsive Behavior', () => {
    it('should show mobile menu button on small screens', () => {
      // Mock mobile breakpoint
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation(query => ({
          matches: query.includes('(max-width: 768px)'),
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      });

      renderWithProviders(<Navbar />);
      
      expect(screen.getByRole('button', { name: /menüyü aç/i })).toBeInTheDocument();
    });

    it('should hide mobile menu button on large screens', () => {
      // Mock desktop breakpoint
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation(query => ({
          matches: query.includes('(min-width: 992px)'),
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      });

      renderWithProviders(<Navbar />);
      
      expect(screen.queryByRole('button', { name: /menüyü aç/i })).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      renderWithProviders(<Navbar />);
      
      const navbar = screen.getByRole('navigation');
      expect(navbar).toHaveAttribute('aria-label', 'Ana navigasyon');
      
      const avatar = screen.getByRole('button', { name: /kullanıcı menüsü/i });
      expect(avatar).toHaveAttribute('aria-haspopup', 'true');
      expect(avatar).toHaveAttribute('aria-expanded', 'false');
    });

    it('should update aria-expanded when menu is opened', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Navbar />);
      
      const avatar = screen.getByRole('button', { name: /kullanıcı menüsü/i });
      expect(avatar).toHaveAttribute('aria-expanded', 'false');
      
      await user.click(avatar);
      
      expect(avatar).toHaveAttribute('aria-expanded', 'true');
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Navbar />);
      
      const avatar = screen.getByRole('button', { name: /kullanıcı menüsü/i });
      
      // Focus and press Enter
      avatar.focus();
      await user.keyboard('{Enter}');
      
      expect(screen.getByText('Profil')).toBeInTheDocument();
      
      // Press Escape to close
      await user.keyboard('{Escape}');
      
      await waitFor(() => {
        expect(screen.queryByText('Profil')).not.toBeInTheDocument();
      });
    });
  });

  describe('Performance', () => {
    it('should not re-render unnecessarily', () => {
      const renderSpy = vi.fn();
      
      const TestNavbar = () => {
        renderSpy();
        return <Navbar />;
      };

      const { rerender } = renderWithProviders(<TestNavbar />);
      
      expect(renderSpy).toHaveBeenCalledTimes(1);
      
      // Re-render with same props
      rerender(<TestNavbar />);
      
      // Should not re-render if props haven't changed
      expect(renderSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing user data gracefully', () => {
      mockUseAuth.mockReturnValue({
        user: null,
        session: { access_token: 'mock-token' },
        loading: false,
        signOut: vi.fn()
      });

      expect(() => renderWithProviders(<Navbar />)).not.toThrow();
      expect(screen.getByText('Giriş Yap')).toBeInTheDocument();
    });

    it('should handle auth hook errors gracefully', () => {
      mockUseAuth.mockImplementation(() => {
        throw new Error('Auth hook error');
      });

      // Should be caught by error boundary
      expect(() => renderWithProviders(<Navbar />)).not.toThrow();
    });
  });
});
