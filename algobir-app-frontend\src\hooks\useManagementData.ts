import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabaseClient';
import { useAuth } from '../context/AuthContext';
import { useToastHelper } from '../components/ToastHelper';

// Kullanıcı ayarları tipi
export interface UserSettings {
  id: string;
  webhook_id: string;
  is_active: boolean;
  investment_amount: number;
  total_investment_amount: number | null;
  custom_webhook_url: string;
  api_key_set: boolean | null;
  token_set: boolean | null;
  api_credentials_expiry_date?: string | null;
  created_at?: string;
  updated_at?: string;
}

// Abonelik detayları tipi
export interface SubscriptionWithDetails {
  subscription_id: string;
  robot_id: string;
  started_at: string;
  ends_at?: string | null;
  is_active: boolean;
  is_deleted?: boolean;
  robot_name: string;
  robot_description?: string;
  robot_image_url?: string | null;
  robot_strategy_type?: string;
}

// Kimlik bilgileri payload tipi
export interface CredentialsPayload {
  apiKey?: string | null;
  token?: string | null;
}

interface UseManagementDataReturn {
  settings: UserSettings | null;
  subscriptions: SubscriptionWithDetails[] | null;
  isLoading: boolean;
  isUpdating: boolean;
  isDeleting: Record<string, boolean>;
  error: any | null;
  updateSettings: (newSettings: Partial<UserSettings>) => Promise<boolean>;
  deleteSubscription: (subscriptionId: string) => Promise<boolean>;
  toggleSubscriptionStatus: (subscriptionId: string, isActive: boolean) => Promise<boolean>;
  refetch: () => Promise<void>;
  saveCredentials: (payload: CredentialsPayload) => Promise<boolean>;
}

export function useManagementData(): UseManagementDataReturn {
  const { user } = useAuth();
  const toast = useToastHelper();
  
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithDetails[] | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<Record<string, boolean>>({});
  const [error, setError] = useState<any | null>(null);

  // Tüm verileri getiren fonksiyon
  const fetchData = useCallback(async (showLoading = true) => {
    if (!user) {
      console.log('[useManagementData] fetchData called but user is null');
      if (showLoading) setIsLoading(false);
      setSettings(null);
      setSubscriptions([]);
      return;
    }
    
    if (showLoading) setIsLoading(true);
    setError(null);
    
    try {
      console.log('[useManagementData] Fetching data for user:', user.id);
      
      // Kullanıcı ayarlarını ve abonelikleri paralel olarak getir
      try {
        // Kullanıcı ayarlarını getir
        const { data: settingsData, error: settingsError } = await supabase
          .from('user_settings')
          .select('id, webhook_id, is_active, investment_amount, total_investment_amount, custom_webhook_url, api_key_set, token_set, api_credentials_expiry_date')
          .eq('id', user.id)
          .single();

        // Kullanıcı ayarları cevabını işle
        if (settingsError) {
          console.error('[useManagementData] Error fetching user settings:', {
            message: settingsError.message,
            details: settingsError.details,
            hint: settingsError.hint,
            code: settingsError.code
          });
          
          // 406 Not Acceptable hatası özellikle ele alınıyor
          if (settingsError.code === '406') {
            console.warn('[useManagementData] 406 Not Acceptable error, likely RLS policy issue. Retrying with basic fields.');
            // Sadece temel alanları almayı deneyelim
            const { data: basicSettings, error: basicError } = await supabase
              .from('user_settings')
              .select('id, is_active')
              .eq('id', user.id)
              .single();
              
            if (basicError) {
              console.error('[useManagementData] Still error after retry with basic fields:', basicError);
              if (showLoading) setIsLoading(false);
              toast.showErrorToast('Hata', 'Kullanıcı ayarları yüklenemedi. Lütfen sayfayı yenileyip tekrar deneyin.');
            } else if (basicSettings) {
              // En azından temel ayarları alabildik
              setSettings({
                ...basicSettings,
                webhook_id: '',
                investment_amount: 0,
                total_investment_amount: 0,
                custom_webhook_url: '',
                api_key_set: false,
                token_set: false
              });
            }
          } 
          // Diğer hata türleri
          else if (settingsError.code !== 'PGRST116') {
            if (showLoading) setIsLoading(false);
            toast.showErrorToast('Hata', `Kullanıcı ayarları yüklenemedi: ${settingsError.message}`);
            throw settingsError;
          }
        } else {
          console.log('[useManagementData] User settings fetched successfully:', settingsData);
          setSettings(settingsData);
        }
      } catch (settingsErr) {
        console.error('[useManagementData] Unexpected error in settings fetch:', settingsErr);
        setSettings(null);
      }

      try {
        // Kullanıcının aktif aboneliklerini getir (RPC ile robot detaylarını da al)
        const { data: subscriptionsData, error: subscriptionsError } = await supabase
          .rpc('fetch_user_subscriptions_detailed', { p_user_id: user.id });

        // Abonelikler cevabını işle
        if (subscriptionsError) {
          console.error('[useManagementData] Error fetching subscriptions:', {
            message: subscriptionsError.message,
            details: subscriptionsError.details,
            hint: subscriptionsError.hint,
            code: subscriptionsError.code
          });
          
          toast.showErrorToast('Hata', `Abonelik bilgileri yüklenemedi: ${subscriptionsError.message}`);
        } else if (subscriptionsData) {
          console.log('[useManagementData] Subscriptions fetched successfully (raw):', subscriptionsData);
          // Sadece aktif ve (açıkça silinmemiş VEYA silinme durumu belirtilmemiş) abonelikleri filtrele
          const activeSubscriptions = subscriptionsData.filter(
            (sub: SubscriptionWithDetails) => sub.is_active === true && (sub.is_deleted === false || sub.is_deleted === undefined)
          );
          console.log('[useManagementData] Filtered active subscriptions:', activeSubscriptions);
          setSubscriptions(activeSubscriptions);
        } else {
          setSubscriptions([]);
        }
      } catch (subscriptionsErr) {
        console.error('[useManagementData] Unexpected error in subscriptions fetch:', subscriptionsErr);
        setSubscriptions([]);
      }
    } catch (err: any) {
      console.error('[useManagementData] Error loading data:', err);
      setError(err);
      toast.showErrorToast('Hata', `Veriler yüklenirken bir sorun oluştu: ${err.message || 'Bilinmeyen hata'}`);
      // Hata durumunda verileri temizle
      setSettings(null);
      setSubscriptions([]);
    } finally {
      if (showLoading) setIsLoading(false);
    }
  }, [user, toast]);

  // Kullanıcı ayarlarını güncelleme
  const updateSettings = useCallback(async (newSettings: Partial<UserSettings>): Promise<boolean> => {
    if (!user) {
      console.error('[useManagementData] updateSettings called but user is null');
      toast.showErrorToast('Hata', 'Kullanıcı bilgisi bulunamadı. Lütfen yeniden giriş yapın.');
      return false;
    }
    
    setIsUpdating(true);
    setError(null);
    
    try {
      console.log('[useManagementData] Updating settings for user:', user.id);
      console.log('[useManagementData] New settings:', newSettings);
      
      // id alanı newSettings içinde varsa çıkar (çakışma olmaması için)
      const { id, ...updateData } = newSettings as any;
      
      const { data, error: updateError } = await supabase
        .from('user_settings')
        .update(updateData)
        .eq('id', user.id)
        .select();
      
      if (updateError) {
        console.error('[useManagementData] Error updating settings:', updateError);
        throw updateError;
      }
      
      console.log('[useManagementData] Settings updated successfully:', data);
      
      // Yerel state'i güncelle
      if (data && data.length > 0) {
        setSettings(prevSettings => {
          if (!prevSettings) return data[0];
          return { ...prevSettings, ...data[0] };
        });
      } else {
        // Veri dönmediyse tüm verileri yeniden yükle
        await fetchData(false);
      }
      
      toast.showSuccessToast('Başarılı', 'Ayarlar başarıyla güncellendi.');
      return true;
    } catch (err: any) {
      console.error('[useManagementData] Error updating settings:', err);
      setError(err);
      toast.showErrorToast('Hata', `Ayarlar güncellenemedi: ${err.message || 'Bilinmeyen hata'}`);
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, [user, toast, fetchData]);

  // API anahtarı ve token'ı güvenli bir şekilde tek seferde kaydetme
  const saveCredentials = useCallback(async (payload: CredentialsPayload): Promise<boolean> => {
    // 1. Log Received Payload
    console.log('[useManagementData] saveCredentials HOOK FUNCTION CALLED. Received payload:', JSON.parse(JSON.stringify(payload)));

    if (!user) {
      console.error('[useManagementData] saveCredentials called but user is null');
      toast.showErrorToast('Hata', 'Kullanıcı bilgisi bulunamadı. Lütfen yeniden giriş yapın.');
      return false;
    }
    
    setIsUpdating(true);
    setError(null);
    
    // 2. Prepare Body for Invoke (ensure both keys are present, possibly as null)
    const bodyToInvoke: CredentialsPayload = {
      apiKey: payload.apiKey !== undefined ? payload.apiKey : null,
      token: payload.token !== undefined ? payload.token : null,
    };
    
    // 3. Log Constructed Body Object
    console.log('[useManagementData] Constructing body for invoke (object):', JSON.parse(JSON.stringify(bodyToInvoke)));
    
    // 4. Stringify Body and Log
    const bodyAsString = JSON.stringify(bodyToInvoke);
    console.log('[useManagementData] Stringified body for Edge Function:', bodyAsString);
    console.log('[useManagementData] Body string length:', bodyAsString.length);
    
    try {
      // Get JWT token for authorization
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        console.error('[useManagementData] No active session found.');
        throw new Error('Oturum bilgisi bulunamadı. Lütfen yeniden giriş yapın.');
      }
      
      const accessToken = session.access_token;
      
      // 5. TRY DIRECT FETCH
      console.log('[useManagementData] Attempting direct fetch to Edge Function');
      
      // Sabit Supabase URL'yi kullan
      const edgeFunctionUrl = 'https://fllklckmycxcgwhboiji.supabase.co/functions/v1/secure-save-api-key';
      console.log('[useManagementData] Edge Function URL:', edgeFunctionUrl);
      
      try {
        const response = await fetch(edgeFunctionUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`,
          },
          body: bodyAsString,
        });
        
        console.log('[useManagementData] Direct fetch response status:', response.status);
        const responseData = await response.json();
        console.log('[useManagementData] Direct fetch response data:', responseData);
        
        if (!response.ok) {
          console.error('[useManagementData] Direct fetch error:', responseData);
          throw new Error(responseData.error || responseData.message || 'Edge function direct fetch error');
        }
        
        console.log('[useManagementData] Direct fetch successful with response:', responseData);
        
        if (responseData.success) {
          setSettings(prev => {
            if (!prev) return prev;
            const updatedSettings = { ...prev };
            if (payload.apiKey !== undefined) {
              updatedSettings.api_key_set = payload.apiKey !== null && payload.apiKey !== '';
            }
            if (payload.token !== undefined) {
              updatedSettings.token_set = payload.token !== null && payload.token !== '';
            }
            return updatedSettings;
          });
          await fetchData(false);
          const updatedCredentials = [];
          if (payload.apiKey !== undefined && payload.apiKey !== null && payload.apiKey !== '') updatedCredentials.push('API anahtarı');
          if (payload.token !== undefined && payload.token !== null && payload.token !== '') updatedCredentials.push('Token');
          
          if (updatedCredentials.length > 0) {
            toast.showSuccessToast('Başarılı', `${updatedCredentials.join(' ve ')} güvenli bir şekilde kaydedildi.`);
          } else {
            toast.showInfoToast('Bilgi', 'Kimlik bilgileri için herhangi bir değişiklik yapılmadı.');
          }
          return true;
        }
        
        // Fallback for unexpected response format
        if (!responseData.error) {
          console.warn('[useManagementData] Edge function returned data but no explicit success field. Assuming success based on no error.');
          await fetchData(false);
          toast.showSuccessToast('Başarılı', 'Kimlik bilgileri işlendi.');
          return true;
        }
        
        throw new Error(responseData.error || 'Bilinmeyen hata');
      } 
      catch (fetchError) {
        console.error('[useManagementData] Direct fetch approach failed:', fetchError);
        throw fetchError;
      }
      
    } catch (err: any) {
      console.error('[useManagementData] Error saving credentials (outer catch):', err);
      setError(err);
      toast.showErrorToast('Hata', `Kimlik bilgileri kaydedilemedi: ${err.message || 'Bilinmeyen hata'}`);
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, [user, toast, fetchData]);

  // Aboneliği silme
  const deleteSubscription = useCallback(async (subscriptionId: string): Promise<boolean> => {
    if (!user) {
      console.error('[useManagementData] deleteSubscription called but user is null');
      toast.showErrorToast('Hata', 'Kullanıcı bilgisi bulunamadı. Lütfen yeniden giriş yapın.');
      return false;
    }
    
    // Bu abonelik için yükleniyor durumunu ayarla
    setIsDeleting(prev => ({ ...prev, [subscriptionId]: true }));
    setError(null);
    
    try {
      console.log(`[useManagementData] Deleting subscription with ID: ${subscriptionId}`);
      
      // RPC fonksiyonunu çağır
      const { data, error: deleteError } = await supabase.rpc('soft_delete_subscription', {
        p_subscription_id_to_delete: subscriptionId,
        p_user_id: user.id
      });
      
      if (deleteError) {
        console.error(`[useManagementData] Error deleting subscription: ID=${subscriptionId}`, deleteError);
        throw deleteError;
      }
      
      console.log(`[useManagementData] Subscription deleted successfully: ID=${subscriptionId}`, data);
      
      // Yerel abonelik listesini güncelle (silinen aboneliği çıkar)
      setSubscriptions(prevSubscriptions => 
        prevSubscriptions ? prevSubscriptions.filter(sub => sub.subscription_id !== subscriptionId) : null
      );
      
      toast.showSuccessToast('Başarılı', 'Robot aboneliğiniz iptal edildi.');
      return true;
    } catch (err: any) {
      console.error('[useManagementData] Error deleting subscription:', err);
      setError(err);
      toast.showErrorToast('Hata', `Abonelik iptal edilemedi: ${err.message || 'Bilinmeyen hata'}`);
      return false;
    } finally {
      // Bu abonelik için yükleniyor durumunu temizle
      setIsDeleting(prev => ({ ...prev, [subscriptionId]: false }));
    }
  }, [user, toast]);

  // Abonelik durumunu değiştirme (aktif/pasif)
  const toggleSubscriptionStatus = useCallback(async (subscriptionId: string, isActive: boolean): Promise<boolean> => {
    if (!user) {
      console.error('[useManagementData] toggleSubscriptionStatus called but user is null');
      toast.showErrorToast('Hata', 'Kullanıcı bilgisi bulunamadı. Lütfen yeniden giriş yapın.');
      return false;
    }
    
    // Bu abonelik için yükleniyor durumunu ayarla
    setIsDeleting(prev => ({ ...prev, [subscriptionId]: true }));
    setError(null);
    
    try {
      console.log(`[useManagementData] Toggling subscription status: ID=${subscriptionId}, isActive=${isActive}`);
      
      // RPC fonksiyonunu çağır
      const { data, error: updateError } = await supabase.rpc('update_subscription_active_status', {
        subscription_id_param: subscriptionId,
        is_active_param: isActive
      });
      
      if (updateError) {
        console.error(`[useManagementData] Error updating subscription status: ID=${subscriptionId}`, updateError);
        throw updateError;
      }
      
      console.log(`[useManagementData] Subscription status updated successfully: ID=${subscriptionId}, isActive=${isActive}`, data);
      
      // Yerel abonelik listesini güncelle
      setSubscriptions(prevSubscriptions => {
        if (!prevSubscriptions) return null;
        
        return prevSubscriptions.map(sub => {
          if (sub.subscription_id === subscriptionId) {
            return { ...sub, is_active: isActive };
          }
          return sub;
        });
      });
      
      toast.showSuccessToast('Başarılı', `Robot aboneliği ${isActive ? 'aktif' : 'pasif'} duruma getirildi.`);
      return true;
    } catch (err: any) {
      console.error('[useManagementData] Error updating subscription status:', err);
      setError(err);
      toast.showErrorToast('Hata', `Abonelik durumu değiştirilemedi: ${err.message || 'Bilinmeyen hata'}`);
      return false;
    } finally {
      // Bu abonelik için yükleniyor durumunu temizle
      setIsDeleting(prev => ({ ...prev, [subscriptionId]: false }));
    }
  }, [user, toast]);

  // Bileşen monte edildiğinde veya kullanıcı değiştiğinde verileri getir
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { 
    settings, 
    subscriptions, 
    isLoading, 
    isUpdating, 
    isDeleting, 
    error, 
    updateSettings, 
    deleteSubscription, 
    toggleSubscriptionStatus,
    refetch: fetchData,
    saveCredentials
  };
} 