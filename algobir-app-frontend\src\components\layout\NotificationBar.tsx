import React, { useState } from 'react';
import {
  Box,
  Flex,
  Text,
  Icon,
  Button,
  CloseButton,
  useColorModeValue,
  <PERSON><PERSON>se,
  <PERSON><PERSON>tack,
  Badge
} from '@chakra-ui/react';
import { FiAlertTriangle, FiSettings } from 'react-icons/fi';
import { differenceInHours, format } from 'date-fns';
import { tr } from 'date-fns/locale';

interface NotificationBarProps {
  apiCredentialsExpiryDate?: string | null | undefined;
  onConfigureClick?: () => void;
  onDismiss?: () => void;
  isDismissed?: boolean;
}

const NotificationBar: React.FC<NotificationBarProps> = ({
  apiCredentialsExpiryDate,
  onConfigureClick,
  onDismiss,
  isDismissed = false
}) => {
  const [isVisible, setIsVisible] = useState(!isDismissed);

  // Theme colors
  const bg = useColorModeValue('red.50', 'red.900');
  const borderColor = useColorModeValue('red.200', 'red.600');
  const textColor = useColorModeValue('red.800', 'red.100');
  const iconColor = useColorModeValue('red.500', 'red.300');

  // Check if API credentials are expiring within 48 hours
  const checkExpiryUrgency = (expiryDate: string | null | undefined) => {
    if (!expiryDate) return null;

    const expiry = new Date(expiryDate);
    const now = new Date();
    const hoursUntilExpiry = differenceInHours(expiry, now);

    if (hoursUntilExpiry <= 48 && hoursUntilExpiry > 0) {
      return {
        hoursLeft: hoursUntilExpiry,
        formattedDate: format(expiry, 'd MMMM yyyy, HH:mm', { locale: tr })
      };
    }

    return null;
  };

  const credentialsUrgency = checkExpiryUrgency(apiCredentialsExpiryDate);

  // Don't show if no urgent expiry or if dismissed
  const shouldShow = credentialsUrgency && isVisible;

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  if (!shouldShow) return null;

  const urgentCredential = 'API Kimlik Bilgileri';
  const urgentData = credentialsUrgency;

  return (
    <Collapse in={isVisible} animateOpacity>
      <Box
        bg={bg}
        borderBottom="1px solid"
        borderColor={borderColor}
        py={{ base: 2, md: 3 }}
        px={{ base: 3, md: 4 }}
        position="relative"
        zIndex="banner"
        w="100%"
        minH={{ base: '48px', md: '56px' }}
        display="flex"
        alignItems="center"
        data-testid="notification-bar"
      >
        <Flex
          align="center"
          justify="space-between"
          maxW="1200px"
          mx="auto"
          gap={4}
        >
          {/* Left side - Alert content */}
          <Flex align="center" flex={1} minW={0}>
            <Icon
              as={FiAlertTriangle}
              color={iconColor}
              w={5}
              h={5}
              mr={3}
              flexShrink={0}
            />
            
            <HStack spacing={2} flex={1} minW={0}>
              <Text
                color={textColor}
                fontSize={{ base: 'sm', md: 'md' }}
                fontWeight="600"
                noOfLines={1}
              >
                ⚠️ {urgentCredential} süresi yakında dolacak!
              </Text>
              
              {urgentData && (
                <Badge
                  colorScheme="red"
                  variant="solid"
                  fontSize="xs"
                  px={2}
                  py={1}
                  borderRadius="md"
                >
                  {urgentData.hoursLeft} saat kaldı
                </Badge>
              )}
            </HStack>
          </Flex>

          {/* Right side - Actions */}
          <HStack spacing={2} flexShrink={0}>
            <Button
              leftIcon={<Icon as={FiSettings} />}
              size="sm"
              colorScheme="red"
              variant="solid"
              onClick={onConfigureClick}
              fontSize="xs"
              fontWeight="600"
              px={4}
              _hover={{
                transform: 'translateY(-1px)',
                boxShadow: 'md'
              }}
            >
              Ayarla
            </Button>
            
            <CloseButton
              size="sm"
              color={textColor}
              onClick={handleDismiss}
              _hover={{
                bg: 'red.100',
                color: 'red.800'
              }}
            />
          </HStack>
        </Flex>

        {/* Additional info for mobile */}
        {urgentData && (
          <Box
            mt={2}
            display={{ base: 'block', md: 'none' }}
          >
            <Text
              color={textColor}
              fontSize="xs"
              opacity={0.8}
            >
              Son kullanma: {urgentData.formattedDate}
            </Text>
          </Box>
        )}
      </Box>
    </Collapse>
  );
};

export default NotificationBar;
