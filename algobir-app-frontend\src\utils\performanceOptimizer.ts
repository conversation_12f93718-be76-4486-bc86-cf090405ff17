/**
 * Performance Optimization Utilities
 * 
 * Comprehensive performance optimization tools for Core Web Vitals improvement
 * Targets: LCP < 2.5s, FID < 100ms, CLS < 0.1
 */

/**
 * Core Web Vitals thresholds
 */
export const PERFORMANCE_THRESHOLDS = {
  LCP: 2500, // Largest Contentful Paint
  FID: 100,  // First Input Delay
  CLS: 0.1,  // Cumulative Layout Shift
  FCP: 1800, // First Contentful Paint
  TTFB: 800, // Time to First Byte
} as const;

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  lcp?: number;
  fid?: number;
  cls?: number;
  fcp?: number;
  ttfb?: number;
  timestamp: number;
  url: string;
  userAgent: string;
}

/**
 * Performance observer for Core Web Vitals
 */
class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    timestamp: Date.now(),
    url: window.location.href,
    userAgent: navigator.userAgent
  };

  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          this.metrics.lcp = lastEntry.startTime;
          this.checkThresholds();
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.metrics.fid = entry.processingStart - entry.startTime;
            this.checkThresholds();
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID observer not supported');
      }

      // Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          this.metrics.cls = clsValue;
          this.checkThresholds();
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS observer not supported');
      }

      // First Contentful Paint (FCP)
      try {
        const fcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.fcp = entry.startTime;
              this.checkThresholds();
            }
          });
        });
        fcpObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(fcpObserver);
      } catch (e) {
        console.warn('FCP observer not supported');
      }
    }

    // Time to First Byte (TTFB)
    if ('performance' in window && 'timing' in performance) {
      const timing = performance.timing;
      this.metrics.ttfb = timing.responseStart - timing.requestStart;
    }
  }

  private checkThresholds() {
    const { lcp, fid, cls, fcp, ttfb } = this.metrics;
    
    // Log performance warnings
    if (lcp && lcp > PERFORMANCE_THRESHOLDS.LCP) {
      console.warn(`⚠️ LCP is ${lcp}ms (threshold: ${PERFORMANCE_THRESHOLDS.LCP}ms)`);
    }
    
    if (fid && fid > PERFORMANCE_THRESHOLDS.FID) {
      console.warn(`⚠️ FID is ${fid}ms (threshold: ${PERFORMANCE_THRESHOLDS.FID}ms)`);
    }
    
    if (cls && cls > PERFORMANCE_THRESHOLDS.CLS) {
      console.warn(`⚠️ CLS is ${cls} (threshold: ${PERFORMANCE_THRESHOLDS.CLS})`);
    }

    if (fcp && fcp > PERFORMANCE_THRESHOLDS.FCP) {
      console.warn(`⚠️ FCP is ${fcp}ms (threshold: ${PERFORMANCE_THRESHOLDS.FCP}ms)`);
    }

    if (ttfb && ttfb > PERFORMANCE_THRESHOLDS.TTFB) {
      console.warn(`⚠️ TTFB is ${ttfb}ms (threshold: ${PERFORMANCE_THRESHOLDS.TTFB}ms)`);
    }
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public disconnect() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Global performance monitor instance
let performanceMonitor: PerformanceMonitor | null = null;

/**
 * Initialize performance monitoring
 */
export function initializePerformanceMonitoring(): PerformanceMonitor {
  if (!performanceMonitor) {
    performanceMonitor = new PerformanceMonitor();
  }
  return performanceMonitor;
}

/**
 * Get current performance metrics
 */
export function getPerformanceMetrics(): PerformanceMetrics | null {
  return performanceMonitor?.getMetrics() || null;
}

/**
 * Resource loading optimization
 */
export class ResourceOptimizer {
  /**
   * Preload critical resources
   */
  static preloadResource(href: string, as: string, crossorigin?: string) {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (crossorigin) link.crossOrigin = crossorigin;
    document.head.appendChild(link);
  }

  /**
   * Prefetch resources for future navigation
   */
  static prefetchResource(href: string) {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    document.head.appendChild(link);
  }

  /**
   * Preconnect to external domains
   */
  static preconnect(href: string, crossorigin?: boolean) {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = href;
    if (crossorigin) link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  }

  /**
   * DNS prefetch for external domains
   */
  static dnsPrefetch(href: string) {
    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = href;
    document.head.appendChild(link);
  }
}

/**
 * Image optimization utilities
 */
export class ImageOptimizer {
  /**
   * Generate responsive image srcSet
   */
  static generateSrcSet(baseUrl: string, sizes: number[]): string {
    return sizes
      .map(size => `${baseUrl}?w=${size}&q=75 ${size}w`)
      .join(', ');
  }

  /**
   * Generate sizes attribute for responsive images
   */
  static generateSizes(breakpoints: { [key: string]: string }): string {
    return Object.entries(breakpoints)
      .map(([media, size]) => `${media} ${size}`)
      .join(', ');
  }

  /**
   * Lazy load image with intersection observer
   */
  static lazyLoadImage(img: HTMLImageElement, src: string, srcSet?: string) {
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const image = entry.target as HTMLImageElement;
            image.src = src;
            if (srcSet) image.srcset = srcSet;
            image.classList.remove('lazy');
            observer.unobserve(image);
          }
        });
      });
      observer.observe(img);
    } else {
      // Fallback for browsers without IntersectionObserver
      img.src = src;
      if (srcSet) img.srcset = srcSet;
    }
  }

  /**
   * Convert image to WebP format (client-side check)
   */
  static supportsWebP(): Promise<boolean> {
    return new Promise((resolve) => {
      const webP = new Image();
      webP.onload = webP.onerror = () => {
        resolve(webP.height === 2);
      };
      webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    });
  }

  /**
   * Get optimized image URL based on device capabilities
   */
  static async getOptimizedImageUrl(baseUrl: string, width?: number, quality = 75): Promise<string> {
    const supportsWebP = await this.supportsWebP();
    const format = supportsWebP ? 'webp' : 'jpg';
    
    let url = `${baseUrl}?f=${format}&q=${quality}`;
    if (width) url += `&w=${width}`;
    
    return url;
  }
}

/**
 * Bundle optimization utilities
 */
export class BundleOptimizer {
  /**
   * Dynamic import with error handling and retry
   */
  static async dynamicImport<T>(
    importFn: () => Promise<T>,
    retries = 3,
    delay = 1000
  ): Promise<T> {
    for (let i = 0; i < retries; i++) {
      try {
        return await importFn();
      } catch (error) {
        if (i === retries - 1) throw error;
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
    throw new Error('Dynamic import failed after retries');
  }

  /**
   * Preload route chunks
   */
  static preloadRouteChunk(routePath: string) {
    // This would be implemented based on your routing structure
    const chunkMap: Record<string, () => Promise<any>> = {
      '/dashboard': () => import('../pages/Dashboard'),
      '/trades': () => import('../pages/Trades'),
      '/statistics': () => import('../pages/Statistics'),
      '/management': () => import('../pages/Management'),
      '/marketplace': () => import('../pages/marketplace/MarketplacePage'),
    };

    const importFn = chunkMap[routePath];
    if (importFn) {
      this.dynamicImport(importFn).catch(() => {
        // Silently fail preloading
      });
    }
  }
}

/**
 * Initialize all performance optimizations
 */
export function initializePerformanceOptimizations() {
  // Initialize performance monitoring
  initializePerformanceMonitoring();

  // Preconnect to external services
  ResourceOptimizer.preconnect('https://fonts.googleapis.com');
  ResourceOptimizer.preconnect('https://fonts.gstatic.com', true);
  ResourceOptimizer.dnsPrefetch('https://api.supabase.co');

  // Preload critical fonts
  ResourceOptimizer.preloadResource(
    'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap',
    'style'
  );

  console.log('🚀 Performance optimizations initialized');
}
