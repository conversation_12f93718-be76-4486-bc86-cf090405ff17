import React from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  FormErrorMessage,
  VStack,
  Heading,
  Text,
  HStack,
  Icon,
  SimpleGrid,
} from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';
import { FaChartLine, FaDollarSign } from 'react-icons/fa';
import { RobotFormData } from '../RobotSetupWizard';

const strategyTypes = [
  { value: 'trend_following', label: 'Trend Takibi', description: 'Piyasa trendlerini takip eder' },
  { value: 'mean_reversion', label: 'Ortalamaya Dönüş', description: 'Fiyat ortalamalarına dayalı strateji' },
  { value: 'breakout', label: 'Kırılım', description: 'Direnç/destek seviyelerini kıran hareketler' },
  { value: 'momentum', label: 'Momentum', description: '<PERSON><PERSON><PERSON><PERSON>ü fiyat hareketlerini yakalar' },
  { value: 'swing_trading', label: 'Salınım Ticareti', description: 'Orta vadeli pozisyonlar' },
  { value: 'scalping', label: 'Scalping', description: 'Kısa vadeli hızlı işlemler' },
  { value: 'other', label: 'Diğer', description: 'Özel/karma strateji' },
];

export const Step2TradingSettings: React.FC = () => {
  const { register, formState: { errors }, setValue, watch } = useFormContext<RobotFormData>();
  
  const selectedStrategy = watch('strategy_type');
  const investmentAmount = watch('investment_amount_per_position');

  return (
    <Box>
      <VStack spacing={6} align="stretch">
        <Box textAlign="center" mb={4}>
          <HStack justify="center" mb={3}>
            <Icon as={FaChartLine} boxSize={8} color="brand.500" />
          </HStack>
          <Heading size="lg" mb={2}>Ticaret Ayarları</Heading>
          <Text color="gray.600">
            Robotunuzun kullanacağı stratejiyi ve yatırım miktarını belirleyin.
          </Text>
        </Box>

        <FormControl isInvalid={!!errors.strategy_type} isRequired>
          <FormLabel fontWeight="semibold">Strateji Türü</FormLabel>
          <Select
            {...register('strategy_type', {
              required: 'Strateji türü seçimi zorunludur'
            })}
            placeholder="Bir strateji türü seçin"
            size="lg"
          >
            {strategyTypes.map((strategy) => (
              <option key={strategy.value} value={strategy.value}>
                {strategy.label}
              </option>
            ))}
          </Select>
          <FormErrorMessage>{errors.strategy_type?.message}</FormErrorMessage>
          
          {selectedStrategy && (
            <Box mt={3} p={3} bg="blue.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="blue.400">
              <Text fontSize="sm" fontWeight="medium" mb={1}>
                {strategyTypes.find(s => s.value === selectedStrategy)?.label}
              </Text>
              <Text fontSize="sm" color="gray.600">
                {strategyTypes.find(s => s.value === selectedStrategy)?.description}
              </Text>
            </Box>
          )}
        </FormControl>

        <FormControl isInvalid={!!errors.investment_amount_per_position} isRequired>
          <FormLabel fontWeight="semibold">
            <HStack>
              <Icon as={FaDollarSign} />
              <Text>Pozisyon Başına Yatırım Miktarı (TL)</Text>
            </HStack>
          </FormLabel>
          <NumberInput
            defaultValue={1000}
            min={100}
            max={1000000}
            size="lg"
            onChange={(valueString) => setValue('investment_amount_per_position', Number(valueString))}
          >
            <NumberInputField
              {...register('investment_amount_per_position', {
                required: 'Yatırım miktarı zorunludur',
                min: {
                  value: 100,
                  message: 'Minimum yatırım miktarı 100 TL olmalıdır'
                },
                max: {
                  value: 1000000,
                  message: 'Maksimum yatırım miktarı 1,000,000 TL olabilir'
                }
              })}
            />
            <NumberInputStepper>
              <NumberIncrementStepper />
              <NumberDecrementStepper />
            </NumberInputStepper>
          </NumberInput>
          <FormErrorMessage>{errors.investment_amount_per_position?.message}</FormErrorMessage>
          <Text fontSize="sm" color="gray.500" mt={1}>
            Her işlemde kullanılacak olan yatırım miktarı. Aboneleriniz bu miktarı referans alarak kendi pozisyon büyüklüklerini ayarlayabilir.
          </Text>
          
          {investmentAmount && (
            <SimpleGrid columns={3} spacing={4} mt={4}>
              <Box p={3} bg="green.50" borderRadius="md" textAlign="center">
                <Text fontSize="xs" color="green.600" fontWeight="medium">DÜŞÜK RİSK</Text>
                <Text fontSize="sm" fontWeight="bold">₺{(investmentAmount * 0.5).toFixed(0)}</Text>
                <Text fontSize="xs" color="gray.600">%50 oranında</Text>
              </Box>
              <Box p={3} bg="yellow.50" borderRadius="md" textAlign="center">
                <Text fontSize="xs" color="yellow.600" fontWeight="medium">ORTA RİSK</Text>
                <Text fontSize="sm" fontWeight="bold">₺{investmentAmount}</Text>
                <Text fontSize="xs" color="gray.600">Önerilen miktar</Text>
              </Box>
              <Box p={3} bg="red.50" borderRadius="md" textAlign="center">
                <Text fontSize="xs" color="red.600" fontWeight="medium">YÜKSEK RİSK</Text>
                <Text fontSize="sm" fontWeight="bold">₺{(investmentAmount * 2).toFixed(0)}</Text>
                <Text fontSize="xs" color="gray.600">%200 oranında</Text>
              </Box>
            </SimpleGrid>
          )}
        </FormControl>
      </VStack>
    </Box>
  );
}; 