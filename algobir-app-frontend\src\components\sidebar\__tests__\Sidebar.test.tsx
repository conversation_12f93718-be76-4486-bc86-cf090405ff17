import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { Icon } from '@chakra-ui/react';
import { FiHome, FiSettings } from 'react-icons/fi';
import Sidebar, { SidebarResponsive } from '../Sidebar';
import { SidebarProvider } from '../../../context/SidebarContext';
import { RouteType } from '../../../routes';

// Mock routes for testing
const mockRoutes: RouteType[] = [
  {
    name: 'Dashboard',
    layout: '',
    path: '/',
    icon: <Icon as={FiHome} width="20px" height="20px" color="inherit" />,
  },
  {
    name: 'Settings',
    layout: '',
    path: '/settings',
    icon: <Icon as={FiSettings} width="20px" height="20px" color="inherit" />,
    hasSubmenu: true,
    children: [
      {
        id: 'profile',
        name: 'Profile Settings',
        path: '/settings/profile',
        description: 'Manage your profile'
      },
      {
        id: 'account',
        name: 'Account Set<PERSON><PERSON>',
        path: '/settings/account',
        description: 'Manage your account'
      }
    ]
  }
];

import { vi } from 'vitest';

// Mock useAdminAuth hook
vi.mock('../../../hooks/useAdminAuth', () => ({
  useAdminAuth: () => ({ isAdmin: true })
}));

// Mock react-custom-scrollbars-2
vi.mock('react-custom-scrollbars-2', () => ({
  Scrollbars: ({ children, ...props }: any) => (
    <div data-testid="scrollbars" {...props}>
      {children}
    </div>
  )
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ChakraProvider>
      <BrowserRouter>
        <SidebarProvider>
          {component}
        </SidebarProvider>
      </BrowserRouter>
    </ChakraProvider>
  );
};

describe('Sidebar Component', () => {
  beforeEach(() => {
    // Mock window.matchMedia for responsive breakpoints
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query.includes('(min-width: 992px)'), // lg breakpoint
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });
  });

  test('should render sidebar with navigation items', () => {
    renderWithProviders(<Sidebar routes={mockRoutes} />);
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  test('should render sidebar with proper ARIA attributes', () => {
    renderWithProviders(<Sidebar routes={mockRoutes} />);
    
    const navigation = screen.getByRole('navigation');
    expect(navigation).toHaveAttribute('aria-label', 'Ana navigasyon');
    expect(navigation).toHaveAttribute('aria-expanded', 'true');
  });

  test('should show collapsed content when sidebar is collapsed', () => {
    renderWithProviders(<Sidebar routes={mockRoutes} />);
    
    // First collapse the sidebar by clicking a toggle button
    // Note: This would require the sidebar to be in collapsed state
    // For now, we'll test the structure exists
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });

  test('should handle mouse hover events', () => {
    renderWithProviders(<Sidebar routes={mockRoutes} />);
    
    const sidebarContainer = screen.getByRole('navigation').parentElement;
    
    // Test mouse enter
    fireEvent.mouseEnter(sidebarContainer!);
    // Mouse hover state is handled internally
    
    // Test mouse leave
    fireEvent.mouseLeave(sidebarContainer!);
    // Mouse hover state is handled internally
  });

  test('should render scrollbars component', () => {
    renderWithProviders(<Sidebar routes={mockRoutes} />);
    
    expect(screen.getByTestId('scrollbars')).toBeInTheDocument();
  });

  test('should apply proper styling and transitions', () => {
    renderWithProviders(<Sidebar routes={mockRoutes} />);
    
    const sidebarContainer = screen.getByRole('navigation').parentElement;
    expect(sidebarContainer).toHaveStyle({
      position: 'relative',
      borderRadius: '0px 16px 16px 0px'
    });
  });
});

describe('SidebarResponsive Component', () => {
  beforeEach(() => {
    // Mock mobile breakpoint
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: !query.includes('(min-width: 992px)'), // Mobile view
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });
  });

  test('should render mobile menu button', () => {
    renderWithProviders(<SidebarResponsive />);
    
    const menuButton = screen.getByRole('button', { name: /menüyü aç/i });
    expect(menuButton).toBeInTheDocument();
    expect(menuButton).toHaveAttribute('aria-label', 'Menüyü aç');
  });

  test('should handle menu button click', () => {
    renderWithProviders(<SidebarResponsive />);
    
    const menuButton = screen.getByRole('button', { name: /menüyü aç/i });
    fireEvent.click(menuButton);
    
    // The click should trigger the onOpen function
    // This is handled by the SidebarContext
  });

  test('should have proper styling for mobile button', () => {
    renderWithProviders(<SidebarResponsive />);
    
    const menuButton = screen.getByRole('button', { name: /menüyü aç/i });
    expect(menuButton).toHaveStyle({
      borderRadius: '9999px', // full rounded
      minWidth: '44px',
      minHeight: '44px'
    });
  });

  test('should not render on desktop', () => {
    // Mock desktop breakpoint
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query.includes('(min-width: 992px)'), // Desktop view
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    const { container } = renderWithProviders(<SidebarResponsive />);
    expect(container.firstChild).toBeNull();
  });
});
