import React from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  Switch,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  FormErrorMessage,
  VStack,
  Heading,
  Text,
  HStack,
  Icon,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Badge,
  SimpleGrid,
} from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';
import { FaStore, FaDollarSign, FaClock, FaUsers } from 'react-icons/fa';
import { RobotFormData } from '../RobotSetupWizard';

const subscriptionPeriods = [
  { value: 7, label: '1 Hafta', popular: false },
  { value: 30, label: '1 Ay', popular: true },
  { value: 90, label: '3 Ay', popular: false },
  { value: 180, label: '6 Ay', popular: false },
  { value: 365, label: '1 Yıl', popular: false },
];

export const Step3MarketplaceSettings: React.FC = () => {
  const { register, formState: { errors }, setValue, watch } = useFormContext<RobotFormData>();
  
  const isPublic = watch('is_public');
  const isFree = watch('is_free');
  const price = watch('price');
  const subscriptionPeriod = watch('subscription_period');

  const calculateMonthlyRevenue = () => {
    if (!price || !subscriptionPeriod) return 0;
    const monthlyPrice = (price / subscriptionPeriod) * 30;
    return monthlyPrice;
  };

  return (
    <Box>
      <VStack spacing={6} align="stretch">
        <Box textAlign="center" mb={4}>
          <HStack justify="center" mb={3}>
            <Icon as={FaStore} boxSize={8} color="brand.500" />
          </HStack>
          <Heading size="lg" mb={2}>Pazar Yeri Ayarları</Heading>
          <Text color="gray.600">
            Robotunuzun pazar yerindeki görünürlüğünü ve fiyatlandırmasını ayarlayın.
          </Text>
        </Box>

        <FormControl>
          <HStack justify="space-between" align="center">
            <Box>
              <FormLabel fontWeight="semibold" mb={1}>
                <HStack>
                  <Icon as={FaUsers} />
                  <Text>Herkese Açık Robot</Text>
                </HStack>
              </FormLabel>
              <Text fontSize="sm" color="gray.600">
                Robotunuz pazar yerinde görünür olsun mu?
              </Text>
            </Box>
            <Switch
              {...register('is_public')}
              size="lg"
              colorScheme="brand"
              defaultChecked={true}
            />
          </HStack>
        </FormControl>

        {isPublic && (
          <>
            <Alert status="info" borderRadius="md">
              <AlertIcon />
              <Box>
                <AlertTitle fontSize="sm">Pazar Yeri Özelliği</AlertTitle>
                <AlertDescription fontSize="sm">
                  Robotunuz herkese açık olduğunda, diğer kullanıcılar abone olabilir ve sizden gelir elde edebilirsiniz.
                </AlertDescription>
              </Box>
            </Alert>

            <FormControl mt={4}>
              <HStack justify="space-between" align="center">
                <Box>
                  <FormLabel fontWeight="semibold" mb={1}>
                    Ücretsiz Robot Yap
                  </FormLabel>
                  <Text fontSize="sm" color="gray.600">
                    Robotunuzu ücretsiz yaparak daha fazla kullanıcıya ulaşabilirsiniz.
                  </Text>
                </Box>
                <Switch
                  {...register('is_free')}
                  size="lg"
                  colorScheme="green"
                  onChange={(e) => {
                    setValue('is_free', e.target.checked);
                    if (e.target.checked) {
                      setValue('price', 0);
                      setValue('subscription_period', 99999);
                    } else {
                      setValue('price', 100);
                      setValue('subscription_period', 30);
                    }
                  }}
                />
              </HStack>
            </FormControl>

            {!isFree && (
              <FormControl isInvalid={!!errors.price} isRequired={isPublic}>
                <FormLabel fontWeight="semibold">
                  <HStack>
                    <Icon as={FaDollarSign} />
                    <Text>Abonelik Ücreti (TL)</Text>
                  </HStack>
                </FormLabel>
                <NumberInput
                  defaultValue={100}
                  min={10}
                  max={10000}
                  size="lg"
                  onChange={(valueString) => setValue('price', Number(valueString))}
                >
                  <NumberInputField
                    {...register('price', {
                      required: isPublic && !isFree ? 'Fiyat zorunludur' : false,
                      min: {
                        value: 10,
                        message: 'Minimum fiyat 10 TL olmalıdır'
                      },
                      max: {
                        value: 10000,
                        message: 'Maksimum fiyat 10,000 TL olabilir'
                      }
                    })}
                  />
                  <NumberInputStepper>
                    <NumberIncrementStepper />
                    <NumberDecrementStepper />
                  </NumberInputStepper>
                </NumberInput>
                <FormErrorMessage>{errors.price?.message}</FormErrorMessage>
                <Text fontSize="sm" color="gray.500" mt={1}>
                  Kullanıcıların robotunuza abone olmak için ödeyeceği ücret.
                </Text>
              </FormControl>
            )}

            {!isFree && (
              <FormControl isInvalid={!!errors.subscription_period} isRequired={isPublic}>
                <FormLabel fontWeight="semibold">
                  <HStack>
                    <Icon as={FaClock} />
                    <Text>Abonelik Süresi</Text>
                  </HStack>
                </FormLabel>
                <SimpleGrid columns={5} spacing={3} mb={4}>
                  {subscriptionPeriods.map((period) => (
                    <Box
                      key={period.value}
                      p={3}
                      borderWidth={2}
                      borderRadius="md"
                      cursor="pointer"
                      textAlign="center"
                      borderColor={subscriptionPeriod === period.value ? "brand.500" : "gray.200"}
                      bg={subscriptionPeriod === period.value ? "brand.50" : "white"}
                      _hover={{ borderColor: "brand.300" }}
                      onClick={() => setValue('subscription_period', period.value)}
                      position="relative"
                    >
                      {period.popular && (
                        <Badge
                          position="absolute"
                          top="-8px"
                          left="50%"
                          transform="translateX(-50%)"
                          colorScheme="orange"
                          fontSize="xs"
                        >
                          Popüler
                        </Badge>
                      )}
                      <Text fontSize="sm" fontWeight="medium">
                        {period.label}
                      </Text>
                      <Text fontSize="xs" color="gray.600">
                        {period.value} gün
                      </Text>
                    </Box>
                  ))}
                </SimpleGrid>
                <input
                  type="hidden"
                  {...register('subscription_period', {
                    required: isPublic && !isFree ? 'Abonelik süresi seçimi zorunludur' : false
                  })}
                />
                <FormErrorMessage>{errors.subscription_period?.message}</FormErrorMessage>
                <Text fontSize="sm" color="gray.500">
                  Abonelerin robotunuzu ne kadar süre kullanabileceğini belirler.
                </Text>
              </FormControl>
            )}

            {isFree && (
              <Box p={4} bg="blue.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="blue.400">
                <Heading size="sm" mb={2} color="blue.700">Ücretsiz Robot Ayarları</Heading>
                <Text fontSize="sm" color="blue.600">
                  Robotunuz ücretsiz olacak ve kullanıcılar sınırsız süre boyunca abone olabilecek.
                  Bu sayede daha fazla kullanıcıya ulaşabilir ve popülerliğinizi artırabilirsiniz.
                </Text>
              </Box>
            )}

            {!isFree && price && subscriptionPeriod && (
              <Box p={4} bg="green.50" borderRadius="md" borderLeft="4px solid" borderLeftColor="green.400">
                <Heading size="sm" mb={2} color="green.700">Gelir Projeksiyonu</Heading>
                <SimpleGrid columns={3} spacing={4}>
                  <Box textAlign="center">
                    <Text fontSize="xl" fontWeight="bold" color="green.600">
                      ₺{price}
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      {subscriptionPeriod > 1000 ? "Sınırsız" : `${subscriptionPeriod} günlük`} gelir
                    </Text>
                  </Box>
                  <Box textAlign="center">
                    <Text fontSize="xl" fontWeight="bold" color="green.600">
                      ₺{price === 0 ? "0" : calculateMonthlyRevenue().toFixed(2)}
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      Abone başına aylık
                    </Text>
                  </Box>
                  <Box textAlign="center">
                    <Text fontSize="xl" fontWeight="bold" color="green.600">
                      ₺{price === 0 ? "0" : (calculateMonthlyRevenue() * 10).toFixed(0)}
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      10 abone ile aylık
                    </Text>
                  </Box>
                </SimpleGrid>
              </Box>
            )}
          </>
        )}

        {!isPublic && (
          <Alert status="warning" borderRadius="md">
            <AlertIcon />
            <Box>
              <AlertTitle fontSize="sm">Özel Robot</AlertTitle>
              <AlertDescription fontSize="sm">
                Robotunuz özel modda olacak ve sadece siz kullanabileceksiniz. Başkaları abone olamayacak.
              </AlertDescription>
            </Box>
          </Alert>
        )}
      </VStack>
    </Box>
  );
}; 