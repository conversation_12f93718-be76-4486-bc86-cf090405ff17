import React from 'react';
import {

  VStack,
  HStack,
  Text,
  Select,
  Badge,
  Card,
  CardBody,
  FormControl,
  FormLabel,
  Input,
  Button,
  ButtonGroup,
  useColorModeValue,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,

} from '@chakra-ui/react';

import { EnhancedStatsData, EnhancedFilterOptions } from '../../hooks/useEnhancedStatistics';

interface StatisticsHeaderProps {
  stats: EnhancedStatsData;
  filters: EnhancedFilterOptions;
  updateFilters: (filters: Partial<EnhancedFilterOptions>) => void;
}

const StatisticsHeader: React.FC<StatisticsHeaderProps> = ({
  stats,
  filters,
  updateFilters
}) => {
  const cardBg = useColorModeValue('white', 'gray.700');
  const textColor = useColorModeValue('gray.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.600');


  const formatCurrency = (value: number) => `₺${value.toFixed(2)}`;
  const formatPercent = (value: number) => `${value.toFixed(2)}%`;

  // Quick filter buttons for date ranges
  const dateRangeButtons = [
    { label: '7G', value: '7d' },
    { label: '30G', value: '30d' },
    { label: '90G', value: '90d' },
    { label: '6A', value: '6m' },
    { label: '1Y', value: '1y' },
    { label: 'Tümü', value: 'all' }
  ];

  return (
    <VStack spacing={4} align="stretch">
      {/* Summary Stats Card */}
      <Card bg={cardBg} borderColor={borderColor}>
        <CardBody>
          <VStack spacing={4} align="stretch">
            <Text fontSize="md" fontWeight="bold" color={textColor}>
              📈 Performans Özeti
            </Text>
            
            <SimpleGrid columns={2} spacing={4}>
              <Stat>
                <StatLabel fontSize="xs">Toplam ROI</StatLabel>
                <StatNumber 
                  fontSize="lg" 
                  color={stats.roiAnalysis.totalROI > 0 ? 'green.500' : 'red.500'}
                >
                  {formatPercent(stats.roiAnalysis.totalROI)}
                </StatNumber>
                <StatHelpText fontSize="xs">
                  {formatCurrency(stats.roiAnalysis.totalReturns)}
                </StatHelpText>
              </Stat>

              <Stat>
                <StatLabel fontSize="xs">Kazanma Oranı</StatLabel>
                <StatNumber 
                  fontSize="lg"
                  color={stats.winRate > 50 ? 'green.500' : 'red.500'}
                >
                  {formatPercent(stats.winRate)}
                </StatNumber>
                <StatHelpText fontSize="xs">
                  {stats.winningTrades}K / {stats.losingTrades}Z
                </StatHelpText>
              </Stat>
            </SimpleGrid>

            <HStack spacing={2} flexWrap="wrap">
              <Badge colorScheme="blue" variant="subtle">
                {stats.totalTrades} İşlem
              </Badge>
              <Badge 
                colorScheme={stats.realtimeMetrics.activeRobots > 0 ? 'green' : 'gray'} 
                variant="subtle"
              >
                {stats.realtimeMetrics.activeRobots} Robot
              </Badge>
              <Badge colorScheme="purple" variant="subtle">
                {formatCurrency(stats.roiAnalysis.totalInvestment)} Yatırım
              </Badge>
            </HStack>
          </VStack>
        </CardBody>
      </Card>

      {/* Filters Card */}
      <Card bg={cardBg} borderColor={borderColor}>
        <CardBody>
          <VStack spacing={4} align="stretch">
            <Text fontSize="md" fontWeight="bold" color={textColor}>
              🔍 Filtreler
            </Text>

            {/* Robot Type Filter */}
            <FormControl>
              <FormLabel fontSize="sm" color={textColor}>Robot Tipi</FormLabel>
              <Select 
                value={filters.robotType} 
                onChange={(e) => updateFilters({ robotType: e.target.value as any })}
                size="sm"
                bg={cardBg}
              >
                <option value="all">Tüm Robotlar</option>
                <option value="solo">Solo-Robot</option>
                <option value="bro">Bro-Robot</option>
              </Select>
            </FormControl>

            {/* Date Range Quick Buttons */}
            <FormControl>
              <FormLabel fontSize="sm" color={textColor}>Zaman Aralığı</FormLabel>
              <ButtonGroup size="sm" isAttached variant="outline" w="full">
                {dateRangeButtons.map((button) => (
                  <Button
                    key={button.value}
                    flex={1}
                    isActive={filters.dateRange === button.value}
                    onClick={() => updateFilters({ dateRange: button.value as any })}
                    colorScheme={filters.dateRange === button.value ? 'blue' : 'gray'}
                    variant={filters.dateRange === button.value ? 'solid' : 'outline'}
                  >
                    {button.label}
                  </Button>
                ))}
              </ButtonGroup>
            </FormControl>

            {/* Symbol Filter */}
            <FormControl>
              <FormLabel fontSize="sm" color={textColor}>Sembol</FormLabel>
              <Input
                placeholder="Örn: THYAO, AKBNK"
                value={filters.symbol || ''}
                onChange={(e) => updateFilters({ symbol: e.target.value || undefined })}
                size="sm"
              />
            </FormControl>

            <Divider />

            {/* Advanced Filters */}
            <Text fontSize="sm" fontWeight="semibold" color={textColor}>
              Gelişmiş Filtreler
            </Text>

            <HStack spacing={2}>
              <FormControl flex={1}>
                <FormLabel fontSize="xs" color={textColor}>Min. İşlem</FormLabel>
                <Input
                  type="number"
                  placeholder="0"
                  value={filters.minTradeSize || ''}
                  onChange={(e) => updateFilters({ 
                    minTradeSize: e.target.value ? Number(e.target.value) : undefined 
                  })}
                  size="sm"
                />
              </FormControl>

              <FormControl flex={1}>
                <FormLabel fontSize="xs" color={textColor}>Max. İşlem</FormLabel>
                <Input
                  type="number"
                  placeholder="∞"
                  value={filters.maxTradeSize || ''}
                  onChange={(e) => updateFilters({ 
                    maxTradeSize: e.target.value ? Number(e.target.value) : undefined 
                  })}
                  size="sm"
                />
              </FormControl>
            </HStack>

            <HStack spacing={2}>
              <Button
                size="sm"
                variant={filters.profitOnly ? 'solid' : 'outline'}
                colorScheme="green"
                onClick={() => updateFilters({ 
                  profitOnly: !filters.profitOnly,
                  lossOnly: false 
                })}
                flex={1}
              >
                Sadece Kar
              </Button>
              <Button
                size="sm"
                variant={filters.lossOnly ? 'solid' : 'outline'}
                colorScheme="red"
                onClick={() => updateFilters({ 
                  lossOnly: !filters.lossOnly,
                  profitOnly: false 
                })}
                flex={1}
              >
                Sadece Zarar
              </Button>
            </HStack>

            {/* ROI Specific Filters */}
            <Text fontSize="sm" fontWeight="semibold" color={textColor}>
              ROI Filtreleri
            </Text>

            <HStack spacing={2}>
              <FormControl flex={1}>
                <FormLabel fontSize="xs" color={textColor}>Min. ROI (%)</FormLabel>
                <Input
                  type="number"
                  placeholder="0"
                  value={filters.minROI || ''}
                  onChange={(e) => updateFilters({
                    minROI: e.target.value ? Number(e.target.value) : undefined
                  })}
                  size="sm"
                />
              </FormControl>

              <FormControl flex={1}>
                <FormLabel fontSize="xs" color={textColor}>Max. ROI (%)</FormLabel>
                <Input
                  type="number"
                  placeholder="∞"
                  value={filters.maxROI || ''}
                  onChange={(e) => updateFilters({
                    maxROI: e.target.value ? Number(e.target.value) : undefined
                  })}
                  size="sm"
                />
              </FormControl>
            </HStack>

            <FormControl>
              <FormLabel fontSize="sm" color={textColor}>Performans Seviyesi</FormLabel>
              <Select
                value={filters.performanceLevel || 'all'}
                onChange={(e) => updateFilters({ performanceLevel: e.target.value as any })}
                size="sm"
                bg={cardBg}
              >
                <option value="all">Tüm Seviyeler</option>
                <option value="excellent">Mükemmel (20%+)</option>
                <option value="good">İyi (10-20%)</option>
                <option value="average">Orta (5-10%)</option>
                <option value="poor">Zayıf (0-5%)</option>
                <option value="loss">Kayıp (0% altı)</option>
              </Select>
            </FormControl>

            <FormControl>
              <FormLabel fontSize="sm" color={textColor}>ROI Aralığı</FormLabel>
              <ButtonGroup size="sm" isAttached variant="outline" w="full">
                <Button
                  flex={1}
                  isActive={filters.roiRange === 'all'}
                  onClick={() => updateFilters({ roiRange: 'all' })}
                  colorScheme={filters.roiRange === 'all' ? 'blue' : 'gray'}
                >
                  Tümü
                </Button>
                <Button
                  flex={1}
                  isActive={filters.roiRange === 'positive'}
                  onClick={() => updateFilters({ roiRange: 'positive' })}
                  colorScheme={filters.roiRange === 'positive' ? 'green' : 'gray'}
                >
                  Pozitif
                </Button>
                <Button
                  flex={1}
                  isActive={filters.roiRange === 'negative'}
                  onClick={() => updateFilters({ roiRange: 'negative' })}
                  colorScheme={filters.roiRange === 'negative' ? 'red' : 'gray'}
                >
                  Negatif
                </Button>
              </ButtonGroup>
            </FormControl>

            {/* Clear Filters */}
            <Button
              size="sm"
              variant="ghost"
              onClick={() => updateFilters({
                robotType: 'all',
                dateRange: 'all',
                symbol: undefined,
                minTradeSize: undefined,
                maxTradeSize: undefined,
                profitOnly: false,
                lossOnly: false,
                minROI: undefined,
                maxROI: undefined,
                roiRange: 'all',
                performanceLevel: 'all',
                volatilityLevel: 'all'
              })}
              w="full"
            >
              Filtreleri Temizle
            </Button>
          </VStack>
        </CardBody>
      </Card>

      {/* Active Filters Display */}
      {(filters.symbol || filters.minTradeSize || filters.maxTradeSize ||
        filters.profitOnly || filters.lossOnly ||
        filters.robotType !== 'all' || filters.dateRange !== 'all' ||
        filters.minROI || filters.maxROI || filters.roiRange !== 'all' ||
        filters.performanceLevel !== 'all') && (
        <Card bg={cardBg} borderColor={borderColor}>
          <CardBody>
            <VStack spacing={2} align="stretch">
              <Text fontSize="sm" fontWeight="semibold" color={textColor}>
                Aktif Filtreler
              </Text>
              <HStack spacing={2} flexWrap="wrap">
                {filters.robotType !== 'all' && (
                  <Badge colorScheme="blue">
                    {filters.robotType === 'solo' ? 'Solo-Robot' : 'Bro-Robot'}
                  </Badge>
                )}
                {filters.dateRange !== 'all' && (
                  <Badge colorScheme="purple">
                    {dateRangeButtons.find(b => b.value === filters.dateRange)?.label}
                  </Badge>
                )}
                {filters.symbol && (
                  <Badge colorScheme="green">{filters.symbol}</Badge>
                )}
                {filters.profitOnly && (
                  <Badge colorScheme="green">Sadece Kar</Badge>
                )}
                {filters.lossOnly && (
                  <Badge colorScheme="red">Sadece Zarar</Badge>
                )}
                {(filters.minTradeSize || filters.maxTradeSize) && (
                  <Badge colorScheme="orange">
                    İşlem: {filters.minTradeSize || 0} - {filters.maxTradeSize || '∞'}
                  </Badge>
                )}
                {(filters.minROI || filters.maxROI) && (
                  <Badge colorScheme="purple">
                    ROI: {filters.minROI || 0}% - {filters.maxROI || '∞'}%
                  </Badge>
                )}
                {filters.roiRange !== 'all' && (
                  <Badge colorScheme={filters.roiRange === 'positive' ? 'green' : 'red'}>
                    {filters.roiRange === 'positive' ? 'Pozitif ROI' : 'Negatif ROI'}
                  </Badge>
                )}
                {filters.performanceLevel !== 'all' && (
                  <Badge colorScheme="teal">
                    {filters.performanceLevel === 'excellent' ? 'Mükemmel' :
                     filters.performanceLevel === 'good' ? 'İyi' :
                     filters.performanceLevel === 'average' ? 'Orta' :
                     filters.performanceLevel === 'poor' ? 'Zayıf' : 'Kayıp'} Performans
                  </Badge>
                )}
              </HStack>
            </VStack>
          </CardBody>
        </Card>
      )}
    </VStack>
  );
};

export default StatisticsHeader;
