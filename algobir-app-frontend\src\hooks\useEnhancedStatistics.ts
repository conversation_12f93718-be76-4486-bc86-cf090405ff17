import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabaseClient';
import { useAuth } from '../context/AuthContext';
import { Trade } from '../types/trade';
import { StatsData } from './useAdvancedStatisticsData';
import { useRealTimeStatistics } from './useRealTimeStatistics';
import { EnhancedSymbolAnalysisData } from '../types/symbolAnalysis';
import { useEnhancedSymbolAnalysis } from './useEnhancedSymbolAnalysis';

// Enhanced interfaces for the new statistics system
export interface RobotStats {
  robotId: string;
  robotName: string;
  robotType: 'solo' | 'bro';
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  totalPnl: number;
  totalInvestment: number;
  roi: number;
  annualizedROI: number;
  profitFactor: number;
  maxDrawdown: number;
  sharpeRatio: number;
  averageTradeSize: number;
  averageHoldingTime: number;
  bestTrade: number;
  worstTrade: number;
  consecutiveWins: number;
  consecutiveLosses: number;
  monthlyPerformance: { month: string; pnl: number; trades: number; roi: number }[];
  symbolPerformance: { symbol: string; pnl: number; trades: number; winRate: number }[];
  timePerformance: { hour: number; pnl: number; trades: number }[];

  // Enhanced Bro-Robot specific metrics
  subscriptionMetrics?: {
    subscriberCount: number;
    subscriptionRevenue: number;
    averageSubscriptionDuration: number;
    churnRate: number;
    monthlyRecurringRevenue: number;
    lifetimeValue: number;
    conversionRate: number;
    subscriptionGrowthRate: number;
  };

  sellerMetrics?: {
    sellerId: string;
    sellerName: string;
    sellerRating: number;
    totalRobotsCreated: number;
    totalSubscribers: number;
    sellerExperience: number; // in months
    responseTime: number; // in hours
    supportQuality: number; // 1-5 rating
  };

  performanceAnalytics?: {
    volatility: number;
    beta: number;
    alpha: number;
    informationRatio: number;
    treynorRatio: number;
    calmarRatio: number;
    sortinoRatio: number;
    valueAtRisk95: number;
    valueAtRisk99: number;
    conditionalVaR: number;
    maxDrawdownDuration: number;
    recoveryFactor: number;
    ulcerIndex: number;
    gainToPainRatio: number;
  };

  riskMetrics?: {
    riskLevel: 'low' | 'medium' | 'high' | 'very_high';
    riskScore: number; // 0-100
    volatilityRank: number;
    drawdownRisk: number;
    concentrationRisk: number;
    liquidityRisk: number;
    marketRisk: number;
  };

  tradingPatterns?: {
    bestTradingHours: { hour: number; performance: number }[];
    bestTradingDays: { day: string; performance: number }[];
    seasonalPerformance: { season: string; performance: number }[];
    marketConditionPerformance: { condition: string; performance: number }[];
    averageTradeFrequency: number;
    tradingConsistency: number;
  };
}

export interface ROIAnalysis {
  totalInvestment: number;
  totalReturns: number;
  totalROI: number;
  annualizedROI: number;
  monthlyROI: { month: string; roi: number; investment: number; returns: number }[];
  quarterlyROI: { quarter: string; roi: number; investment: number; returns: number }[];
  yearlyROI: { year: string; roi: number; investment: number; returns: number }[];
  roiByRobot: { robotId: string; robotName: string; roi: number; investment: number }[];
  roiBySymbol: { symbol: string; roi: number; investment: number; trades: number }[];
  compoundGrowthRate: number;
  riskAdjustedReturn: number;
  // Additional metrics for enhanced analysis
  volatility?: number;
  sharpeRatio?: number;
  maxDrawdown?: number;
  calmarRatio?: number;
  sortinoRatio?: number;
  informationRatio?: number;
  treynorRatio?: number;
  jensenAlpha?: number;
  beta?: number;
  // Benchmark comparisons
  benchmarkComparison?: {
    benchmarkName: string;
    benchmarkROI: number;
    outperformance: number;
    correlation: number;
  }[];
  // Risk metrics
  valueAtRisk95?: number;
  valueAtRisk99?: number;
  conditionalVaR?: number;
  ulcerIndex?: number;
  // Performance attribution
  performanceAttribution?: {
    assetAllocation: number;
    stockSelection: number;
    interaction: number;
    total: number;
  };
}

export interface EnhancedStatsData extends StatsData {
  // ROI Analysis
  roiAnalysis: ROIAnalysis;

  // Robot-Specific Data
  soloRobotStats: RobotStats | null;
  broRobotStats: RobotStats[];
  robotComparison: {
    robotId: string;
    robotName: string;
    metrics: {
      roi: number;
      winRate: number;
      profitFactor: number;
      sharpeRatio: number;
      maxDrawdown: number;
    };
  }[];

  // Enhanced Symbol Analysis
  enhancedSymbolAnalysis: EnhancedSymbolAnalysisData;
  
  // Advanced Performance Metrics
  volatility: number;
  informationRatio: number;
  treynorRatio: number;
  jensenAlpha: number;
  beta: number;
  
  // Risk Metrics
  valueAtRisk95: number;
  valueAtRisk99: number;
  conditionalVaR: number;
  maximumDrawdownDuration: number;
  recoveryFactor: number;
  ulcerIndex: number;
  
  // Trading Patterns
  tradingPatterns: {
    bestTradingHours: number[];
    bestTradingDays: string[];
    seasonalPerformance: { season: string; performance: number }[];
    marketConditionPerformance: { condition: string; performance: number }[];
  };
  
  // Real-time Data
  liveUpdates: boolean;
  lastUpdateTime: string;
  realtimeMetrics: {
    currentPnl: number;
    todaysPnl: number;
    openPositions: number;
    activeRobots: number;
  };
}

export interface EnhancedFilterOptions {
  robotType: 'all' | 'solo' | 'bro';
  robotId?: string;
  dateRange: 'all' | '7d' | '30d' | '90d' | '6m' | '1y' | '2y';
  symbol?: string;
  minTradeSize?: number;
  maxTradeSize?: number;
  profitOnly?: boolean;
  lossOnly?: boolean;
  timeRange?: { start: string; end: string };
  // ROI-specific filters
  minROI?: number;
  maxROI?: number;
  roiRange?: 'positive' | 'negative' | 'all';
  performanceLevel?: 'excellent' | 'good' | 'average' | 'poor' | 'loss' | 'all';
  volatilityLevel?: 'low' | 'medium' | 'high' | 'all';
}

export const useEnhancedStatistics = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<EnhancedStatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<EnhancedFilterOptions>({
    robotType: 'all',
    dateRange: 'all',
    roiRange: 'all',
    performanceLevel: 'all',
    volatilityLevel: 'all'
  });

  // Initialize enhanced symbol analysis hook
  const { calculateEnhancedSymbolAnalysis } = useEnhancedSymbolAnalysis([]);

  // Authentication guard - early return if no user
  if (!user) {
    return {
      stats: null,
      loading: false,
      error: 'Kullanıcı oturumu bulunamadı',
      filters,
      updateFilters: () => {},
      refetch: () => {},
      realTime: {
        isConnected: false,
        lastUpdate: null,
        updateCount: 0,
        refreshStats: () => {}
      }
    };
  }

  // Real-time updates integration
  const realTimeStats = useRealTimeStatistics({
    stats,
    onStatsUpdate: setStats,
    enabled: true,
    updateInterval: 30000 // 30 seconds
  });

  // Calculate ROI Analysis with Enhanced Metrics
  const calculateROIAnalysis = useCallback((trades: Trade[], robotNames: { [key: string]: string } = {}): ROIAnalysis => {
    const closedTrades = trades.filter(t => t.position_status === 'Kapalı' && t.pnl !== null);

    if (closedTrades.length === 0) {
      return {
        totalInvestment: 0,
        totalReturns: 0,
        totalROI: 0,
        annualizedROI: 0,
        monthlyROI: [],
        quarterlyROI: [],
        yearlyROI: [],
        roiByRobot: [],
        roiBySymbol: [],
        compoundGrowthRate: 0,
        riskAdjustedReturn: 0
      };
    }

    // Sort trades by date for proper calculations
    const sortedTrades = closedTrades.sort((a, b) =>
      new Date(a.pnl_calculated_at!).getTime() - new Date(b.pnl_calculated_at!).getTime()
    );

    const totalInvestment = sortedTrades.reduce((acc, trade) => {
      return acc + ((trade.calculated_quantity || 0) * (trade.price || 0));
    }, 0);

    const totalReturns = sortedTrades.reduce((acc, trade) => acc + trade.pnl!, 0);
    const totalROI = totalInvestment > 0 ? (totalReturns / totalInvestment) * 100 : 0;

    // Enhanced annualized ROI calculation
    const firstTradeDate = new Date(sortedTrades[0].pnl_calculated_at!);
    const lastTradeDate = new Date(sortedTrades[sortedTrades.length - 1].pnl_calculated_at!);
    const daysDiff = Math.max(1, (lastTradeDate.getTime() - firstTradeDate.getTime()) / (1000 * 60 * 60 * 24));
    const yearsFraction = daysDiff / 365.25;

    // Calculate compound annual growth rate (CAGR)
    const compoundGrowthRate = yearsFraction > 0 && totalInvestment > 0
      ? (Math.pow((totalInvestment + totalReturns) / totalInvestment, 1 / yearsFraction) - 1) * 100
      : 0;

    const annualizedROI = yearsFraction > 0 ? totalROI / yearsFraction : totalROI;

    // Calculate volatility for risk-adjusted return
    const dailyReturns = calculateDailyReturns(sortedTrades);
    const volatility = calculateVolatility(dailyReturns);
    const sharpeRatio = volatility > 0 ? (annualizedROI - 2) / volatility : 0; // Assuming 2% risk-free rate
    const riskAdjustedReturn = sharpeRatio * Math.sqrt(252); // Annualized Sharpe ratio

    // Monthly ROI calculation with enhanced data
    const monthlyData = calculatePeriodROI(sortedTrades, 'month');
    const monthlyROI = Object.entries(monthlyData).map(([period, data]) => ({
      month: period,
      roi: data.investment > 0 ? (data.returns / data.investment) * 100 : 0,
      investment: data.investment,
      returns: data.returns
    })).sort((a, b) => a.month.localeCompare(b.month));

    // Quarterly ROI calculation
    const quarterlyData = calculatePeriodROI(sortedTrades, 'quarter');
    const quarterlyROI = Object.entries(quarterlyData).map(([period, data]) => ({
      quarter: period,
      roi: data.investment > 0 ? (data.returns / data.investment) * 100 : 0,
      investment: data.investment,
      returns: data.returns
    })).sort((a, b) => a.quarter.localeCompare(b.quarter));

    // Yearly ROI calculation
    const yearlyData = calculatePeriodROI(sortedTrades, 'year');
    const yearlyROI = Object.entries(yearlyData).map(([period, data]) => ({
      year: period,
      roi: data.investment > 0 ? (data.returns / data.investment) * 100 : 0,
      investment: data.investment,
      returns: data.returns
    })).sort((a, b) => a.year.localeCompare(b.year));

    // ROI by Robot with enhanced metrics
    const robotData: { [key: string]: { investment: number; returns: number; name: string; trades: number } } = {};
    sortedTrades.forEach(trade => {
      const robotId = trade.robot_id || 'solo';
      const robotName = trade.robot_id
        ? (robotNames[trade.robot_id] || `Robot ${trade.robot_id.slice(0, 8)}`)
        : 'Solo-Robot';
      if (!robotData[robotId]) {
        robotData[robotId] = { investment: 0, returns: 0, name: robotName, trades: 0 };
      }
      robotData[robotId].investment += (trade.calculated_quantity || 0) * (trade.price || 0);
      robotData[robotId].returns += trade.pnl!;
      robotData[robotId].trades += 1;
    });

    const roiByRobot = Object.entries(robotData).map(([robotId, data]) => ({
      robotId,
      robotName: data.name,
      roi: data.investment > 0 ? (data.returns / data.investment) * 100 : 0,
      investment: data.investment
    })).sort((a, b) => b.roi - a.roi);

    // ROI by Symbol calculation
    const symbolData: { [key: string]: { investment: number; returns: number; trades: number } } = {};
    sortedTrades.forEach(trade => {
      const symbol = trade.symbol || 'Unknown';
      if (!symbolData[symbol]) {
        symbolData[symbol] = { investment: 0, returns: 0, trades: 0 };
      }
      symbolData[symbol].investment += (trade.calculated_quantity || 0) * (trade.price || 0);
      symbolData[symbol].returns += trade.pnl!;
      symbolData[symbol].trades += 1;
    });

    const roiBySymbol = Object.entries(symbolData).map(([symbol, data]) => ({
      symbol,
      roi: data.investment > 0 ? (data.returns / data.investment) * 100 : 0,
      investment: data.investment,
      trades: data.trades
    })).sort((a, b) => b.roi - a.roi);

    return {
      totalInvestment,
      totalReturns,
      totalROI,
      annualizedROI,
      monthlyROI,
      quarterlyROI,
      yearlyROI,
      roiByRobot,
      roiBySymbol,
      compoundGrowthRate,
      riskAdjustedReturn
    };
  }, []);

  // Helper function to calculate daily returns for volatility
  const calculateDailyReturns = useCallback((trades: Trade[]): number[] => {
    const dailyPnL: { [key: string]: number } = {};

    trades.forEach(trade => {
      const date = new Date(trade.pnl_calculated_at!).toISOString().slice(0, 10);
      if (!dailyPnL[date]) {
        dailyPnL[date] = 0;
      }
      dailyPnL[date] += trade.pnl!;
    });

    const sortedDates = Object.keys(dailyPnL).sort();
    const returns: number[] = [];

    for (let i = 1; i < sortedDates.length; i++) {
      const prevPnL = dailyPnL[sortedDates[i - 1]];
      const currentPnL = dailyPnL[sortedDates[i]];
      if (prevPnL !== 0) {
        returns.push((currentPnL - prevPnL) / Math.abs(prevPnL));
      }
    }

    return returns;
  }, []);

  // Helper function to calculate volatility
  const calculateVolatility = useCallback((returns: number[]): number => {
    if (returns.length < 2) return 0;

    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / (returns.length - 1);

    return Math.sqrt(variance) * 100; // Convert to percentage
  }, []);

  // Helper function to calculate ROI by period (month, quarter, year)
  const calculatePeriodROI = useCallback((trades: Trade[], periodType: 'month' | 'quarter' | 'year'): { [key: string]: { investment: number; returns: number } } => {
    const periodData: { [key: string]: { investment: number; returns: number } } = {};

    trades.forEach(trade => {
      const date = new Date(trade.pnl_calculated_at!);
      let period: string;

      switch (periodType) {
        case 'month':
          period = date.toISOString().slice(0, 7); // YYYY-MM
          break;
        case 'quarter':
          const quarter = Math.floor(date.getMonth() / 3) + 1;
          period = `${date.getFullYear()}-Q${quarter}`;
          break;
        case 'year':
          period = date.getFullYear().toString();
          break;
        default:
          period = date.toISOString().slice(0, 7);
      }

      if (!periodData[period]) {
        periodData[period] = { investment: 0, returns: 0 };
      }
      periodData[period].investment += (trade.calculated_quantity || 0) * (trade.price || 0);
      periodData[period].returns += trade.pnl!;
    });

    return periodData;
  }, []);

  // Helper function to apply ROI-specific filters
  const applyROIFilters = useCallback((roiAnalysis: ROIAnalysis, filters: EnhancedFilterOptions): ROIAnalysis => {
    let filteredRoiAnalysis = { ...roiAnalysis };

    // Filter robots by ROI range
    if (filters.minROI !== undefined || filters.maxROI !== undefined) {
      filteredRoiAnalysis.roiByRobot = roiAnalysis.roiByRobot.filter(robot => {
        if (filters.minROI !== undefined && robot.roi < filters.minROI) return false;
        if (filters.maxROI !== undefined && robot.roi > filters.maxROI) return false;
        return true;
      });
    }

    // Filter by performance level
    if (filters.performanceLevel && filters.performanceLevel !== 'all') {
      const performanceRanges = {
        excellent: { min: 20, max: Infinity },
        good: { min: 10, max: 20 },
        average: { min: 5, max: 10 },
        poor: { min: 0, max: 5 },
        loss: { min: -Infinity, max: 0 }
      };

      const range = performanceRanges[filters.performanceLevel];
      if (range) {
        filteredRoiAnalysis.roiByRobot = filteredRoiAnalysis.roiByRobot.filter(robot =>
          robot.roi >= range.min && robot.roi < range.max
        );
      }
    }

    // Filter symbols by ROI if applicable
    if (filteredRoiAnalysis.roiBySymbol && (filters.minROI !== undefined || filters.maxROI !== undefined)) {
      filteredRoiAnalysis.roiBySymbol = filteredRoiAnalysis.roiBySymbol.filter(symbol => {
        if (filters.minROI !== undefined && symbol.roi < filters.minROI) return false;
        if (filters.maxROI !== undefined && symbol.roi > filters.maxROI) return false;
        return true;
      });
    }

    return filteredRoiAnalysis;
  }, []);

  // Helper function to calculate maximum drawdown
  const calculateMaxDrawdown = useCallback((trades: Trade[]): number => {
    if (trades.length === 0) return 0;

    // Sort trades by date
    const sortedTrades = [...trades].sort((a, b) =>
      new Date(a.created_at!).getTime() - new Date(b.created_at!).getTime()
    );

    let runningPnL = 0;
    let peak = 0;
    let maxDrawdown = 0;

    sortedTrades.forEach(trade => {
      runningPnL += trade.pnl || 0;

      // Update peak if we have a new high
      if (runningPnL > peak) {
        peak = runningPnL;
      }

      // Calculate current drawdown
      const currentDrawdown = peak - runningPnL;
      if (currentDrawdown > maxDrawdown) {
        maxDrawdown = currentDrawdown;
      }
    });

    // Return as percentage of peak
    return peak > 0 ? (maxDrawdown / peak) * 100 : 0;
  }, []);

  // Enhanced Bro-Robot metrics calculation functions
  const calculateSubscriptionMetrics = useCallback((_robotId: string) => {
    // Mock subscription data - in real implementation, this would come from API
    const mockSubscriptionData = {
      subscriberCount: Math.floor(Math.random() * 500) + 50,
      subscriptionRevenue: Math.floor(Math.random() * 50000) + 10000,
      averageSubscriptionDuration: Math.floor(Math.random() * 12) + 3, // months
      churnRate: Math.random() * 0.15 + 0.05, // 5-20%
      monthlyRecurringRevenue: Math.floor(Math.random() * 10000) + 2000,
      lifetimeValue: Math.floor(Math.random() * 5000) + 1000,
      conversionRate: Math.random() * 0.1 + 0.02, // 2-12%
      subscriptionGrowthRate: Math.random() * 0.3 + 0.05 // 5-35%
    };

    return mockSubscriptionData;
  }, []);

  const calculateSellerMetrics = useCallback((robotId: string) => {
    // Mock seller data - in real implementation, this would come from API
    const mockSellerData = {
      sellerId: `seller_${robotId}`,
      sellerName: `Seller ${robotId.slice(-4)}`,
      sellerRating: Math.random() * 2 + 3, // 3-5 rating
      totalRobotsCreated: Math.floor(Math.random() * 10) + 1,
      totalSubscribers: Math.floor(Math.random() * 1000) + 100,
      sellerExperience: Math.floor(Math.random() * 36) + 6, // 6-42 months
      responseTime: Math.random() * 12 + 1, // 1-13 hours
      supportQuality: Math.random() * 2 + 3 // 3-5 rating
    };

    return mockSellerData;
  }, []);

  const calculatePerformanceAnalytics = useCallback((trades: Trade[]) => {
    if (!trades.length) return undefined;

    const returns = trades.map(t => t.pnl || 0);
    const positiveReturns = returns.filter(r => r > 0);
    const negativeReturns = returns.filter(r => r < 0);

    // Calculate various performance metrics
    const volatility = calculateVolatility(returns);
    const meanReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;

    // Risk-free rate assumption (Turkish government bonds ~15%)
    const riskFreeRate = 0.15;

    const sharpeRatio = volatility > 0 ? (meanReturn - riskFreeRate) / volatility : 0;
    const sortinoRatio = negativeReturns.length > 0 ?
      meanReturn / Math.sqrt(negativeReturns.reduce((sum, r) => sum + r * r, 0) / negativeReturns.length) : 0;

    // Value at Risk calculations
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const var95Index = Math.floor(returns.length * 0.05);
    const var99Index = Math.floor(returns.length * 0.01);

    return {
      volatility,
      beta: Math.random() * 1.5 + 0.5, // Mock beta
      alpha: meanReturn - riskFreeRate,
      informationRatio: sharpeRatio,
      treynorRatio: sharpeRatio, // Simplified
      calmarRatio: meanReturn / Math.abs(Math.min(...returns)),
      sortinoRatio,
      valueAtRisk95: sortedReturns[var95Index] || 0,
      valueAtRisk99: sortedReturns[var99Index] || 0,
      conditionalVaR: var95Index > 0 ? sortedReturns.slice(0, var95Index).reduce((sum, r) => sum + r, 0) / var95Index : 0,
      maxDrawdownDuration: Math.floor(Math.random() * 30) + 1, // days
      recoveryFactor: positiveReturns.length > 0 ?
        positiveReturns.reduce((sum, r) => sum + r, 0) / Math.abs(Math.min(...returns)) : 0,
      ulcerIndex: Math.sqrt(returns.reduce((sum, r) => sum + Math.pow(Math.min(0, r), 2), 0) / returns.length),
      gainToPainRatio: positiveReturns.length > 0 && negativeReturns.length > 0 ?
        positiveReturns.reduce((sum, r) => sum + r, 0) / Math.abs(negativeReturns.reduce((sum, r) => sum + r, 0)) : 0
    };
  }, []);

  const calculateRiskMetrics = useCallback((trades: Trade[], performanceAnalytics: any) => {
    if (!trades.length || !performanceAnalytics) return undefined;

    const volatility = performanceAnalytics.volatility;
    const maxDrawdown = Math.abs(Math.min(...trades.map(t => t.pnl || 0)));

    // Risk scoring algorithm
    let riskScore = 0;

    // Volatility component (40% weight)
    if (volatility > 0.3) riskScore += 40;
    else if (volatility > 0.2) riskScore += 30;
    else if (volatility > 0.1) riskScore += 20;
    else riskScore += 10;

    // Drawdown component (30% weight)
    if (maxDrawdown > 0.2) riskScore += 30;
    else if (maxDrawdown > 0.15) riskScore += 22;
    else if (maxDrawdown > 0.1) riskScore += 15;
    else riskScore += 8;

    // Consistency component (30% weight)
    const winRate = trades.filter(t => (t.pnl || 0) > 0).length / trades.length;
    if (winRate < 0.3) riskScore += 30;
    else if (winRate < 0.4) riskScore += 22;
    else if (winRate < 0.5) riskScore += 15;
    else riskScore += 8;

    let riskLevel: 'low' | 'medium' | 'high' | 'very_high';
    if (riskScore <= 30) riskLevel = 'low';
    else if (riskScore <= 50) riskLevel = 'medium';
    else if (riskScore <= 75) riskLevel = 'high';
    else riskLevel = 'very_high';

    return {
      riskLevel,
      riskScore,
      volatilityRank: Math.floor((volatility / 0.5) * 100),
      drawdownRisk: Math.floor((maxDrawdown / 0.3) * 100),
      concentrationRisk: Math.floor(Math.random() * 100), // Mock
      liquidityRisk: Math.floor(Math.random() * 100), // Mock
      marketRisk: Math.floor(Math.random() * 100) // Mock
    };
  }, []);

  const calculateTradingPatterns = useCallback((trades: Trade[]) => {
    if (!trades.length) return undefined;

    // Hour-based performance
    const hourlyPerformance = Array.from({ length: 24 }, (_, hour) => {
      const hourTrades = trades.filter(t => {
        if (!t.received_at) return false;
        const tradeHour = new Date(t.received_at).getHours();
        return tradeHour === hour;
      });
      const performance = hourTrades.reduce((sum, t) => sum + (t.pnl || 0), 0);
      return { hour, performance };
    }).filter(h => h.performance !== 0).slice(0, 5);

    // Day-based performance
    const dayNames = ['Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma'];
    const dailyPerformance = dayNames.map(day => ({
      day,
      performance: Math.random() * 1000 - 500 // Mock data
    })).slice(0, 3);

    // Seasonal performance
    const seasonalPerformance = [
      { season: 'İlkbahar', performance: Math.random() * 1000 - 500 },
      { season: 'Yaz', performance: Math.random() * 1000 - 500 },
      { season: 'Sonbahar', performance: Math.random() * 1000 - 500 },
      { season: 'Kış', performance: Math.random() * 1000 - 500 }
    ];

    // Market condition performance
    const marketConditionPerformance = [
      { condition: 'Boğa Piyasası', performance: Math.random() * 1000 + 200 },
      { condition: 'Ayı Piyasası', performance: Math.random() * 500 - 300 },
      { condition: 'Yatay Piyasa', performance: Math.random() * 400 - 200 }
    ];

    return {
      bestTradingHours: hourlyPerformance,
      bestTradingDays: dailyPerformance,
      seasonalPerformance,
      marketConditionPerformance,
      averageTradeFrequency: trades.length / 30, // trades per day
      tradingConsistency: Math.random() * 100 // Mock consistency score
    };
  }, []);

  // Calculate Robot-Specific Statistics
  const calculateRobotStats = useCallback((trades: Trade[], robotId: string, robotType: 'solo' | 'bro', robotNames: { [key: string]: string } = {}): RobotStats => {
    const robotTrades = trades.filter(t =>
      t.position_status === 'Kapalı' &&
      t.pnl !== null &&
      (robotId === 'solo' ? !t.robot_id : t.robot_id === robotId)
    );

    const totalTrades = robotTrades.length;
    const winningTrades = robotTrades.filter(t => t.pnl! > 0).length;
    const losingTrades = totalTrades - winningTrades;
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;

    const totalPnl = robotTrades.reduce((acc, t) => acc + t.pnl!, 0);
    const totalInvestment = robotTrades.reduce((acc, t) => acc + ((t.calculated_quantity || 0) * (t.price || 0)), 0);
    const roi = totalInvestment > 0 ? (totalPnl / totalInvestment) * 100 : 0;

    const grossProfit = robotTrades.filter(t => t.pnl! > 0).reduce((acc, t) => acc + t.pnl!, 0);
    const grossLoss = Math.abs(robotTrades.filter(t => t.pnl! <= 0).reduce((acc, t) => acc + t.pnl!, 0));
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0;

    // Get actual robot name or fallback to default
    const robotName = robotType === 'solo'
      ? 'Solo-Robot'
      : (robotNames[robotId] || `Robot ${robotId.slice(0, 8)}`);

    // Calculate risk metrics
    const maxDrawdown = calculateMaxDrawdown(robotTrades);
    const dailyReturns = calculateDailyReturns(robotTrades);
    const volatility = calculateVolatility(dailyReturns);

    // Calculate Sharpe ratio (assuming 2% risk-free rate)
    const riskFreeRate = 2; // 2% annual risk-free rate
    const excessReturn = roi - riskFreeRate;
    const sharpeRatio = volatility > 0 ? excessReturn / volatility : 0;

    // Calculate enhanced metrics for Bro-Robots
    const performanceAnalytics = robotType === 'bro' ? calculatePerformanceAnalytics(robotTrades) : undefined;
    const riskMetrics = robotType === 'bro' ? calculateRiskMetrics(robotTrades, performanceAnalytics) : undefined;
    const tradingPatterns = robotType === 'bro' ? calculateTradingPatterns(robotTrades) : undefined;
    const subscriptionMetrics = robotType === 'bro' ? calculateSubscriptionMetrics(robotId) : undefined;
    const sellerMetrics = robotType === 'bro' ? calculateSellerMetrics(robotId) : undefined;

    return {
      robotId,
      robotName,
      robotType,
      totalTrades,
      winningTrades,
      losingTrades,
      winRate,
      totalPnl,
      totalInvestment,
      roi,
      annualizedROI: roi, // Simplified for now
      profitFactor,
      maxDrawdown,
      sharpeRatio,
      averageTradeSize: totalInvestment / Math.max(1, totalTrades),
      averageHoldingTime: 0, // Will be calculated
      bestTrade: robotTrades.length > 0 ? Math.max(...robotTrades.map(t => t.pnl!)) : 0,
      worstTrade: robotTrades.length > 0 ? Math.min(...robotTrades.map(t => t.pnl!)) : 0,
      consecutiveWins: 0, // Will be calculated
      consecutiveLosses: 0, // Will be calculated
      monthlyPerformance: [], // Will be calculated
      symbolPerformance: [], // Will be calculated
      timePerformance: [], // Will be calculated

      // Enhanced Bro-Robot specific metrics
      ...(robotType === 'bro' && {
        subscriptionMetrics,
        sellerMetrics,
        performanceAnalytics,
        riskMetrics,
        tradingPatterns
      })
    };
  }, [calculateMaxDrawdown]);

  // Calculate hourly performance data
  const calculateHourlyPerformance = useCallback((trades: Trade[]) => {
    const hourlyData: { [hour: number]: { pnl: number; trades: number; wins: number } } = {};

    // Initialize all 24 hours
    for (let hour = 0; hour < 24; hour++) {
      hourlyData[hour] = { pnl: 0, trades: 0, wins: 0 };
    }

    trades.forEach(trade => {
      // Use received_at as the time field since entry_time doesn't exist in Trade interface
      if (trade.received_at && trade.pnl !== null && trade.pnl !== undefined) {
        const hour = new Date(trade.received_at).getHours();
        hourlyData[hour].pnl += trade.pnl;
        hourlyData[hour].trades += 1;
        if (trade.pnl > 0) hourlyData[hour].wins += 1;
      }
    });

    return Object.entries(hourlyData).map(([hour, data]) => ({
      hour: parseInt(hour),
      pnl: data.pnl,
      trades: data.trades,
      winRate: data.trades > 0 ? (data.wins / data.trades) * 100 : 0
    }));
  }, []);

  // Calculate daily performance data
  const calculateDailyPerformance = useCallback((trades: Trade[]) => {
    const dailyData: { [date: string]: { pnl: number; trades: number; wins: number } } = {};

    trades.forEach(trade => {
      // Use received_at as the time field since entry_time doesn't exist in Trade interface
      if (trade.received_at && trade.pnl !== null && trade.pnl !== undefined) {
        const date = new Date(trade.received_at).toISOString().split('T')[0];
        if (!dailyData[date]) {
          dailyData[date] = { pnl: 0, trades: 0, wins: 0 };
        }
        dailyData[date].pnl += trade.pnl;
        dailyData[date].trades += 1;
        if (trade.pnl > 0) dailyData[date].wins += 1;
      }
    });

    return Object.entries(dailyData)
      .map(([date, data]) => ({
        day: date,
        pnl: data.pnl,
        trades: data.trades
      }))
      .sort((a, b) => new Date(a.day).getTime() - new Date(b.day).getTime());
  }, []);

  // Calculate weekly performance data
  const calculateWeeklyPerformance = useCallback((trades: Trade[]) => {
    const weeklyData: { [dayOfWeek: number]: { pnl: number; trades: number; wins: number } } = {};

    // Initialize all 7 days (0 = Sunday, 1 = Monday, etc.)
    for (let day = 0; day < 7; day++) {
      weeklyData[day] = { pnl: 0, trades: 0, wins: 0 };
    }

    trades.forEach(trade => {
      // Use received_at as the time field since entry_time doesn't exist in Trade interface
      if (trade.received_at && trade.pnl !== null && trade.pnl !== undefined) {
        const dayOfWeek = new Date(trade.received_at).getDay();
        weeklyData[dayOfWeek].pnl += trade.pnl;
        weeklyData[dayOfWeek].trades += 1;
        if (trade.pnl > 0) weeklyData[dayOfWeek].wins += 1;
      }
    });

    const weekNames = ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7'];
    return Object.entries(weeklyData).map(([dayOfWeek, data]) => ({
      week: weekNames[parseInt(dayOfWeek)],
      pnl: data.pnl,
      trades: data.trades
    }));
  }, []);

  // Calculate monthly performance data
  const calculateMonthlyPerformance = useCallback((trades: Trade[]) => {
    const monthlyData: { [month: string]: { pnl: number; trades: number; wins: number } } = {};

    trades.forEach(trade => {
      // Use received_at as the time field since entry_time doesn't exist in Trade interface
      if (trade.received_at && trade.pnl !== null && trade.pnl !== undefined) {
        const date = new Date(trade.received_at);
        const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        if (!monthlyData[month]) {
          monthlyData[month] = { pnl: 0, trades: 0, wins: 0 };
        }
        monthlyData[month].pnl += trade.pnl;
        monthlyData[month].trades += 1;
        if (trade.pnl > 0) monthlyData[month].wins += 1;
      }
    });

    return Object.entries(monthlyData)
      .map(([month, data]) => ({
        month,
        pnl: data.pnl,
        trades: data.trades,
        winRate: data.trades > 0 ? (data.wins / data.trades) * 100 : 0
      }))
      .sort((a, b) => new Date(a.month).getTime() - new Date(b.month).getTime());
  }, []);

  // Calculate cumulative P&L data for line chart
  const calculateCumulativePnlData = useCallback((trades: Trade[]) => {
    const closedTrades = trades
      .filter(t => t.position_status === 'Kapalı' && t.pnl !== null && t.pnl_calculated_at)
      .sort((a, b) => new Date(a.pnl_calculated_at!).getTime() - new Date(b.pnl_calculated_at!).getTime());

    if (closedTrades.length === 0) {
      return [];
    }

    let cumulativePnl = 0;
    const cumulativePnlData: { date: string; value: number; trades: number }[] = [];

    closedTrades.forEach((trade, index) => {
      cumulativePnl += trade.pnl!;
      cumulativePnlData.push({
        date: trade.pnl_calculated_at!,
        value: cumulativePnl,
        trades: index + 1
      });
    });

    return cumulativePnlData;
  }, []);

  // Fetch robot names for user's subscriptions
  const fetchRobotNames = useCallback(async (): Promise<{ [key: string]: string }> => {
    try {
      // Get user's active robot subscriptions with robot details
      const { data: subscriptions, error } = await supabase
        .rpc('fetch_user_subscriptions_detailed', { p_user_id: user.id });

      if (error) {
        console.error('Error fetching robot names:', error);
        return {};
      }

      const robotNames: { [key: string]: string } = {};
      subscriptions?.forEach((sub: any) => {
        if (sub.robot_id && sub.robot_name && sub.is_active) {
          robotNames[sub.robot_id] = sub.robot_name;
        }
      });

      console.log(`Fetched ${Object.keys(robotNames).length} active robot subscriptions:`, robotNames);
      return robotNames;
    } catch (err) {
      console.error('Error in fetchRobotNames:', err);
      return {};
    }
  }, [user.id]);

  // Validate data consistency with management page
  const validateDataConsistency = useCallback((robotNames: { [key: string]: string }, trades: any[]) => {
    const robotIdsInTrades = [...new Set(trades.map(t => t.robot_id).filter(Boolean))];
    const subscribedRobotIds = Object.keys(robotNames);

    // Check for trades from unsubscribed robots
    const unsubscribedRobotTrades = robotIdsInTrades.filter(robotId => !subscribedRobotIds.includes(robotId));

    if (unsubscribedRobotTrades.length > 0) {
      console.warn('Found trades from unsubscribed robots:', unsubscribedRobotTrades);
      console.warn('This might indicate data inconsistency or expired subscriptions');
    }

    console.log('Data consistency check:', {
      subscribedRobots: subscribedRobotIds.length,
      robotsWithTrades: robotIdsInTrades.length,
      unsubscribedRobotTrades: unsubscribedRobotTrades.length
    });

    return {
      isConsistent: unsubscribedRobotTrades.length === 0,
      unsubscribedRobots: unsubscribedRobotTrades
    };
  }, []);

  // Main data fetching and calculation
  const fetchAndCalculateStats = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch robot names first with error handling
      let robotNames: { [key: string]: string } = {};
      try {
        robotNames = await fetchRobotNames();
      } catch (robotError) {
        console.warn('Failed to fetch robot names, using fallback names:', robotError);
        // Continue with empty robot names - will use fallback names
      }

      let query = supabase
        .from('trades')
        .select('*')
        .eq('user_id', user.id);

      // Apply filters
      if (filters.robotType === 'solo') {
        query = query.is('robot_id', null);
      } else if (filters.robotType === 'bro') {
        query = query.not('robot_id', 'is', null);
      }

      if (filters.robotId) {
        query = query.eq('robot_id', filters.robotId);
      }

      if (filters.symbol) {
        query = query.eq('symbol', filters.symbol);
      }

      if (filters.dateRange !== 'all') {
        const days = filters.dateRange === '7d' ? 7 :
                    filters.dateRange === '30d' ? 30 :
                    filters.dateRange === '90d' ? 90 :
                    filters.dateRange === '6m' ? 180 :
                    filters.dateRange === '1y' ? 365 : 730;
        const fromDate = new Date();
        fromDate.setDate(fromDate.getDate() - days);
        query = query.gte('pnl_calculated_at', fromDate.toISOString());
      }

      // Apply profit/loss filters
      if (filters.profitOnly) {
        query = query.gt('pnl', 0);
      } else if (filters.lossOnly) {
        query = query.lt('pnl', 0);
      }

      // Apply trade size filters
      if (filters.minTradeSize) {
        query = query.gte('calculated_quantity', filters.minTradeSize);
      }
      if (filters.maxTradeSize) {
        query = query.lte('calculated_quantity', filters.maxTradeSize);
      }

      const { data: trades, error } = await query;

      if (error) {
        // Check for authentication/authorization errors
        if (error.code === 'PGRST301' || error.message.includes('JWT')) {
          setError('Oturum süresi dolmuş. Lütfen tekrar giriş yapın.');
        } else if (error.code === 'PGRST116') {
          setError('Yetkisiz erişim. Bu verileri görme yetkiniz bulunmuyor.');
        } else {
          setError(`Veri yüklenirken hata: ${error.message}`);
        }
        return;
      }

      // Additional security check - ensure all trades belong to the current user
      if (trades && trades.length > 0) {
        const unauthorizedTrades = trades.filter(trade => trade.user_id !== user.id);
        if (unauthorizedTrades.length > 0) {
          console.error('Security violation: Unauthorized trades detected', unauthorizedTrades);
          setError('Güvenlik hatası: Yetkisiz veri tespit edildi.');
          return;
        }
      }

      // Filter trades to only include subscribed robots (for bro-robot trades)
      // Solo trades (robot_id = null) are always included
      const filteredTrades = (trades || []).filter(trade => {
        // Include solo trades
        if (!trade.robot_id) return true;

        // Include bro-robot trades only if user is subscribed to that robot
        return robotNames[trade.robot_id] !== undefined;
      });

      // Validate data consistency


      console.log(`Filtered ${trades?.length || 0} trades to ${filteredTrades.length} (subscribed robots only)`);

      // Use filtered trades for all calculations
      let tradesForCalculation = filteredTrades;

      // Apply ROI-specific filters (post-processing)
      if (filters.roiRange === 'positive') {
        tradesForCalculation = tradesForCalculation.filter(trade => (trade.pnl || 0) > 0);
      } else if (filters.roiRange === 'negative') {
        tradesForCalculation = tradesForCalculation.filter(trade => (trade.pnl || 0) < 0);
      }

      // Calculate enhanced statistics using filtered trades
      let roiAnalysis = calculateROIAnalysis(tradesForCalculation, robotNames);

      // Apply ROI-based filters to robot data
      if (filters.minROI !== undefined || filters.maxROI !== undefined || filters.performanceLevel !== 'all') {
        roiAnalysis = applyROIFilters(roiAnalysis, filters);
      }

      // Get unique robot IDs from filtered trades
      const allRobotIds = [...new Set(tradesForCalculation.map(t => t.robot_id).filter(Boolean))];

      // All robot IDs from filtered trades are already subscribed robots
      const subscribedRobotIds = allRobotIds;

      const hasSoloTrades = tradesForCalculation.some(t => !t.robot_id);

      // Calculate robot-specific stats using filtered trades
      const soloRobotStats = hasSoloTrades ? calculateRobotStats(tradesForCalculation, 'solo', 'solo', robotNames) : null;
      const broRobotStats = subscribedRobotIds.map(robotId =>
        calculateRobotStats(tradesForCalculation, robotId!, 'bro', robotNames)
      );

      // Create robot comparison data
      const robotComparison = [
        ...(soloRobotStats ? [{
          robotId: 'solo',
          robotName: 'Solo-Robot',
          metrics: {
            roi: soloRobotStats.roi,
            winRate: soloRobotStats.winRate,
            profitFactor: soloRobotStats.profitFactor,
            sharpeRatio: soloRobotStats.sharpeRatio,
            maxDrawdown: soloRobotStats.maxDrawdown
          }
        }] : []),
        ...broRobotStats.map(robot => ({
          robotId: robot.robotId,
          robotName: robot.robotName,
          metrics: {
            roi: robot.roi,
            winRate: robot.winRate,
            profitFactor: robot.profitFactor,
            sharpeRatio: robot.sharpeRatio,
            maxDrawdown: robot.maxDrawdown
          }
        }))
      ];

      // Calculate basic stats using filtered trades
      const closedTrades = tradesForCalculation
        .filter(t => t.position_status === 'Kapalı' && t.pnl !== null)
        .sort((a, b) => new Date(a.pnl_calculated_at!).getTime() - new Date(b.pnl_calculated_at!).getTime());

      const totalTrades = closedTrades.length;
      const winningTrades = closedTrades.filter(t => t.pnl! > 0);
      const losingTrades = closedTrades.filter(t => t.pnl! <= 0);
      const winRate = totalTrades > 0 ? (winningTrades.length / totalTrades) * 100 : 0;
      const totalPnl = closedTrades.reduce((acc, t) => acc + t.pnl!, 0);

      // Calculate enhanced symbol analysis
      const enhancedSymbolAnalysis = calculateEnhancedSymbolAnalysis(tradesForCalculation);

      const enhancedStats: EnhancedStatsData = {
        // Basic stats (simplified for now)
        totalTrades,
        winningTrades: winningTrades.length,
        losingTrades: losingTrades.length,
        totalWins: winningTrades.length,
        totalLosses: losingTrades.length,
        grossProfit: winningTrades.reduce((acc, t) => acc + t.pnl!, 0),
        grossLoss: Math.abs(losingTrades.reduce((acc, t) => acc + t.pnl!, 0)),
        winRate,
        totalPnl,
        profitFactor: 0, // Will be calculated
        avgTradePnl: totalPnl / Math.max(1, totalTrades),
        avgWinningTrade: 0,
        avgLosingTrade: 0,
        maxDrawdown: 0,
        maxDrawdownPercent: 0,
        sharpeRatio: 0,
        sortinoRatio: 0,
        calmarRatio: 0,
        maxConsecutiveWins: 0,
        maxConsecutiveLosses: 0,
        averageWinStreak: 0,
        averageLossStreak: 0,
        largestWin: 0,
        largestLoss: 0,
        pnlBySymbol: {},
        pnlByMonth: [],
        cumulativePnlData: calculateCumulativePnlData(tradesForCalculation),
        hourlyPerformance: calculateHourlyPerformance(tradesForCalculation),
        dailyPerformance: calculateDailyPerformance(tradesForCalculation),
        weeklyPerformance: calculateWeeklyPerformance(tradesForCalculation),
        monthlyPerformance: calculateMonthlyPerformance(tradesForCalculation),
        riskMetrics: {
          valueAtRisk: 0,
          expectedShortfall: 0,
          maxDrawdownDuration: 0,
          recoveryFactor: 0,
          ulcerIndex: 0
        },
        performanceByRobot: [],

        // Enhanced features
        roiAnalysis,
        soloRobotStats,
        broRobotStats,
        robotComparison,
        volatility: calculateVolatility(calculateDailyReturns(trades)),
        informationRatio: 0,
        treynorRatio: 0,
        jensenAlpha: 0,
        beta: 0,
        valueAtRisk95: 0,
        valueAtRisk99: 0,
        conditionalVaR: 0,
        maximumDrawdownDuration: 0,
        recoveryFactor: 0,
        ulcerIndex: 0,
        tradingPatterns: {
          bestTradingHours: [],
          bestTradingDays: [],
          seasonalPerformance: [],
          marketConditionPerformance: []
        },
        liveUpdates: false,
        lastUpdateTime: new Date().toISOString(),
        realtimeMetrics: {
          currentPnl: totalPnl,
          todaysPnl: 0,
          openPositions: 0,
          activeRobots: subscribedRobotIds.length + (hasSoloTrades ? 1 : 0)
        },

        // Enhanced Symbol Analysis
        enhancedSymbolAnalysis
      };

      setStats(enhancedStats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Bilinmeyen hata');
    } finally {
      setLoading(false);
    }
  }, [user, filters, calculateROIAnalysis, calculateRobotStats, fetchRobotNames, validateDataConsistency, calculateCumulativePnlData]);

  useEffect(() => {
    fetchAndCalculateStats();
  }, [fetchAndCalculateStats]);

  return {
    stats,
    loading,
    error,
    filters,
    updateFilters: useCallback((newFilters: Partial<EnhancedFilterOptions>) => {
      setFilters(prev => ({ ...prev, ...newFilters }));
    }, []),
    refetch: fetchAndCalculateStats,
    realTime: {
      isConnected: realTimeStats.isConnected,
      lastUpdate: realTimeStats.lastUpdate,
      updateCount: realTimeStats.updateCount,
      refreshStats: realTimeStats.refreshStats
    }
  };
};
