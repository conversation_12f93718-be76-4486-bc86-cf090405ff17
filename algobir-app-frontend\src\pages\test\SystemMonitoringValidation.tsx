import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  SimpleGrid,
  Badge,
  Button,

  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Code,

} from '@chakra-ui/react';
import { useSystemMonitoring } from '../../hooks/useSystemMonitoring';
import RealTimeServiceStatus from '../../components/admin/RealTimeServiceStatus';
import RealTimeSystemMetrics from '../../components/admin/RealTimeSystemMetrics';
import RealTimeUptimeMonitor from '../../components/admin/RealTimeUptimeMonitor';

const SystemMonitoringValidation: React.FC = () => {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const bgGradient = useColorModeValue(
    'linear(to-br, gray.50, blue.50)',
    'linear(to-br, gray.900, blue.900)'
  );
  const cardBg = useColorModeValue('white', 'gray.800');


  // Test the system monitoring hook with fast refresh for validation
  const {
    data: systemData,
    loading: systemLoading,
    error: systemError,
    lastRefresh: systemLastRefresh,
    refetch: refetchSystem,
    isConnected
  } = useSystemMonitoring({
    refreshInterval: 5000, // 5 seconds for testing
    enabled: true
  });

  // Browser API validation tests
  const runBrowserAPITests = async () => {
    setIsRunningTests(true);
    const results = [];

    // Test 1: Performance API
    try {
      const perfStart = performance.now();
      await new Promise(resolve => setTimeout(resolve, 10));
      const perfEnd = performance.now();
      results.push({
        test: 'Performance API',
        status: 'PASS',
        details: `Timing precision: ${(perfEnd - perfStart).toFixed(3)}ms`,
        data: { start: perfStart, end: perfEnd, duration: perfEnd - perfStart }
      });
    } catch (error) {
      results.push({
        test: 'Performance API',
        status: 'FAIL',
        details: `Error: ${error}`,
        data: null
      });
    }

    // Test 2: Memory API
    try {
      const memory = (performance as any).memory;
      if (memory) {
        results.push({
          test: 'Memory API',
          status: 'PASS',
          details: `Heap: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB / ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
          data: {
            used: memory.usedJSHeapSize,
            total: memory.totalJSHeapSize,
            limit: memory.jsHeapSizeLimit,
            percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
          }
        });
      } else {
        results.push({
          test: 'Memory API',
          status: 'UNAVAILABLE',
          details: 'performance.memory not available in this browser',
          data: null
        });
      }
    } catch (error) {
      results.push({
        test: 'Memory API',
        status: 'FAIL',
        details: `Error: ${error}`,
        data: null
      });
    }

    // Test 3: Navigation Timing API
    try {
      const nav = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (nav) {
        results.push({
          test: 'Navigation Timing API',
          status: 'PASS',
          details: `Load time: ${(nav.loadEventEnd - nav.fetchStart).toFixed(2)}ms`,
          data: {
            navigationStart: nav.fetchStart,
            loadEventEnd: nav.loadEventEnd,
            domContentLoaded: nav.domContentLoadedEventEnd - nav.fetchStart,
            loadComplete: nav.loadEventEnd - nav.fetchStart
          }
        });
      } else {
        results.push({
          test: 'Navigation Timing API',
          status: 'UNAVAILABLE',
          details: 'Navigation timing not available',
          data: null
        });
      }
    } catch (error) {
      results.push({
        test: 'Navigation Timing API',
        status: 'FAIL',
        details: `Error: ${error}`,
        data: null
      });
    }

    // Test 4: Connection API
    try {
      const connection = (navigator as any).connection;
      if (connection) {
        results.push({
          test: 'Connection API',
          status: 'PASS',
          details: `Type: ${connection.effectiveType}, Downlink: ${connection.downlink}Mbps, RTT: ${connection.rtt}ms`,
          data: {
            effectiveType: connection.effectiveType,
            downlink: connection.downlink,
            rtt: connection.rtt,
            saveData: connection.saveData
          }
        });
      } else {
        results.push({
          test: 'Connection API',
          status: 'UNAVAILABLE',
          details: 'navigator.connection not available in this browser',
          data: null
        });
      }
    } catch (error) {
      results.push({
        test: 'Connection API',
        status: 'FAIL',
        details: `Error: ${error}`,
        data: null
      });
    }

    // Test 5: Storage Estimate API
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        results.push({
          test: 'Storage Estimate API',
          status: 'PASS',
          details: `Usage: ${((estimate.usage || 0) / 1024 / 1024).toFixed(2)}MB / ${((estimate.quota || 0) / 1024 / 1024).toFixed(2)}MB`,
          data: {
            usage: estimate.usage,
            quota: estimate.quota,
            percentage: estimate.quota ? (estimate.usage! / estimate.quota) * 100 : 0
          }
        });
      } else {
        results.push({
          test: 'Storage Estimate API',
          status: 'UNAVAILABLE',
          details: 'navigator.storage.estimate not available',
          data: null
        });
      }
    } catch (error) {
      results.push({
        test: 'Storage Estimate API',
        status: 'FAIL',
        details: `Error: ${error}`,
        data: null
      });
    }

    setTestResults(results);
    setIsRunningTests(false);
  };

  // Run tests on component mount
  useEffect(() => {
    runBrowserAPITests();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PASS': return 'green';
      case 'FAIL': return 'red';
      case 'UNAVAILABLE': return 'yellow';
      default: return 'gray';
    }
  };

  return (
    <Box
      minH="100vh"
      bgGradient={bgGradient}
      p={8}
    >
      <VStack spacing={8} align="stretch" maxW="1400px" mx="auto">
        <Box textAlign="center">
          <Heading size="lg" mb={2}>
            System Monitoring Validation
          </Heading>
          <Text color="gray.600">
            Comprehensive validation of real-time system monitoring components and browser APIs
          </Text>
        </Box>

        {/* System Monitoring Status */}
        <Card bg={cardBg} borderRadius="xl" boxShadow="lg">
          <CardHeader>
            <Heading size="md">System Monitoring Hook Status</Heading>
          </CardHeader>
          <CardBody>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
              <Stat>
                <StatLabel>Connection Status</StatLabel>
                <StatNumber>
                  <Badge colorScheme={isConnected ? 'green' : 'red'}>
                    {isConnected ? 'Connected' : 'Disconnected'}
                  </Badge>
                </StatNumber>
              </Stat>
              
              <Stat>
                <StatLabel>Loading State</StatLabel>
                <StatNumber>
                  <Badge colorScheme={systemLoading ? 'yellow' : 'green'}>
                    {systemLoading ? 'Loading' : 'Ready'}
                  </Badge>
                </StatNumber>
              </Stat>
              
              <Stat>
                <StatLabel>Error State</StatLabel>
                <StatNumber>
                  <Badge colorScheme={systemError ? 'red' : 'green'}>
                    {systemError ? 'Error' : 'OK'}
                  </Badge>
                </StatNumber>
                {systemError && <StatHelpText color="red.500">{systemError}</StatHelpText>}
              </Stat>
              
              <Stat>
                <StatLabel>Last Refresh</StatLabel>
                <StatNumber fontSize="sm">
                  {systemLastRefresh ? systemLastRefresh.toLocaleTimeString() : 'Never'}
                </StatNumber>
              </Stat>
            </SimpleGrid>

            {systemData && (
              <Box mt={6}>
                <Text fontWeight="bold" mb={2}>Data Summary:</Text>
                <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
                  <Text fontSize="sm">Services: {systemData.services.length}</Text>
                  <Text fontSize="sm">Healthy: {systemData.services.filter(s => s.status === 'healthy').length}</Text>
                  <Text fontSize="sm">Incidents: {systemData.incidents}</Text>
                  <Text fontSize="sm">Overall Health: {systemData.isHealthy ? 'Good' : 'Issues'}</Text>
                </SimpleGrid>
              </Box>
            )}
          </CardBody>
        </Card>

        {/* Browser API Tests */}
        <Card bg={cardBg} borderRadius="xl" boxShadow="lg">
          <CardHeader>
            <HStack justify="space-between">
              <Heading size="md">Browser API Validation</Heading>
              <Button 
                size="sm" 
                onClick={runBrowserAPITests} 
                isLoading={isRunningTests}
                colorScheme="blue"
              >
                Re-run Tests
              </Button>
            </HStack>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              {testResults.map((result, index) => (
                <Card key={index} variant="outline">
                  <CardBody>
                    <HStack justify="space-between" mb={2}>
                      <Text fontWeight="bold">{result.test}</Text>
                      <Badge colorScheme={getStatusColor(result.status)}>
                        {result.status}
                      </Badge>
                    </HStack>
                    <Text fontSize="sm" color="gray.600" mb={2}>
                      {result.details}
                    </Text>
                    {result.data && (
                      <Code fontSize="xs" p={2} borderRadius="md" display="block">
                        {JSON.stringify(result.data, null, 2)}
                      </Code>
                    )}
                  </CardBody>
                </Card>
              ))}
            </VStack>
          </CardBody>
        </Card>

        {/* Real-time Components */}
        {systemData && (
          <>
            <RealTimeServiceStatus
              services={systemData.services}
              loading={systemLoading}
              lastUpdate={systemLastRefresh || undefined}
              isConnected={isConnected}
            />

            <RealTimeSystemMetrics
              metrics={systemData.metrics}
              loading={systemLoading}
              lastUpdate={systemLastRefresh || undefined}
            />

            <RealTimeUptimeMonitor
              uptime={systemData.uptime}
              incidents={systemData.incidents}
              loading={systemLoading}
              lastUpdate={systemLastRefresh || undefined}
            />
          </>
        )}

        {/* Manual Refresh */}
        <Box textAlign="center">
          <Button
            onClick={refetchSystem}
            size="lg"
            colorScheme="blue"
            isLoading={systemLoading}
          >
            Manual Refresh
          </Button>
        </Box>
      </VStack>
    </Box>
  );
};

export default SystemMonitoringValidation;
