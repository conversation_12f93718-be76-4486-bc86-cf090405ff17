import { Box, useColorModeValue, VisuallyHidden } from '@chakra-ui/react';
import { Outlet, Navigate, useLocation, useNavigate } from 'react-router-dom';
import Sidebar from './sidebar/Sidebar';
import Navbar, { NAVBAR_HEIGHT } from './navbar/NavbarAdmin';
import Footer from './Footer';
import { InitialSetupBar } from './layout/InitialSetupBar';
import NotificationBar from './layout/NotificationBar';
import { sidebarRoutes } from '../routes';
import { useAuth } from '../context/AuthContext';
import { useDashboardData } from '../hooks/useDashboardData';
import { useSidebar } from '../context/SidebarContext';
import { TRANSITIONS, PERFORMANCE } from './sidebar/animations';
import { useState } from 'react';



// Use only navbar height since we removed top offset
// This eliminates the gap below navbar completely

export default function Layout() {
  const { user, initialSetupComplete } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Get sidebar state for dynamic layout
  const { currentSidebarWidth, hasActiveSubmenu } = useSidebar();

  // Get dashboard data for API expiry information
  const { apiCredentialsExpiryDate } = useDashboardData();

  // State for notification bar dismissal
  const [isNotificationDismissed, setIsNotificationDismissed] = useState(false);

  // Enhanced responsive content calculations with dynamic sidebar width
  // Main content area needs margin for sidebar, but navbar positions itself independently
  const getContentMarginLeft = () => {
    return {
      base: '0px',
      md: '0px',        // Tablet uses drawer, no margin needed
      lg: currentSidebarWidth  // Content needs margin for sidebar
    };
  };

  const getContentWidth = () => {
    return {
      base: '100%',
      md: '100%',       // Tablet uses full width with drawer
      lg: `calc(100% - ${currentSidebarWidth}${hasActiveSubmenu ? ' - 260px' : ''})`
    };
  };

  // Dynamic sidebar width for layout positioning
  const getDynamicSidebarWidth = () => {
    return {
      base: '0px',
      md: '0px',
      lg: currentSidebarWidth
    };
  };

  // İzinsiz erişim kontrolü
  if (!user) {
    return <Navigate to="/login" />;
  }

  // Functions for getting active route
  
  // Get active route details
  const getActiveRoute = () => {
    let activeRouteName = 'Dashboard';
    
    for (let i = 0; i < sidebarRoutes.length; i++) {
      if (location.pathname === sidebarRoutes[i].path || 
          (sidebarRoutes[i].path !== '/' && location.pathname.startsWith(sidebarRoutes[i].path))) {
        return sidebarRoutes[i].name;
      }
    }
    
    return activeRouteName;
  };
  
  // Get secondary state (if route has additional properties)
  const getActiveNavbar = () => {
    let activeSecondary = false;
    
    for (let i = 0; i < sidebarRoutes.length; i++) {
      if (location.pathname === sidebarRoutes[i].path || 
          (sidebarRoutes[i].path !== '/' && location.pathname.startsWith(sidebarRoutes[i].path))) {
        return sidebarRoutes[i].secondary || false;
      }
    }
    
    return activeSecondary;
  };

  // Semantic color tokens for better theming
  const mainBg = useColorModeValue('bg-secondary', 'gray.900');
  
  return (
    <>
      {/* Skip link for keyboard navigation */}
      <VisuallyHidden>
        <a 
          href="#main-content" 
          className="skip-link"
          tabIndex={0}
          onFocus={(e) => {
            e.currentTarget.style.position = 'fixed';
            e.currentTarget.style.top = '8px';
            e.currentTarget.style.left = '8px';
            e.currentTarget.style.zIndex = '9999';
          }}
          onBlur={(e) => {
            e.currentTarget.style.position = 'absolute';
            e.currentTarget.style.top = '-40px';
            e.currentTarget.style.left = '6px';
          }}
        >
          Ana içeriğe atla
        </a>
      </VisuallyHidden>

      <Box 
        display="flex" 
        minHeight="100vh"
        bg={mainBg}
        // Ensure proper focus management
        sx={{
          '&:focus-within .sidebar-focus': {
            transform: 'translateX(0)',
          },
        }}
      >
        {/* Sidebar - Responsive positioning with seamless integration */}
        <Box
          as="nav"
          aria-label="Ana navigasyon"
          position="fixed"
          left="0"
          top="0"
          h="100vh"
          w={getDynamicSidebarWidth()}
          zIndex={1050} // Lower than navbar to prevent overlap
          transform={{
            base: 'translateX(-100%)', // Hidden on mobile by default
            md: 'translateX(-100%)',   // Hidden on tablet (uses drawer)
            lg: 'translateX(0)'        // Visible on desktop and up
          }}
          transition={TRANSITIONS.layout}
          sx={{
            // Performance optimizations
            willChange: PERFORMANCE.willChange,
            // Ensure proper layering and eliminate gaps
            isolation: 'isolate',
            // Ensure no margin/padding interference
            margin: 0,
            padding: 0,
            // Prevent any potential gaps from box-sizing
            boxSizing: 'border-box',
            '@media (prefers-reduced-motion: reduce)': {
              transition: 'none'
            }
          }}
          className="sidebar-focus"
        >
          <Sidebar routes={sidebarRoutes} />
        </Box>

        {/* Main content area - Dynamic width based on sidebar state */}
        <Box
          marginLeft={getContentMarginLeft()}
          width={getContentWidth()}
          display="flex"
          flexDirection="column"
          flex="1"
          minW="0" // Prevents flex item from overflowing
          transition={TRANSITIONS.layout}
          sx={{
            // Performance optimizations
            willChange: PERFORMANCE.willChange,
            '@media (prefers-reduced-motion: reduce)': {
              transition: 'none'
            }
          }}
          position="relative"
        >
          {/* Navigation header - Fixed positioned navbar handles its own positioning */}
          <Navbar
            brandText={getActiveRoute()}
            secondary={getActiveNavbar()}
          />

          {/* Notification Bar for API Credentials Expiry - Below Navbar */}
          <NotificationBar
            apiCredentialsExpiryDate={apiCredentialsExpiryDate}
            onConfigureClick={() => navigate('/management')}
            onDismiss={() => setIsNotificationDismissed(true)}
            isDismissed={isNotificationDismissed}
          />

          {/* Container for main content and footer */}
          <Box
            display="flex"
            flexDirection="column"
            flex="1"
            pt={NAVBAR_HEIGHT}
            overflowY="hidden"
            minH="0" // Allows flex child to shrink
          >
            {/* Main content */}
            <Box
              as="main"
              role="main"
              id="main-content" // Target for skip link
              tabIndex={-1} // Makes it focusable for skip link
              mx="auto"
              p={{ 
                base: '4',    // 16px on mobile
                sm: '5',      // 20px on small screens  
                md: '6',      // 24px on tablets
                lg: '8',      // 32px on desktop
              }}
              pb={{ 
                base: '4', 
                md: '6' 
              }}
              flex="1"
              width="100%"
              maxWidth={{ 
                base: "100%", 
                "2xl": "1600px" 
              }}
              overflowY="auto"
              position="relative"
              className="fade-in"
              // Better focus management
              _focus={{
                outline: 'none',
              }}
              // Smooth scrolling
              sx={{
                scrollBehavior: 'smooth',
                // Custom scrollbar for better UX
                '&::-webkit-scrollbar': {
                  width: '8px',
                },
                '&::-webkit-scrollbar-track': {
                  background: 'gray.100',
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: 'gray.300',
                  borderRadius: '4px',
                  _hover: {
                    background: 'gray.400',
                  },
                },
                // Firefox scrollbar
                scrollbarWidth: 'thin',
                scrollbarColor: 'gray.300 gray.100',
              }}
            >
              {/* Initial Setup Bar - Show if user hasn't completed setup */}
              {user && initialSetupComplete === false && <InitialSetupBar />}
              
              <Outlet />
            </Box>
            
            {/* Footer */}
            <Box
              as="footer"
              role="contentinfo"
              mt="auto" // Pushes footer to bottom
            >
              <Footer />
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
} 