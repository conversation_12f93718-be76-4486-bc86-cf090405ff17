import { useState } from 'react';
import { EnhancedStatsData } from './useEnhancedStatistics';
import * as XLSX from 'xlsx';

export interface ExportOptions {
  format: 'excel' | 'csv' | 'json' | 'pdf';
  includeCharts: boolean;
  includeSummary: boolean;
  includeRobotDetails: boolean;
  includeTradeHistory: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
}

export const useStatisticsExport = () => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const exportStatistics = async (
    stats: EnhancedStatsData,
    options: ExportOptions
  ): Promise<void> => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      switch (options.format) {
        case 'excel':
          await exportToExcel(stats, options);
          break;
        case 'csv':
          await exportToCSV(stats, options);
          break;
        case 'json':
          await exportToJSON(stats, options);
          break;
        case 'pdf':
          await exportToPDF(stats, options);
          break;
        default:
          throw new Error('Desteklenmeyen format');
      }
    } catch (error) {
      console.error('Export error:', error);
      throw error;
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const exportToExcel = async (stats: EnhancedStatsData, options: ExportOptions) => {
    const workbook = XLSX.utils.book_new();

    setExportProgress(20);

    // Summary sheet
    if (options.includeSummary) {
      const summaryData = [
        ['Metrik', 'Değer'],
        ['Toplam İşlem', stats.totalTrades],
        ['Kazanma Oranı', `${stats.winRate.toFixed(2)}%`],
        ['Toplam P&L', `₺${stats.totalPnl.toFixed(2)}`],
        ['Toplam ROI', `${stats.roiAnalysis.totalROI.toFixed(2)}%`],
        ['Yıllık ROI', `${stats.roiAnalysis.annualizedROI.toFixed(2)}%`],
        ['Toplam Yatırım', `₺${stats.roiAnalysis.totalInvestment.toFixed(2)}`],
        ['Toplam Getiri', `₺${stats.roiAnalysis.totalReturns.toFixed(2)}`],
        ['Profit Factor', stats.profitFactor.toFixed(2)],
        ['Sharpe Ratio', stats.sharpeRatio.toFixed(2)],
        ['Max Drawdown', `${stats.maxDrawdownPercent.toFixed(2)}%`],
        ['Aktif Robot', stats.realtimeMetrics.activeRobots],
        ['Açık Pozisyon', stats.realtimeMetrics.openPositions]
      ];

      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Özet');
    }

    setExportProgress(40);

    // Robot details sheet
    if (options.includeRobotDetails) {
      const robotData = [
        ['Robot Adı', 'Tip', 'İşlem Sayısı', 'Kazanma Oranı', 'ROI', 'Toplam P&L', 'Profit Factor']
      ];

      if (stats.soloRobotStats) {
        robotData.push([
          'Solo-Robot',
          'Solo',
          stats.soloRobotStats.totalTrades.toString(),
          `${stats.soloRobotStats.winRate.toFixed(2)}%`,
          `${stats.soloRobotStats.roi.toFixed(2)}%`,
          `₺${stats.soloRobotStats.totalPnl.toFixed(2)}`,
          stats.soloRobotStats.profitFactor.toFixed(2)
        ]);
      }

      stats.broRobotStats.forEach(robot => {
        robotData.push([
          robot.robotName,
          'Bro',
          robot.totalTrades.toString(),
          `${robot.winRate.toFixed(2)}%`,
          `${robot.roi.toFixed(2)}%`,
          `₺${robot.totalPnl.toFixed(2)}`,
          robot.profitFactor.toFixed(2)
        ]);
      });

      const robotSheet = XLSX.utils.aoa_to_sheet(robotData);
      XLSX.utils.book_append_sheet(workbook, robotSheet, 'Robot Detayları');
    }

    setExportProgress(60);

    // Monthly performance sheet
    const monthlyData = [
      ['Ay', 'P&L', 'İşlem Sayısı', 'ROI']
    ];

    stats.roiAnalysis.monthlyROI.forEach(month => {
      monthlyData.push([
        month.month,
        `₺${month.returns.toFixed(2)}`,
        '', // Trade count would need to be calculated
        `${month.roi.toFixed(2)}%`
      ]);
    });

    const monthlySheet = XLSX.utils.aoa_to_sheet(monthlyData);
    XLSX.utils.book_append_sheet(workbook, monthlySheet, 'Aylık Performans');

    setExportProgress(80);

    // Symbol performance sheet
    if (Object.keys(stats.pnlBySymbol).length > 0) {
      const symbolData = [
        ['Sembol', 'P&L', 'İşlem Sayısı']
      ];

      Object.entries(stats.pnlBySymbol).forEach(([symbol, data]) => {
        const pnl = typeof data === 'object' && data !== null ? (data as any).pnl || 0 : (data as number) || 0;
        symbolData.push([
          symbol,
          `₺${pnl.toFixed(2)}`,
          '' // Trade count would need to be calculated
        ]);
      });

      const symbolSheet = XLSX.utils.aoa_to_sheet(symbolData);
      XLSX.utils.book_append_sheet(workbook, symbolSheet, 'Sembol Performansı');
    }

    setExportProgress(90);

    // Generate and download file
    const fileName = `trading-statistics-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);

    setExportProgress(100);
  };

  const exportToCSV = async (stats: EnhancedStatsData, options: ExportOptions) => {
    let csvContent = '';

    if (options.includeSummary) {
      csvContent += 'ÖZET İSTATİSTİKLER\n';
      csvContent += 'Metrik,Değer\n';
      csvContent += `Toplam İşlem,${stats.totalTrades}\n`;
      csvContent += `Kazanma Oranı,${stats.winRate.toFixed(2)}%\n`;
      csvContent += `Toplam P&L,₺${stats.totalPnl.toFixed(2)}\n`;
      csvContent += `Toplam ROI,${stats.roiAnalysis.totalROI.toFixed(2)}%\n`;
      csvContent += `Yıllık ROI,${stats.roiAnalysis.annualizedROI.toFixed(2)}%\n`;
      csvContent += '\n';
    }

    if (options.includeRobotDetails) {
      csvContent += 'ROBOT DETAYLARI\n';
      csvContent += 'Robot Adı,Tip,İşlem Sayısı,Kazanma Oranı,ROI,Toplam P&L\n';
      
      if (stats.soloRobotStats) {
        csvContent += `Solo-Robot,Solo,${stats.soloRobotStats.totalTrades},${stats.soloRobotStats.winRate.toFixed(2)}%,${stats.soloRobotStats.roi.toFixed(2)}%,₺${stats.soloRobotStats.totalPnl.toFixed(2)}\n`;
      }

      stats.broRobotStats.forEach(robot => {
        csvContent += `${robot.robotName},Bro,${robot.totalTrades},${robot.winRate.toFixed(2)}%,${robot.roi.toFixed(2)}%,₺${robot.totalPnl.toFixed(2)}\n`;
      });
      csvContent += '\n';
    }

    // Monthly performance
    csvContent += 'AYLIK PERFORMANS\n';
    csvContent += 'Ay,P&L,ROI\n';
    stats.roiAnalysis.monthlyROI.forEach(month => {
      csvContent += `${month.month},₺${month.returns.toFixed(2)},${month.roi.toFixed(2)}%\n`;
    });

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `trading-statistics-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToJSON = async (stats: EnhancedStatsData, options: ExportOptions) => {
    const exportData = {
      exportDate: new Date().toISOString(),
      summary: options.includeSummary ? {
        totalTrades: stats.totalTrades,
        winRate: stats.winRate,
        totalPnl: stats.totalPnl,
        totalROI: stats.roiAnalysis.totalROI,
        annualizedROI: stats.roiAnalysis.annualizedROI,
        totalInvestment: stats.roiAnalysis.totalInvestment,
        totalReturns: stats.roiAnalysis.totalReturns,
        profitFactor: stats.profitFactor,
        sharpeRatio: stats.sharpeRatio,
        maxDrawdown: stats.maxDrawdownPercent,
        activeRobots: stats.realtimeMetrics.activeRobots,
        openPositions: stats.realtimeMetrics.openPositions
      } : undefined,
      robotDetails: options.includeRobotDetails ? {
        soloRobot: stats.soloRobotStats,
        broRobots: stats.broRobotStats
      } : undefined,
      monthlyPerformance: stats.roiAnalysis.monthlyROI,
      symbolPerformance: stats.pnlBySymbol
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `trading-statistics-${new Date().toISOString().split('T')[0]}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToPDF = async (stats: EnhancedStatsData, options: ExportOptions) => {
    try {
      // Create a comprehensive HTML report for PDF generation
      const reportWindow = window.open('', '_blank');
      if (!reportWindow) {
        throw new Error('Popup engellendi. Lütfen popup engelleyiciyi devre dışı bırakın.');
      }

      const htmlContent = generateProfessionalHTMLReport(stats, options);
      reportWindow.document.write(htmlContent);
      reportWindow.document.close();

      // Wait for content to load, then trigger print
      setTimeout(() => {
        reportWindow.print();

        // Close window after printing (optional)
        reportWindow.addEventListener('afterprint', () => {
          setTimeout(() => {
            reportWindow.close();
          }, 1000);
        });
      }, 1500);

    } catch (error) {
      console.error('PDF export error:', error);
      throw error;
    }
  };

  const generateProfessionalHTMLReport = (stats: EnhancedStatsData, options: ExportOptions): string => {
    const currentDate = new Date();
    const reportDate = currentDate.toLocaleDateString('tr-TR');
    const reportTime = currentDate.toLocaleTimeString('tr-TR');

    return `
      <!DOCTYPE html>
      <html lang="tr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AlgoBir Trading İstatistikleri Raporu</title>
        <style>
          @media print {
            body { margin: 0; }
            .page-break { page-break-before: always; }
            .no-print { display: none; }
          }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            color: #333;
            background: #fff;
          }

          .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            border-bottom: 3px solid #4299E1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            margin: -20px -20px 40px -20px;
            padding: 40px 20px;
          }

          .company-logo {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
          }

          .report-title {
            font-size: 1.8em;
            margin: 15px 0;
            font-weight: 300;
          }

          .report-meta {
            font-size: 0.9em;
            opacity: 0.9;
            margin-top: 20px;
          }

          .section {
            margin-bottom: 40px;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }

          .section h2 {
            color: #4299E1;
            border-bottom: 2px solid #4299E1;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.4em;
          }

          .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
          }

          .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #4299E1;
          }

          .metric-label {
            font-weight: 600;
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .metric-value {
            font-size: 1.8em;
            font-weight: bold;
            margin: 8px 0;
          }

          .positive { color: #48BB78; }
          .negative { color: #F56565; }
          .neutral { color: #4299E1; }

          table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          }

          th, td {
            padding: 15px 12px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
          }

          th {
            background: #4299E1;
            color: white;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85em;
            letter-spacing: 0.5px;
          }

          tr:hover {
            background: #f7fafc;
          }

          .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
          }

          .chart-placeholder {
            background: #f8f9fa;
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            color: #718096;
            margin: 20px 0;
          }

          .disclaimer {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 40px;
            font-size: 0.85em;
            line-height: 1.5;
          }

          .disclaimer h3 {
            color: #c53030;
            margin-top: 0;
          }

          .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #718096;
            font-size: 0.85em;
          }

          .performance-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
          }

          .indicator-excellent { background: #c6f6d5; color: #22543d; }
          .indicator-good { background: #bee3f8; color: #2a4365; }
          .indicator-average { background: #fef5e7; color: #744210; }
          .indicator-poor { background: #fed7d7; color: #742a2a; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-logo">🤖 AlgoBir</div>
          <div class="report-title">Trading Performans Raporu</div>
          <div class="report-meta">
            <div>Rapor Tarihi: ${reportDate} - ${reportTime}</div>
            <div>Rapor Türü: Kapsamlı Performans Analizi</div>
          </div>
        </div>

        ${options.includeSummary ? `
        <div class="section">
          <h2>📊 Genel Performans Özeti</h2>
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-label">Toplam İşlem Sayısı</div>
              <div class="metric-value neutral">${stats.totalTrades}</div>
              <div style="font-size: 0.8em; color: #666;">Gerçekleştirilen toplam işlem</div>
            </div>
            <div class="metric-card">
              <div class="metric-label">Kazanma Oranı</div>
              <div class="metric-value ${stats.winRate > 60 ? 'positive' : stats.winRate > 40 ? 'neutral' : 'negative'}">${stats.winRate.toFixed(2)}%</div>
              <div class="performance-indicator ${stats.winRate > 70 ? 'indicator-excellent' : stats.winRate > 60 ? 'indicator-good' : stats.winRate > 50 ? 'indicator-average' : 'indicator-poor'}">
                ${stats.winRate > 70 ? 'Mükemmel' : stats.winRate > 60 ? 'İyi' : stats.winRate > 50 ? 'Ortalama' : 'Geliştirilmeli'}
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-label">Toplam Kar/Zarar</div>
              <div class="metric-value ${stats.totalPnl > 0 ? 'positive' : 'negative'}">₺${stats.totalPnl.toFixed(2)}</div>
              <div style="font-size: 0.8em; color: #666;">Net kar/zarar tutarı</div>
            </div>
            <div class="metric-card">
              <div class="metric-label">Toplam ROI</div>
              <div class="metric-value ${stats.roiAnalysis.totalROI > 0 ? 'positive' : 'negative'}">${stats.roiAnalysis.totalROI.toFixed(2)}%</div>
              <div class="performance-indicator ${stats.roiAnalysis.totalROI > 20 ? 'indicator-excellent' : stats.roiAnalysis.totalROI > 10 ? 'indicator-good' : stats.roiAnalysis.totalROI > 0 ? 'indicator-average' : 'indicator-poor'}">
                ${stats.roiAnalysis.totalROI > 20 ? 'Çok İyi' : stats.roiAnalysis.totalROI > 10 ? 'İyi' : stats.roiAnalysis.totalROI > 0 ? 'Pozitif' : 'Negatif'}
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-label">Profit Factor</div>
              <div class="metric-value ${stats.profitFactor > 1 ? 'positive' : 'negative'}">${(stats.profitFactor || 0).toFixed(2)}</div>
              <div style="font-size: 0.8em; color: #666;">Kar/Zarar oranı</div>
            </div>
            <div class="metric-card">
              <div class="metric-label">Ortalama İşlem</div>
              <div class="metric-value ${stats.avgTradePnl > 0 ? 'positive' : 'negative'}">₺${(stats.avgTradePnl || 0).toFixed(2)}</div>
              <div style="font-size: 0.8em; color: #666;">İşlem başına ortalama</div>
            </div>
          </div>
        </div>
        ` : ''}

        ${options.includeRobotDetails ? `
        <div class="section page-break">
          <h2>🤖 Robot Performans Analizi</h2>

          ${stats.soloRobotStats ? `
          <div style="margin-bottom: 30px;">
            <h3 style="color: #4299E1; margin-bottom: 15px;">Solo-Robot Performansı</h3>
            <div class="metrics-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
              <div class="metric-card">
                <div class="metric-label">Toplam İşlem</div>
                <div class="metric-value neutral">${stats.soloRobotStats.totalTrades}</div>
              </div>
              <div class="metric-card">
                <div class="metric-label">Kazanma Oranı</div>
                <div class="metric-value ${stats.soloRobotStats.winRate > 50 ? 'positive' : 'negative'}">${stats.soloRobotStats.winRate.toFixed(2)}%</div>
              </div>
              <div class="metric-card">
                <div class="metric-label">ROI</div>
                <div class="metric-value ${stats.soloRobotStats.roi > 0 ? 'positive' : 'negative'}">${stats.soloRobotStats.roi.toFixed(2)}%</div>
              </div>
              <div class="metric-card">
                <div class="metric-label">Toplam P&L</div>
                <div class="metric-value ${stats.soloRobotStats.totalPnl > 0 ? 'positive' : 'negative'}">₺${stats.soloRobotStats.totalPnl.toFixed(2)}</div>
              </div>
              <div class="metric-card">
                <div class="metric-label">Profit Factor</div>
                <div class="metric-value ${stats.soloRobotStats.profitFactor > 1 ? 'positive' : 'negative'}">${stats.soloRobotStats.profitFactor.toFixed(2)}</div>
              </div>
              <div class="metric-card">
                <div class="metric-label">En İyi İşlem</div>
                <div class="metric-value positive">₺${stats.soloRobotStats.bestTrade.toFixed(2)}</div>
              </div>
            </div>
          </div>
          ` : ''}

          ${stats.broRobotStats.length > 0 ? `
          <div>
            <h3 style="color: #4299E1; margin-bottom: 15px;">Bro-Robot Performansları</h3>
            <table>
              <thead>
                <tr>
                  <th>Robot Adı</th>
                  <th>İşlem Sayısı</th>
                  <th>Kazanma Oranı</th>
                  <th>ROI</th>
                  <th>Toplam P&L</th>
                  <th>Profit Factor</th>
                  <th>Performans</th>
                </tr>
              </thead>
              <tbody>
                ${stats.broRobotStats.map(robot => `
                <tr>
                  <td style="font-weight: 600;">${robot.robotName}</td>
                  <td>${robot.totalTrades}</td>
                  <td class="${robot.winRate > 50 ? 'positive' : 'negative'}">${robot.winRate.toFixed(2)}%</td>
                  <td class="${robot.roi > 0 ? 'positive' : 'negative'}">${robot.roi.toFixed(2)}%</td>
                  <td class="${robot.totalPnl > 0 ? 'positive' : 'negative'}">₺${robot.totalPnl.toFixed(2)}</td>
                  <td class="${robot.profitFactor > 1 ? 'positive' : 'negative'}">${robot.profitFactor.toFixed(2)}</td>
                  <td>
                    <span class="performance-indicator ${robot.roi > 15 ? 'indicator-excellent' : robot.roi > 5 ? 'indicator-good' : robot.roi > 0 ? 'indicator-average' : 'indicator-poor'}">
                      ${robot.roi > 15 ? 'Mükemmel' : robot.roi > 5 ? 'İyi' : robot.roi > 0 ? 'Ortalama' : 'Zayıf'}
                    </span>
                  </td>
                </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
          ` : '<p style="text-align: center; color: #666; font-style: italic;">Aktif Bro-Robot aboneliği bulunmuyor.</p>'}
        </div>
        ` : ''}

        ${options.includeCharts ? `
        <div class="section page-break">
          <h2>📈 Görsel Analiz</h2>
          <div class="chart-placeholder">
            <h3>Performans Grafikleri</h3>
            <p>Bu bölümde normalde interaktif grafikler yer alır:</p>
            <ul style="text-align: left; display: inline-block;">
              <li>Kümülatif kar/zarar trendi</li>
              <li>Aylık performans analizi</li>
              <li>Sembol bazlı dağılım</li>
              <li>Saatlik performans haritası</li>
            </ul>
            <p><em>Detaylı grafikler için web arayüzünü kullanınız.</em></p>
          </div>
        </div>
        ` : ''}

        <div class="section page-break">
          <h2>⚠️ Risk Analizi ve Öneriler</h2>
          <div class="metrics-grid" style="grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));">
            <div class="metric-card">
              <div class="metric-label">Maksimum Düşüş</div>
              <div class="metric-value negative">₺${(stats.maxDrawdown || 0).toFixed(2)}</div>
              <div style="font-size: 0.8em; color: #666;">En büyük kayıp miktarı</div>
            </div>
            <div class="metric-card">
              <div class="metric-label">Sharpe Oranı</div>
              <div class="metric-value ${(stats.sharpeRatio || 0) > 1 ? 'positive' : 'neutral'}">${(stats.sharpeRatio || 0).toFixed(2)}</div>
              <div style="font-size: 0.8em; color: #666;">Risk ayarlı getiri</div>
            </div>
          </div>

          <div style="margin-top: 20px; padding: 20px; background: #f0f8ff; border-radius: 8px; border-left: 4px solid #4299E1;">
            <h4 style="color: #4299E1; margin-top: 0;">💡 Performans Önerileri</h4>
            <ul style="margin: 10px 0; padding-left: 20px;">
              ${stats.winRate < 50 ? '<li>Kazanma oranınızı artırmak için strateji analizi yapmanız önerilir.</li>' : ''}
              ${stats.roiAnalysis.totalROI < 5 ? '<li>ROI performansınızı iyileştirmek için risk yönetimi gözden geçirilebilir.</li>' : ''}
              ${(stats.profitFactor || 0) < 1.5 ? '<li>Profit factor değerinizi artırmak için zarar kesme stratejilerinizi optimize edebilirsiniz.</li>' : ''}
              ${stats.totalTrades < 50 ? '<li>Daha güvenilir istatistikler için işlem sayınızı artırmanız önerilir.</li>' : ''}
              <li>Düzenli performans takibi yaparak stratejilerinizi sürekli iyileştirin.</li>
              <li>Risk yönetimi kurallarına sıkı sıkıya uyun ve pozisyon büyüklüklerinizi kontrol edin.</li>
            </ul>
          </div>
        </div>

        <div class="disclaimer">
          <h3>⚠️ Önemli Uyarılar ve Yasal Bildirim</h3>
          <p><strong>Risk Uyarısı:</strong> Bu raporda yer alan bilgiler yalnızca bilgilendirme amaçlıdır ve yatırım tavsiyesi niteliği taşımaz. Geçmiş performans gelecekteki sonuçları garanti etmez.</p>

          <p><strong>Sorumluluk Reddi:</strong> AlgoBir platformu ve bu rapor, finansal piyasalardaki yatırım kararlarınızdan doğabilecek kayıplardan sorumlu değildir. Tüm yatırım kararları kendi sorumluluğunuzdadır.</p>

          <p><strong>Veri Doğruluğu:</strong> Bu rapordaki veriler mevcut sistem kayıtlarına dayanmaktadır. Veri doğruluğu için düzenli kontroller yapılsa da, teknik hatalar veya sistem güncellemeleri nedeniyle farklılıklar olabilir.</p>

          <p><strong>Gizlilik:</strong> Bu rapor kişisel finansal bilgilerinizi içermektedir. Lütfen güvenli bir şekilde saklayın ve yetkisiz kişilerle paylaşmayın.</p>

          <p><strong>Güncellik:</strong> Bu rapor ${new Date().toLocaleDateString('tr-TR')} tarihinde oluşturulmuştur. Güncel veriler için lütfen platformu ziyaret edin.</p>

          <p style="margin-top: 15px; font-size: 0.8em; color: #666;">
            <strong>İletişim:</strong> Sorularınız için AlgoBir destek ekibiyle iletişime geçebilirsiniz.<br>
            <strong>Platform:</strong> AlgoBir Trading Analytics Platform<br>
            <strong>Rapor Versiyonu:</strong> v2.0 - Gelişmiş Analitik
          </p>
        </div>

        <div class="footer">
          <p>Bu rapor AlgoBir Trading Analytics Platform tarafından otomatik olarak oluşturulmuştur.</p>
          <p>© ${new Date().getFullYear()} AlgoBir. Tüm hakları saklıdır.</p>
          <p style="margin-top: 10px; font-size: 0.8em;">
            Rapor ID: ${Math.random().toString(36).substr(2, 9).toUpperCase()} |
            Oluşturma Zamanı: ${new Date().toISOString()}
          </p>
        </div>
      </body>
      </html>
    `;
  };

  // Keep the old function for backward compatibility


  return {
    exportStatistics,
    isExporting,
    exportProgress
  };
};
