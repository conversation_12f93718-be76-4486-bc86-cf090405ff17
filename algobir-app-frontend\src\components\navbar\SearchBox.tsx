import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Input,
  InputGroup,
  InputLeftElement,
  VStack,
  HStack,
  Text,
  Icon,
  useColorModeValue,
  Flex,
  Badge,
  Kbd,
  useToast,
  Divider,
  Button,
  Spinner,
  useBreakpointValue
} from '@chakra-ui/react';
import {
  FiSearch,
  FiTrendingUp,
  FiUser,
  FiFile,
  FiArrowRight,
  FiClock,
  FiX,
  FiBook,
  FiHelpCircle
} from 'react-icons/fi';
import { RiRobotFill } from 'react-icons/ri';
import { useSearchData } from '../../hooks/useSearchData';
import { performAdvancedSearch, debounce, SearchResult } from '../../utils/searchUtils';

// SearchResult interface is now imported from searchUtils

interface SearchBoxProps {
  size?: 'sm' | 'md' | 'lg';
  placeholder?: string;
  isMobileModal?: boolean;
  onClose?: () => void;
}

const SearchBox: React.FC<SearchBoxProps> = ({
  size = 'md',
  placeholder = 'Ara... (Ctrl+K)',
  isMobileModal = false,
  onClose
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [popularSearches] = useState<string[]>([
    'İşlemler', 'Robotlar', 'İstatistikler', 'Dashboard', 'Profil'
  ]);



  const inputRef = useRef<HTMLInputElement>(null);
  const boxRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const toast = useToast();
  const isMobile = useBreakpointValue({ base: true, lg: false });

  // Get comprehensive search data
  const { allItems, loading: dataLoading } = useSearchData();

  // Color mode values
  const bg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const inputBg = useColorModeValue('gray.50', 'gray.700');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  const shadow = useColorModeValue(
    '0 8px 25px rgba(0, 0, 0, 0.15)',
    '0 8px 25px rgba(0, 0, 0, 0.4)'
  );

  // Enhanced keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Global search shortcut
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        inputRef.current?.focus();
        setIsOpen(true);
        return;
      }

      // Only handle navigation keys when search is open
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          setIsOpen(false);
          setSelectedIndex(-1);
          inputRef.current?.blur();
          // Close mobile modal if it's a mobile modal
          if (isMobileModal && onClose) {
            onClose();
          }
          break;

        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev =>
            prev < results.length - 1 ? prev + 1 : prev
          );
          break;

        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > -1 ? prev - 1 : -1);
          break;

        case 'Enter':
          e.preventDefault();
          if (selectedIndex >= 0 && results[selectedIndex]) {
            handleResultSelect(results[selectedIndex]);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex]);

  // Click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (boxRef.current && !boxRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Local storage'dan son aramaları yükle
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // Sabit sayfa sonuçları - şimdilik kullanılmıyor ama gelecekte kullanılabilir
  /* const pageResults: SearchResult[] = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      subtitle: 'Ana sayfa ve genel bakış',
      type: 'page',
      url: '/'
    },
    {
      id: 'trades',
      title: 'İşlemlerim',
      subtitle: 'Tüm alım satım işlemleri',
      type: 'page',

      url: '/trades'
    },
    {
      id: 'open-positions',
      title: 'Açık Pozisyonlar',
      subtitle: 'Devam eden işlemler',
      type: 'page',

      url: '/open-positions'
    },
    {
      id: 'marketplace',
      title: 'Robot Pazarı',
      subtitle: 'Robot arama ve abonelik',
      type: 'page',

      url: '/marketplace'
    },
    {
      id: 'management',
      title: 'Yönetim',
      subtitle: 'Hesap ayarları ve API',
      type: 'page',

      url: '/management'
    },
    {
      id: 'statistics',
      title: 'İstatistikler',
      subtitle: 'Performans analizi',
      type: 'page',

      url: '/statistics'
    }
  ]; */

  // Enhanced search function with fuzzy matching
  const performSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (!searchQuery.trim()) {
        setResults([]);
        setSelectedIndex(-1);
        return;
      }

      setLoading(true);

      try {
        // Use advanced search with fuzzy matching and relevance scoring
        const searchResults = performAdvancedSearch(searchQuery, allItems, {
          maxResults: 15,
          minScore: 0.1,
          groupByType: true
        });

        setResults(searchResults);
        setSelectedIndex(-1);
      } catch (error) {
        console.error('Search error:', error);
        setResults([]);
      } finally {
        setLoading(false);
      }
    }, 300),
    [allItems]
  );

  // Helper functions
  const clearRecentSearch = (search: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const newRecentSearches = recentSearches.filter(s => s !== search);
    setRecentSearches(newRecentSearches);
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches));
  };

  const clearAllRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('recentSearches');
  };

  // Enhanced type icons
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'trade': return FiTrendingUp;
      case 'robot': return RiRobotFill;
      case 'page': return FiFile;
      case 'user': return FiUser;
      case 'documentation': return FiBook;
      case 'help': return FiHelpCircle;
      default: return FiSearch;
    }
  };

  // Enhanced type colors
  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'trade': return 'blue';
      case 'robot': return 'purple';
      case 'page': return 'green';
      case 'user': return 'orange';
      case 'documentation': return 'teal';
      case 'help': return 'cyan';
      default: return 'gray';
    }
  };

  // Enhanced type display names
  const getTypeDisplayName = (type: string) => {
    switch (type) {
      case 'trade': return 'İşlem';
      case 'robot': return 'Robot';
      case 'page': return 'Sayfa';
      case 'user': return 'Kullanıcı';
      case 'documentation': return 'Dokümantasyon';
      case 'help': return 'Yardım';
      default: return 'Diğer';
    }
  };

  // Search input change handler
  const handleSearchChange = (value: string) => {
    setQuery(value);
    performSearch(value);
  };

  // Enhanced result selection with immediate navigation
  const handleResultSelect = (result: SearchResult) => {
    // Add to recent searches
    const newRecentSearches = [result.title, ...recentSearches.filter(s => s !== result.title)].slice(0, 5);
    setRecentSearches(newRecentSearches);
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches));

    // Close search and reset state
    setIsOpen(false);
    setQuery('');
    setSelectedIndex(-1);

    // Close mobile modal if it's a mobile modal
    if (isMobileModal && onClose) {
      onClose();
    }

    // Navigate immediately to the result
    if (result.url) {
      navigate(result.url);

      // Show success toast
      toast({
        title: `${getTypeDisplayName(result.type)}: ${result.title}`,
        description: result.subtitle || 'Sayfaya yönlendiriliyorsunuz',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    }
  };



  return (
    <Box position="relative" ref={boxRef} w="100%" maxW="500px">
      <InputGroup size={size}>
        <InputLeftElement pointerEvents="none">
          <Icon as={FiSearch} color={secondaryTextColor} />
        </InputLeftElement>
        <Input
          ref={inputRef}
          placeholder={placeholder}
          value={query}
          onChange={(e) => handleSearchChange(e.target.value)}
          onFocus={() => setIsOpen(true)}
          bg={inputBg}
          border="1px solid"
          borderColor={borderColor}
          borderRadius="xl"
          _hover={{ borderColor: 'blue.300' }}
          _focus={{
            borderColor: 'blue.500',
            boxShadow: '0 0 0 1px rgba(59, 130, 246, 0.5)',
            bg: bg
          }}
          transition="all 0.2s"
        />
      </InputGroup>

      {/* Arama Sonuçları Dropdown */}
      {isOpen && (
        <Box
          position="absolute"
          top="100%"
          left="0"
          right="0"
          mt="2"
          bg={bg}
          borderRadius="xl"
          border="1px solid"
          borderColor={borderColor}
          boxShadow={shadow}
          zIndex={9999}
          maxH="400px"
          overflowY="auto"
          p="3"
        >
            {/* Loading state with spinner */}
            {(loading || dataLoading) && (
              <Flex justify="center" align="center" py="6">
                <Spinner size="md" color="blue.500" thickness="3px" />
                <Text ml="3" color={secondaryTextColor}>
                  Aranıyor...
                </Text>
              </Flex>
            )}

            {/* Enhanced search results */}
            {!loading && !dataLoading && results.length > 0 && (
              <VStack spacing="1" align="stretch">
                <HStack justify="space-between" px="2" py="1">
                  <Text fontSize="xs" fontWeight="bold" color={secondaryTextColor}>
                    ARAMA SONUÇLARI ({results.length})
                  </Text>
                  <HStack spacing="1">
                    <Kbd fontSize="xs">↑↓</Kbd>
                    <Kbd fontSize="xs">Enter</Kbd>
                  </HStack>
                </HStack>

                {results.map((result, index) => (
                  <Button
                    key={result.id}
                    variant="ghost"
                    justifyContent="flex-start"
                    h="auto"
                    py="3"
                    px="3"
                    borderRadius="lg"
                    bg={selectedIndex === index ? hoverBg : 'transparent'}
                    _hover={{ bg: hoverBg }}
                    onClick={() => handleResultSelect(result)}
                    position="relative"
                  >
                    <HStack spacing="3" w="100%">
                      <Flex
                        w="8"
                        h="8"
                        bg={`${getTypeLabel(result.type)}.100`}
                        borderRadius="md"
                        align="center"
                        justify="center"
                        flexShrink="0"
                      >
                        <Icon
                          as={getTypeIcon(result.type)}
                          color={`${getTypeLabel(result.type)}.500`}
                          boxSize="4"
                        />
                      </Flex>

                      <VStack align="start" spacing="0" flex="1" minW="0">
                        <HStack spacing="2" w="100%">
                          <Text
                            fontWeight="medium"
                            color={textColor}
                            noOfLines={1}
                            dangerouslySetInnerHTML={{
                              __html: result.highlightedTitle || result.title
                            }}
                          />
                          {result.badge && (
                            <Badge colorScheme={getTypeLabel(result.type)} size="sm" flexShrink="0">
                              {result.badge}
                            </Badge>
                          )}
                          <Badge variant="subtle" colorScheme={getTypeLabel(result.type)} size="xs" flexShrink="0">
                            {getTypeDisplayName(result.type)}
                          </Badge>
                        </HStack>
                        {result.subtitle && (
                          <Text
                            fontSize="sm"
                            color={secondaryTextColor}
                            noOfLines={1}
                            dangerouslySetInnerHTML={{
                              __html: result.highlightedSubtitle || result.subtitle
                            }}
                          />
                        )}
                        {result.relevanceScore && (
                          <Text fontSize="xs" color={secondaryTextColor} opacity="0.7">
                            Relevance: {(result.relevanceScore * 100).toFixed(0)}%
                          </Text>
                        )}
                      </VStack>

                      <Icon as={FiArrowRight} color={secondaryTextColor} boxSize="4" flexShrink="0" />
                    </HStack>
                  </Button>
                ))}
              </VStack>
            )}

            {/* No results state */}
            {!loading && !dataLoading && results.length === 0 && query.trim() !== '' && (
              <VStack spacing="3" py="6" align="center">
                <Icon as={FiSearch} boxSize="8" color={secondaryTextColor} opacity="0.5" />
                <VStack spacing="1" textAlign="center">
                  <Text fontWeight="medium" color={textColor}>
                    Sonuç bulunamadı
                  </Text>
                  <Text fontSize="sm" color={secondaryTextColor}>
                    "{query}" için herhangi bir sonuç bulunamadı
                  </Text>
                </VStack>
              </VStack>
            )}

            {/* Recent searches */}
            {!loading && !dataLoading && results.length === 0 && query === '' && recentSearches.length > 0 && (
              <VStack spacing="1" align="stretch">
                <HStack justify="space-between" px="2" py="1">
                  <Text fontSize="xs" fontWeight="bold" color={secondaryTextColor}>
                    SON ARAMALAR
                  </Text>
                  <Button
                    size="xs"
                    variant="ghost"
                    onClick={clearAllRecentSearches}
                    color={secondaryTextColor}
                    _hover={{ color: textColor }}
                  >
                    Temizle
                  </Button>
                </HStack>
                {recentSearches.map((search, index) => (
                  <Button
                    key={`recent-${index}`}
                    variant="ghost"
                    justifyContent="flex-start"
                    h="auto"
                    py="2"
                    px="3"
                    borderRadius="lg"
                    _hover={{ bg: hoverBg }}
                    onClick={() => handleSearchChange(search)}
                  >
                    <HStack spacing="3" w="100%">
                      <Icon as={FiClock} color={secondaryTextColor} boxSize="4" />
                      <Text color={textColor} flex="1">
                        {search}
                      </Text>
                      <Button
                        size="xs"
                        variant="ghost"
                        onClick={(e) => clearRecentSearch(search, e)}
                        opacity="0.5"
                        _hover={{ opacity: 1 }}
                      >
                        <Icon as={FiX} boxSize="3" />
                      </Button>
                    </HStack>
                  </Button>
                ))}
              </VStack>
            )}

            {/* Popular searches when no recent searches */}
            {!loading && !dataLoading && results.length === 0 && query === '' && recentSearches.length === 0 && (
              <VStack spacing="1" align="stretch">
                <Text fontSize="xs" fontWeight="bold" color={secondaryTextColor} px="2" py="1">
                  POPÜLER ARAMALAR
                </Text>
                {popularSearches.map((search, index) => (
                  <Button
                    key={`popular-${index}`}
                    variant="ghost"
                    justifyContent="flex-start"
                    h="auto"
                    py="2"
                    px="3"
                    borderRadius="lg"
                    _hover={{ bg: hoverBg }}
                    onClick={() => handleSearchChange(search)}
                  >
                    <HStack spacing="3" w="100%">
                      <Icon as={FiTrendingUp} color={secondaryTextColor} boxSize="4" />
                      <Text color={textColor} flex="1">
                        {search}
                      </Text>
                      <Icon as={FiArrowRight} color={secondaryTextColor} boxSize="3" />
                    </HStack>
                  </Button>
                ))}
              </VStack>
            )}

            {/* Keyboard shortcuts help */}
            {!isMobile && (
              <>
                <Divider my="2" />
                <HStack justify="center" spacing="4" py="2" fontSize="xs" color={secondaryTextColor}>
                  <HStack spacing="1">
                    <Kbd>Ctrl</Kbd>
                    <Text>+</Text>
                    <Kbd>K</Kbd>
                    <Text>Arama</Text>
                  </HStack>
                  <HStack spacing="1">
                    <Kbd>↑↓</Kbd>
                    <Text>Gezin</Text>
                  </HStack>
                  <HStack spacing="1">
                    <Kbd>Enter</Kbd>
                    <Text>Seç</Text>
                  </HStack>
                  <HStack spacing="1">
                    <Kbd>Esc</Kbd>
                    <Text>Kapat</Text>
                  </HStack>
                </HStack>
              </>
            )}
        </Box>
      )}
    </Box>
  );
};

export default SearchBox; 