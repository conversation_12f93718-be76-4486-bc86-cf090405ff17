import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Badge,
  Icon,
  Progress,
  Divider,
  Tooltip,
  CircularProgress,
  CircularProgressLabel,
  Button,

  Select,
  Alert,
  AlertIcon,
  Flex,
  Spinner
} from '@chakra-ui/react';
import {
  FiZap,
  FiActivity,
  FiTarget,
  FiCheckCircle,
  FiDatabase,
  FiSend,
  FiRefreshCw,
  FiWifi,
  FiWifiOff
} from 'react-icons/fi';
import { motion } from 'framer-motion';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import useOrderTransmissionMetrics from '../../hooks/useOrderTransmissionMetrics';
import OrderTransmissionSpeedChart from './OrderTransmissionSpeedChart';

const MotionBox = motion(Box);

interface OrderTransmissionPerformanceDashboardProps {
  defaultTimeRange?: number;
  showChart?: boolean;
  showDetailedMetrics?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

const OrderTransmissionPerformanceDashboard: React.FC<OrderTransmissionPerformanceDashboardProps> = ({
  defaultTimeRange = 24,
  showChart = true,
  showDetailedMetrics = true,
  autoRefresh = true,
  refreshInterval = 30000
}) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState(defaultTimeRange);
  const [selectedSignalSource, setSelectedSignalSource] = useState<'solo-robot' | 'bro-robot' | null>(null);

  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');
  const alertBg = useColorModeValue('blue.50', 'blue.900');

  // Fetch metrics data
  const {
    stats,
    loading,
    error,
    isConnected,
    lastUpdate,
    refetch
  } = useOrderTransmissionMetrics({
    timeRangeHours: selectedTimeRange,
    signalSource: selectedSignalSource,
    enabled: true,
    autoRefresh,
    refreshInterval
  });

  // Helper function to get performance color
  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'green';
    if (value <= thresholds.warning) return 'yellow';
    return 'red';
  };

  // Performance thresholds (in milliseconds)
  const thresholds = {
    total: { good: 100, warning: 500 },
    parsing: { good: 10, warning: 50 },
    transformation: { good: 50, warning: 200 },
    webhook: { good: 200, warning: 1000 }
  };

  // Get performance status
  const getPerformanceStatus = () => {
    if (!stats) return { status: 'unknown', message: 'Veri yok' };
    
    const avgTime = stats.avg_total_processing_time_ms;
    if (avgTime <= thresholds.total.good) {
      return { status: 'excellent', message: 'Mükemmel performans' };
    } else if (avgTime <= thresholds.total.warning) {
      return { status: 'good', message: 'İyi performans' };
    } else {
      return { status: 'poor', message: 'Performans iyileştirmesi gerekli' };
    }
  };

  const performanceStatus = getPerformanceStatus();

  if (loading && !stats) {
    return (
      <Card bg={cardBg} borderRadius="xl" boxShadow="lg">
        <CardBody>
          <VStack spacing={4} py={8}>
            <Spinner size="xl" color="blue.500" thickness="4px" />
            <Text color={mutedColor}>Performans verileri yükleniyor...</Text>
          </VStack>
        </CardBody>
      </Card>
    );
  }

  return (
    <VStack spacing={6} align="stretch">
      {/* Header with Controls */}
      <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
        <CardHeader>
          <Flex justify="space-between" align="center" wrap="wrap" gap={4}>
            <VStack align="start" spacing={1}>
              <Heading size="lg" color={textColor}>
                Emir İletim Performans Paneli
              </Heading>
              <HStack spacing={3}>
                <Icon
                  as={isConnected ? FiWifi : FiWifiOff}
                  color={isConnected ? 'green.500' : 'red.500'}
                />
                <Text fontSize="sm" color={mutedColor}>
                  {isConnected ? 'Canlı Bağlantı Aktif' : 'Bağlantı Kesildi'}
                </Text>
                {lastUpdate && (
                  <Text fontSize="xs" color={mutedColor}>
                    Son Güncelleme: {format(lastUpdate, 'HH:mm:ss', { locale: tr })}
                  </Text>
                )}
              </HStack>
            </VStack>

            <HStack spacing={3}>
              <Select
                size="sm"
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(Number(e.target.value))}
                w="140px"
              >
                <option value={1}>Son 1 Saat</option>
                <option value={6}>Son 6 Saat</option>
                <option value={24}>Son 24 Saat</option>
                <option value={168}>Son 7 Gün</option>
              </Select>

              <Select
                size="sm"
                value={selectedSignalSource || ''}
                onChange={(e) => setSelectedSignalSource(e.target.value as any || null)}
                w="140px"
              >
                <option value="">Tüm Sinyaller</option>
                <option value="solo-robot">Solo-Robot</option>
                <option value="bro-robot">Bro-Robot</option>
              </Select>

              <Button
                size="sm"
                leftIcon={<FiRefreshCw />}
                onClick={refetch}
                isLoading={loading}
                variant="outline"
              >
                Yenile
              </Button>
            </HStack>
          </Flex>
        </CardHeader>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert status="error" borderRadius="xl">
          <AlertIcon />
          <VStack align="start" spacing={1}>
            <Text fontWeight="bold">Veri yüklenirken hata oluştu</Text>
            <Text fontSize="sm">{error}</Text>
          </VStack>
        </Alert>
      )}

      {/* Performance Status Alert */}
      {stats && (
        <Alert 
          status={performanceStatus.status === 'excellent' ? 'success' : 
                 performanceStatus.status === 'good' ? 'info' : 'warning'}
          borderRadius="xl"
          bg={alertBg}
        >
          <AlertIcon />
          <VStack align="start" spacing={1}>
            <Text fontWeight="bold">Sistem Performans Durumu</Text>
            <Text fontSize="sm">{performanceStatus.message}</Text>
            <Text fontSize="xs" color={mutedColor}>
              Ortalama işlem süresi: {stats.avg_total_processing_time_ms.toFixed(1)}ms
            </Text>
          </VStack>
        </Alert>
      )}

      {/* Main Metrics Grid */}
      {stats && (
        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
              <CardBody>
                <Stat>
                  <StatLabel fontSize="sm" color={mutedColor}>
                    Toplam Sinyal
                  </StatLabel>
                  <StatNumber fontSize="2xl" color="blue.500">
                    {stats.total_signals.toLocaleString('tr-TR')}
                  </StatNumber>
                  <StatHelpText>
                    <HStack spacing={2}>
                      <Badge colorScheme="blue" variant="subtle" fontSize="xs">
                        Solo: {stats.solo_robot_count}
                      </Badge>
                      <Badge colorScheme="purple" variant="subtle" fontSize="xs">
                        Bro: {stats.bro_robot_count}
                      </Badge>
                    </HStack>
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </MotionBox>

          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
              <CardBody>
                <Stat>
                  <StatLabel fontSize="sm" color={mutedColor}>
                    Başarı Oranı
                  </StatLabel>
                  <StatNumber fontSize="2xl" color="green.500">
                    %{stats.success_rate.toFixed(1)}
                  </StatNumber>
                  <StatHelpText>
                    <Icon as={FiCheckCircle} color="green.500" mr={1} />
                    Sistem güvenilirliği
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </MotionBox>

          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
              <CardBody>
                <Stat>
                  <StatLabel fontSize="sm" color={mutedColor}>
                    Ortalama Hız
                  </StatLabel>
                  <StatNumber 
                    fontSize="2xl" 
                    color={getPerformanceColor(stats.avg_total_processing_time_ms, thresholds.total) + '.500'}
                  >
                    {stats.avg_total_processing_time_ms.toFixed(1)}ms
                  </StatNumber>
                  <StatHelpText>
                    <Icon as={FiZap} mr={1} />
                    İşlem süresi
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </MotionBox>

          <MotionBox
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
              <CardBody>
                <Stat>
                  <StatLabel fontSize="sm" color={mutedColor}>
                    Sinyal/Dakika
                  </StatLabel>
                  <StatNumber fontSize="2xl" color="purple.500">
                    {stats.signals_per_minute.toFixed(1)}
                  </StatNumber>
                  <StatHelpText>
                    <Icon as={FiActivity} mr={1} />
                    İşlem yoğunluğu
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </MotionBox>
        </SimpleGrid>
      )}

      {/* Chart Section */}
      {showChart && (
        <OrderTransmissionSpeedChart
          timeRangeHours={selectedTimeRange}
          height={400}
          showControls={false}
          autoRefresh={autoRefresh}
        />
      )}

      {/* Detailed Metrics */}
      {showDetailedMetrics && stats && (
        <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
          {/* Processing Time Breakdown */}
          <MotionBox
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Card bg={cardBg} borderRadius="xl" boxShadow="lg" border="1px solid" borderColor={borderColor}>
              <CardHeader>
                <Heading size="md" color={textColor}>
                  İşlem Süresi Dağılımı
                </Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <Box>
                    <HStack justify="space-between" mb={2}>
                      <HStack>
                        <Icon as={FiDatabase} color="blue.500" />
                        <Text fontSize="sm" fontWeight="medium">JSON Ayrıştırma</Text>
                      </HStack>
                      <Text fontSize="sm" color={mutedColor}>
                        {stats.avg_json_parsing_time_ms.toFixed(1)}ms
                      </Text>
                    </HStack>
                    <Progress
                      value={(stats.avg_json_parsing_time_ms / stats.avg_total_processing_time_ms) * 100}
                      colorScheme={getPerformanceColor(stats.avg_json_parsing_time_ms, thresholds.parsing)}
                      size="sm"
                      borderRadius="full"
                    />
                  </Box>

                  <Box>
                    <HStack justify="space-between" mb={2}>
                      <HStack>
                        <Icon as={FiTarget} color="green.500" />
                        <Text fontSize="sm" fontWeight="medium">Veri Dönüştürme</Text>
                      </HStack>
                      <Text fontSize="sm" color={mutedColor}>
                        {stats.avg_transformation_time_ms.toFixed(1)}ms
                      </Text>
                    </HStack>
                    <Progress
                      value={(stats.avg_transformation_time_ms / stats.avg_total_processing_time_ms) * 100}
                      colorScheme={getPerformanceColor(stats.avg_transformation_time_ms, thresholds.transformation)}
                      size="sm"
                      borderRadius="full"
                    />
                  </Box>

                  <Box>
                    <HStack justify="space-between" mb={2}>
                      <HStack>
                        <Icon as={FiSend} color="orange.500" />
                        <Text fontSize="sm" fontWeight="medium">Webhook İletimi</Text>
                      </HStack>
                      <Text fontSize="sm" color={mutedColor}>
                        {stats.avg_webhook_delivery_time_ms.toFixed(1)}ms
                      </Text>
                    </HStack>
                    <Progress
                      value={(stats.avg_webhook_delivery_time_ms / stats.avg_total_processing_time_ms) * 100}
                      colorScheme={getPerformanceColor(stats.avg_webhook_delivery_time_ms, thresholds.webhook)}
                      size="sm"
                      borderRadius="full"
                    />
                  </Box>
                </VStack>
              </CardBody>
            </Card>
          </MotionBox>

          {/* Performance Percentiles */}
          <MotionBox
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Card bg={cardBg} borderRadius="xl" boxShadow="lg" border="1px solid" borderColor={borderColor}>
              <CardHeader>
                <Heading size="md" color={textColor}>
                  Performans Dağılımı
                </Heading>
              </CardHeader>
              <CardBody>
                <VStack spacing={6}>
                  <SimpleGrid columns={2} spacing={4} w="full">
                    <VStack>
                      <CircularProgress
                        value={Math.min((stats.fastest_processing_time_ms / 100) * 100, 100)}
                        color="green.400"
                        size="80px"
                        thickness="8px"
                      >
                        <CircularProgressLabel fontSize="xs" fontWeight="bold">
                          En Hızlı
                        </CircularProgressLabel>
                      </CircularProgress>
                      <Text fontSize="sm" textAlign="center">
                        {stats.fastest_processing_time_ms.toFixed(1)}ms
                      </Text>
                    </VStack>

                    <VStack>
                      <CircularProgress
                        value={Math.min((stats.slowest_processing_time_ms / 1000) * 100, 100)}
                        color="red.400"
                        size="80px"
                        thickness="8px"
                      >
                        <CircularProgressLabel fontSize="xs" fontWeight="bold">
                          En Yavaş
                        </CircularProgressLabel>
                      </CircularProgress>
                      <Text fontSize="sm" textAlign="center">
                        {stats.slowest_processing_time_ms.toFixed(1)}ms
                      </Text>
                    </VStack>
                  </SimpleGrid>

                  <Divider />

                  <VStack spacing={3} w="full">
                    <HStack justify="space-between" w="full">
                      <Text fontSize="sm" fontWeight="medium">95. Yüzdelik</Text>
                      <Badge 
                        colorScheme={getPerformanceColor(stats.p95_total_processing_time_ms, thresholds.total)}
                        variant="subtle"
                      >
                        {stats.p95_total_processing_time_ms.toFixed(1)}ms
                      </Badge>
                    </HStack>

                    <HStack justify="space-between" w="full">
                      <Text fontSize="sm" fontWeight="medium">99. Yüzdelik</Text>
                      <Badge 
                        colorScheme={getPerformanceColor(stats.p99_total_processing_time_ms, thresholds.total)}
                        variant="subtle"
                      >
                        {stats.p99_total_processing_time_ms.toFixed(1)}ms
                      </Badge>
                    </HStack>

                    <Tooltip label="Sinyallerin %95'i bu sürede işleniyor">
                      <Text fontSize="xs" color={mutedColor} textAlign="center">
                        Performans hedefleri için referans değerler
                      </Text>
                    </Tooltip>
                  </VStack>
                </VStack>
              </CardBody>
            </Card>
          </MotionBox>
        </SimpleGrid>
      )}
    </VStack>
  );
};

export default OrderTransmissionPerformanceDashboard;
