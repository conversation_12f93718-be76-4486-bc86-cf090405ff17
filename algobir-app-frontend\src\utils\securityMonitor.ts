/**
 * Security Monitor Utility
 * Advanced security monitoring and audit logging for frontend
 * Phase 2.2: Advanced Row Level Security Enhancement
 */

import { supabase } from '../supabaseClient';

interface SecurityEvent {
  id: string;
  user_id: string;
  action: string;
  table_name: string;
  record_id?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
}

interface SecuritySummary {
  total_events: number;
  critical_events: number;
  error_events: number;
  warning_events: number;
  unique_users: number;
  most_active_tables: Array<{
    table_name: string;
    event_count: number;
  }>;
  recent_critical_events: Array<{
    action: string;
    table_name: string;
    user_id: string;
    created_at: string;
  }>;
}

interface SuspiciousActivity {
  suspicious_users: Array<{
    user_id: string;
    event_count: number;
    severity: string;
  }>;
  failed_access_attempts: number;
  privilege_escalations: Array<{
    user_id: string;
    created_at: string;
    new_values: Record<string, any>;
  }>;
}

/**
 * Security Monitor Class
 * Provides comprehensive security monitoring and audit capabilities
 */
export class SecurityMonitor {
  private static instance: SecurityMonitor;
  private eventQueue: SecurityEvent[] = [];
  private isProcessing = false;
  private batchSize = 10;
  private flushInterval = 5000; // 5 seconds

  private constructor() {
    // Start periodic flush of events
    setInterval(() => {
      this.flushEvents();
    }, this.flushInterval);

    // Flush events before page unload
    window.addEventListener('beforeunload', () => {
      this.flushEvents();
    });
  }

  static getInstance(): SecurityMonitor {
    if (!SecurityMonitor.instance) {
      SecurityMonitor.instance = new SecurityMonitor();
    }
    return SecurityMonitor.instance;
  }

  /**
   * Log a security event
   */
  async logEvent(
    action: string,
    tableName: string,
    recordId?: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>,
    severity: SecurityEvent['severity'] = 'info'
  ): Promise<void> {
    try {
      // Get client information
      const userAgent = navigator.userAgent;
      
      // Add to queue for batch processing
      this.eventQueue.push({
        id: crypto.randomUUID(),
        user_id: '', // Will be set by backend
        action,
        table_name: tableName,
        record_id: recordId,
        old_values: oldValues,
        new_values: newValues,
        user_agent: userAgent,
        created_at: new Date().toISOString(),
        severity
      });

      // Flush immediately for critical events
      if (severity === 'critical' || severity === 'error') {
        await this.flushEvents();
      }

      // Flush if queue is getting large
      if (this.eventQueue.length >= this.batchSize) {
        await this.flushEvents();
      }
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  /**
   * Flush queued events to database
   */
  private async flushEvents(): Promise<void> {
    if (this.isProcessing || this.eventQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const eventsToProcess = [...this.eventQueue];
    this.eventQueue = [];

    try {
      // Use RPC function to log events (bypasses RLS)
      await Promise.all(
        eventsToProcess.map(event =>
          supabase.rpc('log_security_event', {
            p_action: event.action,
            p_table_name: event.table_name,
            p_record_id: event.record_id || null,
            p_old_values: event.old_values || null,
            p_new_values: event.new_values || null,
            p_severity: event.severity
          })
        )
      );
    } catch (error) {
      console.error('Failed to flush security events:', error);
      // Re-queue failed events
      this.eventQueue.unshift(...eventsToProcess);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get security summary for admin dashboard
   */
  async getSecuritySummary(hours: number = 24): Promise<SecuritySummary | null> {
    try {
      const { data, error } = await supabase.rpc('get_security_summary', {
        p_hours: hours
      });

      if (error) {
        console.error('Failed to get security summary:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Failed to get security summary:', error);
      return null;
    }
  }

  /**
   * Detect suspicious activity
   */
  async detectSuspiciousActivity(hours: number = 1): Promise<SuspiciousActivity | null> {
    try {
      const { data, error } = await supabase.rpc('detect_suspicious_activity', {
        p_hours: hours
      });

      if (error) {
        console.error('Failed to detect suspicious activity:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Failed to detect suspicious activity:', error);
      return null;
    }
  }

  /**
   * Get recent security events (admin only)
   */
  async getRecentEvents(
    limit: number = 50,
    severity?: SecurityEvent['severity']
  ): Promise<SecurityEvent[]> {
    try {
      let query = supabase
        .from('security_audit_log')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (severity) {
        query = query.eq('severity', severity);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Failed to get recent events:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get recent events:', error);
      return [];
    }
  }

  /**
   * Monitor specific user activity (admin only)
   */
  async getUserActivity(
    userId: string,
    hours: number = 24
  ): Promise<SecurityEvent[]> {
    try {
      const { data, error } = await supabase
        .from('security_audit_log')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - hours * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Failed to get user activity:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get user activity:', error);
      return [];
    }
  }

  /**
   * Check if current user has admin privileges
   */
  async isAdmin(): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('is_admin');
      
      if (error) {
        console.error('Failed to check admin status:', error);
        return false;
      }

      return data === true;
    } catch (error) {
      console.error('Failed to check admin status:', error);
      return false;
    }
  }

  /**
   * Convenience methods for common security events
   */
  
  async logLogin(success: boolean): Promise<void> {
    await this.logEvent(
      success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED',
      'auth',
      undefined,
      undefined,
      { success },
      success ? 'info' : 'warning'
    );
  }

  async logLogout(): Promise<void> {
    await this.logEvent('LOGOUT', 'auth', undefined, undefined, undefined, 'info');
  }

  async logDataAccess(tableName: string, recordId?: string): Promise<void> {
    await this.logEvent('DATA_ACCESS', tableName, recordId, undefined, undefined, 'info');
  }

  async logDataModification(
    tableName: string,
    recordId: string,
    oldValues: Record<string, any>,
    newValues: Record<string, any>
  ): Promise<void> {
    await this.logEvent(
      'DATA_MODIFICATION',
      tableName,
      recordId,
      oldValues,
      newValues,
      'warning'
    );
  }

  async logSuspiciousActivity(
    action: string,
    details: Record<string, any>
  ): Promise<void> {
    await this.logEvent(
      `SUSPICIOUS_${action}`,
      'security',
      undefined,
      undefined,
      details,
      'error'
    );
  }

  async logCriticalSecurityEvent(
    action: string,
    details: Record<string, any>
  ): Promise<void> {
    await this.logEvent(
      `CRITICAL_${action}`,
      'security',
      undefined,
      undefined,
      details,
      'critical'
    );
  }
}

// Export singleton instance
export const securityMonitor = SecurityMonitor.getInstance();

// Export convenience functions
export const logSecurityEvent = securityMonitor.logEvent.bind(securityMonitor);
export const getSecuritySummary = securityMonitor.getSecuritySummary.bind(securityMonitor);
export const detectSuspiciousActivity = securityMonitor.detectSuspiciousActivity.bind(securityMonitor);
