import { useState, useEffect } from 'react';
import { supabase } from '../supabaseClient';
import { RobotDetails, RobotWeeklyStatistics } from '../types/stats';

interface UseRobotDetailsReturn {
  robotDetails: RobotDetails | null;
  weeklyStatistics: RobotWeeklyStatistics | null;
  isLoading: boolean;
  isLoadingStats: boolean;
  error: string | null;
  statsError: string | null;
  refetch: () => void;
}

export function useRobotDetails(robotId: string): UseRobotDetailsReturn {
  const [robotDetails, setRobotDetails] = useState<RobotDetails | null>(null);
  const [weeklyStatistics, setWeeklyStatistics] = useState<RobotWeeklyStatistics | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingStats, setIsLoadingStats] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [statsError, setStatsError] = useState<string | null>(null);

  const fetchRobotDetails = async () => {
    if (!robotId) return;

    try {
      setIsLoading(true);
      setError(null);

      const { data, error } = await supabase.rpc('get_robot_details', {
        p_robot_id: robotId
      });

      if (error) throw error;

      if (data.error) {
        throw new Error(data.error);
      }

      setRobotDetails(data);
    } catch (err: any) {
      console.error('Robot details fetch error:', err);
      setError(err.message || 'Robot detayları yüklenirken hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchWeeklyStatistics = async () => {
    if (!robotId) return;

    try {
      setIsLoadingStats(true);
      setStatsError(null);

      const { data, error } = await supabase.rpc('get_robot_weekly_statistics', {
        p_robot_id: robotId
      });

      if (error) throw error;
      setWeeklyStatistics(data);
    } catch (err: any) {
      console.error('Robot weekly statistics fetch error:', err);
      setStatsError(err.message || 'Robot istatistikleri yüklenirken hata oluştu');
    } finally {
      setIsLoadingStats(false);
    }
  };

  const refetch = () => {
    fetchRobotDetails();
    fetchWeeklyStatistics();
  };

  useEffect(() => {
    if (robotId) {
      fetchRobotDetails();
      fetchWeeklyStatistics();
    } else {
      setRobotDetails(null);
      setWeeklyStatistics(null);
      setError(null);
      setStatsError(null);
    }
  }, [robotId]);

  return {
    robotDetails,
    weeklyStatistics,
    isLoading,
    isLoadingStats,
    error,
    statsError,
    refetch
  };
} 