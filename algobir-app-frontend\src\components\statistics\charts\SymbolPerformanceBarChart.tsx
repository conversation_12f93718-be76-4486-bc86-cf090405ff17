import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonGroup,
  Select,
  useColorModeValue,
  Tooltip,
  Badge,
  Flex,
  Spacer,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Switch,
  FormControl,
  FormLabel
} from '@chakra-ui/react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Cell,

} from 'recharts';
import { InfoIcon, DownloadIcon } from '@chakra-ui/icons';
import { SymbolMetrics } from '../../../types/symbolAnalysis';

interface SymbolPerformanceBarChartProps {
  data: SymbolMetrics[];
  height?: number;
  onSymbolClick?: (symbol: string) => void;
  onSymbolSelect?: (symbols: string[]) => void;
  showComparison?: boolean;
  maxSymbols?: number;
}

type MetricType = 'totalPnl' | 'roi' | 'winRate' | 'totalTrades' | 'sharpeRatio' | 'volatility' | 'profitFactor';

interface MetricConfig {
  key: MetricType;
  label: string;
  formatValue: (value: number) => string;
  color: string;
  description: string;
}

const metricConfigs: MetricConfig[] = [
  {
    key: 'totalPnl',
    label: 'Toplam Kar/Zarar',
    formatValue: (value: number) => `₺${value.toFixed(2)}`,
    color: '#3182CE',
    description: 'Sembol için toplam kar/zarar tutarı'
  },
  {
    key: 'roi',
    label: 'ROI (%)',
    formatValue: (value: number) => `${value.toFixed(2)}%`,
    color: '#38A169',
    description: 'Yatırım getirisi yüzdesi'
  },
  {
    key: 'winRate',
    label: 'Kazanma Oranı (%)',
    formatValue: (value: number) => `${value.toFixed(1)}%`,
    color: '#9F7AEA',
    description: 'Kazanan işlem yüzdesi'
  },
  {
    key: 'totalTrades',
    label: 'İşlem Sayısı',
    formatValue: (value: number) => value.toString(),
    color: '#ED8936',
    description: 'Toplam işlem sayısı'
  },
  {
    key: 'sharpeRatio',
    label: 'Sharpe Oranı',
    formatValue: (value: number) => value.toFixed(3),
    color: '#E53E3E',
    description: 'Risk ayarlı getiri ölçütü'
  },
  {
    key: 'volatility',
    label: 'Volatilite',
    formatValue: (value: number) => value.toFixed(2),
    color: '#00B5D8',
    description: 'Fiyat oynaklığı ölçütü'
  },
  {
    key: 'profitFactor',
    label: 'Kar Faktörü',
    formatValue: (value: number) => value.toFixed(2),
    color: '#D69E2E',
    description: 'Ortalama kazanç / Ortalama kayıp oranı'
  }
];

const SymbolPerformanceBarChart: React.FC<SymbolPerformanceBarChartProps> = ({
  data,
  height = 400,
  onSymbolClick,
  onSymbolSelect,
  showComparison = false,
  maxSymbols = 20
}) => {
  const [selectedMetric, setSelectedMetric] = useState<MetricType>('totalPnl');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showOnlyProfitable, setShowOnlyProfitable] = useState(false);
  const [selectedSymbols, setSelectedSymbols] = useState<string[]>([]);
  const [hoveredBar, setHoveredBar] = useState<string | null>(null);

  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const gridColor = useColorModeValue('#f0f0f0', '#2d3748');

  // Get current metric config
  const currentMetric = metricConfigs.find(m => m.key === selectedMetric) || metricConfigs[0];

  // Process and filter data
  const processedData = useMemo(() => {
    let filteredData = [...data];

    // Filter profitable only if enabled
    if (showOnlyProfitable) {
      filteredData = filteredData.filter(item => item.totalPnl > 0);
    }

    // Sort by selected metric
    filteredData.sort((a, b) => {
      const aValue = a[selectedMetric] as number;
      const bValue = b[selectedMetric] as number;
      return sortOrder === 'desc' ? bValue - aValue : aValue - bValue;
    });

    // Limit to max symbols
    filteredData = filteredData.slice(0, maxSymbols);

    // Transform for chart
    return filteredData.map((item, index) => ({
      symbol: item.symbol,
      value: item[selectedMetric] as number,
      rank: index + 1,
      totalPnl: item.totalPnl,
      roi: item.roi,
      winRate: item.winRate,
      totalTrades: item.totalTrades,
      sharpeRatio: item.sharpeRatio,
      volatility: item.volatility,
      profitFactor: item.profitFactor,
      color: item.totalPnl > 0 ? '#48BB78' : '#F56565'
    }));
  }, [data, selectedMetric, sortOrder, showOnlyProfitable, maxSymbols]);

  // Handle symbol selection for comparison
  const handleSymbolSelect = (symbol: string) => {
    if (!showComparison) return;

    const newSelection = selectedSymbols.includes(symbol)
      ? selectedSymbols.filter(s => s !== symbol)
      : [...selectedSymbols, symbol];

    setSelectedSymbols(newSelection);
    onSymbolSelect?.(newSelection);
  };

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;

    const data = payload[0].payload;
    
    return (
      <Box
        bg={bgColor}
        p={4}
        borderRadius="md"
        boxShadow="lg"
        border="1px solid"
        borderColor={borderColor}
        minW="200px"
      >
        <Text fontWeight="bold" mb={2} color={textColor}>
          {label}
        </Text>
        <VStack align="start" spacing={1}>
          <Text fontSize="sm" color={textColor}>
            <strong>{currentMetric.label}:</strong> {currentMetric.formatValue(data.value)}
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>Sıralama:</strong> #{data.rank}
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>Toplam P&L:</strong> ₺{data.totalPnl.toFixed(2)}
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>ROI:</strong> {data.roi.toFixed(2)}%
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>Kazanma Oranı:</strong> {data.winRate.toFixed(1)}%
          </Text>
          <Text fontSize="sm" color={textColor}>
            <strong>İşlem Sayısı:</strong> {data.totalTrades}
          </Text>
        </VStack>
      </Box>
    );
  };

  // Handle bar click
  const handleBarClick = (data: any) => {
    if (showComparison) {
      handleSymbolSelect(data.symbol);
    } else {
      onSymbolClick?.(data.symbol);
    }
  };

  return (
    <VStack spacing={4} align="stretch">
      {/* Controls */}
      <VStack spacing={4} align="stretch">
        <Flex
          direction={{ base: "column", md: "row" }}
          wrap="wrap"
          gap={4}
          align={{ base: "stretch", md: "center" }}
        >
          {/* Metric Selection */}
          <HStack spacing={2} w={{ base: "full", md: "auto" }}>
            <Text fontSize="sm" fontWeight="medium" color={textColor} minW="fit-content">
              Metrik:
            </Text>
            <Select
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value as MetricType)}
              size="sm"
              w={{ base: "full", md: "200px" }}
            >
              {metricConfigs.map(config => (
                <option key={config.key} value={config.key}>
                  {config.label}
                </option>
              ))}
            </Select>
            <Tooltip label={currentMetric.description}>
              <IconButton
                aria-label="Metrik bilgisi"
                icon={<InfoIcon />}
                size="sm"
                variant="ghost"
                minW="fit-content"
              />
            </Tooltip>
          </HStack>

          {/* Sort Order */}
          <ButtonGroup size="sm" isAttached w={{ base: "full", md: "auto" }}>
            <Button
              variant={sortOrder === 'desc' ? 'solid' : 'outline'}
              onClick={() => setSortOrder('desc')}
              fontSize={{ base: "xs", md: "sm" }}
              flex={{ base: 1, md: "none" }}
            >
              Yüksek → Düşük
            </Button>
            <Button
              variant={sortOrder === 'asc' ? 'solid' : 'outline'}
              onClick={() => setSortOrder('asc')}
              fontSize={{ base: "xs", md: "sm" }}
              flex={{ base: 1, md: "none" }}
            >
              Düşük → Yüksek
            </Button>
          </ButtonGroup>

          <Spacer display={{ base: "none", md: "block" }} />

          {/* Filters */}
          <HStack w={{ base: "full", md: "auto" }} justify={{ base: "center", md: "flex-end" }}>
            <FormControl display="flex" alignItems="center" w="auto">
              <FormLabel htmlFor="profitable-only" mb="0" fontSize="sm" minW="fit-content">
                Sadece Karlı
              </FormLabel>
              <Switch
                id="profitable-only"
                isChecked={showOnlyProfitable}
                onChange={(e) => setShowOnlyProfitable(e.target.checked)}
                size="sm"
              />
            </FormControl>
          </HStack>

          {/* Export Menu */}
          <Menu>
            <MenuButton as={IconButton} icon={<DownloadIcon />} size="sm" variant="outline">
            </MenuButton>
            <MenuList>
              <MenuItem>PNG olarak indir</MenuItem>
              <MenuItem>CSV olarak indir</MenuItem>
              <MenuItem>Excel olarak indir</MenuItem>
            </MenuList>
          </Menu>
        </Flex>
      </VStack>

      {/* Current Metric Info */}
      <HStack>
        <Badge colorScheme="blue" variant="subtle">
          {currentMetric.label}
        </Badge>
        <Text fontSize="sm" color="gray.500">
          {currentMetric.description}
        </Text>
        {selectedSymbols.length > 0 && (
          <Badge colorScheme="green" variant="subtle">
            {selectedSymbols.length} sembol seçili
          </Badge>
        )}
      </HStack>

      {/* Chart */}
      <Box h={{ base: `${Math.min(height, 300)}px`, md: `${height}px` }} w="100%" overflowX="auto">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={processedData}
            margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
            <XAxis
              dataKey="symbol"
              angle={-45}
              textAnchor="end"
              height={80}
              interval={0}
              fontSize={12}
              stroke={textColor}
            />
            <YAxis
              tickFormatter={currentMetric.formatValue}
              stroke={textColor}
              fontSize={12}
            />
            <RechartsTooltip content={<CustomTooltip />} />
            <Bar
              dataKey="value"
              cursor="pointer"
              onClick={handleBarClick}
              onMouseEnter={(data) => setHoveredBar(data.symbol)}
              onMouseLeave={() => setHoveredBar(null)}
            >
              {processedData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={
                    selectedSymbols.includes(entry.symbol)
                      ? '#4299E1' // Selected color
                      : hoveredBar === entry.symbol
                      ? '#63B3ED' // Hover color
                      : entry.color // Default color
                  }
                  stroke={selectedSymbols.includes(entry.symbol) ? '#2B6CB0' : 'none'}
                  strokeWidth={selectedSymbols.includes(entry.symbol) ? 2 : 0}
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </Box>

      {/* Summary Stats */}
      <HStack justify="space-between" fontSize="sm" color="gray.500">
        <Text>
          Toplam: {processedData.length} sembol
        </Text>
        <Text>
          En yüksek: {processedData.length > 0 ? currentMetric.formatValue(processedData[0].value) : 'N/A'}
        </Text>
        <Text>
          En düşük: {processedData.length > 0 ? currentMetric.formatValue(processedData[processedData.length - 1].value) : 'N/A'}
        </Text>
        <Text>
          Ortalama: {processedData.length > 0 ? 
            currentMetric.formatValue(processedData.reduce((sum, item) => sum + item.value, 0) / processedData.length) : 'N/A'}
        </Text>
      </HStack>
    </VStack>
  );
};

export default SymbolPerformanceBarChart;
