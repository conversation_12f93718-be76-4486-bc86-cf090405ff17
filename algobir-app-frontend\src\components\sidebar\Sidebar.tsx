import { 
  Box, 
  <PERSON><PERSON>, 
  <PERSON>er, 
  <PERSON>er<PERSON><PERSON>, 
  Drawer<PERSON>lose<PERSON>utton, 
  Drawer<PERSON>ontent, 
  DrawerOverlay, 
  Icon, 
  Button,
  useColorModeValue,
  BoxProps
} from '@chakra-ui/react';
import { IoMenuOutline } from 'react-icons/io5';
import SidebarContent from './components/Content';
import SidebarContentCollapsed from './components/ContentCollapsed';
import SubmenuPanel from './components/SubmenuPanel';
import { RouteType } from '../../routes';
import { TRANSITIONS, PERFORMANCE } from './animations';
import { Scrollbars } from 'react-custom-scrollbars-2';
import { useSidebar } from '../../context/SidebarContext';
import { useRef } from 'react';

interface SidebarProps extends BoxProps {
  routes: RouteType[];
}

// Horizon UI Scrollbar bileşenleri
const renderTrack = ({ ...props }) => {
  const trackStyle = {
    position: 'absolute',
    maxWidth: '100%',
    width: 6,
    transition: 'opacity 200ms ease 0s',
    opacity: 1, // Her zaman görünür hale getir
    background: 'rgba(0, 0, 0, 0.02)',
    bottom: 2,
    top: 2,
    borderRadius: 3,
    right: 2
  } as const;
  return <div style={trackStyle} {...props} />;
};

const renderThumb = ({ ...props }) => {
  const thumbStyle = {
    borderRadius: 15,
    background: 'rgba(112, 144, 176, 0.3)',
    transition: 'all 0.2s ease',
    '&:hover': {
      background: 'rgba(112, 144, 176, 0.5)'
    }
  } as const;
  return <div style={thumbStyle} {...props} />;
};

const renderView = ({ style, ...props }: any) => {
  const viewStyle = {
    ...style,
    marginRight: -17, // Scrollbar için sağ margin
    paddingRight: 17,
  } as const;
  return (
    <div style={viewStyle} {...props} />
  );
};

function Sidebar(props: SidebarProps) {
  const { routes, ...rest } = props;
  const {
    isMobile,
    isOpen,
    onClose,
    currentSidebarWidth,
    isCollapsed,
    hasActiveSubmenu,
    activeSubmenu,
    submenuItems,
    setSidebarHovered
  } = useSidebar();

  // Horizon UI renk değerleri
  const shadow = useColorModeValue(
    '14px 17px 40px 4px rgba(112, 144, 176, 0.08)',
    '14px 17px 40px 4px rgba(12, 44, 55, 0.18)'
  );
  const sidebarBg = useColorModeValue('white', 'navy.800');

  
  // Mobil cihazlarda drawer olarak göster - Enhanced with submenu support
  if (isMobile) {
    return (
      <Drawer
        isOpen={isOpen}
        placement="left"
        onClose={onClose}
        returnFocusOnClose={false}
        onOverlayClick={onClose}
        size="xs"
        autoFocus={false}
      >
        <DrawerOverlay
          bg="blackAlpha.300"
          backdropFilter="blur(10px)"
        />
        <DrawerContent
          w={{ base: "280px", sm: "300px" }}
          maxW={{ base: "280px", sm: "300px" }}
          bg={sidebarBg}
          boxShadow={shadow}
          borderRadius="0px 16px 16px 0px"
          transition={TRANSITIONS.submenu}
          overflowX="hidden"
          sx={{
            // Performance optimizations using sx prop
            willChange: PERFORMANCE.willChange,
            backfaceVisibility: PERFORMANCE.backfaceVisibility,
            // Ensure proper layering
            isolation: 'isolate',
          }}
        >
          <DrawerCloseButton
            zIndex='3'
            _focus={{ boxShadow: 'none' }}
            _hover={{ boxShadow: 'none' }}
            top="20px"
            right="20px"
          />
          <DrawerBody p="0" overflow="hidden">
            <Box overflowY="auto" height="100%">
              {/* Mobile always shows full content - no collapsed mode */}
              <SidebarContent routes={routes} />
            </Box>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    );
  }

  // Masaüstü versiyonu - Collapsible sidebar with submenu support
  return (
    <>
      {/* Main Sidebar - Enhanced responsive behavior with seamless integration */}
      <Box
        display={{ base: 'none', lg: 'block' }}
        position='fixed'
        left="0"
        top="0"
        minH='100vh'
        zIndex={1050} // Lower than navbar (1200) to prevent overlap
        {...rest}
      >
        <Box
          bg={sidebarBg}
          w={currentSidebarWidth}
          h="100vh"
          overflowX="hidden"
          overflowY="hidden"
          boxShadow={shadow}
          position="relative"
          borderRadius="0"
          transition={TRANSITIONS.sidebar}
          onMouseEnter={() => setSidebarHovered(true)}
          onMouseLeave={() => setSidebarHovered(false)}
          role="navigation"
          aria-label={isCollapsed ? "Daraltılmış ana navigasyon" : "Ana navigasyon"}
          aria-expanded={!isCollapsed}
          tabIndex={-1}
          data-testid="sidebar"
          sx={{
            // Performance optimizations
            willChange: PERFORMANCE.willChange,
            backfaceVisibility: PERFORMANCE.backfaceVisibility,
            // Ensure proper layering and eliminate gaps
            isolation: 'isolate',
            // Ensure no margin/padding interference
            margin: 0,
            padding: 0,
            // Prevent any potential gaps from box-sizing
            boxSizing: 'border-box',
            // Reduce motion for accessibility
            '@media (prefers-reduced-motion: reduce)': {
              transition: 'none'
            }
          }}
        >
          <Scrollbars
            autoHide={true} // Auto-hide for cleaner appearance
            hideTracksWhenNotNeeded={true}
            renderTrackVertical={renderTrack}
            renderThumbVertical={renderThumb}
            renderView={renderView}
            style={{
              height: '100vh',
              // Ensure scrollbar doesn't interfere with layout
              overflow: 'hidden'
            }}
          >
            {isCollapsed ? (
              <SidebarContentCollapsed routes={routes} />
            ) : (
              <SidebarContent routes={routes} />
            )}
          </Scrollbars>
        </Box>
      </Box>

      {/* Submenu Panel */}
      {hasActiveSubmenu && (
        <SubmenuPanel
          isOpen={hasActiveSubmenu}
          items={submenuItems}
          title={routes.find(r => r.path === activeSubmenu)?.name}
        />
      )}
    </>
  );
}

export function SidebarResponsive() {
  const { onOpen, isMobile } = useSidebar();
  const btnRef = useRef<HTMLButtonElement>(null);

  const handleOpen = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onOpen();
  };

  // Show on mobile and tablet (lg breakpoint and below)
  if (!isMobile) return null;
  
  return (
    <Flex display="flex" alignItems="center">
      <Button 
        ref={btnRef}
        variant="ghost"
        display="flex"
        alignItems="center"
        justifyContent="center"
        onClick={handleOpen}
        minW="44px"
        minH="44px"
        h="44px"
        w="44px"
        p={0}
        borderRadius="full"
        _hover={{ 
          bg: useColorModeValue('gray.100', 'whiteAlpha.100'),
          transform: 'scale(1.05)'
        }}
        _active={{ 
          bg: useColorModeValue('gray.200', 'whiteAlpha.200'),
          transform: 'scale(0.95)'
        }}
        transition="all 0.2s ease"
        aria-label="Menüyü aç"
        title="Menüyü aç"
      >
        <Icon
          as={IoMenuOutline}
          color={useColorModeValue('gray.700', 'white')}
          w="20px"
          h="20px"
          _hover={{ cursor: 'pointer' }}
        />
      </Button>
    </Flex>
  );
}

export default Sidebar; 