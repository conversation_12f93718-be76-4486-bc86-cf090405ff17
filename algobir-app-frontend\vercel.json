{"version": 2, "name": "algobir-app-frontend", "framework": "vite", "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm ci", "devCommand": "npm run dev", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"app/api/**/*.js": {"runtime": "nodejs18.x"}}, "regions": ["iad1"], "github": {"enabled": true, "autoAlias": false}}