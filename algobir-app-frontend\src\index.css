@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');

:root {
  font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

*:focus {
  outline: none;
}

body {
  min-height: 100vh;
  overflow-x: hidden;
  font-family: 'DM Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  letter-spacing: -0.025em;
  line-height: 1.6;
}

html {
  font-family: 'D<PERSON> Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  scroll-behavior: smooth;
}

/* Horizon UI scrollbar stilleri */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Karanlık tema için scrollbar */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

/* Selection stilleri */
::selection {
  background: rgba(66, 42, 251, 0.2);
  color: #422AFB;
}

/* Horizon UI animasyonları */
@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(10px);
  }
  to { 
    opacity: 1; 
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Horizon UI utility classes */
.glass-effect {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark-glass-effect {
  backdrop-filter: blur(20px);
  background: rgba(17, 28, 68, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive text utilities */
.text-responsive {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
}

.heading-responsive {
  font-size: clamp(1.5rem, 4vw, 2.25rem);
}

/* Focus ring utilities */
.focus-ring:focus {
  box-shadow: 0 0 0 3px rgba(66, 42, 251, 0.1);
  border-color: #422AFB;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
}

/* Horizon UI teması için gölge efektleri */
.shadow-card {
  box-shadow: 0px 3.5px 5.5px rgba(0, 0, 0, 0.02);
}

.shadow-navbar {
  box-shadow: 0px 7px 23px rgba(0, 0, 0, 0.05);
}

.shadow-button {
  box-shadow: 0px 3.5px 5.5px rgba(0, 0, 0, 0.02);
}

/* Karanlık temada kart stilleri */
.dark-mode .card {
  background: #111c44; /* navy.800 */
} 