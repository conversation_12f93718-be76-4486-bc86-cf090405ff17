import { Button, Flex, Link, Text, useColorModeValue, Box } from '@chakra-ui/react';

export default function SidebarPromotionCard() {
  const bgColor = 'linear-gradient(135deg, #868CFF 0%, #4318FF 100%)';
  const boxShadow = useColorModeValue('0px 18px 40px rgba(112, 144, 176, 0.12)', 'none');

  return (
    <Flex
      direction='column'
      bg={bgColor}
      borderRadius='20px'
      position='relative'
      p="20px 15px"
      boxShadow={boxShadow}
      overflow="hidden"
    >
      {/* Dekoratif daire */}
      <Box
        position="absolute"
        w="150px"
        h="150px"
        bg="linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)"
        borderRadius="full"
        top="-30px"
        right="-30px"
        zIndex="0"
      />
      
      <Flex direction='column' zIndex="1">
        <Text
          fontSize={{ base: 'lg', xl: '18px' }}
          color='white'
          fontWeight='bold'
          lineHeight='150%'
          mb='10px'
        >
          Pazaryerini Keşfedin
        </Text>
        <Text 
          fontSize='14px' 
          color='whiteAlpha.800'
          mb='20px'
          fontWeight="medium"
        >
          En iyi ticaret robotlarına göz atın ve hemen işlem yapmaya başlayın!
        </Text>
      </Flex>
      
      <Link href='/marketplace' _hover={{ textDecoration: 'none' }}>
        <Button
          bg='whiteAlpha.300'
          _hover={{ bg: 'whiteAlpha.400' }}
          _active={{ bg: 'whiteAlpha.500' }}
          color='white'
          fontWeight='medium'
          fontSize='sm'
          borderRadius="12px"
          w="100%"
        >
          Pazaryerine Git
        </Button>
      </Link>
    </Flex>
  );
} 