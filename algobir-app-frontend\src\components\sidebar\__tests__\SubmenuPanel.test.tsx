import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { Icon } from '@chakra-ui/react';
import { FiSettings, FiUser, FiHome, FiMail, FiPhone, FiCalendar, FiFile, FiFolder } from 'react-icons/fi';
import { vi } from 'vitest';
import SubmenuPanel from '../components/SubmenuPanel';
import { SidebarProvider, SubmenuItem } from '../../../context/SidebarContext';

// Mock react-custom-scrollbars-2
vi.mock('react-custom-scrollbars-2', () => ({
  Scrollbars: function MockScrollbars(props: any) {
    const { children, renderTrackVertical, renderThumbVertical, renderView, ...otherProps } = props;
    return React.createElement('div', {
      'data-testid': 'scrollbars',
      ref: props.ref,
      ...otherProps,
      style: { height: '100%', overflow: 'auto' }
    }, [
      React.createElement('div', { 'data-testid': 'scroll-track', key: 'track' },
        renderTrackVertical && renderTrackVertical({})),
      React.createElement('div', { 'data-testid': 'scroll-thumb', key: 'thumb' },
        renderThumbVertical && renderThumbVertical({})),
      React.createElement('div', { 'data-testid': 'scroll-view', key: 'view' },
        renderView && renderView({ style: {} })),
      children
    ]);
  }
}));

// Mock submenu items
const mockSubmenuItems: SubmenuItem[] = [
  {
    id: 'profile',
    name: 'Profile Settings',
    path: '/settings/profile',
    icon: <Icon as={FiUser} width="16px" height="16px" color="inherit" />,
    description: 'Manage your profile information'
  },
  {
    id: 'account',
    name: 'Account Settings',
    path: '/settings/account',
    icon: <Icon as={FiSettings} width="16px" height="16px" color="inherit" />,
    description: 'Manage your account preferences'
  }
];

// Large submenu items for testing scrolling
const largeSubmenuItems: SubmenuItem[] = [
  {
    id: 'home',
    name: 'Dashboard Home',
    path: '/dashboard/home',
    icon: <Icon as={FiHome} width="16px" height="16px" color="inherit" />,
    description: 'Main dashboard overview with key metrics and insights'
  },
  {
    id: 'profile',
    name: 'Profile Settings',
    path: '/settings/profile',
    icon: <Icon as={FiUser} width="16px" height="16px" color="inherit" />,
    description: 'Manage your personal profile information and preferences'
  },
  {
    id: 'account',
    name: 'Account Settings',
    path: '/settings/account',
    icon: <Icon as={FiSettings} width="16px" height="16px" color="inherit" />,
    description: 'Configure account security, billing, and general settings'
  },
  {
    id: 'mail',
    name: 'Email Management',
    path: '/communication/email',
    icon: <Icon as={FiMail} width="16px" height="16px" color="inherit" />,
    description: 'Manage email notifications, templates, and communication settings'
  },
  {
    id: 'phone',
    name: 'Phone Settings',
    path: '/communication/phone',
    icon: <Icon as={FiPhone} width="16px" height="16px" color="inherit" />,
    description: 'Configure phone numbers, SMS notifications, and call preferences'
  },
  {
    id: 'calendar',
    name: 'Calendar Integration',
    path: '/tools/calendar',
    icon: <Icon as={FiCalendar} width="16px" height="16px" color="inherit" />,
    description: 'Sync with external calendars and manage scheduling preferences'
  },
  {
    id: 'files',
    name: 'File Management',
    path: '/storage/files',
    icon: <Icon as={FiFile} width="16px" height="16px" color="inherit" />,
    description: 'Upload, organize, and manage your documents and media files'
  },
  {
    id: 'folders',
    name: 'Folder Organization',
    path: '/storage/folders',
    icon: <Icon as={FiFolder} width="16px" height="16px" color="inherit" />,
    description: 'Create and manage folder structures for better file organization'
  }
];

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ChakraProvider>
      <BrowserRouter>
        <SidebarProvider>
          {component}
        </SidebarProvider>
      </BrowserRouter>
    </ChakraProvider>
  );
};

describe('SubmenuPanel Component', () => {
  test('should not render when isOpen is false', () => {
    const { container } = renderWithProviders(
      <SubmenuPanel isOpen={false} items={mockSubmenuItems} title="Settings" />
    );
    
    expect(container.firstChild).toBeNull();
  });

  test('should render when isOpen is true', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Profile Settings')).toBeInTheDocument();
    expect(screen.getByText('Account Settings')).toBeInTheDocument();
  });

  test('should render submenu items with descriptions', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    expect(screen.getByText('Manage your profile information')).toBeInTheDocument();
    expect(screen.getByText('Manage your account preferences')).toBeInTheDocument();
  });

  test('should have proper ARIA attributes', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    const dialog = screen.getByRole('dialog');
    expect(dialog).toHaveAttribute('aria-modal', 'true');
    expect(dialog).toHaveAttribute('aria-label', 'Settings alt menüsü');
    expect(dialog).toHaveAttribute('aria-describedby', 'submenu-description');
  });

  test('should render close button with proper accessibility', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    const closeButton = screen.getByRole('button', { name: /alt menüyü kapat/i });
    expect(closeButton).toBeInTheDocument();
    expect(closeButton).toHaveAttribute('aria-label', 'Alt menüyü kapat (Escape tuşu)');
  });

  test('should render menu items with proper roles', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    const menuItems = screen.getAllByRole('menuitem');
    expect(menuItems).toHaveLength(2);
    
    menuItems.forEach(item => {
      expect(item).toHaveAttribute('tabIndex', '0');
    });
  });

  test('should handle close button click', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    const closeButton = screen.getByRole('button', { name: /alt menüyü kapat/i });
    fireEvent.click(closeButton);
    
    // The close action is handled by the SidebarContext
    // We can't directly test the state change here without mocking the context
  });

  test('should handle keyboard navigation', async () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    // Test Escape key
    fireEvent.keyDown(document, { key: 'Escape' });
    
    // The escape handler is attached to document
    // The actual closing is handled by the context
  });

  test('should focus first item when opened', async () => {
    const { rerender } = renderWithProviders(
      <SubmenuPanel isOpen={false} items={mockSubmenuItems} title="Settings" />
    );
    
    // Rerender with isOpen=true to trigger focus effect
    rerender(
      <ChakraProvider>
        <BrowserRouter>
          <SidebarProvider>
            <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
          </SidebarProvider>
        </BrowserRouter>
      </ChakraProvider>
    );
    
    // Wait for focus to be set (after animation delay)
    await waitFor(() => {
      const firstLink = screen.getByRole('link', { name: /profile settings/i });
      expect(firstLink).toBeInTheDocument();
    }, { timeout: 200 });
  });

  test('should render with default title when none provided', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} />
    );
    
    expect(screen.getByText('Menü')).toBeInTheDocument();
  });

  test('should handle empty items array', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={[]} title="Empty Menu" />
    );
    
    expect(screen.getByText('Empty Menu')).toBeInTheDocument();
    expect(screen.queryByRole('menuitem')).not.toBeInTheDocument();
  });

  test('should render items without icons', () => {
    const itemsWithoutIcons: SubmenuItem[] = [
      {
        id: 'no-icon',
        name: 'No Icon Item',
        path: '/no-icon',
        description: 'Item without icon'
      }
    ];
    
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={itemsWithoutIcons} title="Test" />
    );
    
    expect(screen.getByText('No Icon Item')).toBeInTheDocument();
    expect(screen.getByText('Item without icon')).toBeInTheDocument();
  });

  test('should have proper positioning styles', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );
    
    const panel = screen.getByRole('dialog');
    expect(panel).toHaveStyle({
      position: 'fixed',
      left: '70px',
      top: '0',
      height: '100vh',
      width: '280px'
    });
  });

  test('should handle menu item clicks and close submenu', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );

    const profileLink = screen.getByRole('link', { name: /profile settings/i });
    fireEvent.click(profileLink);

    // The click should trigger navigation and close the submenu
    // This is handled by the onClick handler in the component
  });

  test('should apply hover styles to menu items', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );

    const menuItems = screen.getAllByRole('menuitem');
    const firstItem = menuItems[0];

    // Test hover interaction
    fireEvent.mouseEnter(firstItem);
    fireEvent.mouseLeave(firstItem);

    // Hover styles are applied via CSS, so we just verify the element exists
    expect(firstItem).toBeInTheDocument();
  });

  test('should handle focus events properly', () => {
    renderWithProviders(
      <SubmenuPanel isOpen={true} items={mockSubmenuItems} title="Settings" />
    );

    const menuItems = screen.getAllByRole('menuitem');
    const firstItem = menuItems[0];

    fireEvent.focus(firstItem);
    expect(firstItem).toHaveFocus();

    fireEvent.blur(firstItem);
    expect(firstItem).not.toHaveFocus();
  });

  // Scrolling functionality tests
  describe('Scrolling Functionality', () => {
    test('should render Scrollbars component when submenu is open', () => {
      renderWithProviders(
        <SubmenuPanel isOpen={true} items={largeSubmenuItems} title="Large Menu" />
      );

      expect(screen.getByTestId('scrollbars')).toBeInTheDocument();
      expect(screen.getByTestId('scroll-track')).toBeInTheDocument();
      expect(screen.getByTestId('scroll-thumb')).toBeInTheDocument();
      expect(screen.getByTestId('scroll-view')).toBeInTheDocument();
    });

    test('should render all items in large submenu', () => {
      renderWithProviders(
        <SubmenuPanel isOpen={true} items={largeSubmenuItems} title="Large Menu" />
      );

      // Check that all items are rendered
      expect(screen.getByText('Dashboard Home')).toBeInTheDocument();
      expect(screen.getByText('Profile Settings')).toBeInTheDocument();
      expect(screen.getByText('Account Settings')).toBeInTheDocument();
      expect(screen.getByText('Email Management')).toBeInTheDocument();
      expect(screen.getByText('Phone Settings')).toBeInTheDocument();
      expect(screen.getByText('Calendar Integration')).toBeInTheDocument();
      expect(screen.getByText('File Management')).toBeInTheDocument();
      expect(screen.getByText('Folder Organization')).toBeInTheDocument();

      // Verify we have the expected number of menu items
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems).toHaveLength(8);
    });

    test('should have proper scrollable container structure', () => {
      renderWithProviders(
        <SubmenuPanel isOpen={true} items={largeSubmenuItems} title="Large Menu" />
      );

      const scrollbars = screen.getByTestId('scrollbars');
      expect(scrollbars).toHaveStyle({
        height: '100%',
        overflow: 'auto'
      });
    });

    test('should handle keyboard navigation with arrow keys', () => {
      renderWithProviders(
        <SubmenuPanel isOpen={true} items={largeSubmenuItems} title="Large Menu" />
      );

      // Test arrow down navigation
      fireEvent.keyDown(document, { key: 'ArrowDown' });
      fireEvent.keyDown(document, { key: 'ArrowUp' });

      // The actual focus management and scrolling is complex to test
      // without a real DOM environment, but we can verify the event handlers exist
      expect(screen.getByTestId('scrollbars')).toBeInTheDocument();
    });

    test('should maintain header position when scrolling', () => {
      renderWithProviders(
        <SubmenuPanel isOpen={true} items={largeSubmenuItems} title="Large Menu" />
      );

      // The header should be outside the scrollable area
      const title = screen.getByText('Large Menu');
      const closeButton = screen.getByRole('button', { name: /alt menüyü kapat/i });

      expect(title).toBeInTheDocument();
      expect(closeButton).toBeInTheDocument();

      // Header should not be inside the scrollable container
      const scrollbars = screen.getByTestId('scrollbars');
      expect(scrollbars).not.toContainElement(title);
      expect(scrollbars).not.toContainElement(closeButton);
    });

    test('should render custom scrollbar components', () => {
      renderWithProviders(
        <SubmenuPanel isOpen={true} items={largeSubmenuItems} title="Large Menu" />
      );

      // Verify custom scrollbar elements are rendered
      expect(screen.getByTestId('scroll-track')).toBeInTheDocument();
      expect(screen.getByTestId('scroll-thumb')).toBeInTheDocument();
      expect(screen.getByTestId('scroll-view')).toBeInTheDocument();
    });

    test('should handle empty items with scrollbar', () => {
      renderWithProviders(
        <SubmenuPanel isOpen={true} items={[]} title="Empty Scrollable Menu" />
      );

      expect(screen.getByText('Empty Scrollable Menu')).toBeInTheDocument();
      expect(screen.getByTestId('scrollbars')).toBeInTheDocument();
      expect(screen.queryByRole('menuitem')).not.toBeInTheDocument();
    });

    test('should preserve accessibility with scrolling', () => {
      renderWithProviders(
        <SubmenuPanel isOpen={true} items={largeSubmenuItems} title="Large Menu" />
      );

      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-modal', 'true');
      expect(dialog).toHaveAttribute('aria-label', 'Large Menu alt menüsü');
      expect(dialog).toHaveAttribute('aria-describedby', 'submenu-description');

      // All menu items should still have proper roles
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems).toHaveLength(8);

      menuItems.forEach(item => {
        expect(item).toHaveAttribute('tabIndex', '0');
        expect(item).toHaveAttribute('role', 'menuitem');
      });
    });

    test('should handle scroll container mouse events', () => {
      renderWithProviders(
        <SubmenuPanel isOpen={true} items={largeSubmenuItems} title="Large Menu" />
      );

      const dialog = screen.getByRole('dialog');

      // Test mouse enter/leave events on the panel
      fireEvent.mouseEnter(dialog);
      fireEvent.mouseLeave(dialog);

      // These events should not cause errors
      expect(dialog).toBeInTheDocument();
    });
  });
});
