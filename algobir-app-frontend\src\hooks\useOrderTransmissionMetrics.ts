import { useEffect, useRef, useState } from 'react';
import { supabase } from '../supabaseClient';

export interface OrderTransmissionMetric {
  id: string;
  trade_id: number | null;
  signal_type: string;
  symbol: string;
  order_side: string;
  json_parsing_time_ms: number;
  transformation_time_ms: number;
  webhook_delivery_time_ms: number | null;
  total_processing_time_ms: number;
  signal_source: 'solo-robot' | 'bro-robot';
  processing_status: 'success' | 'partial' | 'failed';
  created_at: string;
  signal_received_at: string;
  processing_completed_at: string;
}

export interface OrderTransmissionStats {
  total_signals: number;
  avg_json_parsing_time_ms: number;
  avg_transformation_time_ms: number;
  avg_webhook_delivery_time_ms: number;
  avg_total_processing_time_ms: number;
  p95_total_processing_time_ms: number;
  p99_total_processing_time_ms: number;
  success_rate: number;
  signals_per_minute: number;
  fastest_processing_time_ms: number;
  slowest_processing_time_ms: number;
  solo_robot_count: number;
  bro_robot_count: number;
}

interface UseOrderTransmissionMetricsProps {
  timeRangeHours?: number;
  signalSource?: 'solo-robot' | 'bro-robot' | null;
  limit?: number;
  enabled?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

interface UseOrderTransmissionMetricsReturn {
  metrics: OrderTransmissionMetric[];
  stats: OrderTransmissionStats | null;
  loading: boolean;
  error: string | null;
  isConnected: boolean;
  lastUpdate: Date | null;
  refetch: () => Promise<void>;
}

export const useOrderTransmissionMetrics = ({
  timeRangeHours = 24,
  signalSource = null,
  limit = 100,
  enabled = true,
  autoRefresh = true,
  refreshInterval = 30000 // 30 seconds
}: UseOrderTransmissionMetricsProps = {}): UseOrderTransmissionMetricsReturn => {
  const [metrics, setMetrics] = useState<OrderTransmissionMetric[]>([]);
  const [stats, setStats] = useState<OrderTransmissionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const subscriptionRef = useRef<any>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch metrics data
  const fetchMetrics = async () => {
    try {
      setError(null);
      
      // Fetch recent metrics
      const { data: metricsData, error: metricsError } = await supabase.rpc(
        'get_order_transmission_metrics',
        {
          p_time_range_hours: timeRangeHours,
          p_signal_source: signalSource,
          p_limit: limit
        }
      );

      if (metricsError) throw metricsError;

      // Fetch aggregated stats
      const { data: statsData, error: statsError } = await supabase.rpc(
        'get_order_transmission_stats',
        {
          p_time_range_hours: timeRangeHours
        }
      );

      if (statsError) throw statsError;

      setMetrics(metricsData || []);
      setStats(statsData?.[0] || null);
      setLastUpdate(new Date());
      
    } catch (err: any) {
      console.error('Error fetching order transmission metrics:', err);
      setError(err.message || 'Failed to fetch metrics');
    }
  };

  // Set up real-time subscription
  useEffect(() => {
    const setupRealtimeSubscription = async () => {
      if (!enabled) {
        setIsConnected(false);
        return;
      }

      try {
        // Subscribe to order_transmission_metrics table changes
        const subscription = supabase
          .channel('order_transmission_metrics_realtime')
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'order_transmission_metrics'
            },
            (payload) => {
              console.log('Real-time order transmission metric update:', payload);
              // Refetch data when new metrics are added
              fetchMetrics();
            }
          )
          .subscribe((status) => {
            console.log('Order transmission metrics subscription status:', status);
            setIsConnected(status === 'SUBSCRIBED');
          });

        subscriptionRef.current = subscription;

        // Set up periodic refresh if enabled
        if (autoRefresh && refreshInterval > 0) {
          intervalRef.current = setInterval(() => {
            fetchMetrics();
          }, refreshInterval);
        }

      } catch (error) {
        console.error('Error setting up real-time subscription:', error);
        setIsConnected(false);
      }
    };

    if (enabled) {
      // Initial fetch
      setLoading(true);
      fetchMetrics().finally(() => setLoading(false));
    }

    // Setup real-time subscription
    setupRealtimeSubscription();

    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [enabled, timeRangeHours, signalSource, limit, autoRefresh, refreshInterval]);

  // Refetch function for manual refresh
  const refetch = async () => {
    setLoading(true);
    await fetchMetrics();
    setLoading(false);
  };

  return {
    metrics,
    stats,
    loading,
    error,
    isConnected,
    lastUpdate,
    refetch
  };
};

export default useOrderTransmissionMetrics;
