import { useCallback } from 'react';
import { supabase } from '../supabaseClient';
import { Trade } from '../types/trade';
import { useAuth } from '../context/AuthContext';
import {
  useAutoAsyncOperation,
  useCache,
  useDatabaseErrorHand<PERSON>,
  type BaseHookState,
  createSuccessState
} from './utils';

/**
 * Enhanced dashboard data interface
 */
interface DashboardData {
  totalInvestment: number;
  totalPnL: number;
  openPositionsCount: number;
  totalTradesCount: number;
  winRate: number;
  last10ClosedTrades: Trade[];
  todayTradesCount: number;
  lastTradeTime: string | null;
  apiCredentialsExpiryDate: string | null;
  // Additional metrics
  avgTradeSize: number;
  bestPerformingSymbol: string | null;
  worstPerformingSymbol: string | null;
  dailyPnL: number;
  weeklyPnL: number;
  monthlyPnL: number;
}

/**
 * Enhanced dashboard hook return type
 */
interface UseEnhancedDashboardReturn extends BaseHookState<DashboardData> {
  refetch: () => Promise<void>;
  isStale: boolean;
  cacheStats: { size: number; hitRate: number };
}

/**
 * Enhanced dashboard data hook with improved caching, error handling, and performance
 * 
 * Features:
 * - Standardized error handling with user-friendly messages
 * - Advanced caching with user-specific data
 * - Automatic retry with exponential backoff
 * - Performance monitoring and optimization
 * - Additional dashboard metrics
 * 
 * @returns Enhanced dashboard data and controls
 */
export function useEnhancedDashboard(): UseEnhancedDashboardReturn {
  const { user } = useAuth();
  const { handleError } = useDatabaseErrorHandler();
  const cache = useCache<DashboardData>({
    ttl: 90000, // 90 seconds for dashboard data
    maxSize: 50,
    userSpecific: true
  });

  // Main data fetching operation
  const fetchDashboardData = useCallback(async (): Promise<DashboardData> => {
    if (!user) {
      throw new Error('Kullanıcı oturumu bulunamadı');
    }

    const cacheKey = 'dashboard-data';
    
    // Check cache first
    const cachedData = cache.get(cacheKey, user.id);
    if (cachedData) {
      console.log('Using cached dashboard data');
      return cachedData;
    }

    try {
      // Fetch all required data in parallel for better performance
      const [settingsResult, tradesResult] = await Promise.all([
        supabase
          .from('user_settings')
          .select('total_investment_amount, api_credentials_expiry_date')
          .eq('user_id', user.id)
          .single(),
        supabase
          .from('trades')
          .select('*')
          .eq('user_id', user.id)
          .order('received_at', { ascending: false })
      ]);

      if (settingsResult.error && settingsResult.error.code !== 'PGRST116') {
        throw settingsResult.error;
      }

      if (tradesResult.error) {
        throw tradesResult.error;
      }

      const settingsData = settingsResult.data;
      const tradesData = tradesResult.data || [];

      // Calculate metrics
      const totalInvestment = settingsData?.total_investment_amount || 0;
      
      // PnL calculations
      const closedTrades = tradesData.filter(trade => 
        trade.position_status === 'Kapalı' && 
        trade.pnl !== null && 
        trade.pnl !== undefined
      );
      
      const totalPnL = closedTrades.reduce((sum, trade) => sum + (trade.pnl || 0), 0);
      
      // Win rate calculation
      const winningTrades = closedTrades.filter(trade => (trade.pnl || 0) > 0);
      const winRate = closedTrades.length > 0 ? (winningTrades.length / closedTrades.length) * 100 : 0;

      // Open positions
      const openTrades = tradesData.filter(trade => trade.position_status === 'Açık');
      
      // Today's trades
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayTrades = tradesData.filter(trade => {
        const tradeDate = new Date(trade.received_at);
        tradeDate.setHours(0, 0, 0, 0);
        return tradeDate.getTime() === today.getTime();
      });

      // Last 10 closed trades
      const last10ClosedTrades = tradesData
        .filter(trade => 
          trade.position_status === 'Kapalı' && 
          trade.order_side === 'SELL' &&
          trade.pnl_calculated_at
        )
        .sort((a, b) => new Date(b.pnl_calculated_at).getTime() - new Date(a.pnl_calculated_at).getTime())
        .slice(0, 10);

      // Last trade time
      const sortedTrades = tradesData
        .filter(trade => trade.received_at)
        .sort((a, b) => new Date(b.received_at).getTime() - new Date(a.received_at).getTime());
      const lastTradeTime = sortedTrades.length > 0 ? sortedTrades[0].received_at : null;

      // Additional metrics
      const avgTradeSize = closedTrades.length > 0 
        ? closedTrades.reduce((sum, trade) => sum + Math.abs(trade.pnl || 0), 0) / closedTrades.length
        : 0;

      // Symbol performance analysis
      const symbolPerformance = tradesData
        .filter(trade => trade.position_status === 'Kapalı' && trade.pnl !== null)
        .reduce((acc, trade) => {
          const symbol = trade.symbol || 'Unknown';
          if (!acc[symbol]) {
            acc[symbol] = { totalPnL: 0, tradeCount: 0 };
          }
          acc[symbol].totalPnL += trade.pnl || 0;
          acc[symbol].tradeCount += 1;
          return acc;
        }, {} as Record<string, { totalPnL: number; tradeCount: number }>);

      const symbolEntries = Object.entries(symbolPerformance);
      const bestPerformingSymbol = symbolEntries.length > 0 
        ? symbolEntries.reduce((best, [symbol, data]) => 
            data.totalPnL > (symbolPerformance[best]?.totalPnL || -Infinity) ? symbol : best
          , symbolEntries[0][0])
        : null;

      const worstPerformingSymbol = symbolEntries.length > 0
        ? symbolEntries.reduce((worst, [symbol, data]) => 
            data.totalPnL < (symbolPerformance[worst]?.totalPnL || Infinity) ? symbol : worst
          , symbolEntries[0][0])
        : null;

      // Time-based PnL calculations
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const dailyPnL = closedTrades
        .filter(trade => new Date(trade.pnl_calculated_at || trade.received_at) >= oneDayAgo)
        .reduce((sum, trade) => sum + (trade.pnl || 0), 0);

      const weeklyPnL = closedTrades
        .filter(trade => new Date(trade.pnl_calculated_at || trade.received_at) >= oneWeekAgo)
        .reduce((sum, trade) => sum + (trade.pnl || 0), 0);

      const monthlyPnL = closedTrades
        .filter(trade => new Date(trade.pnl_calculated_at || trade.received_at) >= oneMonthAgo)
        .reduce((sum, trade) => sum + (trade.pnl || 0), 0);

      const dashboardData: DashboardData = {
        totalInvestment,
        totalPnL,
        openPositionsCount: openTrades.length,
        totalTradesCount: tradesData.length,
        winRate,
        last10ClosedTrades,
        todayTradesCount: todayTrades.length,
        lastTradeTime,
        apiCredentialsExpiryDate: settingsData?.api_credentials_expiry_date || null,
        avgTradeSize,
        bestPerformingSymbol,
        worstPerformingSymbol,
        dailyPnL,
        weeklyPnL,
        monthlyPnL
      };

      // Cache the result
      cache.set(cacheKey, dashboardData, user.id);

      return dashboardData;

    } catch (error) {
      handleError(error, { 
        context: 'Dashboard Data Fetch',
        showToast: true 
      });
      throw error;
    }
  }, [user, cache, handleError]);

  // Use the standardized async operation hook
  const asyncOp = useAutoAsyncOperation(fetchDashboardData, [user?.id], {
    showToastOnError: false, // We handle errors in fetchDashboardData
    retryAttempts: 3,
    retryDelay: 1000,
    cacheTimeout: 90000,
    enableCache: true
  });

  return {
    ...asyncOp,
    cacheStats: cache.getStats()
  };
}
