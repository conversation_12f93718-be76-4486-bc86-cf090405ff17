import React from 'react';
import { Card, useColorModeValue, CardProps } from '@chakra-ui/react';

interface Props extends CardProps {
  children: React.ReactNode;
  delay?: number;
}

/**
 * Animasyonlu kutu bileşeni
 */
const CardWithAnimation: React.FC<Props> = ({ children, delay = 0, ...props }) => {
  // Animasyon stili
  const animationStyle = { 
    animationDelay: `${delay * 0.1}s`,
    opacity: 0,
    animationName: 'fadeIn',
    animationDuration: '0.5s',
    animationFillMode: 'forwards',
    animationTimingFunction: 'ease-out'
  };

  // Global stil ekle
  React.useEffect(() => {
    if (typeof document !== 'undefined' && !document.getElementById('card-animation-style')) {
      const style = document.createElement('style');
      style.id = 'card-animation-style';
      style.innerHTML = `
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(15px); }
          to { opacity: 1; transform: translateY(0); }
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  // Horizon UI'daki Card stillerine göre düzenleyelim
  const cardBg = useColorModeValue('white', 'navy.800');
  const cardShadow = useColorModeValue(
    '14px 17px 40px 4px rgba(112, 144, 176, 0.18)', 
    'unset'
  );
  const cardBorder = useColorModeValue('transparent', 'rgba(255, 255, 255, 0.15)');

  return (
    <Card 
      style={animationStyle}
      bg={cardBg}
      boxShadow={cardShadow}
      borderRadius="20px"
      border="1px solid"
      borderColor={cardBorder}
      display="flex"
      flexDirection="column"
      width="100%"
      position="relative"
      minWidth="0px"
      overflow="hidden"
      backgroundClip="border-box"
      p={props.p || "20px"}
      {...props}
    >
      {children}
    </Card>
  );
};

export default CardWithAnimation; 