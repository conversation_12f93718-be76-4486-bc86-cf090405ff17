---
type: "manual"
priority: "high"
scope: ["statistics", "analytics", "dashboards", "charts", "reports"]
last_updated: "2025-01-29"
---

# Statistics & Analytics Features

## 📊 Statistics Architecture

### Dashboard Structure
```typescript
// Statistics page with comprehensive analytics
// Route: /statistics with nested sub-routes

interface StatisticsArchitecture {
  mainRoute: '/statistics';
  subRoutes: {
    overview: '/statistics/overview';           // General overview
    performance: '/statistics/performance';     // Performance analytics
    soloRobot: '/statistics/solo-robot';       // Solo-robot specific
    broRobots: '/statistics/bro-robots';       // Bro-robots specific
    robotComparison: '/statistics/robot-comparison';
    riskAnalysis: '/statistics/risk-analysis';
    timeAnalysis: '/statistics/time-analysis';
    symbolAnalysis: '/statistics/symbol-analysis';
    reports: '/statistics/reports';            // PDF reports
  };
}

// Component Structure
src/components/statistics/
├── dashboards/
│   ├── OverviewDashboard.tsx         # General overview
│   ├── PerformanceDashboard.tsx      # Performance analytics
│   ├── RiskDashboard.tsx             # Risk analysis
│   ├── RobotDashboard.tsx            # Robot-specific analytics
│   ├── ROIDashboard.tsx              # ROI analysis
│   └── ComparativeDashboard.tsx      # Robot comparison
├── charts/
│   ├── ProfitLossChart.tsx           # P&L visualization
│   ├── ROIChart.tsx                  # ROI trends
│   ├── VolumeChart.tsx               # Trading volume
│   ├── DrawdownChart.tsx             # Drawdown analysis
│   └── CorrelationMatrix.tsx         # Symbol correlation
└── reports/
    ├── PDFReportGenerator.tsx        # PDF generation
    ├── ReportTemplates.tsx           # Report layouts
    └── ExportUtils.tsx               # Data export utilities
```

### Real-time Data Integration
```typescript
// Real-time statistics updates
const useRealTimeStatistics = (userId: string, filters: StatisticsFilters) => {
  const [statistics, setStatistics] = useState<StatisticsData>({});
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  
  // Initial data fetch
  useEffect(() => {
    fetchStatistics();
  }, [filters]);
  
  // Real-time subscription
  useEffect(() => {
    const subscription = supabase
      .channel('statistics_updates')
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'trades' },
        (payload) => {
          if (payload.new.user_id === userId) {
            updateStatisticsRealTime(payload.new);
            setLastUpdate(new Date());
          }
        }
      )
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'order_transmission_metrics' },
        (payload) => {
          updatePerformanceMetrics(payload.new);
        }
      )
      .subscribe();
    
    return () => subscription.unsubscribe();
  }, [userId]);
  
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const data = await supabase.rpc('get_user_statistics', {
        p_user_id: userId,
        p_date_from: filters.dateFrom,
        p_date_to: filters.dateTo,
        p_robot_ids: filters.robotIds
      });
      
      setStatistics(data);
    } catch (error) {
      console.error('Statistics fetch error:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return { statistics, loading, lastUpdate, refetch: fetchStatistics };
};
```

## 📈 Performance Analytics

### ROI Analysis Dashboard
```typescript
// ROI calculation and visualization
interface ROIAnalytics {
  totalInvestment: number;
  currentValue: number;
  totalReturn: number;
  returnPercentage: number;
  dailyReturns: DailyReturn[];
  monthlyReturns: MonthlyReturn[];
  yearlyReturns: YearlyReturn[];
  benchmarkComparison: BenchmarkData;
}

// ROI Chart Component
const ROIChart = ({ data, period }: { data: ROIAnalytics; period: string }) => {
  const chartData = useMemo(() => {
    switch (period) {
      case 'daily':
        return data.dailyReturns.map(item => ({
          date: item.date,
          roi: item.cumulativeReturn,
          benchmark: item.benchmarkReturn
        }));
      case 'monthly':
        return data.monthlyReturns.map(item => ({
          date: item.month,
          roi: item.monthlyReturn,
          benchmark: item.benchmarkReturn
        }));
      default:
        return data.dailyReturns;
    }
  }, [data, period]);
  
  return (
    <ResponsiveContainer width="100%" height={400}>
      <LineChart data={chartData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis 
          dataKey="date" 
          tickFormatter={(date) => format(new Date(date), 'dd/MM')}
        />
        <YAxis tickFormatter={(value) => `${value}%`} />
        <Tooltip 
          labelFormatter={(date) => format(new Date(date), 'dd MMMM yyyy')}
          formatter={(value: number, name: string) => [
            `${value.toFixed(2)}%`, 
            name === 'roi' ? 'Portföy Getirisi' : 'Benchmark'
          ]}
        />
        <Line 
          type="monotone" 
          dataKey="roi" 
          stroke="#38B2AC" 
          strokeWidth={2}
          name="roi"
        />
        <Line 
          type="monotone" 
          dataKey="benchmark" 
          stroke="#E53E3E" 
          strokeWidth={1}
          strokeDasharray="5 5"
          name="benchmark"
        />
      </LineChart>
    </ResponsiveContainer>
  );
};
```

### Performance Metrics
```typescript
// Key performance indicators
interface PerformanceMetrics {
  // Profitability
  totalProfit: number;
  totalLoss: number;
  netProfit: number;
  profitFactor: number;
  
  // Win/Loss Ratios
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  
  // Risk Metrics
  maxDrawdown: number;
  maxDrawdownDuration: number;
  sharpeRatio: number;
  sortinoRatio: number;
  
  // Trading Frequency
  avgTradesPerDay: number;
  avgTradesPerMonth: number;
  
  // Order Execution
  avgOrderTransmissionTime: number;
  orderSuccessRate: number;
}

// Performance Metrics Display
const PerformanceMetricsGrid = ({ metrics }: { metrics: PerformanceMetrics }) => (
  <SimpleGrid columns={{ base: 2, md: 4, lg: 6 }} spacing={6}>
    <StatCard
      label="Net Kar"
      value={formatCurrency(metrics.netProfit)}
      color={metrics.netProfit >= 0 ? 'green' : 'red'}
      icon={metrics.netProfit >= 0 ? TrendingUpIcon : TrendingDownIcon}
    />
    
    <StatCard
      label="Kazanma Oranı"
      value={`${metrics.winRate.toFixed(1)}%`}
      color={metrics.winRate >= 50 ? 'green' : 'orange'}
    />
    
    <StatCard
      label="Kar Faktörü"
      value={metrics.profitFactor.toFixed(2)}
      color={metrics.profitFactor >= 1 ? 'green' : 'red'}
    />
    
    <StatCard
      label="Maksimum Düşüş"
      value={`${metrics.maxDrawdown.toFixed(2)}%`}
      color="red"
    />
    
    <StatCard
      label="Sharpe Oranı"
      value={metrics.sharpeRatio.toFixed(2)}
      color={metrics.sharpeRatio >= 1 ? 'green' : 'orange'}
    />
    
    <StatCard
      label="Emir İletim Hızı"
      value={`${metrics.avgOrderTransmissionTime.toFixed(0)}ms`}
      color={metrics.avgOrderTransmissionTime <= 1000 ? 'green' : 'orange'}
    />
  </SimpleGrid>
);
```

## 🤖 Robot-Specific Analytics

### Solo-Robot Statistics
```typescript
// Solo-robot performance tracking
const SoloRobotAnalytics = ({ userId }: { userId: string }) => {
  const [robotStats, setRobotStats] = useState<SoloRobotStats>({});
  
  useEffect(() => {
    const fetchSoloRobotStats = async () => {
      const { data } = await supabase.rpc('get_solo_robot_statistics', {
        p_user_id: userId
      });
      
      setRobotStats(data);
    };
    
    fetchSoloRobotStats();
  }, [userId]);
  
  return (
    <VStack spacing={6} align="stretch">
      {/* Performance Overview */}
      <Card>
        <CardHeader>
          <Heading size="md">Solo-Robot Performansı</Heading>
        </CardHeader>
        <CardBody>
          <PerformanceMetricsGrid metrics={robotStats.performance} />
        </CardBody>
      </Card>
      
      {/* Trading Activity */}
      <Card>
        <CardHeader>
          <Heading size="md">İşlem Aktivitesi</Heading>
        </CardHeader>
        <CardBody>
          <TradingActivityChart data={robotStats.tradingActivity} />
        </CardBody>
      </Card>
      
      {/* Symbol Distribution */}
      <Card>
        <CardHeader>
          <Heading size="md">Sembol Dağılımı</Heading>
        </CardHeader>
        <CardBody>
          <SymbolDistributionChart data={robotStats.symbolDistribution} />
        </CardBody>
      </Card>
    </VStack>
  );
};
```

### Bro-Robots Statistics
```typescript
// Bro-robot subscription and performance analytics
const BroRobotsAnalytics = ({ userId }: { userId: string }) => {
  const [subscriptions, setSubscriptions] = useState<BroRobotSubscription[]>([]);
  const [aggregateStats, setAggregateStats] = useState<AggregateStats>({});
  
  useEffect(() => {
    const fetchBroRobotStats = async () => {
      const { data: subsData } = await supabase
        .from('subscriptions')
        .select(`
          *,
          robots (
            id, name, performance_data,
            profiles:seller_id (username)
          )
        `)
        .eq('user_id', userId)
        .eq('is_active', true);
      
      setSubscriptions(subsData || []);
      
      // Fetch aggregate statistics
      const { data: aggData } = await supabase.rpc('get_bro_robot_aggregate_stats', {
        p_user_id: userId
      });
      
      setAggregateStats(aggData);
    };
    
    fetchBroRobotStats();
  }, [userId]);
  
  return (
    <VStack spacing={6} align="stretch">
      {/* Subscription Overview */}
      <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
        <StatCard
          label="Aktif Abonelik"
          value={subscriptions.length.toString()}
          color="blue"
        />
        <StatCard
          label="Toplam Aylık Maliyet"
          value={formatCurrency(aggregateStats.totalMonthlyCost)}
          color="orange"
        />
        <StatCard
          label="Ortalama Robot Performansı"
          value={`${aggregateStats.avgPerformance?.toFixed(2)}%`}
          color={aggregateStats.avgPerformance >= 0 ? 'green' : 'red'}
        />
      </SimpleGrid>
      
      {/* Individual Robot Performance */}
      <Card>
        <CardHeader>
          <Heading size="md">Robot Performans Karşılaştırması</Heading>
        </CardHeader>
        <CardBody>
          <RobotComparisonChart subscriptions={subscriptions} />
        </CardBody>
      </Card>
    </VStack>
  );
};
```

## 📊 Advanced Analytics

### Risk Analysis
```typescript
// Risk metrics and analysis
interface RiskAnalytics {
  valueAtRisk: number;           // VaR at 95% confidence
  conditionalVaR: number;        // Expected shortfall
  beta: number;                  // Market beta
  volatility: number;            // Portfolio volatility
  correlationMatrix: CorrelationData[];
  riskContribution: RiskContribution[];
}

// Risk Dashboard Component
const RiskAnalysisDashboard = ({ userId }: { userId: string }) => {
  const [riskData, setRiskData] = useState<RiskAnalytics>({});
  
  return (
    <VStack spacing={6} align="stretch">
      {/* Risk Metrics */}
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={6}>
        <StatCard
          label="Value at Risk (95%)"
          value={formatCurrency(riskData.valueAtRisk)}
          color="red"
        />
        <StatCard
          label="Conditional VaR"
          value={formatCurrency(riskData.conditionalVaR)}
          color="red"
        />
        <StatCard
          label="Beta"
          value={riskData.beta?.toFixed(2)}
          color={riskData.beta > 1 ? 'red' : 'green'}
        />
        <StatCard
          label="Volatilite"
          value={`${riskData.volatility?.toFixed(2)}%`}
          color="orange"
        />
      </SimpleGrid>
      
      {/* Correlation Matrix */}
      <Card>
        <CardHeader>
          <Heading size="md">Sembol Korelasyon Matrisi</Heading>
        </CardHeader>
        <CardBody>
          <CorrelationMatrix data={riskData.correlationMatrix} />
        </CardBody>
      </Card>
      
      {/* Risk Contribution */}
      <Card>
        <CardHeader>
          <Heading size="md">Risk Katkısı</Heading>
        </CardHeader>
        <CardBody>
          <RiskContributionChart data={riskData.riskContribution} />
        </CardBody>
      </Card>
    </VStack>
  );
};
```

### Time Analysis
```typescript
// Time-based trading analysis
const TimeAnalysisDashboard = ({ userId }: { userId: string }) => {
  const [timeData, setTimeData] = useState<TimeAnalysisData>({});
  
  return (
    <VStack spacing={6} align="stretch">
      {/* Hourly Performance */}
      <Card>
        <CardHeader>
          <Heading size="md">Saatlik Performans Dağılımı</Heading>
        </CardHeader>
        <CardBody>
          <HourlyPerformanceChart data={timeData.hourlyPerformance} />
        </CardBody>
      </Card>
      
      {/* Daily Performance */}
      <Card>
        <CardHeader>
          <Heading size="md">Günlük Performans Dağılımı</Heading>
        </CardHeader>
        <CardBody>
          <DailyPerformanceChart data={timeData.dailyPerformance} />
        </CardBody>
      </Card>
      
      {/* Monthly Seasonality */}
      <Card>
        <CardHeader>
          <Heading size="md">Aylık Mevsimsellik</Heading>
        </CardHeader>
        <CardBody>
          <MonthlySeasonalityChart data={timeData.monthlySeasonality} />
        </CardBody>
      </Card>
    </VStack>
  );
};
```

## 📄 Report Generation

### PDF Report System
```typescript
// PDF report generation with jsPDF
import jsPDF from 'jspdf';
import 'jspdf-autotable';

const generatePerformanceReport = async (userId: string, period: string) => {
  const doc = new jsPDF();
  
  // Header
  doc.setFontSize(20);
  doc.text('Algobir Performans Raporu', 20, 30);
  
  // Report metadata
  doc.setFontSize(12);
  doc.text(`Rapor Tarihi: ${format(new Date(), 'dd/MM/yyyy')}`, 20, 45);
  doc.text(`Dönem: ${period}`, 20, 55);
  
  // Fetch data
  const statistics = await fetchUserStatistics(userId, period);
  
  // Performance summary table
  const performanceData = [
    ['Metrik', 'Değer'],
    ['Toplam Getiri', `${statistics.totalReturn.toFixed(2)}%`],
    ['Net Kar', formatCurrency(statistics.netProfit)],
    ['Kazanma Oranı', `${statistics.winRate.toFixed(1)}%`],
    ['Maksimum Düşüş', `${statistics.maxDrawdown.toFixed(2)}%`],
    ['Sharpe Oranı', statistics.sharpeRatio.toFixed(2)],
    ['Toplam İşlem', statistics.totalTrades.toString()]
  ];
  
  doc.autoTable({
    head: [performanceData[0]],
    body: performanceData.slice(1),
    startY: 70,
    theme: 'grid'
  });
  
  // Add charts (convert to base64 images)
  const chartCanvas = await generateChartImage(statistics);
  if (chartCanvas) {
    doc.addImage(chartCanvas, 'PNG', 20, 150, 170, 100);
  }
  
  // Save PDF
  doc.save(`algobir-rapor-${format(new Date(), 'yyyy-MM-dd')}.pdf`);
};

// Chart to image conversion
const generateChartImage = async (data: StatisticsData): Promise<string | null> => {
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Use Chart.js to render chart to canvas
    const chart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: data.dates,
        datasets: [{
          label: 'Portföy Değeri',
          data: data.portfolioValues,
          borderColor: '#38B2AC',
          backgroundColor: 'rgba(56, 178, 172, 0.1)'
        }]
      },
      options: {
        responsive: false,
        animation: false
      }
    });
    
    // Wait for chart to render
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return canvas.toDataURL('image/png');
  } catch (error) {
    console.error('Chart image generation error:', error);
    return null;
  }
};
```

## Update Requirements

### When to Update This File
- New analytics features or metrics added
- Chart types or visualization changes
- Report generation improvements
- Real-time data integration updates
- Performance calculation modifications
- Dashboard layout or component changes

### Related Files to Update
- Update `frontend/MAIN_APP.mdc` for UI component changes
- Update `backend/SUPABASE_EDGE.mdc` for database queries
- Update `features/TRADING_SYSTEM.mdc` for trading-related analytics
- Update `performance/OPTIMIZATION.mdc` for data processing optimization
- Update `core/PROJECT_CORE.mdc` for major analytics architecture changes
