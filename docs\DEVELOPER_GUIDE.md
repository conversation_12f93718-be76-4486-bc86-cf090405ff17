# Algobir Developer Guide

## 🚀 Getting Started

This guide will help you set up the Algobir development environment and understand the codebase structure, development workflows, and best practices.

## 📋 Prerequisites

### Required Software
- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 9.0.0 or higher
- **Git**: Latest version
- **VS Code**: Recommended IDE with extensions

### Recommended VS Code Extensions
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-playwright.playwright",
    "vitest.explorer"
  ]
}
```

## 🛠️ Development Setup

### 1. Clone the Repository
```bash
git clone https://github.com/algobir/tobot-v2-app.git
cd tobot-v2-app
```

### 2. Install Dependencies
```bash
# Frontend dependencies
cd algobir-app-frontend
npm install

# Install Supabase CLI (if not already installed)
npm install -g @supabase/cli
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env.local

# Configure environment variables
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
```

### 4. Start Development Server
```bash
# Start frontend development server
npm run dev

# Start Supabase local development (optional)
supabase start
```

## 📁 Project Structure

```
algobir-app-frontend/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── admin/          # Admin panel components
│   │   ├── marketplace/    # Marketplace components
│   │   ├── navbar/         # Navigation components
│   │   ├── sidebar/        # Sidebar components
│   │   ├── statistics/     # Statistics components
│   │   ├── trades/         # Trading components
│   │   └── monitoring/     # Monitoring components
│   ├── context/            # React Context providers
│   ├── hooks/              # Custom React hooks
│   ├── pages/              # Page components
│   ├── services/           # API service functions
│   ├── utils/              # Utility functions
│   ├── theme/              # Chakra UI theme
│   └── types/              # TypeScript type definitions
├── tests/                  # Test files
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── e2e/               # End-to-end tests
├── scripts/               # Build and deployment scripts
└── public/                # Static assets
```

## 🧩 Component Development

### Component Structure
```typescript
// components/example/ExampleComponent.tsx
import React from 'react';
import { Box, Text, Button } from '@chakra-ui/react';

interface ExampleComponentProps {
  title: string;
  onAction?: () => void;
  isLoading?: boolean;
}

const ExampleComponent: React.FC<ExampleComponentProps> = ({
  title,
  onAction,
  isLoading = false
}) => {
  return (
    <Box p={4} borderWidth={1} borderRadius="md">
      <Text fontSize="lg" fontWeight="bold" mb={2}>
        {title}
      </Text>
      <Button 
        onClick={onAction}
        isLoading={isLoading}
        colorScheme="blue"
      >
        Action
      </Button>
    </Box>
  );
};

export default ExampleComponent;
```

### Component Best Practices
1. **Use TypeScript interfaces** for all props
2. **Provide default values** for optional props
3. **Use Chakra UI components** for consistency
4. **Implement proper error boundaries**
5. **Add loading states** for async operations
6. **Use memo() for expensive components**

## 🎣 Custom Hooks

### Hook Development Pattern
```typescript
// hooks/useExample.ts
import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../services/supabase';

interface UseExampleOptions {
  enabled?: boolean;
  refetchInterval?: number;
}

export const useExample = (id: string, options: UseExampleOptions = {}) => {
  const { enabled = true, refetchInterval } = options;
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    if (!enabled) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase
        .from('table_name')
        .select('*')
        .eq('id', id)
        .single();
        
      if (error) throw error;
      setData(data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [id, enabled]);

  useEffect(() => {
    fetchData();
    
    if (refetchInterval) {
      const interval = setInterval(fetchData, refetchInterval);
      return () => clearInterval(interval);
    }
  }, [fetchData, refetchInterval]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch
  };
};
```

### Hook Best Practices
1. **Use TypeScript** for all hooks
2. **Provide options parameter** for configuration
3. **Return consistent interface** (data, loading, error)
4. **Implement proper cleanup** in useEffect
5. **Use useCallback** for expensive operations
6. **Handle error states** gracefully

## 🔌 API Integration

### Service Layer Pattern
```typescript
// services/robotService.ts
import { supabase } from './supabase';
import type { Robot, CreateRobotRequest } from '../types/robot';

export class RobotService {
  static async getRobots(userId: string): Promise<Robot[]> {
    const { data, error } = await supabase
      .from('robots')
      .select(`
        *,
        profiles!inner(username)
      `)
      .eq('seller_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createRobot(request: CreateRobotRequest): Promise<Robot> {
    const { data, error } = await supabase
      .from('robots')
      .insert(request)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateRobot(id: string, updates: Partial<Robot>): Promise<Robot> {
    const { data, error } = await supabase
      .from('robots')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteRobot(id: string): Promise<void> {
    const { error } = await supabase
      .from('robots')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
}
```

### API Best Practices
1. **Use service classes** for API operations
2. **Implement proper error handling**
3. **Use TypeScript types** for requests/responses
4. **Add loading states** in components
5. **Implement retry logic** for critical operations
6. **Cache frequently accessed data**

## 🧪 Testing

### Unit Testing with Vitest
```typescript
// tests/unit/components/ExampleComponent.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import { describe, it, expect, vi } from 'vitest';
import ExampleComponent from '../../../src/components/ExampleComponent';

const renderWithChakra = (component: React.ReactElement) => {
  return render(
    <ChakraProvider>
      {component}
    </ChakraProvider>
  );
};

describe('ExampleComponent', () => {
  it('renders with title', () => {
    renderWithChakra(
      <ExampleComponent title="Test Title" />
    );
    
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('calls onAction when button is clicked', () => {
    const mockAction = vi.fn();
    
    renderWithChakra(
      <ExampleComponent title="Test" onAction={mockAction} />
    );
    
    fireEvent.click(screen.getByRole('button'));
    expect(mockAction).toHaveBeenCalledOnce();
  });

  it('shows loading state', () => {
    renderWithChakra(
      <ExampleComponent title="Test" isLoading={true} />
    );
    
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

### E2E Testing with Playwright
```typescript
// tests/e2e/dashboard.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('/dashboard');
  });

  test('displays user dashboard', async ({ page }) => {
    await expect(page.locator('h1')).toContainText('Dashboard');
    await expect(page.locator('[data-testid="stats-card"]')).toBeVisible();
  });

  test('navigates to marketplace', async ({ page }) => {
    await page.click('[data-testid="marketplace-link"]');
    await page.waitForURL('/marketplace');
    await expect(page.locator('h1')).toContainText('Marketplace');
  });
});
```

### Testing Best Practices
1. **Write tests for critical paths**
2. **Use data-testid attributes** for reliable selectors
3. **Mock external dependencies**
4. **Test error states** and edge cases
5. **Maintain test isolation**
6. **Use descriptive test names**

## 🎨 Styling Guidelines

### Chakra UI Theme Usage
```typescript
// theme/index.ts
import { extendTheme } from '@chakra-ui/react';

const theme = extendTheme({
  colors: {
    brand: {
      50: '#e3f2fd',
      100: '#bbdefb',
      500: '#2196f3',
      900: '#0d47a1'
    }
  },
  components: {
    Button: {
      baseStyle: {
        fontWeight: 'semibold',
        borderRadius: 'md'
      },
      variants: {
        solid: {
          bg: 'brand.500',
          color: 'white',
          _hover: {
            bg: 'brand.600'
          }
        }
      }
    }
  }
});

export default theme;
```

### Responsive Design
```typescript
// Use responsive props
<Box
  w={{ base: '100%', md: '50%', lg: '33%' }}
  p={{ base: 4, md: 6, lg: 8 }}
>
  Content
</Box>

// Use responsive arrays
<Text fontSize={['sm', 'md', 'lg']}>
  Responsive text
</Text>
```

## 🔧 Development Tools

### Available Scripts
```json
{
  "dev": "vite",                          // Start development server
  "build": "tsc && vite build",           // Build for production
  "build:optimized": "vite build --config vite.config.optimization.ts",
  "test": "vitest",                       // Run unit tests
  "test:e2e": "playwright test",          // Run E2E tests
  "lint": "tsc --noEmit",                 // Type checking
  "lint:fix": "eslint . --ext ts,tsx --fix",
  "preview": "vite preview",              // Preview production build
  "optimize": "node scripts/build-optimizer.js"
}
```

### Debug Configuration
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug React App",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/vite",
      "args": ["--mode", "development"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal"
    }
  ]
}
```

## 🚀 Performance Optimization

### Code Splitting
```typescript
// Lazy load components
const LazyMarketplace = lazy(() => import('./pages/Marketplace'));
const LazyStatistics = lazy(() => import('./pages/Statistics'));

// Use Suspense for loading states
<Suspense fallback={<LoadingSpinner />}>
  <LazyMarketplace />
</Suspense>
```

### Memoization
```typescript
// Memoize expensive components
const MemoizedTradesList = memo(TradesList);

// Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return calculateComplexValue(data);
}, [data]);

// Memoize callbacks
const handleClick = useCallback(() => {
  onAction(id);
}, [onAction, id]);
```

### Performance Monitoring
```typescript
// Use the monitoring hook
const { trackEvent, startTimer, recordMetric } = useMonitoring();

// Track user interactions
const handleButtonClick = () => {
  trackEvent('button_click', 1, { button: 'create_robot' });
  onCreateRobot();
};

// Measure performance
const timer = startTimer('api_call');
const result = await apiCall();
timer.end();
```

## 🔒 Security Best Practices

### Input Validation
```typescript
// Validate and sanitize inputs
const sanitizeInput = (input: string): string => {
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .trim();
};

// Use schema validation
const robotSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500),
  robot_type: z.enum(['solo', 'bro'])
});
```

### Authentication Checks
```typescript
// Protect routes with authentication
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();
  
  if (loading) return <LoadingSpinner />;
  if (!user) return <Navigate to="/login" />;
  
  return <>{children}</>;
};

// Check permissions
const usePermissions = () => {
  const { user } = useAuth();
  
  return {
    canCreateRobots: user?.role === 'admin' || user?.role === 'user',
    canAccessAdmin: user?.role === 'admin',
    canManageUsers: user?.role === 'admin'
  };
};
```

## 📚 Additional Resources

### Documentation Links
- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Chakra UI Documentation](https://chakra-ui.com/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Vite Documentation](https://vitejs.dev/guide/)
- [Vitest Documentation](https://vitest.dev/guide/)
- [Playwright Documentation](https://playwright.dev/docs/intro)

### Community Resources
- [Algobir Discord Server](https://discord.gg/algobir)
- [GitHub Discussions](https://github.com/algobir/tobot-v2-app/discussions)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/algobir)

## 🤝 Contributing

### Development Workflow
1. **Create feature branch** from `develop`
2. **Implement changes** following coding standards
3. **Write tests** for new functionality
4. **Run quality checks** (lint, test, build)
5. **Create pull request** with detailed description
6. **Code review** and address feedback
7. **Merge to develop** after approval

### Code Review Checklist
- [ ] Code follows TypeScript best practices
- [ ] Components are properly typed
- [ ] Tests are included and passing
- [ ] Performance considerations addressed
- [ ] Security best practices followed
- [ ] Documentation updated if needed
- [ ] No console.log statements in production code
- [ ] Error handling implemented
- [ ] Accessibility considerations addressed

This developer guide provides a comprehensive foundation for contributing to the Algobir platform. Follow these guidelines to maintain code quality, consistency, and performance across the application.
