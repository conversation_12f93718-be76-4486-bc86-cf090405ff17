import React, { useMemo } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  HStack,
  VStack,
  Badge,
  Icon,
  useColorModeValue,

  Select
} from '@chakra-ui/react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  ReferenceLine,

  Cell
} from 'recharts';
import {
  FiTarget,
  FiShield,
  FiTrendingUp,
  FiActivity,

} from 'react-icons/fi';

interface RiskReturnScatterPlotProps {
  symbolData: Array<{
    symbol: string;
    pnl: number;
    trades: number;
    winRate: number;
  }>;
  monthlyData: Array<{
    month: string;
    pnl: number;
    trades: number;
    roi: number;
  }>;
  dataType?: 'symbol' | 'monthly';
  height?: string;
  title?: string;
}

const RiskReturnScatterPlot: React.FC<RiskReturnScatterPlotProps> = ({
  symbolData,
  monthlyData,
  dataType = 'symbol',
  height = '400px',
  title = 'Risk-Getiri Analizi'
}) => {
  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');
  const gridColor = useColorModeValue('#f0f0f0', '#2d3748');
  
  // Chart colors
  const primaryColor = useColorModeValue('#3182CE', '#63B3ED');
  const successColor = useColorModeValue('#38A169', '#68D391');
  const dangerColor = useColorModeValue('#E53E3E', '#FC8181');


  // Enhanced formatting functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };



  // Calculate risk metrics
  const calculateRisk = (data: any[], type: 'symbol' | 'monthly') => {
    if (type === 'symbol') {
      return data.map(item => {
        // Risk proxy: inverse of win rate (lower win rate = higher risk)
        const risk = 100 - item.winRate;
        // Return: ROI based on P&L
        const returnValue = (item.pnl / Math.max(1, item.trades)) * 100;
        
        return {
          ...item,
          risk,
          return: returnValue,
          size: Math.max(20, Math.min(200, item.trades * 2)), // Bubble size based on trades
          color: item.pnl > 0 ? successColor : dangerColor,
          quadrant: getQuadrant(risk, returnValue)
        };
      });
    } else {
      // Calculate volatility for monthly data
      const returns = data.map(d => d.roi);
      const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
      
      return data.map(item => {
        // Risk: volatility (standard deviation of returns)
        const variance = Math.pow(item.roi - avgReturn, 2);
        const risk = Math.sqrt(variance);
        
        return {
          ...item,
          risk,
          return: item.roi,
          size: Math.max(20, Math.min(200, item.trades * 2)),
          color: item.pnl > 0 ? successColor : dangerColor,
          quadrant: getQuadrant(risk, item.roi)
        };
      });
    }
  };

  // Determine quadrant for risk-return positioning
  const getQuadrant = (risk: number, returnValue: number) => {
    if (returnValue > 0 && risk < 50) return 'optimal'; // High return, low risk
    if (returnValue > 0 && risk >= 50) return 'aggressive'; // High return, high risk
    if (returnValue <= 0 && risk < 50) return 'conservative'; // Low return, low risk
    return 'poor'; // Low return, high risk
  };

  // Process data for scatter plot
  const scatterData = useMemo(() => {
    const rawData = dataType === 'symbol' ? symbolData : monthlyData;
    return calculateRisk(rawData, dataType);
  }, [symbolData, monthlyData, dataType]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (!scatterData.length) return null;

    const avgRisk = scatterData.reduce((sum, d) => sum + d.risk, 0) / scatterData.length;
    const avgReturn = scatterData.reduce((sum, d) => sum + d.return, 0) / scatterData.length;
    
    const quadrantCounts = {
      optimal: scatterData.filter(d => d.quadrant === 'optimal').length,
      aggressive: scatterData.filter(d => d.quadrant === 'aggressive').length,
      conservative: scatterData.filter(d => d.quadrant === 'conservative').length,
      poor: scatterData.filter(d => d.quadrant === 'poor').length
    };

    const bestRiskAdjusted = scatterData.reduce((best, item) => {
      const sharpe = item.return / Math.max(1, item.risk);
      const bestSharpe = best.return / Math.max(1, best.risk);
      return sharpe > bestSharpe ? item : best;
    }, scatterData[0]);

    return {
      avgRisk,
      avgReturn,
      quadrantCounts,
      bestRiskAdjusted,
      totalItems: scatterData.length
    };
  }, [scatterData]);

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          bg={cardBg}
          p={4}
          borderRadius="lg"
          shadow="lg"
          border="1px"
          borderColor={borderColor}
        >
          <VStack spacing={2} align="start">
            <Text fontWeight="bold" color={textColor}>
              {dataType === 'symbol' ? data.symbol : data.month}
            </Text>
            <HStack spacing={4}>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">
                  {dataType === 'symbol' ? 'Risk (100-Kazanma%)' : 'Risk (Volatilite)'}
                </Text>
                <Text fontWeight="bold" color="orange.500">
                  {data.risk.toFixed(1)}%
                </Text>
              </VStack>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">
                  {dataType === 'symbol' ? 'Getiri (İşlem Başı)' : 'Getiri (ROI)'}
                </Text>
                <Text 
                  fontWeight="bold" 
                  color={data.return > 0 ? 'green.500' : 'red.500'}
                >
                  {data.return.toFixed(1)}%
                </Text>
              </VStack>
            </HStack>
            <HStack spacing={4}>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">İşlem Sayısı</Text>
                <Text fontWeight="bold" color={primaryColor}>
                  {data.trades}
                </Text>
              </VStack>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">P&L</Text>
                <Text 
                  fontWeight="bold" 
                  color={data.pnl > 0 ? 'green.500' : 'red.500'}
                >
                  {formatCurrency(data.pnl)}
                </Text>
              </VStack>
            </HStack>
            <Badge 
              colorScheme={
                data.quadrant === 'optimal' ? 'green' :
                data.quadrant === 'aggressive' ? 'orange' :
                data.quadrant === 'conservative' ? 'blue' : 'red'
              }
              variant="subtle"
            >
              {data.quadrant === 'optimal' ? 'Optimal' :
               data.quadrant === 'aggressive' ? 'Agresif' :
               data.quadrant === 'conservative' ? 'Muhafazakar' : 'Zayıf'}
            </Badge>
          </VStack>
        </Box>
      );
    }
    return null;
  };

  return (
    <Card bg={cardBg} borderRadius="xl" shadow="sm">
      <CardHeader pb={2}>
        <HStack justify="space-between" flexWrap="wrap">
          <VStack align="start" spacing={1}>
            <HStack spacing={3}>
              <Icon as={FiTarget} color="blue.500" boxSize={6} />
              <Heading size="md" color={textColor}>
                {title}
              </Heading>
            </HStack>
            {stats && (
              <HStack spacing={4} flexWrap="wrap">
                <Badge colorScheme="green" variant="subtle" px={3} py={1}>
                  <Icon as={FiTrendingUp} mr={1} />
                  {stats.quadrantCounts.optimal} Optimal
                </Badge>
                <Badge colorScheme="orange" variant="subtle" px={3} py={1}>
                  <Icon as={FiActivity} mr={1} />
                  {stats.quadrantCounts.aggressive} Agresif
                </Badge>
                <Badge colorScheme="blue" variant="subtle" px={3} py={1}>
                  <Icon as={FiShield} mr={1} />
                  {stats.quadrantCounts.conservative} Muhafazakar
                </Badge>
              </HStack>
            )}
          </VStack>

          <VStack align="end" spacing={2}>
            <Select size="sm" w="auto" defaultValue={dataType}>
              <option value="symbol">Sembol Bazlı</option>
              <option value="monthly">Aylık Bazlı</option>
            </Select>
          </VStack>
        </HStack>
      </CardHeader>

      <CardBody pt={0}>
        <Box h={height}>
          <ResponsiveContainer width="100%" height="100%">
            <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
              <XAxis 
                type="number"
                dataKey="risk"
                name="Risk"
                tick={{ fontSize: 12, fill: textColor }}
                axisLine={{ stroke: borderColor }}
                label={{ 
                  value: dataType === 'symbol' ? 'Risk (100-Kazanma%)' : 'Risk (Volatilite)', 
                  position: 'insideBottom', 
                  offset: -10,
                  style: { textAnchor: 'middle', fill: textColor }
                }}
              />
              <YAxis 
                type="number"
                dataKey="return"
                name="Return"
                tick={{ fontSize: 12, fill: textColor }}
                axisLine={{ stroke: borderColor }}
                label={{ 
                  value: dataType === 'symbol' ? 'Getiri (İşlem Başı %)' : 'Getiri (ROI %)', 
                  angle: -90, 
                  position: 'insideLeft',
                  style: { textAnchor: 'middle', fill: textColor }
                }}
              />
              <RechartsTooltip content={<CustomTooltip />} />
              
              {/* Reference lines for quadrants */}
              {stats && (
                <>
                  <ReferenceLine 
                    x={stats.avgRisk} 
                    stroke={borderColor} 
                    strokeDasharray="5 5"
                    label={{ value: "Ort. Risk", position: "insideTopRight" }}
                  />
                  <ReferenceLine 
                    y={stats.avgReturn} 
                    stroke={borderColor} 
                    strokeDasharray="5 5"
                    label={{ value: "Ort. Getiri", position: "insideTopRight" }}
                  />
                </>
              )}
              
              <Scatter data={scatterData} fill={primaryColor}>
                {scatterData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Scatter>
            </ScatterChart>
          </ResponsiveContainer>
        </Box>

        {/* Analysis Summary */}
        {stats && (
          <VStack spacing={4} mt={4} p={4} bg={useColorModeValue('gray.50', 'gray.800')} borderRadius="lg">
            <Text fontSize="sm" fontWeight="semibold" color={textColor}>
              Risk-Getiri Analizi Özeti
            </Text>
            <HStack justify="space-around" w="full" flexWrap="wrap">
              <VStack spacing={1}>
                <Text fontSize="xs" color="gray.500">Ortalama Risk</Text>
                <Text fontWeight="bold" color="orange.500">
                  {stats.avgRisk.toFixed(1)}%
                </Text>
              </VStack>
              <VStack spacing={1}>
                <Text fontSize="xs" color="gray.500">Ortalama Getiri</Text>
                <Text 
                  fontWeight="bold" 
                  color={stats.avgReturn > 0 ? 'green.500' : 'red.500'}
                >
                  {stats.avgReturn.toFixed(1)}%
                </Text>
              </VStack>
              <VStack spacing={1}>
                <Text fontSize="xs" color="gray.500">En İyi Risk-Ayarlı</Text>
                <Text fontWeight="bold" color="blue.500">
                  {dataType === 'symbol' ? stats.bestRiskAdjusted.symbol : stats.bestRiskAdjusted.month}
                </Text>
              </VStack>
              <VStack spacing={1}>
                <Text fontSize="xs" color="gray.500">Sharpe Oranı</Text>
                <Text fontWeight="bold" color="purple.500">
                  {(stats.bestRiskAdjusted.return / Math.max(1, stats.bestRiskAdjusted.risk)).toFixed(2)}
                </Text>
              </VStack>
            </HStack>
          </VStack>
        )}
      </CardBody>
    </Card>
  );
};

export default RiskReturnScatterPlot;
