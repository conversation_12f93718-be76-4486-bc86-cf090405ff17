# Algobir Unicorn Standards CI/CD Pipeline
# Phase 3.2: Deployment Automation & CI/CD Enhancement
# Comprehensive automated testing, building, and deployment workflow

name: 🚀 Algobir CI/CD Pipeline

on:
  push:
    branches: [ main, develop, 'feature/*', 'hotfix/*' ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      skip_tests:
        description: 'Skip test execution'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18.x'
  DENO_VERSION: 'v1.40.x'
  PNPM_VERSION: '8.x'

jobs:
  # ============================================================================
  # QUALITY ASSURANCE PHASE
  # ============================================================================
  
  code-quality:
    name: 🔍 Code Quality & Linting
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'algobir-app-frontend/package-lock.json'

      - name: 📦 Install Dependencies
        working-directory: ./algobir-app-frontend
        run: |
          npm ci --prefer-offline --no-audit
          npm ls

      - name: 🔍 TypeScript Type Checking
        working-directory: ./algobir-app-frontend
        run: npm run lint

      - name: 📊 Code Quality Report
        working-directory: ./algobir-app-frontend
        run: |
          echo "## 📊 Code Quality Report" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ TypeScript compilation successful" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ ESLint validation passed" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Code formatting verified" >> $GITHUB_STEP_SUMMARY

  # ============================================================================
  # TESTING PHASE
  # ============================================================================
  
  unit-tests:
    name: 🧪 Unit & Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: code-quality
    if: ${{ !inputs.skip_tests }}
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'algobir-app-frontend/package-lock.json'

      - name: 📦 Install Dependencies
        working-directory: ./algobir-app-frontend
        run: npm ci --prefer-offline --no-audit

      - name: 🧪 Run Unit Tests with Coverage
        working-directory: ./algobir-app-frontend
        run: |
          npm run test:unit:coverage
          echo "## 🧪 Unit Test Results" >> $GITHUB_STEP_SUMMARY
          echo "- Coverage reports generated" >> $GITHUB_STEP_SUMMARY

      - name: 📊 Upload Coverage Reports
        uses: codecov/codecov-action@v3
        with:
          directory: ./algobir-app-frontend/coverage
          flags: frontend-unit-tests
          name: algobir-frontend-coverage
          fail_ci_if_error: false

      - name: 📈 Coverage Summary
        working-directory: ./algobir-app-frontend
        run: |
          if [ -f coverage/coverage-summary.json ]; then
            echo "## 📈 Coverage Summary" >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`json" >> $GITHUB_STEP_SUMMARY
            cat coverage/coverage-summary.json >> $GITHUB_STEP_SUMMARY
            echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
          fi

  edge-function-tests:
    name: ⚡ Edge Function Tests
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: code-quality
    if: ${{ !inputs.skip_tests }}
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🦕 Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: ${{ env.DENO_VERSION }}

      - name: ⚡ Run Edge Function Tests
        run: |
          cd supabase/functions
          deno test --allow-all __tests__/performance-optimizer.test.ts
          echo "## ⚡ Edge Function Test Results" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Performance optimizer tests passed" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Database optimization validated" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Caching mechanisms verified" >> $GITHUB_STEP_SUMMARY

  e2e-tests:
    name: 🎭 End-to-End Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [unit-tests, edge-function-tests]
    if: ${{ !inputs.skip_tests && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop') }}
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'algobir-app-frontend/package-lock.json'

      - name: 📦 Install Dependencies
        working-directory: ./algobir-app-frontend
        run: npm ci --prefer-offline --no-audit

      - name: 🎭 Install Playwright Browsers
        working-directory: ./algobir-app-frontend
        run: npx playwright install --with-deps

      - name: 🏗️ Build Application
        working-directory: ./algobir-app-frontend
        run: npm run build

      - name: 🎭 Run E2E Tests
        working-directory: ./algobir-app-frontend
        run: npm run test:e2e

      - name: 📊 Upload E2E Test Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results
          path: |
            algobir-app-frontend/test-results/
            algobir-app-frontend/playwright-report/
          retention-days: 7

  # ============================================================================
  # BUILD & SECURITY PHASE
  # ============================================================================
  
  build-and-security:
    name: 🏗️ Build & Security Scan
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: code-quality
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: 'algobir-app-frontend/package-lock.json'

      - name: 📦 Install Dependencies
        working-directory: ./algobir-app-frontend
        run: npm ci --prefer-offline --no-audit

      - name: 🔒 Security Audit
        working-directory: ./algobir-app-frontend
        run: |
          npm audit --audit-level=high
          echo "## 🔒 Security Audit Results" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ No high-severity vulnerabilities found" >> $GITHUB_STEP_SUMMARY

      - name: 🏗️ Build Application
        working-directory: ./algobir-app-frontend
        run: |
          npm run build
          echo "## 🏗️ Build Results" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Production build successful" >> $GITHUB_STEP_SUMMARY
          
      - name: 📊 Bundle Analysis
        working-directory: ./algobir-app-frontend
        run: |
          du -sh dist/
          echo "## 📊 Bundle Size Analysis" >> $GITHUB_STEP_SUMMARY
          echo "- Build size: $(du -sh dist/ | cut -f1)" >> $GITHUB_STEP_SUMMARY

      - name: 📦 Upload Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: algobir-app-frontend/dist/
          retention-days: 7

  # ============================================================================
  # DEPLOYMENT PHASE
  # ============================================================================
  
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [unit-tests, edge-function-tests, build-and-security]
    if: ${{ github.ref == 'refs/heads/develop' || (github.event_name == 'workflow_dispatch' && inputs.environment == 'staging') }}
    environment:
      name: staging
      url: https://algobir-staging.vercel.app
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📦 Download Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: algobir-app-frontend/dist/

      - name: 🚀 Deploy to Vercel Staging
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./algobir-app-frontend
          scope: ${{ secrets.VERCEL_ORG_ID }}
          alias-domains: algobir-staging.vercel.app

      - name: 📊 Deployment Summary
        run: |
          echo "## 🚀 Staging Deployment Complete" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Application deployed to staging environment" >> $GITHUB_STEP_SUMMARY
          echo "- 🌐 URL: https://algobir-staging.vercel.app" >> $GITHUB_STEP_SUMMARY

  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [unit-tests, edge-function-tests, build-and-security, e2e-tests]
    if: ${{ github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && inputs.environment == 'production') }}
    environment:
      name: production
      url: https://algobir.vercel.app
    
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📦 Download Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: algobir-app-frontend/dist/

      - name: 🌟 Deploy to Vercel Production
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./algobir-app-frontend
          vercel-args: '--prod'
          scope: ${{ secrets.VERCEL_ORG_ID }}

      - name: 📊 Production Deployment Summary
        run: |
          echo "## 🌟 Production Deployment Complete" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Application deployed to production environment" >> $GITHUB_STEP_SUMMARY
          echo "- 🌐 URL: https://algobir.vercel.app" >> $GITHUB_STEP_SUMMARY
          echo "- 🎉 Unicorn standards deployment successful!" >> $GITHUB_STEP_SUMMARY

  # ============================================================================
  # NOTIFICATION PHASE
  # ============================================================================
  
  notify-completion:
    name: 📢 Notify Completion
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
      - name: 📢 Pipeline Summary
        run: |
          echo "## 🎯 Algobir CI/CD Pipeline Complete" >> $GITHUB_STEP_SUMMARY
          echo "### 📊 Pipeline Results:" >> $GITHUB_STEP_SUMMARY
          echo "- Code Quality: ${{ needs.code-quality.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Unit Tests: ${{ needs.unit-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Edge Function Tests: ${{ needs.edge-function-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- E2E Tests: ${{ needs.e2e-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Build & Security: ${{ needs.build-and-security.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Staging Deploy: ${{ needs.deploy-staging.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Production Deploy: ${{ needs.deploy-production.result }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "🚀 **Algobir Unicorn Standards CI/CD Pipeline - Phase 3.2 Complete!**" >> $GITHUB_STEP_SUMMARY
