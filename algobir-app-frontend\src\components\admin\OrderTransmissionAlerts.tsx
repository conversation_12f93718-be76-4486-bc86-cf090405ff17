import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Badge,
  Icon,
  useColorModeValue,
  Button,
  Collapse,
  useDisclosure,
  Tooltip
} from '@chakra-ui/react';
import {
  FiAlertTriangle,
  FiAlertCircle,
  FiInfo,
  FiCheckCircle,
  FiChevronDown,
  FiChevronUp,
  FiX
} from 'react-icons/fi';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import useOrderTransmissionMetrics from '../../hooks/useOrderTransmissionMetrics';

const MotionBox = motion(Box);

interface Alert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  title: string;
  description: string;
  timestamp: Date;
  value?: number;
  threshold?: number;
  dismissible?: boolean;
}

interface OrderTransmissionAlertsProps {
  timeRangeHours?: number;
  showDismissed?: boolean;
}

const OrderTransmissionAlerts: React.FC<OrderTransmissionAlertsProps> = ({
  timeRangeHours = 1,
  showDismissed = false
}) => {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(new Set());
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: true });

  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  // Fetch metrics data
  const { stats, error, isConnected } = useOrderTransmissionMetrics({
    timeRangeHours,
    enabled: true,
    autoRefresh: true,
    refreshInterval: 30000
  });

  // Performance thresholds
  const thresholds = {
    totalProcessingTime: { warning: 500, error: 1000 },
    jsonParsingTime: { warning: 50, error: 100 },
    transformationTime: { warning: 200, error: 500 },
    webhookDeliveryTime: { warning: 1000, error: 2000 },
    successRate: { warning: 95, error: 90 },
    signalsPerMinute: { warning: 0.1, error: 0 }
  };

  // Generate alerts based on current metrics
  useEffect(() => {
    if (!stats) return;

    const newAlerts: Alert[] = [];
    const now = new Date();

    // Connection status alert
    if (!isConnected) {
      newAlerts.push({
        id: 'connection-lost',
        type: 'error',
        title: 'Bağlantı Kesildi',
        description: 'Canlı veri akışı kesildi. Sistem izleme durdu.',
        timestamp: now,
        dismissible: false
      });
    }

    // Error alert
    if (error) {
      newAlerts.push({
        id: 'data-error',
        type: 'error',
        title: 'Veri Hatası',
        description: `Performans verileri alınırken hata oluştu: ${error}`,
        timestamp: now,
        dismissible: true
      });
    }

    // Performance alerts
    if (stats.avg_total_processing_time_ms >= thresholds.totalProcessingTime.error) {
      newAlerts.push({
        id: 'slow-processing-critical',
        type: 'error',
        title: 'Kritik Performans Sorunu',
        description: 'Ortalama işlem süresi kritik seviyede yüksek',
        timestamp: now,
        value: stats.avg_total_processing_time_ms,
        threshold: thresholds.totalProcessingTime.error,
        dismissible: true
      });
    } else if (stats.avg_total_processing_time_ms >= thresholds.totalProcessingTime.warning) {
      newAlerts.push({
        id: 'slow-processing-warning',
        type: 'warning',
        title: 'Performans Uyarısı',
        description: 'Ortalama işlem süresi normalden yüksek',
        timestamp: now,
        value: stats.avg_total_processing_time_ms,
        threshold: thresholds.totalProcessingTime.warning,
        dismissible: true
      });
    }

    // Success rate alerts
    if (stats.success_rate <= thresholds.successRate.error) {
      newAlerts.push({
        id: 'low-success-rate-critical',
        type: 'error',
        title: 'Kritik Başarı Oranı',
        description: 'Sistem başarı oranı kritik seviyede düşük',
        timestamp: now,
        value: stats.success_rate,
        threshold: thresholds.successRate.error,
        dismissible: true
      });
    } else if (stats.success_rate <= thresholds.successRate.warning) {
      newAlerts.push({
        id: 'low-success-rate-warning',
        type: 'warning',
        title: 'Başarı Oranı Uyarısı',
        description: 'Sistem başarı oranı normalden düşük',
        timestamp: now,
        value: stats.success_rate,
        threshold: thresholds.successRate.warning,
        dismissible: true
      });
    }

    // Low activity alert
    if (stats.signals_per_minute <= thresholds.signalsPerMinute.warning) {
      newAlerts.push({
        id: 'low-activity',
        type: 'info',
        title: 'Düşük Aktivite',
        description: 'Son dönemde sinyal aktivitesi düşük',
        timestamp: now,
        value: stats.signals_per_minute,
        dismissible: true
      });
    }

    // High performance alert
    if (stats.avg_total_processing_time_ms <= 50 && stats.success_rate >= 99) {
      newAlerts.push({
        id: 'excellent-performance',
        type: 'success',
        title: 'Mükemmel Performans',
        description: 'Sistem optimal performansta çalışıyor',
        timestamp: now,
        dismissible: true
      });
    }

    // Component-specific alerts
    if (stats.avg_json_parsing_time_ms >= thresholds.jsonParsingTime.warning) {
      newAlerts.push({
        id: 'slow-json-parsing',
        type: 'warning',
        title: 'JSON Ayrıştırma Yavaş',
        description: 'JSON ayrıştırma süresi normalden yüksek',
        timestamp: now,
        value: stats.avg_json_parsing_time_ms,
        threshold: thresholds.jsonParsingTime.warning,
        dismissible: true
      });
    }

    if (stats.avg_webhook_delivery_time_ms >= thresholds.webhookDeliveryTime.warning) {
      newAlerts.push({
        id: 'slow-webhook-delivery',
        type: 'warning',
        title: 'Webhook İletimi Yavaş',
        description: 'Webhook iletim süresi normalden yüksek',
        timestamp: now,
        value: stats.avg_webhook_delivery_time_ms,
        threshold: thresholds.webhookDeliveryTime.warning,
        dismissible: true
      });
    }

    setAlerts(newAlerts);
  }, [stats, error, isConnected]);

  // Dismiss alert
  const dismissAlert = (alertId: string) => {
    setDismissedAlerts(prev => new Set([...prev, alertId]));
  };

  // Get alert icon
  const getAlertIcon = (type: Alert['type']) => {
    switch (type) {
      case 'error':
        return FiAlertCircle;
      case 'warning':
        return FiAlertTriangle;
      case 'info':
        return FiInfo;
      case 'success':
        return FiCheckCircle;
      default:
        return FiInfo;
    }
  };

  // Filter alerts
  const visibleAlerts = alerts.filter(alert => 
    showDismissed || !dismissedAlerts.has(alert.id)
  );

  const criticalAlerts = visibleAlerts.filter(alert => alert.type === 'error');
  const warningAlerts = visibleAlerts.filter(alert => alert.type === 'warning');
  const infoAlerts = visibleAlerts.filter(alert => alert.type === 'info' || alert.type === 'success');

  if (visibleAlerts.length === 0) {
    return (
      <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
        <CardBody>
          <HStack spacing={3}>
            <Icon as={FiCheckCircle} color="green.500" boxSize={5} />
            <VStack align="start" spacing={0}>
              <Text fontSize="sm" fontWeight="bold" color={textColor}>
                Sistem Normal Çalışıyor
              </Text>
              <Text fontSize="xs" color={mutedColor}>
                Herhangi bir performans uyarısı bulunmuyor
              </Text>
            </VStack>
          </HStack>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
      <CardHeader pb={2}>
        <HStack justify="space-between" align="center">
          <HStack spacing={3}>
            <Icon as={FiAlertTriangle} color="orange.500" boxSize={5} />
            <VStack align="start" spacing={0}>
              <Heading size="sm" color={textColor}>
                Sistem Uyarıları
              </Heading>
              <HStack spacing={2}>
                {criticalAlerts.length > 0 && (
                  <Badge colorScheme="red" variant="subtle" fontSize="xs">
                    {criticalAlerts.length} Kritik
                  </Badge>
                )}
                {warningAlerts.length > 0 && (
                  <Badge colorScheme="yellow" variant="subtle" fontSize="xs">
                    {warningAlerts.length} Uyarı
                  </Badge>
                )}
                {infoAlerts.length > 0 && (
                  <Badge colorScheme="blue" variant="subtle" fontSize="xs">
                    {infoAlerts.length} Bilgi
                  </Badge>
                )}
              </HStack>
            </VStack>
          </HStack>

          <Button
            size="sm"
            variant="ghost"
            onClick={onToggle}
            rightIcon={<Icon as={isOpen ? FiChevronUp : FiChevronDown} />}
          >
            {isOpen ? 'Gizle' : 'Göster'}
          </Button>
        </HStack>
      </CardHeader>

      <Collapse in={isOpen} animateOpacity>
        <CardBody pt={0}>
          <VStack spacing={3} align="stretch">
            <AnimatePresence>
              {visibleAlerts.map((alert, index) => (
                <MotionBox
                  key={alert.id}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Alert
                    status={alert.type}
                    borderRadius="lg"
                    variant="left-accent"
                  >
                    <AlertIcon as={getAlertIcon(alert.type)} />
                    <Box flex="1">
                      <HStack justify="space-between" align="start">
                        <VStack align="start" spacing={1} flex="1">
                          <AlertTitle fontSize="sm">
                            {alert.title}
                          </AlertTitle>
                          <AlertDescription fontSize="xs">
                            {alert.description}
                          </AlertDescription>
                          
                          {alert.value !== undefined && (
                            <HStack spacing={2} mt={1}>
                              <Text fontSize="xs" color={mutedColor}>
                                Değer: {alert.value.toFixed(1)}
                                {alert.title.includes('Süre') ? 'ms' : 
                                 alert.title.includes('Oran') ? '%' : ''}
                              </Text>
                              {alert.threshold && (
                                <Text fontSize="xs" color={mutedColor}>
                                  Eşik: {alert.threshold}
                                  {alert.title.includes('Süre') ? 'ms' : 
                                   alert.title.includes('Oran') ? '%' : ''}
                                </Text>
                              )}
                            </HStack>
                          )}

                          <Text fontSize="xs" color={mutedColor}>
                            {format(alert.timestamp, 'HH:mm:ss', { locale: tr })}
                          </Text>
                        </VStack>

                        {alert.dismissible && (
                          <Tooltip label="Uyarıyı kapat">
                            <Button
                              size="xs"
                              variant="ghost"
                              onClick={() => dismissAlert(alert.id)}
                              aria-label="Uyarıyı kapat"
                            >
                              <Icon as={FiX} />
                            </Button>
                          </Tooltip>
                        )}
                      </HStack>
                    </Box>
                  </Alert>
                </MotionBox>
              ))}
            </AnimatePresence>
          </VStack>
        </CardBody>
      </Collapse>
    </Card>
  );
};

export default OrderTransmissionAlerts;
