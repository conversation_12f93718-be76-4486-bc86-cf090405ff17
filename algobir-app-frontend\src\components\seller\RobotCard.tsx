import {
  Box,
  Heading,
  Text,
  Badge,
  Flex,
  Image,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  useColorModeValue
} from '@chakra-ui/react';
import { FiMoreVertical, FiEdit2, FiTrash2 } from 'react-icons/fi';

// Robot tipi
interface Robot {
  id: string;
  seller_id: string;
  name: string;
  description: string | null;
  strategy_type: string | null;
  is_public: boolean;
  deleted_at?: string | null;
  image_url: string | null;
  created_at: string;
  version: string;
}

interface RobotCardProps {
  robot: Robot;
  onDelete?: (robot: Robot) => void;
}

// Strateji tür isimlerini Türkçe'ye çevirir
const getStrategyName = (strategyType: string | null): string => {
  if (!strategyType) return 'Belirtilmemiş';

  const strategyMap: Record<string, string> = {
    'trend_following': 'Trend Takibi',
    'mean_reversion': 'Ortalamaya Dönüş',
    'breakout': '<PERSON>ı<PERSON>ıl<PERSON><PERSON>',
    'momentum': 'Momentum',
    'swing_trading': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    'scalping': 'Scalping',
    'other': 'Diğer'
  };

  return strategyMap[strategyType] || strategyType;
};

const RobotCard = ({ robot, onDelete }: RobotCardProps) => {
  const bgColor = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.600', 'gray.300');
  
  // Oluşturulma tarihi formatı
  const formattedDate = new Date(robot.created_at).toLocaleDateString('tr-TR', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
  
  // Varsayılan resim
  const imageUrl = robot.image_url || "https://placehold.co/300?text=Robot+Görseli";
  
  // Event handlers for card actions  
  const handleEdit = () => {
    console.log("Düzenle:", robot.id);
    // Bu component artık sadece UI, edit işlemi parent'ta yapılıyor
  };
  
  const handleDelete = () => {
    if (onDelete) {
      onDelete(robot);
    }
  };
  
  return (
    <Box
      borderWidth="1px"
      borderRadius="lg"
      overflow="hidden"
      bg={bgColor}
      borderColor={borderColor}
      boxShadow="sm"
      position="relative"
      transition="transform 0.3s, box-shadow 0.3s"
      _hover={{
        transform: 'translateY(-4px)',
        boxShadow: 'md'
      }}
    >


      {/* Resim */}
      <Box position="relative" height="160px" overflow="hidden">
        <Image
          src={imageUrl}
          alt={robot.name}
          objectFit="cover"
          w="100%"
          h="100%"
          fallbackSrc="/assets/images/robot-placeholder.png"
          onError={(e) => { e.currentTarget.src = '/assets/images/robot-placeholder.png'; }}
        />
        <Badge
          position="absolute"
          bottom="2"
          left="2"
          colorScheme={robot.is_public ? "green" : "purple"}
        >
          {robot.is_public ? "Herkese Açık" : "Özel"}
        </Badge>
      </Box>

      {/* İçerik */}
      <Box p={4}>
        {/* Robot İsmi ve Aksiyon Menüsü */}
        <Flex justify="space-between" align="center" mb={2}>
          <Heading size="md" noOfLines={1} flex="1" mr={2}>
            {robot.name}
          </Heading>
          <Menu>
            <MenuButton
              as={IconButton}
              icon={<FiMoreVertical />}
              variant="ghost"
              size="sm"
              aria-label="Daha fazla seçenek"
            />
            <MenuList>
              <MenuItem icon={<FiEdit2 />} onClick={handleEdit}>
                Düzenle
              </MenuItem>
              <MenuItem icon={<FiTrash2 />} onClick={handleDelete} color="red.500">
                Sil
              </MenuItem>
            </MenuList>
          </Menu>
        </Flex>
        
        {robot.description && (
          <Text color={textColor} fontSize="sm" noOfLines={2} mb={3}>
            {robot.description}
          </Text>
        )}
        
        <Flex wrap="wrap" gap={2} mb={3}>
          {robot.strategy_type && (
            <Badge colorScheme="blue" variant="subtle">
              {getStrategyName(robot.strategy_type)}
            </Badge>
          )}
        </Flex>
        
        <Text fontSize="xs" color={textColor} mt={2}>
          Oluşturulma: {formattedDate}
        </Text>
      </Box>
    </Box>
  );
};

export default RobotCard; 