import React from 'react';
import {
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Box,
  Text,
  Tag,
  Flex
} from '@chakra-ui/react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { Trade } from '../../types/trade';
import Card from '../card/Card';

interface RecentTradesTableProps {
  trades: Trade[];
}

const RecentTradesTable: React.FC<RecentTradesTableProps> = ({ trades }) => {
  const formatCurrency = (value: number | null | undefined) => {
    if (value === null || value === undefined) return 'N/A';
    return new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }).format(value);
  };

  const getPnlColor = (pnl: number | null | undefined) => (pnl && pnl > 0 ? 'green.500' : 'red.500');
  const getTagColor = (pnl: number | null | undefined) => (pnl && pnl > 0 ? 'green' : 'red');

  return (
    <Card data-testid="recent-trades-table">
      <Text fontSize="xl" fontWeight="bold" mb={4}>
        Son Kapanan İşlemler
      </Text>
      <TableContainer>
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>Sembol</Th>
              <Th>Kapanış Tarihi</Th>
              <Th isNumeric>K/Z Miktarı</Th>
              <Th isNumeric>K/Z Yüzdesi</Th>
            </Tr>
          </Thead>
          <Tbody>
            {trades.map((trade) => (
              <Tr key={trade.id}>
                <Td>
                  <Flex align="center">
                    <Tag size="sm" colorScheme={trade.order_side === 'BUY' ? 'blue' : 'orange'} mr={2}>
                        {trade.order_side}
                    </Tag>
                    <Text fontWeight="bold">{trade.symbol}</Text>
                  </Flex>
                </Td>
                <Td>{trade.pnl_calculated_at ? format(new Date(trade.pnl_calculated_at), 'd MMMM yyyy, HH:mm', { locale: tr }) : 'N/A'}</Td>
                <Td isNumeric color={getPnlColor(trade.pnl)} fontWeight="medium">{formatCurrency(trade.pnl)}</Td>
                <Td isNumeric>
                  <Tag size="md" variant="subtle" colorScheme={getTagColor(trade.pnl)}>
                    {trade.pnl_percentage?.toFixed(2) ?? '0.00'}%
                  </Tag>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </TableContainer>
       {trades.length === 0 && (
        <Box textAlign="center" p={10}>
          <Text>Henüz kapanan bir işlem bulunmuyor.</Text>
        </Box>
      )}
    </Card>
  );
};

export default RecentTradesTable; 