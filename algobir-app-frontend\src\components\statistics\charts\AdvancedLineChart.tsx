import React, { useState, useMemo } from 'react';
import {
  Box,
  HStack,
  VStack,
  Text,
  ButtonGroup,
  useColorModeValue,
  Tooltip,
  Badge,
  IconButton,
  Menu,
  MenuButton,
  MenuList,

  Switch,
  FormControl,
  FormLabel
} from '@chakra-ui/react';
import {

  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Brush,

  Area,
  ComposedChart,
  Bar
} from 'recharts';
import { SettingsIcon, DownloadIcon, AddIcon, MinusIcon } from '@chakra-ui/icons';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

interface DataPoint {
  date: string;
  value: number;
  volume?: number;
  [key: string]: any;
}

interface AdvancedLineChartProps {
  data: DataPoint[];
  title?: string;
  dataKey: string;
  color?: string;
  height?: number;
  showBrush?: boolean;
  showVolume?: boolean;
  showMovingAverage?: boolean;
  showTrendLine?: boolean;
  enableZoom?: boolean;
  enableExport?: boolean;
  formatValue?: (value: number) => string;
  formatDate?: (date: string) => string;
  onDataPointClick?: (data: DataPoint) => void;
}

const AdvancedLineChart: React.FC<AdvancedLineChartProps> = ({
  data,
  title,
  dataKey,
  color = '#3182CE',
  height = 400,
  showBrush = true,
  showVolume = false,
  showMovingAverage = false,
  showTrendLine = false,
  enableZoom = true,
  enableExport = true,
  formatValue = (value: number) => `₺${value.toFixed(2)}`,
  formatDate = (date: string) => new Date(date).toLocaleDateString('tr-TR'),
  onDataPointClick
}) => {
  const [zoomLevel, setZoomLevel] = useState(1);

  const [chartSettings, setChartSettings] = useState({
    showGrid: true,
    showDots: false,
    smoothLine: true,
    showArea: false,
    showMovingAverage: showMovingAverage,
    showTrendLine: showTrendLine
  });

  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'white');
  const gridColor = useColorModeValue('#f0f0f0', '#2d3748');

  // Calculate moving average
  const dataWithMA = useMemo(() => {
    if (!chartSettings.showMovingAverage) return data;
    
    const period = 7; // 7-day moving average
    return data.map((item, index) => {
      if (index < period - 1) return { ...item, ma: null };
      
      const sum = data.slice(index - period + 1, index + 1)
        .reduce((acc, curr) => acc + curr[dataKey], 0);
      return { ...item, ma: sum / period };
    });
  }, [data, dataKey, chartSettings.showMovingAverage]);

  // Calculate trend line
  const trendLineData = useMemo(() => {
    if (!chartSettings.showTrendLine || data.length < 2) return [];
    
    const n = data.length;
    const sumX = data.reduce((sum, _, i) => sum + i, 0);
    const sumY = data.reduce((sum, item) => sum + item[dataKey], 0);
    const sumXY = data.reduce((sum, item, i) => sum + i * item[dataKey], 0);
    const sumXX = data.reduce((sum, _, i) => sum + i * i, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    return data.map((item, i) => ({
      ...item,
      trend: slope * i + intercept
    }));
  }, [data, dataKey, chartSettings.showTrendLine]);

  const finalData = chartSettings.showTrendLine ? trendLineData : dataWithMA;

  // Chart export functionality
  const exportChart = () => {
    // Implementation for chart export
    console.log('Exporting chart...');
  };

  // Zoom functionality
  const handleZoom = (direction: 'in' | 'out') => {
    if (direction === 'in' && zoomLevel < 3) {
      setZoomLevel(prev => prev + 0.5);
    } else if (direction === 'out' && zoomLevel > 0.5) {
      setZoomLevel(prev => prev - 0.5);
    }
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <MotionBox
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          bg={bgColor}
          p={3}
          borderRadius="md"
          boxShadow="lg"
          border="1px"
          borderColor="gray.200"
        >
          <VStack spacing={1} align="start">
            <Text fontSize="sm" fontWeight="bold">
              {formatDate(label)}
            </Text>
            {payload.map((entry: any, index: number) => (
              <HStack key={index} spacing={2}>
                <Box w={3} h={3} bg={entry.color} borderRadius="sm" />
                <Text fontSize="sm">
                  {entry.name}: {formatValue(entry.value)}
                </Text>
              </HStack>
            ))}
          </VStack>
        </MotionBox>
      );
    }
    return null;
  };

  // Custom dot for data points
  const CustomDot = (props: any) => {
    const { cx, cy, payload } = props;
    if (!chartSettings.showDots) return null;
    
    return (
      <circle
        cx={cx}
        cy={cy}
        r={3}
        fill={color}
        stroke="white"
        strokeWidth={2}
        style={{ cursor: 'pointer' }}
        onClick={() => onDataPointClick?.(payload)}
      />
    );
  };

  return (
    <Box bg={bgColor} borderRadius="lg" p={4} position="relative">
      {/* Header */}
      <HStack justify="space-between" mb={4}>
        <VStack align="start" spacing={1}>
          {title && (
            <Text fontSize="lg" fontWeight="bold" color={textColor}>
              {title}
            </Text>
          )}
          <HStack spacing={2}>
            <Badge colorScheme="blue" variant="subtle">
              {data.length} veri noktası
            </Badge>
            {zoomLevel !== 1 && (
              <Badge colorScheme="green" variant="subtle">
                {zoomLevel}x zoom
              </Badge>
            )}
          </HStack>
        </VStack>

        <HStack spacing={2}>
          {/* Zoom Controls */}
          {enableZoom && (
            <ButtonGroup size="sm" isAttached variant="outline">
              <IconButton
                aria-label="Yakınlaştır"
                icon={<AddIcon />}
                onClick={() => handleZoom('in')}
                isDisabled={zoomLevel >= 3}
              />
              <IconButton
                aria-label="Uzaklaştır"
                icon={<MinusIcon />}
                onClick={() => handleZoom('out')}
                isDisabled={zoomLevel <= 0.5}
              />
            </ButtonGroup>
          )}

          {/* Export Button */}
          {enableExport && (
            <Tooltip label="Grafiği indir">
              <IconButton
                aria-label="İndir"
                icon={<DownloadIcon />}
                size="sm"
                variant="outline"
                onClick={exportChart}
              />
            </Tooltip>
          )}

          {/* Settings Menu */}
          <Menu>
            <MenuButton
              as={IconButton}
              aria-label="Ayarlar"
              icon={<SettingsIcon />}
              size="sm"
              variant="outline"
            />
            <MenuList>
              <Box p={3}>
                <VStack spacing={3} align="stretch">
                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="show-grid" mb="0" fontSize="sm">
                      Izgara
                    </FormLabel>
                    <Switch
                      id="show-grid"
                      size="sm"
                      isChecked={chartSettings.showGrid}
                      onChange={(e) => setChartSettings(prev => ({
                        ...prev,
                        showGrid: e.target.checked
                      }))}
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="show-dots" mb="0" fontSize="sm">
                      Noktalar
                    </FormLabel>
                    <Switch
                      id="show-dots"
                      size="sm"
                      isChecked={chartSettings.showDots}
                      onChange={(e) => setChartSettings(prev => ({
                        ...prev,
                        showDots: e.target.checked
                      }))}
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="show-area" mb="0" fontSize="sm">
                      Alan
                    </FormLabel>
                    <Switch
                      id="show-area"
                      size="sm"
                      isChecked={chartSettings.showArea}
                      onChange={(e) => setChartSettings(prev => ({
                        ...prev,
                        showArea: e.target.checked
                      }))}
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="show-ma" mb="0" fontSize="sm">
                      Hareketli Ortalama
                    </FormLabel>
                    <Switch
                      id="show-ma"
                      size="sm"
                      isChecked={chartSettings.showMovingAverage}
                      onChange={(e) => setChartSettings(prev => ({
                        ...prev,
                        showMovingAverage: e.target.checked
                      }))}
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center">
                    <FormLabel htmlFor="show-trend" mb="0" fontSize="sm">
                      Trend Çizgisi
                    </FormLabel>
                    <Switch
                      id="show-trend"
                      size="sm"
                      isChecked={chartSettings.showTrendLine}
                      onChange={(e) => setChartSettings(prev => ({
                        ...prev,
                        showTrendLine: e.target.checked
                      }))}
                    />
                  </FormControl>
                </VStack>
              </Box>
            </MenuList>
          </Menu>
        </HStack>
      </HStack>

      {/* Chart */}
      <Box h={`${height}px`}>
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart data={finalData}>
            {chartSettings.showGrid && (
              <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
            )}
            <XAxis 
              dataKey="date" 
              tickFormatter={formatDate}
              stroke={textColor}
            />
            <YAxis 
              tickFormatter={formatValue}
              stroke={textColor}
            />
            <RechartsTooltip content={<CustomTooltip />} />
            
            {/* Volume bars (if enabled) */}
            {showVolume && (
              <Bar 
                dataKey="volume" 
                fill={color} 
                opacity={0.3}
                yAxisId="volume"
              />
            )}

            {/* Area (if enabled) */}
            {chartSettings.showArea && (
              <Area
                type={chartSettings.smoothLine ? "monotone" : "linear"}
                dataKey={dataKey}
                stroke={color}
                fill={color}
                fillOpacity={0.1}
              />
            )}

            {/* Main line */}
            <Line
              type={chartSettings.smoothLine ? "monotone" : "linear"}
              dataKey={dataKey}
              stroke={color}
              strokeWidth={2}
              dot={<CustomDot />}
              activeDot={{ r: 6, stroke: color, strokeWidth: 2, fill: 'white' }}
            />

            {/* Moving Average */}
            {chartSettings.showMovingAverage && (
              <Line
                type="monotone"
                dataKey="ma"
                stroke="#ff7300"
                strokeWidth={1}
                strokeDasharray="5 5"
                dot={false}
                name="7-günlük ortalama"
              />
            )}

            {/* Trend Line */}
            {chartSettings.showTrendLine && (
              <Line
                type="linear"
                dataKey="trend"
                stroke="#8884d8"
                strokeWidth={1}
                strokeDasharray="10 5"
                dot={false}
                name="Trend"
              />
            )}

            {/* Brush for zooming */}
            {showBrush && (
              <Brush 
                dataKey="date" 
                height={30} 
                stroke={color}
                tickFormatter={formatDate}
              />
            )}
          </ComposedChart>
        </ResponsiveContainer>
      </Box>

      {/* Chart Statistics */}
      <HStack justify="space-between" mt={4} fontSize="sm" color="gray.500">
        <Text>
          Min: {formatValue(Math.min(...data.map(d => d[dataKey])))}
        </Text>
        <Text>
          Max: {formatValue(Math.max(...data.map(d => d[dataKey])))}
        </Text>
        <Text>
          Ort: {formatValue(data.reduce((sum, d) => sum + d[dataKey], 0) / data.length)}
        </Text>
      </HStack>
    </Box>
  );
};

export default AdvancedLineChart;
