import React from 'react';
import { useEnhancedStatistics } from '../../hooks/useEnhancedStatistics';
import RobotComparison from '../../components/statistics/robots/RobotComparison';
import { Box, Text } from '@chakra-ui/react';

const RobotComparisonPage: React.FC = () => {
  const { stats, loading, error } = useEnhancedStatistics();

  if (loading) {
    return (
      <Box p={8} textAlign="center">
        <Text>Yükleniyor...</Text>
      </Box>
    );
  }

  if (error || !stats) {
    return (
      <Box p={8} textAlign="center">
        <Text color="red.500">Veri yüklenirken hata oluştu</Text>
      </Box>
    );
  }

  return <RobotComparison stats={stats} />;
};

export default RobotComparisonPage;
