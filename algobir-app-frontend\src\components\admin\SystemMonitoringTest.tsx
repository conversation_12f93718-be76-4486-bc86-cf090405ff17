import React from 'react';
import {
  <PERSON>,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  Badge,
  Spinner,
  Alert,
  AlertIcon,
  SimpleGrid,
  Progress,
  Button
} from '@chakra-ui/react';
import { useSystemMonitoring } from '../../hooks/useSystemMonitoring';

/**
 * Test component to verify that the real-time system monitoring works correctly
 */
const SystemMonitoringTest: React.FC = () => {
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  // Test the system monitoring hook
  const {
    data,
    loading,
    error,
    lastRefresh,
    refetch,
    isConnected
  } = useSystemMonitoring({
    refreshInterval: 10000, // 10 seconds for testing
    enabled: true
  });

  return (
    <Card
      bg={cardBg}
      borderRadius="xl"
      boxShadow="md"
      border="1px solid"
      borderColor={borderColor}
    >
      <CardHeader>
        <HStack justify="space-between">
          <Heading size="md" color={textColor}>
            System Monitoring Test
          </Heading>
          <Button size="sm" onClick={refetch} isLoading={loading}>
            Refresh
          </Button>
        </HStack>
      </CardHeader>
      <CardBody>
        <VStack spacing={4} align="stretch">
          {/* Connection Status */}
          <HStack justify="space-between">
            <Text fontSize="sm" fontWeight="medium">Connection Status:</Text>
            <Badge colorScheme={isConnected ? 'green' : 'red'}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </HStack>

          {/* Loading State */}
          <HStack justify="space-between">
            <Text fontSize="sm" fontWeight="medium">Loading:</Text>
            {loading ? <Spinner size="sm" /> : <Badge colorScheme="green">Ready</Badge>}
          </HStack>

          {/* Error State */}
          {error && (
            <Alert status="error" size="sm">
              <AlertIcon />
              <Text fontSize="sm">{error}</Text>
            </Alert>
          )}

          {/* Last Refresh */}
          {lastRefresh && (
            <HStack justify="space-between">
              <Text fontSize="sm" fontWeight="medium">Last Refresh:</Text>
              <Text fontSize="sm">{lastRefresh.toLocaleTimeString()}</Text>
            </HStack>
          )}

          {/* System Data Summary */}
          {data && (
            <VStack spacing={4} align="stretch">
              <Text fontSize="sm" fontWeight="bold" color={textColor}>
                System Data Summary:
              </Text>

              {/* Services */}
              <Box>
                <Text fontSize="sm" fontWeight="medium" mb={2}>Services ({data.services.length}):</Text>
                <SimpleGrid columns={2} spacing={2}>
                  {data.services.map((service) => (
                    <HStack key={service.name} justify="space-between">
                      <Text fontSize="xs">{service.name}:</Text>
                      <Badge 
                        colorScheme={
                          service.status === 'healthy' ? 'green' : 
                          service.status === 'warning' ? 'yellow' : 'red'
                        }
                        size="sm"
                      >
                        {service.status}
                      </Badge>
                    </HStack>
                  ))}
                </SimpleGrid>
              </Box>

              {/* Metrics */}
              <Box>
                <Text fontSize="sm" fontWeight="medium" mb={2}>System Metrics:</Text>
                <VStack spacing={1} align="stretch" fontSize="xs">
                  <HStack justify="space-between">
                    <Text>CPU Usage:</Text>
                    <Text>{data.metrics.cpu.toFixed(1)}%</Text>
                  </HStack>
                  <Progress value={data.metrics.cpu} size="xs" colorScheme="orange" />
                  
                  <HStack justify="space-between">
                    <Text>Memory Usage:</Text>
                    <Text>{data.metrics.memory.toFixed(1)}%</Text>
                  </HStack>
                  <Progress value={data.metrics.memory} size="xs" colorScheme="purple" />
                  
                  <HStack justify="space-between">
                    <Text>Network Load:</Text>
                    <Text>{data.metrics.network.toFixed(1)}%</Text>
                  </HStack>
                  <Progress value={data.metrics.network} size="xs" colorScheme="green" />
                  
                  <HStack justify="space-between">
                    <Text>Requests:</Text>
                    <Text>{data.metrics.requests}</Text>
                  </HStack>
                  
                  <HStack justify="space-between">
                    <Text>Errors:</Text>
                    <Text color={data.metrics.errors > 0 ? 'red.500' : 'green.500'}>
                      {data.metrics.errors}
                    </Text>
                  </HStack>
                  
                  <HStack justify="space-between">
                    <Text>Response Time:</Text>
                    <Text>{data.metrics.responseTime}ms</Text>
                  </HStack>
                </VStack>
              </Box>

              {/* Uptime */}
              <Box>
                <Text fontSize="sm" fontWeight="medium" mb={2}>Uptime Metrics:</Text>
                <VStack spacing={1} align="stretch" fontSize="xs">
                  <HStack justify="space-between">
                    <Text>Overall:</Text>
                    <Text>{data.uptime.overall.toFixed(2)}%</Text>
                  </HStack>
                  <HStack justify="space-between">
                    <Text>Frontend:</Text>
                    <Text>{data.uptime.frontend.toFixed(2)}%</Text>
                  </HStack>
                  <HStack justify="space-between">
                    <Text>Backend:</Text>
                    <Text>{data.uptime.backend.toFixed(2)}%</Text>
                  </HStack>
                  <HStack justify="space-between">
                    <Text>Database:</Text>
                    <Text>{data.uptime.database.toFixed(2)}%</Text>
                  </HStack>
                  <HStack justify="space-between">
                    <Text>Edge Functions:</Text>
                    <Text>{data.uptime.edgeFunctions.toFixed(2)}%</Text>
                  </HStack>
                </VStack>
              </Box>

              {/* Health Status */}
              <HStack justify="space-between">
                <Text fontSize="sm" fontWeight="medium">System Health:</Text>
                <Badge 
                  colorScheme={data.isHealthy ? 'green' : 'red'}
                  variant="solid"
                >
                  {data.isHealthy ? 'Healthy' : 'Issues Detected'}
                </Badge>
              </HStack>

              <HStack justify="space-between">
                <Text fontSize="sm" fontWeight="medium">Active Incidents:</Text>
                <Badge 
                  colorScheme={data.incidents === 0 ? 'green' : 'red'}
                  variant="subtle"
                >
                  {data.incidents}
                </Badge>
              </HStack>
            </VStack>
          )}

          {/* No Data State */}
          {!data && !loading && !error && (
            <Text fontSize="sm" color={mutedColor} textAlign="center">
              No system data available
            </Text>
          )}
        </VStack>
      </CardBody>
    </Card>
  );
};

export default SystemMonitoringTest;
