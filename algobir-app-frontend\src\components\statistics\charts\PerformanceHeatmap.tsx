import React, { useMemo } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  HStack,
  VStack,
  SimpleGrid,
  Badge,
  Icon,
  useColorModeValue,
  Tooltip,

} from '@chakra-ui/react';
import {
  FiCalendar,
  FiClock,
  FiTrendingUp,
  FiTrendingDown,
  FiActivity
} from 'react-icons/fi';

interface PerformanceHeatmapProps {
  timeData: Array<{
    hour: number;
    pnl: number;
    trades: number;
  }>;
  symbolData: Array<{
    symbol: string;
    pnl: number;
    trades: number;
    winRate: number;
  }>;
  monthlyData: Array<{
    month: string;
    pnl: number;
    trades: number;
    roi: number;
  }>;
  type?: 'time' | 'symbol' | 'monthly';
  title?: string;
}

const PerformanceHeatmap: React.FC<PerformanceHeatmapProps> = ({
  timeData,
  symbolData,
  monthlyData,
  type = 'time',
  title
}) => {
  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');
  const bgColor = useColorModeValue('gray.50', 'gray.800');

  // Enhanced formatting functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(value / 100);
  };

  // Get color intensity based on performance
  const getColorIntensity = (value: number, min: number, max: number) => {
    if (max === min) return 0.5;
    return (value - min) / (max - min);
  };

  // Get color scheme based on value
  const getColorScheme = (value: number) => {
    if (value > 0) return 'green';
    if (value < 0) return 'red';
    return 'gray';
  };

  // Time-based heatmap data
  const timeHeatmapData = useMemo(() => {
    if (!timeData.length) return [];
    
    const minPnL = Math.min(...timeData.map(d => d.pnl));
    const maxPnL = Math.max(...timeData.map(d => d.pnl));
    const maxTrades = Math.max(...timeData.map(d => d.trades));

    return timeData.map(item => ({
      ...item,
      intensity: getColorIntensity(Math.abs(item.pnl), 0, Math.max(Math.abs(minPnL), Math.abs(maxPnL))),
      colorScheme: getColorScheme(item.pnl),
      tradeIntensity: getColorIntensity(item.trades, 0, maxTrades),
      hourLabel: `${item.hour.toString().padStart(2, '0')}:00`
    }));
  }, [timeData]);

  // Symbol-based heatmap data
  const symbolHeatmapData = useMemo(() => {
    if (!symbolData.length) return [];
    
    const minPnL = Math.min(...symbolData.map(d => d.pnl));
    const maxPnL = Math.max(...symbolData.map(d => d.pnl));
    const maxTrades = Math.max(...symbolData.map(d => d.trades));

    return symbolData.slice(0, 20).map(item => ({
      ...item,
      intensity: getColorIntensity(Math.abs(item.pnl), 0, Math.max(Math.abs(minPnL), Math.abs(maxPnL))),
      colorScheme: getColorScheme(item.pnl),
      tradeIntensity: getColorIntensity(item.trades, 0, maxTrades)
    }));
  }, [symbolData]);

  // Monthly heatmap data
  const monthlyHeatmapData = useMemo(() => {
    if (!monthlyData.length) return [];
    
    const minPnL = Math.min(...monthlyData.map(d => d.pnl));
    const maxPnL = Math.max(...monthlyData.map(d => d.pnl));
    const maxTrades = Math.max(...monthlyData.map(d => d.trades));

    return monthlyData.map(item => ({
      ...item,
      intensity: getColorIntensity(Math.abs(item.pnl), 0, Math.max(Math.abs(minPnL), Math.abs(maxPnL))),
      colorScheme: getColorScheme(item.pnl),
      tradeIntensity: getColorIntensity(item.trades, 0, maxTrades)
    }));
  }, [monthlyData]);

  // Render heatmap cell
  const HeatmapCell = ({ item, showLabel = true }: { item: any; showLabel?: boolean }) => (
    <Tooltip
      label={
        <VStack spacing={1} align="start">
          <Text fontWeight="bold">
            {type === 'time' ? item.hourLabel : 
             type === 'symbol' ? item.symbol : 
             item.month}
          </Text>
          <Text>P&L: {formatCurrency(item.pnl)}</Text>
          <Text>İşlemler: {item.trades}</Text>
          {type === 'symbol' && <Text>Kazanma: {formatPercent(item.winRate)}</Text>}
          {type === 'monthly' && <Text>ROI: {formatPercent(item.roi)}</Text>}
        </VStack>
      }
      placement="top"
    >
      <Box
        bg={`${item.colorScheme}.${Math.round(item.intensity * 400) + 100}`}
        borderRadius="md"
        p={3}
        minH="60px"
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        cursor="pointer"
        transition="all 0.2s"
        _hover={{
          transform: 'scale(1.05)',
          shadow: 'md'
        }}
        border="1px"
        borderColor={borderColor}
      >
        {showLabel && (
          <Text fontSize="xs" fontWeight="bold" color="white" textAlign="center">
            {type === 'time' ? item.hourLabel : 
             type === 'symbol' ? item.symbol : 
             item.month}
          </Text>
        )}
        <Text fontSize="xs" color="white" fontWeight="medium">
          {formatCurrency(item.pnl)}
        </Text>
        <Text fontSize="xs" color="white" opacity={0.8}>
          {item.trades} işlem
        </Text>
      </Box>
    </Tooltip>
  );

  const getCurrentData = () => {
    switch (type) {
      case 'time':
        return timeHeatmapData;
      case 'symbol':
        return symbolHeatmapData;
      case 'monthly':
        return monthlyHeatmapData;
      default:
        return timeHeatmapData;
    }
  };

  const getCurrentTitle = () => {
    if (title) return title;
    switch (type) {
      case 'time':
        return 'Saatlik Performans Haritası';
      case 'symbol':
        return 'Sembol Performans Haritası';
      case 'monthly':
        return 'Aylık Performans Haritası';
      default:
        return 'Performans Haritası';
    }
  };

  const getCurrentIcon = () => {
    switch (type) {
      case 'time':
        return FiClock;
      case 'symbol':
        return FiActivity;
      case 'monthly':
        return FiCalendar;
      default:
        return FiTrendingUp;
    }
  };

  const getGridColumns = () => {
    switch (type) {
      case 'time':
        return { base: 4, md: 6, lg: 8, xl: 12 };
      case 'symbol':
        return { base: 2, md: 3, lg: 4, xl: 5 };
      case 'monthly':
        return { base: 2, md: 3, lg: 4, xl: 6 };
      default:
        return { base: 3, md: 4, lg: 6 };
    }
  };

  const currentData = getCurrentData();
  const stats = useMemo(() => {
    const totalPnL = currentData.reduce((sum, item) => sum + item.pnl, 0);
    const totalTrades = currentData.reduce((sum, item) => sum + item.trades, 0);
    const profitableItems = currentData.filter(item => item.pnl > 0).length;
    const bestPerformer = currentData.reduce((best, item) => 
      item.pnl > best.pnl ? item : best, currentData[0] || { pnl: 0 });
    const worstPerformer = currentData.reduce((worst, item) => 
      item.pnl < worst.pnl ? item : worst, currentData[0] || { pnl: 0 });

    return {
      totalPnL,
      totalTrades,
      profitableItems,
      successRate: currentData.length > 0 ? (profitableItems / currentData.length) * 100 : 0,
      bestPerformer,
      worstPerformer
    };
  }, [currentData]);

  return (
    <Card bg={cardBg} borderRadius="xl" shadow="sm">
      <CardHeader pb={2}>
        <HStack justify="space-between" flexWrap="wrap">
          <VStack align="start" spacing={1}>
            <HStack spacing={3}>
              <Icon as={getCurrentIcon()} color="blue.500" boxSize={6} />
              <Heading size="md" color={textColor}>
                {getCurrentTitle()}
              </Heading>
            </HStack>
            <HStack spacing={4} flexWrap="wrap">
              <Badge 
                colorScheme={stats.totalPnL > 0 ? 'green' : 'red'} 
                variant="subtle"
                px={3}
                py={1}
              >
                <Icon as={stats.totalPnL > 0 ? FiTrendingUp : FiTrendingDown} mr={1} />
                {formatCurrency(stats.totalPnL)}
              </Badge>
              <Badge colorScheme="blue" variant="subtle" px={3} py={1}>
                {stats.successRate.toFixed(1)}% Başarı
              </Badge>
              <Badge colorScheme="purple" variant="subtle" px={3} py={1}>
                {stats.totalTrades} İşlem
              </Badge>
            </HStack>
          </VStack>
        </HStack>
      </CardHeader>

      <CardBody pt={0}>
        {currentData.length > 0 ? (
          <>
            <SimpleGrid columns={getGridColumns()} spacing={3} mb={6}>
              {currentData.map((item, index) => (
                <HeatmapCell key={index} item={item} />
              ))}
            </SimpleGrid>

            {/* Performance Summary */}
            <VStack spacing={4} p={4} bg={bgColor} borderRadius="lg">
              <Text fontSize="sm" fontWeight="semibold" color={textColor}>
                Performans Özeti
              </Text>
              <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4} w="full">
                <VStack spacing={1}>
                  <Text fontSize="xs" color="gray.500">En İyi</Text>
                  <Text fontSize="sm" fontWeight="bold" color="green.500">
                    {type === 'time' ? (stats.bestPerformer as any).hourLabel :
                     type === 'symbol' ? (stats.bestPerformer as any).symbol :
                     (stats.bestPerformer as any).month}
                  </Text>
                  <Text fontSize="xs" color="green.500">
                    {formatCurrency(stats.bestPerformer.pnl)}
                  </Text>
                </VStack>
                <VStack spacing={1}>
                  <Text fontSize="xs" color="gray.500">En Kötü</Text>
                  <Text fontSize="sm" fontWeight="bold" color="red.500">
                    {type === 'time' ? (stats.worstPerformer as any).hourLabel :
                     type === 'symbol' ? (stats.worstPerformer as any).symbol :
                     (stats.worstPerformer as any).month}
                  </Text>
                  <Text fontSize="xs" color="red.500">
                    {formatCurrency(stats.worstPerformer.pnl)}
                  </Text>
                </VStack>
                <VStack spacing={1}>
                  <Text fontSize="xs" color="gray.500">Karlı {type === 'time' ? 'Saatler' : type === 'symbol' ? 'Semboller' : 'Aylar'}</Text>
                  <Text fontSize="sm" fontWeight="bold" color="blue.500">
                    {stats.profitableItems}/{currentData.length}
                  </Text>
                  <Text fontSize="xs" color="blue.500">
                    {stats.successRate.toFixed(1)}%
                  </Text>
                </VStack>
                <VStack spacing={1}>
                  <Text fontSize="xs" color="gray.500">Ortalama</Text>
                  <Text 
                    fontSize="sm" 
                    fontWeight="bold" 
                    color={stats.totalPnL > 0 ? 'green.500' : 'red.500'}
                  >
                    {formatCurrency(stats.totalPnL / Math.max(1, currentData.length))}
                  </Text>
                  <Text fontSize="xs" color="gray.500">
                    per {type === 'time' ? 'saat' : type === 'symbol' ? 'sembol' : 'ay'}
                  </Text>
                </VStack>
              </SimpleGrid>
            </VStack>
          </>
        ) : (
          <VStack spacing={4} py={8} textAlign="center">
            <Icon as={getCurrentIcon()} boxSize={12} color="gray.400" />
            <Text color="gray.500">Henüz veri bulunmuyor</Text>
          </VStack>
        )}
      </CardBody>
    </Card>
  );
};

export default PerformanceHeatmap;
