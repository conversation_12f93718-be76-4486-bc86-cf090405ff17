import React from 'react';
import { Outlet } from 'react-router-dom';
import StatisticsLayout from '../components/statistics/StatisticsLayout';

// This is the main Statistics component - now uses nested routing with Outlet
// The StatisticsLayout provides the common layout and the Outlet renders the specific sub-page

const EnhancedStatistics: React.FC = () => {
  return (
    <StatisticsLayout>
      <Outlet />
    </StatisticsLayout>
  );
};

export default EnhancedStatistics;

// Also export as Statistics for backward compatibility
export { EnhancedStatistics as Statistics };
