import { Box, Flex, Stack, Text, Heading, useColorModeValue } from '@chakra-ui/react';
import { RouteType } from '../../../routes';
import { SidebarLinks } from './Links.tsx';
import SidebarPromotionCard from './SidebarPromotionCard';

// FUNCTIONS
function SidebarContent(props: { routes: RouteType[] }) {
  const { routes } = props;
  
  // Horizon UI renk değerleri
  const brandColor = useColorModeValue('brand.500', 'white');
  const textColor = useColorModeValue('secondaryGray.600', 'white');
  const separatorColor = useColorModeValue('gray.200', 'navy.600');
  
  // SIDEBAR
  return (
    <Flex direction='column' minH='100vh' pt='20px' px='16px' pb='20px'>
      {/* Brand Logo/Başlık */}
      <Flex
        h="60px"
        mb="24px"
        alignItems="center"
        justifyContent="flex-start"
        borderBottom="1px solid"
        borderColor={separatorColor}
        pb="16px"
        flexShrink={0}
        w="100%"
      >
        <Heading 
          size="lg" 
          fontWeight="bold" 
          color={brandColor} 
          noOfLines={1}
          letterSpacing="-0.025em"
        >
          Algobir
        </Heading>
      </Flex>
      
      {/* Ana Navigasyon - scroll edilebilir alan */}
      <Stack direction='column' spacing={1} flexGrow={1} w="100%">
        <Box w="100%">
          <Text
            color={textColor}
            fontWeight="600"
            mb="16px"
            fontSize="xs"
            letterSpacing="0.05em"
            textTransform="uppercase"
          >
            Ana Menü
          </Text>
          <SidebarLinks routes={routes} />
        </Box>
      </Stack>

      {/* Promosyon kartı - her zaman alt kısımda */}
      <Box mt='24px' mb='16px' borderRadius='16px' flexShrink={0} w="100%">
        <SidebarPromotionCard />
      </Box>

      {/* Alt bölüm - telif hakkı bilgisi - her zaman en altta */}
      <Box
        textAlign="center"
        pt="16px"
        borderTop="1px solid"
        borderColor={separatorColor}
        flexShrink={0}
        w="100%"
      >
        <Text 
          fontSize="xs" 
          color={textColor}
          fontWeight="500"
        >
          © {new Date().getFullYear()} Algobir
        </Text>
        <Text 
          fontSize="xs" 
          color={textColor}
          mt="4px"
          opacity={0.7}
        >
          v2.0.0
        </Text>
      </Box>
    </Flex>
  );
}

export default SidebarContent; 