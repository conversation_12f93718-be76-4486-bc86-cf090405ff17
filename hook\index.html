<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AlgoBir Webhook Servisi</title>
    <style>
        body { 
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: #f4f7fe;
            color: #1B254B;
            text-align: center;
        }
        .container {
            padding: 40px;
            background-color: white;
            border-radius: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        p {
            font-size: 16px;
            color: #6A738B;
        }
        code {
            background-color: #e0e5f2;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AlgoBir Webhook Servisi</h1>
        <p>Durum: <strong>Aktif</strong></p>
        <p>Bu adres, otomatik sinyal işleme için kullanılmaktadır.<br>Doğrudan tarayıcı erişimi için tasarlanmamıştır.</p>
        <p><small>Örnek kullanım: <code>/algobir-webhook-listener/...</code></small></p>
    </div>
</body>
</html>