import { test, expect } from '@playwright/test';

test.describe('Order Transmission Monitoring Performance Validation', () => {
  test.beforeEach(async ({ page }) => {
    // Mock Supabase with realistic data
    await page.addInitScript(() => {
      // Generate mock data for performance testing
      const generateMockMetrics = (count: number) => {
        return Array.from({ length: count }, (_, i) => ({
          id: `metric-${i}`,
          trade_id: 1000 + i,
          signal_type: i % 2 === 0 ? 'BUY' : 'SELL',
          symbol: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT'][i % 4],
          order_side: i % 2 === 0 ? 'BUY' : 'SELL',
          json_parsing_time_ms: 3 + Math.random() * 10,
          transformation_time_ms: 10 + Math.random() * 30,
          webhook_delivery_time_ms: 20 + Math.random() * 100,
          total_processing_time_ms: 35 + Math.random() * 140,
          signal_source: i % 3 === 0 ? 'solo-robot' : 'bro-robot',
          processing_status: Math.random() > 0.05 ? 'success' : 'partial',
          created_at: new Date(Date.now() - i * 60000).toISOString(),
          signal_received_at: new Date(Date.now() - i * 60000).toISOString(),
          processing_completed_at: new Date(Date.now() - i * 60000).toISOString()
        }));
      };

      window.mockSupabase = {
        auth: {
          getUser: () => Promise.resolve({
            data: { user: { id: 'test-admin', email: '<EMAIL>' } },
            error: null
          })
        },
        from: () => ({
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({
                data: { is_superuser: true },
                error: null
              })
            })
          })
        }),
        rpc: (functionName: string, params: any) => {
          if (functionName === 'get_order_transmission_metrics') {
            const count = params?.p_limit || 100;
            return new Promise(resolve => {
              // Simulate realistic API delay
              setTimeout(() => {
                resolve({
                  data: generateMockMetrics(count),
                  error: null
                });
              }, 50 + Math.random() * 100);
            });
          }
          if (functionName === 'get_order_transmission_stats') {
            return new Promise(resolve => {
              setTimeout(() => {
                resolve({
                  data: [{
                    total_signals: 1500,
                    avg_json_parsing_time_ms: 6.8,
                    avg_transformation_time_ms: 22.4,
                    avg_webhook_delivery_time_ms: 68.2,
                    avg_total_processing_time_ms: 97.4,
                    p95_total_processing_time_ms: 185.6,
                    p99_total_processing_time_ms: 298.3,
                    success_rate: 97.8,
                    signals_per_minute: 3.2,
                    fastest_processing_time_ms: 28.5,
                    slowest_processing_time_ms: 456.7,
                    solo_robot_count: 900,
                    bro_robot_count: 600
                  }],
                  error: null
                });
              }, 30 + Math.random() * 70);
            });
          }
          return Promise.resolve({ data: [], error: null });
        },
        channel: () => ({
          on: () => ({ subscribe: () => Promise.resolve() }),
          subscribe: () => Promise.resolve()
        }),
        removeChannel: () => {}
      };
    });
  });

  test('should load admin status page within acceptable time limits', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/admin/status');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Page should load within 5 seconds
    expect(loadTime).toBeLessThan(5000);
    
    // Monitoring section should be visible
    await expect(page.locator('text=Emir İletim Hızı Monitörü')).toBeVisible();
  });

  test('should handle large datasets without performance degradation', async ({ page }) => {
    // Override mock to return large dataset
    await page.addInitScript(() => {
      const originalRpc = window.mockSupabase.rpc;
      window.mockSupabase.rpc = (functionName: string, params: any) => {
        if (functionName === 'get_order_transmission_metrics') {
          // Return large dataset (1000 records)
          const count = 1000;
          const data = Array.from({ length: count }, (_, i) => ({
            id: `metric-${i}`,
            trade_id: 1000 + i,
            signal_type: i % 2 === 0 ? 'BUY' : 'SELL',
            symbol: ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT'][i % 4],
            order_side: i % 2 === 0 ? 'BUY' : 'SELL',
            json_parsing_time_ms: 3 + Math.random() * 10,
            transformation_time_ms: 10 + Math.random() * 30,
            webhook_delivery_time_ms: 20 + Math.random() * 100,
            total_processing_time_ms: 35 + Math.random() * 140,
            signal_source: i % 3 === 0 ? 'solo-robot' : 'bro-robot',
            processing_status: Math.random() > 0.05 ? 'success' : 'partial',
            created_at: new Date(Date.now() - i * 60000).toISOString(),
            signal_received_at: new Date(Date.now() - i * 60000).toISOString(),
            processing_completed_at: new Date(Date.now() - i * 60000).toISOString()
          }));
          
          return Promise.resolve({ data, error: null });
        }
        return originalRpc(functionName, params);
      };
    });

    const startTime = Date.now();
    
    await page.goto('/admin/status');
    await page.waitForLoadState('networkidle');
    
    // Switch to performance metrics tab to trigger data rendering
    await page.locator('text=Performans Metrikleri').click();
    await page.waitForTimeout(2000);
    
    const renderTime = Date.now() - startTime;
    
    // Should handle large dataset within reasonable time (10 seconds)
    expect(renderTime).toBeLessThan(10000);
    
    // Content should still be visible and functional
    await expect(page.locator('text=Toplam Sinyal')).toBeVisible();
    await expect(page.locator('text=1500')).toBeVisible(); // Total signals
  });

  test('should maintain responsive interactions with real-time updates', async ({ page }) => {
    await page.goto('/admin/status');
    await page.waitForLoadState('networkidle');
    
    // Measure tab switching performance
    const startTime = Date.now();
    
    await page.locator('text=Performans Metrikleri').click();
    await page.waitForSelector('text=Toplam Sinyal', { timeout: 3000 });
    
    const switchTime = Date.now() - startTime;
    
    // Tab switching should be fast (under 1 second)
    expect(switchTime).toBeLessThan(1000);
    
    // Switch back to chart tab
    const chartSwitchStart = Date.now();
    
    await page.locator('text=Canlı Grafik').click();
    await page.waitForTimeout(500);
    
    const chartSwitchTime = Date.now() - chartSwitchStart;
    
    // Chart tab switching should also be fast
    expect(chartSwitchTime).toBeLessThan(1000);
  });

  test('should not cause memory leaks with continuous updates', async ({ page }) => {
    await page.goto('/admin/status');
    await page.waitForLoadState('networkidle');
    
    // Get initial memory usage
    const initialMemory = await page.evaluate(() => {
      if (performance.memory) {
        return performance.memory.usedJSHeapSize;
      }
      return 0;
    });
    
    // Simulate continuous updates by switching tabs multiple times
    for (let i = 0; i < 10; i++) {
      await page.locator('text=Performans Metrikleri').click();
      await page.waitForTimeout(200);
      await page.locator('text=Canlı Grafik').click();
      await page.waitForTimeout(200);
    }
    
    // Force garbage collection if available
    await page.evaluate(() => {
      if (window.gc) {
        window.gc();
      }
    });
    
    await page.waitForTimeout(1000);
    
    // Get final memory usage
    const finalMemory = await page.evaluate(() => {
      if (performance.memory) {
        return performance.memory.usedJSHeapSize;
      }
      return 0;
    });
    
    if (initialMemory > 0 && finalMemory > 0) {
      const memoryIncrease = finalMemory - initialMemory;
      const memoryIncreasePercent = (memoryIncrease / initialMemory) * 100;
      
      // Memory increase should be reasonable (less than 50% increase)
      expect(memoryIncreasePercent).toBeLessThan(50);
    }
  });

  test('should handle network delays gracefully', async ({ page }) => {
    // Simulate slow network
    await page.route('**/*', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 100)); // 100ms delay
      await route.continue();
    });
    
    const startTime = Date.now();
    
    await page.goto('/admin/status');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Should still load within reasonable time even with network delays
    expect(loadTime).toBeLessThan(8000);
    
    // Content should be visible
    await expect(page.locator('text=Emir İletim Hızı Monitörü')).toBeVisible();
  });

  test('should maintain performance with multiple chart interactions', async ({ page }) => {
    await page.goto('/admin/status');
    await page.waitForLoadState('networkidle');
    
    // Test multiple chart type switches if controls are available
    const chartTypeButtons = page.locator('button').filter({ hasText: /Çizgi|Alan|Çubuk/ });
    
    if (await chartTypeButtons.count() > 0) {
      const startTime = Date.now();
      
      // Switch between chart types multiple times
      for (let i = 0; i < 5; i++) {
        const buttons = await chartTypeButtons.all();
        for (const button of buttons) {
          if (await button.isVisible()) {
            await button.click();
            await page.waitForTimeout(100);
          }
        }
      }
      
      const interactionTime = Date.now() - startTime;
      
      // Chart interactions should be responsive (under 3 seconds total)
      expect(interactionTime).toBeLessThan(3000);
    }
  });

  test('should handle concurrent data updates efficiently', async ({ page }) => {
    // Mock rapid data updates
    await page.addInitScript(() => {
      let updateCount = 0;
      const originalRpc = window.mockSupabase.rpc;
      
      window.mockSupabase.rpc = (functionName: string, params: any) => {
        updateCount++;
        
        // Simulate data changes on each call
        if (functionName === 'get_order_transmission_stats') {
          return Promise.resolve({
            data: [{
              total_signals: 1500 + updateCount,
              avg_json_parsing_time_ms: 6.8 + (updateCount * 0.1),
              avg_transformation_time_ms: 22.4 + (updateCount * 0.2),
              avg_webhook_delivery_time_ms: 68.2 + (updateCount * 0.5),
              avg_total_processing_time_ms: 97.4 + (updateCount * 0.8),
              p95_total_processing_time_ms: 185.6,
              p99_total_processing_time_ms: 298.3,
              success_rate: Math.max(95, 97.8 - (updateCount * 0.1)),
              signals_per_minute: 3.2 + (updateCount * 0.05),
              fastest_processing_time_ms: 28.5,
              slowest_processing_time_ms: 456.7,
              solo_robot_count: 900 + updateCount,
              bro_robot_count: 600 + updateCount
            }],
            error: null
          });
        }
        
        return originalRpc(functionName, params);
      };
    });
    
    await page.goto('/admin/status');
    await page.waitForLoadState('networkidle');
    
    // Switch to performance metrics to trigger updates
    await page.locator('text=Performans Metrikleri').click();
    
    const startTime = Date.now();
    
    // Trigger multiple rapid updates by refreshing
    for (let i = 0; i < 5; i++) {
      const refreshButton = page.locator('button:has-text("Yenile")');
      if (await refreshButton.count() > 0) {
        await refreshButton.first().click();
        await page.waitForTimeout(200);
      }
    }
    
    const updateTime = Date.now() - startTime;
    
    // Multiple updates should complete within reasonable time
    expect(updateTime).toBeLessThan(5000);
    
    // Data should still be visible and updated
    await expect(page.locator('text=Toplam Sinyal')).toBeVisible();
  });

  test('should maintain accessibility during performance operations', async ({ page }) => {
    await page.goto('/admin/status');
    await page.waitForLoadState('networkidle');

    // Check that interactive elements remain accessible during operations
    await page.locator('text=Performans Metrikleri').click();

    // Verify keyboard navigation works
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');

    // Check that focus is visible and functional
    const focusedElement = await page.locator(':focus');
    await expect(focusedElement).toBeVisible();

    // Verify screen reader compatibility
    const monitoringSection = page.locator('text=Emir İletim Hızı Monitörü').locator('..');
    await expect(monitoringSection).toBeVisible();
  });

  test('should validate real-time data accuracy', async ({ page }) => {
    await page.goto('/admin/status');
    await page.waitForLoadState('networkidle');

    // Switch to performance metrics
    await page.locator('text=Performans Metrikleri').click();
    await page.waitForTimeout(1000);

    // Capture initial values
    const initialSignalCount = await page.locator('text=Toplam Sinyal').locator('..').locator('text=/\\d+/').first().textContent();
    const initialSuccessRate = await page.locator('text=Başarı Oranı').locator('..').locator('text=/%\\d+\\.\\d+/').first().textContent();

    // Trigger refresh
    const refreshButton = page.locator('button:has-text("Yenile")');
    if (await refreshButton.count() > 0) {
      await refreshButton.first().click();
      await page.waitForTimeout(1000);
    }

    // Verify data is still present and valid
    await expect(page.locator('text=Toplam Sinyal')).toBeVisible();
    await expect(page.locator('text=Başarı Oranı')).toBeVisible();

    // Values should be numeric and reasonable
    const currentSignalCount = await page.locator('text=Toplam Sinyal').locator('..').locator('text=/\\d+/').first().textContent();
    const currentSuccessRate = await page.locator('text=Başarı Oranı').locator('..').locator('text=/%\\d+\\.\\d+/').first().textContent();

    if (currentSignalCount) {
      const signalNum = parseInt(currentSignalCount.replace(/[^\d]/g, ''));
      expect(signalNum).toBeGreaterThan(0);
      expect(signalNum).toBeLessThan(1000000); // Reasonable upper bound
    }

    if (currentSuccessRate) {
      const successNum = parseFloat(currentSuccessRate.replace(/[^\d.]/g, ''));
      expect(successNum).toBeGreaterThanOrEqual(0);
      expect(successNum).toBeLessThanOrEqual(100);
    }
  });
});
