import React, { useMemo, useState } from 'react';
import {
  Box,
  HStack,
  VStack,
  Text,

  useColorModeValue,
  Badge,
  Tooltip,
  SimpleGrid,
  Select,
  IconButton,
  Menu,
  MenuButton,
  Menu<PERSON>ist,
  MenuItem
} from '@chakra-ui/react';
import { SettingsIcon, DownloadIcon } from '@chakra-ui/icons';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

interface HeatmapDataPoint {
  x: string | number;
  y: string | number;
  value: number;
  label?: string;
  [key: string]: any;
}

interface HeatmapChartProps {
  data: HeatmapDataPoint[];
  title?: string;
  xAxisLabel?: string;
  yAxisLabel?: string;
  width?: number;
  height?: number;
  cellSize?: number;
  colorScheme?: 'blue' | 'green' | 'red' | 'purple' | 'orange';
  showValues?: boolean;
  showTooltip?: boolean;
  formatValue?: (value: number) => string;
  onCellClick?: (data: HeatmapDataPoint) => void;
}

const colorSchemes = {
  blue: ['#EBF8FF', '#BEE3F8', '#90CDF4', '#63B3ED', '#4299E1', '#3182CE', '#2B77CB', '#2C5282'],
  green: ['#F0FFF4', '#C6F6D5', '#9AE6B4', '#68D391', '#48BB78', '#38A169', '#2F855A', '#276749'],
  red: ['#FED7D7', '#FEB2B2', '#FC8181', '#F56565', '#E53E3E', '#C53030', '#9B2C2C', '#742A2A'],
  purple: ['#FAF5FF', '#E9D8FD', '#D6BCFA', '#B794F6', '#9F7AEA', '#805AD5', '#6B46C1', '#553C9A'],
  orange: ['#FFFAF0', '#FEEBC8', '#FBD38D', '#F6AD55', '#ED8936', '#DD6B20', '#C05621', '#9C4221']
};

const HeatmapChart: React.FC<HeatmapChartProps> = ({
  data,
  title,
  xAxisLabel,
  yAxisLabel,
  width = 800,
  height = 400,
  cellSize = 40,
  colorScheme = 'blue',
  showValues = true,
  showTooltip = true,
  formatValue = (value: number) => value.toFixed(2),
  onCellClick
}) => {
  const [hoveredCell, setHoveredCell] = useState<HeatmapDataPoint | null>(null);
  const [selectedColorScheme, setSelectedColorScheme] = useState(colorScheme);

  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Process data to create matrix
  const { matrix, xLabels, yLabels, minValue, maxValue } = useMemo(() => {
    const xSet = new Set<string>();
    const ySet = new Set<string>();
    
    data.forEach(point => {
      xSet.add(String(point.x));
      ySet.add(String(point.y));
    });

    const xLabels = Array.from(xSet).sort();
    const yLabels = Array.from(ySet).sort();
    
    const matrix: (HeatmapDataPoint | null)[][] = yLabels.map(() => 
      xLabels.map(() => null)
    );

    let minValue = Infinity;
    let maxValue = -Infinity;

    data.forEach(point => {
      const xIndex = xLabels.indexOf(String(point.x));
      const yIndex = yLabels.indexOf(String(point.y));
      
      if (xIndex !== -1 && yIndex !== -1) {
        matrix[yIndex][xIndex] = point;
        minValue = Math.min(minValue, point.value);
        maxValue = Math.max(maxValue, point.value);
      }
    });

    return { matrix, xLabels, yLabels, minValue, maxValue };
  }, [data]);

  // Get color for value
  const getColor = (value: number | null) => {
    if (value === null) return 'transparent';
    
    const colors = colorSchemes[selectedColorScheme];
    const normalizedValue = (value - minValue) / (maxValue - minValue);
    const colorIndex = Math.floor(normalizedValue * (colors.length - 1));
    
    return colors[Math.max(0, Math.min(colorIndex, colors.length - 1))];
  };

  // Get text color based on background
  const getTextColor = (value: number | null) => {
    if (value === null) return textColor;
    
    const normalizedValue = (value - minValue) / (maxValue - minValue);
    return normalizedValue > 0.5 ? 'white' : textColor;
  };

  // Calculate dimensions
  const chartWidth = Math.max(width, xLabels.length * cellSize + 100);
  const chartHeight = Math.max(height, yLabels.length * cellSize + 100);

  return (
    <Box bg={bgColor} borderRadius="lg" p={4} border="1px" borderColor={borderColor}>
      {/* Header */}
      <HStack justify="space-between" mb={4}>
        <VStack align="start" spacing={1}>
          {title && (
            <Text fontSize="lg" fontWeight="bold" color={textColor}>
              {title}
            </Text>
          )}
          <HStack spacing={2}>
            <Badge colorScheme="blue" variant="subtle">
              {xLabels.length} × {yLabels.length} matris
            </Badge>
            <Badge colorScheme="green" variant="subtle">
              {data.length} veri noktası
            </Badge>
          </HStack>
        </VStack>

        <HStack spacing={2}>
          {/* Color Scheme Selector */}
          <Select
            size="sm"
            value={selectedColorScheme}
            onChange={(e) => setSelectedColorScheme(e.target.value as any)}
            w="120px"
          >
            <option value="blue">Mavi</option>
            <option value="green">Yeşil</option>
            <option value="red">Kırmızı</option>
            <option value="purple">Mor</option>
            <option value="orange">Turuncu</option>
          </Select>

          {/* Export Button */}
          <Tooltip label="Grafiği indir">
            <IconButton
              aria-label="İndir"
              icon={<DownloadIcon />}
              size="sm"
              variant="outline"
            />
          </Tooltip>

          {/* Settings */}
          <Menu>
            <MenuButton
              as={IconButton}
              aria-label="Ayarlar"
              icon={<SettingsIcon />}
              size="sm"
              variant="outline"
            />
            <MenuList>
              <MenuItem>Değerleri Göster/Gizle</MenuItem>
              <MenuItem>Tooltip Aç/Kapat</MenuItem>
              <MenuItem>Renk Skalasını Ayarla</MenuItem>
            </MenuList>
          </Menu>
        </HStack>
      </HStack>

      {/* Chart Container */}
      <Box position="relative" overflowX="auto">
        <Box w={`${chartWidth}px`} h={`${chartHeight}px`} position="relative">
          {/* Y-axis labels */}
          <VStack
            position="absolute"
            left="0"
            top="60px"
            spacing={0}
            align="end"
            pr={2}
          >
            {yLabels.map((label) => (
              <Box
                key={label}
                h={`${cellSize}px`}
                display="flex"
                alignItems="center"
              >
                <Text fontSize="sm" color={textColor}>
                  {label}
                </Text>
              </Box>
            ))}
          </VStack>

          {/* X-axis labels */}
          <HStack
            position="absolute"
            left="80px"
            top="20px"
            spacing={0}
            align="start"
          >
            {xLabels.map((label) => (
              <Box
                key={label}
                w={`${cellSize}px`}
                textAlign="center"
              >
                <Text
                  fontSize="sm"
                  color={textColor}
                  transform="rotate(-45deg)"
                  transformOrigin="center"
                  whiteSpace="nowrap"
                >
                  {label}
                </Text>
              </Box>
            ))}
          </HStack>

          {/* Heatmap Grid */}
          <Box position="absolute" left="80px" top="60px">
            {matrix.map((row, yIndex) => (
              <HStack key={yIndex} spacing={0}>
                {row.map((cell, xIndex) => (
                  <MotionBox
                    key={`${xIndex}-${yIndex}`}
                    w={`${cellSize}px`}
                    h={`${cellSize}px`}
                    bg={getColor(cell?.value || null)}
                    border="1px"
                    borderColor={borderColor}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    cursor={cell ? "pointer" : "default"}
                    position="relative"
                    whileHover={{ scale: cell ? 1.05 : 1 }}
                    transition={{ duration: 0.2 }}
                    onMouseEnter={() => cell && setHoveredCell(cell)}
                    onMouseLeave={() => setHoveredCell(null)}
                    onClick={() => cell && onCellClick?.(cell)}
                  >
                    {cell && showValues && (
                      <Text
                        fontSize="xs"
                        fontWeight="bold"
                        color={getTextColor(cell.value)}
                        textAlign="center"
                      >
                        {formatValue(cell.value)}
                      </Text>
                    )}
                  </MotionBox>
                ))}
              </HStack>
            ))}
          </Box>

          {/* Axis Labels */}
          {xAxisLabel && (
            <Text
              position="absolute"
              bottom="10px"
              left="50%"
              transform="translateX(-50%)"
              fontSize="sm"
              fontWeight="semibold"
              color={textColor}
            >
              {xAxisLabel}
            </Text>
          )}

          {yAxisLabel && (
            <Text
              position="absolute"
              left="10px"
              top="50%"
              transform="translateY(-50%) rotate(-90deg)"
              fontSize="sm"
              fontWeight="semibold"
              color={textColor}
            >
              {yAxisLabel}
            </Text>
          )}
        </Box>
      </Box>

      {/* Color Scale Legend */}
      <HStack justify="center" mt={4} spacing={4}>
        <Text fontSize="sm" color={textColor}>
          {formatValue(minValue)}
        </Text>
        <HStack spacing={0}>
          {colorSchemes[selectedColorScheme].map((color, index) => (
            <Box
              key={index}
              w="20px"
              h="10px"
              bg={color}
              border="1px"
              borderColor={borderColor}
            />
          ))}
        </HStack>
        <Text fontSize="sm" color={textColor}>
          {formatValue(maxValue)}
        </Text>
      </HStack>

      {/* Tooltip */}
      {showTooltip && hoveredCell && (
        <MotionBox
          position="fixed"
          bg={bgColor}
          p={3}
          borderRadius="md"
          boxShadow="lg"
          border="1px"
          borderColor={borderColor}
          zIndex={1000}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          style={{
            pointerEvents: 'none',
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)'
          }}
        >
          <VStack spacing={1} align="start">
            <Text fontSize="sm" fontWeight="bold">
              {hoveredCell.label || `${hoveredCell.x} × ${hoveredCell.y}`}
            </Text>
            <Text fontSize="sm">
              Değer: {formatValue(hoveredCell.value)}
            </Text>
            <Text fontSize="xs" color="gray.500">
              X: {hoveredCell.x}, Y: {hoveredCell.y}
            </Text>
          </VStack>
        </MotionBox>
      )}

      {/* Statistics */}
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4} mt={4}>
        <Box textAlign="center">
          <Text fontSize="lg" fontWeight="bold" color="green.500">
            {formatValue(maxValue)}
          </Text>
          <Text fontSize="xs" color="gray.500">Maksimum</Text>
        </Box>
        <Box textAlign="center">
          <Text fontSize="lg" fontWeight="bold" color="red.500">
            {formatValue(minValue)}
          </Text>
          <Text fontSize="xs" color="gray.500">Minimum</Text>
        </Box>
        <Box textAlign="center">
          <Text fontSize="lg" fontWeight="bold" color="blue.500">
            {formatValue((maxValue + minValue) / 2)}
          </Text>
          <Text fontSize="xs" color="gray.500">Ortalama</Text>
        </Box>
        <Box textAlign="center">
          <Text fontSize="lg" fontWeight="bold" color="purple.500">
            {formatValue(maxValue - minValue)}
          </Text>
          <Text fontSize="xs" color="gray.500">Aralık</Text>
        </Box>
      </SimpleGrid>
    </Box>
  );
};

export default HeatmapChart;
