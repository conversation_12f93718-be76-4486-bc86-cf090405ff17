import React from 'react';
import { useEnhancedStatistics } from '../../hooks/useEnhancedStatistics';
import ROIDashboard from '../../components/statistics/dashboards/ROIDashboard';
import { Box, Text } from '@chakra-ui/react';

const ROIAnalysisPage: React.FC = () => {
  const { stats, loading, error } = useEnhancedStatistics();

  if (loading) {
    return (
      <Box p={8} textAlign="center">
        <Text>Yükleniyor...</Text>
      </Box>
    );
  }

  if (error || !stats) {
    return (
      <Box p={8} textAlign="center">
        <Text color="red.500">Veri yüklenirken hata oluştu</Text>
      </Box>
    );
  }

  return <ROIDashboard stats={stats} />;
};

export default ROIAnalysisPage;
