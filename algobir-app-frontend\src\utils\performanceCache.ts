/**
 * Advanced Performance Caching System
 * Multi-level caching with intelligent invalidation and compression
 */

interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  compressed?: boolean;
  size?: number;
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  totalSize: number;
  entryCount: number;
}

interface CacheConfig {
  maxSize: number; // Maximum cache size in bytes
  maxEntries: number; // Maximum number of entries
  defaultTTL: number; // Default TTL in milliseconds
  compressionThreshold: number; // Compress entries larger than this
  enableCompression: boolean;
  enableStats: boolean;
}

class PerformanceCache {
  private cache = new Map<string, CacheEntry>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalSize: 0,
    entryCount: 0
  };
  
  private config: CacheConfig = {
    maxSize: 50 * 1024 * 1024, // 50MB
    maxEntries: 1000,
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    compressionThreshold: 10 * 1024, // 10KB
    enableCompression: true,
    enableStats: true
  };

  constructor(config?: Partial<CacheConfig>) {
    this.config = { ...this.config, ...config };
    
    // Cleanup interval
    setInterval(() => this.cleanup(), 60000); // Every minute
  }

  // Compress data using built-in compression
  private compress(data: string): string {
    if (!this.config.enableCompression) return data;
    
    try {
      // Simple compression using btoa/atob with JSON
      const compressed = btoa(JSON.stringify(data));
      return compressed.length < data.length ? compressed : data;
    } catch {
      return data;
    }
  }

  // Decompress data
  private decompress(data: string, compressed: boolean): string {
    if (!compressed || !this.config.enableCompression) return data;
    
    try {
      return JSON.parse(atob(data));
    } catch {
      return data;
    }
  }

  // Calculate approximate size of data
  private calculateSize(data: any): number {
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch {
      return JSON.stringify(data).length * 2; // Rough estimate
    }
  }

  // LRU eviction strategy
  private evictLRU(): void {
    if (this.cache.size === 0) return;

    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      const entry = this.cache.get(oldestKey);
      if (entry) {
        this.stats.totalSize -= entry.size || 0;
        this.stats.evictions++;
      }
      this.cache.delete(oldestKey);
      this.stats.entryCount--;
    }
  }

  // Set cache entry
  set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const entryTTL = ttl || this.config.defaultTTL;
    const size = this.calculateSize(data);
    
    // Check if we need to evict entries
    while (
      (this.cache.size >= this.config.maxEntries) ||
      (this.stats.totalSize + size > this.config.maxSize)
    ) {
      this.evictLRU();
    }

    // Prepare data for storage
    let serializedData = JSON.stringify(data);
    let compressed = false;

    if (this.config.enableCompression && size > this.config.compressionThreshold) {
      const compressedData = this.compress(serializedData);
      if (compressedData.length < serializedData.length) {
        serializedData = compressedData;
        compressed = true;
      }
    }

    const entry: CacheEntry = {
      data: serializedData,
      timestamp: now,
      ttl: entryTTL,
      accessCount: 0,
      lastAccessed: now,
      compressed,
      size
    };

    // Remove old entry if exists
    const oldEntry = this.cache.get(key);
    if (oldEntry) {
      this.stats.totalSize -= oldEntry.size || 0;
    } else {
      this.stats.entryCount++;
    }

    this.cache.set(key, entry);
    this.stats.totalSize += size;
  }

  // Get cache entry
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      if (this.config.enableStats) this.stats.misses++;
      return null;
    }

    const now = Date.now();
    
    // Check if expired
    if (now - entry.timestamp > entry.ttl) {
      this.delete(key);
      if (this.config.enableStats) this.stats.misses++;
      return null;
    }

    // Update access stats
    entry.accessCount++;
    entry.lastAccessed = now;
    
    if (this.config.enableStats) this.stats.hits++;

    try {
      const decompressedData = this.decompress(entry.data, entry.compressed || false);
      return JSON.parse(decompressedData);
    } catch {
      // If parsing fails, remove corrupted entry
      this.delete(key);
      return null;
    }
  }

  // Check if key exists and is valid
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  // Delete cache entry
  delete(key: string): boolean {
    const entry = this.cache.get(key);
    if (entry) {
      this.stats.totalSize -= entry.size || 0;
      this.stats.entryCount--;
    }
    return this.cache.delete(key);
  }

  // Clear all cache
  clear(): void {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalSize: 0,
      entryCount: 0
    };
  }

  // Cleanup expired entries
  cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.delete(key));
    
    if (keysToDelete.length > 0) {
      console.log(`[PerformanceCache] Cleaned up ${keysToDelete.length} expired entries`);
    }
  }

  // Get cache statistics
  getStats(): CacheStats & { hitRate: number; avgEntrySize: number } {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;
    const avgEntrySize = this.stats.entryCount > 0 ? this.stats.totalSize / this.stats.entryCount : 0;

    return {
      ...this.stats,
      hitRate: Math.round(hitRate * 100) / 100,
      avgEntrySize: Math.round(avgEntrySize)
    };
  }

  // Get cache keys
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  // Get cache size info
  getSizeInfo(): {
    currentSize: number;
    maxSize: number;
    utilization: number;
    entryCount: number;
    maxEntries: number;
  } {
    return {
      currentSize: this.stats.totalSize,
      maxSize: this.config.maxSize,
      utilization: (this.stats.totalSize / this.config.maxSize) * 100,
      entryCount: this.stats.entryCount,
      maxEntries: this.config.maxEntries
    };
  }

  // Invalidate entries by pattern
  invalidatePattern(pattern: string | RegExp): number {
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
    const keysToDelete: string[] = [];

    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.length;
  }

  // Preload data with key
  async preload<T>(key: string, fetcher: () => Promise<T>, ttl?: number): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const data = await fetcher();
    this.set(key, data, ttl);
    return data;
  }
}

// Global cache instances
export const globalCache = new PerformanceCache();

export const userCache = new PerformanceCache({
  maxSize: 10 * 1024 * 1024, // 10MB
  maxEntries: 200,
  defaultTTL: 10 * 60 * 1000 // 10 minutes
});

export const apiCache = new PerformanceCache({
  maxSize: 20 * 1024 * 1024, // 20MB
  maxEntries: 500,
  defaultTTL: 2 * 60 * 1000 // 2 minutes
});

export const staticCache = new PerformanceCache({
  maxSize: 5 * 1024 * 1024, // 5MB
  maxEntries: 100,
  defaultTTL: 60 * 60 * 1000 // 1 hour
});

// Cache key generators
export const CacheKeys = {
  user: (userId: string) => `user:${userId}`,
  userSettings: (userId: string) => `user_settings:${userId}`,
  trades: (userId: string, filters?: string) => `trades:${userId}:${filters || 'all'}`,
  statistics: (userId: string, period: string) => `stats:${userId}:${period}`,
  robots: (userId?: string) => `robots:${userId || 'public'}`,
  robotDetails: (robotId: string) => `robot:${robotId}`,
  marketplaceData: (page: number, filters?: string) => `marketplace:${page}:${filters || 'all'}`,
  notifications: (userId: string) => `notifications:${userId}`,
  dashboard: (userId: string) => `dashboard:${userId}`,
  performance: (userId: string, timeframe: string) => `performance:${userId}:${timeframe}`
};

export { PerformanceCache };
export default globalCache;
