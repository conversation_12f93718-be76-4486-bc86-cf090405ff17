import { mode } from '@chakra-ui/theme-tools';

const Card = {
  baseStyle: (props: any) => ({
    p: { base: '16px', md: '20px' },
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    position: 'relative',
    borderRadius: { base: '16px', md: '20px' },
    minWidth: '0px',
    overflowWrap: 'break-word',
    bg: mode('white', 'navy.800')(props),
    backgroundClip: 'border-box',
    boxShadow: mode('0px 3.5px 5.5px rgba(0, 0, 0, 0.02)', 'none')(props),
    border: '0px solid transparent',
    overflow: 'hidden',
    transition: 'all 0.2s ease'
  }),
  variants: {
    elevated: (props: any) => ({
      boxShadow: mode('0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)', '0 25px 50px -12px rgba(0, 0, 0, 0.25)')(props),
    }),
    outline: (props: any) => ({
      border: '1px solid',
      borderColor: mode('gray.200', 'navy.600')(props),
      boxShadow: 'none',
    }),
    glass: (props: any) => ({
      bg: mode('rgba(255, 255, 255, 0.8)', 'rgba(17, 28, 68, 0.8)')(props),
      backdropFilter: 'blur(20px)',
      border: '1px solid',
      borderColor: mode('rgba(255, 255, 255, 0.2)', 'rgba(255, 255, 255, 0.1)')(props),
    }),
  },
  sizes: {
    sm: {
      p: { base: '12px', md: '16px' },
      borderRadius: { base: '12px', md: '16px' },
    },
    md: {
      p: { base: '16px', md: '20px' },
      borderRadius: { base: '16px', md: '20px' },
    },
    lg: {
      p: { base: '20px', md: '24px' },
      borderRadius: { base: '20px', md: '24px' },
    },
  },
  defaultProps: {
    variant: 'elevated',
    size: 'md',
  },
};

export const CardComponent = {
  components: {
    Card
  }
}; 