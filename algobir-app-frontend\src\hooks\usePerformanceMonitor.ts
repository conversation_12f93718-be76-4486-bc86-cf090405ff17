/**
 * Advanced Performance Monitoring Hook
 * Real-time performance tracking with Web Vitals and custom metrics
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'navigation' | 'resource' | 'measure' | 'custom';
  metadata?: Record<string, any>;
}

interface WebVitals {
  FCP?: number; // First Contentful Paint
  LCP?: number; // Largest Contentful Paint
  FID?: number; // First Input Delay
  CLS?: number; // Cumulative Layout Shift
  TTFB?: number; // Time to First Byte
}

interface PerformanceStats {
  pageLoadTime: number;
  domContentLoaded: number;
  resourceLoadTime: number;
  memoryUsage?: number;
  connectionType?: string;
  webVitals: WebVitals;
  customMetrics: PerformanceMetric[];
}

interface PerformanceConfig {
  enableWebVitals: boolean;
  enableResourceTracking: boolean;
  enableMemoryTracking: boolean;
  enableCustomMetrics: boolean;
  sampleRate: number; // 0-1, percentage of sessions to track
  reportingEndpoint?: string;
  bufferSize: number;
}

const defaultConfig: PerformanceConfig = {
  enableWebVitals: true,
  enableResourceTracking: true,
  enableMemoryTracking: true,
  enableCustomMetrics: true,
  sampleRate: 1.0,
  bufferSize: 100
};

export const usePerformanceMonitor = (config: Partial<PerformanceConfig> = {}) => {
  const finalConfig = { ...defaultConfig, ...config };
  const location = useLocation();
  
  const [stats, setStats] = useState<PerformanceStats>({
    pageLoadTime: 0,
    domContentLoaded: 0,
    resourceLoadTime: 0,
    webVitals: {},
    customMetrics: []
  });
  
  const metricsBuffer = useRef<PerformanceMetric[]>([]);
  const observerRef = useRef<PerformanceObserver | null>(null);
  const navigationStartTime = useRef<number>(Date.now());
  const isTracking = useRef<boolean>(Math.random() < finalConfig.sampleRate);

  // Web Vitals measurement
  const measureWebVitals = useCallback(() => {
    if (!finalConfig.enableWebVitals || !isTracking.current) return;

    try {
      // First Contentful Paint
      const fcpEntries = performance.getEntriesByName('first-contentful-paint');
      if (fcpEntries.length > 0) {
        const fcp = fcpEntries[0] as PerformanceEntry;
        setStats(prev => ({
          ...prev,
          webVitals: { ...prev.webVitals, FCP: fcp.startTime }
        }));
      }

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        const lcpObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          setStats(prev => ({
            ...prev,
            webVitals: { ...prev.webVitals, LCP: lastEntry.startTime }
          }));
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      }

      // First Input Delay
      if ('PerformanceObserver' in window) {
        const fidObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          entries.forEach((entry: any) => {
            setStats(prev => ({
              ...prev,
              webVitals: { ...prev.webVitals, FID: entry.processingStart - entry.startTime }
            }));
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
      }

      // Cumulative Layout Shift
      if ('PerformanceObserver' in window) {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
              setStats(prev => ({
                ...prev,
                webVitals: { ...prev.webVitals, CLS: clsValue }
              }));
            }
          });
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      }

    } catch (error) {
      console.warn('[PerformanceMonitor] Web Vitals measurement failed:', error);
    }
  }, [finalConfig.enableWebVitals]);

  // Navigation timing measurement
  const measureNavigationTiming = useCallback(() => {
    if (!isTracking.current) return;

    try {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        const pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
        const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;
        const ttfb = navigation.responseStart - navigation.fetchStart;
        
        setStats(prev => ({
          ...prev,
          pageLoadTime,
          domContentLoaded,
          webVitals: { ...prev.webVitals, TTFB: ttfb }
        }));

        // Record custom metric
        recordMetric('page_load_time', pageLoadTime, 'navigation', {
          url: window.location.pathname,
          referrer: document.referrer
        });
      }
    } catch (error) {
      console.warn('[PerformanceMonitor] Navigation timing measurement failed:', error);
    }
  }, []);

  // Resource timing measurement
  const measureResourceTiming = useCallback(() => {
    if (!finalConfig.enableResourceTracking || !isTracking.current) return;

    try {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      let totalResourceTime = 0;
      
      resources.forEach(resource => {
        const loadTime = resource.responseEnd - resource.startTime;
        totalResourceTime += loadTime;
        
        // Track slow resources
        if (loadTime > 1000) { // > 1 second
          recordMetric('slow_resource', loadTime, 'resource', {
            name: resource.name,
            type: resource.initiatorType,
            size: resource.transferSize
          });
        }
      });

      setStats(prev => ({
        ...prev,
        resourceLoadTime: totalResourceTime
      }));
    } catch (error) {
      console.warn('[PerformanceMonitor] Resource timing measurement failed:', error);
    }
  }, [finalConfig.enableResourceTracking]);

  // Memory usage measurement
  const measureMemoryUsage = useCallback(() => {
    if (!finalConfig.enableMemoryTracking || !isTracking.current) return;

    try {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = memory.usedJSHeapSize / (1024 * 1024); // MB
        
        setStats(prev => ({
          ...prev,
          memoryUsage
        }));

        // Alert on high memory usage
        if (memoryUsage > 100) { // > 100MB
          recordMetric('high_memory_usage', memoryUsage, 'custom', {
            totalHeapSize: memory.totalJSHeapSize / (1024 * 1024),
            heapSizeLimit: memory.jsHeapSizeLimit / (1024 * 1024)
          });
        }
      }
    } catch (error) {
      console.warn('[PerformanceMonitor] Memory measurement failed:', error);
    }
  }, [finalConfig.enableMemoryTracking]);

  // Connection information
  const measureConnectionInfo = useCallback(() => {
    if (!isTracking.current) return;

    try {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        setStats(prev => ({
          ...prev,
          connectionType: connection.effectiveType
        }));
      }
    } catch (error) {
      console.warn('[PerformanceMonitor] Connection info measurement failed:', error);
    }
  }, []);

  // Record custom metric
  const recordMetric = useCallback((
    name: string,
    value: number,
    type: PerformanceMetric['type'] = 'custom',
    metadata?: Record<string, any>
  ) => {
    if (!finalConfig.enableCustomMetrics || !isTracking.current) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type,
      metadata
    };

    metricsBuffer.current.push(metric);
    
    // Keep buffer size manageable
    if (metricsBuffer.current.length > finalConfig.bufferSize) {
      metricsBuffer.current = metricsBuffer.current.slice(-finalConfig.bufferSize);
    }

    setStats(prev => ({
      ...prev,
      customMetrics: [...metricsBuffer.current]
    }));

    // Report critical metrics immediately
    if (value > 3000 && type === 'navigation') { // > 3 seconds
      reportMetric(metric);
    }
  }, [finalConfig.enableCustomMetrics, finalConfig.bufferSize]);

  // Report metric to endpoint
  const reportMetric = useCallback(async (metric: PerformanceMetric) => {
    if (!finalConfig.reportingEndpoint) return;

    try {
      await fetch(finalConfig.reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...metric,
          url: window.location.pathname,
          userAgent: navigator.userAgent,
          sessionId: sessionStorage.getItem('session_id') || 'unknown'
        })
      });
    } catch (error) {
      console.warn('[PerformanceMonitor] Failed to report metric:', error);
    }
  }, [finalConfig.reportingEndpoint]);

  // Measure component render time
  const measureRenderTime = useCallback((componentName: string) => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      recordMetric(`render_${componentName}`, renderTime, 'custom', {
        component: componentName
      });
    };
  }, [recordMetric]);

  // Start performance monitoring
  useEffect(() => {
    if (!isTracking.current) return;

    navigationStartTime.current = Date.now();

    // Initial measurements
    setTimeout(() => {
      measureNavigationTiming();
      measureResourceTiming();
      measureMemoryUsage();
      measureConnectionInfo();
      measureWebVitals();
    }, 100);

    // Periodic measurements
    const interval = setInterval(() => {
      measureMemoryUsage();
    }, 30000); // Every 30 seconds

    return () => {
      clearInterval(interval);
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [
    measureNavigationTiming,
    measureResourceTiming,
    measureMemoryUsage,
    measureConnectionInfo,
    measureWebVitals
  ]);

  // Track route changes
  useEffect(() => {
    if (!isTracking.current) return;

    const routeChangeTime = Date.now() - navigationStartTime.current;
    recordMetric('route_change', routeChangeTime, 'navigation', {
      from: document.referrer,
      to: location.pathname
    });
    
    navigationStartTime.current = Date.now();
  }, [location.pathname, recordMetric]);

  // Get performance score (0-100)
  const getPerformanceScore = useCallback((): number => {
    const { webVitals, pageLoadTime } = stats;
    let score = 100;

    // Deduct points for poor metrics
    if (webVitals.FCP && webVitals.FCP > 2500) score -= 20;
    if (webVitals.LCP && webVitals.LCP > 4000) score -= 25;
    if (webVitals.FID && webVitals.FID > 300) score -= 20;
    if (webVitals.CLS && webVitals.CLS > 0.25) score -= 15;
    if (pageLoadTime > 3000) score -= 20;

    return Math.max(0, score);
  }, [stats]);

  return {
    stats,
    recordMetric,
    measureRenderTime,
    getPerformanceScore,
    isTracking: isTracking.current
  };
};

export default usePerformanceMonitor;
