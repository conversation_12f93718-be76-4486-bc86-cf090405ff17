import React from 'react';
import {
  VStack,
  Text,
  SimpleGrid,
  Card,
  CardBody,
  HStack,
  Box
} from '@chakra-ui/react';
import { useEnhancedStatistics } from '../../hooks/useEnhancedStatistics';
import AdvancedLineChart from '../../components/statistics/charts/AdvancedLineChart';
import InteractivePieChart from '../../components/statistics/charts/InteractivePieChart';

const PerformancePage: React.FC = () => {
  const { stats, loading, error } = useEnhancedStatistics();

  if (loading) {
    return (
      <Box p={8} textAlign="center">
        <Text>Yükleniyor...</Text>
      </Box>
    );
  }

  if (error || !stats) {
    return (
      <Box p={8} textAlign="center">
        <Text color="red.500">Veri yüklenirken hata oluştu</Text>
      </Box>
    );
  }

  const formatCurrency = (value: number) => `₺${value.toFixed(2)}`;
  const formatPercentage = (value: number) => `${value.toFixed(2)}%`;

  // Prepare profit/loss analysis data
  const profitLossData = [
    { name: '<PERSON><PERSON>', value: stats.winningTrades, color: '#48BB78' },
    { name: 'Kaybeden İşlemler', value: stats.losingTrades, color: '#F56565' }
  ];

  // Prepare win rate analytics data
  const winRateData = [
    { name: 'Kazanma Oranı', value: stats.winRate, color: '#4299E1' },
    { name: 'Kayıp Oranı', value: 100 - stats.winRate, color: '#ED8936' }
  ];

  // Prepare trading patterns data (monthly performance)
  const tradingPatternsData = stats.roiAnalysis?.monthlyROI?.map((item: any) => ({
    date: item.month,
    value: item.roi,
    trades: item.investment / 1000, // Simplified trade count estimation
    returns: item.returns
  })) || [];

  // Performance metrics cards data
  const performanceMetrics = [
    {
      title: 'Toplam Kar/Zarar',
      value: formatCurrency(stats.totalPnl),
      change: stats.totalPnl > 0 ? '+' : '',
      color: stats.totalPnl > 0 ? 'green' : 'red',
      icon: '💰'
    },
    {
      title: 'Kazanma Oranı',
      value: formatPercentage(stats.winRate),
      change: stats.winRate > 50 ? 'İyi' : 'Geliştirilmeli',
      color: stats.winRate > 50 ? 'green' : 'orange',
      icon: '🎯'
    },
    {
      title: 'Profit Factor',
      value: stats.profitFactor?.toFixed(2) || '0.00',
      change: stats.profitFactor > 1 ? 'Karlı' : 'Zararlı',
      color: stats.profitFactor > 1 ? 'green' : 'red',
      icon: '📊'
    },
    {
      title: 'Ortalama İşlem',
      value: formatCurrency(stats.avgTradePnl || 0),
      change: stats.avgTradePnl > 0 ? 'Pozitif' : 'Negatif',
      color: stats.avgTradePnl > 0 ? 'green' : 'red',
      icon: '📈'
    }
  ];

  return (
    <VStack spacing={6} align="stretch">
      <Text fontSize="2xl" fontWeight="bold">Performans Analizi</Text>

      {/* Performance Metrics Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6}>
        {performanceMetrics.map((metric, index) => (
          <Card key={index} p={4} borderRadius="16px" boxShadow="md">
            <CardBody>
              <VStack align="start" spacing={2}>
                <HStack>
                  <Text fontSize="2xl">{metric.icon}</Text>
                  <Text fontSize="sm" color="gray.500" fontWeight="medium">
                    {metric.title}
                  </Text>
                </HStack>
                <Text fontSize="2xl" fontWeight="bold" color={`${metric.color}.500`}>
                  {metric.value}
                </Text>
                <Text fontSize="xs" color={`${metric.color}.400`}>
                  {metric.change}
                </Text>
              </VStack>
            </CardBody>
          </Card>
        ))}
      </SimpleGrid>

      {/* Charts Grid */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* Profit/Loss Analysis */}
        <InteractivePieChart
          data={profitLossData}
          title="Kar/Zarar Dağılımı"
          height={350}
          formatValue={(value) => `${value} işlem`}
          showPercentages={true}
        />

        {/* Win Rate Analytics */}
        <InteractivePieChart
          data={winRateData}
          title="Kazanma/Kayıp Oranı"
          height={350}
          formatValue={(value) => `${value.toFixed(1)}%`}
          showPercentages={true}
          viewMode="donut"
        />
      </SimpleGrid>

      {/* Trading Patterns Analysis */}
      <Card p={6} borderRadius="16px" boxShadow="md">
        <VStack align="stretch" spacing={4}>
          <Text fontSize="lg" fontWeight="bold">Aylık Performans Trendi</Text>
          <AdvancedLineChart
            data={tradingPatternsData}
            title="Aylık ROI Performansı"
            dataKey="value"
            formatValue={formatPercentage}
            height={400}
            showBrush={true}
            showMovingAverage={true}
            color="#4299E1"
          />
        </VStack>
      </Card>
    </VStack>
  );
};

export default PerformancePage;
