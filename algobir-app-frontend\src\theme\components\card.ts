import { mode } from '@chakra-ui/theme-tools';

const Card = {
  baseStyle: (props: any) => ({
    p: '20px',
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    position: 'relative',
    borderRadius: '20px',
    minWidth: '0px',
    overflowWrap: 'break-word',
    bg: mode('#ffffff', 'navy.800')(props),
    backgroundClip: 'border-box',
    boxShadow: mode(
      '14px 17px 40px 4px rgba(112, 144, 176, 0.18)',
      'unset'
    )(props),
    border: mode('1px solid', '1px solid')(props),
    borderColor: mode('transparent', 'rgba(255, 255, 255, 0.15)')(props),
  }),
};

export const CardComponent = {
  components: {
    Card,
  },
}; 