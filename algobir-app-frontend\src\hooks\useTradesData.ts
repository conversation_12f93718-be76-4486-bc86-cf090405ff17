import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabaseClient';
import { Trade, SignalSourceType } from '../types/trade';
import { useAuth } from '../context/AuthContext';
import { useToast } from '@chakra-ui/react';

// Filtreleme seçenekleri için arayüz
export interface TradeFilters {
  is_closed?: boolean;
  position_status?: string;
  trade_category?: string;
  symbol?: string;
  order_side?: string; // BUY/SELL filtreleme için
  signal_source_type?: SignalSourceType; // Solo veya Bro robot filtreleme için
  is_deleted?: boolean; // Silinmiş işlemleri dahil etmek/etmemek için
  // Diğer potansiyel filtre alanları buraya eklenebilir
}

interface UseTradesDataReturn {
  trades: Trade[];
  isLoading: boolean;
  error: any | null;
  refetch: () => Promise<void>;
  handleDeleteTrade: (tradeId: number) => Promise<void>;
  handleCancelSignalBatch: (trade: Trade) => Promise<void>;
  // Sayfalama için yeni değerler
  currentPage: number;
  setCurrentPage: (page: number) => void;
  pageSize: number;
  setPageSize: (size: number) => void;
  totalPages: number;
  totalTrades: number;
  isDeletingTradeId: number | null;
  isCancellingSignalId: number | string | null;
}

// İsteğe bağlı filtreler argümanını kabul et
export function useTradesData(filters?: TradeFilters): UseTradesDataReturn {
  const { user } = useAuth();
  const [trades, setTrades] = useState<Trade[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<any | null>(null);
  const [isDeletingTradeId, setIsDeletingTradeId] = useState<number | null>(null);
  const [isCancellingSignalId, setIsCancellingSignalId] = useState<number | string | null>(null);
  const toast = useToast();

  // Sayfalama için state'ler
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(15); // Varsayılan sayfa başına 15 öğe
  const [totalTrades, setTotalTrades] = useState<number>(0);
  const totalPages = Math.max(1, Math.ceil(totalTrades / pageSize));

  // Kararlı bir bağımlılık referansı için filtreleri string'e dönüştür
  const filtersString = JSON.stringify(filters);

  // İşlem silme fonksiyonu
  const handleDeleteTrade = async (tradeId: number) => {
    if (!user) {
      toast({
        title: 'Hata',
        description: 'Kullanıcı oturumu bulunamadı.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    setIsDeletingTradeId(tradeId);
    console.log(`[DELETE_TRADE] İşlem silme başlatılıyor, ID: ${tradeId}, Tip: ${typeof tradeId}`);
    
    try {
      // Soft delete RPC'sini çağır
      console.log(`[DELETE_TRADE] 'soft_delete_trade' RPC'si çağrılıyor, trade_id_to_delete: ${tradeId} (tip: ${typeof tradeId})`);
      const { error: rpcError } = await supabase
        .rpc('soft_delete_trade', {
          trade_id_to_delete: tradeId // tradeId'nin number (BIGINT) olduğundan emin olun
        });
      
      if (rpcError) {
        console.error('[DELETE_TRADE] RPC hatası:', rpcError);
        toast({
          title: 'İşlem Silinemedi',
          description: `RPC Hatası: ${rpcError.message}. Veritabanında UUID parametreli 'soft_delete_trade' fonksiyonunu kaldırdığınızdan emin olun.`,
          status: 'error',
          duration: 7000, // Bu özel hata için daha uzun süre
          isClosable: true,
        });
        throw rpcError;
      }
      
      toast({
        title: 'İşlem Silindi',
        description: 'İşlem başarıyla silindi.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
      // Daha gelişmiş optimistik güncelleme:
      // Solo-robot işlemleri için: Listeden tamamen kaldır
      // Bro-robot işlemleri için: is_deleted = true olarak işaretle
      setTrades(prevTrades =>
        prevTrades.map(t => {
          if (t.id === tradeId) {
            const isSoloRobotTrade = !t.robot_id;
            if (isSoloRobotTrade) return null; // Solo işlemse kaldırılacak olarak işaretle
            return { ...t, is_deleted: true }; // Bro işlemse silindi olarak işaretle
          }
          return t;
        }).filter(t => t !== null) as Trade[] // null değerleri (silinen solo işlemleri) kaldır
      );
      
      // Arka planda da en güncel verileri çekelim
      await fetchTrades();
    } catch (err: any) {
      console.error('[DELETE_TRADE] İşlem silme genel hatası:', JSON.stringify(err, null, 2));
      toast({
        title: 'İşlem Silinemedi',
        description: err.message || 'Bilinmeyen bir hata oluştu.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsDeletingTradeId(null);
    }
  };

  const fetchTrades = useCallback(async () => {
    // Mevcut filtreleri JSON string'den parse et
    const currentFilters = filtersString ? JSON.parse(filtersString) as TradeFilters : undefined;

    if (!user) {
      setIsLoading(false);
      setTrades([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Sayfalama için range hesapla
      const from = (currentPage - 1) * pageSize;
      const to = from + pageSize - 1;

      console.log(`[FETCH_TRADES] Başlatılıyor, sayfa: ${currentPage}, aralık: ${from}-${to}, filtreler:`, currentFilters);

      // Eğer filtreler mevcutsa, RPC yerine doğrudan sorgu kullan
      if (currentFilters && Object.keys(currentFilters).length > 0) {
        // HTTP 400 hatası çözümü: İlişkiyi açıkça belirten join ifadesi kullanıyoruz
        let query = supabase
          .from('trades')
          .select(`
            id, 
            user_id, 
            symbol, 
            signal_type, 
            position_id, 
            price, 
            calculated_quantity, 
            received_at, 
            pnl, 
            pnl_percentage, 
            exit_price, 
            exit_date, 
            is_closed, 
            is_deleted, 
            original_signal_id,
            created_at, 
            updated_at, 
            status, 
            system_name, 
            trade_category, 
            position_status, 
            signal_name, 
            category, 
            forwarded, 
            order_side, 
            name, 
            closing_trade_id, 
            robot_id, 
            webhook_id, 
            forwarding_status, 
            forwarded_at, 
            robot_seller_id
          `, { count: 'exact' }) // Tüm gerekli alanları açıkça belirtiyoruz
          .eq('user_id', user.id);

        // Filtreleri koşullu olarak uygula (is_deleted hariç)
        Object.entries(currentFilters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            // Özel işlem gerektiren filtreler
            if (key === 'signal_source_type') {
              // Solo-robot ve bro-robot filtresi
              if (value === SignalSourceType.SOLO_ROBOT) {
                // Solo-robot işlemleri için webhook_id IS NOT NULL
                query = query.not('webhook_id', 'is', null);
              } else if (value === SignalSourceType.BRO_ROBOT) {
                // Bro-robot işlemleri için robot_id IS NOT NULL
                query = query.not('robot_id', 'is', null);
              }
            } else if (key === 'symbol' && typeof value === 'string') {
              // Sembol için kısmi eşleşme (ilike)
              query = query.ilike('symbol', `%${value}%`);
            } else if (key === 'trade_category' && typeof value === 'string') {
              // Trade category için büyük/küçük harf duyarsız eşleşme
              // Dropdown'dan gelen "Alım" ve "Satım" değerlerini destekle
              query = query.ilike('trade_category', value);
            } else if (key === 'order_side' && typeof value === 'string') {
              // Order side için büyük/küçük harf duyarsız eşleşme
              // Hem BUY/SELL hem de buy/sell değerlerini destekle
              query = query.ilike('order_side', value);
            } else {
              // Standart filtreler
              query = query.eq(key as keyof Trade, value);
            }
          }
        });

        // KRİTİK FİLTRE: SELL işlemleri için açık pozisyon kontrolü
        // SELL işlemleri sadece PnL hesaplanmış olanları göster (gerçekten kapatılmış pozisyonlar)
        // Bu filtre her durumda uygulanır, sadece SELL filtresi aktifken değil
        // Böylece açık pozisyonu olmayan SELL sinyalleri hiç gözükmez
        
        // Temel filtreleme: ya BUY işlemi ya da PnL hesaplanmış SELL işlemi
        query = query.or('order_side.eq.BUY,and(order_side.eq.SELL,pnl.not.is.null)');

        // is_deleted filtresi için son mantık
        let finalQuery = query;
        
        if (currentFilters && typeof currentFilters.is_deleted === 'boolean') {
          // Eğer is_deleted filtresi açıkça belirtilmişse, onu uygula
          // (genellikle admin görünümleri veya özel filtreler için)
          finalQuery = finalQuery.eq('is_deleted', currentFilters.is_deleted);
        } else {
          // Varsayılan davranış (/trades sayfası için):
          // Hem solo-robot hem de bro-robot için silinmemiş olanları göster
          // is_deleted NULL veya FALSE olanları getir
          finalQuery = finalQuery.or('is_deleted.is.false,is_deleted.is.null');
        }

        // Sıralama ekle ve sayfalama uygula
        finalQuery = finalQuery
          .order('received_at', { ascending: false })
          .range(from, to);

        console.log(`[FETCH_TRADES] Sorgu oluşturuldu: ${JSON.stringify(finalQuery)}`);
        
        const { data, error: fetchError, count } = await finalQuery;
        
        if (fetchError) {
          console.error('[FETCH_TRADES] Veri çekme hatası:', fetchError);
          throw fetchError;
        }

        // Toplam işlem sayısını ayarla (sayfalama için)
        if (count !== null && count !== undefined) {
          setTotalTrades(count);
        }

        // Verinin tipini kontrol etmek için logla
        if (data && data.length > 0) {
          console.log('[FETCH_TRADES] Örnek veri yapısı:', {
            id: data[0].id,
            id_type: typeof data[0].id,
            id_constructor: data[0].id && data[0].id.constructor ? data[0].id.constructor.name : null,
            id_value: JSON.stringify(data[0].id),
            id_keys: data[0].id && typeof data[0].id === 'object' ? Object.keys(data[0].id) : null,
            has_robot_id: !!data[0].robot_id,
            trade: { ...data[0] }
          });
          
          // İlk 3 kaydın ID değerlerini ayrıntılı kontrol et
          for (let i = 0; i < Math.min(3, data.length); i++) {
            console.log(`[FETCH_TRADES] Trade[${i}] ID detayları:`, {
              id: data[i].id,
              id_type: typeof data[i].id,
              id_is_object: typeof data[i].id === 'object' && data[i].id !== null,
              id_has_uuid: data[i].id && typeof data[i].id === 'object' && 'uuid' in data[i].id,
              id_stringified: JSON.stringify(data[i].id),
              symbol: data[i].symbol
            });
          }
        }

        if (data) {
          // robot_id'leri olan kayıtları bulalım
          const tradesWithRobotIds = data.filter(t => t.robot_id);
          const robotIds = [...new Set(tradesWithRobotIds.map(t => t.robot_id))];
          
          // Eğer robot_id'li kayıtlar varsa, satıcı bilgilerini ayrı bir sorgu ile çekelim
          let robotSellerMap: Record<string, string> = {};
          let robotDataMap: Record<string, any> = {};
          
          if (robotIds.length > 0) {
            console.log(`[FETCH_TRADES] ${robotIds.length} robot için bilgi çekiliyor`);
            
            const { data: robotData, error: robotError } = await supabase
              .from('robots')
              .select('id, seller_id, name')
              .in('id', robotIds);
              
            if (robotError) {
              console.error('[FETCH_TRADES] Robot bilgileri alınamadı:', robotError);
            } else if (robotData) {
              // robot id -> seller id map oluştur
              robotSellerMap = robotData.reduce((map, robot) => {
                map[robot.id] = robot.seller_id;
                return map;
              }, {} as Record<string, string>);
              
              // robot id -> robot data map oluştur
              robotDataMap = robotData.reduce((map, robot) => {
                map[robot.id] = robot;
                return map;
              }, {} as Record<string, any>);
              
              console.log(`[FETCH_TRADES] ${Object.keys(robotSellerMap).length} robot bilgisi alındı`);
            }
          }
          
          const processedTrades = data.map((trade: any) => {
            // PNL, fiyat ve miktar değerlerini sayısal değerlere güvenli şekilde dönüştürme
            let pnlValue: number | null = null;
            let priceValue: number | null = null;
            let calculatedQuantityValue: number | null = null;
            let exitPriceValue: number | null = null;
            
            // PNL değeri işleme
            if (trade.pnl !== undefined && trade.pnl !== null) {
              try {
                pnlValue = typeof trade.pnl === 'string' ? parseFloat(trade.pnl) : trade.pnl;
                if (pnlValue === null || Number.isNaN(pnlValue)) pnlValue = null;
              } catch (e) {
                console.error('PNL parse error:', e);
                pnlValue = null;
              }
            }
            
            // Fiyat değeri işleme
            if (trade.price !== undefined && trade.price !== null) {
              try {
                priceValue = typeof trade.price === 'string' ? parseFloat(trade.price) : trade.price;
                if (priceValue === null || Number.isNaN(priceValue)) priceValue = null;
              } catch (e) {
                console.error('Price parse error:', e);
                priceValue = null;
              }
            }
            
            // Miktar değeri işleme
            if (trade.calculated_quantity !== undefined && trade.calculated_quantity !== null) {
              try {
                calculatedQuantityValue = typeof trade.calculated_quantity === 'string' ? 
                  parseFloat(trade.calculated_quantity) : trade.calculated_quantity;
                if (calculatedQuantityValue === null || Number.isNaN(calculatedQuantityValue)) calculatedQuantityValue = null;
              } catch (e) {
                console.error('Quantity parse error:', e);
                calculatedQuantityValue = null;
              }
            }
            
            // Çıkış fiyatı değeri işleme
            if (trade.exit_price !== undefined && trade.exit_price !== null) {
              try {
                exitPriceValue = typeof trade.exit_price === 'string' ? parseFloat(trade.exit_price) : trade.exit_price;
                if (exitPriceValue === null || Number.isNaN(exitPriceValue)) exitPriceValue = null;
              } catch (e) {
                console.error('Exit price parse error:', e);
                exitPriceValue = null;
              }
            }
            
            // Signal kaynağını belirle (Solo-Robot or Bro-Robot)
            const signalSourceType = trade.robot_id 
              ? SignalSourceType.BRO_ROBOT 
              : trade.webhook_id 
                ? SignalSourceType.SOLO_ROBOT 
                : undefined;
            
            // Signal tipi belirleme
            let signalType = trade.signal_type;
            if (!signalType) {
              const upperName = trade.name ? trade.name.toUpperCase() : '';
              
              if (upperName.includes('BUY')) {
                signalType = 'BUY';
              } else if (upperName.includes('ATRTP')) {
                signalType = 'ATRTP';
              } else if (upperName.includes('KTP')) {
                signalType = 'KTP';
              } else if (upperName.includes('STOP')) {
                signalType = 'STOP';
              } else if (upperName.includes('SELL')) {
                signalType = 'SELL';
              } else {
                signalType = trade.order_side === 'buy' ? 'BUY' : trade.order_side === 'sell' ? 'SELL' : null;
              }
            }
            
            // Kategori belirleme - backend'den gelen kategori varsa kullan, yoksa hesapla
            let tradeCategory = trade.trade_category;
            if (!tradeCategory) {
              if (signalType === 'BUY' || trade.order_side === 'BUY') {
                tradeCategory = 'Alım';
              } else if (['SELL', 'TP_SELL', 'SL_SELL', 'ATRTP', 'KTP', 'STOP'].includes(signalType) || trade.order_side === 'SELL') {
                tradeCategory = 'Satım';
              } else {
                tradeCategory = 'Bilinmiyor';
              }
            }
            
            // Pozisyon durumu - backend'den gelen position_status değerini kullan, yoksa hesapla
            let positionStatus = trade.position_status;
            if (!positionStatus) {
              if (trade.is_closed === true || trade.trade_category === 'Satım') {
                positionStatus = 'Kapalı';
              } else if (trade.is_closed === false || trade.trade_category === 'Alım') {
                positionStatus = 'Açık';
              } else {
                positionStatus = 'Bilinmiyor';
              }
            }
            
            // Sistem adı - backend'den gelen sistem adı varsa kullan, yoksa signal kaynağına göre belirle
            let systemName = trade.system_name;
            
            // Robot ya da webhook id varlığına göre işlem kaynağını belirle 
            const isRobotSignal = !!trade.robot_id;
            const isWebhookSignal = !!trade.webhook_id;
            
            if (!systemName || systemName === 'SellerSignalRelay' || systemName === 'Robot Sinyali') {
              // DB'den gelen değer yoksa veya eski isimlerse, daha açıklayıcı bir isim bulmaya çalış
              if (isRobotSignal) {
                // Bro-Robot sinyali - robot_id var
                // Robot adı bilgisi varsa kullan
                const robotInfo = trade.robot_id && robotDataMap[trade.robot_id];
                if (robotInfo && robotInfo.name) {
                  systemName = `${robotInfo.name} (Bro-Robot)`;
                } else {
                  // Robot adı bilgisi yoksa, name alanından çıkarmaya çalış
                  const nameParts = trade.name ? trade.name.split(' ') : [];
                  if (nameParts.length > 0 && !['BUY', 'SELL', 'TP', 'SL'].includes(nameParts[0].toUpperCase())) {
                    // İlk kelime muhtemelen robot adı
                    systemName = `${nameParts[0]} (Bro-Robot)`;
                  } else {
                    // name'den çıkarılamadıysa robot ID'sini kullan
                    systemName = `Bro-Robot #${trade.robot_id.substring(0, 6)}`;
                  }
                }
              } else if (isWebhookSignal) {
                // Solo-Robot sinyali - webhook_id var
                systemName = 'Solo-Robot';
              } else {
                // Diğer durumlar için
                systemName = 'Bilinmiyor';
              }
            } else {
              // Backend'den gelen sistem adını kaynak belirtici ile zenginleştir
              if (isRobotSignal && !systemName.includes('Bro-Robot')) {
                systemName = `${systemName} (Bro-Robot)`;
              } else if (isWebhookSignal && !systemName.includes('Solo-Robot')) {
                systemName = `${systemName} (Solo-Robot)`;
              }
            }
            
            // Robot satıcı ID'sini belirle (ayrı sorguda çekilen map'ten)
            const robotSellerId = trade.robot_id ? robotSellerMap[trade.robot_id] || null : null;
            
            // Robot nesnesini oluştur (eğer robot_id varsa ve robotDataMap'te bu ID'ye karşılık gelen veri varsa)
            let robotObject = undefined;
            if (trade.robot_id && robotDataMap[trade.robot_id]) {
              robotObject = {
                id: trade.robot_id,
                name: robotDataMap[trade.robot_id].name || 'İsimsiz Robot',
                seller_id: robotDataMap[trade.robot_id].seller_id
              };
            }
            
            return {
              ...trade,
              id: typeof trade.id === 'number' ? trade.id : Number(trade.id),
              pnl: pnlValue,
              price: priceValue,
              calculated_quantity: calculatedQuantityValue,
              exit_price: exitPriceValue,
              signal_type: signalType,
              trade_category: tradeCategory,
              position_status: positionStatus,
              system_name: systemName,
              signal_source_type: signalSourceType,
              robot_seller_id: robotSellerId,
              robot: robotObject
            };
          });
          
          setTrades(processedTrades || []);
        }
      } else {
        // Filtre yoksa, tüm işlemleri getir (sayfalanmış)
        // HTTP 400 hatası çözümü: robots join'i yerine ayrı sorgu kullanalım
        let query = supabase
          .from('trades')
          .select(`
            id, 
            user_id, 
            symbol, 
            signal_type, 
            position_id, 
            price, 
            calculated_quantity, 
            received_at, 
            pnl, 
            pnl_percentage, 
            exit_price, 
            exit_date, 
            is_closed, 
            is_deleted, 
            created_at, 
            updated_at, 
            status, 
            system_name, 
            trade_category, 
            position_status, 
            signal_name, 
            category, 
            forwarded, 
            order_side, 
            name, 
            closing_trade_id, 
            robot_id, 
            webhook_id, 
            forwarding_status, 
            forwarded_at, 
            robot_seller_id
          `, { count: 'exact' }) // Tüm gerekli alanları açıkça belirtiyoruz
          .eq('user_id', user.id);
          
        // Varsayılan davranış (/trades sayfası için):
        // Solo-robot: Sadece silinmemişleri göster
        // Bro-robot: Tümünü göster (silinmiş veya silinmemiş), stil üstü çizili gösterecek
        let finalQuery = query.or(
          'and(robot_id.is.null,is_deleted.is.false),robot_id.not.is.null'
          // Şunları getirir:
          // 1. Solo işlemler (robot_id is null) VE silinmemiş olanlar
          // 2. TÜM Bro işlemleri (robot_id is not null), is_deleted durumuna bakılmaksızın
        );

        finalQuery = finalQuery
          .order('received_at', { ascending: false })
          .range(from, to);

        console.log(`[FETCH_TRADES] Filtresiz sorgu oluşturuldu: ${JSON.stringify(finalQuery)}`);

        const { data: tradesData, error: tradesError, count } = await finalQuery;
        
        if (tradesError) {
          console.error('[FETCH_TRADES] Veri çekme hatası:', tradesError);
          throw tradesError;
        }

        // Toplam işlem sayısını ayarla (sayfalama için)
        if (count !== null && count !== undefined) {
          setTotalTrades(count);
        }

        if (tradesData) {
          // robot_id'leri olan kayıtları bulalım
          const tradesWithRobotIds = tradesData.filter(t => t.robot_id);
          const robotIds = [...new Set(tradesWithRobotIds.map(t => t.robot_id))];
          
          // Eğer robot_id'li kayıtlar varsa, satıcı bilgilerini ayrı bir sorgu ile çekelim
          let robotSellerMap: Record<string, string> = {};
          let robotDataMap: Record<string, any> = {};
          
          if (robotIds.length > 0) {
            console.log(`[FETCH_TRADES] ${robotIds.length} robot için bilgi çekiliyor`);
            
            const { data: robotData, error: robotError } = await supabase
              .from('robots')
              .select('id, seller_id, name')
              .in('id', robotIds);
              
            if (robotError) {
              console.error('[FETCH_TRADES] Robot bilgileri alınamadı:', robotError);
            } else if (robotData) {
              // robot id -> seller id map oluştur
              robotSellerMap = robotData.reduce((map, robot) => {
                map[robot.id] = robot.seller_id;
                return map;
              }, {} as Record<string, string>);
              
              // robot id -> robot data map oluştur
              robotDataMap = robotData.reduce((map, robot) => {
                map[robot.id] = robot;
                return map;
              }, {} as Record<string, any>);
              
              console.log(`[FETCH_TRADES] ${Object.keys(robotSellerMap).length} robot bilgisi alındı`);
            }
          }
          
          const processedTrades = tradesData.map((trade: any) => {
            // PNL, fiyat ve miktar değerlerini sayısal değerlere güvenli şekilde dönüştürme
            let pnlValue: number | null = null;
            let priceValue: number | null = null;
            let calculatedQuantityValue: number | null = null;
            let exitPriceValue: number | null = null;
            
            // PNL değeri işleme
            if (trade.pnl !== undefined && trade.pnl !== null) {
              try {
                pnlValue = typeof trade.pnl === 'string' ? parseFloat(trade.pnl) : trade.pnl;
                if (pnlValue === null || Number.isNaN(pnlValue)) pnlValue = null;
              } catch (e) {
                console.error('PNL parse error:', e);
                pnlValue = null;
              }
            }
            
            // Fiyat değeri işleme
            if (trade.price !== undefined && trade.price !== null) {
              try {
                priceValue = typeof trade.price === 'string' ? parseFloat(trade.price) : trade.price;
                if (priceValue === null || Number.isNaN(priceValue)) priceValue = null;
              } catch (e) {
                console.error('Price parse error:', e);
                priceValue = null;
              }
            }
            
            // Miktar değeri işleme
            if (trade.calculated_quantity !== undefined && trade.calculated_quantity !== null) {
              try {
                calculatedQuantityValue = typeof trade.calculated_quantity === 'string' ? 
                  parseFloat(trade.calculated_quantity) : trade.calculated_quantity;
                if (calculatedQuantityValue === null || Number.isNaN(calculatedQuantityValue)) calculatedQuantityValue = null;
              } catch (e) {
                console.error('Quantity parse error:', e);
                calculatedQuantityValue = null;
              }
            }
            
            // Çıkış fiyatı değeri işleme
            if (trade.exit_price !== undefined && trade.exit_price !== null) {
              try {
                exitPriceValue = typeof trade.exit_price === 'string' ? parseFloat(trade.exit_price) : trade.exit_price;
                if (exitPriceValue === null || Number.isNaN(exitPriceValue)) exitPriceValue = null;
              } catch (e) {
                console.error('Exit price parse error:', e);
                exitPriceValue = null;
              }
            }
            
            // Signal kaynağını belirle (Solo-Robot or Bro-Robot)
            const signalSourceType = trade.robot_id 
              ? SignalSourceType.BRO_ROBOT 
              : trade.webhook_id 
                ? SignalSourceType.SOLO_ROBOT 
                : undefined;
            
            // Signal tipi belirleme  
            let signalType = trade.signal_type;
            
            if (!signalType) {
              const upperName = trade.name ? trade.name.toUpperCase() : '';
              
              if (upperName.includes('BUY')) {
                signalType = 'BUY';
              } else if (upperName.includes('ATRTP')) {
                signalType = 'ATRTP';
              } else if (upperName.includes('KTP')) {
                signalType = 'KTP';
              } else if (upperName.includes('STOP')) {
                signalType = 'STOP';
              } else if (upperName.includes('SELL')) {
                signalType = 'SELL';
              } else {
                signalType = trade.order_side === 'buy' ? 'BUY' : trade.order_side === 'sell' ? 'SELL' : null;
              }
            }
            
            // Kategori belirleme
            let tradeCategory = trade.trade_category;
            if (!tradeCategory) {
              if (signalType === 'BUY' || trade.order_side === 'buy') {
                tradeCategory = 'Alım';
              } else if (['SELL', 'TP_SELL', 'SL_SELL', 'ATRTP', 'KTP', 'STOP'].includes(signalType) || trade.order_side === 'sell') {
                tradeCategory = 'Satım';
              } else {
                tradeCategory = 'Bilinmiyor';
              }
            }
            
            // Pozisyon durumu - backend'den gelen position_status değerini kullan, yoksa hesapla
            let positionStatus = trade.position_status;
            if (!positionStatus) {
              if (trade.is_closed === true || trade.trade_category === 'Satım') {
                positionStatus = 'Kapalı';
              } else if (trade.is_closed === false || trade.trade_category === 'Alım') {
                positionStatus = 'Açık';
              } else {
                positionStatus = 'Bilinmiyor';
              }
            }
            
            // Sistem adı - backend'den gelen sistem adı varsa kullan, yoksa signal kaynağına göre belirle
            let systemName = trade.system_name;
            
            // Robot ya da webhook id varlığına göre işlem kaynağını belirle 
            const isRobotSignal = !!trade.robot_id;
            const isWebhookSignal = !!trade.webhook_id;
            
            if (!systemName || systemName === 'SellerSignalRelay' || systemName === 'Robot Sinyali') {
              // DB'den gelen değer yoksa veya eski isimlerse, daha açıklayıcı bir isim bulmaya çalış
              if (isRobotSignal) {
                // Bro-Robot sinyali - robot_id var
                // Robot adı bilgisi varsa kullan
                const robotInfo = trade.robot_id && robotDataMap[trade.robot_id];
                if (robotInfo && robotInfo.name) {
                  systemName = `${robotInfo.name} (Bro-Robot)`;
                } else {
                  // Robot adı bilgisi yoksa, name alanından çıkarmaya çalış
                  const nameParts = trade.name ? trade.name.split(' ') : [];
                  if (nameParts.length > 0 && !['BUY', 'SELL', 'TP', 'SL'].includes(nameParts[0].toUpperCase())) {
                    // İlk kelime muhtemelen robot adı
                    systemName = `${nameParts[0]} (Bro-Robot)`;
                  } else {
                    // name'den çıkarılamadıysa robot ID'sini kullan
                    systemName = `Bro-Robot #${trade.robot_id.substring(0, 6)}`;
                  }
                }
              } else if (isWebhookSignal) {
                // Solo-Robot sinyali - webhook_id var
                systemName = 'Solo-Robot';
              } else {
                // Diğer durumlar için
                systemName = 'Bilinmiyor';
              }
            } else {
              // Backend'den gelen sistem adını kaynak belirtici ile zenginleştir
              if (isRobotSignal && !systemName.includes('Bro-Robot')) {
                systemName = `${systemName} (Bro-Robot)`;
              } else if (isWebhookSignal && !systemName.includes('Solo-Robot')) {
                systemName = `${systemName} (Solo-Robot)`;
              }
            }
            
            // Robot satıcı ID'sini belirle (ayrı sorguda çekilen map'ten)
            const robotSellerId = trade.robot_id ? robotSellerMap[trade.robot_id] || null : null;
            
            // Robot nesnesini oluştur (eğer robot_id varsa ve robotDataMap'te bu ID'ye karşılık gelen veri varsa)
            let robotObject = undefined;
            if (trade.robot_id && robotDataMap[trade.robot_id]) {
              robotObject = {
                id: trade.robot_id,
                name: robotDataMap[trade.robot_id].name || 'İsimsiz Robot',
                seller_id: robotDataMap[trade.robot_id].seller_id
              };
            }
            
            return {
              ...trade,
              id: typeof trade.id === 'number' ? trade.id : Number(trade.id),
              pnl: pnlValue,
              price: priceValue,
              calculated_quantity: calculatedQuantityValue,
              exit_price: exitPriceValue,
              signal_type: signalType,
              trade_category: tradeCategory,
              position_status: positionStatus,
              system_name: systemName,
              signal_source_type: signalSourceType,
              robot_seller_id: robotSellerId,
              robot: robotObject
            };
          });
          
          setTrades(processedTrades || []);
        }
      }
    } catch (err: any) {
      console.error('İşlemler alınamadı:', err);
      setError(err.message || 'İşlemler yüklenirken bir hata oluştu.');
      setTrades([]);
      setTotalTrades(0);
    } finally {
      setIsLoading(false);
    }
  }, [user, filtersString, currentPage, pageSize]);

  // Filtrelerde değişiklik olduğunda sayfa numarasını sıfırla
  useEffect(() => {
    setCurrentPage(1); // Filtreleme değiştiğinde ilk sayfaya dön
  }, [filtersString]);

  useEffect(() => {
    fetchTrades();
  }, [fetchTrades]);

  // Satıcı tarafından sinyal iptali fonksiyonu
  const handleCancelSignalBatch = async (trade: Trade) => {
    if (!user) {
      toast({
        title: 'Hata',
        description: 'Kullanıcı oturumu bulunamadı.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    if (!trade.robot_id) {
      toast({
        title: 'Hata',
        description: 'Bu işlem bir robot ile ilişkilendirilmemiş.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    // Artık UUID kontrolü yok, doğrudan numeric id kullan
    setIsCancellingSignalId(trade.id);
    console.log(`[CANCEL_SIGNAL] Sinyal iptal işlemi başlatılıyor, robot: ${trade.robot_id}, işlem: ${trade.id}`);
    try {
      if (trade.signal_batch_id) {
        const { data, error } = await supabase.rpc('cancel_signal_batch_for_robot', {
          p_robot_id: trade.robot_id,
          p_signal_batch_id: trade.signal_batch_id
        });
        if (error) {
          console.error('[CANCEL_SIGNAL] RPC hatası:', error);
          let userMessage = 'Sinyal iptal edilirken bir hata oluştu.';
          
          if (error.message?.includes('not authorized') || error.message?.includes('not the seller')) {
            userMessage = 'Bu robotun satıcısı olmadığınız için sinyali iptal edemezsiniz.';
          }
          
          toast({
            title: 'Sinyal İptal Edilemedi',
            description: userMessage,
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
          return;
        }
        const updatedCount = data?.updated_trades || 0;
        toast({
          title: 'Sinyal İptal Edildi',
          description: `${updatedCount} işlem başarıyla iptal edildi.`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      } else {
        const { data, error } = await supabase.rpc('cancel_signal_batch_by_criteria', {
          p_robot_id: trade.robot_id,
          p_signal_name: trade.name || '',
          p_signal_symbol: trade.symbol || '',
          p_signal_side: trade.order_side || '',
          p_approx_received_at: trade.received_at,
          p_time_window_minutes: 5
        });
        if (error) {
          console.error('[CANCEL_SIGNAL] Kriter bazlı RPC hatası:', error);
          toast({
            title: 'Sinyal İptal Edilemedi',
            description: 'Kriter bazlı sinyal iptali sırasında bir hata oluştu.',
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
          return;
        }
        const updatedCount = data?.updated_trades || 0;
        toast({
          title: 'Sinyal İptal Edildi',
          description: `${updatedCount} işlem kriter bazlı olarak iptal edildi.`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      }
      await fetchTrades();
    } catch (err: any) {
      console.error('[CANCEL_SIGNAL] Genel hata:', err);
      toast({
        title: 'Sinyal İptal Edilemedi',
        description: err.message || 'Bilinmeyen bir hata oluştu.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsCancellingSignalId(null);
    }
  };

  return { 
    trades, 
    isLoading, 
    error, 
    refetch: fetchTrades,
    handleDeleteTrade,
    handleCancelSignalBatch,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    totalPages,
    totalTrades,
    isDeletingTradeId,
    isCancellingSignalId
  };
} 