# Changelog

## [3.0.0] 2025-13-01

### Upgraded to React 19 ⚡️

## [2.0.0] 2024-07-24

### Vulnerabilities removed

- Most vulnerabilities removed, besides those cause by `react-scripts`. We kept this depedency due to the fact that there are
  many users who still use it, and there is already a Next.js version for thos who want to migrate from `react-scripts`.
- Updated to React 18.

## [1.1.1] 2022-11-01

🚀 Feature:
-Added TimelineRow

## [1.1.0] 2022-09-21

### React Tables V8

🚀 Feature: Now Horizon UI Typescript comes in V8 version for React Tables. You can clone the repository [here](https://github.com/horizon-ui/horizon-ui-chakra-ts/tree/feature/react-table-v8) !

## [1.1.0] 2022-09-21

### React Tables V8

🚀 Feature: Now Horizon UI Typescript comes in V8 version for React Tables. You can clone the repository [here](https://github.com/horizon-ui/horizon-ui-chakra-ts/tree/feature/react-table-v8) !

## [1.0.1] 2022-09-7

### Bug Fixing

Mapbox bug when using `yarn` fixed.

## [1.0.0] 2022-08-31

### Official Release

- Added TypeScript!
