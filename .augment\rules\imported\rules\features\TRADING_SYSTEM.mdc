---
type: "manual"
priority: "critical"
scope: ["trading", "robots", "signals", "orders", "real-time"]
last_updated: "2025-01-29"
---

# Trading System Features - Solo & Bro Robots

## 🤖 Robot Types & Architecture

### Solo-Robot System
```typescript
// Individual user trading automation
// Flow: TradingView → algobir-webhook-listener → User's Broker

interface SoloRobotFlow {
  signalSource: 'TradingView';
  endpoint: '/functions/v1/algobir-webhook-listener';
  authentication: 'webhook_id'; // Unique per user
  processing: 'individual';
  orderExecution: 'direct_to_broker';
}

// Signal Processing Pattern
const processSoloSignal = async (signal: TradingViewSignal, webhookId: string) => {
  // 1. Validate user by webhook_id
  const userSettings = await CacheManager.getUserSettingsByWebhookId(supabase, webhookId);
  
  // 2. Parse signal name and extract trading data
  const parsedSignal = parseSignalName(signal.signal_name);
  
  // 3. Create trade record
  const tradeRecord = await createTradeRecord({
    user_id: userSettings.id,
    symbol: parsedSignal.symbol,
    order_side: parsedSignal.orderSide,
    quantity: calculatedQuantity,
    price: signal.price
  });
  
  // 4. Execute order via broker API
  await executeOrder(tradeRecord);
  
  // 5. Send notification
  await createNotification(userSettings.id, notificationData);
};
```

### Bro-Robot System
```typescript
// Signal provider sharing with subscribers
// Flow: TradingView → seller-signal-endpoint → signal-relay-function → Subscribers

interface BroRobotFlow {
  signalSource: 'TradingView';
  endpoint: '/functions/v1/seller-signal-endpoint/{robot_id}';
  authentication: 'robot_id_validation';
  processing: 'broadcast_to_subscribers';
  orderExecution: 'individual_subscriber_brokers';
}

// Signal Distribution Pattern
const processBroSignal = async (signal: TradingViewSignal, robotId: string) => {
  // 1. Validate robot exists and is active
  const robot = await validateRobot(robotId);
  
  // 2. Get active subscribers
  const subscribers = await getActiveSubscribers(robotId);
  
  // 3. Relay signal to signal-relay-function
  await supabase.functions.invoke('signal-relay-function', {
    body: { robot_id: robotId, signal_data: signal }
  });
  
  // 4. Process for each subscriber individually
  for (const subscriber of subscribers) {
    await processSubscriberSignal(subscriber, signal, robot);
  }
};
```

## 📊 Signal Processing & Parsing

### Signal Name Parsing
```typescript
// Signal format: "SystemName ACTION SYMBOL [ADDITIONAL_INFO]"
// Examples: "Tobot AL THYAO", "System1 SILME AKBNK", "ProBot BUY ISCTR"

function parseSignalName(signalName: string) {
  const parts = signalName.trim().split(/\s+/);
  
  let systemName = 'Unknown';
  let signalType = 'UNKNOWN';
  let orderSide = 'BUY';
  let symbol = 'UNKNOWN';
  
  if (parts.length >= 2) {
    systemName = parts[0];
    signalType = parts[1].toUpperCase();
    
    // Determine order side from signal type
    if (['SELL', 'CLOSE', 'EXIT', 'SİLME', 'PTCTSELL'].includes(signalType)) {
      orderSide = 'SELL';
    } else {
      orderSide = 'BUY';
    }
    
    // Extract symbol (usually 3rd part)
    if (parts.length >= 3) {
      symbol = parts[2].toUpperCase();
    }
  }
  
  return {
    systemName,
    signalType,
    orderSide,
    symbol,
    originalSignal: signalName
  };
}
```

### Turkish Trading Terminology
```typescript
// Trading Categories and Status
const tradingTerms = {
  categories: {
    'BUY': 'ALIM',
    'SELL': 'SATIM'
  },
  positionStatus: {
    'BUY': 'Açık',
    'SELL': 'Kapalı'
  },
  signalTypes: {
    'AL': 'BUY',
    'SİLME': 'SELL',
    'PTCTSELL': 'SELL',
    'BUY': 'BUY',
    'SELL': 'SELL'
  }
};
```

## 💰 Investment & Position Management

### Investment Calculation
```typescript
// Solo-Robot Investment Logic
const calculateInvestmentAmount = (userSettings: UserSettings, signal: ParsedSignal) => {
  const totalInvestment = userSettings.total_investment_amount || 10000; // Default 10K TL
  
  if (signal.orderSide === 'BUY') {
    // For BUY orders, use total investment amount
    return totalInvestment;
  } else {
    // For SELL orders, find open position quantity
    return getOpenPositionQuantity(userSettings.id, signal.symbol);
  }
};

// Position Tracking
const getOpenPositionQuantity = async (userId: string, symbol: string) => {
  const { data } = await supabase
    .from('trades')
    .select('quantity, order_side')
    .eq('user_id', userId)
    .eq('symbol', symbol)
    .order('created_at', { ascending: false });
  
  let totalQuantity = 0;
  for (const trade of data || []) {
    if (trade.order_side === 'BUY') {
      totalQuantity += trade.quantity;
    } else {
      totalQuantity -= trade.quantity;
    }
  }
  
  return Math.max(0, totalQuantity); // Ensure non-negative
};
```

## 🔔 Notification System

### Turkish Notification Templates
```typescript
// Buy Signal Notification
const createBuyNotification = (trade: Trade, robotName: string) => ({
  title: `${trade.symbol} ALIM Sinyali`,
  message: `${robotName} - ${trade.quantity} adet ${trade.symbol} ${formatTurkishCurrency(trade.price)} TL'den alındı. Toplam: ${formatTurkishCurrency(trade.total_amount)} TL`,
  type: 'trade_opened',
  severity: 'success',
  metadata: {
    trade_id: trade.id,
    symbol: trade.symbol,
    order_side: trade.order_side,
    quantity: trade.quantity,
    price: trade.price,
    total_amount: trade.total_amount,
    source: 'solo-robot'
  },
  action_url: '/trades',
  action_label: 'İşlemleri Görüntüle'
});

// Sell Signal Notification
const createSellNotification = (trade: Trade, robotName: string) => ({
  title: `${trade.symbol} SATIM Sinyali`,
  message: `${robotName} - ${trade.quantity} adet ${trade.symbol} ${formatTurkishCurrency(trade.price)} TL'den satıldı. Toplam: ${formatTurkishCurrency(trade.total_amount)} TL`,
  type: 'trade_closed',
  severity: 'info',
  metadata: {
    trade_id: trade.id,
    symbol: trade.symbol,
    order_side: trade.order_side,
    quantity: trade.quantity,
    price: trade.price,
    total_amount: trade.total_amount,
    source: 'solo-robot'
  },
  action_url: '/trades',
  action_label: 'İşlemleri Görüntüle'
});

// Turkish Currency Formatting
function formatTurkishCurrency(amount: number): string {
  return new Intl.NumberFormat('tr-TR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}
```

## ⚡ Performance Optimization

### Sub-second Order Transmission
```typescript
// Performance Metrics Collection
interface PerformanceMetrics {
  signalReceivedAt: Date;
  jsonParsingStartTime: number;
  jsonParsingEndTime: number;
  transformationStartTime: number;
  transformationEndTime: number;
  webhookDeliveryStartTime: number;
  webhookDeliveryEndTime: number;
  totalStartTime: number;
}

// Optimization Strategies
const optimizeOrderTransmission = {
  // 1. Asynchronous Processing
  processAsync: async (trade: Trade) => {
    // Don't wait for broker response to return success
    executeOrderAsync(trade);
    return { success: true, trade_id: trade.id };
  },
  
  // 2. Connection Pooling
  useConnectionPool: true,
  poolSize: 25,
  maxConnections: 150,
  
  // 3. Caching
  cacheUserSettings: true,
  cacheExpiry: 5 * 60 * 1000, // 5 minutes
  
  // 4. Memory Management
  releaseMemoryAfterProcessing: true,
  forceGarbageCollection: true
};
```

### Real-time Data Updates
```typescript
// Real-time Trade Updates
const setupRealTimeSubscriptions = () => {
  // Trades subscription
  const tradesSubscription = supabase
    .channel('trades')
    .on('postgres_changes', 
      { event: 'INSERT', schema: 'public', table: 'trades' },
      (payload) => {
        // Update UI immediately
        updateTradesUI(payload.new);
        
        // Show notification
        showTradeNotification(payload.new);
      }
    )
    .subscribe();
  
  // Performance metrics subscription
  const metricsSubscription = supabase
    .channel('performance')
    .on('postgres_changes',
      { event: 'INSERT', schema: 'public', table: 'order_transmission_metrics' },
      (payload) => {
        updatePerformanceMetrics(payload.new);
      }
    )
    .subscribe();
};
```

## 🛡️ Error Handling & Resilience

### Robust Error Handling
```typescript
// Trade Processing with Error Recovery
const processTradeWithResilience = async (signal: TradingViewSignal) => {
  const maxRetries = 3;
  let attempt = 0;
  
  while (attempt < maxRetries) {
    try {
      // Attempt trade processing
      const result = await processTradeSignal(signal);
      return result;
      
    } catch (error) {
      attempt++;
      console.error(`Trade processing attempt ${attempt} failed:`, error);
      
      if (attempt >= maxRetries) {
        // Log final failure
        await logTradeFailure(signal, error);
        
        // Send error notification
        await sendErrorNotification(signal, error);
        
        throw error;
      }
      
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};

// Graceful Degradation
const handleBrokerUnavailable = async (trade: Trade) => {
  // Save trade record even if broker is unavailable
  const tradeRecord = await createTradeRecord(trade);
  
  // Queue for later execution
  await queueForRetry(tradeRecord);
  
  // Notify user of delay
  await notifyUserOfDelay(trade.user_id, trade.symbol);
  
  return { success: true, delayed: true, trade_id: tradeRecord.id };
};
```

## 📈 Analytics & Monitoring

### Trading Performance Tracking
```typescript
// Performance Metrics
interface TradingMetrics {
  totalTrades: number;
  successfulTrades: number;
  failedTrades: number;
  averageProcessingTime: number;
  profitLoss: number;
  winRate: percentage;
  averageOrderTransmissionTime: number;
}

// Real-time Monitoring
const monitorTradingPerformance = async () => {
  const metrics = await supabase.rpc('get_trading_performance_metrics', {
    time_period: '24h',
    user_id: userId
  });
  
  return {
    orderTransmissionSpeed: metrics.avg_transmission_time,
    successRate: (metrics.successful_orders / metrics.total_orders) * 100,
    errorRate: (metrics.failed_orders / metrics.total_orders) * 100,
    profitability: metrics.total_profit_loss
  };
};
```

## Update Requirements

### When to Update This File
- New robot types or trading strategies added
- Signal processing logic changes
- Performance optimization implementations
- Notification system updates
- Error handling improvements
- Analytics and monitoring enhancements

### Related Files to Update
- Update `backend/SUPABASE_EDGE.mdc` for backend changes
- Update `frontend/MAIN_APP.mdc` for UI updates
- Update `performance/OPTIMIZATION.mdc` for performance changes
- Update `security/GUIDELINES.mdc` for security updates
- Update `features/MARKETPLACE.mdc` for robot marketplace changes
