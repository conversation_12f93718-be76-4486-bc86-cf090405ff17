/**
 * Optimized Seller Signal Endpoint (BRO-ROBOT Flow Part 1)
 * Phase 2.4: Supabase Edge Functions Performance Optimization
 * Enhanced with performance monitoring, caching, and connection pooling
 */

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.8';
import { getCorsHeaders } from '../_shared/cors.ts';
import { 
  createPerformanceMonitor, 
  cache, 
  dbOptimizer, 
  responseOptimizer,
  rateLimiter,
  memoryManager,
  connectionPool
} from '../_shared/performance-optimizer.ts';

// Environment validation
const ENV_VARS = {
  supabaseUrl: Deno.env.get('SUPABASE_URL'),
  serviceRoleKey: Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
};

function validateEnvironment(): string[] {
  const missing: string[] = [];
  Object.entries(ENV_VARS).forEach(([key, value]) => {
    if (!value) missing.push(key);
  });
  return missing;
}

// Optimized robot validation with aggressive caching
class RobotValidator {
  private static readonly CACHE_TTL = 300000; // 5 minutes
  private static readonly ROBOT_ID_REGEX = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

  static async validateRobot(
    robotId: string, 
    supabase: any, 
    monitor: any
  ): Promise<{ valid: boolean; sellerId?: string; robot?: any; error?: string }> {
    // Format validation first (no DB call needed)
    if (!robotId || !this.ROBOT_ID_REGEX.test(robotId)) {
      return { valid: false, error: 'Invalid Robot ID format' };
    }

    const cacheKey = `robot_validation_${robotId}`;
    
    // Check cache first
    const cached = cache.get(cacheKey);
    if (cached) {
      monitor.recordCacheHit();
      return cached;
    }

    monitor.recordCacheMiss();

    try {
      const result = await dbOptimizer.executeQuery(
        supabase,
        `robot_${robotId}`,
        async () => {
          const { data, error } = await supabase
            .from('robots')
            .select('id, name, seller_id, is_public, is_emergency_stopped, deleted_at')
            .eq('id', robotId)
            .is('deleted_at', null)
            .single();
          return { data, error };
        },
        this.CACHE_TTL,
        monitor
      );

      if (result.error || !result.data) {
        const response = { valid: false, error: 'Robot not found or inactive' };
        cache.set(cacheKey, response, 60000); // Cache failures for 1 minute
        return response;
      }

      const robot = result.data;

      // Validation checks
      if (robot.is_emergency_stopped) {
        const response = { valid: false, sellerId: robot.seller_id, error: 'Robot is in emergency stop mode' };
        cache.set(cacheKey, response, 30000); // Cache for 30 seconds (might change quickly)
        return response;
      }

      if (!robot.is_public) {
        const response = { valid: false, sellerId: robot.seller_id, error: 'Robot is not publicly available' };
        cache.set(cacheKey, response, this.CACHE_TTL);
        return response;
      }

      const response = { valid: true, sellerId: robot.seller_id, robot };
      cache.set(cacheKey, response, this.CACHE_TTL);
      return response;

    } catch (error) {
      const response = { 
        valid: false, 
        error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
      cache.set(cacheKey, response, 60000);
      return response;
    }
  }
}

// Optimized seller status validation
class SellerValidator {
  private static readonly CACHE_TTL = 180000; // 3 minutes

  static async validateSellerStatus(
    sellerId: string, 
    supabase: any, 
    monitor: any
  ): Promise<{ active: boolean; error?: string }> {
    const cacheKey = `seller_status_${sellerId}`;
    
    const cached = cache.get(cacheKey);
    if (cached) {
      monitor.recordCacheHit();
      return cached;
    }

    monitor.recordCacheMiss();

    try {
      const result = await dbOptimizer.executeQuery(
        supabase,
        `seller_${sellerId}`,
        async () => {
          const { data, error } = await supabase
            .from('user_settings')
            .select('is_active')
            .eq('id', sellerId)
            .single();
          return { data, error };
        },
        this.CACHE_TTL,
        monitor
      );

      if (result.error || !result.data) {
        const response = { active: false, error: 'Seller settings not found' };
        cache.set(cacheKey, response, 60000);
        return response;
      }

      const response = { active: result.data.is_active === true };
      cache.set(cacheKey, response, this.CACHE_TTL);
      return response;

    } catch (error) {
      const response = { 
        active: false, 
        error: `Seller validation error: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
      cache.set(cacheKey, response, 60000);
      return response;
    }
  }
}

// Optimized signal relay with connection pooling
class SignalRelay {
  static async relaySignal(
    robotId: string, 
    signalData: any, 
    supabase: any, 
    monitor: any
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      monitor.startDbQuery();
      
      const { data, error } = await supabase.functions.invoke(
        'signal-relay-function',
        {
          body: { robot_id: robotId, signal_data: signalData }
        }
      );

      monitor.endDbQuery();

      if (error) {
        return { 
          success: false, 
          error: `Signal relay failed: ${error.message}` 
        };
      }

      return { success: true, data };

    } catch (error) {
      monitor.endDbQuery();
      return { 
        success: false, 
        error: `Signal relay exception: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }
}

// URL parsing utility
function extractRobotIdFromUrl(url: string): string | null {
  const urlObj = new URL(url);
  const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);
  
  // Look for UUID in path parts (from end to start)
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  
  for (let i = pathParts.length - 1; i >= 0; i--) {
    if (uuidRegex.test(pathParts[i])) {
      return pathParts[i];
    }
  }
  
  // Check query parameters as fallback
  const robotIdFromQuery = urlObj.searchParams.get('robot_id');
  if (robotIdFromQuery && uuidRegex.test(robotIdFromQuery)) {
    return robotIdFromQuery;
  }
  
  return null;
}

serve(async (req) => {
  const monitor = createPerformanceMonitor();
  const corsHeaders = getCorsHeaders(req.headers);
  
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return responseOptimizer.createJsonResponse('ok', 200, corsHeaders, monitor);
  }

  // Method validation
  if (req.method !== 'POST') {
    return responseOptimizer.createErrorResponse(
      'Method Not Allowed. Only POST is accepted.',
      405,
      corsHeaders,
      monitor
    );
  }

  // Environment validation
  const missingEnvVars = validateEnvironment();
  if (missingEnvVars.length > 0) {
    return responseOptimizer.createErrorResponse(
      `Missing environment variables: ${missingEnvVars.join(', ')}`,
      500,
      corsHeaders,
      monitor
    );
  }

  try {
    // Memory management
    const memoryStatus = memoryManager.checkMemoryUsage();
    if (memoryStatus.needsCleanup) {
      console.warn('[seller-signal-endpoint] High memory usage, cleanup performed');
    }

    // Rate limiting
    const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';
    
    // Extract robot ID for more specific rate limiting
    const robotId = extractRobotIdFromUrl(req.url);
    if (!robotId) {
      return responseOptimizer.createErrorResponse(
        'robot_id not found in URL path or query params',
        400,
        corsHeaders,
        monitor
      );
    }

    const rateLimitId = `${clientIP}:${robotId}`;
    const rateLimit = rateLimiter.checkLimit(rateLimitId, 30, 60000); // 30 requests per minute per IP/robot
    
    if (!rateLimit.allowed) {
      const rateLimitHeaders = new Headers(corsHeaders);
      rateLimitHeaders.set('Retry-After', '60');
      rateLimitHeaders.set('X-RateLimit-Remaining', rateLimit.remaining.toString());
      
      return responseOptimizer.createErrorResponse(
        'Rate limit exceeded. Maximum 30 requests per minute per IP/robot combination.',
        429,
        rateLimitHeaders,
        monitor
      );
    }

    // Initialize Supabase client with connection pooling
    const supabase = connectionPool.getPool(
      'supabase_main',
      () => createClient(ENV_VARS.supabaseUrl!, ENV_VARS.serviceRoleKey!)
    );

    // Robot validation
    const robotValidation = await RobotValidator.validateRobot(robotId, supabase, monitor);
    if (!robotValidation.valid) {
      return responseOptimizer.createErrorResponse(
        robotValidation.error!,
        403,
        corsHeaders,
        monitor
      );
    }

    // Seller status validation
    const sellerValidation = await SellerValidator.validateSellerStatus(
      robotValidation.sellerId!, 
      supabase, 
      monitor
    );
    
    if (!sellerValidation.active) {
      const message = `Signal ignored. Seller ${robotValidation.sellerId} is inactive.`;
      return responseOptimizer.createJsonResponse(
        { success: false, message },
        200,
        corsHeaders,
        monitor
      );
    }

    // Parse signal data
    const requestBody = await req.text();
    monitor.setRequestSize(new TextEncoder().encode(requestBody).length);
    
    let signalData: any;
    try {
      signalData = JSON.parse(requestBody);
    } catch {
      return responseOptimizer.createErrorResponse(
        'Invalid JSON in request body',
        400,
        corsHeaders,
        monitor
      );
    }

    if (!signalData) {
      return responseOptimizer.createErrorResponse(
        'Signal data is missing from request body',
        400,
        corsHeaders,
        monitor
      );
    }

    // Relay signal to subscribers
    const relayResult = await SignalRelay.relaySignal(robotId, signalData, supabase, monitor);
    
    if (!relayResult.success) {
      return responseOptimizer.createErrorResponse(
        relayResult.error!,
        500,
        corsHeaders,
        monitor
      );
    }

    // Success response
    const responseData = {
      success: true,
      message: 'Signal relay invoked successfully',
      robot_id: robotId,
      processing_time_ms: monitor.getCurrentMetrics().duration,
      response: relayResult.data,
      performance: {
        cache_hits: monitor.getCurrentMetrics().cacheHits,
        cache_misses: monitor.getCurrentMetrics().cacheMisses,
        db_queries: monitor.getCurrentMetrics().dbQueryCount
      }
    };

    return responseOptimizer.createJsonResponse(responseData, 200, corsHeaders, monitor);

  } catch (error) {
    console.error('[seller-signal-endpoint-optimized] Critical error:', error);
    
    const statusCode = error.message?.includes('robot_id not found') ? 400 :
                      error.message?.includes('not found') ? 404 :
                      error.message?.includes('Rate limit') ? 429 : 500;

    return responseOptimizer.createErrorResponse(
      `Failed to process signal: ${error instanceof Error ? error.message : 'Unknown error'}`,
      statusCode,
      corsHeaders,
      monitor
    );
  }
});
