import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabaseClient';

// Interface for platform statistics
interface PlatformStatistics {
  active_users_count: {
    value: number;
    last_updated: string;
  };
  total_transactions_count: {
    value: number;
    last_updated: string;
  };
}

// Interface for the hook return value
interface UsePlatformStatisticsReturn {
  statistics: PlatformStatistics | null;
  activeUsersCount: number;
  totalTransactionsCount: number;
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
  refetch: () => Promise<void>;
}

export function usePlatformStatistics(): UsePlatformStatisticsReturn {
  const [statistics, setStatistics] = useState<PlatformStatistics | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch platform statistics from the cache
  const fetchStatistics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('[PLATFORM-STATS] Fetching platform statistics...');

      // Call the get_platform_statistics RPC function
      const { data, error: rpcError } = await supabase.rpc('get_platform_statistics');

      if (rpcError) {
        console.error('[PLATFORM-STATS] RPC Error:', rpcError);
        throw new Error(rpcError.message);
      }

      if (data) {
        console.log('[PLATFORM-STATS] Statistics fetched successfully:', data);
        setStatistics(data);
      } else {
        console.warn('[PLATFORM-STATS] No statistics data returned');
        setStatistics(null);
      }
    } catch (err: any) {
      console.error('[PLATFORM-STATS] Error fetching statistics:', err);
      setError(err.message || 'Failed to fetch platform statistics');
      setStatistics(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch statistics on component mount
  useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  // Set up real-time subscription to platform_statistics table
  useEffect(() => {
    console.log('[PLATFORM-STATS] Setting up real-time subscription...');

    const subscription = supabase
      .channel('platform_statistics_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'platform_statistics'
        },
        (payload) => {
          console.log('[PLATFORM-STATS] Real-time update received:', payload);
          // Refetch statistics when the cache is updated
          fetchStatistics();
        }
      )
      .subscribe();

    return () => {
      console.log('[PLATFORM-STATS] Cleaning up real-time subscription...');
      subscription.unsubscribe();
    };
  }, [fetchStatistics]);

  // Derived values for easy access
  const activeUsersCount = statistics?.active_users_count?.value || 0;
  const totalTransactionsCount = statistics?.total_transactions_count?.value || 0;
  
  // Get the most recent update time
  const lastUpdated = statistics?.active_users_count?.last_updated || 
                     statistics?.total_transactions_count?.last_updated || 
                     null;

  return {
    statistics,
    activeUsersCount,
    totalTransactionsCount,
    isLoading,
    error,
    lastUpdated,
    refetch: fetchStatistics
  };
}
