import React from 'react';
import { 
  Box, 
  SimpleGrid, 
  Heading, 
  Text, 
  VStack, 
  Icon, 
  Container, 
  useColorModeValue,
  HStack,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Badge,
  Progress,
  Button,
  Alert,
  AlertIcon,
  AlertTitle,
    AlertDescription,
  Grid,
  GridItem,
  Center
} from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import Card from '../../components/card/Card';
import { 
  FaUser, 
  FaUsers, 
  FaQuestionCircle, 
  FaRocket, 
  FaShieldAlt, 
  FaCheck,
  FaArrowRight,
  FaDollarSign,
  FaRobot,
  FaGift,
  FaCloud,
  FaMobile,
  FaTerminal
} from 'react-icons/fa';
import { 
  MdSupport,
  MdPeople,
  MdMonetizationOn,
  MdHelpOutline,
  MdTimer,
  MdFlashOn,
  MdApi,
  MdCode,
  MdUpdate
} from 'react-icons/md';
import { SiTradingview } from 'react-icons/si';

const GuidePage: React.FC = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const cardBg = useColorModeValue('white', 'gray.800');
  const accentColor = useColorModeValue('purple.600', 'purple.300');
  
  return (
    <Box minH="100vh" bg={bgColor}>
      <Container maxW="1400px" pb={20}>
        <VStack spacing={20} align="stretch">
          {/* Hero Section */}
          <VStack spacing={12} textAlign="center">
            <Badge 
              colorScheme="purple" 
              fontSize="2xl" 
              px={12} 
              py={5} 
              borderRadius="full"
              bgGradient="linear(to-r, purple.400, pink.400, purple.600)"
              color="white"
              fontWeight="bold"
              textTransform="none"
              boxShadow="2xl"
              border="3px solid"
              borderColor="purple.200"
            >
              📚 Algobir Eğitim Merkezi
            </Badge>
            <VStack spacing={8}>
              <Heading 
                as="h1" 
                size="4xl" 
                bgGradient="linear(to-r, purple.400, blue.400, green.400, pink.400)"
                bgClip="text"
                fontWeight="black"
                letterSpacing="tight"
                lineHeight="1.1"
                textAlign="center"
                maxW="1200px"
              >
                Kapsamlı Kullanım Rehberleri
                <br />
                <Text as="span" fontSize="3xl" color={accentColor}>
                  Sıfırdan Profesyonel Seviyeye
                </Text>
              </Heading>
              <Text fontSize="2xl" color={textColor} maxW="1000px" lineHeight="1.8" fontWeight="medium">
                Hem bireysel traders hem de robot geliştiricileri için tasarlanmış, 
                <strong> dünya standartlarında</strong> detaylı kılavuzlar. Her adımı görsellerle, 
                kod örnekleriyle ve gerçek senaryolarla öğrenin.
              </Text>
              <HStack spacing={8} pt={6} flexWrap="wrap" justify="center">
                <Badge colorScheme="green" fontSize="lg" px={8} py={4} borderRadius="full" _hover={{ transform: 'scale(1.05)' }} transition="all 0.3s">
                  ✅ %100 Ücretsiz Eğitim
                </Badge>
                <Badge colorScheme="blue" fontSize="lg" px={8} py={4} borderRadius="full" _hover={{ transform: 'scale(1.05)' }} transition="all 0.3s">
                  🎯 Adım Adım Rehber
                </Badge>
                <Badge colorScheme="orange" fontSize="lg" px={8} py={4} borderRadius="full" _hover={{ transform: 'scale(1.05)' }} transition="all 0.3s">
                  💡 Gerçek Örnekler
                </Badge>
                <Badge colorScheme="purple" fontSize="lg" px={8} py={4} borderRadius="full" _hover={{ transform: 'scale(1.05)' }} transition="all 0.3s">
                  🏆 Uzman Seviye İçerik
                </Badge>
              </HStack>
            </VStack>
          </VStack>

          {/* Platform İstatistikleri */}
          <Box 
            bg={cardBg} 
            p={12} 
            borderRadius="3xl" 
            border="3px solid" 
            borderColor="purple.200"
            boxShadow="2xl"
            position="relative"
            overflow="hidden"
            _hover={{ transform: 'translateY(-5px)', boxShadow: '3xl' }}
            transition="all 0.4s"
          >
            <Box
              position="absolute"
              top="0"
              left="0"
              right="0"
              h="8px"
              bgGradient="linear(to-r, purple.400, pink.400, blue.400, green.400)"
            />
            <VStack spacing={12}>
              <VStack spacing={6}>
                <Heading size="2xl" color={accentColor} textAlign="center">
                  🚀 Platform Performans Metrikleri
                </Heading>
                <Text fontSize="xl" color={textColor} textAlign="center" maxW="800px" lineHeight="1.6">
                  Binlerce kullanıcı tarafından test edilmiş, kanıtlanmış performans değerleri
        </Text>
      </VStack>
              <SimpleGrid columns={{ base: 2, md: 4 }} spacing={12} w="full">
                <Stat 
                  textAlign="center" 
                  bg={useColorModeValue('green.50', 'green.900')} 
                  p={10} 
                  borderRadius="2xl" 
                  border="3px solid" 
                  borderColor="green.300"
                  _hover={{ transform: 'translateY(-8px)', borderColor: 'green.400' }}
                  transition="all 0.3s"
                  cursor="pointer"
                >
                  <Icon as={MdTimer} boxSize={8} color="green.500" mb={4} />
                  <StatLabel fontSize="lg" color="gray.600" fontWeight="bold">Ortalama Kurulum</StatLabel>
                  <StatNumber color="green.500" fontSize="4xl" fontWeight="black">12 dk</StatNumber>
                  <StatHelpText fontSize="md" color="green.600" fontWeight="medium">Solo-Robot için</StatHelpText>
                  <Progress value={95} colorScheme="green" size="lg" mt={6} borderRadius="full" />
                </Stat>
                <Stat 
                  textAlign="center" 
                  bg={useColorModeValue('blue.50', 'blue.900')} 
                  p={10} 
                  borderRadius="2xl" 
                  border="3px solid" 
                  borderColor="blue.300"
                  _hover={{ transform: 'translateY(-8px)', borderColor: 'blue.400' }}
                  transition="all 0.3s"
                  cursor="pointer"
                >
                  <Icon as={MdFlashOn} boxSize={8} color="blue.500" mb={4} />
                  <StatLabel fontSize="lg" color="gray.600" fontWeight="bold">Sinyal Hızı</StatLabel>
                  <StatNumber color="blue.500" fontSize="4xl" fontWeight="black">&lt;1.5s</StatNumber>
                  <StatHelpText fontSize="md" color="blue.600" fontWeight="medium">TradingView → Borsa</StatHelpText>
                  <Progress value={98} colorScheme="blue" size="lg" mt={6} borderRadius="full" />
                </Stat>
                <Stat 
                  textAlign="center" 
                  bg={useColorModeValue('purple.50', 'purple.900')} 
                  p={10} 
                  borderRadius="2xl" 
                  border="3px solid" 
                  borderColor="purple.300"
                  _hover={{ transform: 'translateY(-8px)', borderColor: 'purple.400' }}
                  transition="all 0.3s"
                  cursor="pointer"
                >
                  <Icon as={FaRobot} boxSize={8} color="purple.500" mb={4} />
                  <StatLabel fontSize="lg" color="gray.600" fontWeight="bold">Aktif Robotlar</StatLabel>
                  <StatNumber color="purple.500" fontSize="4xl" fontWeight="black">1,200+</StatNumber>
                  <StatHelpText fontSize="md" color="purple.600" fontWeight="medium">Pazaryerinde</StatHelpText>
                  <Progress value={85} colorScheme="purple" size="lg" mt={6} borderRadius="full" />
                </Stat>
                <Stat 
                  textAlign="center" 
                  bg={useColorModeValue('orange.50', 'orange.900')} 
                  p={10} 
                  borderRadius="2xl" 
                  border="3px solid" 
                  borderColor="orange.300"
                  _hover={{ transform: 'translateY(-8px)', borderColor: 'orange.400' }}
                  transition="all 0.3s"
            cursor="pointer"
                >
                  <Icon as={FaShieldAlt} boxSize={8} color="orange.500" mb={4} />
                  <StatLabel fontSize="lg" color="gray.600" fontWeight="bold">Güvenlik Skoru</StatLabel>
                  <StatNumber color="orange.500" fontSize="4xl" fontWeight="black">A+</StatNumber>
                  <StatHelpText fontSize="md" color="orange.600" fontWeight="medium">256-bit SSL</StatHelpText>
                  <Progress value={100} colorScheme="orange" size="lg" mt={6} borderRadius="full" />
                </Stat>
              </SimpleGrid>
            </VStack>
          </Box>

          {/* Hangi Kılavuzu Seçmelisiniz? */}
          <Box 
            bg={cardBg} 
            p={12} 
            borderRadius="3xl" 
            border="3px solid" 
            borderColor="blue.200"
            boxShadow="xl"
          >
            <VStack spacing={10}>
              <VStack spacing={4}>
                <Heading size="2xl" color="blue.600" textAlign="center">
                  🤔 Hangi Kılavuzu Seçmelisiniz?
                </Heading>
                <Text fontSize="xl" color={textColor} textAlign="center" maxW="800px">
                  İhtiyaçlarınıza ve hedeflerinize göre doğru rehberi seçin
                </Text>
              </VStack>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={10} w="full">
                <Box 
                  bg={useColorModeValue('green.50', 'green.900')} 
                  p={8} 
                  borderRadius="2xl" 
                  border="3px solid" 
                  borderColor="green.300"
                  _hover={{ transform: 'scale(1.02)', borderColor: 'green.400' }}
                  transition="all 0.3s"
                >
                  <VStack spacing={4} align="start">
                    <Icon as={FaUser} boxSize={6} color="green.500" />
                    <Heading size="lg" color="green.600">Solo-Robot Size Uygun:</Heading>
                    <VStack align="start" spacing={2}>
                      <HStack><Icon as={FaCheck} color="green.500" /><Text>Kendi stratejilerinizi otomatize etmek istiyorsunuz</Text></HStack>
                      <HStack><Icon as={FaCheck} color="green.500" /><Text>TradingView kullanıyorsunuz</Text></HStack>
                      <HStack><Icon as={FaCheck} color="green.500" /><Text>Bireysel trader olarak işlem yapıyorsunuz</Text></HStack>
                      <HStack><Icon as={FaCheck} color="green.500" /><Text>Hızlı kurulum istiyorsunuz (15 dk)</Text></HStack>
                      <HStack><Icon as={FaCheck} color="green.500" /><Text>Teknik bilgi gerektirmeyen çözüm arıyorsunuz</Text></HStack>
                    </VStack>
                  </VStack>
                </Box>
                <Box 
                  bg={useColorModeValue('purple.50', 'purple.900')} 
                  p={8} 
                  borderRadius="2xl" 
                  border="3px solid" 
                  borderColor="purple.300"
                  _hover={{ transform: 'scale(1.02)', borderColor: 'purple.400' }}
                  transition="all 0.3s"
          >
            <VStack spacing={4} align="start">
                    <Icon as={FaUsers} boxSize={6} color="purple.500" />
                    <Heading size="lg" color="purple.600">Bro-Robot Size Uygun:</Heading>
                    <VStack align="start" spacing={2}>
                      <HStack><Icon as={FaCheck} color="purple.500" /><Text>Robotlarınızı satarak gelir elde etmek istiyorsunuz</Text></HStack>
                      <HStack><Icon as={FaCheck} color="purple.500" /><Text>Ticaret stratejinizi paylaşmak istiyorsunuz</Text></HStack>
                      <HStack><Icon as={FaCheck} color="purple.500" /><Text>Pasif gelir kaynağı arıyorsunuz</Text></HStack>
                      <HStack><Icon as={FaCheck} color="purple.500" /><Text>API entegrasyonu yapabiliyorsunuz</Text></HStack>
                      <HStack><Icon as={FaCheck} color="purple.500" /><Text>Büyük kullanıcı kitlesine ulaşmak istiyorsunuz</Text></HStack>
                    </VStack>
                  </VStack>
                </Box>
              </SimpleGrid>
            </VStack>
          </Box>

          {/* Main Guide Cards */}
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={12} w="full">
            {/* Solo-Robot Guide Card */}
            <Card
              bg={cardBg}
              p={0}
              borderRadius="3xl"
              border="3px solid"
              borderColor="green.300"
              overflow="hidden"
              _hover={{ 
                transform: 'translateY(-10px)', 
                boxShadow: '2xl',
                borderColor: 'green.400'
              }}
              transition="all 0.4s ease-in-out"
              cursor="pointer"
              position="relative"
            >
              <Box
                h="8px"
                w="full"
                bgGradient="linear(to-r, green.400, blue.400, green.500)"
              />
              <VStack spacing={8} p={10} align="stretch" h="full">
                <VStack spacing={6}>
                  <Center
                    w={20}
                    h={20}
                    borderRadius="full"
                    bg={useColorModeValue('green.100', 'green.800')}
                    border="4px solid"
                    borderColor="green.300"
                  >
                    <Icon as={FaUser} boxSize={10} color="green.500" />
                  </Center>
                  <VStack spacing={4} textAlign="center">
                    <Heading size="xl" color="green.600" fontWeight="black">
                      Solo-Robot
                    </Heading>
                    <Text fontSize="lg" color={textColor} fontWeight="medium" lineHeight="1.6">
                      Bireysel traders için tasarlanmış, TradingView entegrasyonlu otomatik ticaret sistemi
                    </Text>
                  </VStack>
                </VStack>

                <VStack spacing={6} flex="1">
                  <Grid templateColumns="1fr 1fr" gap={4} w="full">
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={MdTimer} color="green.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="green.600">15 Dakika</Text>
                        <Text fontSize="xs" color={textColor}>Kurulum Süresi</Text>
                      </VStack>
                    </GridItem>
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={MdFlashOn} color="green.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="green.600">&lt;2 Saniye</Text>
                        <Text fontSize="xs" color={textColor}>Sinyal Hızı</Text>
                      </VStack>
                    </GridItem>
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={SiTradingview} color="green.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="green.600">TradingView</Text>
                        <Text fontSize="xs" color={textColor}>Doğrudan Entegrasyon</Text>
                      </VStack>
                    </GridItem>
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={FaShieldAlt} color="green.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="green.600">256-bit</Text>
                        <Text fontSize="xs" color={textColor}>SSL Güvenlik</Text>
                      </VStack>
                    </GridItem>
                  </Grid>

                  <VStack spacing={3} w="full">
                    <Text fontSize="md" fontWeight="bold" color="green.600">Öne Çıkan Özellikler:</Text>
                    <VStack spacing={2} align="start" w="full">
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="green.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Plug-and-play kurulum</Text>
                      </HStack>
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="green.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Otomatik P&L hesaplama</Text>
                      </HStack>
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="green.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>15+ borsa desteği</Text>
                      </HStack>
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="green.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Gerçek zamanlı izleme</Text>
                      </HStack>
                    </VStack>
                  </VStack>
                </VStack>

                <Button
                  as={RouterLink}
                  to="/guide/solo-robot"
                  size="lg"
                  colorScheme="green"
                  variant="solid"
                  rightIcon={<FaArrowRight />}
                  _hover={{ transform: 'scale(1.05)' }}
                  transition="all 0.3s"
                  fontWeight="bold"
                  fontSize="lg"
                  h={14}
                >
                  Detaylı Rehberi Görüntüle
                </Button>
              </VStack>
            </Card>

            {/* Bro-Robot Guide Card */}
            <Card
              bg={cardBg}
              p={0}
              borderRadius="3xl"
              border="3px solid"
              borderColor="purple.300"
              overflow="hidden"
              _hover={{ 
                transform: 'translateY(-10px)', 
                boxShadow: '2xl',
                borderColor: 'purple.400'
              }}
              transition="all 0.4s ease-in-out"
              cursor="pointer"
              position="relative"
            >
              <Box
                h="8px"
                w="full"
                bgGradient="linear(to-r, purple.400, pink.400, purple.500)"
              />
              <VStack spacing={8} p={10} align="stretch" h="full">
                <VStack spacing={6}>
                  <Center
                    w={20}
                    h={20}
                    borderRadius="full"
                    bg={useColorModeValue('purple.100', 'purple.800')}
                    border="4px solid"
                    borderColor="purple.300"
                  >
                    <Icon as={FaUsers} boxSize={10} color="purple.500" />
                  </Center>
                  <VStack spacing={4} textAlign="center">
                    <Heading size="xl" color="purple.600" fontWeight="black">
                      Bro-Robot
                    </Heading>
                    <Text fontSize="lg" color={textColor} fontWeight="medium" lineHeight="1.6">
                      Robot geliştiricileri için pazaryeri sistemi ve pasif gelir platformu
                    </Text>
                  </VStack>
                </VStack>

                <VStack spacing={6} flex="1">
                  <Grid templateColumns="1fr 1fr" gap={4} w="full">
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={MdMonetizationOn} color="purple.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="purple.600">₺2,500+</Text>
                        <Text fontSize="xs" color={textColor}>Aylık Gelir</Text>
                      </VStack>
                    </GridItem>
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={MdPeople} color="purple.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="purple.600">Sınırsız</Text>
                        <Text fontSize="xs" color={textColor}>Abone Sayısı</Text>
                      </VStack>
                    </GridItem>
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={MdApi} color="purple.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="purple.600">REST API</Text>
                        <Text fontSize="xs" color={textColor}>Webhook Desteği</Text>
                      </VStack>
                    </GridItem>
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={FaDollarSign} color="purple.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="purple.600">%0</Text>
                        <Text fontSize="xs" color={textColor}>Platform Komisyonu</Text>
                      </VStack>
                    </GridItem>
                  </Grid>

                  <VStack spacing={3} w="full">
                    <Text fontSize="md" fontWeight="bold" color="purple.600">Öne Çıkan Özellikler:</Text>
                    <VStack spacing={2} align="start" w="full">
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="purple.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Pazaryerinde listeleme</Text>
                      </HStack>
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="purple.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Otomatik abone yönetimi</Text>
                      </HStack>
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="purple.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Gelişmiş istatistikler</Text>
                      </HStack>
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="purple.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Webhook API desteği</Text>
                      </HStack>
                    </VStack>
                  </VStack>
                </VStack>

                <Button
                  as={RouterLink}
                  to="/guide/bro-robot"
                  size="lg"
                  colorScheme="purple"
                  variant="solid"
                  rightIcon={<FaArrowRight />}
                  _hover={{ transform: 'scale(1.05)' }}
                  transition="all 0.3s"
                  fontWeight="bold"
                  fontSize="lg"
                  h={14}
                >
                  Detaylı Rehberi Görüntüle
                </Button>
            </VStack>
          </Card>

            {/* FAQ Guide Card */}
          <Card
              bg={cardBg}
              p={0}
              borderRadius="3xl"
              border="3px solid"
              borderColor="orange.300"
              overflow="hidden"
              _hover={{ 
                transform: 'translateY(-10px)', 
                boxShadow: '2xl',
                borderColor: 'orange.400'
              }}
              transition="all 0.4s ease-in-out"
            cursor="pointer"
              position="relative"
            >
              <Box
                h="8px"
                w="full"
                bgGradient="linear(to-r, orange.400, yellow.400, orange.500)"
              />
              <VStack spacing={8} p={10} align="stretch" h="full">
                <VStack spacing={6}>
                  <Center
                    w={20}
                    h={20}
                    borderRadius="full"
                    bg={useColorModeValue('orange.100', 'orange.800')}
                    border="4px solid"
                    borderColor="orange.300"
                  >
                    <Icon as={FaQuestionCircle} boxSize={10} color="orange.500" />
                  </Center>
                  <VStack spacing={4} textAlign="center">
                    <Heading size="xl" color="orange.600" fontWeight="black">
                      Sık Sorulan Sorular
                    </Heading>
                    <Text fontSize="lg" color={textColor} fontWeight="medium" lineHeight="1.6">
                      En çok merak edilen konular ve detaylı cevapları
                    </Text>
                  </VStack>
                </VStack>

                <VStack spacing={6} flex="1">
                  <Grid templateColumns="1fr 1fr" gap={4} w="full">
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={MdHelpOutline} color="orange.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="orange.600">50+</Text>
                        <Text fontSize="xs" color={textColor}>Soru & Cevap</Text>
                      </VStack>
                    </GridItem>
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={MdSupport} color="orange.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="orange.600">7/24</Text>
                        <Text fontSize="xs" color={textColor}>Destek</Text>
                      </VStack>
                    </GridItem>
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={MdCode} color="orange.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="orange.600">Teknik</Text>
                        <Text fontSize="xs" color={textColor}>Destek</Text>
                      </VStack>
                    </GridItem>
                    <GridItem>
                      <VStack spacing={2}>
                        <Icon as={MdUpdate} color="orange.500" boxSize={6} />
                        <Text fontSize="sm" fontWeight="bold" color="orange.600">Güncel</Text>
                        <Text fontSize="xs" color={textColor}>İçerik</Text>
                      </VStack>
                    </GridItem>
                  </Grid>

                  <VStack spacing={3} w="full">
                    <Text fontSize="md" fontWeight="bold" color="orange.600">Kapsadığı Konular:</Text>
                    <VStack spacing={2} align="start" w="full">
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="orange.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Teknik sorun çözümleri</Text>
                      </HStack>
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="orange.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Güvenlik konuları</Text>
                      </HStack>
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="orange.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Borsa entegrasyonları</Text>
                      </HStack>
                      <HStack spacing={3}>
                        <Icon as={FaCheck} color="orange.500" boxSize={4} />
                        <Text fontSize="sm" color={textColor}>Platform kullanımı</Text>
                      </HStack>
                    </VStack>
                  </VStack>
                </VStack>

                <Button
                  as={RouterLink}
                  to="/guide/faq"
                  size="lg"
                  colorScheme="orange"
                  variant="solid"
                  rightIcon={<FaArrowRight />}
                  _hover={{ transform: 'scale(1.05)' }}
                  transition="all 0.3s"
                  fontWeight="bold"
                  fontSize="lg"
                  h={14}
                >
                  SSS'yi Görüntüle
                </Button>
              </VStack>
            </Card>
          </SimpleGrid>

          {/* Additional Features */}
          <Box 
            bg={cardBg} 
            p={12} 
            borderRadius="3xl" 
            border="3px solid" 
            borderColor="gray.200"
            boxShadow="xl"
          >
            <VStack spacing={10}>
              <VStack spacing={4}>
                <Heading size="2xl" color={accentColor} textAlign="center">
                  🎯 Neden Algobir'i Seçmelisiniz?
                </Heading>
                <Text fontSize="xl" color={textColor} textAlign="center" maxW="800px">
                  Rakiplerinden farklı olarak sunduğumuz benzersiz avantajlar
              </Text>
              </VStack>
              <SimpleGrid columns={{ base: 2, md: 4 }} spacing={8} w="full">
                <VStack spacing={4} p={6} bg={useColorModeValue('blue.50', 'blue.900')} borderRadius="xl" border="2px solid" borderColor="blue.200">
                  <Icon as={FaCloud} boxSize={8} color="blue.500" />
                  <Text fontWeight="bold" color="blue.600" textAlign="center">Bulut Tabanlı</Text>
                  <Text fontSize="sm" color={textColor} textAlign="center">Sunucu kurulumu gerektirmez</Text>
                </VStack>
                <VStack spacing={4} p={6} bg={useColorModeValue('green.50', 'green.900')} borderRadius="xl" border="2px solid" borderColor="green.200">
                  <Icon as={FaMobile} boxSize={8} color="green.500" />
                  <Text fontWeight="bold" color="green.600" textAlign="center">Mobil Uyumlu</Text>
                  <Text fontSize="sm" color={textColor} textAlign="center">Her cihazdan erişilebilir</Text>
                </VStack>
                <VStack spacing={4} p={6} bg={useColorModeValue('purple.50', 'purple.900')} borderRadius="xl" border="2px solid" borderColor="purple.200">
                  <Icon as={FaTerminal} boxSize={8} color="purple.500" />
                  <Text fontWeight="bold" color="purple.600" textAlign="center">Açık Kaynak</Text>
                  <Text fontSize="sm" color={textColor} textAlign="center">Şeffaf ve güvenilir kod</Text>
                </VStack>
                <VStack spacing={4} p={6} bg={useColorModeValue('orange.50', 'orange.900')} borderRadius="xl" border="2px solid" borderColor="orange.200">
                  <Icon as={FaGift} boxSize={8} color="orange.500" />
                  <Text fontWeight="bold" color="orange.600" textAlign="center">%100 Ücretsiz</Text>
                  <Text fontSize="sm" color={textColor} textAlign="center">Gizli ücret bulunmaz</Text>
                </VStack>
              </SimpleGrid>
            </VStack>
          </Box>

          {/* Call to Action */}
          <Alert 
            status="info" 
            borderRadius="3xl" 
            p={12}
            bg={useColorModeValue('brand.50', 'brand.900')}
            border="3px solid"
            borderColor="brand.200"
          >
            <AlertIcon boxSize={8} />
            <Box flex="1">
              <AlertTitle fontSize="2xl" mb={4}>
                🚀 Hemen Başlayın!
              </AlertTitle>
              <AlertDescription fontSize="lg" lineHeight="1.8">
                Yukarıdaki rehberlerden birini seçerek Algobir'in gücünü keşfedin. 
                Her iki seçenek de tamamen ücretsizdir ve profesyonel destek sağlanır.
                <br />
                <strong>Ortalama kurulum süresi sadece 15-30 dakika!</strong>
              </AlertDescription>
              <HStack spacing={6} mt={8}>
                <Button 
                  as={RouterLink} 
                  to="/guide/solo-robot" 
                  colorScheme="green" 
                  size="lg"
                  rightIcon={<FaRocket />}
                  _hover={{ transform: 'scale(1.05)' }}
                  transition="all 0.3s"
                >
                  Solo-Robot'a Başla
                </Button>
                <Button 
                  as={RouterLink} 
                  to="/guide/bro-robot" 
                  colorScheme="purple" 
                  size="lg"
                  rightIcon={<FaUsers />}
                  _hover={{ transform: 'scale(1.05)' }}
                  transition="all 0.3s"
                >
                  Bro-Robot'a Başla
                </Button>
              </HStack>
            </Box>
          </Alert>
        </VStack>
      </Container>
    </Box>
  );
};

export default GuidePage; 