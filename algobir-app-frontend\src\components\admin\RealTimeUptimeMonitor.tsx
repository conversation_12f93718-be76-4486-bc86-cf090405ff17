import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  SimpleGrid,
  Progress,
  Badge,
  Icon,
  CircularProgress,
  CircularProgressLabel,
  Divider
} from '@chakra-ui/react';
import {
  FaDesktop,
  FaServer,
  FaDatabase,
  FaCloud,

  FaExclamationTriangle,
  FaCheckCircle,
  FaClock
} from 'react-icons/fa';
import { motion } from 'framer-motion';
import { UptimeMetrics } from '../../hooks/useSystemMonitoring';

const MotionBox = motion(Box);

interface RealTimeUptimeMonitorProps {
  uptime: UptimeMetrics;
  incidents: number;
  loading?: boolean;
  lastUpdate?: Date;
}

const RealTimeUptimeMonitor: React.FC<RealTimeUptimeMonitorProps> = ({
  uptime,
  incidents,
  loading = false,
  lastUpdate
}) => {
  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  // Helper function to get uptime color
  const getUptimeColor = (value: number) => {
    if (value >= 99.5) return 'green';
    if (value >= 99.0) return 'yellow';
    if (value >= 95.0) return 'orange';
    return 'red';
  };

  // Helper function to get uptime status
  const getUptimeStatus = (value: number) => {
    if (value >= 99.9) return 'Mükemmel';
    if (value >= 99.5) return 'Çok İyi';
    if (value >= 99.0) return 'İyi';
    if (value >= 95.0) return 'Orta';
    return 'Düşük';
  };



  const uptimeComponents = [
    {
      name: 'Frontend',
      value: uptime.frontend,
      icon: FaDesktop,
      description: 'React uygulaması'
    },
    {
      name: 'Backend',
      value: uptime.backend,
      icon: FaServer,
      description: 'API servisleri'
    },
    {
      name: 'Veritabanı',
      value: uptime.database,
      icon: FaDatabase,
      description: 'PostgreSQL'
    },
    {
      name: 'Edge Functions',
      value: uptime.edgeFunctions,
      icon: FaCloud,
      description: 'Webhook işleme'
    }
  ];

  return (
    <Card
      bg={cardBg}
      borderRadius="xl"
      boxShadow="lg"
      border="1px solid"
      borderColor={borderColor}
      overflow="hidden"
    >
      <CardHeader pb={2}>
        <HStack justify="space-between" align="center">
          <VStack align="start" spacing={1}>
            <Heading size="md" color={textColor}>
              Sistem Çalışma Süresi
            </Heading>
            <HStack spacing={2}>
              <Icon
                as={loading ? FaClock : FaCheckCircle}
                color={loading ? 'yellow.500' : 'green.500'}
                boxSize={3}
              />
              <Text fontSize="xs" color={mutedColor}>
                {loading ? 'Güncelleniyor...' : 
                 lastUpdate ? `Son güncelleme: ${lastUpdate.toLocaleTimeString('tr-TR')}` : 
                 'Veri yok'}
              </Text>
            </HStack>
          </VStack>

          <VStack align="end" spacing={1}>
            <Badge
              colorScheme={getUptimeColor(uptime.overall)}
              variant="solid"
              borderRadius="full"
              px={3}
              py={1}
              fontSize="sm"
            >
              %{uptime.overall.toFixed(2)} Genel
            </Badge>
            <HStack spacing={2}>
              <Icon
                as={incidents > 0 ? FaExclamationTriangle : FaCheckCircle}
                color={incidents > 0 ? 'red.500' : 'green.500'}
                boxSize={3}
              />
              <Text fontSize="xs" color={mutedColor}>
                {incidents} Aktif Olay
              </Text>
            </HStack>
          </VStack>
        </HStack>
      </CardHeader>

      <CardBody pt={0}>
        <VStack spacing={6} align="stretch">
          {/* Overall Uptime Display */}
          <Box textAlign="center">
            <CircularProgress
              value={uptime.overall}
              color={`${getUptimeColor(uptime.overall)}.400`}
              size="120px"
              thickness="8px"
            >
              <CircularProgressLabel>
                <VStack spacing={0}>
                  <Text fontSize="lg" fontWeight="bold" color={textColor}>
                    {uptime.overall.toFixed(2)}%
                  </Text>
                  <Text fontSize="xs" color={mutedColor}>
                    Genel Uptime
                  </Text>
                </VStack>
              </CircularProgressLabel>
            </CircularProgress>
            <Text fontSize="sm" color={mutedColor} mt={2}>
              {getUptimeStatus(uptime.overall)} performans seviyesi
            </Text>
          </Box>

          <Divider />

          {/* Component Uptime Grid */}
          <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
            {uptimeComponents.map((component, index) => {
              return (
                <MotionBox
                  key={component.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card
                    bg={useColorModeValue('gray.50', 'gray.700')}
                    borderRadius="lg"
                    border="1px solid"
                    borderColor={`${getUptimeColor(component.value)}.200`}
                    p={3}
                    _hover={{ transform: 'translateY(-2px)', transition: 'all 0.2s' }}
                  >
                    <VStack spacing={3}>
                      <HStack spacing={2} w="full" justify="center">
                        <Icon
                          as={component.icon}
                          color={`${getUptimeColor(component.value)}.500`}
                          boxSize={4}
                        />
                        <Text fontSize="sm" fontWeight="medium" color={textColor}>
                          {component.name}
                        </Text>
                      </HStack>

                      <CircularProgress
                        value={component.value}
                        color={`${getUptimeColor(component.value)}.400`}
                        size="60px"
                        thickness="6px"
                      >
                        <CircularProgressLabel fontSize="xs" fontWeight="bold">
                          {component.value.toFixed(1)}%
                        </CircularProgressLabel>
                      </CircularProgress>

                      <VStack spacing={1}>
                        <Badge
                          colorScheme={getUptimeColor(component.value)}
                          variant="subtle"
                          fontSize="xs"
                        >
                          {getUptimeStatus(component.value)}
                        </Badge>
                      </VStack>

                      <Text fontSize="xs" color={mutedColor} textAlign="center">
                        {component.description}
                      </Text>
                    </VStack>
                  </Card>
                </MotionBox>
              );
            })}
          </SimpleGrid>

          {/* Uptime Progress Bars */}
          <VStack spacing={3} align="stretch">
            <Text fontSize="sm" fontWeight="medium" color={textColor}>
              Detaylı Uptime Analizi
            </Text>
            
            {uptimeComponents.map((component) => (
              <Box key={component.name}>
                <HStack justify="space-between" mb={1}>
                  <HStack spacing={2}>
                    <Icon as={component.icon} boxSize={3} color={mutedColor} />
                    <Text fontSize="sm" color={textColor}>
                      {component.name}
                    </Text>
                  </HStack>
                  <HStack spacing={2}>
                    <Badge
                      colorScheme={getUptimeColor(component.value)}
                      variant="subtle"
                      fontSize="xs"
                    >
                      {getUptimeStatus(component.value)}
                    </Badge>
                    <Text fontSize="sm" fontWeight="bold" color={textColor}>
                      {component.value.toFixed(2)}%
                    </Text>
                  </HStack>
                </HStack>
                <Progress
                  value={component.value}
                  colorScheme={getUptimeColor(component.value)}
                  size="sm"
                  borderRadius="full"
                  bg={useColorModeValue('gray.100', 'gray.600')}
                />
              </Box>
            ))}
          </VStack>

          {/* Incidents Summary */}
          <Box
            bg={incidents > 0 ? 
              useColorModeValue('red.50', 'red.900') : 
              useColorModeValue('green.50', 'green.900')
            }
            p={4}
            borderRadius="lg"
            border="1px solid"
            borderColor={incidents > 0 ? 'red.200' : 'green.200'}
          >
            <HStack justify="space-between" align="center">
              <HStack spacing={3}>
                <Icon
                  as={incidents > 0 ? FaExclamationTriangle : FaCheckCircle}
                  color={incidents > 0 ? 'red.500' : 'green.500'}
                  boxSize={5}
                />
                <VStack align="start" spacing={0}>
                  <Text fontSize="sm" fontWeight="bold" color={textColor}>
                    {incidents > 0 ? 'Aktif Olaylar Var' : 'Tüm Sistemler Normal'}
                  </Text>
                  <Text fontSize="xs" color={mutedColor}>
                    {incidents > 0 ? 
                      `${incidents} olay izleniyor` : 
                      'Herhangi bir sorun tespit edilmedi'
                    }
                  </Text>
                </VStack>
              </HStack>

              <Badge
                colorScheme={incidents > 0 ? 'red' : 'green'}
                variant="solid"
                borderRadius="full"
                px={3}
                py={1}
              >
                {incidents > 0 ? `${incidents} Olay` : 'Sorunsuz'}
              </Badge>
            </HStack>
          </Box>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default RealTimeUptimeMonitor;
