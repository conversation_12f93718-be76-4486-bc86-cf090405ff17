import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Button,
  ButtonGroup,
  Checkbox,
  CheckboxGroup,
  Stack,
  RangeSlider,
  RangeSliderTrack,
  RangeSliderFilledTrack,
  RangeSliderThumb,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  FormControl,
  FormLabel,
  Badge,
  Wrap,
  WrapItem,
  IconButton,
  Collapse,
  useDisclosure,
  useColorModeValue,
  Divider,

  Flex
} from '@chakra-ui/react';
import { SearchIcon, ChevronDownIcon, ChevronUpIcon, CloseIcon } from '@chakra-ui/icons';
import { SymbolAnalysisFilters as FilterType, SymbolMetrics } from '../../../types/symbolAnalysis';

interface SymbolAnalysisFiltersProps {
  data: SymbolMetrics[];
  filters: FilterType;
  onFiltersChange: (filters: Partial<FilterType>) => void;
  onReset?: () => void;
  compact?: boolean;
}

const SymbolAnalysisFilters: React.FC<SymbolAnalysisFiltersProps> = ({
  data,
  filters,
  onFiltersChange,
  onReset,
  compact = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const { isOpen: isAdvancedOpen, onToggle: toggleAdvanced } = useDisclosure({ defaultIsOpen: !compact });

  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');

  // Calculate data ranges for sliders
  const dataRanges = useMemo(() => {
    if (!data || data.length === 0) {
      return {
        pnl: { min: -1000, max: 1000 },
        roi: { min: -100, max: 100 },
        trades: { min: 1, max: 100 },
        winRate: { min: 0, max: 100 },
        volatility: { min: 0, max: 100 },
        sharpe: { min: -3, max: 3 }
      };
    }

    const pnlValues = data.map(d => d.totalPnl);
    const roiValues = data.map(d => d.roi);
    const tradeValues = data.map(d => d.totalTrades);
    const winRateValues = data.map(d => d.winRate);
    const volatilityValues = data.map(d => d.volatility);
    const sharpeValues = data.map(d => d.sharpeRatio);

    return {
      pnl: { min: Math.min(...pnlValues), max: Math.max(...pnlValues) },
      roi: { min: Math.min(...roiValues), max: Math.max(...roiValues) },
      trades: { min: Math.min(...tradeValues), max: Math.max(...tradeValues) },
      winRate: { min: Math.min(...winRateValues), max: Math.max(...winRateValues) },
      volatility: { min: Math.min(...volatilityValues), max: Math.max(...volatilityValues) },
      sharpe: { min: Math.min(...sharpeValues), max: Math.max(...sharpeValues) }
    };
  }, [data]);

  // Handle search
  useEffect(() => {
    const filteredSymbols = data
      .filter(symbol => 
        symbol.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .map(symbol => symbol.symbol);

    if (searchTerm) {
      onFiltersChange({ symbols: filteredSymbols });
    } else if (filters.symbols.length > 0 && searchTerm === '') {
      onFiltersChange({ symbols: [] });
    }
  }, [searchTerm, data, onFiltersChange]);

  // Get unique symbols for selection
  const availableSymbols = useMemo(() => {
    return data.map(d => d.symbol).sort();
  }, [data]);

  // Handle range changes
  const handlePnlRangeChange = (values: number[]) => {
    onFiltersChange({
      performanceRange: {
        ...filters.performanceRange,
        minPnl: values[0],
        maxPnl: values[1]
      }
    });
  };

  const handleRoiRangeChange = (values: number[]) => {
    onFiltersChange({
      performanceRange: {
        ...filters.performanceRange,
        minROI: values[0],
        maxROI: values[1]
      }
    });
  };

  const handleTradeCountRangeChange = (values: number[]) => {
    onFiltersChange({
      tradeCountRange: {
        min: values[0],
        max: values[1]
      }
    });
  };

  // Handle date range change
  const handleDateRangeChange = (field: 'start' | 'end', value: string) => {
    onFiltersChange({
      dateRange: {
        ...filters.dateRange,
        [field]: value
      }
    });
  };

  // Handle symbol selection
  const handleSymbolSelection = (symbols: string[]) => {
    onFiltersChange({ symbols });
  };

  // Handle sort change
  const handleSortChange = (sortBy: FilterType['sortBy'], sortOrder: FilterType['sortOrder']) => {
    onFiltersChange({ sortBy, sortOrder });
  };

  // Reset all filters
  const handleReset = () => {
    setSearchTerm('');
    onReset?.();
  };

  // Count active filters
  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (filters.symbols.length > 0) count++;
    if (filters.performanceRange.minPnl !== undefined || filters.performanceRange.maxPnl !== undefined) count++;
    if (filters.performanceRange.minROI !== undefined || filters.performanceRange.maxROI !== undefined) count++;
    if (filters.tradeCountRange.min !== undefined || filters.tradeCountRange.max !== undefined) count++;
    if (filters.riskLevel !== 'all') count++;
    if (filters.showOnlyProfitable) count++;
    if (filters.minTradeCount > 1) count++;
    return count;
  }, [filters]);

  return (
    <Box
      p={4}
      bg={bgColor}
      borderRadius="md"
      border="1px solid"
      borderColor={borderColor}
    >
      <VStack spacing={4} align="stretch">
        {/* Header */}
        <HStack justify="space-between">
          <HStack>
            <Text fontSize="lg" fontWeight="bold" color={textColor}>
              Filtreler
            </Text>
            {activeFiltersCount > 0 && (
              <Badge colorScheme="blue" variant="solid">
                {activeFiltersCount} aktif
              </Badge>
            )}
          </HStack>
          <HStack>
            {activeFiltersCount > 0 && (
              <Button size="sm" variant="ghost" onClick={handleReset}>
                Temizle
              </Button>
            )}
            {compact && (
              <IconButton
                aria-label="Toggle advanced filters"
                icon={isAdvancedOpen ? <ChevronUpIcon /> : <ChevronDownIcon />}
                size="sm"
                variant="ghost"
                onClick={toggleAdvanced}
              />
            )}
          </HStack>
        </HStack>

        {/* Search */}
        <FormControl>
          <FormLabel fontSize="sm">Sembol Arama</FormLabel>
          <InputGroup>
            <InputLeftElement pointerEvents="none">
              <SearchIcon color="gray.300" />
            </InputLeftElement>
            <Input
              placeholder="Sembol adı ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size="sm"
            />
          </InputGroup>
        </FormControl>

        {/* Quick Filters */}
        <VStack spacing={3} align="stretch">
          <Flex
            direction={{ base: "column", md: "row" }}
            wrap="wrap"
            gap={3}
            align={{ base: "stretch", md: "center" }}
          >
            <Checkbox
              isChecked={filters.showOnlyProfitable}
              onChange={(e) => onFiltersChange({ showOnlyProfitable: e.target.checked })}
              size="sm"
            >
              Sadece Karlı
            </Checkbox>

            <HStack spacing={2} w={{ base: "full", md: "auto" }}>
              <Text fontSize="sm" color={textColor} minW="fit-content">Risk:</Text>
              <Select
                value={filters.riskLevel}
                onChange={(e) => onFiltersChange({ riskLevel: e.target.value as FilterType['riskLevel'] })}
                size="sm"
                w={{ base: "full", md: "120px" }}
              >
                <option value="all">Tüm Risk</option>
                <option value="low">Düşük Risk</option>
                <option value="medium">Orta Risk</option>
                <option value="high">Yüksek Risk</option>
              </Select>
            </HStack>

            <VStack spacing={2} align="stretch" w={{ base: "full", md: "auto" }}>
              <Text fontSize="sm" color={textColor}>Sıralama:</Text>
              <HStack spacing={2}>
                <Select
                  value={filters.sortBy}
                  onChange={(e) => handleSortChange(e.target.value as FilterType['sortBy'], filters.sortOrder)}
                  size="sm"
                  w={{ base: "full", md: "120px" }}
                >
                  <option value="pnl">P&L</option>
                  <option value="roi">ROI</option>
                  <option value="winRate">Kazanma Oranı</option>
                  <option value="trades">İşlem Sayısı</option>
                  <option value="volatility">Volatilite</option>
                  <option value="sharpe">Sharpe</option>
                </Select>
                <ButtonGroup size="sm" isAttached>
                  <Button
                    variant={filters.sortOrder === 'desc' ? 'solid' : 'outline'}
                    onClick={() => handleSortChange(filters.sortBy, 'desc')}
                    fontSize={{ base: "xs", md: "sm" }}
                  >
                    ↓
                  </Button>
                  <Button
                    variant={filters.sortOrder === 'asc' ? 'solid' : 'outline'}
                    onClick={() => handleSortChange(filters.sortBy, 'asc')}
                    fontSize={{ base: "xs", md: "sm" }}
                  >
                    ↑
                  </Button>
                </ButtonGroup>
              </HStack>
            </VStack>
          </Flex>
        </VStack>

        {/* Advanced Filters */}
        <Collapse in={isAdvancedOpen}>
          <VStack spacing={4} align="stretch">
            <Divider />

            {/* Date Range */}
            <FormControl>
              <FormLabel fontSize="sm">Tarih Aralığı</FormLabel>
              <HStack>
                <Input
                  type="date"
                  value={filters.dateRange.start}
                  onChange={(e) => handleDateRangeChange('start', e.target.value)}
                  size="sm"
                />
                <Text fontSize="sm" color={textColor}>-</Text>
                <Input
                  type="date"
                  value={filters.dateRange.end}
                  onChange={(e) => handleDateRangeChange('end', e.target.value)}
                  size="sm"
                />
              </HStack>
            </FormControl>

            {/* Performance Range */}
            <FormControl>
              <FormLabel fontSize="sm">
                P&L Aralığı: ₺{filters.performanceRange.minPnl?.toFixed(0) || dataRanges.pnl.min.toFixed(0)} - 
                ₺{filters.performanceRange.maxPnl?.toFixed(0) || dataRanges.pnl.max.toFixed(0)}
              </FormLabel>
              <RangeSlider
                min={dataRanges.pnl.min}
                max={dataRanges.pnl.max}
                step={Math.abs(dataRanges.pnl.max - dataRanges.pnl.min) / 100}
                defaultValue={[
                  filters.performanceRange.minPnl || dataRanges.pnl.min,
                  filters.performanceRange.maxPnl || dataRanges.pnl.max
                ]}
                onChangeEnd={handlePnlRangeChange}
              >
                <RangeSliderTrack>
                  <RangeSliderFilledTrack />
                </RangeSliderTrack>
                <RangeSliderThumb index={0} />
                <RangeSliderThumb index={1} />
              </RangeSlider>
            </FormControl>

            {/* ROI Range */}
            <FormControl>
              <FormLabel fontSize="sm">
                ROI Aralığı: {filters.performanceRange.minROI?.toFixed(1) || dataRanges.roi.min.toFixed(1)}% - 
                {filters.performanceRange.maxROI?.toFixed(1) || dataRanges.roi.max.toFixed(1)}%
              </FormLabel>
              <RangeSlider
                min={dataRanges.roi.min}
                max={dataRanges.roi.max}
                step={0.1}
                defaultValue={[
                  filters.performanceRange.minROI || dataRanges.roi.min,
                  filters.performanceRange.maxROI || dataRanges.roi.max
                ]}
                onChangeEnd={handleRoiRangeChange}
              >
                <RangeSliderTrack>
                  <RangeSliderFilledTrack />
                </RangeSliderTrack>
                <RangeSliderThumb index={0} />
                <RangeSliderThumb index={1} />
              </RangeSlider>
            </FormControl>

            {/* Trade Count Range */}
            <FormControl>
              <FormLabel fontSize="sm">
                İşlem Sayısı: {filters.tradeCountRange.min || dataRanges.trades.min} - 
                {filters.tradeCountRange.max || dataRanges.trades.max}
              </FormLabel>
              <RangeSlider
                min={dataRanges.trades.min}
                max={dataRanges.trades.max}
                step={1}
                defaultValue={[
                  filters.tradeCountRange.min || dataRanges.trades.min,
                  filters.tradeCountRange.max || dataRanges.trades.max
                ]}
                onChangeEnd={handleTradeCountRangeChange}
              >
                <RangeSliderTrack>
                  <RangeSliderFilledTrack />
                </RangeSliderTrack>
                <RangeSliderThumb index={0} />
                <RangeSliderThumb index={1} />
              </RangeSlider>
            </FormControl>

            {/* Minimum Trade Count */}
            <FormControl>
              <FormLabel fontSize="sm">Minimum İşlem Sayısı</FormLabel>
              <NumberInput
                value={filters.minTradeCount}
                onChange={(_, value) => onFiltersChange({ minTradeCount: value || 1 })}
                min={1}
                max={dataRanges.trades.max}
                size="sm"
                w="120px"
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>

            {/* Symbol Selection */}
            {availableSymbols.length > 0 && (
              <FormControl>
                <FormLabel fontSize="sm">Sembol Seçimi ({filters.symbols.length} seçili)</FormLabel>
                <Box maxH="150px" overflowY="auto" border="1px solid" borderColor={borderColor} borderRadius="md" p={2}>
                  <CheckboxGroup
                    value={filters.symbols}
                    onChange={handleSymbolSelection}
                  >
                    <Stack spacing={1}>
                      {availableSymbols.map(symbol => (
                        <Checkbox key={symbol} value={symbol} size="sm">
                          {symbol}
                        </Checkbox>
                      ))}
                    </Stack>
                  </CheckboxGroup>
                </Box>
              </FormControl>
            )}
          </VStack>
        </Collapse>

        {/* Active Filters Display */}
        {activeFiltersCount > 0 && (
          <Box>
            <Text fontSize="sm" fontWeight="medium" mb={2} color={textColor}>
              Aktif Filtreler:
            </Text>
            <Wrap>
              {filters.symbols.length > 0 && (
                <WrapItem>
                  <Badge variant="outline" colorScheme="blue">
                    {filters.symbols.length} sembol seçili
                    <IconButton
                      aria-label="Remove symbol filter"
                      icon={<CloseIcon />}
                      size="xs"
                      variant="ghost"
                      ml={1}
                      onClick={() => onFiltersChange({ symbols: [] })}
                    />
                  </Badge>
                </WrapItem>
              )}
              {filters.showOnlyProfitable && (
                <WrapItem>
                  <Badge variant="outline" colorScheme="green">
                    Sadece karlı
                    <IconButton
                      aria-label="Remove profitable filter"
                      icon={<CloseIcon />}
                      size="xs"
                      variant="ghost"
                      ml={1}
                      onClick={() => onFiltersChange({ showOnlyProfitable: false })}
                    />
                  </Badge>
                </WrapItem>
              )}
              {filters.riskLevel !== 'all' && (
                <WrapItem>
                  <Badge variant="outline" colorScheme="orange">
                    {filters.riskLevel} risk
                    <IconButton
                      aria-label="Remove risk filter"
                      icon={<CloseIcon />}
                      size="xs"
                      variant="ghost"
                      ml={1}
                      onClick={() => onFiltersChange({ riskLevel: 'all' })}
                    />
                  </Badge>
                </WrapItem>
              )}
            </Wrap>
          </Box>
        )}
      </VStack>
    </Box>
  );
};

export default SymbolAnalysisFilters;
