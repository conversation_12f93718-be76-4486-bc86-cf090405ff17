import { createContext, ReactNode, useState, useContext, useCallback, useEffect, useRef } from 'react';
import { useBreakpointValue, useDisclosure } from '@chakra-ui/react';
import { useLocation } from 'react-router-dom';

// Sidebar genişlik sabitleri - responsive values
const SIDEBAR_WIDTH_EXPANDED = "285px";
const SIDEBAR_WIDTH_COLLAPSED = "64px"; // Slightly smaller for better mobile compatibility

// Sidebar durumları
export type SidebarState = 'expanded' | 'collapsed' | 'collapsed-with-submenu';

// Submenu item interface
export interface SubmenuItem {
  id: string;
  name: string;
  path: string;
  icon?: React.ReactElement;
  description?: string;
}

interface SidebarContextType {
  // Sidebar'ın görünür olma durumu (mobil için)
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  onToggle: () => void;

  // Yeni collapsible sidebar durumları
  sidebarState: SidebarState;
  isCollapsed: boolean;
  isExpanded: boolean;
  hasActiveSubmenu: boolean;

  // Sidebar state yönetimi
  expandSidebar: () => void;
  collapseSidebar: () => void;
  toggleSidebarState: () => void;
  collapseOnNavigation: () => void;

  // Submenu yönetimi
  activeSubmenu: string | null;
  submenuItems: SubmenuItem[];
  openSubmenu: (menuId: string, items: SubmenuItem[]) => void;
  closeSubmenu: () => void;

  // Mouse interaction tracking
  isMouseNearLeftEdge: boolean;
  mouseX: number;
  isSidebarHovered: boolean;
  setSidebarHovered: (hovered: boolean) => void;

  // Responsive durumlar
  isMobile: boolean;
  sidebarWidth: string;
  currentSidebarWidth: string;

  // Eski methodlar (geriye uyumluluk için)
  toggleSidebar: boolean;
  setToggleSidebar: (value: boolean) => void;
}

export const SidebarContext = createContext<SidebarContextType>({
  isOpen: false,
  onOpen: () => {},
  onClose: () => {},
  onToggle: () => {},

  sidebarState: 'collapsed',
  isCollapsed: true,
  isExpanded: false,
  hasActiveSubmenu: false,

  expandSidebar: () => {},
  collapseSidebar: () => {},
  toggleSidebarState: () => {},
  collapseOnNavigation: () => {},

  activeSubmenu: null,
  submenuItems: [],
  openSubmenu: () => {},
  closeSubmenu: () => {},

  isMouseNearLeftEdge: false,
  mouseX: 0,
  isSidebarHovered: false,
  setSidebarHovered: () => {},

  isMobile: false,
  sidebarWidth: SIDEBAR_WIDTH_COLLAPSED,
  currentSidebarWidth: SIDEBAR_WIDTH_COLLAPSED,
  toggleSidebar: false,
  setToggleSidebar: () => {},
});

interface SidebarProviderProps {
  children: ReactNode;
}

export const SidebarProvider = ({ children }: SidebarProviderProps) => {
  // Chakra UI disclosure hook'unu kullanarak sidebar drawer'ını yönetelim (mobil için)
  const { isOpen, onOpen: originalOnOpen, onClose: originalOnClose, onToggle } = useDisclosure();

  // Router location hook'u ile route değişikliklerini takip ediyoruz
  const location = useLocation();

  // Yeni sidebar state management - Start in collapsed mode by default
  const [sidebarState, setSidebarState] = useState<SidebarState>('collapsed');
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [submenuItems, setSubmenuItems] = useState<SubmenuItem[]>([]);
  const [mouseX, setMouseX] = useState(0);
  const [isMouseNearLeftEdge, setIsMouseNearLeftEdge] = useState(false);
  const [isSidebarHovered, setIsSidebarHovered] = useState(false);

  // Eski state ve metodlar için (geriye uyumluluk)
  const [toggleSidebar, setToggleSidebar] = useState(false);

  // Önceki isMobile değerini takip etmek için useRef kullanıyoruz
  const prevIsMobileRef = useRef<boolean | null>(null);

  // Timeout refs for manual override capability
  const expandTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const collapseTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Enhanced responsive breakpoints - tablet and below are considered mobile
  const isMobile = useBreakpointValue({ base: true, lg: false }) || false;
  
  // Computed values
  const isCollapsed = sidebarState === 'collapsed' || sidebarState === 'collapsed-with-submenu';
  const isExpanded = sidebarState === 'expanded';
  const hasActiveSubmenu = sidebarState === 'collapsed-with-submenu' && activeSubmenu !== null;
  const currentSidebarWidth = isCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH_EXPANDED;

  // Helper function to clear pending timeouts (for manual override)
  const clearPendingTimeouts = useCallback(() => {
    if (expandTimeoutRef.current) {
      clearTimeout(expandTimeoutRef.current);
      expandTimeoutRef.current = null;
    }
    if (collapseTimeoutRef.current) {
      clearTimeout(collapseTimeoutRef.current);
      collapseTimeoutRef.current = null;
    }
  }, []);

  // Sidebar state management functions
  const expandSidebar = useCallback(() => {
    if (!isMobile) {
      clearPendingTimeouts(); // Clear any pending auto-actions
      setSidebarState('expanded');
      setActiveSubmenu(null);
      setSubmenuItems([]);
    }
  }, [isMobile, clearPendingTimeouts]);

  const collapseSidebar = useCallback(() => {
    if (!isMobile) {
      clearPendingTimeouts(); // Clear any pending auto-actions
      setSidebarState('collapsed');
      setActiveSubmenu(null);
      setSubmenuItems([]);
    }
  }, [isMobile, clearPendingTimeouts]);

  // Auto-collapse on navigation - called when user clicks navigation links
  const collapseOnNavigation = useCallback(() => {
    if (!isMobile) {
      clearPendingTimeouts(); // Clear any pending auto-actions
      setSidebarState('collapsed');
      setActiveSubmenu(null);
      setSubmenuItems([]);
      console.log('[SidebarContext] Auto-collapsing sidebar on navigation');
    }
  }, [isMobile, clearPendingTimeouts]);

  const toggleSidebarState = useCallback(() => {
    if (!isMobile) {
      clearPendingTimeouts(); // Clear any pending auto-actions
      if (isExpanded) {
        collapseSidebar();
      } else {
        expandSidebar();
      }
    }
  }, [isMobile, isExpanded, collapseSidebar, expandSidebar, clearPendingTimeouts]);

  // Submenu management functions
  const openSubmenu = useCallback((menuId: string, items: SubmenuItem[]) => {
    if (!isMobile) {
      setSidebarState('collapsed-with-submenu');
      setActiveSubmenu(menuId);
      setSubmenuItems(items);
    }
  }, [isMobile]);

  const closeSubmenu = useCallback(() => {
    if (!isMobile) {
      setSidebarState('collapsed');
      setActiveSubmenu(null);
      setSubmenuItems([]);
    }
  }, [isMobile]);

  // Debug için özel open/close metodları (mobil için)
  const onOpen = useCallback(() => {
    console.log('[SidebarContext] onOpen çağrıldı');
    originalOnOpen();
  }, [originalOnOpen]);

  const onClose = useCallback(() => {
    console.log('[SidebarContext] onClose çağrıldı');
    originalOnClose();
  }, [originalOnClose]);
  
  // Specific sidebar navigation behavior implementation
  useEffect(() => {
    if (isMobile) return;

    const handleMouseMove = (event: MouseEvent) => {
      const x = event.clientX;
      const y = event.clientY;

      // Update mouse position
      setMouseX(x);

      // Clear existing timeouts to prevent conflicts
      if (expandTimeoutRef.current) {
        clearTimeout(expandTimeoutRef.current);
        expandTimeoutRef.current = null;
      }
      if (collapseTimeoutRef.current) {
        clearTimeout(collapseTimeoutRef.current);
        collapseTimeoutRef.current = null;
      }

      // Constants for behavior specification
      const NAVBAR_HEIGHT = 64; // Account for navbar height
      const LEFT_EDGE_THRESHOLD = 55; // 50-60px from left edge as specified
      const SIDEBAR_WIDTH_PX = 285; // Expanded sidebar width in pixels
      const BUFFER_ZONE = 65; // 50-80px buffer zone as specified
      const EXPAND_DELAY = 125; // 100-150ms delay as specified
      const COLLAPSE_DELAY = 900; // 800-1000ms delay as specified

      // Left edge detection for auto-expand
      const isNearLeftEdge = x <= LEFT_EDGE_THRESHOLD && y > NAVBAR_HEIGHT && y < window.innerHeight - 50;
      setIsMouseNearLeftEdge(isNearLeftEdge);

      // Mouse hover auto-expand behavior
      if (isNearLeftEdge && isCollapsed && !hasActiveSubmenu) {
        expandTimeoutRef.current = setTimeout(() => {
          // Verify conditions are still valid before expanding
          if (isCollapsed && !hasActiveSubmenu) {
            console.log('[SidebarContext] Auto-expanding sidebar - mouse near left edge');
            expandSidebar();
          }
        }, EXPAND_DELAY);
      }

      // Enhanced mouse leave auto-collapse behavior
      const SUBMENU_WIDTH_PX = 260; // Submenu panel width
      const totalSidebarWidth = hasActiveSubmenu ? SIDEBAR_WIDTH_PX + SUBMENU_WIDTH_PX : SIDEBAR_WIDTH_PX;

      // Enhanced boundary detection with better edge case handling
      const isAwayFromSidebar = x > totalSidebarWidth + BUFFER_ZONE;
      const isCompletelyOutside = x > totalSidebarWidth + BUFFER_ZONE &&
                                  (y < NAVBAR_HEIGHT || y > window.innerHeight - 50);

      // Only trigger auto-collapse if mouse is clearly outside sidebar boundaries
      // Enhanced conditions for more reliable behavior
      if ((isAwayFromSidebar || isCompletelyOutside) &&
          isExpanded &&
          !hasActiveSubmenu &&
          !isSidebarHovered) {
        collapseTimeoutRef.current = setTimeout(() => {
          // Double-check conditions are still valid before collapsing
          const currentMouseX = event.clientX;
          const currentTotalWidth = hasActiveSubmenu ? SIDEBAR_WIDTH_PX + SUBMENU_WIDTH_PX : SIDEBAR_WIDTH_PX;
          const stillAwayFromSidebar = currentMouseX > currentTotalWidth + BUFFER_ZONE;

          if (isExpanded &&
              !hasActiveSubmenu &&
              !isSidebarHovered &&
              stillAwayFromSidebar) {
            console.log('[SidebarContext] Auto-collapsing sidebar - mouse away from sidebar');
            collapseSidebar();
          }
        }, COLLAPSE_DELAY);
      }
    };

    const handleMouseLeave = () => {
      setIsMouseNearLeftEdge(false);

      // Enhanced mouse leave behavior - auto-collapse when mouse leaves window
      const COLLAPSE_DELAY = 900; // 800-1000ms delay as specified

      if (isExpanded && !hasActiveSubmenu && !isSidebarHovered) {
        collapseTimeoutRef.current = setTimeout(() => {
          // Verify conditions are still valid before collapsing
          if (isExpanded && !hasActiveSubmenu && !isSidebarHovered) {
            console.log('[SidebarContext] Auto-collapsing sidebar - mouse left window');
            collapseSidebar();
          }
        }, COLLAPSE_DELAY);
      } else {
        // Clear all timeouts when mouse leaves the window
        clearPendingTimeouts();
      }
    };

    // Add event listeners with proper options
    document.addEventListener('mousemove', handleMouseMove, { passive: true });
    document.addEventListener('mouseleave', handleMouseLeave, { passive: true });

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseleave', handleMouseLeave);

      // Clear timeouts on cleanup
      clearPendingTimeouts();
    };
  }, [isMobile, isCollapsed, isExpanded, hasActiveSubmenu, isSidebarHovered, expandSidebar, collapseSidebar, clearPendingTimeouts]);

  // Keyboard navigation support
  useEffect(() => {
    if (isMobile) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Toggle sidebar with Ctrl/Cmd + B
      if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
        event.preventDefault();
        toggleSidebarState();
      }

      // Close submenu with Escape
      if (event.key === 'Escape' && hasActiveSubmenu) {
        event.preventDefault();
        closeSubmenu();
      }

      // Expand sidebar with Ctrl/Cmd + Shift + E
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'E') {
        event.preventDefault();
        expandSidebar();
      }

      // Collapse sidebar with Ctrl/Cmd + Shift + C
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        collapseSidebar();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isMobile, hasActiveSubmenu, toggleSidebarState, closeSubmenu, expandSidebar, collapseSidebar]);

  // Debug için isMobile değişikliklerini izleyelim
  useEffect(() => {
    const prevIsMobile = prevIsMobileRef.current;
    console.log(`[SidebarContext] isMobile değişti: ${prevIsMobile} -> ${isMobile}`);

    // Masaüstünden mobile geçiş yapıldığında sidebar state'ini sıfırla
    if (prevIsMobile === false && isMobile === true) {
      setSidebarState('expanded');
      setActiveSubmenu(null);
      setSubmenuItems([]);
      if (isOpen) {
        console.log('[SidebarContext] Masaüstünden mobile geçiş yapıldı, drawer kapatılıyor');
        originalOnClose();
      }
    }

    // Mobile'dan masaüstüne geçiş yapıldığında drawer'ı kapat
    if (prevIsMobile === true && isMobile === false && isOpen) {
      originalOnClose();
    }

    // Önceki değeri güncelle
    prevIsMobileRef.current = isMobile;
  }, [isMobile, isOpen, originalOnClose]);
  
  // Route değişikliği sonrası mobile'da sidebar'ı kapat ve submenu'ları temizle
  useEffect(() => {
    if (isMobile && isOpen) {
      console.log('[SidebarContext] Route değişti ve mobile görünümde, sidebar kapatılıyor');
      originalOnClose();
    }

    // Route değişikliğinde submenu'ları temizle (hem mobile hem desktop)
    if (activeSubmenu) {
      closeSubmenu();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname, isMobile]);

  // Eski toggleSidebar state'ini isOpen ile senkronize edelim (geriye uyumluluk)
  useEffect(() => {
    console.log(`[SidebarContext] isOpen değişti: ${isOpen}, toggleSidebar güncelleniyor`);
    setToggleSidebar(isOpen);
  }, [isOpen]);

  return (
    <SidebarContext.Provider
      value={{
        // Mobil drawer state
        isOpen,
        onOpen,
        onClose,
        onToggle,

        // Collapsible sidebar state
        sidebarState,
        isCollapsed,
        isExpanded,
        hasActiveSubmenu,

        // Sidebar state management
        expandSidebar,
        collapseSidebar,
        toggleSidebarState,
        collapseOnNavigation,

        // Submenu management
        activeSubmenu,
        submenuItems,
        openSubmenu,
        closeSubmenu,

        // Mouse interaction
        isMouseNearLeftEdge,
        mouseX,
        isSidebarHovered,
        setSidebarHovered: setIsSidebarHovered,

        // Responsive & legacy
        isMobile,
        sidebarWidth: SIDEBAR_WIDTH_EXPANDED,
        currentSidebarWidth,
        toggleSidebar,
        setToggleSidebar,
      }}
    >
      {children}
    </SidebarContext.Provider>
  );
};

// Sidebar context hook
export const useSidebar = () => {
  return useContext(SidebarContext);
};

export default SidebarContext; 