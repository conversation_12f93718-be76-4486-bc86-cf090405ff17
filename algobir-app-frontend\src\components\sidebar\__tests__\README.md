# Sidebar Testing Suite

This directory contains comprehensive tests for the modern collapsible sidebar navigation system.

## Test Structure

### Unit Tests
- **SidebarContext.test.tsx**: Tests for the sidebar state management context
- **Sidebar.test.tsx**: Tests for the main sidebar component
- **SubmenuPanel.test.tsx**: Tests for the submenu panel component
- **SidebarAccessibility.test.tsx**: Comprehensive accessibility tests

### Integration Tests
- **SidebarIntegration.test.tsx**: Tests for complete sidebar workflows and interactions

### End-to-End Tests
- **sidebar.e2e.spec.ts**: Playwright tests for real browser interactions

### Test Utilities
- **setup.ts**: Test configuration and utilities

## Running Tests

### Unit and Integration Tests
```bash
# Run all sidebar tests
npm test -- --testPathPattern=sidebar

# Run specific test file
npm test SidebarContext.test.tsx

# Run tests in watch mode
npm test -- --watch --testPathPattern=sidebar

# Run tests with coverage
npm test -- --coverage --testPathPattern=sidebar
```

### End-to-End Tests
```bash
# Run Playwright tests
npx playwright test sidebar.e2e.spec.ts

# Run with UI mode
npx playwright test --ui sidebar.e2e.spec.ts

# Run on specific browser
npx playwright test --project=chromium sidebar.e2e.spec.ts
```

### Accessibility Tests
```bash
# Run accessibility-specific tests
npm test SidebarAccessibility.test.tsx

# Run with axe-core integration
npm test -- --testNamePattern="accessibility"
```

## Test Coverage

### Components Tested
- ✅ SidebarContext (state management)
- ✅ Sidebar (main component)
- ✅ SidebarContent (expanded content)
- ✅ SidebarContentCollapsed (collapsed content)
- ✅ SubmenuPanel (submenu functionality)
- ✅ SidebarLinks (navigation links)
- ✅ SidebarResponsive (mobile menu)

### Features Tested
- ✅ Sidebar expand/collapse functionality
- ✅ Submenu open/close interactions
- ✅ Mouse edge detection and auto-expand
- ✅ Keyboard navigation and shortcuts
- ✅ Responsive design (mobile/tablet/desktop)
- ✅ Accessibility compliance (WCAG 2.1)
- ✅ Animation and transition smoothness
- ✅ Focus management
- ✅ ARIA attributes and screen reader support

### Browser Compatibility (E2E)
- ✅ Chrome/Chromium
- ✅ Firefox
- ✅ Safari/WebKit
- ✅ Mobile browsers

### Responsive Breakpoints Tested
- ✅ Mobile (< 768px)
- ✅ Tablet (768px - 991px)
- ✅ Desktop (≥ 992px)

## Test Scenarios

### Core Functionality
1. **Sidebar State Management**
   - Default expanded state
   - Toggle between expanded/collapsed
   - Submenu state transitions
   - State persistence

2. **Mouse Interactions**
   - Left edge detection and auto-expand
   - Hover states and visual feedback
   - Click interactions for menu items
   - Submenu opening/closing

3. **Keyboard Navigation**
   - Tab navigation through menu items
   - Keyboard shortcuts (Ctrl+B, Ctrl+Shift+E, Ctrl+Shift+C)
   - Escape key to close submenus
   - Focus management

4. **Responsive Behavior**
   - Mobile drawer functionality
   - Tablet responsive design
   - Desktop collapsible behavior
   - Viewport size changes

### Accessibility Testing
1. **ARIA Compliance**
   - Proper role attributes
   - Aria-expanded states
   - Aria-label descriptions
   - Aria-describedby relationships

2. **Keyboard Accessibility**
   - Full keyboard navigation
   - Focus indicators
   - Focus trapping in modals
   - Logical tab order

3. **Screen Reader Support**
   - Meaningful announcements
   - State change notifications
   - Descriptive labels
   - Context information

### Performance Testing
1. **Animation Performance**
   - Smooth transitions
   - Hardware acceleration
   - Reduced motion support
   - Frame rate consistency

2. **Memory Management**
   - Event listener cleanup
   - Component unmounting
   - State cleanup
   - Memory leak prevention

## Mock Strategy

### External Dependencies
- **React Router**: Mocked for consistent navigation testing
- **Chakra UI**: Partial mocks for responsive hooks
- **Custom Scrollbars**: Mocked for simplified testing
- **Admin Auth**: Mocked for permission testing

### Browser APIs
- **matchMedia**: Mocked for responsive testing
- **IntersectionObserver**: Mocked for visibility testing
- **ResizeObserver**: Mocked for size change testing
- **requestAnimationFrame**: Mocked for animation testing

## Continuous Integration

### Test Pipeline
1. **Lint and Type Check**: ESLint and TypeScript validation
2. **Unit Tests**: Jest with React Testing Library
3. **Integration Tests**: Component interaction testing
4. **Accessibility Tests**: axe-core integration
5. **E2E Tests**: Playwright cross-browser testing
6. **Visual Regression**: Screenshot comparison (if configured)

### Coverage Requirements
- **Statements**: > 90%
- **Branches**: > 85%
- **Functions**: > 90%
- **Lines**: > 90%

## Debugging Tests

### Common Issues
1. **Timing Issues**: Use `waitFor` for async operations
2. **Animation Conflicts**: Use test utilities to control animations
3. **Responsive Mocking**: Ensure proper breakpoint mocking
4. **Event Cleanup**: Verify event listeners are properly cleaned up

### Debug Commands
```bash
# Run tests with debug output
npm test -- --verbose sidebar

# Run single test with debugging
npm test -- --testNamePattern="should collapse sidebar" --verbose

# Debug Playwright tests
npx playwright test --debug sidebar.e2e.spec.ts
```

## Contributing

When adding new sidebar features:
1. Add corresponding unit tests
2. Update integration tests if needed
3. Add E2E tests for user-facing features
4. Ensure accessibility compliance
5. Update this documentation

## Test Data

Test fixtures and mock data are defined within each test file to ensure test isolation and maintainability.
