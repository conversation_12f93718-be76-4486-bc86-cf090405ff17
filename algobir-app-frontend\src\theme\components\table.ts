import { mode } from '@chakra-ui/theme-tools';

export const TableComponent = {
  components: {
    Table: {
      baseStyle: (props: any) => ({
        th: {
          color: mode('secondaryGray.600', 'white')(props),
          fontSize: '10px',
          fontWeight: 'bold',
          letterSpacing: '1px',
          textTransform: 'uppercase',
          borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
        },
        td: {
          color: mode('secondaryGray.900', 'white')(props),
          borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          fontSize: 'sm',
        },
        table: {
          fontVariantNumeric: 'lining-nums tabular-nums',
        },
      }),
      variants: {
        simple: (props: any) => ({
          th: {
            color: mode('secondaryGray.600', 'white')(props),
            borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          },
          td: {
            color: mode('secondaryGray.900', 'white')(props),
            borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          },
        }),
        striped: (props: any) => ({
          th: {
            color: mode('secondaryGray.600', 'white')(props),
            borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          },
          td: {
            color: mode('secondaryGray.900', 'white')(props),
            borderColor: mode('secondaryGray.100', 'whiteAlpha.100')(props),
          },
          tbody: {
            tr: {
              '&:nth-of-type(odd)': {
                td: {
                  bg: mode('secondaryGray.50', 'whiteAlpha.50')(props),
                },
              },
            },
          },
        }),
      },
    },
  },
}; 