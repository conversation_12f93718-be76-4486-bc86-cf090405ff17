import React from 'react';
import { Box, Heading } from '@chakra-ui/react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { StatsData } from '../../hooks/useAdvancedStatisticsData';

interface Props { stats: StatsData }

const TradeAnalysisTab: React.FC<Props> = ({ stats }) => {
    const pnlBySymbolData = Object.entries(stats.pnlBySymbol).map(([symbol, pnl]) => ({ name: symbol, "K/Z": pnl }));

    return (
        <Box h="400px">
            <Heading size="md" mb={4}>Sembole Göre Kâr/Zarar</Heading>
            <ResponsiveContainer width="100%" height="100%">
                <BarChart data={pnlBySymbolData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value: number) => `₺${value.toFixed(2)}`} />
                    <Legend />
                    <Bar dataKey="K/Z" fill="#82ca9d" />
                </BarChart>
            </ResponsiveContainer>
        </Box>
    );
};

export default TradeAnalysisTab; 