---
type: "manual"
priority: "high"
scope: ["backend", "supabase", "edge-functions", "database", "api"]
last_updated: "2025-01-29"
---

# Backend Rules - Supabase Edge Functions & Database

## 🏗️ Supabase Architecture

### Project Configuration
```toml
# supabase/config.toml
project_id = "fllklckmycxcgwhboiji"
region = "eu-central-1"

[db]
major_version = 15
[db.pooler]
enabled = true
pool_mode = "transaction"
default_pool_size = 25
max_client_conn = 150

[auth]
enabled = true
jwt_expiry = 3600
enable_manual_linking = false

[functions."algobir-webhook-listener"]
verify_jwt = false  # Webhook endpoint

[functions."seller-signal-endpoint"]
verify_jwt = false  # Webhook endpoint

[functions."signal-relay-function"]
verify_jwt = false  # Internal function
```

## 🔧 Edge Functions Architecture

### 1. Solo-Robot Flow: algobir-webhook-listener
```typescript
// Purpose: Process TradingView signals for individual users
// Flow: TradingView → Webhook → Database → Order Execution

import { createClient } from 'jsr:@supabase/supabase-js@2';
import { getCorsHeaders } from '../_shared/cors.ts';
import { CacheManager } from '../_shared/cache-manager.ts';
import { MemoryOptimizer } from '../_shared/memory-optimizer.ts';

// Key Features:
- Signal parsing and validation
- User authentication via webhook_id
- Trade record creation
- Performance metrics collection
- Order transmission to broker
- Real-time notifications

// Performance Optimization:
- Memory optimization with garbage collection
- Caching for user settings
- Asynchronous processing
- Sub-second response times
```

### 2. Bro-Robot Flow: seller-signal-endpoint
```typescript
// Purpose: Receive signals from Bro-Robot sellers
// Flow: TradingView → seller-signal-endpoint → signal-relay-function

// Key Features:
- Rate limiting (30 requests/minute)
- Robot validation
- Signal relay to subscribers
- Error handling and logging

// Security:
- No JWT verification (webhook endpoint)
- Robot ID validation
- Rate limiting protection
```

### 3. Signal Distribution: signal-relay-function
```typescript
// Purpose: Distribute Bro-Robot signals to subscribers
// Flow: seller-signal-endpoint → signal-relay-function → Subscribers

// Key Features:
- Subscriber lookup and validation
- Bulk trade creation
- Individual order transmission
- Performance monitoring
- Error resilience

// Optimization:
- Batch processing for multiple subscribers
- Memory management
- Connection pooling
```

## 🗄️ Database Schema

### Core Tables
```sql
-- User Management
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  username TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE user_settings (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  webhook_id UUID UNIQUE,
  custom_webhook_url TEXT,
  encrypted_api_key TEXT,
  encrypted_token TEXT,
  is_superuser BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  total_investment_amount NUMERIC
);

-- Trading System
CREATE TABLE robots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  seller_id UUID REFERENCES profiles(id),
  performance_data JSONB,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  deleted_at TIMESTAMPTZ NULL
);

CREATE TABLE trades (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id UUID REFERENCES profiles(id),
  robot_id UUID REFERENCES robots(id),
  symbol TEXT NOT NULL,
  order_side TEXT CHECK (order_side IN ('BUY', 'SELL')),
  quantity NUMERIC NOT NULL,
  price NUMERIC NOT NULL,
  total_amount NUMERIC GENERATED ALWAYS AS (quantity * price) STORED,
  trade_category TEXT DEFAULT 'ALIM',
  position_status TEXT DEFAULT 'Açık',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES profiles(id),
  robot_id UUID REFERENCES robots(id),
  started_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT TRUE,
  is_deleted BOOLEAN DEFAULT FALSE
);
```

### Performance Monitoring
```sql
CREATE TABLE order_transmission_metrics (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  trade_id BIGINT REFERENCES trades(id),
  webhook_id UUID,
  robot_id UUID REFERENCES robots(id),
  signal_type TEXT,
  symbol TEXT,
  order_side TEXT,
  json_parsing_time_ms NUMERIC,
  transformation_time_ms NUMERIC,
  webhook_delivery_time_ms NUMERIC,
  total_processing_time_ms NUMERIC,
  signal_source TEXT,
  processing_status TEXT,
  error_details JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔒 Row Level Security (RLS)

### Security Policies
```sql
-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Trades access control
CREATE POLICY "Users can view own trades" ON trades
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own trades" ON trades
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admin access
CREATE POLICY "Admins can view all data" ON trades
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_settings 
      WHERE id = auth.uid() AND is_superuser = TRUE
    )
  );
```

## 📡 Real-time Subscriptions

### Notification System
```sql
-- RPC Function for notifications
CREATE OR REPLACE FUNCTION create_notification(
  p_user_id UUID,
  p_title TEXT,
  p_message TEXT,
  p_type TEXT,
  p_severity TEXT DEFAULT 'info',
  p_metadata JSONB DEFAULT '{}',
  p_action_url TEXT DEFAULT NULL,
  p_action_label TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  INSERT INTO notifications (
    user_id, title, message, type, severity, 
    metadata, action_url, action_label
  ) VALUES (
    p_user_id, p_title, p_message, p_type, p_severity,
    p_metadata, p_action_url, p_action_label
  ) RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Real-time Channels
```typescript
// Frontend subscription pattern
const subscription = supabase
  .channel('trades')
  .on('postgres_changes', 
    { event: 'INSERT', schema: 'public', table: 'trades' },
    (payload) => {
      if (payload.new.user_id === user?.id) {
        // Update UI with new trade
        setTrades(prev => [payload.new, ...prev]);
      }
    }
  )
  .subscribe();
```

## ⚡ Performance Optimization

### Caching Strategy
```typescript
// Cache Manager Implementation
export class CacheManager {
  private static userSettingsCache = new Map();
  private static cacheExpiry = 5 * 60 * 1000; // 5 minutes

  static async getUserSettingsByWebhookId(supabase: any, webhookId: string) {
    const cacheKey = `user_settings_${webhookId}`;
    const cached = this.userSettingsCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    
    const { data } = await supabase
      .from('user_settings')
      .select('*')
      .eq('webhook_id', webhookId)
      .eq('is_active', true)
      .single();
    
    this.userSettingsCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    return data;
  }
}
```

### Memory Management
```typescript
// Memory Optimizer
export class MemoryOptimizer {
  static releasePerformanceMetrics(metrics: any) {
    Object.keys(metrics).forEach(key => {
      delete metrics[key];
    });
  }
  
  static forceGcIfNeeded() {
    if (Math.random() < 0.1) { // 10% chance
      if (typeof Deno !== 'undefined' && Deno.core?.ops?.op_gc) {
        Deno.core.ops.op_gc();
      }
    }
  }
  
  static stringifyJsonOptimized(obj: any): string {
    return JSON.stringify(obj, null, 0); // No indentation for smaller size
  }
}
```

## 🔐 Security Implementation

### API Key Encryption
```typescript
// Vault Integration
import { upsertVaultSecret } from '../_shared/getVaultSecret.ts';

// Encrypt and store API keys
await upsertVaultSecret(supabase, `api_key_${userId}`, apiKey);
await upsertVaultSecret(supabase, `token_${userId}`, token);

// Update user settings with encryption flags
await supabase
  .from('user_settings')
  .update({
    api_key_set: true,
    token_set: true,
    encrypted_api_key: 'vault:encrypted',
    encrypted_token: 'vault:encrypted'
  })
  .eq('id', userId);
```

### Rate Limiting
```typescript
// Simple in-memory rate limiting
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 30;

function checkRateLimit(identifier: string): boolean {
  const now = Date.now();
  const windowStart = now - RATE_LIMIT_WINDOW;
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, []);
  }
  
  const requests = rateLimitMap.get(identifier);
  const validRequests = requests.filter((time: number) => time > windowStart);
  
  if (validRequests.length >= RATE_LIMIT_MAX_REQUESTS) {
    return false; // Rate limit exceeded
  }
  
  validRequests.push(now);
  rateLimitMap.set(identifier, validRequests);
  return true;
}
```

## 📊 Monitoring & Analytics

### Performance Metrics Collection
```typescript
// RPC Function for metrics
CREATE OR REPLACE FUNCTION insert_order_transmission_metrics(
  p_trade_id BIGINT,
  p_webhook_id UUID,
  p_robot_id UUID,
  p_signal_type TEXT,
  p_symbol TEXT,
  p_order_side TEXT,
  p_json_parsing_time_ms NUMERIC,
  p_transformation_time_ms NUMERIC,
  p_webhook_delivery_time_ms NUMERIC,
  p_total_processing_time_ms NUMERIC,
  p_signal_source TEXT,
  p_processing_status TEXT,
  p_error_details JSONB
) RETURNS VOID AS $$
BEGIN
  INSERT INTO order_transmission_metrics (
    trade_id, webhook_id, robot_id, signal_type, symbol, order_side,
    json_parsing_time_ms, transformation_time_ms, webhook_delivery_time_ms,
    total_processing_time_ms, signal_source, processing_status, error_details
  ) VALUES (
    p_trade_id, p_webhook_id, p_robot_id, p_signal_type, p_symbol, p_order_side,
    p_json_parsing_time_ms, p_transformation_time_ms, p_webhook_delivery_time_ms,
    p_total_processing_time_ms, p_signal_source, p_processing_status, p_error_details
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Update Requirements

### When to Update This File
- New Edge Functions added
- Database schema changes
- Security policy modifications
- Performance optimization implementations
- API endpoint changes
- Real-time subscription updates

### Related Files to Update
- Update `security/GUIDELINES.mdc` for security changes
- Update `performance/OPTIMIZATION.mdc` for performance updates
- Update `features/*.mdc` for feature-specific backend changes
- Update `core/PROJECT_CORE.mdc` for major architecture changes
