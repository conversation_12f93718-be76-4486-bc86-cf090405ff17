import { test, expect } from '@playwright/test';

test.describe('Enhanced Statistics Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the statistics page
    await page.goto('/statistics');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
  });

  test('should load the enhanced statistics page', async ({ page }) => {
    // Check if the main heading is visible
    await expect(page.locator('text=Trading Analizi')).toBeVisible();
    
    // Check if the navigation menu is present
    await expect(page.locator('text=<PERSON><PERSON><PERSON>')).toBeVisible();
    
    // Verify that key navigation items are present
    await expect(page.locator('text=Genel Bakış')).toBeVisible();
    await expect(page.locator('text=ROI <PERSON>lizi')).toBeVisible();
    await expect(page.locator('text=Robot Analizi')).toBeVisible();
  });

  test('should display statistics filters', async ({ page }) => {
    // Check if filter section is visible
    await expect(page.locator('text=Filtreler')).toBeVisible();
    
    // Check robot type filter
    await expect(page.locator('select').first()).toBeVisible();
    
    // Check date range buttons
    await expect(page.locator('text=7G')).toBeVisible();
    await expect(page.locator('text=30G')).toBeVisible();
    await expect(page.locator('text=Tümü')).toBeVisible();
  });

  test('should navigate between different tabs', async ({ page }) => {
    // Click on ROI Analysis tab
    await page.click('text=ROI Analizi');
    await expect(page.locator('text=ROI ve Karlılık Analizi')).toBeVisible();
    
    // Click on Robot Analysis tab
    await page.click('text=Robot Analizi');
    await expect(page.locator('text=Robot performans karşılaştırması')).toBeVisible();
    
    // Click back to Overview
    await page.click('text=Genel Bakış');
    await expect(page.locator('text=Performans özeti')).toBeVisible();
  });

  test('should display performance metrics', async ({ page }) => {
    // Check if key performance indicators are visible
    await expect(page.locator('text=Toplam ROI')).toBeVisible();
    await expect(page.locator('text=Aktif Robot')).toBeVisible();
    await expect(page.locator('text=Toplam İşlem')).toBeVisible();
    await expect(page.locator('text=Kazanma Oranı')).toBeVisible();
  });

  test('should handle filter changes', async ({ page }) => {
    // Change robot type filter
    await page.selectOption('select', 'solo');
    
    // Wait for data to update
    await page.waitForTimeout(1000);
    
    // Check if active filters are displayed
    await expect(page.locator('text=Aktif Filtreler')).toBeVisible();
    await expect(page.locator('text=Solo-Robot')).toBeVisible();
    
    // Change date range
    await page.click('text=30G');
    await page.waitForTimeout(1000);
    
    // Clear filters
    await page.click('text=Filtreleri Temizle');
    await page.waitForTimeout(1000);
  });

  test('should display charts and visualizations', async ({ page }) => {
    // Check if charts are rendered (look for SVG elements which are common in chart libraries)
    await expect(page.locator('svg').first()).toBeVisible();
    
    // Navigate to ROI Analysis to check more charts
    await page.click('text=ROI Analizi');
    await page.waitForTimeout(2000);
    
    // Check for chart containers
    await expect(page.locator('text=ROI Trendi')).toBeVisible();
  });

  test('should open export modal', async ({ page }) => {
    // Click on export button
    await page.click('[aria-label="İndir"]');
    
    // Check if export modal opens
    await expect(page.locator('text=İstatistikleri Dışa Aktar')).toBeVisible();
    
    // Check export format options
    await expect(page.locator('text=Excel (.xlsx)')).toBeVisible();
    await expect(page.locator('text=CSV (.csv)')).toBeVisible();
    await expect(page.locator('text=JSON (.json)')).toBeVisible();
    await expect(page.locator('text=PDF (.pdf)')).toBeVisible();
    
    // Close modal
    await page.click('text=İptal');
  });

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check if hamburger menu is visible on mobile
    await expect(page.locator('[aria-label="Menüyü aç"]')).toBeVisible();
    
    // Click hamburger menu
    await page.click('[aria-label="Menüyü aç"]');
    
    // Check if mobile navigation drawer opens
    await expect(page.locator('text=Trading Analizi')).toBeVisible();
    
    // Check if navigation items are accessible
    await expect(page.locator('text=Genel Bakış')).toBeVisible();
    
    // Close drawer by clicking outside or close button
    await page.click('[aria-label="Close"]');
  });

  test('should handle real-time updates indicator', async ({ page }) => {
    // Check if last update time is displayed
    await expect(page.locator('text=Son güncelleme:')).toBeVisible();
    
    // Check if refresh button works
    await page.click('[aria-label="Yenile"]');
    await page.waitForTimeout(1000);
  });

  test('should display robot-specific analytics', async ({ page }) => {
    // Navigate to Robot Analysis
    await page.click('text=Robot Analizi');
    
    // Check for robot comparison section
    await expect(page.locator('text=Robot Performans Karşılaştırması')).toBeVisible();
    
    // Navigate to Solo Robot section
    await page.click('text=Solo-Robot');
    await page.waitForTimeout(1000);
    
    // Check for solo robot analytics
    await expect(page.locator('text=Solo-Robot Performans Analizi')).toBeVisible();
    
    // Navigate to Bro Robots section
    await page.click('text=Bro-Robotlar');
    await page.waitForTimeout(1000);
    
    // Check for bro robot analytics
    await expect(page.locator('text=Bro-Robot Performans Analizi')).toBeVisible();
  });

  test('should handle error states gracefully', async ({ page }) => {
    // Simulate network error by intercepting requests
    await page.route('**/trades*', route => route.abort());
    
    // Refresh the page to trigger the error
    await page.reload();
    
    // Check if error message is displayed
    await expect(page.locator('text=hata')).toBeVisible();
    
    // Check if retry button is available
    await expect(page.locator('text=Tekrar Dene')).toBeVisible();
  });

  test('should display loading states', async ({ page }) => {
    // Intercept API calls to add delay
    await page.route('**/trades*', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      route.continue();
    });
    
    // Refresh to trigger loading
    await page.reload();
    
    // Check if loading indicator is shown
    await expect(page.locator('text=İstatistikler yükleniyor')).toBeVisible();
  });

  test('should maintain state when switching tabs', async ({ page }) => {
    // Apply a filter
    await page.selectOption('select', 'solo');
    await page.waitForTimeout(1000);
    
    // Navigate to different tab
    await page.click('text=ROI Analizi');
    await page.waitForTimeout(1000);
    
    // Navigate back to overview
    await page.click('text=Genel Bakış');
    await page.waitForTimeout(1000);
    
    // Check if filter is still applied
    await expect(page.locator('text=Solo-Robot')).toBeVisible();
  });

  test('should handle empty data states', async ({ page }) => {
    // Mock empty data response
    await page.route('**/trades*', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ data: [], count: 0 })
      });
    });
    
    // Refresh the page
    await page.reload();
    
    // Check if empty state message is displayed
    await expect(page.locator('text=Henüz kapalı işleminiz bulunmuyor')).toBeVisible();
  });

  test('should validate accessibility features', async ({ page }) => {
    // Check for proper heading hierarchy
    await expect(page.locator('h1')).toBeVisible();
    
    // Check for ARIA labels on interactive elements
    await expect(page.locator('[aria-label="Yenile"]')).toBeVisible();
    await expect(page.locator('[aria-label="İndir"]')).toBeVisible();
    
    // Check for keyboard navigation support
    await page.keyboard.press('Tab');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Enter');
  });

  test('should display correct data formatting', async ({ page }) => {
    // Check for Turkish currency formatting
    await expect(page.locator('text=₺')).toBeVisible();
    
    // Check for percentage formatting
    await expect(page.locator('text=%')).toBeVisible();
    
    // Check for Turkish date formatting
    await expect(page.locator('text=Son güncelleme:')).toBeVisible();
  });
});
