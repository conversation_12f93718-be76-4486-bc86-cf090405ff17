import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Card,
  CardBody,
  SimpleGrid,
  VStack,
  HStack,
  Text,


  Progress,
  Badge,
  Icon,

  useColorModeValue,
  Skeleton,

  Button,
  ButtonGroup
} from '@chakra-ui/react';
import {
  FiTrendingUp,
  FiTrendingDown,
  FiTarget,
  FiShield,
  FiActivity,
  FiDollarSign,
  FiClock,
  FiBarChart,

  FiZap
} from 'react-icons/fi';
import { RobotStats } from '../../../hooks/useEnhancedStatistics';

interface EnhancedPerformanceMetricsProps {
  robotStats: RobotStats;
  isRealTime?: boolean;
  showComparison?: boolean;
  compactView?: boolean;
}

interface MetricConfig {
  key: string;
  label: string;
  value: string | number;
  rawValue: number;
  icon: any;
  color: string;
  trend: 'increase' | 'decrease' | 'neutral';
  change: number;
  description: string;
  category: 'performance' | 'risk' | 'trading' | 'profitability';
  format: 'currency' | 'percentage' | 'number' | 'ratio';
  benchmark?: number;
  target?: number;
}

const EnhancedPerformanceMetrics: React.FC<EnhancedPerformanceMetricsProps> = ({
  robotStats,
  isRealTime = false,

  compactView = false
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');


  // Enhanced formatting functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };
  
  const formatPercent = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value / 100);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('tr-TR').format(value);
  };

  const formatRatio = (value: number) => {
    return value.toFixed(2);
  };

  // Real-time update simulation
  useEffect(() => {
    if (!isRealTime) return;

    const interval = setInterval(() => {
      setIsUpdating(true);
      setTimeout(() => {
        setIsUpdating(false);
        setLastUpdate(new Date());
      }, 500);
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [isRealTime]);

  // Enhanced metrics calculation
  const metrics = useMemo((): MetricConfig[] => {
    const calculateSharpeRatio = () => {
      // Enhanced Sharpe ratio calculation
      const riskFreeRate = 0.15; // 15% risk-free rate for Turkey
      const excessReturn = robotStats.annualizedROI - riskFreeRate;
      const volatility = Math.sqrt(robotStats.monthlyPerformance.reduce((acc, month) => {
        const monthlyReturn = month.roi / 100;
        const avgReturn = robotStats.annualizedROI / 100 / 12;
        return acc + Math.pow(monthlyReturn - avgReturn, 2);
      }, 0) / Math.max(1, robotStats.monthlyPerformance.length - 1)) * Math.sqrt(12);
      
      return volatility > 0 ? excessReturn / volatility : 0;
    };

    const calculateMaxDrawdown = () => {
      let peak = 0;
      let maxDD = 0;
      let runningTotal = 0;

      robotStats.monthlyPerformance.forEach(month => {
        runningTotal += month.pnl;
        if (runningTotal > peak) {
          peak = runningTotal;
        }
        const drawdown = (peak - runningTotal) / Math.max(peak, 1) * 100;
        if (drawdown > maxDD) {
          maxDD = drawdown;
        }
      });

      return maxDD;
    };

    const calculateProfitFactor = () => {
      const grossProfit = robotStats.monthlyPerformance
        .filter(m => m.pnl > 0)
        .reduce((sum, m) => sum + m.pnl, 0);
      const grossLoss = Math.abs(robotStats.monthlyPerformance
        .filter(m => m.pnl < 0)
        .reduce((sum, m) => sum + m.pnl, 0));
      
      return grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? 999 : 0;
    };

    const enhancedSharpe = calculateSharpeRatio();
    const enhancedMaxDD = calculateMaxDrawdown();
    const enhancedProfitFactor = calculateProfitFactor();

    return [
      {
        key: 'totalPnl',
        label: 'Net Kar/Zarar',
        value: formatCurrency(robotStats.totalPnl),
        rawValue: robotStats.totalPnl,
        icon: FiDollarSign,
        color: robotStats.totalPnl > 0 ? 'green' : 'red',
        trend: robotStats.totalPnl > 0 ? 'increase' : 'decrease',
        change: robotStats.totalPnl,
        description: 'Toplam net kar/zarar',
        category: 'profitability',
        format: 'currency',
        benchmark: 0,
        target: robotStats.totalInvestment * 0.2
      },
      {
        key: 'roi',
        label: 'Toplam ROI',
        value: formatPercent(robotStats.roi),
        rawValue: robotStats.roi,
        icon: FiTrendingUp,
        color: robotStats.roi > 0 ? 'green' : 'red',
        trend: robotStats.roi > 0 ? 'increase' : 'decrease',
        change: robotStats.roi,
        description: 'Toplam yatırım getirisi',
        category: 'performance',
        format: 'percentage',
        benchmark: 15,
        target: 25
      },
      {
        key: 'annualizedROI',
        label: 'Yıllık ROI',
        value: formatPercent(robotStats.annualizedROI),
        rawValue: robotStats.annualizedROI,
        icon: FiTarget,
        color: robotStats.annualizedROI > 15 ? 'green' : robotStats.annualizedROI > 0 ? 'orange' : 'red',
        trend: robotStats.annualizedROI > 0 ? 'increase' : 'decrease',
        change: robotStats.annualizedROI,
        description: 'Yıllıklandırılmış getiri oranı',
        category: 'performance',
        format: 'percentage',
        benchmark: 15,
        target: 30
      },
      {
        key: 'winRate',
        label: 'Kazanma Oranı',
        value: formatPercent(robotStats.winRate),
        rawValue: robotStats.winRate,
        icon: FiTarget,
        color: robotStats.winRate > 60 ? 'green' : robotStats.winRate > 50 ? 'orange' : 'red',
        trend: robotStats.winRate > 50 ? 'increase' : 'decrease',
        change: robotStats.winRate - 50,
        description: 'Başarılı işlem oranı',
        category: 'performance',
        format: 'percentage',
        benchmark: 50,
        target: 65
      },
      {
        key: 'profitFactor',
        label: 'Profit Factor',
        value: formatRatio(enhancedProfitFactor),
        rawValue: enhancedProfitFactor,
        icon: FiBarChart,
        color: enhancedProfitFactor > 1.5 ? 'green' : enhancedProfitFactor > 1 ? 'orange' : 'red',
        trend: enhancedProfitFactor > 1 ? 'increase' : 'decrease',
        change: enhancedProfitFactor - 1,
        description: 'Brüt kar/brüt zarar oranı',
        category: 'profitability',
        format: 'ratio',
        benchmark: 1,
        target: 2
      },
      {
        key: 'maxDrawdown',
        label: 'Max Drawdown',
        value: formatPercent(enhancedMaxDD),
        rawValue: enhancedMaxDD,
        icon: FiShield,
        color: enhancedMaxDD < 10 ? 'green' : enhancedMaxDD < 20 ? 'orange' : 'red',
        trend: 'decrease',
        change: -enhancedMaxDD,
        description: 'Maksimum düşüş oranı',
        category: 'risk',
        format: 'percentage',
        benchmark: 20,
        target: 10
      },
      {
        key: 'sharpeRatio',
        label: 'Sharpe Ratio',
        value: formatRatio(enhancedSharpe),
        rawValue: enhancedSharpe,
        icon: FiActivity,
        color: enhancedSharpe > 1 ? 'green' : enhancedSharpe > 0.5 ? 'orange' : 'red',
        trend: enhancedSharpe > 0 ? 'increase' : 'decrease',
        change: enhancedSharpe,
        description: 'Risk ayarlı getiri oranı',
        category: 'risk',
        format: 'ratio',
        benchmark: 0.5,
        target: 1.5
      },
      {
        key: 'totalTrades',
        label: 'Toplam İşlem',
        value: formatNumber(robotStats.totalTrades),
        rawValue: robotStats.totalTrades,
        icon: FiActivity,
        color: 'blue',
        trend: 'neutral',
        change: 0,
        description: 'Gerçekleştirilen toplam işlem sayısı',
        category: 'trading',
        format: 'number',
        benchmark: 100,
        target: 500
      },
      {
        key: 'averageTradeSize',
        label: 'Ortalama İşlem',
        value: formatCurrency(robotStats.averageTradeSize),
        rawValue: robotStats.averageTradeSize,
        icon: FiClock,
        color: 'blue',
        trend: 'neutral',
        change: 0,
        description: 'Ortalama işlem büyüklüğü',
        category: 'trading',
        format: 'currency',
        benchmark: 1000,
        target: 5000
      }
    ];
  }, [robotStats]);

  // Filter metrics by category
  const filteredMetrics = useMemo(() => {
    if (selectedCategory === 'all') return metrics;
    return metrics.filter(metric => metric.category === selectedCategory);
  }, [metrics, selectedCategory]);

  const categories = [
    { key: 'all', label: 'Tümü', icon: FiBarChart },
    { key: 'performance', label: 'Performans', icon: FiTrendingUp },
    { key: 'profitability', label: 'Karlılık', icon: FiDollarSign },
    { key: 'risk', label: 'Risk', icon: FiShield },
    { key: 'trading', label: 'İşlem', icon: FiActivity }
  ];

  return (
    <VStack spacing={6} align="stretch">
      {/* Header with Category Filters */}
      <HStack justify="space-between" flexWrap="wrap">
        <VStack align="start" spacing={1}>
          <HStack spacing={3}>
            <Text fontSize="lg" fontWeight="semibold" color={textColor}>
              Performans Metrikleri
            </Text>
            {isRealTime && (
              <Badge colorScheme="green" variant="subtle" px={2} py={1}>
                <Icon as={FiZap} mr={1} />
                Canlı
              </Badge>
            )}
          </HStack>
          {isRealTime && (
            <Text fontSize="xs" color="gray.500">
              Son güncelleme: {lastUpdate.toLocaleTimeString('tr-TR')}
            </Text>
          )}
        </VStack>

        <ButtonGroup size="sm" isAttached variant="outline">
          {categories.map((category) => (
            <Button
              key={category.key}
              isActive={selectedCategory === category.key}
              onClick={() => setSelectedCategory(category.key)}
              leftIcon={<Icon as={category.icon} />}
            >
              {category.label}
            </Button>
          ))}
        </ButtonGroup>
      </HStack>

      {/* Metrics Grid */}
      <SimpleGrid 
        columns={{ base: 1, md: 2, lg: compactView ? 2 : 3, xl: compactView ? 3 : 4 }} 
        spacing={6}
      >
        {filteredMetrics.map((metric) => (
          <Card 
            key={metric.key}
            bg={cardBg} 
            borderRadius="xl" 
            shadow="sm"
            border="1px"
            borderColor={borderColor}
            _hover={{ 
              shadow: 'md', 
              transform: 'translateY(-2px)',
              borderColor: `${metric.color}.200`
            }}
            transition="all 0.2s"
            position="relative"
            overflow="hidden"
          >
            {isUpdating && (
              <Box
                position="absolute"
                top={0}
                left={0}
                right={0}
                h="2px"
                bg={`${metric.color}.500`}
                opacity={0.7}
                animation="pulse 1s infinite"
              />
            )}
            
            <CardBody p={6}>
              <VStack spacing={4} align="stretch">
                {/* Metric Header */}
                <HStack justify="space-between">
                  <HStack spacing={3}>
                    <Box
                      p={2}
                      borderRadius="lg"
                      bg={`${metric.color}.50`}
                      color={`${metric.color}.500`}
                    >
                      <Icon as={metric.icon} boxSize={5} />
                    </Box>
                    <VStack align="start" spacing={0}>
                      <Text fontSize="sm" fontWeight="medium" color="gray.500">
                        {metric.label}
                      </Text>
                    </VStack>
                  </HStack>
                  {metric.trend !== 'neutral' && (
                    <Icon 
                      as={metric.trend === 'increase' ? FiTrendingUp : FiTrendingDown}
                      color={metric.trend === 'increase' ? 'green.500' : 'red.500'}
                      boxSize={4}
                    />
                  )}
                </HStack>

                {/* Metric Value */}
                <VStack spacing={2} align="stretch">
                  <Skeleton isLoaded={!isUpdating}>
                    <Text 
                      fontSize="3xl" 
                      fontWeight="bold"
                      color={`${metric.color}.500`}
                      lineHeight="1"
                    >
                      {metric.value}
                    </Text>
                  </Skeleton>
                  <Text fontSize="xs" color="gray.500">
                    {metric.description}
                  </Text>
                </VStack>

                {/* Progress/Benchmark Indicator */}
                {metric.benchmark !== undefined && (
                  <VStack spacing={2} align="stretch">
                    <HStack justify="space-between" fontSize="xs">
                      <Text color="gray.500">Hedef</Text>
                      <Text color={`${metric.color}.500`} fontWeight="medium">
                        {metric.format === 'percentage' ? `${metric.target}%` : 
                         metric.format === 'currency' ? formatCurrency(metric.target!) :
                         metric.format === 'ratio' ? metric.target?.toFixed(1) :
                         formatNumber(metric.target!)}
                      </Text>
                    </HStack>
                    <Progress 
                      value={Math.min(100, (Math.abs(metric.rawValue) / (metric.target || 1)) * 100)} 
                      colorScheme={metric.color}
                      size="sm"
                      borderRadius="full"
                      bg={`${metric.color}.50`}
                    />
                  </VStack>
                )}

                {/* Change Indicator */}
                {metric.change !== 0 && metric.trend !== 'neutral' && (
                  <HStack spacing={2} fontSize="sm">
                    <Icon
                      as={metric.change > 0 ? FiTrendingUp : FiTrendingDown}
                      color={metric.change > 0 ? 'green.500' : 'red.500'}
                      boxSize={4}
                    />
                    <Text
                      color={metric.change > 0 ? 'green.500' : 'red.500'}
                      fontWeight="medium"
                    >
                      {metric.change > 0 ? '+' : ''}{Math.abs(metric.change).toFixed(2)}
                      {metric.format === 'percentage' ? '%' : ''}
                    </Text>
                  </HStack>
                )}
              </VStack>
            </CardBody>
          </Card>
        ))}
      </SimpleGrid>
    </VStack>
  );
};

export default EnhancedPerformanceMetrics;
