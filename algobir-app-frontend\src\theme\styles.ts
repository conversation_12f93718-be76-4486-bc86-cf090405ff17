import { mode } from '@chakra-ui/theme-tools';

export const globalStyles = {
  colors: {
    brand: {
      50: '#E3F2F9',
      100: '#C5E4F3',
      200: '#A2D4EC',
      300: '#7AC1E4',
      400: '#47A9DA',
      500: '#0088CC', // Main Brand Color (Algobir Blue)
      600: '#007AB8',
      700: '#006BA1',
      800: '#005885',
      900: '#003F5E',
    },
    brandScheme: {
      100: '#E3F2F9',
      200: '#47A9DA',
      300: '#47A9DA',
      400: '#47A9DA',
      500: '#0088CC',
      600: '#007AB8',
      700: '#003F5E',
      800: '#005885',
      900: '#003F5E'
    },
    brandTabs: {
      100: '#E3F2F9',
      200: '#0088CC',
      300: '#0088CC',
      400: '#0088CC',
      500: '#0088CC',
      600: '#007AB8',
      700: '#003F5E',
      800: '#005885',
      900: '#003F5E'
    },
    background: {
      light: '#F4F7FE',
      dark: '#1B254B',
    },
    secondaryGray: {
      100: "#E0E5F2",
      200: "#E1E9F8",
      300: "#F4F7FE",
      400: "#E9EDF7",
      500: "#8F9BBA",
      600: "#A3AED0",
      700: "#707EAE",
      800: "#707EAE",
      900: "#1B2559"
    },
    red: {
      100: '#FEEFEE',
      500: '#EE5D50',
      600: '#E31A1A'
    },
    blue: {
      50: '#EFF4FB',
      500: '#3965FF'
    },
    orange: {
      100: '#FFF6DA',
      500: '#FFB547'
    },
    green: {
      100: '#E6FAF5',
      500: '#01B574'
    },
    navy: {
      50: '#d0dcfb',
      100: '#aac0fe',
      200: '#a3b9f8',
      300: '#728fea',
      400: '#3652ba',
      500: '#1b3bbb',
      600: '#24388a',
      700: '#1B254B',
      800: '#111c44',
      900: '#0b1437'
    },
    gray: {
      100: '#FAFCFE'
    }
  },
  fonts: {
    heading: `'DM Sans', sans-serif`,
    body: `'DM Sans', sans-serif`,
  },
  styles: {
    global: (props: any) => ({
      body: {
        overflowX: 'hidden',
        bg: mode('secondaryGray.300', 'navy.900')(props),
        fontFamily: 'DM Sans',
        letterSpacing: '-0.5px',
        color: mode('secondaryGray.900', 'white')(props),
      },
      input: {
        color: mode('gray.700', 'white')(props)
      },
      html: {
        fontFamily: 'DM Sans',
        height: '100%',
      },
      '#root': {
        height: '100%',
      },
    }),
  },
}; 