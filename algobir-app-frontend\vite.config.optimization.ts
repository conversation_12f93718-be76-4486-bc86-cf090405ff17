/**
 * Advanced Vite Configuration for Build & Deployment Optimization
 * Phase 3.4: Build & Deployment Process Optimization
 * Enterprise-grade build optimization with advanced chunking, caching, and performance
 */

import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import { createHtmlPlugin } from 'vite-plugin-html';
import { VitePWA } from 'vite-plugin-pwa';

// Build optimization configuration
const BUILD_OPTIMIZATION = {
  // Chunk splitting strategy
  chunkSizeWarningLimit: 1000, // 1MB warning threshold
  maxChunkSize: 500, // 500KB max chunk size
  
  // Asset optimization
  assetsInlineLimit: 4096, // 4KB inline threshold
  cssCodeSplit: true,
  
  // Compression and minification
  minify: 'terser',
  sourcemap: false, // Disable in production for security
  
  // Tree shaking and dead code elimination
  treeshake: {
    preset: 'recommended',
    moduleSideEffects: false
  }
};

// Advanced chunk splitting configuration
const getChunkSplittingConfig = (mode: string) => {
  const isProduction = mode === 'production';
  
  return {
    manualChunks: (id: string) => {
      // Vendor libraries chunking
      if (id.includes('node_modules')) {
        // React ecosystem
        if (id.includes('react') || id.includes('react-dom')) {
          return 'react-vendor';
        }
        
        // Chakra UI and styling
        if (id.includes('@chakra-ui') || id.includes('@emotion') || id.includes('framer-motion')) {
          return 'ui-vendor';
        }
        
        // Charts and visualization
        if (id.includes('recharts') || id.includes('d3')) {
          return 'charts-vendor';
        }
        
        // Supabase and API
        if (id.includes('@supabase') || id.includes('postgrest')) {
          return 'api-vendor';
        }
        
        // Utilities and helpers
        if (id.includes('date-fns') || id.includes('lodash') || id.includes('uuid')) {
          return 'utils-vendor';
        }
        
        // Form handling
        if (id.includes('react-hook-form') || id.includes('yup') || id.includes('formik')) {
          return 'forms-vendor';
        }
        
        // Routing
        if (id.includes('react-router')) {
          return 'router-vendor';
        }
        
        // Testing libraries (should not be in production)
        if (!isProduction && (id.includes('vitest') || id.includes('@testing-library'))) {
          return 'test-vendor';
        }
        
        // Other vendor libraries
        return 'vendor';
      }
      
      // Application code chunking
      if (id.includes('/src/')) {
        // Components chunking
        if (id.includes('/components/')) {
          if (id.includes('/admin/')) return 'admin-components';
          if (id.includes('/marketplace/')) return 'marketplace-components';
          if (id.includes('/statistics/')) return 'statistics-components';
          if (id.includes('/trades/')) return 'trades-components';
          if (id.includes('/monitoring/')) return 'monitoring-components';
          return 'common-components';
        }
        
        // Pages chunking
        if (id.includes('/pages/')) {
          return 'pages';
        }
        
        // Services and utilities
        if (id.includes('/services/') || id.includes('/utils/')) {
          return 'services-utils';
        }
        
        // Context and hooks
        if (id.includes('/context/') || id.includes('/hooks/')) {
          return 'context-hooks';
        }
      }
      
      return undefined;
    }
  };
};

// Performance optimization plugins
const getOptimizationPlugins = (mode: string, env: Record<string, string>) => {
  const isProduction = mode === 'production';
  const isDevelopment = mode === 'development';
  
  const plugins = [
    // React plugin with optimization
    react({
      // Enable React Fast Refresh in development
      fastRefresh: isDevelopment,
      // Optimize JSX in production
      jsxRuntime: 'automatic',
      jsxImportSource: isProduction ? '@emotion/react' : undefined,
      babel: {
        plugins: isProduction ? [
          // Remove console statements in production
          ['transform-remove-console', { exclude: ['error', 'warn'] }],
          // Remove debugger statements
          'transform-remove-debugger'
        ] : []
      }
    }),
    
    // HTML plugin for optimization
    createHtmlPlugin({
      minify: isProduction ? {
        collapseWhitespace: true,
        removeComments: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: true,
        removeStyleLinkTypeAttributes: true,
        useShortDoctype: true,
        minifyCSS: true,
        minifyJS: true
      } : false,
      inject: {
        data: {
          title: env.VITE_APP_TITLE || 'Algobir Trading Platform',
          description: env.VITE_APP_DESCRIPTION || 'Advanced Algorithmic Trading Platform',
          keywords: env.VITE_APP_KEYWORDS || 'trading, algorithms, crypto, forex',
          author: env.VITE_APP_AUTHOR || 'Algobir Team',
          viewport: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',
          themeColor: env.VITE_THEME_COLOR || '#4299E1'
        }
      }
    })
  ];
  
  // Production-only plugins
  if (isProduction) {
    // Bundle analyzer
    if (env.ANALYZE_BUNDLE === 'true') {
      plugins.push(
        visualizer({
          filename: 'dist/bundle-analysis.html',
          open: true,
          gzipSize: true,
          brotliSize: true,
          template: 'treemap' // 'treemap', 'sunburst', 'network'
        }) as any
      );
    }
    
    // PWA plugin for production
    if (env.VITE_ENABLE_PWA === 'true') {
      plugins.push(
        VitePWA({
          registerType: 'autoUpdate',
          workbox: {
            globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2}'],
            runtimeCaching: [
              {
                urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
                handler: 'CacheFirst',
                options: {
                  cacheName: 'google-fonts-cache',
                  expiration: {
                    maxEntries: 10,
                    maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
                  },
                  cacheKeyWillBeUsed: async ({ request }) => {
                    return `${request.url}?v=1`;
                  }
                }
              },
              {
                urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
                handler: 'CacheFirst',
                options: {
                  cacheName: 'gstatic-fonts-cache',
                  expiration: {
                    maxEntries: 10,
                    maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
                  }
                }
              },
              {
                urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
                handler: 'CacheFirst',
                options: {
                  cacheName: 'images-cache',
                  expiration: {
                    maxEntries: 100,
                    maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
                  }
                }
              }
            ]
          },
          manifest: {
            name: env.VITE_APP_TITLE || 'Algobir Trading Platform',
            short_name: 'Algobir',
            description: env.VITE_APP_DESCRIPTION || 'Advanced Algorithmic Trading Platform',
            theme_color: env.VITE_THEME_COLOR || '#4299E1',
            background_color: '#ffffff',
            display: 'standalone',
            orientation: 'portrait',
            scope: '/',
            start_url: '/',
            icons: [
              {
                src: '/icons/icon-72x72.png',
                sizes: '72x72',
                type: 'image/png'
              },
              {
                src: '/icons/icon-96x96.png',
                sizes: '96x96',
                type: 'image/png'
              },
              {
                src: '/icons/icon-128x128.png',
                sizes: '128x128',
                type: 'image/png'
              },
              {
                src: '/icons/icon-144x144.png',
                sizes: '144x144',
                type: 'image/png'
              },
              {
                src: '/icons/icon-152x152.png',
                sizes: '152x152',
                type: 'image/png'
              },
              {
                src: '/icons/icon-192x192.png',
                sizes: '192x192',
                type: 'image/png'
              },
              {
                src: '/icons/icon-384x384.png',
                sizes: '384x384',
                type: 'image/png'
              },
              {
                src: '/icons/icon-512x512.png',
                sizes: '512x512',
                type: 'image/png'
              }
            ]
          }
        })
      );
    }
  }
  
  return plugins;
};

// Advanced build configuration
const getBuildConfig = (mode: string, env: Record<string, string>) => {
  const isProduction = mode === 'production';
  
  return {
    target: 'es2020',
    outDir: 'dist',
    assetsDir: 'assets',
    assetsInlineLimit: BUILD_OPTIMIZATION.assetsInlineLimit,
    cssCodeSplit: BUILD_OPTIMIZATION.cssCodeSplit,
    sourcemap: isProduction ? false : 'inline',
    minify: isProduction ? BUILD_OPTIMIZATION.minify : false,
    
    // Rollup options for advanced optimization
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      output: {
        // Advanced chunking strategy
        ...getChunkSplittingConfig(mode),
        
        // Asset naming for caching
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
          return `assets/js/[name]-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name!.split('.');
          const ext = info[info.length - 1];
          
          if (/\.(css)$/.test(assetInfo.name!)) {
            return `assets/css/[name]-[hash].${ext}`;
          }
          
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name!)) {
            return `assets/images/[name]-[hash].${ext}`;
          }
          
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name!)) {
            return `assets/fonts/[name]-[hash].${ext}`;
          }
          
          return `assets/[name]-[hash].${ext}`;
        },
        entryFileNames: 'assets/js/[name]-[hash].js',
        
        // Optimization settings
        compact: isProduction,
        generatedCode: {
          constBindings: true,
          objectShorthand: true
        }
      },
      
      // External dependencies (if needed)
      external: [],
      
      // Tree shaking configuration
      treeshake: BUILD_OPTIMIZATION.treeshake
    },
    
    // Terser options for minification
    terserOptions: isProduction ? {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2
      },
      mangle: {
        safari10: true
      },
      format: {
        comments: false
      }
    } : undefined,
    
    // Chunk size warning limit
    chunkSizeWarningLimit: BUILD_OPTIMIZATION.chunkSizeWarningLimit,
    
    // Watch options for development
    watch: mode === 'development' ? {
      buildDelay: 100,
      exclude: ['node_modules/**', 'dist/**']
    } : undefined
  };
};

// Main Vite configuration
export default defineConfig(({ mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd(), '');
  const isProduction = mode === 'production';
  const isDevelopment = mode === 'development';
  
  return {
    plugins: getOptimizationPlugins(mode, env),
    
    // Build configuration
    build: getBuildConfig(mode, env),
    
    // Development server configuration
    server: {
      port: parseInt(env.VITE_PORT) || 3000,
      host: env.VITE_HOST || 'localhost',
      open: env.VITE_OPEN_BROWSER === 'true',
      cors: true,
      hmr: {
        overlay: true
      }
    },
    
    // Preview server configuration
    preview: {
      port: parseInt(env.VITE_PREVIEW_PORT) || 4173,
      host: env.VITE_PREVIEW_HOST || 'localhost',
      cors: true
    },
    
    // Path resolution
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@pages': resolve(__dirname, 'src/pages'),
        '@hooks': resolve(__dirname, 'src/hooks'),
        '@utils': resolve(__dirname, 'src/utils'),
        '@services': resolve(__dirname, 'src/services'),
        '@context': resolve(__dirname, 'src/context'),
        '@theme': resolve(__dirname, 'src/theme'),
        '@assets': resolve(__dirname, 'src/assets')
      }
    },
    
    // CSS configuration
    css: {
      devSourcemap: isDevelopment,
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/theme/variables.scss";`
        }
      }
    },
    
    // Dependency optimization
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        '@chakra-ui/react',
        '@emotion/react',
        '@emotion/styled',
        'framer-motion',
        'react-router-dom',
        'react-hook-form',
        '@supabase/supabase-js',
        'recharts',
        'date-fns'
      ],
      exclude: [
        // Exclude test dependencies from optimization
        'vitest',
        '@testing-library/react',
        '@testing-library/jest-dom'
      ]
    },
    
    // Environment variables
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      __BUILD_MODE__: JSON.stringify(mode)
    },
    
    // ESBuild configuration
    esbuild: {
      target: 'es2020',
      drop: isProduction ? ['console', 'debugger'] : [],
      legalComments: 'none'
    }
  };
});
