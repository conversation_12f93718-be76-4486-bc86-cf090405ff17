import { test, expect, Page } from '@playwright/test';

// Helper function to wait for animations
const waitForAnimation = async (page: Page, duration = 300) => {
  await page.waitForTimeout(duration);
};

// Helper function to get sidebar width
const getSidebarWidth = async (page: Page) => {
  const sidebar = page.locator('[role="navigation"]').first();
  const box = await sidebar.boundingBox();
  return box?.width || 0;
};

test.describe('Collapsible Sidebar E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Ensure we're on desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
  });

  test('should display sidebar in expanded state by default on desktop', async ({ page }) => {
    // Check if sidebar is visible
    const sidebar = page.locator('[role="navigation"]');
    await expect(sidebar).toBeVisible();
    
    // Check if sidebar has expanded width
    const width = await getSidebarWidth(page);
    expect(width).toBeGreaterThan(200); // Should be around 285px when expanded
    
    // Check if menu text is visible
    await expect(page.locator('text=Dashboard')).toBeVisible();
    await expect(page.locator('text=İstatistikler')).toBeVisible();
  });

  test('should collapse sidebar when toggle button is clicked', async ({ page }) => {
    // Find and click the sidebar toggle button
    const toggleButton = page.locator('button[aria-label*="Sidebar"]');
    await expect(toggleButton).toBeVisible();
    await toggleButton.click();
    
    // Wait for animation
    await waitForAnimation(page);
    
    // Check if sidebar is collapsed
    const width = await getSidebarWidth(page);
    expect(width).toBeLessThan(100); // Should be around 70px when collapsed
    
    // Menu text should not be visible, only icons
    await expect(page.locator('text=Dashboard')).not.toBeVisible();
  });

  test('should expand sidebar when hovering near left edge', async ({ page }) => {
    // First collapse the sidebar
    const toggleButton = page.locator('button[aria-label*="Sidebar"]');
    await toggleButton.click();
    await waitForAnimation(page);
    
    // Verify it's collapsed
    let width = await getSidebarWidth(page);
    expect(width).toBeLessThan(100);
    
    // Move mouse to left edge
    await page.mouse.move(30, 400);
    await waitForAnimation(page, 500); // Wait for auto-expand delay
    
    // Check if sidebar expanded
    width = await getSidebarWidth(page);
    expect(width).toBeGreaterThan(200);
  });

  test('should open submenu when clicking menu item with submenu', async ({ page }) => {
    // Click on Statistics menu item (has submenu)
    const statisticsButton = page.locator('text=İstatistikler').locator('xpath=ancestor::*[@role="button"][1]');
    await statisticsButton.click();
    
    // Wait for submenu animation
    await waitForAnimation(page);
    
    // Check if submenu panel is visible
    const submenuPanel = page.locator('[role="dialog"]');
    await expect(submenuPanel).toBeVisible();
    
    // Check if submenu items are visible
    await expect(page.locator('text=Performans Analizi')).toBeVisible();
    await expect(page.locator('text=Zaman Analizi')).toBeVisible();
    
    // Check if sidebar is in collapsed state with submenu
    const width = await getSidebarWidth(page);
    expect(width).toBeLessThan(100); // Main sidebar should be collapsed
  });

  test('should close submenu when clicking close button', async ({ page }) => {
    // Open submenu first
    const statisticsButton = page.locator('text=İstatistikler').locator('xpath=ancestor::*[@role="button"][1]');
    await statisticsButton.click();
    await waitForAnimation(page);
    
    // Verify submenu is open
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Click close button
    const closeButton = page.locator('button[aria-label*="kapat"]');
    await closeButton.click();
    await waitForAnimation(page);
    
    // Verify submenu is closed
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test('should handle keyboard shortcuts', async ({ page }) => {
    // Test Ctrl+B to toggle sidebar
    await page.keyboard.press('Control+b');
    await waitForAnimation(page);
    
    // Sidebar should be collapsed
    let width = await getSidebarWidth(page);
    expect(width).toBeLessThan(100);
    
    // Test Ctrl+Shift+E to expand
    await page.keyboard.press('Control+Shift+E');
    await waitForAnimation(page);
    
    // Sidebar should be expanded
    width = await getSidebarWidth(page);
    expect(width).toBeGreaterThan(200);
    
    // Test Ctrl+Shift+C to collapse
    await page.keyboard.press('Control+Shift+C');
    await waitForAnimation(page);
    
    // Sidebar should be collapsed
    width = await getSidebarWidth(page);
    expect(width).toBeLessThan(100);
  });

  test('should close submenu with Escape key', async ({ page }) => {
    // Open submenu
    const statisticsButton = page.locator('text=İstatistikler').locator('xpath=ancestor::*[@role="button"][1]');
    await statisticsButton.click();
    await waitForAnimation(page);
    
    // Verify submenu is open
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    
    // Press Escape
    await page.keyboard.press('Escape');
    await waitForAnimation(page);
    
    // Verify submenu is closed
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test('should work properly on tablet view', async ({ page }) => {
    // Switch to tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await waitForAnimation(page);
    
    // Sidebar should be hidden, mobile menu button should be visible
    const sidebar = page.locator('[role="navigation"]');
    await expect(sidebar).not.toBeVisible();
    
    const mobileMenuButton = page.locator('button[aria-label*="Menüyü aç"]');
    await expect(mobileMenuButton).toBeVisible();
    
    // Click mobile menu button
    await mobileMenuButton.click();
    await waitForAnimation(page);
    
    // Drawer should be visible
    const drawer = page.locator('[role="dialog"]').first();
    await expect(drawer).toBeVisible();
  });

  test('should work properly on mobile view', async ({ page }) => {
    // Switch to mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await waitForAnimation(page);
    
    // Sidebar should be hidden, mobile menu button should be visible
    const sidebar = page.locator('[role="navigation"]');
    await expect(sidebar).not.toBeVisible();
    
    const mobileMenuButton = page.locator('button[aria-label*="Menüyü aç"]');
    await expect(mobileMenuButton).toBeVisible();
    
    // Click mobile menu button
    await mobileMenuButton.click();
    await waitForAnimation(page);
    
    // Drawer should be visible with full menu
    const drawer = page.locator('[role="dialog"]').first();
    await expect(drawer).toBeVisible();
    await expect(page.locator('text=Dashboard')).toBeVisible();
  });

  test('should maintain smooth animations during state transitions', async ({ page }) => {
    // Enable animation testing
    await page.addStyleTag({
      content: `
        *, *::before, *::after {
          animation-duration: 0.1s !important;
          animation-delay: 0s !important;
          transition-duration: 0.1s !important;
          transition-delay: 0s !important;
        }
      `
    });
    
    // Test sidebar toggle animation
    const toggleButton = page.locator('button[aria-label*="Sidebar"]');
    await toggleButton.click();
    
    // Animation should complete quickly with our CSS override
    await waitForAnimation(page, 150);
    
    // Verify final state
    const width = await getSidebarWidth(page);
    expect(width).toBeLessThan(100);
  });

  test('should handle accessibility features', async ({ page }) => {
    // Check ARIA attributes
    const sidebar = page.locator('[role="navigation"]');
    await expect(sidebar).toHaveAttribute('aria-expanded', 'true');
    
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    
    // Check focus management
    const focusedElement = page.locator(':focus');
    await expect(focusedElement).toBeVisible();
    
    // Test screen reader announcements (check aria-label attributes)
    const toggleButton = page.locator('button[aria-label*="Sidebar"]');
    await expect(toggleButton).toHaveAttribute('aria-label');
  });

  test('should handle content area adjustment', async ({ page }) => {
    // Get initial content area position
    const content = page.locator('main, [role="main"]').first();
    const initialBox = await content.boundingBox();
    
    // Toggle sidebar
    const toggleButton = page.locator('button[aria-label*="Sidebar"]');
    await toggleButton.click();
    await waitForAnimation(page);
    
    // Content area should adjust
    const newBox = await content.boundingBox();
    expect(newBox?.x).not.toBe(initialBox?.x);
  });
});
