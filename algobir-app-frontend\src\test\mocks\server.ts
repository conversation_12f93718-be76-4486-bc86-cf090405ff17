/**
 * Mock Service Worker (MSW) Server Configuration
 * Phase 3.1: Automated Testing Pipeline Setup
 * API mocking for unit and integration tests
 */

import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';

// Mock data generators
const generateMockUser = (id: string = 'test-user-id') => ({
  id,
  email: '<EMAIL>',
  user_metadata: {
    full_name: 'Test User',
    avatar_url: null
  },
  app_metadata: {},
  aud: 'authenticated',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
});

const generateMockRobot = (id: string = 'test-robot-id') => ({
  id,
  name: 'Test Robot',
  description: 'A test trading robot',
  seller_id: 'test-user-id',
  is_active: true,
  performance_metrics: {
    total_trades: 100,
    winning_trades: 75,
    win_rate: 75.0,
    total_profit: 1250.50
  },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
});

const generateMockTrade = (id: string = 'test-trade-id') => ({
  id,
  user_id: 'test-user-id',
  robot_id: 'test-robot-id',
  symbol: 'BTCUSDT',
  order_side: 'BUY',
  quantity: 0.001,
  price: 45000.00,
  status: 'completed',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString()
});

const generateMockNotification = (id: string = 'test-notification-id') => ({
  id,
  user_id: 'test-user-id',
  title: 'Test Notification',
  message: 'This is a test notification',
  type: 'info',
  is_read: false,
  metadata: {},
  created_at: new Date().toISOString()
});

// API handlers
const handlers = [
  // Authentication endpoints
  http.post('*/auth/v1/token', () => {
    return HttpResponse.json({
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      expires_in: 3600,
      token_type: 'bearer',
      user: generateMockUser()
    });
  }),

  http.get('*/auth/v1/user', () => {
    return HttpResponse.json(generateMockUser());
  }),

  http.post('*/auth/v1/logout', () => {
    return HttpResponse.json({});
  }),

  // User profiles
  http.get('*/rest/v1/profiles', () => {
    return HttpResponse.json([
      {
        id: 'test-user-id',
        username: 'testuser',
        full_name: 'Test User',
        avatar_url: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]);
  }),

  http.patch('*/rest/v1/profiles', () => {
    return HttpResponse.json({
      id: 'test-user-id',
      username: 'testuser',
      full_name: 'Updated Test User',
      avatar_url: null,
      updated_at: new Date().toISOString()
    });
  }),

  // User settings
  http.get('*/rest/v1/user_settings', () => {
    return HttpResponse.json([
      {
        id: 'test-user-id',
        webhook_id: 'test-webhook-id',
        api_key_set: true,
        token_set: true,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]);
  }),

  http.patch('*/rest/v1/user_settings', () => {
    return HttpResponse.json({
      id: 'test-user-id',
      updated_at: new Date().toISOString()
    });
  }),

  // Robots
  http.get('*/rest/v1/robots', () => {
    return HttpResponse.json([
      generateMockRobot('robot-1'),
      generateMockRobot('robot-2'),
      generateMockRobot('robot-3')
    ]);
  }),

  http.post('*/rest/v1/robots', () => {
    return HttpResponse.json(generateMockRobot('new-robot-id'));
  }),

  http.patch('*/rest/v1/robots', () => {
    return HttpResponse.json(generateMockRobot('updated-robot-id'));
  }),

  http.delete('*/rest/v1/robots', () => {
    return HttpResponse.json({});
  }),

  // Trades
  http.get('*/rest/v1/trades', () => {
    return HttpResponse.json([
      generateMockTrade('trade-1'),
      generateMockTrade('trade-2'),
      generateMockTrade('trade-3')
    ]);
  }),

  http.post('*/rest/v1/trades', () => {
    return HttpResponse.json(generateMockTrade('new-trade-id'));
  }),

  // Notifications
  http.get('*/rest/v1/notifications', () => {
    return HttpResponse.json([
      generateMockNotification('notification-1'),
      generateMockNotification('notification-2'),
      generateMockNotification('notification-3')
    ]);
  }),

  http.post('*/rest/v1/notifications', () => {
    return HttpResponse.json(generateMockNotification('new-notification-id'));
  }),

  http.patch('*/rest/v1/notifications', () => {
    return HttpResponse.json({
      id: 'notification-1',
      is_read: true,
      updated_at: new Date().toISOString()
    });
  }),

  // Subscriptions
  http.get('*/rest/v1/subscriptions', () => {
    return HttpResponse.json([
      {
        id: 'subscription-1',
        user_id: 'test-user-id',
        robot_id: 'robot-1',
        started_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        is_active: true
      }
    ]);
  }),

  http.post('*/rest/v1/subscriptions', () => {
    return HttpResponse.json({
      id: 'new-subscription-id',
      user_id: 'test-user-id',
      robot_id: 'robot-1',
      started_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      is_active: true
    });
  }),

  // Performance metrics
  http.get('*/rest/v1/order_transmission_metrics', () => {
    return HttpResponse.json([
      {
        id: 'metric-1',
        trade_id: 1001,
        signal_source: 'TradingView',
        signal_type: 'BUY',
        processing_status: 'success',
        json_parsing_time_ms: 5.2,
        transformation_time_ms: 12.8,
        webhook_delivery_time_ms: 45.6,
        total_processing_time_ms: 63.6,
        created_at: new Date().toISOString()
      },
      {
        id: 'metric-2',
        trade_id: 1002,
        signal_source: 'TradingView',
        signal_type: 'SELL',
        processing_status: 'success',
        json_parsing_time_ms: 4.8,
        transformation_time_ms: 11.2,
        webhook_delivery_time_ms: 38.9,
        total_processing_time_ms: 54.9,
        created_at: new Date().toISOString()
      }
    ]);
  }),

  // Edge Functions
  http.post('*/functions/v1/algobir-webhook-listener', () => {
    return HttpResponse.json({
      success: true,
      message: 'Signal processed successfully',
      trade_id: 'new-trade-id',
      processing_time_ms: 45.2
    });
  }),

  http.post('*/functions/v1/seller-signal-endpoint', () => {
    return HttpResponse.json({
      success: true,
      message: 'BRO-ROBOT signal processed',
      subscribers_notified: 5,
      processing_time_ms: 78.5
    });
  }),

  http.post('*/functions/v1/signal-relay-function', () => {
    return HttpResponse.json({
      success: true,
      message: 'Signal relayed to subscribers',
      trades_created: 3,
      processing_time_ms: 92.1
    });
  }),

  http.get('*/functions/v1/performance-dashboard-optimized', () => {
    return HttpResponse.json({
      timestamp: new Date().toISOString(),
      metrics: {
        total_requests: 150,
        successful_requests: 142,
        success_rate: 94.67,
        avg_processing_time_ms: 65.4
      },
      health: {
        overall_status: 'healthy',
        services: {
          database: { status: 'healthy', response_time_ms: 12.3 },
          cache: { status: 'healthy', size: 45 }
        }
      }
    });
  }),

  // Error scenarios for testing
  http.get('*/rest/v1/error-test', () => {
    return HttpResponse.json(
      { error: 'Test error', message: 'This is a test error' },
      { status: 500 }
    );
  }),

  http.get('*/rest/v1/unauthorized-test', () => {
    return HttpResponse.json(
      { error: 'Unauthorized', message: 'Authentication required' },
      { status: 401 }
    );
  }),

  // Catch-all handler for unhandled requests
  http.all('*', ({ request }) => {
    console.warn(`Unhandled ${request.method} request to ${request.url}`);
    return HttpResponse.json(
      { error: 'Not found', message: 'This endpoint is not mocked' },
      { status: 404 }
    );
  })
];

// Create and export the server
export const server = setupServer(...handlers);

// Export handlers for individual test customization
export { handlers };

// Export mock data generators for test utilities
export {
  generateMockUser,
  generateMockRobot,
  generateMockTrade,
  generateMockNotification
};
