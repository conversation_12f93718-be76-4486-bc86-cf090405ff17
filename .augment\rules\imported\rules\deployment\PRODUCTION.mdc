---
type: "manual"
priority: "high"
scope: ["deployment", "production", "ci-cd", "monitoring", "environment"]
last_updated: "2025-01-30"
---

# Production Deployment Rules

## 🚀 CI/CD Pipeline Implementation (2025-01-30) ✅

### GitHub Actions Workflow
Complete CI/CD pipeline implemented with:
- **Automated Testing**: TypeScript check, unit tests, E2E tests
- **Build Validation**: Successful build verification before deployment
- **Staging Deployment**: Automatic deployment to staging on develop branch
- **Production Deployment**: Automatic deployment to production on main branch
- **Quality Gates**: Tests must pass before deployment proceeds

### Vercel Configuration Enhanced
```json
// algobir-app-frontend/vercel.json - Complete configuration
{
  "version": 2,
  "name": "algobir-app-frontend",
  "framework": "vite",
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "installCommand": "npm ci",
  "devCommand": "npm run dev",
  "rewrites": [{"source": "/(.*)", "destination": "/index.html"}],
  "headers": [
    {
      "source": "/assets/(.*)",
      "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]
    },
    {
      "source": "/(.*)",
      "headers": [
        {"key": "X-Content-Type-Options", "value": "nosniff"},
        {"key": "X-Frame-Options", "value": "DENY"},
        {"key": "X-XSS-Protection", "value": "1; mode=block"},
        {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"},
        {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}
      ]
    }
  ],
  "regions": ["iad1"],
  "github": {"enabled": true, "autoAlias": false}
}
```

### Build Cache Optimization
Advanced caching strategies implemented:
- **Dependency Caching**: 7-day TTL for node_modules
- **TypeScript Cache**: 24-hour TTL for compilation cache
- **Asset Cache**: 30-day TTL for optimized assets
- **Cache Management Scripts**: Clear, stats, and selective clearing

## 🚀 Deployment Architecture

### Frontend Deployment (Vercel)
```json
// vercel.json - Production configuration
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=0, must-revalidate"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    },
    {
      "source": "/static/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ],
  "env": {
    "VITE_SUPABASE_URL": "@supabase_url",
    "VITE_SUPABASE_ANON_KEY": "@supabase_anon_key",
    "VITE_WEBHOOK_BASE_URL": "@webhook_base_url"
  }
}
```

### Backend Deployment (Supabase)
```toml
# supabase/config.toml - Production settings
project_id = "fllklckmycxcgwhboiji"

[api]
enabled = true
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[db]
major_version = 15

[db.pooler]
enabled = true
pool_mode = "transaction"
default_pool_size = 25
max_client_conn = 150

[auth]
enabled = true
site_url = "https://algobir.vercel.app"
additional_redirect_urls = ["https://algobir.com"]
jwt_expiry = 3600
enable_manual_linking = false

[storage]
enabled = true
file_size_limit = "50MiB"

[realtime]
enabled = true
max_concurrent_users = 200

# Edge Functions configuration
[functions."algobir-webhook-listener"]
verify_jwt = false

[functions."seller-signal-endpoint"]
verify_jwt = false

[functions."signal-relay-function"]
verify_jwt = false
```

## 🔧 Environment Configuration

### Environment Variables
```bash
# Production Environment Variables (.env.production)

# Supabase Configuration
VITE_SUPABASE_URL=https://fllklckmycxcgwhboiji.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Webhook Configuration
VITE_WEBHOOK_BASE_URL=https://hook.algobir.com
WEBHOOK_SECRET=your-webhook-secret-key

# External APIs
BROKER_API_URL=https://api.broker.com
BROKER_API_KEY=your-broker-api-key

# Monitoring & Analytics
VERCEL_ANALYTICS_ID=your-analytics-id
SENTRY_DSN=your-sentry-dsn

# Security
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key

# Performance Monitoring
NEW_RELIC_LICENSE_KEY=your-newrelic-key
DATADOG_API_KEY=your-datadog-key
```

### Environment Validation
```typescript
// Environment validation schema
import { z } from 'zod';

const envSchema = z.object({
  VITE_SUPABASE_URL: z.string().url(),
  VITE_SUPABASE_ANON_KEY: z.string().min(1),
  VITE_WEBHOOK_BASE_URL: z.string().url(),
  NODE_ENV: z.enum(['development', 'production', 'test']),
});

// Validate environment on startup
const validateEnvironment = () => {
  try {
    const env = envSchema.parse(import.meta.env);
    console.log('Environment validation successful');
    return env;
  } catch (error) {
    console.error('Environment validation failed:', error);
    throw new Error('Invalid environment configuration');
  }
};

// Use in main.tsx
const env = validateEnvironment();
```

## 📊 Monitoring & Observability

### Application Monitoring
```typescript
// Monitoring setup with multiple providers
import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/react';
import * as Sentry from '@sentry/react';

// Sentry configuration for error tracking
Sentry.init({
  dsn: import.meta.env.VITE_SENTRY_DSN,
  environment: import.meta.env.NODE_ENV,
  tracesSampleRate: 0.1,
  integrations: [
    new Sentry.BrowserTracing({
      routingInstrumentation: Sentry.reactRouterV6Instrumentation(
        React.useEffect,
        useLocation,
        useNavigationType,
        createRoutesFromChildren,
        matchRoutes
      ),
    }),
  ],
});

// Performance monitoring
const PerformanceMonitor = {
  trackPageLoad: (pageName: string) => {
    const loadTime = performance.now();
    
    // Send to analytics
    gtag('event', 'page_load_time', {
      page_name: pageName,
      load_time: Math.round(loadTime)
    });
    
    // Send to custom monitoring
    fetch('/api/metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        metric: 'page_load_time',
        value: loadTime,
        page: pageName,
        timestamp: Date.now()
      })
    });
  },
  
  trackUserAction: (action: string, metadata?: any) => {
    gtag('event', 'user_action', {
      action_name: action,
      ...metadata
    });
  },
  
  trackError: (error: Error, context?: any) => {
    Sentry.captureException(error, {
      contexts: { additional: context }
    });
  }
};
```

### Database Monitoring
```sql
-- Performance monitoring views
CREATE OR REPLACE VIEW performance_metrics AS
SELECT 
  'database_connections' as metric,
  COUNT(*) as value,
  NOW() as timestamp
FROM pg_stat_activity
WHERE state = 'active'

UNION ALL

SELECT 
  'avg_query_time' as metric,
  AVG(mean_exec_time) as value,
  NOW() as timestamp
FROM pg_stat_statements
WHERE calls > 100

UNION ALL

SELECT 
  'cache_hit_ratio' as metric,
  (sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read))) * 100 as value,
  NOW() as timestamp
FROM pg_statio_user_tables;

-- Monitoring function for Edge Functions
CREATE OR REPLACE FUNCTION log_performance_metric(
  p_metric_name TEXT,
  p_value NUMERIC,
  p_metadata JSONB DEFAULT '{}'
) RETURNS VOID AS $$
BEGIN
  INSERT INTO performance_logs (metric_name, value, metadata, created_at)
  VALUES (p_metric_name, p_value, p_metadata, NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'algobir-app-frontend/package-lock.json'
      
      - name: Install dependencies
        working-directory: ./algobir-app-frontend
        run: npm ci
      
      - name: Run type checking
        working-directory: ./algobir-app-frontend
        run: npm run lint
      
      - name: Run tests
        working-directory: ./algobir-app-frontend
        run: npm run test
      
      - name: Build application
        working-directory: ./algobir-app-frontend
        run: npm run build
        env:
          VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}

  deploy-frontend:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./algobir-app-frontend
          vercel-args: '--prod'

  deploy-backend:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest
      
      - name: Deploy Edge Functions
        run: |
          supabase functions deploy --project-ref ${{ secrets.SUPABASE_PROJECT_REF }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
      
      - name: Run database migrations
        run: |
          supabase db push --project-ref ${{ secrets.SUPABASE_PROJECT_REF }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
```

### Deployment Scripts
```bash
#!/bin/bash
# deploy.sh - Production deployment script

set -e

echo "🚀 Starting production deployment..."

# Frontend deployment
echo "📦 Building frontend..."
cd algobir-app-frontend
npm ci
npm run build

echo "🌐 Deploying to Vercel..."
vercel --prod

# Backend deployment
echo "🔧 Deploying Edge Functions..."
cd ../
supabase functions deploy --project-ref $SUPABASE_PROJECT_REF

echo "🗄️ Running database migrations..."
supabase db push --project-ref $SUPABASE_PROJECT_REF

# Health checks
echo "🏥 Running health checks..."
./scripts/health-check.sh

echo "✅ Deployment completed successfully!"
```

## 🏥 Health Checks & Monitoring

### Application Health Checks
```typescript
// Health check endpoints
const healthChecks = {
  async checkDatabase(): Promise<HealthStatus> {
    try {
      const start = Date.now();
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);
      
      const responseTime = Date.now() - start;
      
      return {
        status: error ? 'unhealthy' : 'healthy',
        responseTime,
        error: error?.message
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: 0,
        error: error.message
      };
    }
  },
  
  async checkEdgeFunctions(): Promise<HealthStatus> {
    try {
      const start = Date.now();
      const response = await fetch('/functions/v1/health-check');
      const responseTime = Date.now() - start;
      
      return {
        status: response.ok ? 'healthy' : 'unhealthy',
        responseTime,
        error: response.ok ? undefined : `HTTP ${response.status}`
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: 0,
        error: error.message
      };
    }
  },
  
  async checkRealtime(): Promise<HealthStatus> {
    try {
      const start = Date.now();
      const channel = supabase.channel('health-check');
      
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve({
            status: 'unhealthy',
            responseTime: 5000,
            error: 'Timeout'
          });
        }, 5000);
        
        channel.subscribe((status) => {
          clearTimeout(timeout);
          const responseTime = Date.now() - start;
          
          resolve({
            status: status === 'SUBSCRIBED' ? 'healthy' : 'unhealthy',
            responseTime,
            error: status === 'SUBSCRIBED' ? undefined : `Status: ${status}`
          });
        });
      });
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: 0,
        error: error.message
      };
    }
  }
};

// Health check API endpoint
export const healthCheckHandler = async (): Promise<Response> => {
  const checks = await Promise.all([
    healthChecks.checkDatabase(),
    healthChecks.checkEdgeFunctions(),
    healthChecks.checkRealtime()
  ]);
  
  const [database, edgeFunctions, realtime] = checks;
  
  const overallStatus = checks.every(check => check.status === 'healthy') 
    ? 'healthy' 
    : 'unhealthy';
  
  const healthReport = {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    checks: {
      database,
      edgeFunctions,
      realtime
    }
  };
  
  return new Response(JSON.stringify(healthReport), {
    status: overallStatus === 'healthy' ? 200 : 503,
    headers: { 'Content-Type': 'application/json' }
  });
};
```

### Automated Monitoring
```bash
#!/bin/bash
# health-check.sh - Automated health monitoring

HEALTH_ENDPOINT="https://algobir.vercel.app/api/health"
WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

check_health() {
  response=$(curl -s -w "%{http_code}" "$HEALTH_ENDPOINT")
  http_code="${response: -3}"
  body="${response%???}"
  
  if [ "$http_code" -eq 200 ]; then
    echo "✅ Health check passed"
    return 0
  else
    echo "❌ Health check failed (HTTP $http_code)"
    echo "Response: $body"
    
    # Send alert to Slack
    curl -X POST -H 'Content-type: application/json' \
      --data "{\"text\":\"🚨 Algobir health check failed: HTTP $http_code\"}" \
      "$WEBHOOK_URL"
    
    return 1
  fi
}

# Run health check
check_health
```

## 🔒 Production Security

### Security Headers
```typescript
// Security middleware for production
const securityHeaders = {
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' https://vercel.live",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
    "font-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ')
};
```

### Production Secrets Management
```bash
# Secrets management with Vercel CLI
vercel env add VITE_SUPABASE_URL production
vercel env add VITE_SUPABASE_ANON_KEY production
vercel env add WEBHOOK_SECRET production

# Supabase secrets
supabase secrets set BROKER_API_KEY=your-api-key --project-ref $PROJECT_REF
supabase secrets set ENCRYPTION_KEY=your-encryption-key --project-ref $PROJECT_REF
```

## 📈 Performance Optimization

### Production Build Optimization
```typescript
// vite.config.ts - Production optimizations
export default defineConfig({
  build: {
    target: 'es2020',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug']
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor-react': ['react', 'react-dom'],
          'vendor-ui': ['@chakra-ui/react', '@emotion/react'],
          'vendor-charts': ['recharts', 'chart.js'],
          'vendor-utils': ['date-fns', 'lodash']
        }
      }
    },
    chunkSizeWarningLimit: 1000,
    sourcemap: false,
    assetsInlineLimit: 4096
  }
});
```

## Update Requirements

### When to Update This File
- Deployment configuration changes
- New environment variables added
- Monitoring system updates
- Security configuration modifications
- CI/CD pipeline improvements
- Health check enhancements

### Related Files to Update
- Update `security/GUIDELINES.mdc` for production security
- Update `performance/OPTIMIZATION.mdc` for production performance
- Update `core/PROJECT_CORE.mdc` for deployment architecture
- Update `backend/SUPABASE_EDGE.mdc` for backend deployment
- Update `frontend/MAIN_APP.mdc` for frontend deployment
