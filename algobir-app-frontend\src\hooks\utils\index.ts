/**
 * Hook Utilities - Barrel Export
 * 
 * Standardized utilities for consistent hook patterns across the application
 * Provides better error handling, caching, loading states, and TypeScript support
 */

// Async operation utilities
export {
  useAsyncOperation,
  useAutoAsyncOperation,
  type AsyncOperationState,
  type AsyncOperationConfig,
  type AsyncOperationReturn
} from './useAsyncOperation';

// Caching utilities
export {
  useCache,
  useGlobalCache,
  type CacheConfig,
  type CacheOperations
} from './useCache';

// Error handling utilities
export {
  useError<PERSON>and<PERSON>,
  useDatabaseErrorHandler,
  useApiErrorHandler,
  useFormErrorHandler,
  type ErrorType,
  type ErrorInfo,
  type ErrorHandlerConfig,
  type ErrorHandlerReturn
} from './useErrorHandler';

// TypeScript utilities and types
export {
  type LoadingState,
  type BaseHookState,
  type ExtendedHookState,
  type HookConfig,
  type PaginationState,
  type PaginationControls,
  type PaginatedHookReturn,
  type FilterState,
  type FilterControls,
  type FilteredHookReturn,
  type SearchState,
  type SearchControls,
  type SearchableHookReturn,
  type CrudOperations,
  type CrudHookReturn,
  type SubscriptionState,
  type RealtimeHookReturn,
  type HookData,
  type PartialHookState,
  type LoadedHookState,
  isLoaded,
  isLoading,
  hasError,
  createInitialHookState,
  createLoadingState,
  createSuccessState,
  createErrorState
} from './types';

/**
 * Hook Utility Guidelines
 * 
 * 1. **Consistent State Management**
 *    - Use BaseHookState<T> for all data hooks
 *    - Implement loading, error, and data states consistently
 *    - Use TypeScript utilities for better type safety
 * 
 * 2. **Error Handling**
 *    - Use useErrorHandler for consistent error processing
 *    - Categorize errors by type (network, auth, validation, etc.)
 *    - Provide user-friendly error messages
 * 
 * 3. **Caching Strategy**
 *    - Use useCache for component-level caching
 *    - Use useGlobalCache for app-wide data sharing
 *    - Implement proper cache invalidation
 * 
 * 4. **Async Operations**
 *    - Use useAsyncOperation for standardized async handling
 *    - Implement retry logic with exponential backoff
 *    - Handle operation cancellation properly
 * 
 * 5. **Performance Optimization**
 *    - Cache frequently accessed data
 *    - Implement proper dependency arrays
 *    - Use abort controllers for cleanup
 * 
 * Example Usage:
 * 
 * ```typescript
 * // Enhanced hook with utilities
 * export function useEnhancedData() {
 *   const { handleError } = useDatabaseErrorHandler();
 *   const cache = useCache<DataType>({ ttl: 300000 });
 *   
 *   const operation = useCallback(async () => {
 *     try {
 *       // Check cache first
 *       const cached = cache.get('data', user?.id);
 *       if (cached) return cached;
 *       
 *       // Fetch from API
 *       const data = await fetchData();
 *       
 *       // Cache result
 *       cache.set('data', data, user?.id);
 *       
 *       return data;
 *     } catch (error) {
 *       handleError(error, { context: 'Data Fetch' });
 *       throw error;
 *     }
 *   }, [cache, handleError, user?.id]);
 *   
 *   return useAutoAsyncOperation(operation, [user?.id], {
 *     showToastOnError: true,
 *     retryAttempts: 3,
 *     cacheTimeout: 300000
 *   });
 * }
 * ```
 */
