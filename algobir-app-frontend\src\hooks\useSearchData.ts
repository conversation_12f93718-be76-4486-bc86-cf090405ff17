import { useState, useEffect, useMemo } from 'react';
import { SearchableItem } from '../utils/searchUtils';
import { useTradesData } from './useTradesData';
import { useMarketplaceData } from './useMarketplaceData';
import { supabase } from '../supabaseClient';

/**
 * Comprehensive search data provider
 * Aggregates all searchable content from various sources
 */
export function useSearchData() {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get data from existing hooks
  const { trades } = useTradesData();
  const { robots } = useMarketplaceData();
  
  // Fetch users data
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('id, full_name, username, bio')
          .limit(100);
        
        if (error) throw error;
        setUsers(data || []);
      } catch (err: any) {
        console.error('Error fetching users:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUsers();
  }, []);
  
  // Static page data
  const pages: SearchableItem[] = useMemo(() => [
    {
      id: 'dashboard',
      title: 'Dashboard',
      subtitle: 'Ana sayfa ve genel bakış',
      description: 'Hesap özeti, son işlemler ve performans metrikleri',
      keywords: ['ana sayfa', 'özet', 'genel bakış', 'dashboard'],
      type: 'page',
      url: '/'
    },
    {
      id: 'trades',
      title: 'İşlemler',
      subtitle: 'Tüm alım satım işlemleri',
      description: 'Geçmiş ve aktif işlemlerinizi görüntüleyin, filtreleyin ve yönetin',
      keywords: ['işlem', 'alım', 'satım', 'trade', 'pozisyon'],
      type: 'page',
      url: '/trades'
    },
    {
      id: 'open-positions',
      title: 'Açık İşlemler',
      subtitle: 'Aktif pozisyonlar',
      description: 'Şu anda açık olan pozisyonlarınızı görüntüleyin ve yönetin',
      keywords: ['açık', 'pozisyon', 'aktif', 'open'],
      type: 'page',
      url: '/open-positions'
    },
    {
      id: 'statistics',
      title: 'İstatistikler',
      subtitle: 'Performans analizi ve raporlar',
      description: 'Detaylı performans metrikleri, kar/zarar analizi ve istatistikler',
      keywords: ['istatistik', 'analiz', 'performans', 'rapor', 'kar', 'zarar'],
      type: 'page',
      url: '/statistics'
    },
    {
      id: 'marketplace',
      title: 'Robot Pazarı',
      subtitle: 'Ticaret robotları',
      description: 'Ticaret robotlarını keşfedin, satın alın ve yönetin',
      keywords: ['robot', 'pazar', 'marketplace', 'bot', 'algoritma'],
      type: 'page',
      url: '/marketplace'
    },
    {
      id: 'seller-robots',
      title: 'Robotlarım',
      subtitle: 'Sahip olduğunuz robotlar',
      description: 'Satın aldığınız ve oluşturduğunuz robotları yönetin',
      keywords: ['robotlarım', 'sahip', 'yönetim', 'my robots'],
      type: 'page',
      url: '/seller/robots'
    },
    {
      id: 'management',
      title: 'Yönetim',
      subtitle: 'Hesap ve sistem yönetimi',
      description: 'Hesap ayarları, API anahtarları ve sistem konfigürasyonu',
      keywords: ['yönetim', 'ayar', 'api', 'konfigürasyon', 'management'],
      type: 'page',
      url: '/management'
    },
    {
      id: 'profile',
      title: 'Profil',
      subtitle: 'Kullanıcı profili',
      description: 'Profil bilgilerinizi görüntüleyin ve düzenleyin',
      keywords: ['profil', 'kullanıcı', 'bilgi', 'profile'],
      type: 'page',
      url: '/profile'
    },
    {
      id: 'guide',
      title: 'Rehber',
      subtitle: 'Kullanım kılavuzu',
      description: 'Platform kullanımı hakkında detaylı rehber ve yardım',
      keywords: ['rehber', 'yardım', 'kılavuz', 'guide', 'help'],
      type: 'page',
      url: '/guide'
    },
    {
      id: 'admin',
      title: 'Admin Paneli',
      subtitle: 'Yönetici paneli',
      description: 'Sistem yönetimi ve admin işlemleri',
      keywords: ['admin', 'yönetici', 'panel', 'sistem'],
      type: 'page',
      url: '/admin'
    }
  ], []);
  
  // Documentation/Help content
  const documentation: SearchableItem[] = useMemo(() => [
    {
      id: 'help-getting-started',
      title: 'Başlangıç Rehberi',
      subtitle: 'Platform kullanımına başlayın',
      description: 'Algobir platformunu kullanmaya başlamak için temel adımlar',
      keywords: ['başlangıç', 'başla', 'getting started', 'ilk adım'],
      type: 'help',
      url: '/guide'
    },
    {
      id: 'help-robots',
      title: 'Robot Kullanımı',
      subtitle: 'Ticaret robotları nasıl kullanılır',
      description: 'Solo-Robot ve Bro-Robot kullanım kılavuzu',
      keywords: ['robot', 'solo', 'bro', 'kullanım', 'bot'],
      type: 'help',
      url: '/guide/solo-robot'
    },
    {
      id: 'help-api',
      title: 'API Entegrasyonu',
      subtitle: 'API anahtarları ve webhook kurulumu',
      description: 'Broker API anahtarlarını nasıl ekleyeceğinizi öğrenin',
      keywords: ['api', 'anahtar', 'webhook', 'entegrasyon', 'broker'],
      type: 'help',
      url: '/management'
    },
    {
      id: 'help-trading',
      title: 'İşlem Yönetimi',
      subtitle: 'Alım satım işlemlerini yönetin',
      description: 'İşlemlerinizi nasıl takip edeceğinizi ve yöneteceğinizi öğrenin',
      keywords: ['işlem', 'yönetim', 'takip', 'trading'],
      type: 'help',
      url: '/trades'
    }
  ], []);
  
  // Convert trades to searchable items
  const tradeItems: SearchableItem[] = useMemo(() => {
    if (!trades) return [];
    
    return trades.slice(0, 50).map(trade => ({
      id: `trade-${trade.id}`,
      title: `${trade.symbol} ${trade.order_side || 'İşlem'}`,
      subtitle: `${trade.calculated_quantity} - ${new Date(trade.created_at!).toLocaleDateString('tr-TR')}`,
      description: `${trade.system_name || 'Sistem'} tarafından gerçekleştirilen ${trade.order_side} işlemi`,
      keywords: [trade.symbol, trade.order_side || '', trade.system_name || '', trade.status || ''],
      type: 'trade',
      url: `/trades?search=${trade.symbol}`,
      badge: trade.status,
      metadata: trade
    }));
  }, [trades]);
  
  // Convert robots to searchable items
  const robotItems: SearchableItem[] = useMemo(() => {
    if (!robots) return [];
    
    return robots.map(robot => ({
      id: `robot-${robot.id}`,
      title: robot.name,
      subtitle: robot.description || 'Robot açıklaması mevcut değil',
      description: robot.description || '',
      keywords: [robot.name, robot.is_public ? 'public' : 'private', 'robot', 'bot'],
      type: 'robot',
      url: `/marketplace/robot/${robot.id}`,
      badge: robot.is_public ? 'Herkese Açık' : 'Özel',
      metadata: robot
    }));
  }, [robots]);
  
  // Convert users to searchable items
  const userItems: SearchableItem[] = useMemo(() => {
    return users.map(user => ({
      id: `user-${user.id}`,
      title: user.full_name || user.username || 'İsimsiz Kullanıcı',
      subtitle: user.bio || 'Kullanıcı profili',
      description: user.bio || '',
      keywords: [user.full_name || '', user.username || '', 'kullanıcı', 'user'],
      type: 'user',
      url: `/user/${user.id}`,
      metadata: user
    }));
  }, [users]);
  
  // Combine all searchable items
  const allItems: SearchableItem[] = useMemo(() => [
    ...pages,
    ...documentation,
    ...robotItems,
    ...tradeItems,
    ...userItems
  ], [pages, documentation, robotItems, tradeItems, userItems]);
  
  return {
    allItems,
    pages,
    documentation,
    robots: robotItems,
    trades: tradeItems,
    users: userItems,
    loading,
    error
  };
}
