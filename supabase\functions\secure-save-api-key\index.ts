// supabase/functions/secure-save-api-key/index.ts
// Kullanıcıların API anahtarlarını ve tokenlarını güvenli bir şekilde Vault'a kaydeder
console.log('[secure-save-api-key] Handler starting.');
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { getCorsHeaders } from '../_shared/cors.ts';
import { upsertVaultSecret } from '../_shared/getVaultSecret.ts';
console.log('[secure-save-api-key] Imports successful.');
serve(async (req)=>{
  const functionStartTime = Date.now();
  console.log(`[secure-save-api-key] Request received: ${req.method} ${req.url}`);
  
  // Header bilgilerini loglama
  const contentType = req.headers.get('Content-Type');
  const contentLength = req.headers.get('Content-Length');
  console.log(`[secure-save-api-key] Content-Type: ${contentType}`);
  console.log(`[secure-save-api-key] Content-Length: ${contentLength}`);
  
  // Dinamik CORS başlıklarını oluştur
  const corsHeaders = getCorsHeaders(req.headers);
  const responseHeaders = new Headers(corsHeaders);
  responseHeaders.set('Content-Type', 'application/json');
  // CORS OPTIONS isteğini işle
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: responseHeaders
    });
  }
  // Ortam değişkenlerini kontrol et
  const supabaseUrl = Deno.env.get('SUPABASE_URL');
  const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
  const projectRefId = Deno.env.get('PROJECT_REF_ID');
  const managementApiToken = Deno.env.get('MANAGEMENT_API_TOKEN');
  if (!supabaseUrl || !serviceRoleKey || !projectRefId || !managementApiToken) {
    const missing = [
      !supabaseUrl && 'URL',
      !serviceRoleKey && 'SRV_KEY',
      !projectRefId && 'REF_ID',
      !managementApiToken && 'MGMT_TOKEN'
    ].filter(Boolean).join(', ');
    console.error(`[secure-save-api-key] FATAL: Missing environment variables: ${missing}`);
    return new Response(JSON.stringify({
      success: false,
      error: `Internal server config error. Missing: ${missing}`
    }), {
      status: 500,
      headers: responseHeaders
    });
  }
  let userId = null;
  try {
    console.log('[secure-save-api-key] Authenticating user...');
    // JWT token'dan kullanıcıyı doğrula
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new Error('Missing or invalid Authorization header.');
    }
    const jwt = authHeader.replace('Bearer ', '');
    const supabaseAuthClient = createClient(supabaseUrl, serviceRoleKey);
    const { data, error: userError } = await supabaseAuthClient.auth.getUser(jwt);
    if (userError || !data.user) {
      throw userError || new Error('Invalid token or user not found.');
    }
    
    // Kullanıcı bilgilerini detaylı logla
    console.log(`[secure-save-api-key] User auth data:`, {
      id: data.user.id,
      email: data.user.email,
      created_at: data.user.created_at,
      updated_at: data.user.updated_at
    });
    
    userId = data.user.id;
    console.log(`[secure-save-api-key] User authenticated: ${userId}`);
    console.log(`[secure-save-api-key] User ID type: ${typeof userId}, length: ${userId.length}`);
    
    // Kullanıcı ID'sinin geçerli bir UUID olup olmadığını kontrol et
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(userId)) {
      console.error(`[secure-save-api-key] WARNING: User ID does not appear to be a valid UUID: ${userId}`);
    }
    
    // İstek gövdesini güvenli bir şekilde işle
    let apiKeyPresent = false;
    let tokenPresent = false;
    let apiKeyToSave = null;
    let tokenToSave = null;
    let requestBodyError = null;
    
    try {
      // Önce raw text olarak al
      const rawBody = await req.text();
      console.log(`[secure-save-api-key] Raw request body length: ${rawBody.length} bytes`);
      
      // Sadece loglama amaçlı - içeriği maskeleyin
      if (rawBody.length > 0) {
        const sanitizedLog = rawBody.length > 100 
          ? `${rawBody.substring(0, 20)}...${rawBody.substring(rawBody.length - 20)}` 
          : '[Redacted for security]';
        console.log(`[secure-save-api-key] Raw request body preview: ${sanitizedLog}`);
      } else {
        console.log('[secure-save-api-key] Raw request body is empty');
        requestBodyError = 'Request body cannot be empty. API credentials required.';
      }
      
      // İçerik boş değilse parse et
      if (rawBody && rawBody.trim() !== "") {
        const body = JSON.parse(rawBody);
        
        // apiKey alanının varlığını ve değerini kontrol et
        apiKeyPresent = 'apiKey' in body;
        apiKeyToSave = body.apiKey; // null veya "" olması durumunda bile değeri al
        
        // token alanının varlığını ve değerini kontrol et
        tokenPresent = 'token' in body;
        tokenToSave = body.token; // null veya "" olması durumunda bile değeri al
        
        console.log(`[secure-save-api-key] Parsed body - apiKey field exists: ${apiKeyPresent}, token field exists: ${tokenPresent}`);
        console.log(`[secure-save-api-key] Parsed body - apiKey value type: ${apiKeyToSave === null ? 'null' : typeof apiKeyToSave}`);
        console.log(`[secure-save-api-key] Parsed body - token value type: ${tokenToSave === null ? 'null' : typeof tokenToSave}`);
      } else {
        console.log('[secure-save-api-key] Request body is empty or whitespace.');
        requestBodyError = 'Request body cannot be empty. API credentials required.';
      }
    } catch (e) {
      console.error('[secure-save-api-key] Error parsing request body:', e.message);
      requestBodyError = `Invalid JSON in request body: ${e.message}`;
    }
    
    // Request body hatası varsa döndür
    if (requestBodyError) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: requestBodyError 
      }), {
        headers: responseHeaders,
        status: 400
      });
    }
    
    // En az bir alan gönderilip gönderilmediğini kontrol et
    if (!apiKeyPresent && !tokenPresent) {
      const errorMsg = 'No API key or token field provided in request.';
      console.error(`[secure-save-api-key] ${errorMsg}`);
      return new Response(JSON.stringify({ 
        success: false, 
        error: errorMsg 
      }), {
        headers: responseHeaders,
        status: 400
      });
    }
    
    console.log(`[secure-save-api-key] Processing request for user ${userId}`);
    console.log(`[secure-save-api-key] - apiKey field present: ${apiKeyPresent}, value to process: ${apiKeyToSave === null ? 'null' : (apiKeyToSave === '' ? 'empty string' : 'non-empty value')}`);
    console.log(`[secure-save-api-key] - token field present: ${tokenPresent}, value to process: ${tokenToSave === null ? 'null' : (tokenToSave === '' ? 'empty string' : 'non-empty value')}`);
    
    // DB'de güncellenecek alanlar
    const updates = {};
    const vaultOps = [];
    let vaultOpsOverallSuccess = true;
    const vaultErrors = [];
    
    // API Key alanı gönderildiyse işle
    if (apiKeyPresent) {
      // Doğru userId kullanımı
      if (!userId || typeof userId !== 'string' || userId.length < 10) {
        console.error(`[secure-save-api-key] Invalid userId: ${userId}`);
        vaultOpsOverallSuccess = false;
        vaultErrors.push(`Invalid userId: ${userId}`);
      } else {
        // ÖNEMLİ: Secret name'i doğru formatta oluştur - user_ACTUAL_UUID_api_key
        const secretNameApiKey = `user_${userId}_api_key`;
        
        if (apiKeyToSave === null || apiKeyToSave === '') {
          // API Key'i silmek istiyoruz (değer null veya boş string ise)
          console.log(`[secure-save-api-key] User ${userId} requested to clear API key`);
          updates.api_key_set = false;
          
          // Vault'ta boş string ile güncelle (silme işlemi)
          const result = await upsertVaultSecret(projectRefId, managementApiToken, secretNameApiKey, '');
          vaultOps.push({ type: 'api_key', action: 'clear', result });
          
          if (!result.success) {
            vaultOpsOverallSuccess = false;
            vaultErrors.push(`API Key Clear: ${result.error?.message || 'Vault Error'}`);
          }
        } else {
          // API Key'i kaydet (değer bir string)
          console.log(`[secure-save-api-key] User ${userId} provided API key to save`);
          updates.api_key_set = true;
          
          // Debug: Secret name'i logla
          console.log(`[secure-save-api-key] DEBUG: Using secret name for API key: ${secretNameApiKey}`);
          
          // Vault'a kaydet
          const result = await upsertVaultSecret(projectRefId, managementApiToken, secretNameApiKey, apiKeyToSave);
          vaultOps.push({ type: 'api_key', action: 'save', result });
          
          if (!result.success) {
            vaultOpsOverallSuccess = false;
            vaultErrors.push(`API Key Save: ${result.error?.message || 'Vault Error'}`);
          }
        }
      }
    }
    
    // Token alanı gönderildiyse işle
    if (tokenPresent) {
      // Doğru userId kullanımı
      if (!userId || typeof userId !== 'string' || userId.length < 10) {
        console.error(`[secure-save-api-key] Invalid userId: ${userId}`);
        vaultOpsOverallSuccess = false;
        vaultErrors.push(`Invalid userId: ${userId}`);
      } else {
        // ÖNEMLİ: Secret name'i doğru formatta oluştur - user_ACTUAL_UUID_token
        const secretNameToken = `user_${userId}_token`;
        
        if (tokenToSave === null || tokenToSave === '') {
          // Token'ı silmek istiyoruz (değer null veya boş string ise)
          console.log(`[secure-save-api-key] User ${userId} requested to clear token`);
          updates.token_set = false;
          
          // Vault'ta boş string ile güncelle (silme işlemi)
          const result = await upsertVaultSecret(projectRefId, managementApiToken, secretNameToken, '');
          vaultOps.push({ type: 'token', action: 'clear', result });
          
          if (!result.success) {
            vaultOpsOverallSuccess = false;
            vaultErrors.push(`Token Clear: ${result.error?.message || 'Vault Error'}`);
          }
        } else {
          // Token'ı kaydet (değer bir string)
          console.log(`[secure-save-api-key] User ${userId} provided token to save`);
          updates.token_set = true;
          
          // Debug: Secret name'i logla
          console.log(`[secure-save-api-key] DEBUG: Using secret name for token: ${secretNameToken}`);
          
          // Vault'a kaydet
          const result = await upsertVaultSecret(projectRefId, managementApiToken, secretNameToken, tokenToSave);
          vaultOps.push({ type: 'token', action: 'save', result });
          
          if (!result.success) {
            vaultOpsOverallSuccess = false;
            vaultErrors.push(`Token Save: ${result.error?.message || 'Vault Error'}`);
          }
        }
      }
    }
    
    // Vault işlemleri sonucunu kontrol et
    if (!vaultOpsOverallSuccess) {
      console.error(`[secure-save-api-key] Vault operations failed for user ${userId}. Errors: ${vaultErrors.join('; ')}`);
      return new Response(JSON.stringify({
        success: false,
        error: `Failed to save secrets to Vault.`,
        details: vaultErrors.join('; ')
      }), {
        headers: responseHeaders,
        status: 500
      });
    }
    
    // Vault işlemleri sonuçlarını detaylı logla
    console.log(`[secure-save-api-key] Vault operations summary for user ${userId}:`);
    vaultOps.forEach(op => {
      console.log(`[secure-save-api-key] - ${op.type} ${op.action}: ${op.result.success ? 'Success' : 'Failed'}`);
    });
    
    // DB'deki kullanıcı ayarlarını güncelle
    let updateFlagsError = null;
    if (Object.keys(updates).length > 0) {
      console.log(`[secure-save-api-key] Updating user_settings flags for user: ${userId}`, updates);
      const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey);
      const { error: dbError } = await supabaseAdmin.from('user_settings').update(updates).eq('id', userId);
      if (dbError) {
        updateFlagsError = dbError;
        console.error(`[secure-save-api-key] WARN: Failed user_settings flag update for user ${userId}:`, JSON.stringify(dbError));
      } else {
        console.log(`[secure-save-api-key] Success user_settings flag update for user ${userId}.`);
      }
    } else {
      console.log(`[secure-save-api-key] No user_settings flags to update for user: ${userId}`);
    }
    
    // Sonuç mesajını hazırla
    const successMessage = updateFlagsError 
      ? 'Secrets processed, but status flags update failed.' 
      : 'Secrets processed successfully.';
      
    // Yapılan işlemleri özet olarak hazırla
    const summary = {
      api_key: apiKeyPresent 
        ? (apiKeyToSave === null || apiKeyToSave === '' ? 'cleared' : 'saved') 
        : 'unchanged',
      token: tokenPresent 
        ? (tokenToSave === null || tokenToSave === '' ? 'cleared' : 'saved') 
        : 'unchanged'
    };
    
    console.log(`[secure-save-api-key] Operation completed for user ${userId}. Summary:`, summary);
    const endTime = Date.now();
    console.log(`[secure-save-api-key] Total execution time: ${endTime - functionStartTime}ms`);
    
    return new Response(JSON.stringify({
      success: true,
      message: successMessage,
      summary: summary,
      warning: updateFlagsError ? `Flags update failed: ${updateFlagsError.message}` : null
    }), {
      headers: responseHeaders,
      status: 200
    });
  } catch (error) {
    console.error('[secure-save-api-key] Unhandled Exception:', error.message, error.stack);
    
    // Hata tipine göre HTTP durum kodu belirle
    const statusCode = error.message?.includes('Authorization') || error.message?.includes('token') 
      ? 401  // Yetkilendirme hatası
      : error.message?.includes('payload') || error.message?.includes('required') 
        ? 400  // İstek hatası
        : 500; // Sunucu hatası
    
    const errorResponsePayload = {
      success: false,
      error: statusCode === 500 
        ? 'An unexpected server error occurred.' 
        : error.message || 'Unknown error',
      details: statusCode === 500 ? error.message : undefined
    };
    
    const endTime = Date.now();
    console.log(`[secure-save-api-key] Function failed with status ${statusCode}. Total execution time: ${endTime - functionStartTime}ms`);
    
    return new Response(JSON.stringify(errorResponsePayload), {
      status: statusCode,
      headers: responseHeaders
    });
  }
});
console.log('[secure-save-api-key] Function handler setup complete.');
