import { test, expect } from '@playwright/test';
import { 
  loginUser, 
  goToNotifications, 
  createTestNotification,
  waitForPageLoad,
  expectToast,
  disableDebugMode
} from './utils/test-helpers';

test.describe('Bildirim Sistemi', () => {
  test.beforeEach(async ({ page }) => {
    await loginUser(page);
    await waitForPageLoad(page);
  });

  test('Bildirimler sayfası yükleniyor', async ({ page }) => {
    await goToNotifications(page);
    
    // Say<PERSON> başlığını kontrol et
    await expect(page.locator('h1').first()).toContainText('Bildirimler');
    
    // Bildirim listesinin görünür olduğunu kontrol et
    await expect(page.locator('[data-testid="notifications-list"]')).toBeVisible();
  });

  test('Mevcut bildirimler görüntüleniyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // En az bir bildirim olduğunu kontrol et
    const notifications = page.locator('[data-testid="notification-item"]');
    await expect(notifications.first()).toBeVisible();
    
    // Bildirim içeriğini kontrol et
    const firstNotification = notifications.first();
    await expect(firstNotification.locator('[data-testid="notification-title"]')).toBeVisible(); // Başlık
    await expect(firstNotification.locator('[data-testid="notification-message"]')).toBeVisible(); // Mesaj
  });

  test('Bildirim türleri doğru ikonlarla görüntüleniyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Trade bildirimleri için ikon kontrolü
    const tradeNotifications = page.locator('[data-testid="notification-item"]:has([data-testid*="trade"])');
    if (await tradeNotifications.count() > 0) {
      await expect(tradeNotifications.first().locator('img')).toBeVisible();
    }
    
    // Admin bildirimleri için ikon kontrolü
    const adminNotifications = page.locator('[data-testid="notification-item"]:has([data-testid*="admin"])');
    if (await adminNotifications.count() > 0) {
      await expect(adminNotifications.first().locator('img')).toBeVisible();
    }
  });

  test('Bildirim silme işlevi çalışıyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // İlk bildirimi bul
    const firstNotification = page.locator('[data-testid="notification-item"]').first();
    await expect(firstNotification).toBeVisible();
    
    // Silme butonunu bul ve tıkla
    const deleteButton = firstNotification.locator('button[aria-label*="sil"]');
    if (await deleteButton.isVisible()) {
      await deleteButton.click();
      
      // Onay dialogunu bekle ve onayla
      await page.waitForTimeout(500);
      // Toast mesajını kontrol et (eğer varsa)
    }
  });

  test('RPC test bildirimi oluşturuluyor', async ({ page }) => {
    await goToNotifications(page);
    
    // Mevcut bildirim sayısını al
    const initialCount = await page.locator('[data-testid="notification-item"]').count();
    
    // Test bildirimi oluştur
    await createTestNotification(page);
    
    // Sayfayı yenile
    await page.reload();
    await waitForPageLoad(page);
    
    // Yeni bildirim sayısını kontrol et
    const newCount = await page.locator('[data-testid="notification-item"]').count();
    expect(newCount).toBeGreaterThanOrEqual(initialCount);
  });

  test('Bildirim formatları doğru görüntüleniyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Trade bildirimi formatını kontrol et
    const tradeNotification = page.locator('[data-testid="notification-item"]').first();
    const message = await tradeNotification.locator('[data-testid="notification-message"]').textContent();
    
    if (message && message.includes('TL')) {
      // Türk para formatının doğru olduğunu kontrol et
      expect(message).toMatch(/\d+,\d+ TL/);
      expect(message).toMatch(/₺[\d,]+/);
    }
  });

  test('Daha fazla yükle butonu çalışıyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Mevcut bildirim sayısını al
    const initialCount = await page.locator('[data-testid="notification-item"]').count();
    
    // Daha fazla yükle butonunu bul
    const loadMoreButton = page.locator('button:has-text("Daha Fazla Yükle")');
    
    if (await loadMoreButton.isVisible()) {
      await loadMoreButton.click();
      await waitForPageLoad(page);
      
      // Yeni bildirim sayısını kontrol et
      const newCount = await page.locator('[data-testid="notification-item"]').count();
      expect(newCount).toBeGreaterThanOrEqual(initialCount);
    }
  });

  test('Bildirim detay sayfası açılıyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // İlk bildirimin aksiyon linkini bul
    const firstNotification = page.locator('[data-testid="notification-item"]').first();
    const actionLink = firstNotification.locator('a');
    
    if (await actionLink.isVisible()) {
      const href = await actionLink.getAttribute('href');
      if (href) {
        await actionLink.click();
        
        // Yönlendirilen sayfanın yüklenmesini bekle
        await waitForPageLoad(page);
        
        // URL'nin değiştiğini kontrol et
        expect(page.url()).toContain(href);
      }
    }
  });

  test('Bildirim arama ve filtreleme', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Arama alanı varsa test et
    const searchInput = page.locator('input[placeholder*="ara"]');
    if (await searchInput.isVisible()) {
      await searchInput.fill('test');
      await waitForPageLoad(page);
      
      // Filtrelenmiş sonuçları kontrol et
      const filteredNotifications = page.locator('[data-testid="notification-item"]');
      const count = await filteredNotifications.count();
      
      if (count > 0) {
        // En az bir sonuç "test" kelimesini içermeli
        const firstResult = filteredNotifications.first();
        const text = await firstResult.textContent();
        expect(text?.toLowerCase()).toContain('test');
      }
    }
  });
});
