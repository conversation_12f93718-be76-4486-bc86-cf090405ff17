import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  VStack,
  Heading,
  Text,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  Button,
  Avatar,
  IconButton,
  useColorModeValue,
  Alert,
  AlertIcon,
  AlertDescription,
  useToast,
  Flex,
  SimpleGrid,
  Stack,
  useBreakpointValue,
  Textarea,
  Switch,
  Divider,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure
} from '@chakra-ui/react';
import { FiEdit2, FiUpload, FiSave, FiX, FiLock, FiKey } from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import Card from '../components/card/Card';
import { supabase } from '../supabaseClient';

interface ProfileFormData {
  username: string;
  full_name: string;
  avatar_url: string;
  bio: string;
  display_full_name_publicly: boolean;
  display_bio_publicly: boolean;
  display_robots_publicly: boolean;
  url_slug?: string;
}

const Profile: React.FC = () => {
  const { user } = useAuth();
  const toast = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // State yönetimi
  const [profile, setProfile] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [bioAutoSaveStatus, setBioAutoSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle');
  const bioTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Password change modal
  const { isOpen: isPasswordModalOpen, onOpen: onPasswordModalOpen, onClose: onPasswordModalClose } = useDisclosure();
  const [passwordData, setPasswordData] = useState({
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordLoading, setPasswordLoading] = useState(false);

  const [formData, setFormData] = useState<ProfileFormData>({
    username: '',
    full_name: '',
    avatar_url: '',
    bio: '',
    display_full_name_publicly: false,
    display_bio_publicly: false,
    display_robots_publicly: true,
    url_slug: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Profil verilerini çek
  useEffect(() => {
    const fetchProfile = async () => {
      if (!user?.id) return;
      
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Profil çekme hatası:', error);
          return;
        }

        setProfile(data);
        setFormData({
          username: data?.username || '',
          full_name: data?.full_name || '',
          avatar_url: data?.avatar_url || '',
          bio: data?.bio || '',
          display_full_name_publicly: data?.display_full_name_publicly || false,
          display_bio_publicly: data?.display_bio_publicly || false,
          display_robots_publicly: data?.display_robots_publicly !== undefined ? data.display_robots_publicly : true,
          url_slug: data?.url_slug || ''
        });
      } catch (error) {
        console.error('Profil yükleme hatası:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [user?.id]);

  // Bio auto-save functionality (following MDC security and UX guidelines)
  useEffect(() => {
    if (!profile || !isEditing) return;

    // Clear existing timeout
    if (bioTimeoutRef.current) {
      clearTimeout(bioTimeoutRef.current);
    }

    // Only auto-save if bio has changed from original
    if (formData.bio !== profile.bio) {
      setBioAutoSaveStatus('idle');

      // Set new timeout for auto-save (2 seconds after user stops typing)
      bioTimeoutRef.current = setTimeout(async () => {
        setBioAutoSaveStatus('saving');

        try {
          // Sanitize bio before saving (following security guidelines)
          const sanitizedBio = formData.bio
            .replace(/[<>]/g, '')           // Remove HTML tags
            .replace(/javascript:/gi, '')   // Remove javascript: protocol
            .trim()
            .slice(0, 500);                // Enforce length limit

          const { error } = await supabase
            .from('profiles')
            .update({
              bio: sanitizedBio || null,
              updated_at: new Date().toISOString()
            })
            .eq('id', user?.id);

          if (error) throw error;

          // Update local profile state
          setProfile((prev: any) => prev ? { ...prev, bio: sanitizedBio } : null);
          setBioAutoSaveStatus('saved');

          // Reset status after 2 seconds
          setTimeout(() => setBioAutoSaveStatus('idle'), 2000);

        } catch (error) {
          console.error('Bio auto-save failed:', error);
          setBioAutoSaveStatus('idle');
        }
      }, 2000);
    }

    // Cleanup timeout on unmount
    return () => {
      if (bioTimeoutRef.current) {
        clearTimeout(bioTimeoutRef.current);
      }
    };
  }, [formData.bio, profile?.bio, isEditing, user?.id]);

  // Responsive değerler
  const cardPadding = useBreakpointValue({ base: '16px', sm: '20px', md: '24px' });
  const buttonSize = useBreakpointValue({ base: 'sm', md: 'md' });
  const inputHeight = useBreakpointValue({ base: '44px', md: '48px' });
  const fontSize = useBreakpointValue({ base: 'sm', md: 'md' });

  // Horizon UI renk tema değerleri
  const textColor = useColorModeValue('navy.700', 'white');
  const textColorSecondary = useColorModeValue('secondaryGray.600', 'white');
  const brandColor = useColorModeValue('brand.500', 'white');
  const borderColor = useColorModeValue('secondaryGray.200', 'whiteAlpha.100');
  const inputBg = useColorModeValue('secondaryGray.300', 'navy.900');
  const cardShadow = useColorModeValue("0px 18px 40px rgba(112, 144, 176, 0.12)", "none");
  const cardBg = useColorModeValue('white', 'navy.800');

  // Form değerlerini güncelle
  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Hata varsa temizle
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Form doğrulama
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Kullanıcı adı zorunludur';
    } else if (formData.username.length < 3) {
      newErrors.username = 'Kullanıcı adı en az 3 karakter olmalıdır';
    }

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Ad Soyad zorunludur';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Profil güncelleme
  const handleSave = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          username: formData.username.trim(),
          full_name: formData.full_name.trim(),
          avatar_url: formData.avatar_url || null,
          bio: formData.bio.trim() || null,
          display_full_name_publicly: formData.display_full_name_publicly,
          display_bio_publicly: formData.display_bio_publicly,
          display_robots_publicly: formData.display_robots_publicly,
          updated_at: new Date().toISOString()
        })
        .eq('id', user?.id);

      if (error) throw error;

      // Profil verilerini yeniden çek
      const { data: updatedProfile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user?.id)
        .single();
      
      if (updatedProfile) {
        setProfile(updatedProfile);
      }
      
      setIsEditing(false);
      toast({
        title: 'Başarılı',
        description: 'Profil bilgileri güncellendi',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      console.error('Profil güncelleme hatası:', error);
      toast({
        title: 'Hata',
        description: error.message || 'Profil güncellenirken bir hata oluştu',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Avatar yükleme
  const handleAvatarUpload = async (file: File) => {
    if (!file) return;

    // Dosya boyutu kontrolü (5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: 'Hata',
        description: 'Dosya boyutu 5MB\'den küçük olmalıdır',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Dosya tipi kontrolü
    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Hata',
        description: 'Sadece resim dosyaları yüklenebilir',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${user?.id}_${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      setFormData(prev => ({ ...prev, avatar_url: data.publicUrl }));

      toast({
        title: 'Başarılı',
        description: 'Avatar yüklendi',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      console.error('Avatar yükleme hatası:', error);
      toast({
        title: 'Hata',
        description: error.message || 'Avatar yüklenirken bir hata oluştu',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Düzenleme moduna geç
  const handleEdit = () => {
    setFormData({
      username: profile?.username || '',
      full_name: profile?.full_name || '',
      avatar_url: profile?.avatar_url || '',
      bio: profile?.bio || '',
      display_full_name_publicly: profile?.display_full_name_publicly || false,
      display_bio_publicly: profile?.display_bio_publicly || false,
      display_robots_publicly: profile?.display_robots_publicly !== undefined ? profile.display_robots_publicly : true,
      url_slug: profile?.url_slug || ''
    });
    setErrors({});
    setIsEditing(true);
  };

  // Düzenleme iptal
  const handleCancel = () => {
    setFormData({
      username: profile?.username || '',
      full_name: profile?.full_name || '',
      avatar_url: profile?.avatar_url || '',
      bio: profile?.bio || '',
      display_full_name_publicly: profile?.display_full_name_publicly || false,
      display_bio_publicly: profile?.display_bio_publicly || false,
      display_robots_publicly: profile?.display_robots_publicly !== undefined ? profile.display_robots_publicly : true,
      url_slug: profile?.url_slug || ''
    });
    setErrors({});
    setIsEditing(false);
  };

  // Şifre değiştirme
  const handlePasswordChange = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast({
        title: 'Hata',
        description: 'Şifreler eşleşmiyor',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (passwordData.newPassword.length < 6) {
      toast({
        title: 'Hata',
        description: 'Şifre en az 6 karakter olmalıdır',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setPasswordLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (error) throw error;

      toast({
        title: 'Başarılı',
        description: 'Şifreniz güncellendi',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      setPasswordData({ newPassword: '', confirmPassword: '' });
      onPasswordModalClose();
    } catch (error: any) {
      console.error('Şifre güncelleme hatası:', error);
      toast({
        title: 'Hata',
        description: error.message || 'Şifre güncellenirken bir hata oluştu',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setPasswordLoading(false);
    }
  };

  return (
    <Box
      px={{ base: '16px', md: '24px', lg: '30px' }}
      pb={{ base: '20px', md: '30px' }}
    >
      <VStack spacing={{ base: 6, md: 8 }} align="stretch" maxW="800px" mx="auto">
        {/* Başlık */}
        <Heading 
          as="h1" 
          size={{ base: 'lg', md: 'xl' }}
          color={textColor} 
          textAlign={{ base: 'center', md: 'left' }}
          fontSize={{ base: '24px', md: '32px' }}
          fontWeight="700"
        >
          Profil Bilgileri
        </Heading>

        {/* Profil Kartı */}
        <Card
          p={cardPadding}
          borderRadius="20px"
          boxShadow={cardShadow}
          bg={cardBg}
          transition="all 0.3s"
          _hover={{ 
            transform: 'translateY(-2px)',
            boxShadow: '0 20px 40px rgba(112, 144, 176, 0.15)'
          }}
        >
          {/* Avatar Bölümü */}
          <Flex
            direction={{ base: 'column', sm: 'row' }}
            align={{ base: 'center', sm: 'flex-start' }}
            mb={{ base: 6, md: 8 }}
            gap={{ base: 4, sm: 6 }}
          >
            <Box position="relative">
              <Avatar
                size={{ base: 'xl', md: '2xl' }}
                src={isEditing ? formData.avatar_url : profile?.avatar_url}
                name={profile?.full_name || profile?.username}
                bg={brandColor}
                color="white"
                showBorder
                borderColor={brandColor}
                borderWidth="3px"
                shadow="lg"
              />
              {isEditing && (
                <IconButton
                  aria-label="Avatar yükle"
                  icon={<FiUpload />}
                  size="sm"
                  borderRadius="full"
                  colorScheme="brand"
                  position="absolute"
                  bottom="0"
                  right="0"
                  isLoading={isUploading}
                  onClick={() => fileInputRef.current?.click()}
                  minW="40px"
                  minH="40px"
                  boxShadow="lg"
                />
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                style={{ display: 'none' }}
                onChange={(e) => e.target.files?.[0] && handleAvatarUpload(e.target.files[0])}
              />
            </Box>

            <VStack 
              align={{ base: 'center', sm: 'flex-start' }} 
              spacing={{ base: 2, md: 3 }}
              flex="1"
              textAlign={{ base: 'center', sm: 'left' }}
            >
              <Heading 
                size={{ base: 'md', md: 'lg' }} 
                color={textColor}
                fontSize={{ base: '20px', md: '24px' }}
              >
                {profile?.full_name || 'İsim Belirtilmemiş'}
              </Heading>
              <Text 
                color={textColorSecondary} 
                fontSize={{ base: 'sm', md: 'md' }}
                fontWeight="500"
              >
                @{profile?.username || 'kullanici'}
              </Text>
              <Text 
                color={textColorSecondary} 
                fontSize={{ base: 'xs', md: 'sm' }}
              >
                {user?.email}
              </Text>
            </VStack>

            {/* Düzenle/İptal/Kaydet Butonları */}
            <Stack 
              direction={{ base: 'column', sm: 'column', md: 'row' }}
              spacing={{ base: 2, md: 3 }}
              align="center"
              mt={{ base: 4, sm: 0 }}
            >
              {!isEditing ? (
                <>
                  <Button
                    leftIcon={<FiEdit2 />}
                    colorScheme="brand"
                    variant="outline"
                    size={buttonSize}
                    onClick={handleEdit}
                    minW={{ base: '120px', md: '140px' }}
                    h={inputHeight}
                  >
                    Düzenle
                  </Button>
                  <Button
                    leftIcon={<FiLock />}
                    colorScheme="purple"
                    variant="outline"
                    size={buttonSize}
                    onClick={onPasswordModalOpen}
                    minW={{ base: '120px', md: '140px' }}
                    h={inputHeight}
                  >
                    Şifre Değiştir
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    leftIcon={<FiSave />}
                    colorScheme="brand"
                    size={buttonSize}
                    onClick={handleSave}
                    isLoading={isLoading}
                    loadingText="Kaydediliyor"
                    minW={{ base: '100px', md: '120px' }}
                    h={inputHeight}
                  >
                    Kaydet
                  </Button>
                  <Button
                    leftIcon={<FiX />}
                    variant="ghost"
                    size={buttonSize}
                    onClick={handleCancel}
                    minW={{ base: '80px', md: '100px' }}
                    h={inputHeight}
                  >
                    İptal
                  </Button>
                </>
              )}
            </Stack>
          </Flex>

          {/* Form Alanları */}
          {isEditing && (
            <VStack spacing={{ base: 4, md: 6 }} align="stretch">
              <SimpleGrid 
                columns={{ base: 1, md: 2 }} 
                spacing={{ base: 4, md: 6 }}
              >
                {/* Kullanıcı Adı */}
                <FormControl isInvalid={!!errors.username}>
                  <FormLabel 
                    fontSize={fontSize}
                    fontWeight="600" 
                    color={textColor}
                    mb={{ base: '6px', md: '8px' }}
                  >
                    Kullanıcı Adı
                  </FormLabel>
                  <Input
                    value={formData.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    placeholder="Kullanıcı adınızı girin"
                    fontSize={fontSize}
                    fontWeight="500"
                    borderRadius="16px"
                    h={inputHeight}
                    border="1px solid"
                    borderColor={errors.username ? 'red.300' : borderColor}
                    bg={inputBg}
                    _focus={{
                      borderColor: errors.username ? 'red.500' : brandColor,
                      boxShadow: `0 0 0 1px ${errors.username ? 'red.500' : brandColor}`,
                    }}
                    _placeholder={{ 
                      color: "secondaryGray.600", 
                      fontWeight: "400",
                      fontSize: fontSize
                    }}
                  />
                  {errors.username && (
                    <Text color="red.500" fontSize="xs" mt={1}>
                      {errors.username}
                    </Text>
                  )}
                  <FormHelperText fontSize="xs" color={textColorSecondary}>
                    Kullanıcı adınız benzersiz olmalıdır
                  </FormHelperText>
                </FormControl>

                {/* Ad Soyad */}
                <FormControl isInvalid={!!errors.full_name}>
                  <FormLabel 
                    fontSize={fontSize}
                    fontWeight="600" 
                    color={textColor}
                    mb={{ base: '6px', md: '8px' }}
                  >
                    Ad Soyad
                  </FormLabel>
                  <Input
                    value={formData.full_name}
                    onChange={(e) => handleInputChange('full_name', e.target.value)}
                    placeholder="Ad ve soyadınızı girin"
                    fontSize={fontSize}
                    fontWeight="500"
                    borderRadius="16px"
                    h={inputHeight}
                    border="1px solid"
                    borderColor={errors.full_name ? 'red.300' : borderColor}
                    bg={inputBg}
                    _focus={{
                      borderColor: errors.full_name ? 'red.500' : brandColor,
                      boxShadow: `0 0 0 1px ${errors.full_name ? 'red.500' : brandColor}`,
                    }}
                    _placeholder={{ 
                      color: "secondaryGray.600", 
                      fontWeight: "400",
                      fontSize: fontSize
                    }}
                  />
                  {errors.full_name && (
                    <Text color="red.500" fontSize="xs" mt={1}>
                      {errors.full_name}
                    </Text>
                  )}
                  <FormHelperText fontSize="xs" color={textColorSecondary}>
                    Gerçek adınız ve soyadınız
                  </FormHelperText>
                </FormControl>
              </SimpleGrid>

              {/* Avatar URL (İsteğe bağlı) */}
              <FormControl>
                <FormLabel 
                  fontSize={fontSize}
                  fontWeight="600" 
                  color={textColor}
                  mb={{ base: '6px', md: '8px' }}
                >
                  Avatar URL (İsteğe bağlı)
                </FormLabel>
                <Input
                  value={formData.avatar_url}
                  onChange={(e) => handleInputChange('avatar_url', e.target.value)}
                  placeholder="https://example.com/avatar.jpg"
                  fontSize={fontSize}
                  fontWeight="500"
                  borderRadius="16px"
                  h={inputHeight}
                  border="1px solid"
                  borderColor={borderColor}
                  bg={inputBg}
                  _focus={{
                    borderColor: brandColor,
                    boxShadow: `0 0 0 1px ${brandColor}`,
                  }}
                  _placeholder={{ 
                    color: "secondaryGray.600", 
                    fontWeight: "400",
                    fontSize: fontSize
                  }}
                />
                <FormHelperText fontSize="xs" color={textColorSecondary}>
                  Profil resminin URL'sini girin veya yukarıdaki butonu kullanarak resim yükleyin
                </FormHelperText>
              </FormControl>

              {/* Biyografi - Enhanced with character counter and validation */}
              <FormControl>
                <FormLabel
                  fontSize={fontSize}
                  fontWeight="600"
                  color={textColor}
                  mb={{ base: '6px', md: '8px' }}
                >
                  Biyografi (İsteğe bağlı)
                </FormLabel>
                <Textarea
                  value={formData.bio}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    // Enforce character limit and sanitize input
                    if (newValue.length <= 500) {
                      // Basic sanitization - remove potential HTML tags
                      const sanitizedValue = newValue.replace(/[<>]/g, '');
                      setFormData(prev => ({ ...prev, bio: sanitizedValue }));
                    }
                  }}
                  placeholder="Kendiniz hakkında kısa bir açıklama yazın..."
                  fontSize={fontSize}
                  fontWeight="500"
                  borderRadius="16px"
                  minH={{ base: "80px", md: "100px", lg: "120px" }}
                  border="1px solid"
                  borderColor={
                    formData.bio.length > 450 ? 'orange.300' :
                    formData.bio.length === 500 ? 'red.300' :
                    borderColor
                  }
                  bg={inputBg}
                  resize="vertical"
                  maxLength={500}
                  _focus={{
                    borderColor: formData.bio.length === 500 ? 'red.500' : brandColor,
                    boxShadow: `0 0 0 1px ${formData.bio.length === 500 ? 'red.500' : brandColor}`,
                  }}
                  _placeholder={{
                    color: "secondaryGray.600",
                    fontWeight: "400",
                    fontSize: fontSize
                  }}
                />
                <Flex justify="space-between" align="center" mt={1}>
                  <Flex align="center" gap={2}>
                    <FormHelperText fontSize="xs" color={textColorSecondary} mb={0}>
                      Bu bilgi diğer kullanıcılar tarafından görülebilir (gizlilik ayarlarınıza bağlı)
                    </FormHelperText>
                    {/* Auto-save status indicator */}
                    {isEditing && bioAutoSaveStatus !== 'idle' && (
                      <Text
                        fontSize="xs"
                        color={bioAutoSaveStatus === 'saving' ? 'blue.500' : 'green.500'}
                        fontWeight="500"
                      >
                        {bioAutoSaveStatus === 'saving' ? 'Kaydediliyor...' : 'Kaydedildi ✓'}
                      </Text>
                    )}
                  </Flex>
                  <Text
                    fontSize="xs"
                    color={
                      formData.bio.length === 500 ? 'red.500' :
                      formData.bio.length > 450 ? 'orange.500' :
                      'gray.500'
                    }
                    fontWeight="500"
                  >
                    {500 - formData.bio.length} karakter kaldı
                  </Text>
                </Flex>
              </FormControl>

              <Divider />

              {/* Gizlilik Ayarları */}
              <Box>
                <Heading 
                  size="md" 
                  color={textColor} 
                  mb={4}
                  fontSize={{ base: '18px', md: '20px' }}
                >
                  Gizlilik Ayarları
                </Heading>
                <VStack spacing={4} align="stretch">
                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <FormLabel 
                        fontSize={fontSize}
                        fontWeight="600" 
                        color={textColor}
                        mb="0"
                      >
                        Tam İsmi Herkese Açık Göster
                      </FormLabel>
                      <Text fontSize="xs" color={textColorSecondary}>
                        Diğer kullanıcılar ad soyadınızı görebilir
                      </Text>
                    </Box>
                    <Switch
                      colorScheme="brand"
                      isChecked={formData.display_full_name_publicly}
                      onChange={(e) => setFormData(prev => ({ ...prev, display_full_name_publicly: e.target.checked }))}
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <FormLabel 
                        fontSize={fontSize}
                        fontWeight="600" 
                        color={textColor}
                        mb="0"
                      >
                        Biyografiyi Herkese Açık Göster
                      </FormLabel>
                      <Text fontSize="xs" color={textColorSecondary}>
                        Diğer kullanıcılar biyografinizi görebilir
                      </Text>
                    </Box>
                    <Switch
                      colorScheme="brand"
                      isChecked={formData.display_bio_publicly}
                      onChange={(e) => setFormData(prev => ({ ...prev, display_bio_publicly: e.target.checked }))}
                    />
                  </FormControl>

                  <FormControl display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <FormLabel 
                        fontSize={fontSize}
                        fontWeight="600" 
                        color={textColor}
                        mb="0"
                      >
                        Robotlarımı Herkese Açık Göster
                      </FormLabel>
                      <Text fontSize="xs" color={textColorSecondary}>
                        Diğer kullanıcılar oluşturduğunuz robotları görebilir
                      </Text>
                    </Box>
                    <Switch
                      colorScheme="brand"
                      isChecked={formData.display_robots_publicly}
                      onChange={(e) => setFormData(prev => ({ ...prev, display_robots_publicly: e.target.checked }))}
                    />
                  </FormControl>
                </VStack>
              </Box>
            </VStack>
          )}
        </Card>

        {/* Bilgi kartı */}
        <Card
          p={cardPadding}
          borderRadius="20px"
          boxShadow={cardShadow}
          bg={cardBg}
        >
          <Alert 
            status="info" 
            borderRadius="16px" 
            variant="subtle"
            flexDirection={{ base: 'column', sm: 'row' }}
            alignItems={{ base: 'center', sm: 'flex-start' }}
            textAlign={{ base: 'center', sm: 'left' }}
          >
            <AlertIcon mb={{ base: 2, sm: 0 }} />
            <AlertDescription fontSize={{ base: 'sm', md: 'md' }}>
              Profil bilgileriniz platformda diğer kullanıcılar tarafından görülebilir. 
              Kişisel bilgilerinizi paylaşırken dikkatli olun.
            </AlertDescription>
          </Alert>
        </Card>
      </VStack>

      {/* Şifre Değiştirme Modalı */}
      <Modal isOpen={isPasswordModalOpen} onClose={onPasswordModalClose} size="md">
        <ModalOverlay />
        <ModalContent bg={cardBg} borderRadius="20px">
          <ModalHeader color={textColor}>Şifre Değiştir</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl>
                <FormLabel fontSize={fontSize} fontWeight="600" color={textColor}>
                  Yeni Şifre
                </FormLabel>
                <Input
                  type="password"
                  value={passwordData.newPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                  placeholder="Yeni şifrenizi girin"
                  fontSize={fontSize}
                  fontWeight="500"
                  borderRadius="16px"
                  h={inputHeight}
                  border="1px solid"
                  borderColor={borderColor}
                  bg={inputBg}
                  autoComplete="new-password"
                  _focus={{
                    borderColor: brandColor,
                    boxShadow: `0 0 0 1px ${brandColor}`,
                  }}
                />
                <FormHelperText fontSize="xs" color={textColorSecondary}>
                  En az 6 karakter olmalıdır
                </FormHelperText>
              </FormControl>

              <FormControl>
                <FormLabel fontSize={fontSize} fontWeight="600" color={textColor}>
                  Şifre Tekrarı
                </FormLabel>
                <Input
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  placeholder="Yeni şifrenizi tekrar girin"
                  fontSize={fontSize}
                  fontWeight="500"
                  borderRadius="16px"
                  h={inputHeight}
                  border="1px solid"
                  borderColor={borderColor}
                  bg={inputBg}
                  autoComplete="new-password"
                  _focus={{
                    borderColor: brandColor,
                    boxShadow: `0 0 0 1px ${brandColor}`,
                  }}
                />
              </FormControl>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button
              colorScheme="brand"
              mr={3}
              onClick={handlePasswordChange}
              isLoading={passwordLoading}
              loadingText="Güncelleniyor"
              leftIcon={<FiKey />}
            >
              Şifreyi Güncelle
            </Button>
            <Button variant="ghost" onClick={onPasswordModalClose}>
              İptal
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default Profile; 