/**
 * Monitoring Dashboard Component
 * Phase 3.3: Monitoring & Alerting Systems
 * Real-time monitoring dashboard with performance metrics, errors, and alerts
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  Badge,
  VStack,
  HStack,
  Select,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  useColorModeValue,
  Icon,
  Flex,
  Progress
} from '@chakra-ui/react';
// Recharts imports removed - not currently used
import {
  FiAlertTriangle,
  FiUsers,
  FiClock,
  FiZap,
  FiShield,
  FiEye
} from 'react-icons/fi';

// Types
interface MonitoringData {
  summary?: {
    pageViews: number;
    errors: number;
    criticalErrors: number;
    avgResponseTime: number;
    activeAlerts: number;
  };
  webVitals?: {
    lcp: number;
    fid: number;
    cls: number;
  };
  recentAlerts?: any[];
  metrics?: any[];
  aggregations?: any[];
  errors?: any[];
  topErrors?: any[];
  timestamp: string;
}

interface MonitoringDashboardProps {
  timeRange?: '1h' | '24h' | '7d' | '30d';
  autoRefresh?: boolean;
  refreshInterval?: number;
}

// Color schemes
const SEVERITY_COLORS = {
  critical: 'red',
  high: 'orange',
  medium: 'yellow',
  low: 'blue',
  info: 'gray'
};

const MonitoringDashboard: React.FC<MonitoringDashboardProps> = ({
  timeRange = '24h',
  autoRefresh = true,
  refreshInterval = 30000
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [data, setData] = useState<MonitoringData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Fetch monitoring data
  const fetchData = async (type: string = 'overview') => {
    try {
      setLoading(true);
      setError(null);

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

      const response = await fetch(
        `${supabaseUrl}/functions/v1/monitoring-dashboard?type=${type}&timeRange=${selectedTimeRange}`,
        {
          headers: {
            'Authorization': `Bearer ${anonKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch monitoring data');
      console.error('Monitoring data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Auto-refresh effect
  useEffect(() => {
    const tabTypes = ['overview', 'performance', 'errors', 'business', 'alerts'];
    fetchData(tabTypes[activeTab]);

    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchData(tabTypes[activeTab]);
      }, refreshInterval);

      return () => clearInterval(interval);
    }
  }, [activeTab, selectedTimeRange, autoRefresh, refreshInterval]);

  // Handle tab change
  const handleTabChange = (index: number) => {
    setActiveTab(index);
  };

  // Handle time range change
  const handleTimeRangeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedTimeRange(event.target.value as any);
  };

  // Format numbers for display
  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  // Format duration
  const formatDuration = (ms: number): string => {
    if (ms >= 1000) return `${(ms / 1000).toFixed(1)}s`;
    return `${Math.round(ms)}ms`;
  };

  // Get Web Vitals status
  const getWebVitalStatus = (metric: string, value: number) => {
    const thresholds = {
      lcp: { good: 2500, poor: 4000 },
      fid: { good: 100, poor: 300 },
      cls: { good: 0.1, poor: 0.25 }
    };

    const threshold = thresholds[metric as keyof typeof thresholds];
    if (!threshold) return 'gray';

    if (value <= threshold.good) return 'green';
    if (value <= threshold.poor) return 'yellow';
    return 'red';
  };

  if (loading && !data) {
    return (
      <Box p={6} textAlign="center">
        <Spinner size="xl" />
        <Text mt={4}>Loading monitoring data...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error" m={6}>
        <AlertIcon />
        <AlertTitle>Error loading monitoring data</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <Box p={6}>
      {/* Header */}
      <Flex justify="space-between" align="center" mb={6}>
        <Heading size="lg">Monitoring Dashboard</Heading>
        <HStack spacing={4}>
          <Select
            value={selectedTimeRange}
            onChange={handleTimeRangeChange}
            width="120px"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24h</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
          </Select>
          {loading && <Spinner size="sm" />}
        </HStack>
      </Flex>

      {/* Tabs */}
      <Tabs index={activeTab} onChange={handleTabChange}>
        <TabList>
          <Tab>Overview</Tab>
          <Tab>Performance</Tab>
          <Tab>Errors</Tab>
          <Tab>Business</Tab>
          <Tab>Alerts</Tab>
        </TabList>

        <TabPanels>
          {/* Overview Tab */}
          <TabPanel>
            {data?.summary && (
              <>
                {/* Key Metrics */}
                <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={6} mb={8}>
                  <Card bg={cardBg} borderColor={borderColor}>
                    <CardBody>
                      <Stat>
                        <StatLabel>
                          <HStack>
                            <Icon as={FiUsers} />
                            <Text>Page Views</Text>
                          </HStack>
                        </StatLabel>
                        <StatNumber>{formatNumber(data.summary.pageViews)}</StatNumber>
                        <StatHelpText>
                          <StatArrow type="increase" />
                          {selectedTimeRange}
                        </StatHelpText>
                      </Stat>
                    </CardBody>
                  </Card>

                  <Card bg={cardBg} borderColor={borderColor}>
                    <CardBody>
                      <Stat>
                        <StatLabel>
                          <HStack>
                            <Icon as={FiClock} />
                            <Text>Avg Response Time</Text>
                          </HStack>
                        </StatLabel>
                        <StatNumber>{formatDuration(data.summary.avgResponseTime)}</StatNumber>
                        <StatHelpText>
                          <Badge colorScheme={data.summary.avgResponseTime > 3000 ? 'red' : 'green'}>
                            {data.summary.avgResponseTime > 3000 ? 'Slow' : 'Good'}
                          </Badge>
                        </StatHelpText>
                      </Stat>
                    </CardBody>
                  </Card>

                  <Card bg={cardBg} borderColor={borderColor}>
                    <CardBody>
                      <Stat>
                        <StatLabel>
                          <HStack>
                            <Icon as={FiAlertTriangle} />
                            <Text>Errors</Text>
                          </HStack>
                        </StatLabel>
                        <StatNumber>{formatNumber(data.summary.errors)}</StatNumber>
                        <StatHelpText>
                          <Badge colorScheme={data.summary.criticalErrors > 0 ? 'red' : 'green'}>
                            {data.summary.criticalErrors} Critical
                          </Badge>
                        </StatHelpText>
                      </Stat>
                    </CardBody>
                  </Card>

                  <Card bg={cardBg} borderColor={borderColor}>
                    <CardBody>
                      <Stat>
                        <StatLabel>
                          <HStack>
                            <Icon as={FiShield} />
                            <Text>Active Alerts</Text>
                          </HStack>
                        </StatLabel>
                        <StatNumber>{data.summary.activeAlerts}</StatNumber>
                        <StatHelpText>
                          <Badge colorScheme={data.summary.activeAlerts > 0 ? 'orange' : 'green'}>
                            {data.summary.activeAlerts > 0 ? 'Needs Attention' : 'All Clear'}
                          </Badge>
                        </StatHelpText>
                      </Stat>
                    </CardBody>
                  </Card>
                </Grid>

                {/* Web Vitals */}
                {data.webVitals && (
                  <Card bg={cardBg} borderColor={borderColor} mb={8}>
                    <CardHeader>
                      <Heading size="md">
                        <HStack>
                          <Icon as={FiZap} />
                          <Text>Core Web Vitals</Text>
                        </HStack>
                      </Heading>
                    </CardHeader>
                    <CardBody>
                      <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={6}>
                        <VStack>
                          <Text fontWeight="bold">Largest Contentful Paint</Text>
                          <Text fontSize="2xl" color={`${getWebVitalStatus('lcp', data.webVitals.lcp)}.500`}>
                            {formatDuration(data.webVitals.lcp)}
                          </Text>
                          <Progress
                            value={Math.min((data.webVitals.lcp / 4000) * 100, 100)}
                            colorScheme={getWebVitalStatus('lcp', data.webVitals.lcp)}
                            width="100%"
                          />
                        </VStack>

                        <VStack>
                          <Text fontWeight="bold">First Input Delay</Text>
                          <Text fontSize="2xl" color={`${getWebVitalStatus('fid', data.webVitals.fid)}.500`}>
                            {formatDuration(data.webVitals.fid)}
                          </Text>
                          <Progress
                            value={Math.min((data.webVitals.fid / 300) * 100, 100)}
                            colorScheme={getWebVitalStatus('fid', data.webVitals.fid)}
                            width="100%"
                          />
                        </VStack>

                        <VStack>
                          <Text fontWeight="bold">Cumulative Layout Shift</Text>
                          <Text fontSize="2xl" color={`${getWebVitalStatus('cls', data.webVitals.cls)}.500`}>
                            {data.webVitals.cls.toFixed(3)}
                          </Text>
                          <Progress
                            value={Math.min((data.webVitals.cls / 0.25) * 100, 100)}
                            colorScheme={getWebVitalStatus('cls', data.webVitals.cls)}
                            width="100%"
                          />
                        </VStack>
                      </Grid>
                    </CardBody>
                  </Card>
                )}

                {/* Recent Alerts */}
                {data.recentAlerts && data.recentAlerts.length > 0 && (
                  <Card bg={cardBg} borderColor={borderColor}>
                    <CardHeader>
                      <Heading size="md">
                        <HStack>
                          <Icon as={FiEye} />
                          <Text>Recent Alerts</Text>
                        </HStack>
                      </Heading>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={3} align="stretch">
                        {data.recentAlerts.slice(0, 5).map((alert, index) => (
                          <Alert key={index} status={alert.severity === 'critical' ? 'error' : 'warning'}>
                            <AlertIcon />
                            <Box>
                              <AlertTitle fontSize="sm">
                                {alert.type.charAt(0).toUpperCase() + alert.type.slice(1)} Alert
                              </AlertTitle>
                              <AlertDescription fontSize="xs">
                                {alert.message}
                              </AlertDescription>
                            </Box>
                            <Badge ml="auto" colorScheme={SEVERITY_COLORS[alert.severity as keyof typeof SEVERITY_COLORS]}>
                              {alert.severity}
                            </Badge>
                          </Alert>
                        ))}
                      </VStack>
                    </CardBody>
                  </Card>
                )}
              </>
            )}
          </TabPanel>

          {/* Performance Tab */}
          <TabPanel>
            {data?.aggregations && (
              <Grid templateColumns="repeat(auto-fit, minmax(300px, 1fr))" gap={6}>
                {data.aggregations.map((agg, index) => (
                  <Card key={index} bg={cardBg} borderColor={borderColor}>
                    <CardHeader>
                      <Heading size="sm">{agg.metric}</Heading>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={2} align="stretch">
                        <HStack justify="space-between">
                          <Text fontSize="sm">Average:</Text>
                          <Text fontWeight="bold">{agg.average}</Text>
                        </HStack>
                        <HStack justify="space-between">
                          <Text fontSize="sm">95th Percentile:</Text>
                          <Text fontWeight="bold">{agg.p95}</Text>
                        </HStack>
                        <HStack justify="space-between">
                          <Text fontSize="sm">Min/Max:</Text>
                          <Text fontWeight="bold">{agg.min} / {agg.max}</Text>
                        </HStack>
                        <HStack justify="space-between">
                          <Text fontSize="sm">Count:</Text>
                          <Text fontWeight="bold">{agg.count}</Text>
                        </HStack>
                      </VStack>
                    </CardBody>
                  </Card>
                ))}
              </Grid>
            )}
          </TabPanel>

          {/* Errors Tab */}
          <TabPanel>
            {data?.summary && (
              <Grid templateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap={6} mb={6}>
                <Card bg={cardBg} borderColor={borderColor}>
                  <CardBody textAlign="center">
                    <Text fontSize="2xl" fontWeight="bold" color="red.500">
                      {(data.summary as any).critical || 0}
                    </Text>
                    <Text>Critical</Text>
                  </CardBody>
                </Card>
                <Card bg={cardBg} borderColor={borderColor}>
                  <CardBody textAlign="center">
                    <Text fontSize="2xl" fontWeight="bold" color="orange.500">
                      {(data.summary as any).high || 0}
                    </Text>
                    <Text>High</Text>
                  </CardBody>
                </Card>
                <Card bg={cardBg} borderColor={borderColor}>
                  <CardBody textAlign="center">
                    <Text fontSize="2xl" fontWeight="bold" color="yellow.500">
                      {(data.summary as any).medium || 0}
                    </Text>
                    <Text>Medium</Text>
                  </CardBody>
                </Card>
                <Card bg={cardBg} borderColor={borderColor}>
                  <CardBody textAlign="center">
                    <Text fontSize="2xl" fontWeight="bold" color="blue.500">
                      {(data.summary as any).low || 0}
                    </Text>
                    <Text>Low</Text>
                  </CardBody>
                </Card>
              </Grid>
            )}

            {/* Top Errors */}
            {(data as any)?.topErrors && (
              <Card bg={cardBg} borderColor={borderColor}>
                <CardHeader>
                  <Heading size="md">Most Frequent Errors</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={3} align="stretch">
                    {(data as any).topErrors.slice(0, 10).map((error: any, index: number) => (
                      <HStack key={index} justify="space-between" p={3} bg="gray.50" borderRadius="md">
                        <Text fontSize="sm" flex={1} noOfLines={2}>
                          {error.message}
                        </Text>
                        <Badge colorScheme="red">{error.count}</Badge>
                      </HStack>
                    ))}
                  </VStack>
                </CardBody>
              </Card>
            )}
          </TabPanel>

          {/* Business Tab */}
          <TabPanel>
            {(data as any)?.topEvents && (
              <Card bg={cardBg} borderColor={borderColor}>
                <CardHeader>
                  <Heading size="md">Top Events</Heading>
                </CardHeader>
                <CardBody>
                  <VStack spacing={3} align="stretch">
                    {(data as any).topEvents.map((event: any, index: number) => (
                      <HStack key={index} justify="space-between" p={3} bg="gray.50" borderRadius="md">
                        <Text fontWeight="bold">{event.event}</Text>
                        <HStack>
                          <Badge colorScheme="blue">{event.count} events</Badge>
                          <Badge colorScheme="green">Avg: {event.avgValue}</Badge>
                        </HStack>
                      </HStack>
                    ))}
                  </VStack>
                </CardBody>
              </Card>
            )}
          </TabPanel>

          {/* Alerts Tab */}
          <TabPanel>
            {(data as any)?.alerts && (
              <VStack spacing={4} align="stretch">
                {(data as any).alerts.map((alert: any, index: number) => (
                  <Alert key={index} status={alert.severity === 'critical' ? 'error' : 'warning'}>
                    <AlertIcon />
                    <Box flex={1}>
                      <AlertTitle>
                        {alert.type.charAt(0).toUpperCase() + alert.type.slice(1)} Alert
                        <Badge ml={2} colorScheme={SEVERITY_COLORS[alert.severity as keyof typeof SEVERITY_COLORS]}>
                          {alert.severity}
                        </Badge>
                      </AlertTitle>
                      <AlertDescription>
                        {alert.message}
                      </AlertDescription>
                      <Text fontSize="xs" color="gray.500" mt={1}>
                        {new Date(alert.timestamp).toLocaleString()}
                      </Text>
                    </Box>
                  </Alert>
                ))}
              </VStack>
            )}
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};

export default MonitoringDashboard;
