import { useEffect, useRef, useState, useCallback } from 'react';

interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

interface SwipeGesture {
  direction: 'left' | 'right' | 'up' | 'down';
  distance: number;
  velocity: number;
  duration: number;
}

interface TouchInteractionOptions {
  enableSwipe?: boolean;
  enablePinch?: boolean;
  enableTap?: boolean;
  enableLongPress?: boolean;
  swipeThreshold?: number;
  longPressDelay?: number;
  tapTimeout?: number;
  preventScroll?: boolean;
}

interface TouchInteractionCallbacks {
  onSwipe?: (gesture: SwipeGesture) => void;
  onPinch?: (scale: number, center: TouchPoint) => void;
  onTap?: (point: TouchPoint) => void;
  onDoubleTap?: (point: TouchPoint) => void;
  onLongPress?: (point: TouchPoint) => void;
  onTouchStart?: (event: TouchEvent) => void;
  onTouchMove?: (event: TouchEvent) => void;
  onTouchEnd?: (event: TouchEvent) => void;
}

/**
 * Touch Interactions Hook - Mobile UX optimized
 * Provides comprehensive touch gesture support for mobile devices
 */
export const useTouchInteractions = (
  elementRef: React.RefObject<HTMLElement>,
  options: TouchInteractionOptions = {},
  callbacks: TouchInteractionCallbacks = {}
) => {
  const {
    enableSwipe = true,
    enablePinch = false,
    enableTap = true,
    enableLongPress = false,
    swipeThreshold = 50,
    longPressDelay = 500,
    tapTimeout = 300,
    preventScroll = false
  } = options;

  const {
    onSwipe,
    onPinch,
    onTap,
    onDoubleTap,
    onLongPress,
    onTouchStart,
    onTouchMove,
    onTouchEnd
  } = callbacks;

  // Touch state
  const [isTouch, setIsTouch] = useState(false);
  const [touchCount, setTouchCount] = useState(0);
  
  // Refs for tracking touch data
  const touchStartRef = useRef<TouchPoint | null>(null);
  const touchEndRef = useRef<TouchPoint | null>(null);
  const lastTapRef = useRef<TouchPoint | null>(null);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const initialDistanceRef = useRef<number>(0);
  const initialCenterRef = useRef<TouchPoint | null>(null);

  // Helper functions
  const getTouchPoint = (touch: Touch): TouchPoint => ({
    x: touch.clientX,
    y: touch.clientY,
    timestamp: Date.now()
  });

  const getDistance = (point1: TouchPoint, point2: TouchPoint): number => {
    return Math.sqrt(
      Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2)
    );
  };

  const getTouchCenter = (touches: TouchList): TouchPoint => {
    let x = 0, y = 0;
    for (let i = 0; i < touches.length; i++) {
      x += touches[i].clientX;
      y += touches[i].clientY;
    }
    return {
      x: x / touches.length,
      y: y / touches.length,
      timestamp: Date.now()
    };
  };

  const getSwipeDirection = (start: TouchPoint, end: TouchPoint): SwipeGesture['direction'] => {
    const deltaX = end.x - start.x;
    const deltaY = end.y - start.y;
    
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      return deltaX > 0 ? 'right' : 'left';
    } else {
      return deltaY > 0 ? 'down' : 'up';
    }
  };

  const clearLongPressTimer = useCallback(() => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
  }, []);

  // Touch event handlers
  const handleTouchStart = useCallback((event: TouchEvent) => {
    setIsTouch(true);
    setTouchCount(event.touches.length);

    const touch = event.touches[0];
    const touchPoint = getTouchPoint(touch);
    touchStartRef.current = touchPoint;

    // Handle pinch gesture setup
    if (enablePinch && event.touches.length === 2) {
      const touch1 = getTouchPoint(event.touches[0]);
      const touch2 = getTouchPoint(event.touches[1]);
      initialDistanceRef.current = getDistance(touch1, touch2);
      initialCenterRef.current = getTouchCenter(event.touches);
    }

    // Setup long press timer
    if (enableLongPress && event.touches.length === 1) {
      longPressTimerRef.current = setTimeout(() => {
        if (onLongPress && touchStartRef.current) {
          onLongPress(touchStartRef.current);
        }
      }, longPressDelay);
    }

    // Prevent scroll if needed
    if (preventScroll) {
      event.preventDefault();
    }

    // Call custom handler
    if (onTouchStart) {
      onTouchStart(event);
    }
  }, [enablePinch, enableLongPress, longPressDelay, preventScroll, onLongPress, onTouchStart]);

  const handleTouchMove = useCallback((event: TouchEvent) => {
    // Clear long press timer on move
    clearLongPressTimer();

    // Handle pinch gesture
    if (enablePinch && event.touches.length === 2 && initialDistanceRef.current > 0) {
      const touch1 = getTouchPoint(event.touches[0]);
      const touch2 = getTouchPoint(event.touches[1]);
      const currentDistance = getDistance(touch1, touch2);
      const scale = currentDistance / initialDistanceRef.current;
      const center = getTouchCenter(event.touches);

      if (onPinch) {
        onPinch(scale, center);
      }
    }

    // Prevent scroll if needed
    if (preventScroll) {
      event.preventDefault();
    }

    // Call custom handler
    if (onTouchMove) {
      onTouchMove(event);
    }
  }, [enablePinch, preventScroll, clearLongPressTimer, onPinch, onTouchMove]);

  const handleTouchEnd = useCallback((event: TouchEvent) => {
    setTouchCount(event.touches.length);
    clearLongPressTimer();

    if (event.changedTouches.length > 0) {
      const touch = event.changedTouches[0];
      const touchPoint = getTouchPoint(touch);
      touchEndRef.current = touchPoint;

      // Handle swipe gesture
      if (enableSwipe && touchStartRef.current && event.touches.length === 0) {
        const distance = getDistance(touchStartRef.current, touchPoint);
        
        if (distance > swipeThreshold) {
          const direction = getSwipeDirection(touchStartRef.current, touchPoint);
          const duration = touchPoint.timestamp - touchStartRef.current.timestamp;
          const velocity = distance / duration;

          const swipeGesture: SwipeGesture = {
            direction,
            distance,
            velocity,
            duration
          };

          if (onSwipe) {
            onSwipe(swipeGesture);
          }
        }
      }

      // Handle tap gestures
      if (enableTap && touchStartRef.current && event.touches.length === 0) {
        const distance = getDistance(touchStartRef.current, touchPoint);
        
        // Only consider it a tap if the finger didn't move much
        if (distance < 10) {
          // Check for double tap
          if (onDoubleTap && lastTapRef.current) {
            const timeDiff = touchPoint.timestamp - lastTapRef.current.timestamp;
            const tapDistance = getDistance(lastTapRef.current, touchPoint);
            
            if (timeDiff < tapTimeout && tapDistance < 50) {
              onDoubleTap(touchPoint);
              lastTapRef.current = null; // Reset to prevent triple tap
              return;
            }
          }

          // Single tap
          if (onTap) {
            onTap(touchPoint);
          }
          
          lastTapRef.current = touchPoint;
          
          // Clear last tap after timeout
          setTimeout(() => {
            lastTapRef.current = null;
          }, tapTimeout);
        }
      }
    }

    // Reset touch data when all touches end
    if (event.touches.length === 0) {
      setIsTouch(false);
      touchStartRef.current = null;
      touchEndRef.current = null;
      initialDistanceRef.current = 0;
      initialCenterRef.current = null;
    }

    // Call custom handler
    if (onTouchEnd) {
      onTouchEnd(event);
    }
  }, [
    enableSwipe, 
    enableTap, 
    swipeThreshold, 
    tapTimeout, 
    clearLongPressTimer,
    onSwipe, 
    onTap, 
    onDoubleTap, 
    onTouchEnd
  ]);

  // Attach event listeners
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Add touch event listeners
    element.addEventListener('touchstart', handleTouchStart, { passive: !preventScroll });
    element.addEventListener('touchmove', handleTouchMove, { passive: !preventScroll });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    // Cleanup
    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
      clearLongPressTimer();
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, preventScroll, clearLongPressTimer]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearLongPressTimer();
    };
  }, [clearLongPressTimer]);

  return {
    isTouch,
    touchCount,
    
    // Utility functions
    isTouchDevice: () => 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    
    // Touch state helpers
    isSingleTouch: touchCount === 1,
    isMultiTouch: touchCount > 1,
    
    // Manual trigger functions (for testing or programmatic use)
    simulateSwipe: (direction: SwipeGesture['direction']) => {
      if (onSwipe) {
        onSwipe({
          direction,
          distance: swipeThreshold + 10,
          velocity: 1,
          duration: 200
        });
      }
    },
    
    simulateTap: (point: TouchPoint = { x: 0, y: 0, timestamp: Date.now() }) => {
      if (onTap) {
        onTap(point);
      }
    }
  };
};

export default useTouchInteractions;
