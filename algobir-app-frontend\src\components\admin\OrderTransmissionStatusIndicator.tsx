import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  Badge,
  Icon,
  useColorModeValue,
  Tooltip,
  Progress,

  Divider
} from '@chakra-ui/react';
import {
  FiWifi,
  FiWifiOff,
  FiActivity,
  FiZap,
  FiAlertCircle,
  FiCheckCircle
} from 'react-icons/fi';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import useOrderTransmissionMetrics from '../../hooks/useOrderTransmissionMetrics';

const MotionBox = motion(Box);

interface OrderTransmissionStatusIndicatorProps {
  compact?: boolean;
  showRecentActivity?: boolean;
  timeRangeMinutes?: number;
}

const OrderTransmissionStatusIndicator: React.FC<OrderTransmissionStatusIndicatorProps> = ({
  compact = false,
  showRecentActivity = true,
  timeRangeMinutes = 5
}) => {
  const [recentMetrics, setRecentMetrics] = useState<any[]>([]);

  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');
  const successBg = useColorModeValue('green.50', 'green.900');
  const warningBg = useColorModeValue('yellow.50', 'yellow.900');
  const errorBg = useColorModeValue('red.50', 'red.900');

  // Fetch recent metrics
  const {
    metrics,
    stats,
    loading,
    error,
    isConnected,
    lastUpdate
  } = useOrderTransmissionMetrics({
    timeRangeHours: timeRangeMinutes / 60,
    enabled: true,
    autoRefresh: true,
    refreshInterval: 5000 // 5 seconds for real-time updates
  });

  // Update recent metrics when data changes
  useEffect(() => {
    if (metrics && metrics.length > 0) {
      // Get last 5 metrics for recent activity
      setRecentMetrics(metrics.slice(0, 5));
    }
  }, [metrics]);

  // Get connection status
  const getConnectionStatus = () => {
    if (!isConnected) {
      return {
        status: 'disconnected',
        color: 'red',
        icon: FiWifiOff,
        message: 'Bağlantı Kesildi'
      };
    }

    if (error) {
      return {
        status: 'error',
        color: 'red',
        icon: FiAlertCircle,
        message: 'Hata Var'
      };
    }

    if (loading) {
      return {
        status: 'loading',
        color: 'yellow',
        icon: FiActivity,
        message: 'Yükleniyor'
      };
    }

    return {
      status: 'connected',
      color: 'green',
      icon: FiWifi,
      message: 'Canlı Bağlantı'
    };
  };

  const connectionStatus = getConnectionStatus();

  // Get performance status
  const getPerformanceStatus = () => {
    if (!stats) return { status: 'unknown', color: 'gray', message: 'Veri Yok' };

    const avgTime = stats.avg_total_processing_time_ms;
    if (avgTime <= 100) {
      return { status: 'excellent', color: 'green', message: 'Mükemmel' };
    } else if (avgTime <= 500) {
      return { status: 'good', color: 'yellow', message: 'İyi' };
    } else {
      return { status: 'poor', color: 'red', message: 'Yavaş' };
    }
  };

  const performanceStatus = getPerformanceStatus();

  if (compact) {
    return (
      <HStack spacing={3}>
        <Tooltip label={connectionStatus.message}>
          <HStack spacing={2}>
            <Icon
              as={connectionStatus.icon}
              color={`${connectionStatus.color}.500`}
              boxSize={4}
            />
            <Badge
              colorScheme={connectionStatus.color}
              variant="subtle"
              fontSize="xs"
            >
              {connectionStatus.message}
            </Badge>
          </HStack>
        </Tooltip>

        {stats && (
          <Tooltip label={`Ortalama: ${stats.avg_total_processing_time_ms.toFixed(1)}ms`}>
            <HStack spacing={2}>
              <Icon as={FiZap} color={`${performanceStatus.color}.500`} boxSize={4} />
              <Badge
                colorScheme={performanceStatus.color}
                variant="subtle"
                fontSize="xs"
              >
                {performanceStatus.message}
              </Badge>
            </HStack>
          </Tooltip>
        )}

        {lastUpdate && (
          <Text fontSize="xs" color={mutedColor}>
            {format(lastUpdate, 'HH:mm:ss', { locale: tr })}
          </Text>
        )}
      </HStack>
    );
  }

  return (
    <Card
      bg={cardBg}
      borderRadius="xl"
      boxShadow="md"
      border="1px solid"
      borderColor={borderColor}
      overflow="hidden"
    >
      <CardBody p={4}>
        <VStack spacing={4} align="stretch">
          {/* Connection Status */}
          <HStack justify="space-between">
            <HStack spacing={3}>
              <MotionBox
                animate={{
                  scale: isConnected ? [1, 1.1, 1] : 1,
                  opacity: isConnected ? [1, 0.7, 1] : 1
                }}
                transition={{
                  duration: 2,
                  repeat: isConnected ? Infinity : 0,
                  ease: "easeInOut"
                }}
              >
                <Icon
                  as={connectionStatus.icon}
                  color={`${connectionStatus.color}.500`}
                  boxSize={5}
                />
              </MotionBox>
              <VStack align="start" spacing={0}>
                <Text fontSize="sm" fontWeight="bold" color={textColor}>
                  {connectionStatus.message}
                </Text>
                {lastUpdate && (
                  <Text fontSize="xs" color={mutedColor}>
                    Son güncelleme: {format(lastUpdate, 'HH:mm:ss', { locale: tr })}
                  </Text>
                )}
              </VStack>
            </HStack>

            <Badge
              colorScheme={connectionStatus.color}
              variant="subtle"
              borderRadius="full"
              px={3}
              py={1}
            >
              {isConnected ? 'Aktif' : 'Pasif'}
            </Badge>
          </HStack>

          {/* Performance Metrics */}
          {stats && (
            <>
              <Divider />
              <VStack spacing={3} align="stretch">
                <HStack justify="space-between">
                  <Text fontSize="sm" fontWeight="medium" color={textColor}>
                    Performans Durumu
                  </Text>
                  <Badge
                    colorScheme={performanceStatus.color}
                    variant="subtle"
                    borderRadius="full"
                  >
                    {performanceStatus.message}
                  </Badge>
                </HStack>

                <VStack spacing={2} align="stretch">
                  <HStack justify="space-between">
                    <Text fontSize="xs" color={mutedColor}>Ortalama Süre</Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {stats.avg_total_processing_time_ms.toFixed(1)}ms
                    </Text>
                  </HStack>

                  <HStack justify="space-between">
                    <Text fontSize="xs" color={mutedColor}>Başarı Oranı</Text>
                    <Text fontSize="xs" fontWeight="bold" color="green.500">
                      %{stats.success_rate.toFixed(1)}
                    </Text>
                  </HStack>

                  <HStack justify="space-between">
                    <Text fontSize="xs" color={mutedColor}>Sinyal/Dakika</Text>
                    <Text fontSize="xs" fontWeight="bold">
                      {stats.signals_per_minute.toFixed(1)}
                    </Text>
                  </HStack>
                </VStack>

                {/* Performance Bar */}
                <Box>
                  <Progress
                    value={Math.min((stats.avg_total_processing_time_ms / 1000) * 100, 100)}
                    colorScheme={performanceStatus.color}
                    size="sm"
                    borderRadius="full"
                  />
                  <HStack justify="space-between" mt={1}>
                    <Text fontSize="xs" color={mutedColor}>0ms</Text>
                    <Text fontSize="xs" color={mutedColor}>1000ms</Text>
                  </HStack>
                </Box>
              </VStack>
            </>
          )}

          {/* Recent Activity */}
          {showRecentActivity && recentMetrics.length > 0 && (
            <>
              <Divider />
              <VStack spacing={2} align="stretch">
                <Text fontSize="sm" fontWeight="medium" color={textColor}>
                  Son Aktivite
                </Text>
                <VStack spacing={1} align="stretch" maxH="120px" overflowY="auto">
                  <AnimatePresence>
                    {recentMetrics.map((metric, index) => (
                      <MotionBox
                        key={metric.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <HStack
                          justify="space-between"
                          p={2}
                          borderRadius="md"
                          bg={
                            metric.processing_status === 'success'
                              ? successBg
                              : metric.processing_status === 'partial'
                              ? warningBg
                              : errorBg
                          }
                        >
                          <HStack spacing={2}>
                            <Icon
                              as={
                                metric.processing_status === 'success'
                                  ? FiCheckCircle
                                  : FiAlertCircle
                              }
                              color={
                                metric.processing_status === 'success'
                                  ? 'green.500'
                                  : 'red.500'
                              }
                              boxSize={3}
                            />
                            <VStack align="start" spacing={0}>
                              <Text fontSize="xs" fontWeight="bold">
                                {metric.symbol}
                              </Text>
                              <Text fontSize="xs" color={mutedColor}>
                                {format(new Date(metric.created_at), 'HH:mm:ss', { locale: tr })}
                              </Text>
                            </VStack>
                          </HStack>

                          <VStack align="end" spacing={0}>
                            <Text fontSize="xs" fontWeight="bold">
                              {metric.total_processing_time_ms.toFixed(1)}ms
                            </Text>
                            <Badge
                              size="xs"
                              colorScheme={metric.signal_source === 'solo-robot' ? 'blue' : 'purple'}
                              variant="subtle"
                            >
                              {metric.signal_source === 'solo-robot' ? 'Solo' : 'Bro'}
                            </Badge>
                          </VStack>
                        </HStack>
                      </MotionBox>
                    ))}
                  </AnimatePresence>
                </VStack>
              </VStack>
            </>
          )}
        </VStack>
      </CardBody>
    </Card>
  );
};

export default OrderTransmissionStatusIndicator;
