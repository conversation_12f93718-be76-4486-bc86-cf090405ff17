import { Component, ErrorInfo, ReactNode } from "react";
import logger from "../utils/logger";
import ErrorFallback from "./ErrorFallback";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  isolate?: boolean; // Hata sadece bu component'i etkiler
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

/**
 * Gelişmiş Error Boundary - UX optimized
 * Better error handling, logging ve user experience
 */
class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
    errorId: null,
  };

  public static getDerivedStateFromError(error: Error): Partial<State> {
    // Generate unique error ID for tracking
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return {
      hasError: true,
      error,
      errorId
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Enhanced error logging
    const errorDetails = {
      errorId: this.state.errorId,
      componentStack: errorInfo.componentStack,
      errorBoundary: this.constructor.name,
      retryCount: this.retryCount,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getUserId(), // Helper method to get user ID if available
    };

    // Log to our logger
    logger.logError(error, errorDetails);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Store error info in state
    this.setState({ errorInfo });

    // Report to external error tracking service (if configured)
    this.reportToErrorService(error, errorDetails);
  }

  private getUserId = (): string | null => {
    try {
      // Try to get user ID from auth context or localStorage
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        return user.id || null;
      }
    } catch {
      // Ignore errors when getting user ID
    }
    return null;
  };

  private reportToErrorService = (error: Error, details: any) => {
    try {
      // Here you could integrate with services like Sentry, LogRocket, etc.
      console.error('[ErrorBoundary] Error reported:', {
        message: error.message,
        stack: error.stack,
        details
      });
    } catch (reportError) {
      console.error('[ErrorBoundary] Failed to report error:', reportError);
    }
  };

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
      });
    } else {
      // Max retries reached, reload page
      window.location.reload();
    }
  };



  public render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Use enhanced ErrorFallback component
      return (
        <ErrorFallback
          error={this.state.error}
          resetErrorBoundary={this.handleRetry}
          message="Bir şeyler ters gitti"
          variant="detailed"
          showDetails={process.env.NODE_ENV === 'development'}
          showHomeButton={!this.props.isolate}
        />
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 