---
type: "manual"
priority: "high"
scope: ["responsive", "mobile", "accessibility", "touch", "ux"]
last_updated: "2025-01-30"
created: "2025-01-30"
---

# Responsive Design & Mobile-First Implementation

## 📱 Mobile-First Design Philosophy

### Core Principles
```typescript
// Mobile-first breakpoint system
export const BREAKPOINTS = {
  xs: '320px',    // Extra small phones
  sm: '480px',    // Small phones
  md: '768px',    // Tablets
  lg: '992px',    // Small desktops
  xl: '1280px',   // Large desktops
  '2xl': '1536px', // Extra large screens
  '3xl': '1920px', // Ultra wide screens
} as const;

// Design approach: Start with mobile, enhance for larger screens
const mobileFirstApproach = {
  design: 'Mobile constraints drive better UX decisions',
  performance: 'Mobile-optimized code performs better on all devices',
  accessibility: 'Touch-first design improves accessibility',
  future: 'Mobile usage continues to grow globally'
};
```

## 🎯 Responsive Utilities System

### Device Detection & Breakpoint Management
```typescript
// responsiveUtils.ts - Comprehensive responsive utilities
export const useDeviceType = (): DeviceType => {
  const [isMobile] = useMediaQuery(`(max-width: ${BREAKPOINTS.md})`);
  const [isTablet] = useMediaQuery(`(min-width: ${BREAKPOINTS.md}) and (max-width: ${BREAKPOINTS.lg})`);
  const [isUltrawide] = useMediaQuery(`(min-width: ${BREAKPOINTS['3xl']})`);

  if (isMobile) return 'mobile';
  if (isTablet) return 'tablet';
  if (isUltrawide) return 'ultrawide';
  return 'desktop';
};

// Screen size detection
export const useScreenSize = (): ScreenSize => {
  // Returns: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl'
};

// Touch device detection
export const useIsTouchDevice = (): boolean => {
  return 'ontouchstart' in window || 
         navigator.maxTouchPoints > 0 || 
         (window as any).DocumentTouch && document instanceof (window as any).DocumentTouch;
};
```

### Responsive Container System
```typescript
// ResponsiveContainer.tsx - Mobile-first containers
interface ResponsiveContainerProps {
  variant?: 'page' | 'section' | 'card' | 'full';
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}

// Usage examples:
<ResponsiveContainer variant="page" spacing="md">
  {/* Page content with consistent spacing */}
</ResponsiveContainer>

<ResponsiveGrid columns={{ base: 1, md: 2, lg: 3, xl: 4 }}>
  {items.map(item => <Card key={item.id} {...item} />)}
</ResponsiveGrid>

<ResponsiveStack direction="responsive" spacing={{ base: 4, md: 6 }}>
  <Button>Action 1</Button>
  <Button>Action 2</Button>
</ResponsiveStack>
```

## 👆 Touch Interactions & Gestures

### Advanced Touch Support
```typescript
// useTouchInteractions.ts - Comprehensive touch gesture support
interface TouchInteractionOptions {
  enableSwipe?: boolean;
  enablePinch?: boolean;
  enableTap?: boolean;
  enableLongPress?: boolean;
  swipeThreshold?: number;
  longPressDelay?: number;
  preventScroll?: boolean;
}

// Usage example:
const { isTouch, touchCount } = useTouchInteractions(
  elementRef,
  { 
    enableSwipe: true, 
    enableTap: true,
    swipeThreshold: 50 
  },
  {
    onSwipe: (gesture) => {
      switch (gesture.direction) {
        case 'left': handleNext(); break;
        case 'right': handlePrevious(); break;
        case 'up': handleScrollUp(); break;
        case 'down': handleScrollDown(); break;
      }
    },
    onTap: (point) => handleTap(point),
    onDoubleTap: (point) => handleDoubleTap(point),
    onLongPress: (point) => showContextMenu(point)
  }
);
```

### Touch Target Optimization
```typescript
// Touch target sizes (WCAG compliance)
export const TOUCH_TARGETS = {
  minimum: '44px',    // WCAG minimum
  comfortable: '48px', // Recommended
  large: '56px',      // Large touch targets
} as const;

// Implementation in components:
<IconButton
  minW={TOUCH_TARGETS.comfortable}
  minH={TOUCH_TARGETS.comfortable}
  borderRadius="full"
  aria-label="Menu"
/>
```

## 📐 Responsive Typography & Spacing

### Typography Scale System
```typescript
export const RESPONSIVE_TYPOGRAPHY = {
  heading: {
    h1: { base: '2xl', md: '3xl', lg: '4xl', xl: '5xl' },
    h2: { base: 'xl', md: '2xl', lg: '3xl', xl: '4xl' },
    h3: { base: 'lg', md: 'xl', lg: '2xl', xl: '3xl' },
    h4: { base: 'md', md: 'lg', lg: 'xl', xl: '2xl' },
  },
  body: {
    large: { base: 'md', md: 'lg', lg: 'xl' },
    normal: { base: 'sm', md: 'md', lg: 'lg' },
    small: { base: 'xs', md: 'sm', lg: 'md' },
  }
};

// Usage:
<Heading size={getResponsiveTypography('heading', 'h1')}>
  Main Title
</Heading>
```

### Spacing System
```typescript
export const RESPONSIVE_SPACING = {
  xs: { base: 2, container: 4 },
  sm: { base: 3, container: 6 },
  md: { base: 4, container: 8 },
  lg: { base: 6, container: 12 },
  xl: { base: 8, container: 16 },
  '2xl': { base: 10, container: 20 },
};
```

## 🔍 Mobile Search Experience

### Enhanced Mobile Search
```typescript
// MobileSearchDrawer.tsx - Full-screen mobile search
const MobileSearchDrawer: React.FC<MobileSearchDrawerProps> = ({
  isOpen,
  onClose,
  placeholder = "Ara..."
}) => {
  // Features:
  // - Full-screen drawer for mobile
  // - Touch gesture support (swipe to close)
  // - Keyboard navigation
  // - Recent searches functionality
  // - Voice search ready
  // - Optimized touch targets
  
  return (
    <Drawer
      isOpen={isOpen}
      placement="top"
      onClose={onClose}
      size="full"
    >
      <DrawerContent>
        {/* Enhanced search interface */}
      </DrawerContent>
    </Drawer>
  );
};
```

## ♿ Accessibility Integration

### Comprehensive Accessibility Support
```typescript
// useAccessibility.ts - A11y features
const { 
  announce, 
  focusElement, 
  toggleHighContrast,
  addShortcut 
} = useAccessibility({
  announcePageChanges: true,
  manageFocus: true,
  enableKeyboardShortcuts: true
});

// Built-in keyboard shortcuts:
// Alt + S: Skip to main content
// Alt + H: Toggle high contrast
// Alt + M: Toggle reduced motion
// Ctrl + K: Focus search
// Ctrl + /: Show shortcuts help
```

### Safe Area Support
```typescript
// Safe area utilities for devices with notches
export const useSafeArea = () => {
  const [safeArea, setSafeArea] = useState({
    top: 0, right: 0, bottom: 0, left: 0
  });

  useEffect(() => {
    const updateSafeArea = () => {
      const style = getComputedStyle(document.documentElement);
      setSafeArea({
        top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
        right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
        bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
        left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0'),
      });
    };

    updateSafeArea();
    window.addEventListener('orientationchange', updateSafeArea);
    return () => window.removeEventListener('orientationchange', updateSafeArea);
  }, []);

  return safeArea;
};
```

## 🎨 Responsive Grid Systems

### Predefined Grid Configurations
```typescript
export const RESPONSIVE_GRIDS = {
  cards: { base: 1, sm: 2, md: 2, lg: 3, xl: 4, '2xl': 5 },
  dashboard: { base: 1, md: 2, lg: 3, xl: 4 },
  marketplace: { base: 1, sm: 2, lg: 3, xl: 4, '2xl': 5 },
  statistics: { base: 1, md: 2, xl: 3 }
};

// Usage:
<ResponsiveGrid columns={RESPONSIVE_GRIDS.marketplace}>
  {products.map(product => <ProductCard key={product.id} {...product} />)}
</ResponsiveGrid>
```

## 📊 Performance Considerations

### Mobile Performance Optimization
```typescript
// Reduced motion support
export const useReducedMotion = (): boolean => {
  const [prefersReducedMotion] = useMediaQuery('(prefers-reduced-motion: reduce)');
  return prefersReducedMotion;
};

// High contrast support
export const useHighContrast = (): boolean => {
  const [prefersHighContrast] = useMediaQuery('(prefers-contrast: high)');
  return prefersHighContrast;
};

// Viewport optimization
export const useViewportSize = () => {
  const [size, setSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  useEffect(() => {
    const handleResize = () => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return size;
};
```

## 🔧 Implementation Guidelines

### Component Development Rules
1. **Mobile-First**: Always start with mobile design and enhance for larger screens
2. **Touch Targets**: Minimum 44px touch targets for all interactive elements
3. **Gestures**: Implement swipe, tap, and long press where appropriate
4. **Accessibility**: Include ARIA labels, keyboard navigation, and screen reader support
5. **Performance**: Optimize for mobile networks and lower-end devices
6. **Safe Areas**: Account for device notches and rounded corners

### Testing Requirements
```typescript
// Responsive testing checklist
const testingChecklist = {
  devices: ['iPhone SE', 'iPhone 12', 'iPad', 'Desktop 1920x1080'],
  orientations: ['Portrait', 'Landscape'],
  interactions: ['Touch', 'Mouse', 'Keyboard'],
  accessibility: ['Screen Reader', 'High Contrast', 'Reduced Motion'],
  performance: ['3G Network', 'Slow CPU', 'Low Memory']
};
```

## Update Requirements

### When to Update This File
- New responsive components added
- Breakpoint system changes
- Touch interaction improvements
- Accessibility enhancements
- Mobile UX optimizations
- Performance improvements for mobile devices

### Related Files to Update
- Update `frontend/MAIN_APP.mdc` for component integration
- Update `performance/OPTIMIZATION.mdc` for mobile performance
- Update `security/GUIDELINES.mdc` for mobile security considerations
