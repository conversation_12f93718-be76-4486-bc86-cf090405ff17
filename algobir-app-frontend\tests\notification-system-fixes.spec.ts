import { test, expect } from '@playwright/test';
import { 
  loginUser, 
  goToNotifications, 
  waitForPageLoad,
  disableDebugMode,
  TEST_USER
} from './utils/test-helpers';

test.describe('Bildirim Sistemi Düzeltmeleri', () => {
  test.beforeEach(async ({ page }) => {
    await loginUser(page);
    await waitForPageLoad(page);
  });

  test('Bildirim sistemi çalışıyor ve bildirimler görüntüleniyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Bildirim listesinin görünür olduğunu kontrol et
    await expect(page.locator('[data-testid="notifications-list"]')).toBeVisible();
    
    // En az bir bildirim olduğunu kontrol et
    const notifications = page.locator('[data-testid="notification-item"]');
    const notificationCount = await notifications.count();
    expect(notificationCount).toBeGreaterThan(0);
    
    console.log(`✅ Toplam ${notificationCount} bildirim bulundu`);
  });

  test('Trade bildirimleri doğru Türkçe formatında görüntüleniyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Trade bildirimi arayalım
    const tradeNotifications = page.locator('[data-testid="notification-item"]').filter({
      hasText: /Al Bildirimi|Sat Bildirimi/
    });
    
    if (await tradeNotifications.count() > 0) {
      const firstTradeNotification = tradeNotifications.first();
      const message = await firstTradeNotification.locator('[data-testid="notification-message"]').textContent();
      
      console.log(`📊 Trade bildirimi mesajı: ${message}`);
      
      // Türkçe format kontrolü
      expect(message).toMatch(/Al Bildirimi|Sat Bildirimi/);
      expect(message).toMatch(/hissesinden/);
      expect(message).toMatch(/TL fiyatla/);
      expect(message).toMatch(/adet/);
      expect(message).toMatch(/Toplam tutar/);
      expect(message).toMatch(/₺[\d,]+/);
      expect(message).toMatch(/Robot/);
      
      console.log('✅ Trade bildirimi formatı doğru');
    } else {
      console.log('⚠️ Trade bildirimi bulunamadı');
    }
  });

  test('Bildirim sayısı navbar\'da doğru görüntüleniyor', async ({ page }) => {
    // Ana sayfaya git
    await page.goto('/');
    await waitForPageLoad(page);
    
    // Bildirim butonunu bul
    const notificationButton = page.locator('button[aria-label="Bildirimler"]');
    await expect(notificationButton).toBeVisible();
    
    // Bildirim sayısı badge'ini kontrol et
    const badge = notificationButton.locator('.chakra-badge');
    if (await badge.isVisible()) {
      const badgeText = await badge.textContent();
      const badgeCount = parseInt(badgeText || '0');
      expect(badgeCount).toBeGreaterThan(0);
      
      console.log(`✅ Navbar'da ${badgeCount} okunmamış bildirim gösteriliyor`);
    } else {
      console.log('ℹ️ Okunmamış bildirim yok');
    }
  });

  test('Bildirim dropdown menüsü çalışıyor', async ({ page }) => {
    await page.goto('/');
    await waitForPageLoad(page);
    
    // Bildirim butonuna tıkla
    const notificationButton = page.locator('button[aria-label="Bildirimler"]');
    await notificationButton.click();
    
    // Dropdown menüsünün açılmasını bekle
    await expect(page.locator('[data-testid="notification-dropdown-menu"]')).toBeVisible();
    
    // Dropdown'da bildirimler olduğunu kontrol et
    const dropdownNotifications = page.locator('[data-testid="notification-dropdown-menu"] [data-testid="notification-item"]');
    const dropdownCount = await dropdownNotifications.count();
    
    if (dropdownCount > 0) {
      console.log(`✅ Dropdown'da ${dropdownCount} bildirim görüntüleniyor`);
      
      // İlk bildirimin içeriğini kontrol et
      const firstNotification = dropdownNotifications.first();
      await expect(firstNotification.locator('[data-testid="notification-title"]')).toBeVisible();
      await expect(firstNotification.locator('[data-testid="notification-message"]')).toBeVisible();
    } else {
      console.log('ℹ️ Dropdown\'da bildirim yok');
    }
  });

  test('Manuel bildirim oluşturma testi', async ({ page }) => {
    await goToNotifications(page);
    
    // Mevcut bildirim sayısını al
    const initialCount = await page.locator('[data-testid="notification-item"]').count();
    console.log(`📊 Başlangıç bildirim sayısı: ${initialCount}`);
    
    // Test bildirimi oluşturmak için RPC test butonunu kullan
    const rpcTestButton = page.locator('button:has-text("RPC Test")');
    if (await rpcTestButton.isVisible()) {
      await rpcTestButton.click();
      
      // Toast mesajının görünmesini bekle
      await expect(page.locator('.chakra-toast')).toBeVisible();
      console.log('✅ RPC test bildirimi oluşturuldu');
      
      // Sayfayı yenile
      await page.reload();
      await waitForPageLoad(page);
      
      // Yeni bildirim sayısını kontrol et
      const newCount = await page.locator('[data-testid="notification-item"]').count();
      expect(newCount).toBeGreaterThanOrEqual(initialCount);
      console.log(`📊 Yeni bildirim sayısı: ${newCount}`);
    } else {
      console.log('⚠️ RPC Test butonu bulunamadı');
    }
  });

  test('Bildirim real-time güncellemesi', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Mevcut bildirim sayısını al
    const initialCount = await page.locator('[data-testid="notification-item"]').count();
    
    // Yeni sekmede trade oluştur (simülasyon)
    const newPage = await page.context().newPage();
    await loginUser(newPage);
    await newPage.goto('/management');
    
    // Test trade sinyali gönder (eğer mevcut ise)
    const testButton = newPage.locator('button:has-text("Test")').first();
    if (await testButton.isVisible()) {
      await testButton.click();
      await waitForPageLoad(newPage);
    }
    
    await newPage.close();
    
    // Ana sayfada bildirim güncellemesini bekle
    await page.waitForTimeout(3000);
    await page.reload();
    await waitForPageLoad(page);
    
    const newCount = await page.locator('[data-testid="notification-item"]').count();
    console.log(`📊 Real-time test sonrası bildirim sayısı: ${newCount}`);
  });

  test('Bildirim türleri ve ikonları doğru görüntüleniyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    const notifications = page.locator('[data-testid="notification-item"]');
    const count = await notifications.count();
    
    for (let i = 0; i < Math.min(count, 5); i++) {
      const notification = notifications.nth(i);
      const title = await notification.locator('[data-testid="notification-title"]').textContent();
      const icon = notification.locator('img').first();
      
      // Her bildirimin ikonu olduğunu kontrol et
      await expect(icon).toBeVisible();
      
      console.log(`✅ Bildirim ${i + 1}: "${title}" - İkon mevcut`);
    }
  });

  test('Bildirim aksiyon linkleri çalışıyor', async ({ page }) => {
    await goToNotifications(page);
    await disableDebugMode(page);
    
    // Aksiyon linki olan bildirimi bul
    const notificationWithAction = page.locator('[data-testid="notification-item"]').filter({
      has: page.locator('a')
    }).first();
    
    if (await notificationWithAction.isVisible()) {
      const actionLink = notificationWithAction.locator('a').first();
      const href = await actionLink.getAttribute('href');
      
      if (href) {
        await actionLink.click();
        await waitForPageLoad(page);
        
        // URL'nin değiştiğini kontrol et
        expect(page.url()).toContain(href);
        console.log(`✅ Aksiyon linki çalışıyor: ${href}`);
      }
    } else {
      console.log('ℹ️ Aksiyon linki olan bildirim bulunamadı');
    }
  });
});
