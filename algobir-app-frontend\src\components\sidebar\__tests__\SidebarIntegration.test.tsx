import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ChakraProvider } from '@chakra-ui/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { Icon } from '@chakra-ui/react';
import { FiHome, FiSettings, FiBarChart2 } from 'react-icons/fi';
import { vi } from 'vitest';
import Sidebar from '../Sidebar';
import SubmenuPanel from '../components/SubmenuPanel';
import { SidebarProvider, useSidebar } from '../../../context/SidebarContext';
import { RouteType } from '../../../routes';

// Mock routes with submenu functionality
const mockRoutesWithSubmenus: RouteType[] = [
  {
    name: 'Dashboard',
    layout: '',
    path: '/',
    icon: <Icon as={FiHome} width="20px" height="20px" color="inherit" />,
  },
  {
    name: 'Statistics',
    layout: '',
    path: '/statistics',
    icon: <Icon as={FiBarChart2} width="20px" height="20px" color="inherit" />,
    hasSubmenu: true,
    children: [
      {
        id: 'performance',
        name: 'Performance Analysis',
        path: '/statistics?tab=performance',
        description: 'ROI and profitability analysis'
      },
      {
        id: 'time-analysis',
        name: 'Time Analysis',
        path: '/statistics?tab=time-analysis',
        description: 'Time-based performance'
      }
    ]
  },
  {
    name: 'Settings',
    layout: '',
    path: '/settings',
    icon: <Icon as={FiSettings} width="20px" height="20px" color="inherit" />,
    hasSubmenu: true,
    children: [
      {
        id: 'profile',
        name: 'Profile Settings',
        path: '/settings/profile',
        description: 'Manage your profile'
      }
    ]
  }
];

// Test component that includes both sidebar and submenu
const SidebarWithSubmenuTest = () => {
  const { hasActiveSubmenu, submenuItems, activeSubmenu } = useSidebar();
  
  return (
    <>
      <Sidebar routes={mockRoutesWithSubmenus} />
      {hasActiveSubmenu && (
        <SubmenuPanel
          isOpen={hasActiveSubmenu}
          items={submenuItems}
          title={mockRoutesWithSubmenus.find(r => r.path === activeSubmenu)?.name}
        />
      )}
    </>
  );
};

// Mock hooks and dependencies
vi.mock('../../../hooks/useAdminAuth', () => ({
  useAdminAuth: () => ({ isAdmin: true })
}));

vi.mock('react-custom-scrollbars-2', () => ({
  Scrollbars: ({ children, ...props }: any) => (
    <div data-testid="scrollbars" {...props}>
      {children}
    </div>
  )
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ChakraProvider>
      <BrowserRouter>
        <SidebarProvider>
          {component}
        </SidebarProvider>
      </BrowserRouter>
    </ChakraProvider>
  );
};

describe('Sidebar Integration Tests', () => {
  beforeEach(() => {
    // Mock desktop breakpoint
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: query.includes('(min-width: 992px)'),
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    // Mock window dimensions
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 1000,
    });
  });

  afterEach(() => {
    // Clean up event listeners
    document.removeEventListener('mousemove', vi.fn());
    document.removeEventListener('keydown', vi.fn());
  });

  test('should render sidebar with all menu items', () => {
    renderWithProviders(<SidebarWithSubmenuTest />);
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Statistics')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  test('should open submenu when clicking on menu item with submenu', async () => {
    renderWithProviders(<SidebarWithSubmenuTest />);
    
    // Click on Statistics menu item (has submenu)
    const statisticsItem = screen.getByText('Statistics').closest('[role="button"]');
    expect(statisticsItem).toBeInTheDocument();
    
    fireEvent.click(statisticsItem!);
    
    // Wait for submenu to appear
    await waitFor(() => {
      expect(screen.getByText('Performance Analysis')).toBeInTheDocument();
      expect(screen.getByText('Time Analysis')).toBeInTheDocument();
    });
  });

  test('should close submenu when clicking close button', async () => {
    renderWithProviders(<SidebarWithSubmenuTest />);
    
    // First open submenu
    const statisticsItem = screen.getByText('Statistics').closest('[role="button"]');
    fireEvent.click(statisticsItem!);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Analysis')).toBeInTheDocument();
    });
    
    // Then close it
    const closeButton = screen.getByRole('button', { name: /alt menüyü kapat/i });
    fireEvent.click(closeButton);
    
    await waitFor(() => {
      expect(screen.queryByText('Performance Analysis')).not.toBeInTheDocument();
    });
  });

  test('should handle keyboard navigation for submenu', async () => {
    renderWithProviders(<SidebarWithSubmenuTest />);
    
    // Open submenu
    const statisticsItem = screen.getByText('Statistics').closest('[role="button"]');
    fireEvent.click(statisticsItem!);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Analysis')).toBeInTheDocument();
    });
    
    // Press Escape to close
    fireEvent.keyDown(document, { key: 'Escape' });
    
    await waitFor(() => {
      expect(screen.queryByText('Performance Analysis')).not.toBeInTheDocument();
    });
  });

  test('should handle multiple submenu interactions', async () => {
    renderWithProviders(<SidebarWithSubmenuTest />);
    
    // Open Statistics submenu
    const statisticsItem = screen.getByText('Statistics').closest('[role="button"]');
    fireEvent.click(statisticsItem!);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Analysis')).toBeInTheDocument();
    });
    
    // Switch to Settings submenu
    const settingsItem = screen.getByText('Settings').closest('[role="button"]');
    fireEvent.click(settingsItem!);
    
    await waitFor(() => {
      expect(screen.getByText('Profile Settings')).toBeInTheDocument();
      expect(screen.queryByText('Performance Analysis')).not.toBeInTheDocument();
    });
  });

  test('should handle mouse edge detection for auto-expand', async () => {
    renderWithProviders(<SidebarWithSubmenuTest />);
    
    // Simulate mouse move near left edge
    fireEvent(document, new MouseEvent('mousemove', {
      clientX: 30,
      clientY: 500,
      bubbles: true
    }));
    
    // Wait for potential state changes
    await waitFor(() => {
      // The sidebar should detect mouse near edge
      // State changes are handled internally by the context
      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });
  });

  test('should handle keyboard shortcuts for sidebar control', async () => {
    renderWithProviders(<SidebarWithSubmenuTest />);
    
    // Test Ctrl+B to toggle sidebar
    fireEvent.keyDown(document, { key: 'b', ctrlKey: true });
    
    // Test Ctrl+Shift+E to expand
    fireEvent.keyDown(document, { key: 'E', ctrlKey: true, shiftKey: true });
    
    // Test Ctrl+Shift+C to collapse
    fireEvent.keyDown(document, { key: 'C', ctrlKey: true, shiftKey: true });
    
    // Keyboard shortcuts are handled by the context
    // We verify the sidebar still exists after keyboard interactions
    await waitFor(() => {
      expect(screen.getByRole('navigation')).toBeInTheDocument();
    });
  });

  test('should maintain accessibility during state changes', async () => {
    renderWithProviders(<SidebarWithSubmenuTest />);
    
    const navigation = screen.getByRole('navigation');
    expect(navigation).toHaveAttribute('aria-expanded', 'true');
    
    // Open submenu and verify accessibility
    const statisticsItem = screen.getByText('Statistics').closest('[role="button"]');
    fireEvent.click(statisticsItem!);
    
    await waitFor(() => {
      const submenuDialog = screen.getByRole('dialog');
      expect(submenuDialog).toHaveAttribute('aria-modal', 'true');
      expect(submenuDialog).toHaveAttribute('aria-label', 'Statistics alt menüsü');
    });
  });

  test('should handle route navigation from submenu items', async () => {
    renderWithProviders(<SidebarWithSubmenuTest />);
    
    // Open submenu
    const statisticsItem = screen.getByText('Statistics').closest('[role="button"]');
    fireEvent.click(statisticsItem!);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Analysis')).toBeInTheDocument();
    });
    
    // Click on submenu item
    const performanceLink = screen.getByRole('link', { name: /performance analysis/i });
    expect(performanceLink).toHaveAttribute('href', '/statistics?tab=performance');
    
    fireEvent.click(performanceLink);
    
    // Submenu should close after navigation
    await waitFor(() => {
      expect(screen.queryByText('Performance Analysis')).not.toBeInTheDocument();
    });
  });
});
