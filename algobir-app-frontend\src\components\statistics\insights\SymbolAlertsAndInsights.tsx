import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  Badge,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Button,

  Collapse,
  useDisclosure,
  useColorModeValue,
  SimpleGrid,
  Icon,

  IconButton,
  Flex,

  Select,
  Switch,
  FormControl,
  FormLabel,
  Divider
} from '@chakra-ui/react';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  WarningIcon,
  InfoIcon,
  CheckCircleIcon,
  CloseIcon,
  BellIcon,
  ArrowUpIcon,

} from '@chakra-ui/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { SymbolAlert, SymbolInsight } from '../../../types/symbolAnalysis';

const MotionBox = motion(Box);

interface SymbolAlertsAndInsightsProps {
  alerts: SymbolAlert[];
  insights: SymbolInsight[];
  onDismissAlert?: (alertId: string) => void;
  onSymbolClick?: (symbol: string) => void;
  compact?: boolean;
}

const SymbolAlertsAndInsights: React.FC<SymbolAlertsAndInsightsProps> = ({
  alerts,
  insights,
  onDismissAlert,
  onSymbolClick,
  compact = false
}) => {
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedInsightType, setSelectedInsightType] = useState<string>('all');
  const [showOnlyActive, setShowOnlyActive] = useState(true);
  const [sortBy] = useState<'severity' | 'date' | 'symbol'>('severity');

  const { isOpen: isAlertsOpen, onToggle: toggleAlerts } = useDisclosure({ defaultIsOpen: true });
  const { isOpen: isInsightsOpen, onToggle: toggleInsights } = useDisclosure({ defaultIsOpen: true });

  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');

  // Filter and sort alerts
  const filteredAlerts = useMemo(() => {
    let filtered = alerts.filter(alert => {
      if (showOnlyActive && !alert.isActive) return false;
      if (selectedSeverity !== 'all' && alert.severity !== selectedSeverity) return false;
      return true;
    });

    // Sort alerts
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'severity':
          const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
          return severityOrder[b.severity] - severityOrder[a.severity];
        case 'date':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'symbol':
          return a.symbol.localeCompare(b.symbol);
        default:
          return 0;
      }
    });

    return filtered;
  }, [alerts, selectedSeverity, showOnlyActive, sortBy]);

  // Filter and sort insights
  const filteredInsights = useMemo(() => {
    let filtered = insights.filter(insight => {
      if (selectedInsightType !== 'all' && insight.insightType !== selectedInsightType) return false;
      return true;
    });

    // Sort by confidence
    filtered.sort((a, b) => b.confidence - a.confidence);

    return filtered;
  }, [insights, selectedInsightType]);

  // Get alert icon and color
  const getAlertConfig = (alert: SymbolAlert) => {
    switch (alert.severity) {
      case 'critical':
        return {
          icon: WarningIcon,
          colorScheme: 'red',
          bgColor: 'red.50',
          borderColor: 'red.200',
          alertStatus: 'error' as const
        };
      case 'high':
        return {
          icon: WarningIcon,
          colorScheme: 'orange',
          bgColor: 'orange.50',
          borderColor: 'orange.200',
          alertStatus: 'warning' as const
        };
      case 'medium':
        return {
          icon: InfoIcon,
          colorScheme: 'yellow',
          bgColor: 'yellow.50',
          borderColor: 'yellow.200',
          alertStatus: 'warning' as const
        };
      case 'low':
        return {
          icon: InfoIcon,
          colorScheme: 'blue',
          bgColor: 'blue.50',
          borderColor: 'blue.200',
          alertStatus: 'info' as const
        };
      default:
        return {
          icon: InfoIcon,
          colorScheme: 'gray',
          bgColor: 'gray.50',
          borderColor: 'gray.200',
          alertStatus: 'info' as const
        };
    }
  };

  // Get insight icon and color
  const getInsightConfig = (insight: SymbolInsight) => {
    switch (insight.insightType) {
      case 'opportunity':
        return { icon: ArrowUpIcon, colorScheme: 'green', bgColor: 'green.50', borderColor: 'green.200' };
      case 'warning':
        return { icon: WarningIcon, colorScheme: 'orange', bgColor: 'orange.50', borderColor: 'orange.200' };
      case 'trend':
        return { icon: ArrowUpIcon, colorScheme: 'blue', bgColor: 'blue.50', borderColor: 'blue.200' };
      case 'anomaly':
        return { icon: InfoIcon, colorScheme: 'purple', bgColor: 'purple.50', borderColor: 'purple.200' };
      default:
        return { icon: InfoIcon, colorScheme: 'gray', bgColor: 'gray.50', borderColor: 'gray.200' };
    }
  };

  // Get alert type label
  const getAlertTypeLabel = (type: string) => {
    const labels = {
      performance: 'Performans',
      risk: 'Risk',
      volume: 'Hacim',
      pattern: 'Desen'
    };
    return labels[type as keyof typeof labels] || type;
  };

  // Get insight type label
  const getInsightTypeLabel = (type: string) => {
    const labels = {
      opportunity: 'Fırsat',
      warning: 'Uyarı',
      trend: 'Trend',
      anomaly: 'Anomali'
    };
    return labels[type as keyof typeof labels] || type;
  };

  return (
    <VStack spacing={6} align="stretch">
      {/* Alerts Section */}
      <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
        <CardBody>
          <VStack spacing={4} align="stretch">
            {/* Alerts Header */}
            <VStack spacing={3} align="stretch">
              <Flex
                direction={{ base: "column", md: "row" }}
                align={{ base: "start", md: "center" }}
                justify="space-between"
                gap={3}
              >
                <HStack>
                  <Icon as={BellIcon} color="orange.500" />
                  <Text fontSize={{ base: "md", md: "lg" }} fontWeight="bold">
                    Uyarılar
                  </Text>
                  <Badge colorScheme="orange" variant="subtle">
                    {filteredAlerts.length}
                  </Badge>
                </HStack>
                <HStack spacing={2} w={{ base: "full", md: "auto" }}>
                  {!compact && (
                    <>
                      <Select
                        value={selectedSeverity}
                        onChange={(e) => setSelectedSeverity(e.target.value)}
                        size="sm"
                        w={{ base: "full", md: "120px" }}
                      >
                        <option value="all">Tüm Seviyeler</option>
                        <option value="critical">Kritik</option>
                        <option value="high">Yüksek</option>
                        <option value="medium">Orta</option>
                        <option value="low">Düşük</option>
                      </Select>
                      <FormControl display="flex" alignItems="center" w="auto">
                        <FormLabel htmlFor="active-only" mb="0" fontSize="sm" minW="fit-content">
                          Sadece Aktif
                        </FormLabel>
                        <Switch
                          id="active-only"
                          isChecked={showOnlyActive}
                          onChange={(e) => setShowOnlyActive(e.target.checked)}
                          size="sm"
                        />
                      </FormControl>
                    </>
                  )}
                  <IconButton
                    aria-label="Toggle alerts"
                    icon={isAlertsOpen ? <ChevronUpIcon /> : <ChevronDownIcon />}
                    size="sm"
                    variant="ghost"
                    onClick={toggleAlerts}
                  />
                </HStack>
              </Flex>
            </VStack>

            {/* Alerts Content */}
            <Collapse in={isAlertsOpen}>
              <VStack spacing={3} align="stretch">
                {filteredAlerts.length === 0 ? (
                  <Alert status="success" borderRadius="md">
                    <AlertIcon />
                    <AlertDescription>
                      Şu anda aktif uyarı bulunmuyor. Tüm semboller normal parametreler içinde.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <AnimatePresence>
                    {filteredAlerts.map((alert, index) => {
                      const config = getAlertConfig(alert);
                      return (
                        <MotionBox
                          key={alert.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <Alert
                            status={config.alertStatus}
                            borderRadius="md"
                            bg={useColorModeValue(config.bgColor, 'gray.700')}
                            borderColor={useColorModeValue(config.borderColor, 'gray.600')}
                            borderWidth="1px"
                          >
                            <AlertIcon as={config.icon} />
                            <Box flex="1">
                              <HStack justify="space-between" align="start">
                                <VStack align="start" spacing={1}>
                                  <HStack>
                                    <AlertTitle fontSize="sm" fontWeight="bold">
                                      {alert.title}
                                    </AlertTitle>
                                    <Badge 
                                      colorScheme={config.colorScheme} 
                                      variant="subtle" 
                                      size="sm"
                                    >
                                      {getAlertTypeLabel(alert.alertType)}
                                    </Badge>
                                    <Button
                                      size="xs"
                                      variant="link"
                                      colorScheme="blue"
                                      onClick={() => onSymbolClick?.(alert.symbol)}
                                    >
                                      {alert.symbol}
                                    </Button>
                                  </HStack>
                                  <AlertDescription fontSize="sm">
                                    {alert.description}
                                  </AlertDescription>
                                  <Text fontSize="xs" color="gray.500">
                                    {new Date(alert.createdAt).toLocaleString('tr-TR')}
                                  </Text>
                                </VStack>
                                {onDismissAlert && (
                                  <IconButton
                                    aria-label="Dismiss alert"
                                    icon={<CloseIcon />}
                                    size="xs"
                                    variant="ghost"
                                    onClick={() => onDismissAlert(alert.id)}
                                  />
                                )}
                              </HStack>
                            </Box>
                          </Alert>
                        </MotionBox>
                      );
                    })}
                  </AnimatePresence>
                )}
              </VStack>
            </Collapse>
          </VStack>
        </CardBody>
      </Card>

      {/* Insights Section */}
      <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
        <CardBody>
          <VStack spacing={4} align="stretch">
            {/* Insights Header */}
            <Flex align="center" justify="space-between">
              <HStack>
                <Icon as={CheckCircleIcon} color="green.500" />
                <Text fontSize="lg" fontWeight="bold">
                  Akıllı Öngörüler
                </Text>
                <Badge colorScheme="green" variant="subtle">
                  {filteredInsights.length}
                </Badge>
              </HStack>
              <HStack>
                {!compact && (
                  <Select
                    value={selectedInsightType}
                    onChange={(e) => setSelectedInsightType(e.target.value)}
                    size="sm"
                    w="120px"
                  >
                    <option value="all">Tüm Türler</option>
                    <option value="opportunity">Fırsat</option>
                    <option value="warning">Uyarı</option>
                    <option value="trend">Trend</option>
                    <option value="anomaly">Anomali</option>
                  </Select>
                )}
                <IconButton
                  aria-label="Toggle insights"
                  icon={isInsightsOpen ? <ChevronUpIcon /> : <ChevronDownIcon />}
                  size="sm"
                  variant="ghost"
                  onClick={toggleInsights}
                />
              </HStack>
            </Flex>

            {/* Insights Content */}
            <Collapse in={isInsightsOpen}>
              <VStack spacing={3} align="stretch">
                {filteredInsights.length === 0 ? (
                  <Alert status="info" borderRadius="md">
                    <AlertIcon />
                    <AlertDescription>
                      Şu anda öngörü bulunmuyor. Daha fazla veri toplandıkça öngörüler oluşturulacak.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <SimpleGrid columns={{ base: 1, lg: compact ? 1 : 2 }} spacing={3}>
                    <AnimatePresence>
                      {filteredInsights.map((insight, index) => {
                        const config = getInsightConfig(insight);
                        return (
                          <MotionBox
                            key={insight.id}
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.95 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <Card
                              bg={useColorModeValue(config.bgColor, 'gray.700')}
                              borderColor={useColorModeValue(config.borderColor, 'gray.600')}
                              borderWidth="1px"
                              size="sm"
                            >
                              <CardBody>
                                <VStack align="start" spacing={3}>
                                  <HStack justify="space-between" w="100%">
                                    <HStack>
                                      <Icon as={config.icon} color={`${config.colorScheme}.500`} />
                                      <Badge 
                                        colorScheme={config.colorScheme} 
                                        variant="subtle"
                                      >
                                        {getInsightTypeLabel(insight.insightType)}
                                      </Badge>
                                      <Button
                                        size="xs"
                                        variant="link"
                                        colorScheme="blue"
                                        onClick={() => onSymbolClick?.(insight.symbol)}
                                      >
                                        {insight.symbol}
                                      </Button>
                                    </HStack>
                                    <Badge colorScheme="gray" variant="outline">
                                      %{insight.confidence} güven
                                    </Badge>
                                  </HStack>
                                  
                                  <VStack align="start" spacing={2}>
                                    <Text fontSize="sm" fontWeight="bold">
                                      {insight.title}
                                    </Text>
                                    <Text fontSize="sm" color={textColor}>
                                      {insight.description}
                                    </Text>
                                    {insight.actionRecommendation && (
                                      <>
                                        <Divider />
                                        <Box>
                                          <Text fontSize="xs" fontWeight="bold" color="blue.500" mb={1}>
                                            Önerilen Aksiyon:
                                          </Text>
                                          <Text fontSize="xs" color={textColor}>
                                            {insight.actionRecommendation}
                                          </Text>
                                        </Box>
                                      </>
                                    )}
                                  </VStack>
                                  
                                  <Text fontSize="xs" color="gray.500">
                                    {new Date(insight.createdAt).toLocaleString('tr-TR')}
                                  </Text>
                                </VStack>
                              </CardBody>
                            </Card>
                          </MotionBox>
                        );
                      })}
                    </AnimatePresence>
                  </SimpleGrid>
                )}
              </VStack>
            </Collapse>
          </VStack>
        </CardBody>
      </Card>
    </VStack>
  );
};

export default SymbolAlertsAndInsights;
