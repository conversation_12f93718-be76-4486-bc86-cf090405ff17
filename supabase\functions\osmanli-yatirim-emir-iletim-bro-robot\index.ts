import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.8'
import { getCorsHeaders } from '../_shared/cors.ts'
import { submitOrderWithRetry } from '../_shared/http-client.ts'

interface TradePayload {
  trade_id: number;
}

serve(async (req) => {
  const corsHeaders = getCorsHeaders(req.headers);

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { trade_id }: TradePayload = await req.json();
    if (!trade_id) {
      throw new Error('trade_id is required for bro-robot submission.');
    }

    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { data: tradeData, error: tradeError } = await supabaseAdmin
      .from('trades')
      .select('*, user_id')
      .eq('id', trade_id)
      .single();

    if (tradeError || !tradeData) {
      throw new Error(`[Bro-Robot] Trade with ID ${trade_id} not found. Error: ${tradeError?.message}`);
    }

    const userId = tradeData.user_id;

    const { data: userSettings, error: userSettingsError } = await supabaseAdmin
      .from('user_settings')
      .select('custom_webhook_url, encrypted_api_key, encrypted_token')
      .eq('id', userId)
      .single();

    if (userSettingsError || !userSettings || !userSettings.custom_webhook_url) {
      throw new Error(`[Bro-Robot] User settings or custom_webhook_url not found for subscriber ${userId}. Error: ${userSettingsError?.message}`);
    }

    const apiKey = userSettings.encrypted_api_key;
    const token = userSettings.encrypted_token;

    if (!apiKey || !token) {
      throw new Error(`[Bro-Robot] API key or token not found in user_settings for subscriber ${userId}.`);
    }
    
    const orderPayload = {
      name: tradeData.name,
      symbol: tradeData.symbol,
      orderSide: tradeData.order_side,
      orderType: "mktbest",
      price: String(tradeData.price),
      quantity: String(tradeData.calculated_quantity),
      timeInForce: "day",
      apiKey: apiKey,
      timenow: tradeData.created_at,
      token: token
    };

    // OPTIMIZATION: Use optimized HTTP client with connection pooling and retries
    try {
      const response = await submitOrderWithRetry(userSettings.custom_webhook_url, orderPayload, {
        timeout: 12000, // 12 second timeout for bro-robot orders
        retries: 2, // Retry twice for failed orders
        keepAlive: true
      });

      console.log(`[Bro-Robot] Successfully forwarded order for trade ${trade_id} to subscriber ${userId}'s webhook.`);
    } catch (error) {
      // Enhanced error handling for HTTP client errors
      let errorMessage = `[Bro-Robot] Failed to send order to brokerage for subscriber ${userId}`;

      if (error.message.includes('timeout')) {
        errorMessage = `[Bro-Robot] Order submission timeout for subscriber ${userId}`;
      } else if (error.message.includes('429')) {
        errorMessage += `. Rate limit exceeded for user's webhook URL. This is likely due to webhook.site limits, not an error in our system.`;
      } else {
        errorMessage += `. ${error.message}`;
      }

      console.error(errorMessage);
      throw new Error(errorMessage);
    }
    
    console.log(`[Bro-Robot] Successfully forwarded order for trade ${trade_id} to subscriber ${userId}'s webhook.`);

    return new Response(JSON.stringify({ success: true, message: "Bro-robot order forwarded successfully." }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error(error.message);
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
}) 