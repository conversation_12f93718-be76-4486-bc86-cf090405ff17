import { <PERSON>, Flex, <PERSON>, Tbody, Td, <PERSON>, Th, <PERSON><PERSON>, Tr, <PERSON><PERSON>, IconButton, useColorModeValue, Button, Menu, MenuButton, MenuList, MenuItem, Icon, Tooltip } from '@chakra-ui/react';
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  CellContext
} from '@tanstack/react-table';
import * as React from 'react';
import { DeleteIcon, ChevronDownIcon, TriangleDownIcon, TriangleUpIcon } from '@chakra-ui/icons';
import { MdCancel, MdCheckCircle, MdOutlineError } from 'react-icons/md';

import Card from '../card/Card';

interface Trade {
  id: number;
  user_id: string;
  received_at: string;
  signal_name: string;
  symbol: string;
  category: string;
  price: number | null;
  calculated_quantity: number | null;
  forwarded: boolean;
  order_side: string;
  name?: string;
  system_name: string | null;
  signal_type: string | null;
  trade_category: string | null;
  pnl: number | null;
  position_status: string | null;
  position_id: string | null;
  closing_trade_id: string | null;
  is_deleted: boolean;
}

interface TradeTableProps {
  data: Trade[];
  onDeleteClick: (trade: Trade) => void;
}

const formatNumber = (value: number | null, minimumFractionDigits = 2): string => {
  if (value === null || value === undefined) return '-';
  return value.toLocaleString('tr-TR', { minimumFractionDigits });
};

const formatCurrency = (value: number | null): string => {
  if (value === null || value === undefined) return '-';
  
  try {
    return new Intl.NumberFormat('tr-TR', { 
      style: 'currency', 
      currency: 'TRY',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  } catch (err) {
    console.error('Para formatı hatası:', err, 'Değer:', value);
    return value.toFixed(2) + ' ₺'; // Fallback
  }
};

const getCategoryColor = (category: string | null | undefined): string => {
  if (!category) return 'gray';
  
  switch (category.toLowerCase()) {
    case 'alım':
      return 'green';
    case 'satım':
      return 'blue';
    default:
      return 'gray';
  }
};

const renderPnlCell = (trade: Trade) => {
  const isBuyTrade = trade.trade_category === 'Alım' || trade.signal_type === 'BUY';
  
  const hasPnl = trade.pnl !== null && trade.pnl !== undefined && !isNaN(Number(trade.pnl));
  
  if (hasPnl) {
    const pnlValue = Number(trade.pnl);
    const pnlColor = pnlValue > 0 ? "green.500" : pnlValue < 0 ? "red.500" : "gray.500";
    return (
      <Text fontSize="sm" fontWeight="700" color={pnlColor} textAlign="right">
        {formatCurrency(pnlValue)}
      </Text>
    );
  } else if (isBuyTrade) {
    if (trade.position_status === 'Açık') {
      return <Text fontSize="sm" color="blue.500" fontWeight="500" textAlign="right">İşlem Açıldı</Text>;
    } else if (trade.position_status === 'Bakiye Yetersiz') {
      return <Text fontSize="sm" fontWeight="500" color="orange.500" textAlign="right">Bakiye Yetersiz</Text>;
    } else if (trade.position_status === 'Kapandı') {
      return <Text fontSize="sm" color="purple.500" fontWeight="500" textAlign="right">Hesaplanıyor...</Text>;
    }
  }
  
  return <Text fontSize="sm" color="gray.400" textAlign="right">-</Text>;
};

// İşlem durumu için icon komponentini tanımla
const StatusIcon = ({ status }: { status: string | null }) => {
  if (!status) return null;
  
  switch (status) {
    case 'Açık':
      return <Icon as={MdCheckCircle} color="green.500" boxSize={4} />;
    case 'Kapandı':
      return <Icon as={MdCancel} color="blue.500" boxSize={4} />;
    case 'Bakiye Yetersiz':
      return <Icon as={MdOutlineError} color="orange.500" boxSize={4} />;
    default:
      return null;
  }
};

const columnHelper = createColumnHelper<Trade>();

export default function TradeTable({ data, onDeleteClick }: TradeTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([
    { id: 'received_at', desc: true }
  ]);
  
  const textColor = useColorModeValue('navy.700', 'white');
  const textColorSecondary = useColorModeValue('secondaryGray.600', 'secondaryGray.400');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');
  const headerBg = useColorModeValue('secondaryGray.100', 'navy.800');
  const headerTextColor = useColorModeValue('gray.500', 'white');
  const hoverRowBg = useColorModeValue('secondaryGray.100', 'navy.700');
  const stripedRowBg = useColorModeValue('secondaryGray.50', 'navy.900');
  const cardShadow = useColorModeValue('0px 18px 40px rgba(112, 144, 176, 0.12)', 'none');
  const sortIconColor = useColorModeValue('brand.500', 'brand.400');
  
  const columns = [
    columnHelper.accessor('received_at', {
      id: 'received_at',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          ALINMA ZAMANI
        </Text>
      ),
      cell: (info: CellContext<Trade, string>) => (
        <Text color={textColor} fontSize='sm' fontWeight='500'>
          {new Date(info.getValue()).toLocaleString('tr-TR')}
        </Text>
      )
    }),
    columnHelper.accessor('system_name', {
      id: 'system_name',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          SİSTEM ADI
        </Text>
      ),
      cell: (info: CellContext<Trade, string | null>) => (
        <Text color={textColor} fontSize='sm' fontWeight='500'>
          {info.getValue() || '-'}
        </Text>
      )
    }),
    columnHelper.accessor('signal_type', {
      id: 'signal_type',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          SİNYAL TİPİ
        </Text>
      ),
      cell: (info: CellContext<Trade, string | null>) => (
        <Text color={textColor} fontSize='sm' fontWeight='500'>
          {info.getValue() || '-'}
        </Text>
      )
    }),
    columnHelper.accessor('symbol', {
      id: 'symbol',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          SEMBOL
        </Text>
      ),
      cell: (info: CellContext<Trade, string>) => (
        <Text color={textColor} fontSize='sm' fontWeight='700'>
          {info.getValue()}
        </Text>
      )
    }),
    columnHelper.accessor('trade_category', {
      id: 'trade_category',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          KATEGORİ
        </Text>
      ),
      cell: (info: CellContext<Trade, string | null>) => {
        const category = info.getValue() || '';
        return (
          <Badge colorScheme={getCategoryColor(category)} 
            fontSize="xs" 
            fontWeight="500"
            borderRadius="8px"
            py="4px"
            px="10px"
            textTransform="capitalize"
            letterSpacing="0.5px"
          >
            {category || '-'}
          </Badge>
        );
      }
    }),
    columnHelper.accessor('position_status', {
      id: 'position_status',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          DURUM
        </Text>
      ),
      cell: (info: CellContext<Trade, string | null>) => {
        const status = info.getValue();
        return (
          <Flex align="center">
            <StatusIcon status={status} />
            <Text ml={2} color={textColor} fontSize='sm' fontWeight='500'>
              {status || '-'}
            </Text>
          </Flex>
        );
      }
    }),
    columnHelper.accessor('price', {
      id: 'price',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase"
          textAlign="right">
          FİYAT
        </Text>
      ),
      cell: (info: CellContext<Trade, number | null>) => (
        <Text color={textColor} fontSize='sm' fontWeight='500' textAlign="right">
          {formatNumber(info.getValue())}
        </Text>
      )
    }),
    columnHelper.accessor('calculated_quantity', {
      id: 'calculated_quantity',
      header: () => (
        <Text
          justifyContent="flex-end"
          align="right"
          fontSize={{ sm: '10px', lg: '12px' }}
          fontWeight="700"
          color={headerTextColor}
          textTransform="uppercase"
        >
          MİKTAR
        </Text>
      ),
      cell: (info) => (
        <Text color={textColor} fontSize="sm" fontWeight="500" textAlign="right">
          {info.getValue() !== null ? Math.floor(info.getValue() as number) : '-'}
        </Text>
      ),
    }),
    columnHelper.accessor(row => row.price && row.calculated_quantity ? row.price * row.calculated_quantity : null, {
      id: 'total',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase"
          textAlign="right">
          İŞLEM HACMİ
        </Text>
      ),
      cell: (info: CellContext<Trade, number | null>) => (
        <Text color={textColor} fontSize='sm' fontWeight='500' textAlign="right">
          {formatCurrency(info.getValue())}
        </Text>
      )
    }),
    columnHelper.accessor('pnl', {
      id: 'pnl',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase"
          textAlign="right">
          KAR/ZARAR
        </Text>
      ),
      cell: (info: CellContext<Trade, number | null>) => {
        return renderPnlCell(info.row.original);
      }
    }),
    columnHelper.accessor('id', {
      id: 'actions',
      header: () => (
        <Text
          justifyContent='space-between'
          align='center'
          fontSize={{ sm: '10px', lg: '12px' }}
          color={headerTextColor}
          fontWeight="700"
          textTransform="uppercase">
          İŞLEMLER
        </Text>
      ),
      cell: (info: CellContext<Trade, number>) => (
        <Flex justify="center">
          <Tooltip hasArrow label="İşlemi Sil" placement='top'>
            <IconButton
              aria-label="İşlemi sil"
              icon={<DeleteIcon />}
              size="sm"
              colorScheme="red"
              variant="ghost"
              onClick={() => onDeleteClick(info.row.original)}
              borderRadius="10px"
              _hover={{ bg: 'red.50' }}
            />
          </Tooltip>
        </Flex>
      )
    }),
  ];

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    debugTable: false
  });

  return (
    <Card 
      flexDirection='column' 
      w='100%' 
      px='0px' 
      overflowX={{ sm: 'scroll', lg: 'hidden' }}
      boxShadow={cardShadow}
      mb="20px"
      overflow="hidden"
    >
      <Flex 
        px='25px' 
        justify='space-between' 
        align='center' 
        py="16px" 
        borderBottom="1px solid" 
        borderColor={borderColor}
      >
        <Text color={textColor} fontSize='lg' fontWeight='700' lineHeight='100%'>
          İşlem Listesi
        </Text>
        <Menu>
          <MenuButton 
            as={Button} 
            rightIcon={<ChevronDownIcon />} 
            variant='outline' 
            colorScheme='brand' 
            size='sm'
            borderRadius="10px"
            fontWeight="500"
            fontSize="13px"
            h="38px"
            _hover={{ bg: 'brand.50' }}
          >
            İşlemler
          </MenuButton>
          <MenuList minW="160px" shadow="lg" borderRadius="12px">
            <MenuItem fontSize="sm">Tümünü Göster</MenuItem>
            <MenuItem fontSize="sm">Sadece Alım İşlemleri</MenuItem>
            <MenuItem fontSize="sm">Sadece Satım İşlemleri</MenuItem>
            <MenuItem fontSize="sm">Sadece Açık Pozisyonlar</MenuItem>
          </MenuList>
        </Menu>
      </Flex>
      <Box>
        <Table variant='simple' color='gray.500' mb='0px'>
          <Thead>
            {table.getHeaderGroups().map((headerGroup) => (
              <Tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <Th
                      key={header.id}
                      colSpan={header.colSpan}
                      borderColor={borderColor}
                      cursor='pointer'
                      onClick={header.column.getToggleSortingHandler()}
                      py='14px'
                      px="16px"
                      bg={headerBg}
                      borderBottom="1px solid"
                      borderBottomColor={borderColor}
                      _last={{
                        pr: '24px'
                      }}
                      _first={{
                        pl: '24px'
                      }}
                    >
                      <Flex
                        justifyContent={header.id === 'price' || header.id === 'calculated_quantity' || header.id === 'total' || header.id === 'pnl' ? 'flex-end' : 'flex-start'}
                        align='center'
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getCanSort() && (
                          <Flex ml={1} direction="column" h="16px" w="16px" justify="center" align="center">
                            {header.column.getIsSorted() ? (
                              header.column.getIsSorted() === 'asc' ? (
                                <TriangleUpIcon color={sortIconColor} boxSize="10px" />
                              ) : (
                                <TriangleDownIcon color={sortIconColor} boxSize="10px" />
                              )
                            ) : (
                              <>
                                <TriangleUpIcon color="gray.400" boxSize="8px" mb="-3px" opacity={0.5} />
                                <TriangleDownIcon color="gray.400" boxSize="8px" mt="-3px" opacity={0.5} />
                              </>
                            )}
                          </Flex>
                        )}
                      </Flex>
                    </Th>
                  );
                })}
              </Tr>
            ))}
          </Thead>
          <Tbody>
            {data.length === 0 ? (
              <Tr>
                <Td colSpan={columns.length}>
                  <Text textAlign="center" fontSize="sm" py={6} color={textColorSecondary}>
                    Filtreyle eşleşen veya hiç işlem kaydı bulunmamaktadır.
                  </Text>
                </Td>
              </Tr>
            ) : (
              table.getRowModel().rows.map((row, rowIndex) => {
                return (
                  <Tr 
                    key={row.id}
                    _hover={{ bg: hoverRowBg }}
                    transition="all 0.2s ease"
                    bg={rowIndex % 2 === 0 ? 'transparent' : stripedRowBg}
                    cursor="default"
                  >
                    {row.getVisibleCells().map((cell) => {
                      return (
                        <Td
                          key={cell.id}
                          borderColor={borderColor}
                          py="16px"
                          px="16px"
                          fontSize="sm"
                          borderBottom="1px solid"
                          _last={{
                            pr: '24px'
                          }}
                          _first={{
                            pl: '24px'
                          }}
                          textAlign={
                            cell.column.id === 'price' || 
                            cell.column.id === 'calculated_quantity' || 
                            cell.column.id === 'total' || 
                            cell.column.id === 'pnl' 
                              ? 'right' 
                              : cell.column.id === 'actions' 
                                ? 'center' 
                                : 'left'
                          }
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </Td>
                      );
                    })}
                  </Tr>
                );
              })
            )}
          </Tbody>
        </Table>
      </Box>
    </Card>
  );
} 