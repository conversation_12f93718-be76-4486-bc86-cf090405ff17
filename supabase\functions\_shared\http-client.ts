// Shared HTTP client with connection pooling and optimization
// This module provides optimized HTTP functionality for order transmission

export interface HttpClientOptions {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  keepAlive?: boolean;
  maxConnections?: number;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

const DEFAULT_OPTIONS: HttpClientOptions = {
  timeout: 10000, // 10 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
  keepAlive: true,
  maxConnections: 100
};

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2
};

/**
 * Optimized HTTP client for order transmission with connection pooling
 */
export class OptimizedHttpClient {
  private options: HttpClientOptions;
  private retryConfig: RetryConfig;

  constructor(options: Partial<HttpClientOptions> = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
    this.retryConfig = DEFAULT_RETRY_CONFIG;
  }

  /**
   * Calculate delay for exponential backoff
   */
  private calculateDelay(attempt: number): number {
    const delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, attempt);
    return Math.min(delay, this.retryConfig.maxDelay);
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    // Network errors, timeouts, and 5xx status codes are retryable
    if (error.name === 'AbortError') return true;
    if (error.name === 'TypeError' && error.message.includes('fetch')) return true;
    
    // HTTP status codes that are retryable
    const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
    if (error.status && retryableStatusCodes.includes(error.status)) return true;
    
    return false;
  }

  /**
   * Enhanced fetch with connection pooling, retries, and timeout
   */
  async fetch(url: string, options: RequestInit = {}): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.options.timeout);

    const enhancedOptions: RequestInit = {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Connection': this.options.keepAlive ? 'keep-alive' : 'close',
        'Keep-Alive': `timeout=30, max=${this.options.maxConnections}`,
        'User-Agent': 'Algobir-Trading-Bot/1.0',
        ...options.headers
      }
    };

    let lastError: any;

    for (let attempt = 0; attempt <= this.options.retries!; attempt++) {
      try {
        console.log(`[HTTP-CLIENT] Attempt ${attempt + 1}/${this.options.retries! + 1} for ${url}`);
        
        const response = await fetch(url, enhancedOptions);
        clearTimeout(timeoutId);

        // If response is successful, return it
        if (response.ok) {
          console.log(`[HTTP-CLIENT] Success on attempt ${attempt + 1} for ${url}`);
          return response;
        }

        // If response is not ok, create an error with status
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
        (error as any).status = response.status;
        (error as any).response = response;

        // Check if we should retry
        if (attempt < this.options.retries! && this.isRetryableError(error)) {
          const delay = this.calculateDelay(attempt);
          console.log(`[HTTP-CLIENT] Retrying in ${delay}ms due to ${response.status} status`);
          await this.sleep(delay);
          continue;
        }

        throw error;

      } catch (error) {
        clearTimeout(timeoutId);
        lastError = error;

        // If this is the last attempt or error is not retryable, throw
        if (attempt >= this.options.retries! || !this.isRetryableError(error)) {
          console.error(`[HTTP-CLIENT] Final failure for ${url}:`, error);
          throw error;
        }

        // Calculate delay and retry
        const delay = this.calculateDelay(attempt);
        console.log(`[HTTP-CLIENT] Retrying in ${delay}ms due to error:`, error.message);
        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * POST request with optimized settings
   */
  async post(url: string, data: any, options: RequestInit = {}): Promise<Response> {
    return this.fetch(url, {
      method: 'POST',
      body: JSON.stringify(data),
      ...options
    });
  }

  /**
   * GET request with optimized settings
   */
  async get(url: string, options: RequestInit = {}): Promise<Response> {
    return this.fetch(url, {
      method: 'GET',
      ...options
    });
  }
}

// Singleton instance for reuse across functions
let httpClientInstance: OptimizedHttpClient | null = null;

/**
 * Get shared HTTP client instance
 */
export function getHttpClient(options?: Partial<HttpClientOptions>): OptimizedHttpClient {
  if (!httpClientInstance || options) {
    httpClientInstance = new OptimizedHttpClient(options);
  }
  return httpClientInstance;
}

/**
 * Utility function for order submission with optimized HTTP
 */
export async function submitOrderWithRetry(
  webhookUrl: string, 
  orderPayload: any, 
  options?: Partial<HttpClientOptions>
): Promise<Response> {
  const client = getHttpClient(options);
  
  console.log(`[HTTP-CLIENT] Submitting order to ${webhookUrl}`);
  const startTime = performance.now();
  
  try {
    const response = await client.post(webhookUrl, orderPayload);
    const duration = performance.now() - startTime;
    console.log(`[HTTP-CLIENT] Order submitted successfully in ${duration.toFixed(2)}ms`);
    return response;
  } catch (error) {
    const duration = performance.now() - startTime;
    console.error(`[HTTP-CLIENT] Order submission failed after ${duration.toFixed(2)}ms:`, error);
    throw error;
  }
}
