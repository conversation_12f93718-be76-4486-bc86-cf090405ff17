import { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  Switch,
  FormErrorMessage,
  VStack,
  useToast,
  FormHelperText,
  Box,
  Heading,
  UnorderedList,
  ListItem,
  Text
} from '@chakra-ui/react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../supabaseClient';
import { Robot } from '../../types/robot';
import React from 'react';

interface RobotFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  robot?: Robot;
  mode?: 'edit' | 'create';
}

interface FormInputs {
  name: string;
  description: string;
  strategy_type: string;
  is_public: boolean;
  image_url: string;
  version: string;
  price: number | null;
  subscription_period: number | null;
}

const RobotForm = ({ isO<PERSON>, onClose, onSuccess, robot, mode = 'create' }: RobotFormProps) => {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const toast = useToast();
  
  const {
    handleSubmit,
    register,
    reset,
    setValue,
    formState: { errors }
  } = useForm<FormInputs>({
    defaultValues: robot
      ? {
          name: robot.name || '',
          description: robot.description || '',
          strategy_type: robot.strategy_type || '',
          is_public: robot.is_public,
          image_url: robot.image_url || '',
          version: robot.version || '1.0',
          price: robot.price || null,
          subscription_period: robot.subscription_period || null,
        }
      : {
          name: '',
          description: '',
          strategy_type: '',
          is_public: true,
          image_url: '',
          version: '1.0',
          price: null,
          subscription_period: null,
        },
  });

  React.useEffect(() => {
    if (robot) {
      setValue('name', robot.name || '');
      setValue('description', robot.description || '');
      setValue('strategy_type', robot.strategy_type || '');
      setValue('is_public', robot.is_public);
      setValue('image_url', robot.image_url || '');
      setValue('version', robot.version || '1.0');
      setValue('price', robot.price || null);
      setValue('subscription_period', robot.subscription_period || null);
    }
  }, [robot, setValue]);

  const onSubmit: SubmitHandler<FormInputs> = async (formData: FormInputs) => {
    if (!user) return;
    setIsSubmitting(true);
    try {
      // Veri tiplerini düzelt
      const dataToSubmit = {
        name: formData.name.trim(),
        description: formData.description ? formData.description.trim() : null,
        strategy_type: formData.strategy_type || null,
        is_public: Boolean(formData.is_public),
        image_url: formData.image_url ? formData.image_url.trim() : null,
        version: formData.version ? formData.version.trim() : '1.0',
        price: formData.price !== null ? parseFloat(formData.price.toString()) || 0.00 : null,
        subscription_period: formData.subscription_period !== null ? Number(formData.subscription_period) : null,
      };

      // Güncelleme işlemi
      if (mode === 'edit' && robot) {
        console.log('Robot güncelleme için update çağrısı yapılıyor...');
        console.log('robot.id:', robot.id);
        console.log('user.id:', user.id);
        console.log('Gönderilecek veri (payload):', JSON.stringify(dataToSubmit, null, 2));
        
        // Supabase'e gönderilen isteği detaylı logla
        const { data, error } = await supabase
          .from('robots')
          .update(dataToSubmit)
          .eq('id', robot.id)
          .eq('seller_id', user.id)
          .select();
        
        console.log('Update yanıtı:', { data, error });
        
        if (error) {
          console.error('Robot güncelleme Supabase DETAYLI hatası:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code,
            fullError: JSON.stringify(error, null, 2)
          });
          throw error;
        }
        
        toast({
          title: 'Başarılı',
          description: 'Robot başarıyla güncellendi.',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      } 
      // Yeni robot oluşturma işlemi
      else {
        // seller_id ekle
        const insertData = {
          ...dataToSubmit,
          seller_id: user.id,
        };
        
        console.log('Robot oluşturma için insert çağrısı yapılıyor...');
        console.log('Gönderilecek veri (payload):', JSON.stringify(insertData, null, 2));
        
        const { data, error } = await supabase
          .from('robots')
          .insert([insertData])
          .select();
        
        console.log('Insert yanıtı:', { data, error });
        
        if (error) {
          console.error('Robot oluşturma Supabase DETAYLI hatası:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code,
            fullError: JSON.stringify(error, null, 2)
          });
          throw error;
        }
        
        toast({
          title: 'Başarılı',
          description: 'Robot başarıyla oluşturuldu.',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      }
      
      reset();
      onSuccess();
    } catch (error: any) {
      console.error('Robot kaydetme/güncelleme hatası:', error);
      
      // Daha detaylı hata mesajı göster
      const errorMessage = error?.message 
        ? `Robot kaydedilirken hata: ${error.message}${error.details ? ` (${error.details})` : ''}` 
        : 'Robot kaydedilirken bir hata oluştu.';
      
      toast({
        title: 'Hata',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{mode === 'edit' ? 'Robotu Düzenle' : 'Yeni Robot Ekle'}</ModalHeader>
        <ModalCloseButton />
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalBody>
            <VStack spacing={4}>
              {/* Robot Adı */}
              <FormControl isInvalid={!!errors.name} isRequired>
                <FormLabel>Robot Adı</FormLabel>
                <Input
                  {...register('name', {
                    required: 'Robot adı gereklidir',
                    minLength: { value: 3, message: 'En az 3 karakter gereklidir' },
                  })}
                  placeholder="Robotunuzun adı"
                />
                <FormErrorMessage>
                  {errors.name && errors.name.message}
                </FormErrorMessage>
              </FormControl>
              {/* Açıklama */}
              <FormControl isInvalid={!!errors.description}>
                <FormLabel>Robot Açıklaması</FormLabel>
                <Textarea
                  {...register('description')}
                  placeholder="Robotunuzu detaylı bir şekilde açıklayın"
                  resize="vertical"
                  rows={3}
                />
                <FormErrorMessage>
                  {errors.description && errors.description.message}
                </FormErrorMessage>
                <FormHelperText>
                  Robotunuzun nasıl çalıştığını ve ne tür sinyaller ürettiğini açıklayın. Bu bilgi kullanıcıların robotunuzu 
                  anlamasına yardımcı olacaktır.
                </FormHelperText>
              </FormControl>
              {/* Versiyon */}
              <FormControl isInvalid={!!errors.version}>
                <FormLabel>Versiyon</FormLabel>
                <Input
                  {...register('version')}
                  placeholder="1.0"
                  defaultValue="1.0"
                />
                <FormErrorMessage>
                  {errors.version && errors.version.message}
                </FormErrorMessage>
              </FormControl>
              {/* Strateji Tipi */}
              <FormControl isInvalid={!!errors.strategy_type}>
                <FormLabel>Strateji Tipi</FormLabel>
                <Select
                  {...register('strategy_type')}
                  placeholder="Strateji tipini seçin"
                >
                  <option value="trend_following">Trend Takibi</option>
                  <option value="mean_reversion">Ortalamaya Dönüş</option>
                  <option value="breakout">Kırılım</option>
                  <option value="momentum">Momentum</option>
                  <option value="swing_trading">Salınım Ticareti</option>
                  <option value="scalping">Scalping</option>
                  <option value="other">Diğer</option>
                </Select>
                <FormErrorMessage>
                  {errors.strategy_type && errors.strategy_type.message}
                </FormErrorMessage>
              </FormControl>
              {/* Herkese Açık */}
              <FormControl display="flex" alignItems="center">
                <FormLabel htmlFor="is_public" mb="0">
                  Herkese Açık
                </FormLabel>
                <Switch
                  id="is_public"
                  {...register('is_public')}
                  defaultChecked={robot ? robot.is_public : true}
                  colorScheme="brand"
                />
              </FormControl>
              {/* Resim URL */}
              <FormControl isInvalid={!!errors.image_url}>
                <FormLabel>Resim URL'si</FormLabel>
                <Input
                  {...register('image_url', {
                    pattern: {
                      value: /^(https?:\/\/.*)?$/,
                      message: 'Geçerli bir URL girin',
                    },
                  })}
                  placeholder="Robot görselinin URL'si (opsiyonel)"
                />
                <FormErrorMessage>
                  {errors.image_url && errors.image_url.message}
                </FormErrorMessage>
              </FormControl>
              {/* Fiyat */}
              <FormControl isInvalid={!!errors.price}>
                <FormLabel>Fiyat (TL/Ay)</FormLabel>
                <Input
                  {...register('price', {
                    valueAsNumber: true,
                    validate: (value) => {
                      if (value === null || value === undefined) return true;
                      if (isNaN(value)) return 'Geçerli bir sayı girin';
                      if (value < 0) return 'Fiyat negatif olamaz';
                      return true;
                    }
                  })}
                  type="number"
                  placeholder="Robotunuzun aylık abonelik fiyatı (opsiyonel)"
                  min="0"
                  step="1"
                />
                <FormErrorMessage>
                  {errors.price && errors.price.message}
                </FormErrorMessage>
                <FormHelperText>
                  Robotunuzun aylık abonelik fiyatını TL cinsinden girin. Ücretsiz bir robot için 0 (Sıfır) rakamını yazmalısınız. Aksi halde sistem onay vermeyecektir.
                </FormHelperText>
              </FormControl>
              {/* Abonelik Süresi */}
              <FormControl isInvalid={!!errors.subscription_period}>
                <FormLabel>Abonelik Süresi (Gün)</FormLabel>
                <Input
                  {...register('subscription_period', {
                    valueAsNumber: true,
                    validate: (value) => {
                      if (value === null || value === undefined) return true;
                      if (isNaN(value)) return 'Geçerli bir sayı girin';
                      if (value < 1) return 'Abonelik süresi en az 1 gün olmalıdır';
                      return true;
                    }
                  })}
                  type="number"
                  placeholder="Varsayılan abonelik süresi (gün)"
                  min="1"
                  step="1"
                />
                <FormErrorMessage>
                  {errors.subscription_period && errors.subscription_period.message}
                </FormErrorMessage>
                <FormHelperText>
                  Robotunuza yapılacak aboneliklerin varsayılan süresini gün cinsinden girin. Örneğin: 30 (1 ay).
                </FormHelperText>
              </FormControl>
              {/* Sinyal Gönderimi Hakkında Bilgi */}
              <Box mb={6} p={4} borderWidth="1px" borderRadius="md" bg="blue.50">
                <Heading size="sm" mb={2} color="blue.600">
                  Sinyal Gönderimi Hakkında
                </Heading>
                <Text fontSize="sm" mb={2}>
                  Robotunuz kaydedildikten sonra, sinyalleri göndermek için size özel bir webhook URL'si oluşturulacaktır.
                </Text>
                <UnorderedList fontSize="sm" spacing={1} pl={4}>
                  <ListItem>Webhook URL'ye Bearer token (giriş token'ınız) ile HTTP POST isteği göndermeniz gerekecek</ListItem>
                  <ListItem>Sinyalleriniz, abonelerinize otomatik olarak iletilecektir</ListItem>
                  <ListItem>İstek gövdesi JSON formatında olmalı ve gerekli alanları içermelidir (symbol, orderSide, vb.)</ListItem>
                </UnorderedList>
                <Text fontSize="sm" mt={2} fontWeight="medium">
                  Robotunuzu kaydettikten sonra, webhook kurulumu ile ilgili tüm bilgileri görebileceksiniz.
                </Text>
              </Box>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={handleClose}>
              İptal
            </Button>
            <Button 
              colorScheme="brand" 
              type="submit"
              isLoading={isSubmitting}
            >
              Kaydet
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

export default RobotForm; 