import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  Text,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  useToast,
  Icon,
  useColorModeValue,
  Avatar,
  Box,
  Flex,
  Badge,
  InputGroup,
  InputLeftElement,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription
} from '@chakra-ui/react';
import { 
  FiSend,
  FiSearch,
  FiMail
} from 'react-icons/fi';
import { useForm } from 'react-hook-form';
import { supabase } from '../../supabaseClient';

interface User {
  id: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  email?: string;
}

interface IndividualNotificationData {
  user_id: string;
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'error' | 'success';
  expires_at?: string;
  action_url?: string;
  action_label?: string;
}

interface IndividualNotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: IndividualNotificationData, userName: string) => void;
}

const IndividualNotificationModal: React.FC<IndividualNotificationModalProps> = ({
  isOpen,
  onClose,
  onSubmit
}) => {
  const toast = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [usersLoading, setUsersLoading] = useState(false);

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<Omit<IndividualNotificationData, 'user_id'>>({
    defaultValues: {
      severity: 'info'
    }
  });

  // Kullanıcıları yükle
  const fetchUsers = async () => {
    try {
      setUsersLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          username,
          full_name,
          avatar_url,
          user_settings!inner(is_active, is_superuser)
        `)
        .eq('user_settings.is_active', true)
        .eq('user_settings.is_superuser', false) // Admin olmayan kullanıcılar
        .order('username');

      if (error) {
        console.error('Kullanıcılar yüklenirken hata:', error);
        toast({
          title: 'Hata',
          description: 'Kullanıcılar yüklenirken bir hata oluştu',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      const userList = data?.map(user => ({
        id: user.id,
        username: user.username,
        full_name: user.full_name,
        avatar_url: user.avatar_url
      })) || [];

      setUsers(userList);
      setFilteredUsers(userList);
    } catch (error) {
      console.error('Kullanıcılar yüklenirken hata:', error);
    } finally {
      setUsersLoading(false);
    }
  };

  // Modal açıldığında kullanıcıları yükle
  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    } else {
      // Modal kapandığında state'i temizle
      setSelectedUser(null);
      setSearchTerm('');
      reset();
    }
  }, [isOpen, reset]);

  // Arama filtresi
  useEffect(() => {
    if (!searchTerm) {
      setFilteredUsers(users);
      return;
    }

    const filtered = users.filter(user => 
      user.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.full_name?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredUsers(filtered);
  }, [searchTerm, users]);

  // Form gönderimi
  const handleFormSubmit = async (data: Omit<IndividualNotificationData, 'user_id'>) => {
    if (!selectedUser) {
      toast({
        title: 'Hata',
        description: 'Lütfen bir kullanıcı seçin',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      setLoading(true);
      
      const formData: IndividualNotificationData = {
        ...data,
        user_id: selectedUser.id
      };

      await onSubmit(formData, selectedUser.username || selectedUser.full_name || 'Kullanıcı');
      
      // Form'u temizle ve modal'ı kapat
      reset();
      setSelectedUser(null);
      setSearchTerm('');
      onClose();
    } catch (error) {
      console.error('Form gönderim hatası:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay backdropFilter="blur(10px)" />
      <ModalContent maxH="90vh" overflowY="auto">
        <ModalHeader>
          <HStack>
            <Icon as={FiMail} color="blue.500" />
            <Text>Bireysel Bildirim Gönder</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <form onSubmit={handleSubmit(handleFormSubmit)}>
            <VStack spacing={6} align="stretch">
              {/* Kullanıcı Seçimi */}
              <Box>
                <FormLabel>Kullanıcı Seçin</FormLabel>
                
                {/* Arama Kutusu */}
                <InputGroup mb={4}>
                  <InputLeftElement>
                    <Icon as={FiSearch} color="gray.400" />
                  </InputLeftElement>
                  <Input
                    placeholder="Kullanıcı adı veya tam ad ile ara..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </InputGroup>

                {/* Seçili Kullanıcı */}
                {selectedUser && (
                  <Alert status="success" mb={4} borderRadius="md">
                    <AlertIcon />
                    <Box>
                      <AlertTitle fontSize="sm">Seçili Kullanıcı:</AlertTitle>
                      <AlertDescription>
                        <HStack>
                          <Avatar size="sm" src={selectedUser.avatar_url} name={selectedUser.full_name} />
                          <Text fontWeight="medium">
                            {selectedUser.full_name} (@{selectedUser.username})
                          </Text>
                        </HStack>
                      </AlertDescription>
                    </Box>
                  </Alert>
                )}

                {/* Kullanıcı Listesi */}
                <Box 
                  maxH="200px" 
                  overflowY="auto" 
                  border="1px solid" 
                  borderColor={borderColor}
                  borderRadius="md"
                  bg={cardBg}
                >
                  {usersLoading ? (
                    <Flex justify="center" p={4}>
                      <Spinner size="sm" />
                      <Text ml={2}>Kullanıcılar yükleniyor...</Text>
                    </Flex>
                  ) : filteredUsers.length === 0 ? (
                    <Box p={4} textAlign="center" color="gray.500">
                      {searchTerm ? 'Arama kriterinize uygun kullanıcı bulunamadı' : 'Kullanıcı bulunamadı'}
                    </Box>
                  ) : (
                    filteredUsers.map((user) => (
                      <Flex
                        key={user.id}
                        p={3}
                        align="center"
                        cursor="pointer"
                        bg={selectedUser?.id === user.id ? 'blue.50' : 'transparent'}
                        _hover={{ bg: selectedUser?.id === user.id ? 'blue.100' : 'gray.50' }}
                        onClick={() => setSelectedUser(user)}
                        borderBottom="1px solid"
                        borderColor={borderColor}
                        _last={{ borderBottom: 'none' }}
                      >
                        <Avatar size="sm" src={user.avatar_url} name={user.full_name} mr={3} />
                        <Box flex={1}>
                          <Text fontWeight="medium" fontSize="sm">
                            {user.full_name}
                          </Text>
                          <Text fontSize="xs" color="gray.500">
                            @{user.username}
                          </Text>
                        </Box>
                        {selectedUser?.id === user.id && (
                          <Badge colorScheme="blue" size="sm">
                            Seçili
                          </Badge>
                        )}
                      </Flex>
                    ))
                  )}
                </Box>
              </Box>

              {/* Bildirim Formu */}
              <VStack spacing={4} align="stretch">
                {/* Başlık */}
                <FormControl isRequired isInvalid={!!errors.title}>
                  <FormLabel>Başlık</FormLabel>
                  <Input
                    {...register('title', { 
                      required: 'Başlık gereklidir',
                      minLength: { value: 3, message: 'En az 3 karakter olmalıdır' },
                      maxLength: { value: 100, message: 'En fazla 100 karakter olabilir' }
                    })}
                    placeholder="Bildirim başlığı..."
                  />
                  {errors.title && (
                    <Text color="red.500" fontSize="sm">{errors.title.message}</Text>
                  )}
                </FormControl>

                {/* Mesaj */}
                <FormControl isRequired isInvalid={!!errors.message}>
                  <FormLabel>Mesaj</FormLabel>
                  <Textarea
                    {...register('message', { 
                      required: 'Mesaj gereklidir',
                      minLength: { value: 10, message: 'En az 10 karakter olmalıdır' },
                      maxLength: { value: 500, message: 'En fazla 500 karakter olabilir' }
                    })}
                    placeholder="Bildirim mesajınızı buraya yazın..."
                    rows={4}
                  />
                  {errors.message && (
                    <Text color="red.500" fontSize="sm">{errors.message.message}</Text>
                  )}
                </FormControl>

                {/* Önem Derecesi */}
                <FormControl>
                  <FormLabel>Önem Derecesi</FormLabel>
                  <Select {...register('severity')}>
                    <option value="info">Bilgi</option>
                    <option value="success">Başarı</option>
                    <option value="warning">Uyarı</option>
                    <option value="error">Hata</option>
                  </Select>
                </FormControl>

                {/* Son Geçerlilik */}
                <FormControl>
                  <FormLabel>Son Geçerlilik (İsteğe Bağlı)</FormLabel>
                  <Input
                    type="datetime-local"
                    {...register('expires_at')}
                  />
                </FormControl>

                {/* Eylem Butonu */}
                <VStack spacing={2} align="stretch">
                  <FormControl>
                    <FormLabel>Eylem Butonu Etiketi (İsteğe Bağlı)</FormLabel>
                    <Input
                      {...register('action_label')}
                      placeholder="Butonda görünecek metin..."
                    />
                  </FormControl>

                  <FormControl>
                    <FormLabel>Eylem URL'i (İsteğe Bağlı)</FormLabel>
                    <Input
                      type="url"
                      {...register('action_url')}
                      placeholder="https://..."
                    />
                  </FormControl>
                </VStack>
              </VStack>
            </VStack>
          </form>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button 
              variant="ghost" 
              onClick={onClose}
              disabled={loading}
            >
              İptal Et
            </Button>
            <Button 
              colorScheme="blue" 
              leftIcon={<Icon as={FiSend} />}
              onClick={handleSubmit(handleFormSubmit)}
              isLoading={loading}
              loadingText="Gönderiliyor..."
              disabled={!selectedUser}
              bgGradient="linear(to-r, blue.400, blue.600)"
              _hover={{
                bgGradient: "linear(to-r, blue.500, blue.700)",
              }}
            >
              Bildirimi Gönder
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default IndividualNotificationModal; 