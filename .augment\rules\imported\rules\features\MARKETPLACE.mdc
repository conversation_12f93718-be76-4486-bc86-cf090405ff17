---
type: "manual"
priority: "high"
scope: ["marketplace", "robots", "subscriptions", "discovery", "monetization"]
last_updated: "2025-01-29"
---

# Marketplace Features - Robot Discovery & Subscription

## 🏪 Marketplace Architecture

### Core Marketplace Concept
```typescript
// Robot marketplace for Bro-Robot discovery and subscription
// Users can browse, evaluate, and subscribe to profitable trading robots

interface MarketplaceFlow {
  discovery: 'browse_robots_by_performance';
  evaluation: 'view_statistics_and_reviews';
  subscription: 'subscribe_with_payment';
  management: 'track_subscriptions_and_performance';
}

// Marketplace Components
src/components/marketplace/
├── RobotCard.tsx              # Individual robot display card
├── RobotDetails.tsx           # Detailed robot information page
├── RobotFilters.tsx           # Search and filter controls
├── RobotList.tsx              # Grid/list view of robots
├── SubscriptionModal.tsx      # Subscription purchase flow
├── PerformanceChart.tsx       # Robot performance visualization
└── ReviewSystem.tsx           # User reviews and ratings
```

### Robot Discovery System
```typescript
// Robot Listing with Performance Metrics
interface MarketplaceRobot {
  id: string;
  name: string;
  description: string;
  seller_id: string;
  seller_name: string;
  performance_data: {
    total_return: number;
    win_rate: number;
    total_trades: number;
    avg_monthly_return: number;
    max_drawdown: number;
    sharpe_ratio: number;
    last_30_days_return: number;
  };
  subscription_price: number;
  subscription_period: 'monthly' | 'quarterly' | 'yearly';
  subscriber_count: number;
  rating: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Robot Discovery Query
const discoverRobots = async (filters: MarketplaceFilters) => {
  const { data, error } = await supabase
    .from('robots')
    .select(`
      *,
      profiles:seller_id (username, full_name),
      subscriptions (count),
      reviews (rating)
    `)
    .eq('is_active', true)
    .gte('performance_data->total_return', filters.minReturn || 0)
    .gte('performance_data->win_rate', filters.minWinRate || 0)
    .order(filters.sortBy || 'performance_data->total_return', { ascending: false })
    .range(filters.offset || 0, (filters.offset || 0) + (filters.limit || 20));
  
  return data;
};
```

## 🔍 Search & Filtering System

### Advanced Filtering Options
```typescript
interface MarketplaceFilters {
  // Performance Filters
  minReturn?: number;           // Minimum total return %
  maxReturn?: number;           // Maximum total return %
  minWinRate?: number;          // Minimum win rate %
  minTrades?: number;           // Minimum number of trades
  maxDrawdown?: number;         // Maximum drawdown %
  
  // Subscription Filters
  maxPrice?: number;            // Maximum subscription price
  subscriptionPeriod?: 'monthly' | 'quarterly' | 'yearly';
  
  // Popularity Filters
  minSubscribers?: number;      // Minimum subscriber count
  minRating?: number;           // Minimum rating
  
  // Time Filters
  performancePeriod?: '30d' | '90d' | '1y' | 'all';
  
  // Search
  searchQuery?: string;         // Search in name/description
  
  // Sorting
  sortBy?: 'total_return' | 'win_rate' | 'subscriber_count' | 'rating' | 'created_at';
  sortOrder?: 'asc' | 'desc';
  
  // Pagination
  offset?: number;
  limit?: number;
}

// Filter Implementation
const applyMarketplaceFilters = (query: any, filters: MarketplaceFilters) => {
  let filteredQuery = query;
  
  // Performance filters
  if (filters.minReturn) {
    filteredQuery = filteredQuery.gte('performance_data->total_return', filters.minReturn);
  }
  
  if (filters.minWinRate) {
    filteredQuery = filteredQuery.gte('performance_data->win_rate', filters.minWinRate);
  }
  
  // Search functionality
  if (filters.searchQuery) {
    filteredQuery = filteredQuery.or(`name.ilike.%${filters.searchQuery}%,description.ilike.%${filters.searchQuery}%`);
  }
  
  // Sorting
  if (filters.sortBy) {
    const column = filters.sortBy.includes('->') ? filters.sortBy : filters.sortBy;
    filteredQuery = filteredQuery.order(column, { ascending: filters.sortOrder === 'asc' });
  }
  
  return filteredQuery;
};
```

### Search Implementation
```typescript
// Real-time Search with Debouncing
const useMarketplaceSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [results, setResults] = useState<MarketplaceRobot[]>([]);
  const [loading, setLoading] = useState(false);
  
  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchQuery]);
  
  // Perform search when debounced query changes
  useEffect(() => {
    if (debouncedQuery) {
      performSearch(debouncedQuery);
    } else {
      setResults([]);
    }
  }, [debouncedQuery]);
  
  const performSearch = async (query: string) => {
    setLoading(true);
    try {
      const robots = await discoverRobots({ searchQuery: query });
      setResults(robots || []);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return { searchQuery, setSearchQuery, results, loading };
};
```

## 💳 Subscription Management

### Subscription Flow
```typescript
// Subscription Purchase Process
interface SubscriptionFlow {
  step1: 'robot_selection';
  step2: 'subscription_plan_choice';
  step3: 'payment_processing';
  step4: 'subscription_activation';
  step5: 'confirmation_and_access';
}

// Subscription Plans
interface SubscriptionPlan {
  id: string;
  robot_id: string;
  period: 'monthly' | 'quarterly' | 'yearly';
  price: number;
  discount_percentage?: number;
  features: string[];
  trial_period_days?: number;
}

// Create Subscription
const createSubscription = async (robotId: string, planId: string, userId: string) => {
  try {
    // 1. Validate robot and plan
    const robot = await validateRobotAvailability(robotId);
    const plan = await getSubscriptionPlan(planId);
    
    // 2. Check existing subscription
    const existingSubscription = await checkExistingSubscription(userId, robotId);
    if (existingSubscription?.is_active) {
      throw new Error('Bu robota zaten abonesiniz');
    }
    
    // 3. Process payment (integration with payment provider)
    const paymentResult = await processPayment({
      amount: plan.price,
      currency: 'TRY',
      user_id: userId,
      description: `${robot.name} - ${plan.period} abonelik`
    });
    
    // 4. Create subscription record
    const subscription = await supabase
      .from('subscriptions')
      .insert([{
        user_id: userId,
        robot_id: robotId,
        plan_id: planId,
        started_at: new Date().toISOString(),
        expires_at: calculateExpiryDate(plan.period),
        is_active: true,
        payment_reference: paymentResult.transaction_id
      }])
      .select()
      .single();
    
    // 5. Send confirmation notification
    await createNotification(userId, {
      title: 'Abonelik Başarılı',
      message: `${robot.name} robotuna başarıyla abone oldunuz.`,
      type: 'subscription_created',
      action_url: '/management',
      action_label: 'Aboneliklerimi Görüntüle'
    });
    
    return subscription.data;
    
  } catch (error) {
    console.error('Subscription creation error:', error);
    throw error;
  }
};
```

### Subscription Management
```typescript
// User Subscription Dashboard
const useUserSubscriptions = (userId: string) => {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  
  const fetchSubscriptions = useCallback(async () => {
    const { data, error } = await supabase
      .from('subscriptions')
      .select(`
        *,
        robots (
          id, name, description, performance_data,
          profiles:seller_id (username, full_name)
        )
      `)
      .eq('user_id', userId)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching subscriptions:', error);
    } else {
      setSubscriptions(data || []);
    }
    setLoading(false);
  }, [userId]);
  
  const cancelSubscription = async (subscriptionId: string) => {
    const { error } = await supabase
      .from('subscriptions')
      .update({ 
        is_active: false, 
        cancelled_at: new Date().toISOString() 
      })
      .eq('id', subscriptionId)
      .eq('user_id', userId);
    
    if (!error) {
      await fetchSubscriptions(); // Refresh list
    }
    
    return !error;
  };
  
  const renewSubscription = async (subscriptionId: string, planId: string) => {
    // Implementation for subscription renewal
    // Similar to createSubscription but for existing subscription
  };
  
  useEffect(() => {
    fetchSubscriptions();
  }, [fetchSubscriptions]);
  
  return { subscriptions, loading, cancelSubscription, renewSubscription, refetch: fetchSubscriptions };
};
```

## 📊 Performance Visualization

### Robot Performance Charts
```typescript
// Performance Chart Component
const RobotPerformanceChart = ({ robotId, period = '90d' }: { robotId: string; period: string }) => {
  const [performanceData, setPerformanceData] = useState<PerformanceDataPoint[]>([]);
  
  useEffect(() => {
    const fetchPerformanceData = async () => {
      const { data } = await supabase.rpc('get_robot_performance_history', {
        p_robot_id: robotId,
        p_period: period
      });
      
      setPerformanceData(data || []);
    };
    
    fetchPerformanceData();
  }, [robotId, period]);
  
  return (
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={performanceData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis 
          dataKey="date" 
          tickFormatter={(date) => format(new Date(date), 'dd/MM')}
        />
        <YAxis 
          tickFormatter={(value) => `${value}%`}
        />
        <Tooltip 
          labelFormatter={(date) => format(new Date(date), 'dd MMMM yyyy')}
          formatter={(value: number) => [`${value.toFixed(2)}%`, 'Getiri']}
        />
        <Line 
          type="monotone" 
          dataKey="cumulative_return" 
          stroke="#38B2AC" 
          strokeWidth={2}
          dot={false}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

// Performance Metrics Display
const PerformanceMetrics = ({ robot }: { robot: MarketplaceRobot }) => {
  const metrics = robot.performance_data;
  
  return (
    <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
      <Stat>
        <StatLabel>Toplam Getiri</StatLabel>
        <StatNumber color={metrics.total_return >= 0 ? 'green.500' : 'red.500'}>
          {metrics.total_return.toFixed(2)}%
        </StatNumber>
      </Stat>
      
      <Stat>
        <StatLabel>Kazanma Oranı</StatLabel>
        <StatNumber>{metrics.win_rate.toFixed(1)}%</StatNumber>
      </Stat>
      
      <Stat>
        <StatLabel>Toplam İşlem</StatLabel>
        <StatNumber>{metrics.total_trades}</StatNumber>
      </Stat>
      
      <Stat>
        <StatLabel>Maksimum Düşüş</StatLabel>
        <StatNumber color="red.500">
          {metrics.max_drawdown.toFixed(2)}%
        </StatNumber>
      </Stat>
    </SimpleGrid>
  );
};
```

## ⭐ Review & Rating System

### User Reviews
```typescript
// Review System Implementation
interface RobotReview {
  id: string;
  robot_id: string;
  user_id: string;
  rating: number; // 1-5 stars
  title: string;
  comment: string;
  subscription_duration_days: number;
  verified_subscriber: boolean;
  created_at: string;
  updated_at: string;
}

// Create Review
const createReview = async (review: Omit<RobotReview, 'id' | 'created_at' | 'updated_at'>) => {
  // Verify user has/had subscription
  const subscription = await verifyUserSubscription(review.user_id, review.robot_id);
  
  const { data, error } = await supabase
    .from('robot_reviews')
    .insert([{
      ...review,
      verified_subscriber: !!subscription,
      subscription_duration_days: subscription?.duration_days || 0
    }])
    .select()
    .single();
  
  if (!error) {
    // Update robot's average rating
    await updateRobotRating(review.robot_id);
  }
  
  return data;
};

// Review Display Component
const ReviewList = ({ robotId }: { robotId: string }) => {
  const [reviews, setReviews] = useState<RobotReview[]>([]);
  
  useEffect(() => {
    const fetchReviews = async () => {
      const { data } = await supabase
        .from('robot_reviews')
        .select(`
          *,
          profiles:user_id (username, avatar_url)
        `)
        .eq('robot_id', robotId)
        .order('created_at', { ascending: false });
      
      setReviews(data || []);
    };
    
    fetchReviews();
  }, [robotId]);
  
  return (
    <VStack spacing={4} align="stretch">
      {reviews.map((review) => (
        <Box key={review.id} p={4} borderWidth={1} borderRadius="md">
          <HStack justify="space-between" mb={2}>
            <HStack>
              <Avatar size="sm" src={review.profiles?.avatar_url} />
              <VStack align="start" spacing={0}>
                <Text fontWeight="bold">{review.profiles?.username}</Text>
                <HStack>
                  <StarRating rating={review.rating} />
                  {review.verified_subscriber && (
                    <Badge colorScheme="green" size="sm">Doğrulanmış</Badge>
                  )}
                </HStack>
              </VStack>
            </HStack>
            <Text fontSize="sm" color="gray.500">
              {formatDistanceToNow(new Date(review.created_at), { locale: tr })}
            </Text>
          </HStack>
          
          <Text fontWeight="semibold" mb={1}>{review.title}</Text>
          <Text color="gray.600">{review.comment}</Text>
          
          {review.subscription_duration_days > 0 && (
            <Text fontSize="sm" color="gray.500" mt={2}>
              {review.subscription_duration_days} gün abonelik deneyimi
            </Text>
          )}
        </Box>
      ))}
    </VStack>
  );
};
```

## Update Requirements

### When to Update This File
- New marketplace features or filters added
- Subscription system changes
- Performance visualization updates
- Review and rating system modifications
- Search functionality improvements
- Payment integration changes

### Related Files to Update
- Update `features/TRADING_SYSTEM.mdc` for robot-related changes
- Update `frontend/MAIN_APP.mdc` for UI component updates
- Update `backend/SUPABASE_EDGE.mdc` for database schema changes
- Update `security/GUIDELINES.mdc` for payment security
- Update `performance/OPTIMIZATION.mdc` for search optimization
