// Memory optimization utilities for high-performance signal processing
// Reduces garbage collection overhead and improves memory efficiency

export interface MemoryStats {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
}

export interface ObjectPool<T> {
  acquire(): T;
  release(obj: T): void;
  size(): number;
  clear(): void;
}

/**
 * Generic object pool to reduce object allocation/deallocation overhead
 */
export class GenericObjectPool<T> implements ObjectPool<T> {
  private pool: T[] = [];
  private factory: () => T;
  private reset: (obj: T) => void;
  private maxSize: number;

  constructor(
    factory: () => T,
    reset: (obj: T) => void,
    maxSize: number = 100
  ) {
    this.factory = factory;
    this.reset = reset;
    this.maxSize = maxSize;
  }

  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.factory();
  }

  release(obj: T): void {
    if (this.pool.length < this.maxSize) {
      this.reset(obj);
      this.pool.push(obj);
    }
  }

  size(): number {
    return this.pool.length;
  }

  clear(): void {
    this.pool.length = 0;
  }
}

/**
 * Performance metrics object pool
 */
interface PerformanceMetrics {
  signalReceivedAt: Date;
  jsonParsingStartTime: number;
  jsonParsingEndTime: number;
  transformationStartTime: number;
  transformationEndTime: number;
  webhookDeliveryStartTime: number;
  webhookDeliveryEndTime: number;
  totalStartTime: number;
}

const performanceMetricsPool = new GenericObjectPool<PerformanceMetrics>(
  () => ({
    signalReceivedAt: new Date(),
    jsonParsingStartTime: 0,
    jsonParsingEndTime: 0,
    transformationStartTime: 0,
    transformationEndTime: 0,
    webhookDeliveryStartTime: 0,
    webhookDeliveryEndTime: 0,
    totalStartTime: 0
  }),
  (obj) => {
    obj.signalReceivedAt = new Date();
    obj.jsonParsingStartTime = 0;
    obj.jsonParsingEndTime = 0;
    obj.transformationStartTime = 0;
    obj.transformationEndTime = 0;
    obj.webhookDeliveryStartTime = 0;
    obj.webhookDeliveryEndTime = 0;
    obj.totalStartTime = 0;
  },
  50 // Pool size for performance metrics
);

/**
 * Trade data object pool
 */
interface TradeData {
  user_id: string;
  webhook_id: string | null;
  robot_id: string | null;
  symbol: string;
  order_side: string;
  signal_type: string;
  trade_category: string;
  position_status: string;
  system_name: string;
  price: number;
  calculated_quantity: number;
  investment_amount: number;
  signal_name: string;
  category: string;
  forwarded: boolean;
  status: string;
  raw_signal_data: any;
  name: string;
}

const tradeDataPool = new GenericObjectPool<TradeData>(
  () => ({
    user_id: '',
    webhook_id: null,
    robot_id: null,
    symbol: '',
    order_side: '',
    signal_type: '',
    trade_category: '',
    position_status: '',
    system_name: '',
    price: 0,
    calculated_quantity: 0,
    investment_amount: 0,
    signal_name: '',
    category: '',
    forwarded: false,
    status: 'pending',
    raw_signal_data: null,
    name: ''
  }),
  (obj) => {
    obj.user_id = '';
    obj.webhook_id = null;
    obj.robot_id = null;
    obj.symbol = '';
    obj.order_side = '';
    obj.signal_type = '';
    obj.trade_category = '';
    obj.position_status = '';
    obj.system_name = '';
    obj.price = 0;
    obj.calculated_quantity = 0;
    obj.investment_amount = 0;
    obj.signal_name = '';
    obj.category = '';
    obj.forwarded = false;
    obj.status = 'pending';
    obj.raw_signal_data = null;
    obj.name = '';
  },
  100 // Pool size for trade data
);

/**
 * String buffer pool for efficient string operations
 */
class StringBuffer {
  private buffer: string[] = [];

  append(str: string): StringBuffer {
    this.buffer.push(str);
    return this;
  }

  toString(): string {
    return this.buffer.join('');
  }

  clear(): void {
    this.buffer.length = 0;
  }

  length(): number {
    return this.buffer.length;
  }
}

const stringBufferPool = new GenericObjectPool<StringBuffer>(
  () => new StringBuffer(),
  (obj) => obj.clear(),
  20 // Pool size for string buffers
);

/**
 * Memory optimization utilities
 */
export class MemoryOptimizer {
  private static gcThreshold = 50; // MB
  private static lastGcTime = 0;
  private static gcCooldown = 30000; // 30 seconds

  /**
   * Get performance metrics object from pool
   */
  static acquirePerformanceMetrics(): PerformanceMetrics {
    const metrics = performanceMetricsPool.acquire();
    metrics.signalReceivedAt = new Date();
    metrics.totalStartTime = performance.now();
    return metrics;
  }

  /**
   * Release performance metrics object back to pool
   */
  static releasePerformanceMetrics(metrics: PerformanceMetrics): void {
    performanceMetricsPool.release(metrics);
  }

  /**
   * Get trade data object from pool
   */
  static acquireTradeData(): TradeData {
    return tradeDataPool.acquire();
  }

  /**
   * Release trade data object back to pool
   */
  static releaseTradeData(tradeData: TradeData): void {
    tradeDataPool.release(tradeData);
  }

  /**
   * Get string buffer from pool
   */
  static acquireStringBuffer(): StringBuffer {
    return stringBufferPool.acquire();
  }

  /**
   * Release string buffer back to pool
   */
  static releaseStringBuffer(buffer: StringBuffer): void {
    stringBufferPool.release(buffer);
  }

  /**
   * Get current memory usage
   */
  static getMemoryUsage(): MemoryStats {
    if (typeof Deno !== 'undefined' && Deno.memoryUsage) {
      const usage = Deno.memoryUsage();
      return {
        heapUsed: usage.heapUsed,
        heapTotal: usage.heapTotal,
        external: usage.external,
        rss: usage.rss
      };
    }
    
    // Fallback for environments without Deno.memoryUsage
    return {
      heapUsed: 0,
      heapTotal: 0,
      external: 0,
      rss: 0
    };
  }

  /**
   * Force garbage collection if memory usage is high
   */
  static forceGcIfNeeded(): void {
    const now = Date.now();
    
    // Check cooldown
    if (now - this.lastGcTime < this.gcCooldown) {
      return;
    }

    const memory = this.getMemoryUsage();
    const heapUsedMB = memory.heapUsed / (1024 * 1024);

    if (heapUsedMB > this.gcThreshold) {
      console.log(`[MEMORY] High memory usage detected: ${heapUsedMB.toFixed(2)}MB, forcing GC`);
      
      if (typeof globalThis.gc === 'function') {
        globalThis.gc();
        this.lastGcTime = now;
        
        const afterMemory = this.getMemoryUsage();
        const afterHeapMB = afterMemory.heapUsed / (1024 * 1024);
        console.log(`[MEMORY] GC completed, memory reduced to: ${afterHeapMB.toFixed(2)}MB`);
      }
    }
  }

  /**
   * Optimize JSON parsing with minimal allocations
   */
  static parseJsonOptimized(jsonString: string): any {
    try {
      // Use native JSON.parse which is optimized in V8
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('[MEMORY] JSON parsing failed:', error);
      throw error;
    }
  }

  /**
   * Optimize JSON stringification with minimal allocations
   */
  static stringifyJsonOptimized(obj: any): string {
    try {
      // Use native JSON.stringify which is optimized in V8
      return JSON.stringify(obj);
    } catch (error) {
      console.error('[MEMORY] JSON stringification failed:', error);
      throw error;
    }
  }

  /**
   * Efficient string concatenation using buffer
   */
  static buildString(parts: string[]): string {
    const buffer = this.acquireStringBuffer();
    
    try {
      for (const part of parts) {
        buffer.append(part);
      }
      return buffer.toString();
    } finally {
      this.releaseStringBuffer(buffer);
    }
  }

  /**
   * Get object pool statistics
   */
  static getPoolStats(): any {
    return {
      performanceMetrics: {
        poolSize: performanceMetricsPool.size(),
        maxSize: 50
      },
      tradeData: {
        poolSize: tradeDataPool.size(),
        maxSize: 100
      },
      stringBuffer: {
        poolSize: stringBufferPool.size(),
        maxSize: 20
      }
    };
  }

  /**
   * Clear all object pools
   */
  static clearAllPools(): void {
    performanceMetricsPool.clear();
    tradeDataPool.clear();
    stringBufferPool.clear();
    console.log('[MEMORY] All object pools cleared');
  }

  /**
   * Log memory and pool statistics
   */
  static logMemoryStats(): void {
    const memory = this.getMemoryUsage();
    const pools = this.getPoolStats();
    
    console.log('[MEMORY] Memory Usage:', {
      heapUsed: `${(memory.heapUsed / (1024 * 1024)).toFixed(2)}MB`,
      heapTotal: `${(memory.heapTotal / (1024 * 1024)).toFixed(2)}MB`,
      rss: `${(memory.rss / (1024 * 1024)).toFixed(2)}MB`
    });
    
    console.log('[MEMORY] Pool Stats:', pools);
  }
}
