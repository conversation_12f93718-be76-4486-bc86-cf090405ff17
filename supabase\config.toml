project_id = "fllklckmycxcgwhboiji"

[api]
enabled = true
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[db]
major_version = 15

[db.pooler]
enabled = true
pool_mode = "transaction"
default_pool_size = 25
max_client_conn = 150

[realtime]
enabled = true

[studio]
enabled = true
api_url = "http://127.0.0.1:54321"
openai_api_key = "env(OPENAI_API_KEY)"

[inbucket]
enabled = true

[storage]
enabled = true
file_size_limit = "50MiB"

[auth]
enabled = true
site_url = "http://127.0.0.1:3001"
additional_redirect_urls = ["https://127.0.0.1:3001"]
jwt_expiry = 3600
enable_manual_linking = false

[functions."seller-signal-endpoint"]
verify_jwt = false

[functions."signal-relay-function"]
verify_jwt = false

[functions."signal-relay-debug"]
verify_jwt = false

[functions."platform-statistics-updater"]
verify_jwt = false

[functions."algobir-webhook-listener"]
verify_jwt = false

[edge_runtime]
enabled = true

[analytics]
enabled = false
gcp_project_id = ""
gcp_project_number = ""
gcp_jwt_path = "supabase/gcp.json"