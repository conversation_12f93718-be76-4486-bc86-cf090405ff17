import React from 'react';
import {
  Box,
  Flex,
  VStack,
  Tooltip,
  useColorModeValue,
  IconButton
} from '@chakra-ui/react';
import { NavLink, useLocation } from 'react-router-dom';
import { RouteType } from '../../../routes';
import { useAdminAuth } from '../../../hooks/useAdminAuth';
import { useSidebar } from '../../../context/SidebarContext';
import { TRANSITIONS } from '../animations';

interface SidebarContentCollapsedProps {
  routes: RouteType[];
}

const SidebarContentCollapsed: React.FC<SidebarContentCollapsedProps> = ({ routes }) => {
  const location = useLocation();
  const { isAdmin } = useAdminAuth();
  const { openSubmenu, collapseOnNavigation } = useSidebar();

  // Color mode values
  const brandColor = useColorModeValue('brand.500', 'white');
  const textColor = useColorModeValue('secondaryGray.500', 'white');
  const activeIcon = useColorModeValue('brand.500', 'white');
  const hoverBg = useColorModeValue('gray.100', 'whiteAlpha.100');

  // Check if route is active
  const activeRoute = (routeName: string) => {
    return location.pathname === routeName;
  };

  // Handle menu item click
  const handleMenuClick = (route: RouteType, e: React.MouseEvent) => {
    if (route.hasSubmenu && route.children) {
      e.preventDefault();
      const submenuItems = route.children.map(child => ({
        id: child.id,
        name: child.name,
        path: child.path,
        icon: child.icon,
        description: child.description
      }));
      openSubmenu(route.path, submenuItems);
    }
  };

  // Handle navigation link click - auto-collapse sidebar
  const handleNavigationClick = () => {
    collapseOnNavigation();
  };

  return (
    <Flex direction='column' minH='100vh' pt='20px' px='8px' pb='20px'>
      {/* Toggle Button Removed - Only navbar toggle button should be used */}

      {/* Brand Logo - Collapsed */}
      <Flex
        h="50px"
        mb="24px"
        alignItems="center"
        justifyContent="center"
        pb="16px"
        flexShrink={0}
        w="100%"
      >
        <Box
          w="36px"
          h="36px"
          bg={brandColor}
          borderRadius="10px"
          display="flex"
          alignItems="center"
          justifyContent="center"
          color="white"
          fontWeight="bold"
          fontSize="md"
          flexShrink={0}
        >
          A
        </Box>
      </Flex>
      
      {/* Navigation Icons */}
      <VStack spacing={1} flexGrow={1} w="100%" alignItems="center">
        {routes.map((route: RouteType, index: number) => {
          // Admin paneli kontrolü
          if (route.path === '/admin' && !isAdmin) {
            return null;
          }

          const isActive = activeRoute(route.path);

          return (
            <Tooltip
              key={index}
              label={`${route.name}${route.hasSubmenu ? ' (Alt menü mevcut)' : ''}`}
              placement="right"
              hasArrow
              bg="gray.700"
              color="white"
              fontSize="sm"
              px={3}
              py={2}
              borderRadius="md"
              openDelay={500}
              closeDelay={200}
            >
              <Box w="100%">
                {route.hasSubmenu ? (
                  <IconButton
                    aria-label={`${route.name} alt menüsünü aç`}
                    aria-expanded={false}
                    aria-haspopup="menu"
                    icon={route.icon}
                    variant="ghost"
                    size="md"
                    w="40px"
                    h="40px"
                    minW="40px"
                    borderRadius="10px"
                    color={isActive ? activeIcon : textColor}
                    bg={isActive ? hoverBg : 'transparent'}
                    _hover={{
                      bg: hoverBg,
                      transform: 'scale(1.05)',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    }}
                    _focus={{
                      bg: hoverBg,
                      boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
                      outline: 'none',
                    }}
                    _active={{
                      transform: 'scale(0.95)'
                    }}
                    transition={TRANSITIONS.icon}
                    sx={{
                      '@media (prefers-reduced-motion: reduce)': {
                        transition: 'none'
                      }
                    }}
                    onClick={(e) => handleMenuClick(route, e)}
                  />
                ) : (
                  <NavLink to={route.path} onClick={handleNavigationClick}>
                    <IconButton
                      aria-label={`${route.name} sayfasına git`}
                      icon={route.icon}
                      variant="ghost"
                      size="md"
                      w="40px"
                      h="40px"
                      minW="40px"
                      borderRadius="10px"
                      color={isActive ? activeIcon : textColor}
                      bg={isActive ? hoverBg : 'transparent'}
                      _hover={{
                        bg: hoverBg,
                        transform: 'scale(1.05)',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                      }}
                      _focus={{
                        bg: hoverBg,
                        boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
                        outline: 'none',
                      }}
                      _active={{
                        transform: 'scale(0.95)'
                      }}
                      transition={TRANSITIONS.icon}
                      sx={{
                        '@media (prefers-reduced-motion: reduce)': {
                          transition: 'none'
                        }
                      }}
                    />
                  </NavLink>
                )}
              </Box>
            </Tooltip>
          );
        })}
      </VStack>
    </Flex>
  );
};

export default SidebarContentCollapsed;
