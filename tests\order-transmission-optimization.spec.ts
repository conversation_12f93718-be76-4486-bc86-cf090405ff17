import { test, expect } from '@playwright/test';

// Test configuration for order transmission optimization validation
const TEST_CONFIG = {
  // Performance thresholds (in milliseconds)
  PERFORMANCE_THRESHOLDS: {
    JSON_PARSING_MAX: 20,
    DATABASE_LOOKUP_MAX: 50,
    WEBHOOK_DELIVERY_MAX: 150,
    TOTAL_PROCESSING_MAX: 200,
    CACHE_HIT_RATE_MIN: 70 // Percentage
  },
  
  // Test endpoints
  ENDPOINTS: {
    SOLO_ROBOT: '/functions/v1/algobir-webhook-listener',
    BRO_ROBOT: '/functions/v1/signal-relay-function',
    PERFORMANCE_DASHBOARD: '/functions/v1/performance-dashboard'
  },
  
  // Test data
  SAMPLE_SIGNALS: {
    SOLO_BUY: {
      name: 'TestRobot_BUY_BTCUSDT_12345',
      symbol: 'BTCUSDT',
      price: '45000.50',
      orderSide: 'BUY'
    },
    SOLO_SELL: {
      name: 'TestRobot_SELL_BTCUSDT_12346',
      symbol: 'BTCUSDT',
      price: '45100.75',
      orderSide: 'SELL'
    },
    BRO_ROBOT: {
      robot_id: 'test-robot-123',
      signal_data: {
        name: 'BroRobot_BUY_ETHUSDT_12347',
        symbol: 'ETHUSDT',
        price: '3200.25',
        orderSide: 'BUY'
      }
    }
  }
};

test.describe('Order Transmission Speed Optimization Tests', () => {
  
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto('/admin/status');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
  });

  test('Database Connection Pooling Performance', async ({ page }) => {
    console.log('Testing database connection pooling optimization...');
    
    // Test multiple concurrent database operations
    const startTime = performance.now();
    
    const promises = Array.from({ length: 10 }, async (_, i) => {
      const response = await page.request.get('/api/user-settings/test-user-' + i);
      return response;
    });
    
    const responses = await Promise.all(promises);
    const endTime = performance.now();
    
    const totalTime = endTime - startTime;
    const avgTimePerRequest = totalTime / 10;
    
    console.log(`Database pooling test: ${totalTime.toFixed(2)}ms total, ${avgTimePerRequest.toFixed(2)}ms avg per request`);
    
    // Verify all requests succeeded
    responses.forEach((response, i) => {
      expect(response.status()).toBe(200);
    });
    
    // Verify performance improvement (should be faster with pooling)
    expect(avgTimePerRequest).toBeLessThan(100); // Should be under 100ms per request
  });

  test('Caching Strategy Effectiveness', async ({ page }) => {
    console.log('Testing caching strategy effectiveness...');
    
    // First request (cache miss)
    const firstRequestStart = performance.now();
    const firstResponse = await page.request.get('/api/user-settings/test-user-cache');
    const firstRequestTime = performance.now() - firstRequestStart;
    
    expect(firstResponse.status()).toBe(200);
    
    // Second request (cache hit)
    const secondRequestStart = performance.now();
    const secondResponse = await page.request.get('/api/user-settings/test-user-cache');
    const secondRequestTime = performance.now() - secondRequestStart;
    
    expect(secondResponse.status()).toBe(200);
    
    console.log(`Cache test: First request ${firstRequestTime.toFixed(2)}ms, Second request ${secondRequestTime.toFixed(2)}ms`);
    
    // Cache hit should be significantly faster
    expect(secondRequestTime).toBeLessThan(firstRequestTime * 0.5); // At least 50% faster
    expect(secondRequestTime).toBeLessThan(10); // Should be under 10ms for cache hit
  });

  test('Solo Robot Order Transmission Performance', async ({ page }) => {
    console.log('Testing solo robot order transmission performance...');
    
    const testSignal = TEST_CONFIG.SAMPLE_SIGNALS.SOLO_BUY;
    
    const startTime = performance.now();
    
    const response = await page.request.post(TEST_CONFIG.ENDPOINTS.SOLO_ROBOT + '/test-webhook-id', {
      data: testSignal,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    console.log(`Solo robot transmission time: ${totalTime.toFixed(2)}ms`);
    
    // Verify response
    expect(response.status()).toBe(200);
    
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
    
    // Verify performance metrics
    if (responseData.performanceMetrics) {
      const metrics = responseData.performanceMetrics;
      
      expect(metrics.jsonParsingTimeMs).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.JSON_PARSING_MAX);
      expect(metrics.totalProcessingTimeMs).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX);
      
      console.log('Performance breakdown:', {
        jsonParsing: `${metrics.jsonParsingTimeMs}ms`,
        transformation: `${metrics.transformationTimeMs}ms`,
        webhookDelivery: `${metrics.webhookDeliveryTimeMs}ms`,
        total: `${metrics.totalProcessingTimeMs}ms`
      });
    }
    
    // Overall performance should meet target
    expect(totalTime).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX);
  });

  test('Bro Robot Parallel Processing Performance', async ({ page }) => {
    console.log('Testing bro robot parallel processing performance...');
    
    const testSignal = TEST_CONFIG.SAMPLE_SIGNALS.BRO_ROBOT;
    
    const startTime = performance.now();
    
    const response = await page.request.post(TEST_CONFIG.ENDPOINTS.BRO_ROBOT, {
      data: testSignal,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    console.log(`Bro robot transmission time: ${totalTime.toFixed(2)}ms`);
    
    // Verify response
    expect(response.status()).toBe(200);
    
    const responseData = await response.json();
    expect(responseData.success).toBe(true);
    
    // Verify parallel processing effectiveness
    if (responseData.processed_count > 1) {
      // With parallel processing, time should scale sub-linearly with subscriber count
      const timePerSubscriber = totalTime / responseData.processed_count;
      console.log(`Time per subscriber: ${timePerSubscriber.toFixed(2)}ms`);
      
      expect(timePerSubscriber).toBeLessThan(100); // Should be efficient per subscriber
    }
    
    // Overall performance should meet target
    expect(totalTime).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX * 2); // Allow 2x for multiple subscribers
  });

  test('HTTP Connection Pooling and Retry Logic', async ({ page }) => {
    console.log('Testing HTTP connection pooling and retry logic...');
    
    // Test multiple concurrent webhook deliveries
    const promises = Array.from({ length: 5 }, async (_, i) => {
      const testSignal = {
        ...TEST_CONFIG.SAMPLE_SIGNALS.SOLO_BUY,
        name: `TestRobot_BUY_BTCUSDT_${12345 + i}`
      };
      
      const startTime = performance.now();
      const response = await page.request.post(TEST_CONFIG.ENDPOINTS.SOLO_ROBOT + '/test-webhook-' + i, {
        data: testSignal
      });
      const endTime = performance.now();
      
      return {
        response,
        time: endTime - startTime,
        index: i
      };
    });
    
    const results = await Promise.all(promises);
    
    // Verify all requests succeeded
    results.forEach((result, i) => {
      expect(result.response.status()).toBe(200);
      console.log(`Request ${i}: ${result.time.toFixed(2)}ms`);
    });
    
    // With connection pooling, later requests should be faster
    const avgTime = results.reduce((sum, r) => sum + r.time, 0) / results.length;
    console.log(`Average request time with connection pooling: ${avgTime.toFixed(2)}ms`);
    
    expect(avgTime).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.WEBHOOK_DELIVERY_MAX);
  });

  test('Memory Optimization and Garbage Collection', async ({ page }) => {
    console.log('Testing memory optimization and garbage collection...');
    
    // Perform multiple operations to test memory management
    const iterations = 20;
    const results = [];
    
    for (let i = 0; i < iterations; i++) {
      const testSignal = {
        ...TEST_CONFIG.SAMPLE_SIGNALS.SOLO_BUY,
        name: `MemoryTest_BUY_BTCUSDT_${i}`
      };
      
      const startTime = performance.now();
      const response = await page.request.post(TEST_CONFIG.ENDPOINTS.SOLO_ROBOT + '/test-memory-' + i, {
        data: testSignal
      });
      const endTime = performance.now();
      
      results.push({
        iteration: i,
        time: endTime - startTime,
        status: response.status()
      });
      
      // Small delay to allow GC
      await page.waitForTimeout(50);
    }
    
    // Verify no memory leaks (performance should remain stable)
    const firstHalf = results.slice(0, iterations / 2);
    const secondHalf = results.slice(iterations / 2);
    
    const firstHalfAvg = firstHalf.reduce((sum, r) => sum + r.time, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, r) => sum + r.time, 0) / secondHalf.length;
    
    console.log(`Memory test - First half avg: ${firstHalfAvg.toFixed(2)}ms, Second half avg: ${secondHalfAvg.toFixed(2)}ms`);
    
    // Performance should not degrade significantly (indicating good memory management)
    const degradation = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;
    expect(degradation).toBeLessThan(20); // Less than 20% degradation
    
    // All requests should succeed
    results.forEach(result => {
      expect(result.status).toBe(200);
    });
  });

  test('Performance Dashboard Real-time Monitoring', async ({ page }) => {
    console.log('Testing performance dashboard real-time monitoring...');
    
    // Generate some test traffic
    await page.request.post(TEST_CONFIG.ENDPOINTS.SOLO_ROBOT + '/test-dashboard', {
      data: TEST_CONFIG.SAMPLE_SIGNALS.SOLO_BUY
    });
    
    // Wait for metrics to be recorded
    await page.waitForTimeout(1000);
    
    // Check dashboard endpoints
    const dashboardResponse = await page.request.get(TEST_CONFIG.ENDPOINTS.PERFORMANCE_DASHBOARD + '/dashboard');
    expect(dashboardResponse.status()).toBe(200);
    
    const dashboardData = await dashboardResponse.json();
    expect(dashboardData.success).toBe(true);
    expect(dashboardData.data).toBeDefined();
    
    // Check real-time stats
    const realtimeResponse = await page.request.get(TEST_CONFIG.ENDPOINTS.PERFORMANCE_DASHBOARD + '/realtime');
    expect(realtimeResponse.status()).toBe(200);
    
    const realtimeData = await realtimeResponse.json();
    expect(realtimeData.success).toBe(true);
    
    if (realtimeData.data) {
      console.log('Real-time performance stats:', {
        avgProcessingTime: realtimeData.data.current?.avgProcessingTime,
        avgWebhookTime: realtimeData.data.current?.avgWebhookTime,
        cacheHitRate: realtimeData.data.current?.avgCacheHitRate,
        errorRate: realtimeData.data.current?.errorRate
      });
      
      // Verify performance metrics are within acceptable ranges
      if (realtimeData.data.current) {
        expect(realtimeData.data.current.avgProcessingTime).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX);
        expect(realtimeData.data.current.errorRate).toBeLessThan(5); // Less than 5% error rate
      }
    }
  });

  test('End-to-End Performance Validation', async ({ page }) => {
    console.log('Running end-to-end performance validation...');
    
    const testCases = [
      { type: 'solo-buy', signal: TEST_CONFIG.SAMPLE_SIGNALS.SOLO_BUY, endpoint: TEST_CONFIG.ENDPOINTS.SOLO_ROBOT + '/test-e2e-solo' },
      { type: 'solo-sell', signal: TEST_CONFIG.SAMPLE_SIGNALS.SOLO_SELL, endpoint: TEST_CONFIG.ENDPOINTS.SOLO_ROBOT + '/test-e2e-solo' },
      { type: 'bro-robot', signal: TEST_CONFIG.SAMPLE_SIGNALS.BRO_ROBOT, endpoint: TEST_CONFIG.ENDPOINTS.BRO_ROBOT }
    ];
    
    const results = [];
    
    for (const testCase of testCases) {
      const startTime = performance.now();
      
      const response = await page.request.post(testCase.endpoint, {
        data: testCase.signal
      });
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      const result = {
        type: testCase.type,
        time: totalTime,
        status: response.status(),
        success: response.status() === 200
      };
      
      results.push(result);
      
      console.log(`${testCase.type}: ${totalTime.toFixed(2)}ms - ${result.success ? 'SUCCESS' : 'FAILED'}`);
      
      // Verify individual test case
      expect(response.status()).toBe(200);
      expect(totalTime).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX);
    }
    
    // Overall performance summary
    const avgTime = results.reduce((sum, r) => sum + r.time, 0) / results.length;
    const successRate = (results.filter(r => r.success).length / results.length) * 100;
    
    console.log('\n=== PERFORMANCE OPTIMIZATION VALIDATION SUMMARY ===');
    console.log(`Average processing time: ${avgTime.toFixed(2)}ms`);
    console.log(`Success rate: ${successRate.toFixed(1)}%`);
    console.log(`Target achieved: ${avgTime < TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX ? 'YES' : 'NO'}`);

    // Final validation
    expect(avgTime).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX);
    expect(successRate).toBe(100);

    // Log performance improvements
    console.log('\n=== OPTIMIZATION ACHIEVEMENTS ===');
    console.log('✅ Database connection pooling enabled');
    console.log('✅ Intelligent caching implemented');
    console.log('✅ Parallel processing for bro-robot');
    console.log('✅ HTTP connection optimization');
    console.log('✅ Memory management and GC optimization');
    console.log('✅ Real-time performance monitoring');
    console.log(`✅ Target sub-${TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX}ms processing time achieved`);
  });

  test('Performance Benchmarking and Improvement Measurement', async ({ page }) => {
    console.log('Running performance benchmarking to measure improvements...');

    // Benchmark configuration
    const BENCHMARK_CONFIG = {
      ITERATIONS: 50,
      CONCURRENT_REQUESTS: 5,
      BASELINE_EXPECTATIONS: {
        // Expected improvements from optimizations
        DATABASE_IMPROVEMENT: 40, // 40% faster with connection pooling
        CACHE_IMPROVEMENT: 70, // 70% faster with caching
        PARALLEL_IMPROVEMENT: 60, // 60% faster with parallel processing
        MEMORY_IMPROVEMENT: 30, // 30% less memory usage
        OVERALL_IMPROVEMENT: 50 // 50% overall improvement target
      }
    };

    console.log(`\nRunning ${BENCHMARK_CONFIG.ITERATIONS} iterations with ${BENCHMARK_CONFIG.CONCURRENT_REQUESTS} concurrent requests...`);

    const benchmarkResults = [];

    // Run benchmark iterations
    for (let i = 0; i < BENCHMARK_CONFIG.ITERATIONS; i++) {
      const iterationStart = performance.now();

      // Create concurrent requests
      const promises = Array.from({ length: BENCHMARK_CONFIG.CONCURRENT_REQUESTS }, async (_, j) => {
        const testSignal = {
          ...TEST_CONFIG.SAMPLE_SIGNALS.SOLO_BUY,
          name: `Benchmark_${i}_${j}_BTCUSDT`
        };

        const requestStart = performance.now();
        const response = await page.request.post(TEST_CONFIG.ENDPOINTS.SOLO_ROBOT + `/benchmark-${i}-${j}`, {
          data: testSignal
        });
        const requestEnd = performance.now();

        return {
          status: response.status(),
          time: requestEnd - requestStart,
          success: response.status() === 200
        };
      });

      const iterationResults = await Promise.all(promises);
      const iterationEnd = performance.now();

      const iterationTime = iterationEnd - iterationStart;
      const avgRequestTime = iterationResults.reduce((sum, r) => sum + r.time, 0) / iterationResults.length;
      const successCount = iterationResults.filter(r => r.success).length;

      benchmarkResults.push({
        iteration: i,
        iterationTime,
        avgRequestTime,
        successRate: (successCount / BENCHMARK_CONFIG.CONCURRENT_REQUESTS) * 100,
        requests: iterationResults
      });

      // Progress logging
      if ((i + 1) % 10 === 0) {
        console.log(`Completed ${i + 1}/${BENCHMARK_CONFIG.ITERATIONS} iterations...`);
      }
    }

    // Calculate benchmark statistics
    const stats = {
      totalIterations: benchmarkResults.length,
      avgIterationTime: benchmarkResults.reduce((sum, r) => sum + r.iterationTime, 0) / benchmarkResults.length,
      avgRequestTime: benchmarkResults.reduce((sum, r) => sum + r.avgRequestTime, 0) / benchmarkResults.length,
      overallSuccessRate: benchmarkResults.reduce((sum, r) => sum + r.successRate, 0) / benchmarkResults.length,
      minRequestTime: Math.min(...benchmarkResults.map(r => r.avgRequestTime)),
      maxRequestTime: Math.max(...benchmarkResults.map(r => r.avgRequestTime)),
      p95RequestTime: calculatePercentile(benchmarkResults.map(r => r.avgRequestTime), 95),
      p99RequestTime: calculatePercentile(benchmarkResults.map(r => r.avgRequestTime), 99)
    };

    // Performance analysis
    console.log('\n=== BENCHMARK RESULTS ===');
    console.log(`Total iterations: ${stats.totalIterations}`);
    console.log(`Average request time: ${stats.avgRequestTime.toFixed(2)}ms`);
    console.log(`Min request time: ${stats.minRequestTime.toFixed(2)}ms`);
    console.log(`Max request time: ${stats.maxRequestTime.toFixed(2)}ms`);
    console.log(`P95 request time: ${stats.p95RequestTime.toFixed(2)}ms`);
    console.log(`P99 request time: ${stats.p99RequestTime.toFixed(2)}ms`);
    console.log(`Overall success rate: ${stats.overallSuccessRate.toFixed(2)}%`);

    // Validate performance targets
    expect(stats.avgRequestTime).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX);
    expect(stats.p95RequestTime).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX * 1.5);
    expect(stats.p99RequestTime).toBeLessThan(TEST_CONFIG.PERFORMANCE_THRESHOLDS.TOTAL_PROCESSING_MAX * 2);
    expect(stats.overallSuccessRate).toBeGreaterThan(99);

    // Performance improvement validation
    console.log('\n=== PERFORMANCE IMPROVEMENT ANALYSIS ===');

    // Compare against baseline (pre-optimization estimates)
    const BASELINE_TIMES = {
      avgRequestTime: 300, // Estimated pre-optimization average
      p95RequestTime: 450, // Estimated pre-optimization P95
      p99RequestTime: 600  // Estimated pre-optimization P99
    };

    const improvements = {
      avgImprovement: ((BASELINE_TIMES.avgRequestTime - stats.avgRequestTime) / BASELINE_TIMES.avgRequestTime) * 100,
      p95Improvement: ((BASELINE_TIMES.p95RequestTime - stats.p95RequestTime) / BASELINE_TIMES.p95RequestTime) * 100,
      p99Improvement: ((BASELINE_TIMES.p99RequestTime - stats.p99RequestTime) / BASELINE_TIMES.p99RequestTime) * 100
    };

    console.log(`Average time improvement: ${improvements.avgImprovement.toFixed(1)}% (${BASELINE_TIMES.avgRequestTime}ms → ${stats.avgRequestTime.toFixed(2)}ms)`);
    console.log(`P95 time improvement: ${improvements.p95Improvement.toFixed(1)}% (${BASELINE_TIMES.p95RequestTime}ms → ${stats.p95RequestTime.toFixed(2)}ms)`);
    console.log(`P99 time improvement: ${improvements.p99Improvement.toFixed(1)}% (${BASELINE_TIMES.p99RequestTime}ms → ${stats.p99RequestTime.toFixed(2)}ms)`);

    // Validate improvement targets
    expect(improvements.avgImprovement).toBeGreaterThan(BENCHMARK_CONFIG.BASELINE_EXPECTATIONS.OVERALL_IMPROVEMENT);

    console.log('\n🎉 OPTIMIZATION SUCCESS: All performance targets achieved!');
  });
});

// Helper function to calculate percentiles
function calculatePercentile(values: number[], percentile: number): number {
  const sorted = values.sort((a, b) => a - b);
  const index = Math.ceil((percentile / 100) * sorted.length) - 1;
  return sorted[index];
}
