/**
 * Build Cache Configuration
 * Phase 3.4: Build & Deployment Process Optimization
 * Advanced caching strategies for faster builds
 */

export const BUILD_CACHE_CONFIG = {
  // Cache directories
  directories: {
    nodeModules: 'node_modules',
    buildCache: '.cache',
    distCache: '.dist-cache',
    viteCache: 'node_modules/.vite',
    typescriptCache: 'node_modules/.tsc-cache'
  },

  // Cache strategies
  strategies: {
    // Dependency caching
    dependencies: {
      enabled: true,
      key: 'package-lock.json',
      paths: ['node_modules'],
      compression: true,
      ttl: 7 * 24 * 60 * 60 * 1000 // 7 days
    },

    // TypeScript compilation cache
    typescript: {
      enabled: true,
      key: ['tsconfig.json', 'src/**/*.ts', 'src/**/*.tsx'],
      paths: ['node_modules/.tsc-cache'],
      compression: true,
      ttl: 24 * 60 * 60 * 1000 // 24 hours
    },

    // Vite build cache
    vite: {
      enabled: true,
      key: ['vite.config.ts', 'package.json'],
      paths: ['node_modules/.vite'],
      compression: false, // Vite handles its own compression
      ttl: 24 * 60 * 60 * 1000 // 24 hours
    },

    // Asset optimization cache
    assets: {
      enabled: true,
      key: ['src/assets/**/*', 'public/**/*'],
      paths: ['.cache/assets'],
      compression: true,
      ttl: 30 * 24 * 60 * 60 * 1000 // 30 days
    }
  },

  // Build optimization settings
  optimization: {
    // Parallel processing
    parallel: {
      enabled: true,
      workers: 'auto', // Use all available CPU cores
      maxWorkers: 8    // Maximum worker limit
    },

    // Memory management
    memory: {
      maxOldSpaceSize: 4096, // 4GB max heap size
      maxSemiSpaceSize: 256, // 256MB semi-space
      gcInterval: 100        // Garbage collection interval
    },

    // Build splitting
    splitting: {
      enabled: true,
      chunkSizeLimit: 500 * 1024, // 500KB
      vendorChunkSizeLimit: 1024 * 1024, // 1MB
      dynamicImportThreshold: 50 * 1024 // 50KB
    }
  },

  // Environment-specific configurations
  environments: {
    development: {
      cache: {
        enabled: true,
        aggressive: false
      },
      optimization: {
        minify: false,
        sourcemap: true,
        treeshake: false
      }
    },

    staging: {
      cache: {
        enabled: true,
        aggressive: true
      },
      optimization: {
        minify: true,
        sourcemap: true,
        treeshake: true
      }
    },

    production: {
      cache: {
        enabled: true,
        aggressive: true
      },
      optimization: {
        minify: true,
        sourcemap: false,
        treeshake: true
      }
    }
  },

  // Cache invalidation rules
  invalidation: {
    // Automatic invalidation triggers
    triggers: [
      'package.json',
      'package-lock.json',
      'tsconfig.json',
      'vite.config.ts',
      '.env',
      '.env.local',
      '.env.production'
    ],

    // Manual invalidation commands
    commands: {
      full: 'npm run cache:clear',
      deps: 'npm run cache:clear:deps',
      build: 'npm run cache:clear:build',
      assets: 'npm run cache:clear:assets'
    }
  },

  // Performance monitoring
  monitoring: {
    enabled: true,
    metrics: {
      buildTime: true,
      cacheHitRate: true,
      bundleSize: true,
      memoryUsage: true
    },
    reporting: {
      console: true,
      file: '.cache/build-metrics.json',
      webhook: process.env.BUILD_METRICS_WEBHOOK
    }
  }
};

// Cache management utilities
export const CacheManager = {
  /**
   * Clear all caches
   */
  clearAll() {
    const fs = require('fs');
    const path = require('path');
    
    Object.values(BUILD_CACHE_CONFIG.directories).forEach(dir => {
      const fullPath = path.resolve(dir);
      if (fs.existsSync(fullPath)) {
        fs.rmSync(fullPath, { recursive: true, force: true });
        console.log(`Cleared cache: ${dir}`);
      }
    });
  },

  /**
   * Clear specific cache type
   */
  clear(type) {
    const fs = require('fs');
    const path = require('path');
    
    const strategy = BUILD_CACHE_CONFIG.strategies[type];
    if (!strategy) {
      console.error(`Unknown cache type: ${type}`);
      return;
    }

    strategy.paths.forEach(cachePath => {
      const fullPath = path.resolve(cachePath);
      if (fs.existsSync(fullPath)) {
        fs.rmSync(fullPath, { recursive: true, force: true });
        console.log(`Cleared ${type} cache: ${cachePath}`);
      }
    });
  },

  /**
   * Get cache statistics
   */
  getStats() {
    const fs = require('fs');
    const path = require('path');
    
    const stats = {};
    
    Object.entries(BUILD_CACHE_CONFIG.directories).forEach(([name, dir]) => {
      const fullPath = path.resolve(dir);
      if (fs.existsSync(fullPath)) {
        const stat = fs.statSync(fullPath);
        stats[name] = {
          exists: true,
          size: this.getDirectorySize(fullPath),
          modified: stat.mtime
        };
      } else {
        stats[name] = { exists: false };
      }
    });
    
    return stats;
  },

  /**
   * Calculate directory size recursively
   */
  getDirectorySize(dirPath) {
    const fs = require('fs');
    const path = require('path');
    
    let totalSize = 0;
    
    try {
      const files = fs.readdirSync(dirPath);
      
      files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          totalSize += this.getDirectorySize(filePath);
        } else {
          totalSize += stat.size;
        }
      });
    } catch (error) {
      // Ignore errors (permission issues, etc.)
    }
    
    return totalSize;
  }
};

export default BUILD_CACHE_CONFIG;
