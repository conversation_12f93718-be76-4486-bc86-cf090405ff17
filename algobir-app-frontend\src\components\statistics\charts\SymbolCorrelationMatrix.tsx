import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonGroup,
  Select,
  useColorModeValue,
  Tooltip,
  Badge,
  Flex,
  Spacer,
  Grid,
  GridItem,

  Switch,
  FormControl,
  FormLabel,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  Alert,
  AlertIcon
} from '@chakra-ui/react';

import { motion } from 'framer-motion';
import { SymbolCorrelation } from '../../../types/symbolAnalysis';

const MotionBox = motion(Box);

interface SymbolCorrelationMatrixProps {
  data: SymbolCorrelation[];
  height?: number;
  selectedSymbols?: string[];
  onSymbolClick?: (symbol: string) => void;
  maxSymbols?: number;
}

type DisplayMode = 'full' | 'upper' | 'lower';
type ColorScheme = 'default' | 'heatmap' | 'diverging';

const SymbolCorrelationMatrix: React.FC<SymbolCorrelationMatrixProps> = ({
  data,
  height = 600,
  selectedSymbols = [],
  onSymbolClick,
  maxSymbols = 15
}) => {
  const [displayMode, setDisplayMode] = useState<DisplayMode>('upper');
  const [colorScheme, setColorScheme] = useState<ColorScheme>('diverging');
  const [showValues, setShowValues] = useState(true);
  const [significanceFilter, setSignificanceFilter] = useState(0.05);
  const [minCorrelation, setMinCorrelation] = useState(0);

  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Process correlation data into matrix format
  const { symbols, correlationMatrix, filteredData } = useMemo(() => {
    if (!data || data.length === 0) {
      return { symbols: [], correlationMatrix: {}, filteredData: [] };
    }

    // Get unique symbols
    const allSymbols = new Set<string>();
    data.forEach(item => {
      allSymbols.add(item.symbol1);
      allSymbols.add(item.symbol2);
    });

    let symbolList = Array.from(allSymbols);

    // Filter by selected symbols if any
    if (selectedSymbols.length > 0) {
      symbolList = symbolList.filter(symbol => selectedSymbols.includes(symbol));
    } else {
      // Limit to maxSymbols
      symbolList = symbolList.slice(0, maxSymbols);
    }

    // Create correlation matrix
    const matrix: { [key: string]: { [key: string]: SymbolCorrelation } } = {};
    
    symbolList.forEach(symbol1 => {
      matrix[symbol1] = {};
      symbolList.forEach(symbol2 => {
        // Find correlation data
        const correlation = data.find(item => 
          (item.symbol1 === symbol1 && item.symbol2 === symbol2) ||
          (item.symbol1 === symbol2 && item.symbol2 === symbol1)
        );
        
        if (correlation) {
          matrix[symbol1][symbol2] = correlation;
        } else {
          // Create default correlation for missing pairs
          matrix[symbol1][symbol2] = {
            symbol1,
            symbol2,
            correlation: symbol1 === symbol2 ? 1.0 : 0,
            pValueSignificance: symbol1 === symbol2 ? 0 : 1,
            tradeCountOverlap: 0,
            timeOverlapPercent: 0
          };
        }
      });
    });

    // Filter data based on criteria
    const filtered = data.filter(item => {
      const absCorrelation = Math.abs(item.correlation);
      return absCorrelation >= minCorrelation && 
             item.pValueSignificance <= significanceFilter &&
             symbolList.includes(item.symbol1) &&
             symbolList.includes(item.symbol2);
    });

    return {
      symbols: symbolList,
      correlationMatrix: matrix,
      filteredData: filtered
    };
  }, [data, selectedSymbols, maxSymbols, minCorrelation, significanceFilter]);

  // Get cell color based on correlation value
  const getCellColor = (correlation: number, pValue: number) => {
    // Check significance
    if (pValue > significanceFilter) {
      return useColorModeValue('#F7FAFC', '#2D3748'); // Gray for non-significant
    }

    const absCorr = Math.abs(correlation);
    
    switch (colorScheme) {
      case 'heatmap':
        // Blue intensity based on absolute correlation
        const intensity = absCorr;
        return `rgba(59, 130, 246, ${intensity})`;
        
      case 'diverging':
        // Red for negative, blue for positive
        if (correlation > 0) {
          return `rgba(59, 130, 246, ${absCorr})`; // Blue for positive
        } else {
          return `rgba(239, 68, 68, ${absCorr})`; // Red for negative
        }
        
      default:
        // Default green-red scheme
        if (correlation > 0.7) return '#22C55E'; // Strong positive - green
        if (correlation > 0.3) return '#84CC16'; // Moderate positive - light green
        if (correlation > -0.3) return '#E5E7EB'; // Weak - gray
        if (correlation > -0.7) return '#F97316'; // Moderate negative - orange
        return '#EF4444'; // Strong negative - red
    }
  };

  // Get text color for readability
  const getTextColor = (correlation: number, pValue: number) => {
    if (pValue > significanceFilter) return textColor;
    
    const absCorr = Math.abs(correlation);
    if (colorScheme === 'default') {
      return absCorr > 0.5 ? 'white' : textColor;
    }
    return absCorr > 0.6 ? 'white' : textColor;
  };

  // Should show cell based on display mode
  const shouldShowCell = (rowIndex: number, colIndex: number) => {
    switch (displayMode) {
      case 'upper':
        return colIndex >= rowIndex;
      case 'lower':
        return colIndex <= rowIndex;
      case 'full':
        return true;
      default:
        return true;
    }
  };

  // Handle cell click
  const handleCellClick = (symbol1: string, symbol2: string) => {
    if (symbol1 === symbol2) {
      onSymbolClick?.(symbol1);
    } else {
      // Could implement pair analysis
      console.log(`Correlation between ${symbol1} and ${symbol2}`);
    }
  };

  // Get correlation statistics
  const correlationStats = useMemo(() => {
    if (filteredData.length === 0) return null;

    const correlations = filteredData
      .filter(item => item.symbol1 !== item.symbol2)
      .map(item => item.correlation);

    if (correlations.length === 0) return null;

    const avgCorrelation = correlations.reduce((sum, corr) => sum + corr, 0) / correlations.length;
    const maxCorrelation = Math.max(...correlations);
    const minCorrelation = Math.min(...correlations);
    const strongPositive = correlations.filter(corr => corr > 0.7).length;
    const strongNegative = correlations.filter(corr => corr < -0.7).length;

    return {
      average: avgCorrelation,
      maximum: maxCorrelation,
      minimum: minCorrelation,
      strongPositive,
      strongNegative,
      total: correlations.length
    };
  }, [filteredData]);

  if (symbols.length === 0) {
    return (
      <Alert status="info" borderRadius="md">
        <AlertIcon />
        <Text>Korelasyon analizi için yeterli sembol verisi bulunamadı.</Text>
      </Alert>
    );
  }

  return (
    <VStack spacing={4} align="stretch">
      {/* Controls */}
      <Flex wrap="wrap" gap={4} align="center">
        {/* Display Mode */}
        <ButtonGroup size="sm" isAttached>
          <Button
            variant={displayMode === 'full' ? 'solid' : 'outline'}
            onClick={() => setDisplayMode('full')}
          >
            Tam Matris
          </Button>
          <Button
            variant={displayMode === 'upper' ? 'solid' : 'outline'}
            onClick={() => setDisplayMode('upper')}
          >
            Üst Üçgen
          </Button>
          <Button
            variant={displayMode === 'lower' ? 'solid' : 'outline'}
            onClick={() => setDisplayMode('lower')}
          >
            Alt Üçgen
          </Button>
        </ButtonGroup>

        {/* Color Scheme */}
        <HStack>
          <Text fontSize="sm" fontWeight="medium" color={textColor}>
            Renk:
          </Text>
          <Select
            value={colorScheme}
            onChange={(e) => setColorScheme(e.target.value as ColorScheme)}
            size="sm"
            w="120px"
          >
            <option value="default">Varsayılan</option>
            <option value="heatmap">Isı Haritası</option>
            <option value="diverging">Ayrışan</option>
          </Select>
        </HStack>

        <Spacer />

        {/* Options */}
        <HStack>
          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="show-values" mb="0" fontSize="sm">
              Değerler
            </FormLabel>
            <Switch
              id="show-values"
              isChecked={showValues}
              onChange={(e) => setShowValues(e.target.checked)}
              size="sm"
            />
          </FormControl>
        </HStack>
      </Flex>

      {/* Filters */}
      <HStack spacing={6}>
        <HStack>
          <Text fontSize="sm" color={textColor}>
            Min Korelasyon:
          </Text>
          <Box w="150px">
            <Slider
              value={minCorrelation}
              onChange={setMinCorrelation}
              min={0}
              max={1}
              step={0.1}
            >
              <SliderTrack>
                <SliderFilledTrack />
              </SliderTrack>
              <SliderThumb />
            </Slider>
          </Box>
          <Text fontSize="sm" color={textColor}>
            {minCorrelation.toFixed(1)}
          </Text>
        </HStack>

        <HStack>
          <Text fontSize="sm" color={textColor}>
            Anlamlılık (p):
          </Text>
          <Box w="150px">
            <Slider
              value={significanceFilter}
              onChange={setSignificanceFilter}
              min={0.01}
              max={0.5}
              step={0.01}
            >
              <SliderTrack>
                <SliderFilledTrack />
              </SliderTrack>
              <SliderThumb />
            </Slider>
          </Box>
          <Text fontSize="sm" color={textColor}>
            {significanceFilter.toFixed(2)}
          </Text>
        </HStack>
      </HStack>

      {/* Current Settings Info */}
      <HStack>
        <Badge colorScheme="blue" variant="subtle">
          {symbols.length}x{symbols.length} Matris
        </Badge>
        <Badge colorScheme="green" variant="subtle">
          {displayMode === 'full' ? 'Tam' : displayMode === 'upper' ? 'Üst' : 'Alt'} Görünüm
        </Badge>
        <Badge colorScheme="purple" variant="subtle">
          {filteredData.filter(item => item.symbol1 !== item.symbol2).length} Korelasyon
        </Badge>
      </HStack>

      {/* Correlation Matrix */}
      <Box 
        overflowX="auto" 
        overflowY="auto" 
        maxH={`${height}px`}
        border="1px solid"
        borderColor={borderColor}
        borderRadius="md"
        bg={bgColor}
      >
        <Grid
          templateColumns={`120px repeat(${symbols.length}, 1fr)`}
          gap={1}
          p={2}
          minW={`${120 + symbols.length * 80}px`}
        >
          {/* Header row */}
          <GridItem />
          {symbols.map((symbol, index) => (
            <GridItem key={index}>
              <Box
                p={2}
                textAlign="center"
                fontSize="xs"
                fontWeight="bold"
                color={textColor}
                transform="rotate(-45deg)"
                transformOrigin="center"
                h="60px"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                {symbol}
              </Box>
            </GridItem>
          ))}

          {/* Data rows */}
          {symbols.map((rowSymbol, rowIndex) => (
            <React.Fragment key={rowSymbol}>
              {/* Row label */}
              <GridItem>
                <Box
                  p={2}
                  fontSize="sm"
                  fontWeight="bold"
                  color={textColor}
                  cursor="pointer"
                  onClick={() => onSymbolClick?.(rowSymbol)}
                  _hover={{ bg: 'blue.50' }}
                  borderRadius="md"
                  textAlign="right"
                  border={selectedSymbols.includes(rowSymbol) ? '2px solid' : '1px solid transparent'}
                  borderColor={selectedSymbols.includes(rowSymbol) ? 'blue.500' : 'transparent'}
                >
                  {rowSymbol}
                </Box>
              </GridItem>

              {/* Data cells */}
              {symbols.map((colSymbol, colIndex) => {
                const shouldShow = shouldShowCell(rowIndex, colIndex);
                const cellData = correlationMatrix[rowSymbol]?.[colSymbol];
                
                if (!shouldShow || !cellData) {
                  return (
                    <GridItem key={`${rowSymbol}-${colIndex}`}>
                      <Box w="70px" h="50px" />
                    </GridItem>
                  );
                }

                return (
                  <GridItem key={`${rowSymbol}-${colIndex}`}>
                    <Tooltip
                      label={
                        <VStack align="start" spacing={1}>
                          <Text fontWeight="bold">{rowSymbol} vs {colSymbol}</Text>
                          <Text>Korelasyon: {cellData.correlation.toFixed(3)}</Text>
                          <Text>P-değeri: {cellData.pValueSignificance.toFixed(3)}</Text>
                          <Text>Ortak İşlem: {cellData.tradeCountOverlap}</Text>
                          <Text>Zaman Örtüşmesi: {cellData.timeOverlapPercent.toFixed(1)}%</Text>
                        </VStack>
                      }
                    >
                      <MotionBox
                        w="70px"
                        h="50px"
                        bg={getCellColor(cellData.correlation, cellData.pValueSignificance)}
                        borderRadius="md"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        cursor="pointer"
                        onClick={() => handleCellClick(rowSymbol, colSymbol)}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        border={
                          (selectedSymbols.includes(rowSymbol) || selectedSymbols.includes(colSymbol)) 
                            ? '2px solid' : '1px solid transparent'
                        }
                        borderColor={
                          (selectedSymbols.includes(rowSymbol) || selectedSymbols.includes(colSymbol))
                            ? 'blue.500' : 'transparent'
                        }
                      >
                        {showValues && (
                          <Text 
                            fontSize="xs" 
                            fontWeight="bold" 
                            color={getTextColor(cellData.correlation, cellData.pValueSignificance)}
                          >
                            {cellData.correlation.toFixed(2)}
                          </Text>
                        )}
                      </MotionBox>
                    </Tooltip>
                  </GridItem>
                );
              })}
            </React.Fragment>
          ))}
        </Grid>
      </Box>

      {/* Statistics Summary */}
      {correlationStats && (
        <HStack justify="space-between" fontSize="sm" color="gray.500">
          <Text>
            Ortalama: {correlationStats.average.toFixed(3)}
          </Text>
          <Text>
            En Yüksek: {correlationStats.maximum.toFixed(3)}
          </Text>
          <Text>
            En Düşük: {correlationStats.minimum.toFixed(3)}
          </Text>
          <Text>
            Güçlü Pozitif: {correlationStats.strongPositive}
          </Text>
          <Text>
            Güçlü Negatif: {correlationStats.strongNegative}
          </Text>
        </HStack>
      )}
    </VStack>
  );
};

export default SymbolCorrelationMatrix;
