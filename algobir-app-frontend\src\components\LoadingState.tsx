import React from 'react';
import {
  Box,
  Spinner,
  Text,
  Center,
  VStack,
  Skeleton,
  SkeletonText,
  SkeletonCircle,
  useColorModeValue,
  Progress,
  HStack,
  Icon
} from '@chakra-ui/react';
import { FiLoader } from 'react-icons/fi';

interface LoadingProps {
  text?: string;
  type?: 'spinner' | 'skeleton' | 'progress';
  height?: string;
  width?: string | number;
  count?: number;
  isFullPage?: boolean;
  progress?: number; // 0-100 arası değer
  showProgress?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'minimal' | 'detailed';
  ariaLabel?: string;
}

/**
 * Gelişmiş yükleme durumu bileşeni - UX optimized
 * Accessibility, responsive design ve performance odaklı
 */
const LoadingState: React.FC<LoadingProps> = ({
  text = 'Yükleniyor...',
  type = 'spinner',
  height = '100px',
  width = '100%',
  count = 3,
  isFullPage = false,
  progress = 0,
  showProgress = false,
  size = 'md',
  variant = 'default',
  ariaLabel
}) => {
  // Color mode values for better theming
  const spinnerColor = useColorModeValue('brand.500', 'brand.300');
  const textColor = useColorModeValue('gray.600', 'gray.300');
  const bgColor = useColorModeValue('white', 'gray.800');
  const skeletonStartColor = useColorModeValue('gray.100', 'gray.700');
  const skeletonEndColor = useColorModeValue('gray.300', 'gray.600');

  // Size configurations
  const sizeConfig = {
    sm: { spinner: 'md', text: 'sm', spacing: 3 },
    md: { spinner: 'lg', text: 'md', spacing: 4 },
    lg: { spinner: 'xl', text: 'lg', spacing: 5 },
    xl: { spinner: '2xl', text: 'xl', spacing: 6 }
  };

  const currentSize = sizeConfig[size];
  // Global stil ekle - Performance optimized
  React.useEffect(() => {
    if (typeof document !== 'undefined' && !document.getElementById('loading-animation-style')) {
      const style = document.createElement('style');
      style.id = 'loading-animation-style';
      style.innerHTML = `
        @keyframes pulseAnimation {
          0% { transform: scale(0.95); opacity: 0.7; }
          50% { transform: scale(1); opacity: 1; }
          100% { transform: scale(0.95); opacity: 0.7; }
        }
        @keyframes fadeInUp {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }
        @media (prefers-reduced-motion: reduce) {
          .loading-animation { animation: none !important; }
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  // Progress bar loading type
  if (type === 'progress') {
    return (
      <Center h={isFullPage ? "70vh" : height} w={width}>
        <VStack
          spacing={currentSize.spacing}
          className="loading-animation"
          style={{ animation: 'fadeInUp 0.3s ease-out' }}
          role="status"
          aria-label={ariaLabel || `Yükleniyor: ${progress}% tamamlandı`}
          aria-live="polite"
        >
          <Icon as={FiLoader} boxSize={8} color={spinnerColor} />
          {text && (
            <Text
              fontWeight="medium"
              color={textColor}
              fontSize={currentSize.text}
              textAlign="center"
            >
              {text}
            </Text>
          )}
          {showProgress && (
            <Box w="200px">
              <Progress
                value={progress}
                colorScheme="brand"
                size="sm"
                borderRadius="full"
                bg={useColorModeValue('gray.100', 'gray.700')}
              />
              <Text
                fontSize="xs"
                color={textColor}
                textAlign="center"
                mt={1}
              >
                {Math.round(progress)}%
              </Text>
            </Box>
          )}
        </VStack>
      </Center>
    );
  }

  // Enhanced spinner with variants
  if (type === 'spinner') {
    const renderSpinnerContent = () => {
      if (variant === 'minimal') {
        return (
          <Spinner
            thickness="3px"
            speed="0.8s"
            emptyColor={useColorModeValue('gray.200', 'gray.600')}
            color={spinnerColor}
            size={currentSize.spinner}
          />
        );
      }

      if (variant === 'detailed') {
        return (
          <VStack spacing={currentSize.spacing}>
            <HStack spacing={3}>
              <Spinner
                thickness="4px"
                speed="0.75s"
                emptyColor={useColorModeValue('gray.200', 'gray.600')}
                color={spinnerColor}
                size={currentSize.spinner}
              />
              <Icon as={FiLoader} boxSize={6} color={spinnerColor} />
            </HStack>
            {text && (
              <Text
                fontWeight="medium"
                color={textColor}
                fontSize={currentSize.text}
                textAlign="center"
              >
                {text}
              </Text>
            )}
            <Text fontSize="xs" color={textColor} opacity={0.7}>
              Lütfen bekleyin...
            </Text>
          </VStack>
        );
      }

      // Default variant
      return (
        <VStack spacing={currentSize.spacing}>
          <Spinner
            thickness="4px"
            speed="0.75s"
            emptyColor={useColorModeValue('gray.200', 'gray.600')}
            color={spinnerColor}
            size={currentSize.spinner}
          />
          {text && (
            <Text
              fontWeight="medium"
              color={textColor}
              fontSize={currentSize.text}
              textAlign="center"
            >
              {text}
            </Text>
          )}
        </VStack>
      );
    };

    return (
      <Center h={isFullPage ? "70vh" : height} w={width}>
        <Box
          className="loading-animation"
          style={{ animation: 'pulseAnimation 2s ease-in-out infinite' }}
          role="status"
          aria-label={ariaLabel || text || 'Yükleniyor'}
          aria-live="polite"
        >
          {renderSpinnerContent()}
        </Box>
      </Center>
    );
  }

  // Enhanced skeleton with accessibility
  const skeletonStyle = {
    p: 5,
    mb: 4,
    borderWidth: "1px",
    borderRadius: "lg",
    boxShadow: "sm",
    bg: bgColor,
    transition: "all 0.2s ease"
  };

  return (
    <Box
      w={width}
      h={height}
      role="status"
      aria-label={ariaLabel || "İçerik yükleniyor"}
      aria-live="polite"
    >
      {Array(count).fill(0).map((_, i) => (
        <Box
          key={i}
          {...skeletonStyle}
          className="loading-animation"
          style={{
            animation: `fadeInUp 0.3s ease-out ${i * 0.1}s both`,
            animationDelay: `${i * 100}ms`
          }}
        >
          <SkeletonCircle
            size="12"
            mb={5}
            startColor={skeletonStartColor}
            endColor={skeletonEndColor}
          />
          <SkeletonText
            mt="2"
            noOfLines={4}
            spacing="4"
            skeletonHeight="3"
            startColor={skeletonStartColor}
            endColor={skeletonEndColor}
          />
        </Box>
      ))}
    </Box>
  );
};

// Kart şeklinde skeleton yükleme durumu
export const CardSkeleton: React.FC<{ count?: number }> = ({ count = 3 }) => {
  const cardSkeletonStyle = {
    p: 6, 
    mb: 5, 
    borderWidth: "1px", 
    borderRadius: "lg", 
    overflow: "hidden",
    boxShadow: "sm",
    bg: "white",
    _dark: { bg: 'gray.800' },
    transition: "all 0.2s"
  };

  return (
    <>
      {Array(count).fill(0).map((_, i) => (
        <Box 
          key={i} 
          {...cardSkeletonStyle}
        >
          <SkeletonText mt="2" noOfLines={1} spacing="4" skeletonHeight="6" mb={6} startColor="gray.100" endColor="gray.300" _dark={{ startColor: 'gray.700', endColor: 'gray.600' }} />
          <Skeleton height="24px" my={3} startColor="gray.100" endColor="gray.300" _dark={{ startColor: 'gray.700', endColor: 'gray.600' }} />
          <Skeleton height="24px" my={3} startColor="gray.100" endColor="gray.300" _dark={{ startColor: 'gray.700', endColor: 'gray.600' }} />
          <Skeleton height="24px" my={3} startColor="gray.100" endColor="gray.300" _dark={{ startColor: 'gray.700', endColor: 'gray.600' }} />
        </Box>
      ))}
    </>
  );
};

// Tablo skeleton yükleme durumu
export const TableSkeleton: React.FC<{ rows?: number, columns?: number }> = ({ rows = 5, columns = 4 }) => {
  return (
    <Box borderWidth="1px" borderRadius="lg" overflow="hidden" boxShadow="sm" bg="white" _dark={{ bg: 'gray.800' }}>
      <Box p={4} borderBottomWidth="1px" bg="gray.50" _dark={{ bg: 'gray.700' }}>
        <Skeleton height="24px" width="full" startColor="gray.100" endColor="gray.300" _dark={{ startColor: 'gray.700', endColor: 'gray.600' }} />
      </Box>
      {Array(rows).fill(0).map((_, i) => (
        <Box key={i} p={4} borderBottomWidth={i === rows - 1 ? "0" : "1px"}>
          <Skeleton height="20px" mb={3} startColor="gray.100" endColor="gray.300" _dark={{ startColor: 'gray.700', endColor: 'gray.600' }} />
          <Box display="flex" mt={3} justifyContent="space-between">
            {Array(columns).fill(0).map((_, j) => (
              <Skeleton key={j} height="12px" width={`${90 / columns}%`} startColor="gray.100" endColor="gray.300" _dark={{ startColor: 'gray.700', endColor: 'gray.600' }} />
            ))}
          </Box>
        </Box>
      ))}
    </Box>
  );
};

// Dashboard özet kartları için skeleton
export const StatsSkeleton: React.FC<{ count?: number }> = ({ count = 4 }) => {
  const statsSkeletonStyle = {
    p: 6, 
    borderWidth: "1px", 
    borderRadius: "lg", 
    width: { base: "100%", md: "calc(50% - 12px)", lg: "calc(25% - 18px)" },
    boxShadow: "sm",
    bg: "white",
    _dark: { bg: 'gray.800' }
  };

  return (
    <Box display="flex" flexWrap="wrap" gap={6}>
      {Array(count).fill(0).map((_, i) => (
        <Box 
          key={i} 
          {...statsSkeletonStyle}
        >
          <SkeletonText noOfLines={1} spacing="4" skeletonHeight="5" mb={4} startColor="gray.100" endColor="gray.300" _dark={{ startColor: 'gray.700', endColor: 'gray.600' }} />
          <Skeleton height="48px" mt={5} startColor="gray.100" endColor="gray.300" _dark={{ startColor: 'gray.700', endColor: 'gray.600' }} />
        </Box>
      ))}
    </Box>
  );
};

export default LoadingState; 