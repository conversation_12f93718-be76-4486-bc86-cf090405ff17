// @ts-nocheck - Bu dosya kompleks union type hatalarından dolayı tiplemesi devre dışı bırakıldı
import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  Box, Heading, Spinner, Table, Thead, Tbody, Tr, Th, Td, TableCaption, TableContainer,
  HStack, Input, Select, Button, Icon, useToast, Text,
  Badge, Flex, IconButton, AlertDialog, AlertDialogBody, AlertDialogFooter, 
  AlertDialogHeader, AlertDialogContent, AlertDialogOverlay, useDisclosure,
  Modal, ModalOverlay, ModalContent, ModalHeader, ModalFooter, ModalBody,
  ModalCloseButton, FormControl, FormLabel, NumberInput, NumberInputField,
  FormErrorMessage, FormHelperText, useColorModeValue, InputGroup, InputLeftElement,
  Alert, AlertIcon
} from '@chakra-ui/react';
import { TriangleUpIcon, TriangleDownIcon, DeleteIcon, AddIcon, SearchIcon } from '@chakra-ui/icons';
import { FaFileExcel, FaFilter, FaExclamationCircle, FaAngleDoubleLeft, FaAngleLeft, FaAngleRight, FaAngleDoubleRight } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../supabaseClient';
import * as XLSX from 'xlsx';

// Custom Components
import Card from '../components/card/Card';
import TradeTable from '../components/tables/TradeTable';
import ManualTradeForm from '../components/trades/ManualTradeForm';

// Custom Hooks ve Components
import { useTradesData, TradeFilters } from '../hooks/useTradesData';
import TradesTable from '../components/trades/TradesTable';

// İşlem tipi
interface Trade {
  id: number;
  user_id: string;
  received_at: string;
  signal_name: string;
  symbol: string;
  category: string;
  price: number | null;
  calculated_quantity: number | null;
  forwarded: boolean;
  order_side: string;
  name?: string; // Tradingview'den gelen orijinal isim alanı
  system_name: string | null; // Yeni eklenen sistem adı alanı
  signal_type: string | null; // Yeni eklenen sinyal tipi alanı
  trade_category: string | null; // Alım/Satım kategorisi
  pnl: number | null; // Kar/Zarar (Profit/Loss) değeri - DAIMA number veya null olmalı
  position_status: string | null; // Pozisyon durumu: 'Açık', 'Kapandı', 'Bakiye Yetersiz', 'Hatalı'
  position_id: string | null; // Pozisyon ID
  closing_trade_id: string | null; // Kapanış işleminin ID'si
  is_deleted: boolean; // Soft delete durumu
  created_at: string; // Oluşturulma tarihi
  status?: 'buy' | 'tp' | 'sl' | 'unknown' | 'cancelled_by_user'; // İşlem durumu - cancelled_by_user eklendi
}

// Filtreleme özelliklerini ayrı bir tip olarak tanımla
type TradeKey = 'received_at' | 'price' | 'calculated_quantity' | 'pnl' | 'symbol' | 'trade_category' | 'system_name' | 'signal_type';

// Sıralama konfigürasyonu için tip
interface SortConfig {
  key: string;
  direction: "ascending" | "descending";
}

// Açık pozisyon tipini tanımla
interface OpenPosition {
  id: string;
  symbol: string;
  price: number;
  quantity: number;
  receivedDate: string; // Alış tarihi için yeni alan
}

// İşlemleri filtrele ve sırala
const getFilteredAndSortedTrades = (
  trades: Trade[],
  symbolFilter: string,
  categoryFilter: string,
  sort: SortConfig
): Trade[] => {
  // Filtreleme işlemleri
  let result = [...trades];

  // Symbol filtresi uygula - kısmi eşleşme ve büyük/küçük harf duyarsız
  if (symbolFilter && symbolFilter.trim() !== "") {
    const normalizedFilter = symbolFilter.trim().toLowerCase();
    result = result.filter((trade) => 
      trade.symbol && trade.symbol.toLowerCase().includes(normalizedFilter)
    );
  }

  // Kategori filtresi uygula
  if (categoryFilter && categoryFilter !== "Tümü") {
    result = result.filter((trade) => trade.trade_category === categoryFilter);
  }

  // Sıralama uygula
  if (sort.key) {
    result.sort((a, b) => {
      const aValue = a[sort.key as keyof Trade];
      const bValue = b[sort.key as keyof Trade];

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      // String karşılaştırması
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sort.direction === "ascending"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      // Sayı veya tarih karşılaştırması
      // @ts-ignore: Tip karşılaştırması kontrolü yapıldı
      return sort.direction === "ascending" ? aValue - bValue : bValue - aValue;
    });
  }

  return result;
};

// Normal fonksiyon olarak tanımla, React.FC yerine
function Trades() {
  // Theme renkler
  const textColor = useColorModeValue('navy.700', 'white');
  const secondaryTextColor = useColorModeValue('secondaryGray.600', 'white');
  const brandColor = useColorModeValue('brand.500', 'brand.400');
  const bgCard = useColorModeValue('white', 'navy.700');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');
  const inputBg = useColorModeValue('secondaryGray.300', 'navy.900');
  const placeholderColor = useColorModeValue('secondaryGray.600', 'whiteAlpha.600');
  const shadow = useColorModeValue('0px 18px 40px rgba(112, 144, 176, 0.12)', 'none');
  
  // Auth context'ten kullanıcı bilgisini al
  const { user } = useAuth();
  const toast = useToast();
  
  // Filtreleme için state'ler
  const [symbolFilter, setSymbolFilter] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  
  // Filtreleri trade filtrelerine dönüştür
  const tradeFilters = useMemo(() => {
    const filters: TradeFilters = {};
    
    // Symbol filtresi için artık backend'de ilike kullanıyoruz
    if (symbolFilter && symbolFilter.trim() !== "") {
      filters.symbol = symbolFilter.trim();
    }
    
    // Kategori filtresi - backend'de order_side olarak arayacağız
    if (categoryFilter && categoryFilter !== "") {
      // Dropdown'dan gelen "Alım" ve "Satım" değerlerini BUY/SELL'e çevir
      if (categoryFilter === "Alım") {
        filters.order_side = "BUY";
      } else if (categoryFilter === "Satım") {
        filters.order_side = "SELL";
      }
    }
    
    return filters;
  }, [categoryFilter, symbolFilter]); // Artık symbolFilter'ı da dahil ediyoruz çünkü backend'de kullanıyoruz
  
  // Özel hook ile işlem verilerini al (şimdi sayfalama bilgilerini de içeriyor)
  const { 
    trades, 
    isLoading, 
    error, 
    refetch, 
    handleDeleteTrade,
    handleCancelSignalBatch,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    totalPages,
    totalTrades,
    isDeletingTradeId,
    isCancellingSignalId
  } = useTradesData(tradeFilters);
  
  // Kategori değiştiğinde sayfa numarasını sıfırla
  const handleCategoryChange = (e) => {
    setCategoryFilter(e.target.value);
    setCurrentPage(1); // İlk sayfaya dön
  };

  // Sembol filtresinde değişiklik olduğunda
  const handleSymbolFilterChange = (e) => {
    setSymbolFilter(e.target.value);
    setCurrentPage(1); // İlk sayfaya dön
  };
  
  // Modal kontrolü
  const { isOpen: isAddModalOpen, onOpen: onAddModalOpen, onClose: onAddModalClose } = useDisclosure();

  // Excel'e aktarma
  const handleExportExcel = () => {
    if (trades.length === 0) {
      toast({
        title: 'Dışa Aktarım Hatası',
        description: 'Dışa aktarılacak işlem bulunamadı.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    
    try {
      // Excel için verileri hazırla
      const excelData = trades.map((trade) => ({
        'İşlem Zamanı': new Date(trade.received_at).toLocaleString('tr-TR'),
        'Sistem Adı': trade.system_name || '-',
        'Sinyal Tipi': trade.signal_type || '-',
        'Sembol': trade.symbol,
        'Kategori': trade.trade_category,
        'Fiyat': trade.price ? formatCurrency(trade.price) : '-',
        'Miktar': trade.calculated_quantity ? Math.floor(trade.calculated_quantity) : '-',
        'İşlem Hacmi (TL)': trade.price && trade.calculated_quantity 
          ? formatCurrency(trade.price * trade.calculated_quantity) 
          : '-',
        'Kar/Zarar': trade.pnl != null ? formatCurrency(trade.pnl) : 
                    (trade.trade_category === 'Alım' ? 'İşlem Açıldı' : '-')
      }));
      
      // Çalışma sayfası oluştur
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      
      // Çalışma kitabı oluştur
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'İşlemler');
      
      // Excel dosyasını indir
      XLSX.writeFile(workbook, 'algobir_islemler.xlsx');
      
      toast({
        title: 'Dışa Aktarım Başarılı',
        description: 'İşlemler Excel dosyasına aktarıldı.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (err) {
      console.error('Excel dışa aktarımı başarısız:', err);
      toast({
        title: 'Dışa Aktarım Hatası',
        description: err.message || 'Excel dosyası oluşturulamadı.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Para formatlaması
  const formatCurrency = (value) => {
    if (value === null || value === undefined) return '-';
    
    try {
      return new Intl.NumberFormat('tr-TR', { 
        style: 'currency', 
        currency: 'TRY',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
    } catch (err) {
      console.error('Para formatı hatası:', err, 'Değer:', value);
      return value.toFixed(2) + ' ₺'; // Fallback
    }
  };

  // Yeni işlem ekleme başarılı olduğunda çağrılacak fonksiyon
  const handleAddTradeSuccess = () => {
    refetch(); // İşlem listesini yenile
  };

  return (
    <Box>
      {/* Başlık ve üst kart */}
      <Box mb="20px">
        <Flex direction="column" w="100%">
          <Flex
            mb="20px"
            direction={{ base: 'column', md: 'row' }}
            justify={{ base: 'center', md: 'space-between' }}
            align={{ base: 'start', md: 'center' }}
            w="100%"
          >
            <Heading as="h1" color={textColor} fontSize="2xl" fontWeight="700">
              İşlemler
            </Heading>
            <Flex
              direction={{ base: 'column', md: 'row' }}
              mt={{ base: '20px', md: '0px' }}
              align={{ base: 'start', md: 'center' }}
              justify={{ base: 'start', md: 'end' }}
              gap={4}
            >
              <Button
                leftIcon={<FaFileExcel />}
                variant="outline"
                colorScheme="brand"
                fontWeight="500"
                onClick={handleExportExcel}
                size="md"
                px={6}
                fontSize="sm"
                borderRadius="12px"
                h="38px"
              >
                Excel'e Aktar
              </Button>
              
              <Button
                leftIcon={<AddIcon />}
                colorScheme="brand"
                fontWeight="500"
                onClick={onAddModalOpen}
                size="md"
                px={6}
                fontSize="sm"
                borderRadius="12px"
                h="38px"
                boxShadow="0px 4px 10px rgba(66, 42, 251, 0.3)"
              >
                Yeni İşlem Ekle
              </Button>
            </Flex>
          </Flex>

          {/* Filtreleme Kartı */}
          <Card 
            mb="20px" 
            p={{ base: "16px", md: "24px" }}
            boxShadow="0px 18px 40px rgba(112, 144, 176, 0.12)"
          >
            <Flex 
              direction={{ base: 'column', lg: 'row' }} 
              gap={{ base: 4, md: 6 }} 
              w="100%"
              align={{ base: 'flex-start', lg: 'center' }}
            >
              <InputGroup maxW={{ lg: '320px' }} size="md">
                <InputLeftElement pointerEvents="none" h="40px" pl="2">
                  <SearchIcon color={brandColor} />
                </InputLeftElement>
                <Input
                  placeholder="Sembole göre filtrele"
                  value={symbolFilter}
                  onChange={handleSymbolFilterChange}
                  fontSize="sm"
                  fontWeight="500"
                  variant="filled"
                  bg={inputBg}
                  _placeholder={{ color: placeholderColor }}
                  borderRadius="12px"
                  h="40px"
                  _focus={{
                    borderColor: 'brand.500',
                    bg: inputBg
                  }}
                />
              </InputGroup>
              
              <Select
                placeholder="Tümü"
                value={categoryFilter}
                onChange={handleCategoryChange}
                fontSize="sm"
                fontWeight="500"
                variant="filled"
                bg={inputBg}
                borderRadius="12px"
                _placeholder={{ color: placeholderColor }}
                maxW={{ lg: '200px' }}
                h="40px"
                _focus={{
                  borderColor: 'brand.500',
                  bg: inputBg
                }}
              >
                <option value="Alım">Alım</option>
                <option value="Satım">Satım</option>
              </Select>

              <Text 
                color={secondaryTextColor} 
                fontSize="sm" 
                fontStyle="italic" 
                display={{ base: 'none', lg: 'block' }}
                ml="auto"
              >
                {isLoading ? 'Yükleniyor...' : `${totalTrades} işlemden ${(currentPage - 1) * pageSize + 1}-${Math.min(currentPage * pageSize, totalTrades)} arası gösteriliyor`}
              </Text>
            </Flex>
          </Card>

          {/* İşlemler ana içerik kartı */}
          <Card
            flexDirection='column'
            w='100%'
            p='0px'
            overflowX={{ sm: 'scroll', lg: 'hidden' }}
            boxShadow={shadow}
          >
            {isLoading ? (
              <Flex justify="center" align="center" py={10}>
                <Spinner thickness='4px' speed='0.65s' size='xl' color='brand.500' />
                <Text ml={4} color={textColor} fontWeight="500">İşlemler yükleniyor...</Text>
              </Flex>
            ) : error ? (
              <Alert status="error" borderRadius="12px" m={4}>
                <AlertIcon />
                {error.message || 'İşlemler yüklenirken bir hata oluştu.'}
              </Alert>
            ) : trades.length === 0 ? (
              <Alert status="info" borderRadius="12px" m={4}>
                <AlertIcon />
                {symbolFilter || categoryFilter ? 'Filtrelenen sonuç bulunamadı.' : 'Hiç işlem kaydı bulunamadı.'}
              </Alert>
            ) : (
              <TradesTable 
                trades={trades} 
                isLoading={isLoading}
                onDeleteClick={handleDeleteTrade}
                onCancelSignalBatch={handleCancelSignalBatch}
                currentUserId={user?.id}
                isDeletingTradeId={isDeletingTradeId}
                isCancellingSignalId={isCancellingSignalId}
              />
            )}
          </Card>

          {/* Sayfalama Kontrolleri */}
          {!isLoading && (
            <Flex 
              justifyContent="center" 
              alignItems="center" 
              mt={6} 
              mb={4} 
              gap={{ base: 2, md: 4 }}
              direction={{ base: 'column', sm: 'row' }}
            >
              <HStack spacing={{ base: 1, md: 2 }}>
                <IconButton
                  aria-label="İlk sayfa"
                  icon={<FaAngleDoubleLeft />}
                  onClick={() => setCurrentPage(1)}
                  isDisabled={currentPage === 1 || totalPages === 0}
                  size={{ base: 'xs', md: 'sm' }}
                  variant="outline"
                  colorScheme="brand"
                  minW={{ base: '32px', md: '40px' }}
                  h={{ base: '32px', md: '40px' }}
                />
                <IconButton
                  aria-label="Önceki sayfa"
                  icon={<FaAngleLeft />}
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  isDisabled={currentPage === 1 || totalPages === 0}
                  size={{ base: 'xs', md: 'sm' }}
                  variant="outline"
                  colorScheme="brand"
                  minW={{ base: '32px', md: '40px' }}
                  h={{ base: '32px', md: '40px' }}
                />
                
                {/* Sayfa numaraları */}
                <HStack spacing={{ base: 0.5, md: 1 }}>
                  {Array.from({ length: Math.min(5, totalPages) }).map((_, idx) => {
                    // 5'ten fazla sayfa varsa merkez etrafında göster
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = idx + 1;
                    } else {
                      const startPage = Math.max(1, Math.min(currentPage - 2, totalPages - 4));
                      pageNum = startPage + idx;
                    }

                    return (
                      <Button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        colorScheme="brand"
                        variant={currentPage === pageNum ? "solid" : "outline"}
                        size={{ base: 'xs', md: 'sm' }}
                        fontWeight="500"
                        minW={{ base: '32px', md: '40px' }}
                        h={{ base: '32px', md: '40px' }}
                        fontSize={{ base: '12px', md: '14px' }}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </HStack>
                
                <IconButton
                  aria-label="Sonraki sayfa"
                  icon={<FaAngleRight />}
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  isDisabled={currentPage === totalPages || totalPages === 0}
                  size={{ base: 'xs', md: 'sm' }}
                  variant="outline"
                  colorScheme="brand"
                  minW={{ base: '32px', md: '40px' }}
                  h={{ base: '32px', md: '40px' }}
                />
                <IconButton
                  aria-label="Son sayfa"
                  icon={<FaAngleDoubleRight />}
                  onClick={() => setCurrentPage(totalPages)}
                  isDisabled={currentPage === totalPages || totalPages === 0}
                  size={{ base: 'xs', md: 'sm' }}
                  variant="outline"
                  colorScheme="brand"
                  minW={{ base: '32px', md: '40px' }}
                  h={{ base: '32px', md: '40px' }}
                />
              </HStack>
              
              {totalTrades > 0 && (
                <Text 
                  color={secondaryTextColor} 
                  fontSize="sm" 
                  fontStyle="italic"
                >
                  {`${totalTrades} işlemden ${(currentPage - 1) * pageSize + 1}-${Math.min(currentPage * pageSize, totalTrades)} arası gösteriliyor`}
                </Text>
              )}
            </Flex>
          )}
        </Flex>
      </Box>

      {/* Yeni İşlem Ekleme Modal - ManualTradeForm bileşenini kullanıyoruz */}
      <ManualTradeForm 
        isOpen={isAddModalOpen} 
        onClose={onAddModalClose} 
        onSuccess={handleAddTradeSuccess}
      />
    </Box>
  );
}

export default Trades; 