import React from 'react';
import {
  Box,
  VStack,
  Button,
  Text,
  Alert,
  AlertIcon,
  useToast
} from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { setTestAuthInLocalStorage, clearTestAuth } from '../utils/testAuth';

/**
 * TestAuth Component
 * 
 * This component is for development/testing purposes only.
 * It allows developers to quickly set up test authentication
 * to test protected routes like statistics pages.
 */
const TestAuth: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();

  const handleSetTestAuth = () => {
    try {
      setTestAuthInLocalStorage();
      toast({
        title: 'Test Authentication Set',
        description: 'You can now access protected routes. Refreshing page...',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
      // Refresh the page to apply authentication
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to set test authentication',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleClearAuth = () => {
    try {
      clearTestAuth();
      toast({
        title: 'Authentication Cleared',
        description: 'Test authentication has been removed. Refreshing page...',
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
      
      // Refresh the page to apply changes
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to clear authentication',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleTestStatistics = () => {
    navigate('/statistics/overview');
  };

  return (
    <Box p={8} maxW="600px" mx="auto">
      <VStack spacing={6} align="stretch">
        <Text fontSize="2xl" fontWeight="bold" textAlign="center">
          Test Authentication Helper
        </Text>
        
        <Alert status="warning">
          <AlertIcon />
          This page is for development and testing purposes only. 
          It allows you to set up test authentication to access protected routes.
        </Alert>

        <VStack spacing={4}>
          <Button 
            colorScheme="green" 
            size="lg" 
            width="full"
            onClick={handleSetTestAuth}
          >
            Set Test Authentication
          </Button>
          
          <Button 
            colorScheme="blue" 
            size="lg" 
            width="full"
            onClick={handleTestStatistics}
          >
            Test Statistics Navigation
          </Button>
          
          <Button 
            colorScheme="red" 
            size="lg" 
            width="full"
            onClick={handleClearAuth}
          >
            Clear Authentication
          </Button>
        </VStack>

        <Box p={4} bg="gray.50" borderRadius="md">
          <Text fontSize="sm" color="gray.600">
            <strong>Instructions:</strong>
            <br />
            1. Click "Set Test Authentication" to enable access to protected routes
            <br />
            2. Click "Test Statistics Navigation" to navigate to statistics pages
            <br />
            3. Use "Clear Authentication" to test unauthenticated behavior
          </Text>
        </Box>
      </VStack>
    </Box>
  );
};

export default TestAuth;
