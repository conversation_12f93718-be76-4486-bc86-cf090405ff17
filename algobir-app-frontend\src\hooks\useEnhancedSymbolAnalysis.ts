import { useState, useCallback } from 'react';
import { Trade } from '../types/trade';
import { 
  SymbolMetrics, 
  SymbolTimePerformance, 
  SymbolTradingFrequency,

  SymbolCorrelation,
  EnhancedSymbolAnalysisData,

  SymbolAlert,
  SymbolInsight
} from '../types/symbolAnalysis';

export const useEnhancedSymbolAnalysis = (_trades: Trade[]) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Calculate comprehensive symbol metrics
  const calculateSymbolMetrics = useCallback((trades: Trade[]): SymbolMetrics[] => {
    const symbolGroups: { [symbol: string]: Trade[] } = {};
    
    // Group trades by symbol
    trades.forEach(trade => {
      if (trade.symbol && trade.position_status === 'Kapalı' && trade.pnl !== null) {
        if (!symbolGroups[trade.symbol]) {
          symbolGroups[trade.symbol] = [];
        }
        symbolGroups[trade.symbol].push(trade);
      }
    });

    return Object.entries(symbolGroups).map(([symbol, symbolTrades]) => {
      const totalTrades = symbolTrades.length;
      const winningTrades = symbolTrades.filter(t => t.pnl! > 0);
      const losingTrades = symbolTrades.filter(t => t.pnl! <= 0);
      
      const totalPnl = symbolTrades.reduce((sum, t) => sum + t.pnl!, 0);
      const totalInvestment = symbolTrades.reduce((sum, t) => sum + ((t.calculated_quantity || 0) * (t.price || 0)), 0);
      
      const winRate = totalTrades > 0 ? (winningTrades.length / totalTrades) * 100 : 0;
      const roi = totalInvestment > 0 ? (totalPnl / totalInvestment) * 100 : 0;
      
      const averageTradeSize = totalInvestment / Math.max(1, totalTrades);
      const averagePnl = totalPnl / Math.max(1, totalTrades);
      
      const averageWin = winningTrades.length > 0 ? 
        winningTrades.reduce((sum, t) => sum + t.pnl!, 0) / winningTrades.length : 0;
      const averageLoss = losingTrades.length > 0 ? 
        Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl!, 0) / losingTrades.length) : 0;
      
      const profitFactor = averageLoss > 0 ? Math.abs(averageWin / averageLoss) : 0;
      
      const bestTrade = symbolTrades.length > 0 ? Math.max(...symbolTrades.map(t => t.pnl!)) : 0;
      const worstTrade = symbolTrades.length > 0 ? Math.min(...symbolTrades.map(t => t.pnl!)) : 0;
      
      // Calculate volatility (standard deviation of returns)
      const returns = symbolTrades.map(t => t.pnl!);
      const meanReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
      const variance = returns.reduce((sum, r) => sum + Math.pow(r - meanReturn, 2), 0) / returns.length;
      const volatility = Math.sqrt(variance);
      
      // Calculate Sharpe ratio (assuming 2% risk-free rate)
      const riskFreeRate = 2;
      const excessReturn = roi - riskFreeRate;
      const sharpeRatio = volatility > 0 ? excessReturn / volatility : 0;
      
      // Calculate maximum drawdown
      let maxDrawdown = 0;
      let maxDrawdownPercent = 0;
      let peak = 0;
      let cumulativePnl = 0;
      
      symbolTrades.forEach(trade => {
        cumulativePnl += trade.pnl!;
        if (cumulativePnl > peak) {
          peak = cumulativePnl;
        }
        const drawdown = peak - cumulativePnl;
        if (drawdown > maxDrawdown) {
          maxDrawdown = drawdown;
          maxDrawdownPercent = peak > 0 ? (drawdown / peak) * 100 : 0;
        }
      });
      
      // Calculate consecutive wins/losses
      let consecutiveWins = 0;
      let consecutiveLosses = 0;
      let currentWinStreak = 0;
      let currentLossStreak = 0;
      let maxWinStreak = 0;
      let maxLossStreak = 0;
      
      symbolTrades.forEach(trade => {
        if (trade.pnl! > 0) {
          currentWinStreak++;
          currentLossStreak = 0;
          maxWinStreak = Math.max(maxWinStreak, currentWinStreak);
        } else {
          currentLossStreak++;
          currentWinStreak = 0;
          maxLossStreak = Math.max(maxLossStreak, currentLossStreak);
        }
      });
      
      consecutiveWins = maxWinStreak;
      consecutiveLosses = maxLossStreak;
      
      // Calculate time-based metrics
      const sortedTrades = symbolTrades.sort((a, b) => 
        new Date(a.pnl_calculated_at!).getTime() - new Date(b.pnl_calculated_at!).getTime()
      );
      
      const firstTradeDate = sortedTrades[0]?.pnl_calculated_at || '';
      const lastTradeDate = sortedTrades[sortedTrades.length - 1]?.pnl_calculated_at || '';
      
      const tradingDays = firstTradeDate && lastTradeDate ? 
        Math.ceil((new Date(lastTradeDate).getTime() - new Date(firstTradeDate).getTime()) / (1000 * 60 * 60 * 24)) : 0;
      
      const tradesPerDay = tradingDays > 0 ? totalTrades / tradingDays : 0;
      const tradesPerWeek = tradesPerDay * 7;
      const tradesPerMonth = tradesPerDay * 30;
      
      return {
        symbol,
        totalTrades,
        winningTrades: winningTrades.length,
        losingTrades: losingTrades.length,
        winRate,
        totalPnl,
        totalInvestment,
        roi,
        averageTradeSize,
        averagePnl,
        volatility,
        sharpeRatio,
        maxDrawdown,
        maxDrawdownPercent,
        bestTrade,
        worstTrade,
        averageWin,
        averageLoss,
        profitFactor,
        consecutiveWins,
        consecutiveLosses,
        winStreak: maxWinStreak,
        lossStreak: maxLossStreak,
        averageHoldingTime: 0, // Will be calculated if we have entry/exit times
        firstTradeDate,
        lastTradeDate,
        tradingDays,
        tradesPerDay,
        tradesPerWeek,
        tradesPerMonth
      };
    }).sort((a, b) => b.totalPnl - a.totalPnl);
  }, []);

  // Calculate symbol time performance
  const calculateSymbolTimePerformance = useCallback((trades: Trade[]): SymbolTimePerformance[] => {
    const symbolGroups: { [symbol: string]: Trade[] } = {};
    
    trades.forEach(trade => {
      if (trade.symbol && trade.position_status === 'Kapalı' && trade.pnl !== null) {
        if (!symbolGroups[trade.symbol]) {
          symbolGroups[trade.symbol] = [];
        }
        symbolGroups[trade.symbol].push(trade);
      }
    });

    return Object.entries(symbolGroups).map(([symbol, symbolTrades]) => {
      // Hourly performance
      const hourlyData: { [hour: number]: { pnl: number; trades: number; wins: number; totalInvestment: number } } = {};
      for (let hour = 0; hour < 24; hour++) {
        hourlyData[hour] = { pnl: 0, trades: 0, wins: 0, totalInvestment: 0 };
      }

      // Daily performance
      const dailyData: { [day: number]: { pnl: number; trades: number; wins: number; totalInvestment: number } } = {};
      for (let day = 0; day < 7; day++) {
        dailyData[day] = { pnl: 0, trades: 0, wins: 0, totalInvestment: 0 };
      }

      // Monthly performance
      const monthlyData: { [month: string]: { pnl: number; trades: number; wins: number; investment: number } } = {};

      symbolTrades.forEach(trade => {
        const date = new Date(trade.pnl_calculated_at!);
        const hour = date.getHours();
        const dayOfWeek = date.getDay();
        const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        const investment = (trade.calculated_quantity || 0) * (trade.price || 0);

        // Hourly
        hourlyData[hour].pnl += trade.pnl!;
        hourlyData[hour].trades += 1;
        hourlyData[hour].totalInvestment += investment;
        if (trade.pnl! > 0) hourlyData[hour].wins += 1;

        // Daily
        dailyData[dayOfWeek].pnl += trade.pnl!;
        dailyData[dayOfWeek].trades += 1;
        dailyData[dayOfWeek].totalInvestment += investment;
        if (trade.pnl! > 0) dailyData[dayOfWeek].wins += 1;

        // Monthly
        if (!monthlyData[month]) {
          monthlyData[month] = { pnl: 0, trades: 0, wins: 0, investment: 0 };
        }
        monthlyData[month].pnl += trade.pnl!;
        monthlyData[month].trades += 1;
        monthlyData[month].investment += investment;
        if (trade.pnl! > 0) monthlyData[month].wins += 1;
      });

      const dayNames = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];

      return {
        symbol,
        hourlyPerformance: Object.entries(hourlyData).map(([hour, data]) => ({
          hour: parseInt(hour),
          pnl: data.pnl,
          trades: data.trades,
          winRate: data.trades > 0 ? (data.wins / data.trades) * 100 : 0,
          avgTradeSize: data.trades > 0 ? data.totalInvestment / data.trades : 0
        })),
        dailyPerformance: Object.entries(dailyData).map(([day, data]) => ({
          dayOfWeek: parseInt(day),
          dayName: dayNames[parseInt(day)],
          pnl: data.pnl,
          trades: data.trades,
          winRate: data.trades > 0 ? (data.wins / data.trades) * 100 : 0,
          avgTradeSize: data.trades > 0 ? data.totalInvestment / data.trades : 0
        })),
        monthlyPerformance: Object.entries(monthlyData).map(([month, data]) => ({
          month,
          pnl: data.pnl,
          trades: data.trades,
          winRate: data.trades > 0 ? (data.wins / data.trades) * 100 : 0,
          roi: data.investment > 0 ? (data.pnl / data.investment) * 100 : 0,
          investment: data.investment
        })).sort((a, b) => a.month.localeCompare(b.month)),
        weeklyPerformance: [] // Will be implemented if needed
      };
    });
  }, []);

  // Calculate symbol correlation matrix
  const calculateSymbolCorrelation = useCallback((trades: Trade[]): SymbolCorrelation[] => {
    const symbolGroups: { [symbol: string]: Trade[] } = {};

    // Group trades by symbol
    trades.forEach(trade => {
      if (trade.symbol && trade.position_status === 'Kapalı' && trade.pnl !== null) {
        if (!symbolGroups[trade.symbol]) {
          symbolGroups[trade.symbol] = [];
        }
        symbolGroups[trade.symbol].push(trade);
      }
    });

    const symbols = Object.keys(symbolGroups);
    const correlations: SymbolCorrelation[] = [];

    // Calculate correlation between each pair of symbols
    for (let i = 0; i < symbols.length; i++) {
      for (let j = i; j < symbols.length; j++) {
        const symbol1 = symbols[i];
        const symbol2 = symbols[j];

        if (symbol1 === symbol2) {
          // Self-correlation is always 1
          correlations.push({
            symbol1,
            symbol2,
            correlation: 1.0,
            pValueSignificance: 0,
            tradeCountOverlap: symbolGroups[symbol1].length,
            timeOverlapPercent: 100
          });
          continue;
        }

        const trades1 = symbolGroups[symbol1];
        const trades2 = symbolGroups[symbol2];

        // Get daily returns for both symbols
        const dailyReturns1 = getDailyReturns(trades1);
        const dailyReturns2 = getDailyReturns(trades2);

        // Find overlapping dates
        const commonDates = Object.keys(dailyReturns1).filter(date =>
          dailyReturns2.hasOwnProperty(date)
        );

        if (commonDates.length < 2) {
          // Not enough data for correlation
          correlations.push({
            symbol1,
            symbol2,
            correlation: 0,
            pValueSignificance: 1,
            tradeCountOverlap: 0,
            timeOverlapPercent: 0
          });
          continue;
        }

        // Calculate Pearson correlation coefficient
        const returns1 = commonDates.map(date => dailyReturns1[date]);
        const returns2 = commonDates.map(date => dailyReturns2[date]);

        const correlation = calculatePearsonCorrelation(returns1, returns2);
        const tradeCountOverlap = commonDates.length;
        const totalUniqueDates = new Set([
          ...Object.keys(dailyReturns1),
          ...Object.keys(dailyReturns2)
        ]).size;
        const timeOverlapPercent = totalUniqueDates > 0 ? (commonDates.length / totalUniqueDates) * 100 : 0;

        correlations.push({
          symbol1,
          symbol2,
          correlation,
          pValueSignificance: calculatePValue(correlation, commonDates.length),
          tradeCountOverlap,
          timeOverlapPercent
        });
      }
    }

    return correlations;
  }, []);

  // Helper function to calculate daily returns
  const getDailyReturns = (trades: Trade[]): { [date: string]: number } => {
    const dailyPnl: { [date: string]: number } = {};

    trades.forEach(trade => {
      const date = new Date(trade.pnl_calculated_at!).toISOString().split('T')[0];
      if (!dailyPnl[date]) {
        dailyPnl[date] = 0;
      }
      dailyPnl[date] += trade.pnl!;
    });

    return dailyPnl;
  };

  // Helper function to calculate Pearson correlation coefficient
  const calculatePearsonCorrelation = (x: number[], y: number[]): number => {
    if (x.length !== y.length || x.length === 0) return 0;

    const n = x.length;
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumX2 = x.reduce((sum, val) => sum + val * val, 0);
    const sumY2 = y.reduce((sum, val) => sum + val * val, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

    if (denominator === 0) return 0;
    return numerator / denominator;
  };

  // Helper function to calculate p-value (simplified)
  const calculatePValue = (correlation: number, sampleSize: number): number => {
    if (sampleSize < 3) return 1;

    // Simplified p-value calculation using t-distribution approximation
    const t = Math.abs(correlation) * Math.sqrt((sampleSize - 2) / (1 - correlation * correlation));

    // Very simplified p-value approximation
    if (t > 2.576) return 0.01;  // 99% confidence
    if (t > 1.96) return 0.05;   // 95% confidence
    if (t > 1.645) return 0.1;   // 90% confidence
    return 0.5; // Not significant
  };

  // Calculate symbol trading frequency
  const calculateSymbolTradingFrequency = useCallback((trades: Trade[]): SymbolTradingFrequency[] => {
    const symbolGroups: { [symbol: string]: Trade[] } = {};
    
    trades.forEach(trade => {
      if (trade.symbol && trade.position_status === 'Kapalı' && trade.pnl !== null) {
        if (!symbolGroups[trade.symbol]) {
          symbolGroups[trade.symbol] = [];
        }
        symbolGroups[trade.symbol].push(trade);
      }
    });

    return Object.entries(symbolGroups).map(([symbol, symbolTrades]) => {
      const dailyFrequency: { [date: string]: { tradeCount: number; volume: number; pnl: number } } = {};
      
      symbolTrades.forEach(trade => {
        const date = new Date(trade.pnl_calculated_at!).toISOString().split('T')[0];
        const volume = (trade.calculated_quantity || 0) * (trade.price || 0);
        
        if (!dailyFrequency[date]) {
          dailyFrequency[date] = { tradeCount: 0, volume: 0, pnl: 0 };
        }
        
        dailyFrequency[date].tradeCount += 1;
        dailyFrequency[date].volume += volume;
        dailyFrequency[date].pnl += trade.pnl!;
      });

      const dailyData = Object.entries(dailyFrequency).map(([date, data]) => ({
        date,
        ...data
      })).sort((a, b) => a.date.localeCompare(b.date));

      const tradeCounts = Object.values(dailyFrequency).map(d => d.tradeCount);
      const avgTradesPerDay = tradeCounts.length > 0 ? tradeCounts.reduce((sum, count) => sum + count, 0) / tradeCounts.length : 0;
      const maxTradesPerDay = tradeCounts.length > 0 ? Math.max(...tradeCounts) : 0;
      const minTradesPerDay = tradeCounts.length > 0 ? Math.min(...tradeCounts) : 0;

      return {
        symbol,
        dailyFrequency: dailyData,
        weeklyFrequency: [], // Will be calculated if needed
        monthlyFrequency: [], // Will be calculated if needed
        avgTradesPerDay,
        maxTradesPerDay,
        minTradesPerDay,
        tradingDaysCount: Object.keys(dailyFrequency).length,
        totalTradingDays: dailyData.length,
        tradingFrequencyPercent: 100 // Simplified for now
      };
    });
  }, []);

  // Calculate symbol alerts and insights
  const calculateSymbolAlertsAndInsights = useCallback((symbolMetrics: SymbolMetrics[]): { alerts: SymbolAlert[]; insights: SymbolInsight[] } => {
    const alerts: SymbolAlert[] = [];
    const insights: SymbolInsight[] = [];

    symbolMetrics.forEach(symbol => {
      const alertId = `alert-${symbol.symbol}-${Date.now()}`;
      const insightId = `insight-${symbol.symbol}-${Date.now()}`;

      // Performance Alerts
      if (symbol.totalPnl < -1000) {
        alerts.push({
          id: alertId + '-loss',
          symbol: symbol.symbol,
          alertType: 'performance',
          severity: 'high',
          title: 'Yüksek Kayıp',
          description: `${symbol.symbol} sembolü ₺${Math.abs(symbol.totalPnl).toFixed(2)} kayıp yaşıyor.`,
          value: symbol.totalPnl,
          threshold: -1000,
          createdAt: new Date().toISOString(),
          isActive: true
        });
      }

      if (symbol.winRate < 30) {
        alerts.push({
          id: alertId + '-winrate',
          symbol: symbol.symbol,
          alertType: 'performance',
          severity: 'medium',
          title: 'Düşük Kazanma Oranı',
          description: `${symbol.symbol} sembolünün kazanma oranı %${symbol.winRate.toFixed(1)} ile düşük seviyede.`,
          value: symbol.winRate,
          threshold: 30,
          createdAt: new Date().toISOString(),
          isActive: true
        });
      }

      // Risk Alerts
      if (symbol.volatility > 50) {
        alerts.push({
          id: alertId + '-volatility',
          symbol: symbol.symbol,
          alertType: 'risk',
          severity: 'high',
          title: 'Yüksek Volatilite',
          description: `${symbol.symbol} sembolü %${symbol.volatility.toFixed(1)} volatilite ile yüksek risk taşıyor.`,
          value: symbol.volatility,
          threshold: 50,
          createdAt: new Date().toISOString(),
          isActive: true
        });
      }

      if (symbol.maxDrawdownPercent > 20) {
        alerts.push({
          id: alertId + '-drawdown',
          symbol: symbol.symbol,
          alertType: 'risk',
          severity: 'medium',
          title: 'Yüksek Drawdown',
          description: `${symbol.symbol} sembolü %${symbol.maxDrawdownPercent.toFixed(1)} maksimum düşüş yaşamış.`,
          value: symbol.maxDrawdownPercent,
          threshold: 20,
          createdAt: new Date().toISOString(),
          isActive: true
        });
      }

      // Volume Alerts
      if (symbol.totalTrades < 5) {
        alerts.push({
          id: alertId + '-volume',
          symbol: symbol.symbol,
          alertType: 'volume',
          severity: 'low',
          title: 'Düşük İşlem Hacmi',
          description: `${symbol.symbol} sembolü sadece ${symbol.totalTrades} işlem ile düşük aktivite gösteriyor.`,
          value: symbol.totalTrades,
          threshold: 5,
          createdAt: new Date().toISOString(),
          isActive: true
        });
      }

      // Pattern Alerts
      if (symbol.lossStreak >= 5) {
        alerts.push({
          id: alertId + '-pattern',
          symbol: symbol.symbol,
          alertType: 'pattern',
          severity: 'high',
          title: 'Uzun Kayıp Serisi',
          description: `${symbol.symbol} sembolü ${symbol.lossStreak} ardışık kayıp yaşıyor.`,
          value: symbol.lossStreak,
          threshold: 5,
          createdAt: new Date().toISOString(),
          isActive: true
        });
      }

      // Performance Insights
      if (symbol.totalPnl > 1000 && symbol.winRate > 70) {
        insights.push({
          id: insightId + '-opportunity',
          symbol: symbol.symbol,
          insightType: 'opportunity',
          title: 'Güçlü Performans',
          description: `${symbol.symbol} sembolü ₺${symbol.totalPnl.toFixed(2)} kar ve %${symbol.winRate.toFixed(1)} kazanma oranı ile mükemmel performans sergiliyor.`,
          confidence: 95,
          actionRecommendation: 'Bu sembole yatırım artırılabilir.',
          supportingData: {
            pnl: symbol.totalPnl,
            winRate: symbol.winRate,
            sharpeRatio: symbol.sharpeRatio
          },
          createdAt: new Date().toISOString()
        });
      }

      if (symbol.sharpeRatio > 2) {
        insights.push({
          id: insightId + '-trend',
          symbol: symbol.symbol,
          insightType: 'trend',
          title: 'Mükemmel Risk-Getiri Oranı',
          description: `${symbol.symbol} sembolü ${symbol.sharpeRatio.toFixed(3)} Sharpe oranı ile risk ayarlı getiri açısından çok başarılı.`,
          confidence: 90,
          actionRecommendation: 'Risk yönetimi stratejisi olarak portföyde tutulabilir.',
          supportingData: {
            sharpeRatio: symbol.sharpeRatio,
            volatility: symbol.volatility,
            roi: symbol.roi
          },
          createdAt: new Date().toISOString()
        });
      }

      // Warning Insights
      if (symbol.totalPnl < 0 && symbol.winRate < 40) {
        insights.push({
          id: insightId + '-warning',
          symbol: symbol.symbol,
          insightType: 'warning',
          title: 'Performans Uyarısı',
          description: `${symbol.symbol} sembolü hem negatif getiri (₺${symbol.totalPnl.toFixed(2)}) hem de düşük kazanma oranı (%${symbol.winRate.toFixed(1)}) gösteriyor.`,
          confidence: 85,
          actionRecommendation: 'Bu sembol için strateji gözden geçirilmeli veya pozisyon azaltılmalı.',
          supportingData: {
            pnl: symbol.totalPnl,
            winRate: symbol.winRate,
            totalTrades: symbol.totalTrades
          },
          createdAt: new Date().toISOString()
        });
      }

      // Anomaly Detection
      if (symbol.profitFactor > 5) {
        insights.push({
          id: insightId + '-anomaly',
          symbol: symbol.symbol,
          insightType: 'anomaly',
          title: 'Olağanüstü Kar Faktörü',
          description: `${symbol.symbol} sembolü ${symbol.profitFactor.toFixed(2)} kar faktörü ile olağanüstü performans gösteriyor.`,
          confidence: 80,
          actionRecommendation: 'Bu performansın sürdürülebilirliği analiz edilmeli.',
          supportingData: {
            profitFactor: symbol.profitFactor,
            averageWin: symbol.averageWin,
            averageLoss: symbol.averageLoss
          },
          createdAt: new Date().toISOString()
        });
      }

      if (symbol.volatility < 5 && symbol.totalTrades > 20) {
        insights.push({
          id: insightId + '-stable',
          symbol: symbol.symbol,
          insightType: 'trend',
          title: 'Düşük Volatilite Avantajı',
          description: `${symbol.symbol} sembolü %${symbol.volatility.toFixed(1)} düşük volatilite ile istikrarlı performans sergiliyor.`,
          confidence: 75,
          actionRecommendation: 'Düşük riskli yatırım stratejileri için uygun.',
          supportingData: {
            volatility: symbol.volatility,
            totalTrades: symbol.totalTrades,
            sharpeRatio: symbol.sharpeRatio
          },
          createdAt: new Date().toISOString()
        });
      }
    });

    return { alerts, insights };
  }, []);

  // Main calculation function
  const calculateEnhancedSymbolAnalysis = useCallback((trades: Trade[]): EnhancedSymbolAnalysisData => {
    setLoading(true);
    setError(null);

    try {
      const symbolMetrics = calculateSymbolMetrics(trades);
      const symbolTimePerformance = calculateSymbolTimePerformance(trades);
      const symbolTradingFrequency = calculateSymbolTradingFrequency(trades);
      const correlationMatrix = calculateSymbolCorrelation(trades);
      const { alerts, insights } = calculateSymbolAlertsAndInsights(symbolMetrics);

      const totalSymbols = symbolMetrics.length;
      const profitableSymbols = symbolMetrics.filter(s => s.totalPnl > 0).length;
      const totalTrades = symbolMetrics.reduce((sum, s) => sum + s.totalTrades, 0);
      const totalPnl = symbolMetrics.reduce((sum, s) => sum + s.totalPnl, 0);
      const totalInvestment = symbolMetrics.reduce((sum, s) => sum + s.totalInvestment, 0);

      const bestPerformingSymbol = symbolMetrics.length > 0 ? symbolMetrics[0].symbol : '';
      const worstPerformingSymbol = symbolMetrics.length > 0 ? 
        symbolMetrics.sort((a, b) => a.totalPnl - b.totalPnl)[0].symbol : '';
      const mostTradedSymbol = symbolMetrics.length > 0 ? 
        symbolMetrics.sort((a, b) => b.totalTrades - a.totalTrades)[0].symbol : '';

      return {
        symbolMetrics,
        symbolTimePerformance,
        symbolTradingFrequency,
        symbolRiskReturn: [], // Will be implemented
        correlationMatrix,
        benchmarkComparisons: [], // Will be implemented
        alerts,
        insights,
        summary: {
          totalSymbols,
          profitableSymbols,
          totalTrades,
          totalPnl,
          totalInvestment,
          overallROI: totalInvestment > 0 ? (totalPnl / totalInvestment) * 100 : 0,
          overallWinRate: totalTrades > 0 ? (symbolMetrics.reduce((sum, s) => sum + s.winningTrades, 0) / totalTrades) * 100 : 0,
          bestPerformingSymbol,
          worstPerformingSymbol,
          mostTradedSymbol,
          highestRiskSymbol: '', // Will be calculated
          bestRiskAdjustedSymbol: symbolMetrics.length > 0 ? 
            symbolMetrics.sort((a, b) => b.sharpeRatio - a.sharpeRatio)[0].symbol : ''
        },
        lastUpdated: new Date().toISOString(),
        dataQuality: {
          completeness: 100,
          accuracy: 100,
          freshness: 0
        }
      };
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Symbol analysis calculation failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [calculateSymbolMetrics, calculateSymbolTimePerformance, calculateSymbolTradingFrequency, calculateSymbolCorrelation, calculateSymbolAlertsAndInsights]);

  return {
    calculateEnhancedSymbolAnalysis,
    loading,
    error
  };
};
