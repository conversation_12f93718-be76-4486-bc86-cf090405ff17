import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Card,
  CardBody,
  CardHeader,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  useToast,
  Badge,
  Icon,
  Container,
  useColorModeValue,
  Alert,
  AlertIcon,
  Divider,
  Flex,
  useBreakpointValue,
  FormHelperText,
  InputGroup,
  InputLeftElement,
  Avatar,
  Spinner,
  AlertTitle,
  AlertDescription
} from '@chakra-ui/react';
// Unused imports removed
import { useForm } from 'react-hook-form';
import { useNotifications } from '../../hooks/useNotifications';
import { CreateAnnouncementParams, CreateNotificationParams } from '../../types/notification';
import { supabase } from '../../supabaseClient';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { keyframes } from '@emotion/react';
import NotificationPreviewModal from '../../components/admin/NotificationPreviewModal';
import {
  FaBell,
  FaUsers,
  FaPaperPlane,
  FaTimes,
  FaCrown,
  FaGem,
  FaUser,
  FaSearch
} from 'react-icons/fa';

// Animations
const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
`;



interface NotificationStats {
  totalUsers: number;
  soloUsers: number;
  broSellers: number;
  broSubscribers: number;
}

interface User {
  id: string;
  username: string;
  full_name: string;
  avatar_url?: string;
}

interface AnnouncementFormData {
  title: string;
  message: string;
  target_audience: 'all' | 'solo_users' | 'bro_sellers' | 'bro_subscribers' | 'individual';
  severity: 'info' | 'warning' | 'error' | 'success';
  expires_at?: string;
  action_url?: string;
  action_label?: string;
  is_urgent?: boolean;
  send_email?: boolean;
  individual_user_id?: string;
}

const CreateAnnouncement: React.FC = () => {
  // HOOK'LAR - SIRALAMASI DEĞİŞMEMELİ
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const toast = useToast();
  const { createAnnouncement, createNotification } = useNotifications();
  
  // STATE HOOK'LARI
  const [loading, setLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [stats, setStats] = useState<NotificationStats>({
    totalUsers: 0,
    soloUsers: 0,
    broSellers: 0,
    broSubscribers: 0
  });
  const [statsLoading, setStatsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [usersLoading, setUsersLoading] = useState(false);

  // COLOR MODE HOOK'LARI
  const bgGradient = useColorModeValue(
    'linear(to-br, gray.50, blue.50, purple.50)',
    'linear(to-br, gray.900, blue.900, purple.900)'
  );
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');

  // RESPONSIVE HOOK'LARI
  const containerMaxW = useBreakpointValue({ base: 'container.sm', md: 'container.md', lg: 'container.lg' });

  // URL'den hedef kitleyi al
  const targetFromUrl = searchParams.get('target') as 'all' | 'solo_users' | 'bro_sellers' | 'bro_subscribers' | 'individual' || 'all';

  // FORM HOOK'U
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<AnnouncementFormData>({
    defaultValues: {
      severity: 'info',
      target_audience: targetFromUrl,
      is_urgent: false,
      send_email: false
    }
  });

  // WATCH HOOK'LARI
  const targetAudience = watch('target_audience');
  const severity = watch('severity');
  const isUrgent = watch('is_urgent');
  const title = watch('title');
  const message = watch('message');

  // EFFECT HOOK'LARI - SIRALAMASI DEĞİŞMEMELİ
  useEffect(() => {
    fetchStats();
    fetchUsers();
  }, []);

  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredUsers(users);
    } else {
      const filtered = users.filter(user => 
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.full_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredUsers(filtered);
    }
  }, [searchTerm, users]);

  useEffect(() => {
    if (targetAudience !== 'individual') {
      setSelectedUser(null);
      setSearchTerm('');
      setValue('individual_user_id', undefined);
    }
  }, [targetAudience, setValue]);

  // FONKSIYONLAR
  const fetchUsers = async () => {
    try {
      setUsersLoading(true);
      
      // Basit sorgu ile kullanıcıları çek
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, username, full_name, avatar_url')
        .order('username');

      if (profilesError) {
        console.error('Profiller yüklenirken hata:', profilesError);
        return;
      }

      // Aktif ve admin olmayan kullanıcıları filtrele
      const { data: settingsData, error: settingsError } = await supabase
        .from('user_settings')
        .select('id, is_active, is_superuser')
        .eq('is_active', true)
        .eq('is_superuser', false);

      if (settingsError) {
        console.error('Ayarlar yüklenirken hata:', settingsError);
        return;
      }

      // Aktif kullanıcı ID'lerini al
      const activeUserIds = new Set(settingsData?.map(s => s.id) || []);

      // Profilleri filtrele
      const userList = profilesData
        ?.filter(profile => activeUserIds.has(profile.id))
        ?.map(user => ({
          id: user.id,
          username: user.username || 'Bilinmiyor',
          full_name: user.full_name || 'İsim Belirtilmemiş',
          avatar_url: user.avatar_url
        })) || [];

      setUsers(userList);
      setFilteredUsers(userList);
    } catch (error) {
      console.error('Kullanıcılar yüklenirken hata:', error);
    } finally {
      setUsersLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      setStatsLoading(true);
      
      // Kullanıcı istatistikleri
      const { data: users } = await supabase
        .from('user_settings')
        .select('id, is_superuser');

      const { data: robots } = await supabase
        .from('robots')
        .select('seller_id')
        .is('deleted_at', null);

      const { data: subscriptions } = await supabase
        .from('subscriptions')
        .select('user_id')
        .eq('is_active', true)
        .eq('is_deleted', false);

      let soloUsers = 0;
      let broSellers = 0;
      const subscriberIds = new Set();

      if (users) {
        soloUsers = users.filter(u => !u.is_superuser).length;
      }

      if (robots) {
        const sellerIds = new Set(robots.map(r => r.seller_id));
        broSellers = sellerIds.size;
      }

      if (subscriptions) {
        subscriptions.forEach(sub => subscriberIds.add(sub.user_id));
      }

      setStats({
        totalUsers: users?.length || 0,
        soloUsers,
        broSellers,
        broSubscribers: subscriberIds.size
      });
    } catch (error) {
      console.error('Stats yüklenirken hata:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  const handlePreview = () => {
    setShowPreview(true);
  };

  const handleConfirmSend = async () => {
    // Mevcut form değerlerini al
    const data: AnnouncementFormData = {
      title: title || '',
      message: message || '',
      target_audience: targetAudience,
      severity: severity,
      expires_at: watch('expires_at'),
      action_url: watch('action_url'),
      action_label: watch('action_label'),
      is_urgent: isUrgent,
      send_email: watch('send_email'),
      individual_user_id: selectedUser?.id
    };
    try {
      setLoading(true);

      if (data.target_audience === 'individual') {
        // Bireysel bildirim gönder
        if (!selectedUser) {
          toast({
            title: 'Hata',
            description: 'Lütfen bir kullanıcı seçin',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
          return;
        }

        const notificationParams: CreateNotificationParams = {
          user_id: selectedUser.id,
          title: data.title,
          message: data.message,
          type: 'admin_announcement',
          severity: data.severity,
          action_url: data.action_url || undefined,
          action_label: data.action_label || undefined,
          expires_at: data.expires_at ? new Date(data.expires_at).toISOString() : undefined
        };

        const result = await createNotification(notificationParams);

        if (result) {
          toast({
            title: 'Başarılı! 🎉',
            description: `Bildirim ${selectedUser.full_name || selectedUser.username} kullanıcısına başarıyla gönderildi`,
            status: 'success',
            duration: 5000,
            isClosable: true,
            position: 'top-right'
          });
        }
      } else {
        // Toplu duyuru gönder
        const params: CreateAnnouncementParams = {
          title: data.title,
          message: data.message,
          target_audience: data.target_audience as 'all' | 'solo_users' | 'bro_sellers' | 'bro_subscribers',
          severity: data.severity,
          expires_at: data.expires_at ? new Date(data.expires_at).toISOString() : undefined,
          action_url: data.action_url || undefined,
          action_label: data.action_label || undefined
        };

        const result = await createAnnouncement(params);

        if (result) {
          toast({
            title: 'Başarılı! 🎉',
            description: `Duyuru ${getTargetCount()} kullanıcıya başarıyla gönderildi`,
            status: 'success',
            duration: 5000,
            isClosable: true,
            position: 'top-right'
          });
        }
      }

      // Form'u temizle ve geri dön
      reset();
      setSelectedUser(null);
      setSearchTerm('');
      navigate('/admin/notifications');
    } catch (error: any) {
      console.error('Bildirim gönderirken hata:', error);
      toast({
        title: 'Hata Oluştu',
        description: error.message || 'Bildirim gönderilirken bir hata oluştu',
        status: 'error',
        duration: 5000,
        isClosable: true,
        position: 'top-right'
      });
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = () => {
    // Önizleme göster
    handlePreview();
  };

  const getTargetCount = () => {
    switch (targetAudience) {
      case 'all': return stats.totalUsers;
      case 'solo_users': return stats.soloUsers;
      case 'bro_sellers': return stats.broSellers;
      case 'bro_subscribers': return stats.broSubscribers;
      case 'individual': return selectedUser ? 1 : 0;
      default: return 0;
    }
  };







  // JSX RENDER
  return (
    <Box minH="100vh" bg={bgGradient}>
      <Container maxW={containerMaxW} py={8}>
        {/* Header */}
        <VStack spacing={6} align="stretch">
          <Card bg={cardBg} shadow="xl" borderRadius="2xl" overflow="hidden">
            <CardHeader>
              <HStack spacing={4}>
                <Box
                  p={3}
                  borderRadius="xl"
                  bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                  color="white"
                  animation={`${pulse} 2s infinite`}
                >
                  <Icon as={FaBell} boxSize={6} />
                </Box>
                <VStack align="start" spacing={1}>
                  <Heading size="lg" color={textColor}>
                    Duyuru Oluştur
                  </Heading>
                  <Text color={secondaryTextColor} fontSize="sm">
                    Kullanıcılara bildirim ve duyuru gönder
                  </Text>
                </VStack>
              </HStack>
            </CardHeader>
          </Card>

          {/* İstatistik Kartları */}
          <Card bg={cardBg} shadow="xl" borderRadius="2xl">
            <CardHeader>
              <Heading size="md" color={textColor}>
                📊 Hedef Kitle İstatistikleri
              </Heading>
            </CardHeader>
            <CardBody>
              <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
                <Card 
                  bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" 
                  color="white" 
                  flex={1}
                  _hover={{ transform: 'translateY(-2px)', shadow: 'xl' }}
                  transition="all 0.2s"
                >
                  <CardBody textAlign="center">
                    <Icon as={FaUsers} boxSize={8} mb={2} />
                    {statsLoading ? (
                      <Spinner size="sm" />
                    ) : (
                      <Text fontSize="2xl" fontWeight="bold">
                        {stats.totalUsers}
                      </Text>
                    )}
                    <Text fontSize="sm" opacity={0.9}>Toplam Kullanıcı</Text>
                  </CardBody>
                </Card>

                <Card 
                  bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" 
                  color="white" 
                  flex={1}
                  _hover={{ transform: 'translateY(-2px)', shadow: 'xl' }}
                  transition="all 0.2s"
                >
                  <CardBody textAlign="center">
                    <Icon as={FaUser} boxSize={8} mb={2} />
                    {statsLoading ? (
                      <Spinner size="sm" />
                    ) : (
                      <Text fontSize="2xl" fontWeight="bold">
                        {stats.soloUsers}
                      </Text>
                    )}
                    <Text fontSize="sm" opacity={0.9}>Solo Kullanıcı</Text>
                  </CardBody>
                </Card>

                <Card 
                  bg="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" 
                  color="white" 
                  flex={1}
                  _hover={{ transform: 'translateY(-2px)', shadow: 'xl' }}
                  transition="all 0.2s"
                >
                  <CardBody textAlign="center">
                    <Icon as={FaCrown} boxSize={8} mb={2} />
                    {statsLoading ? (
                      <Spinner size="sm" />
                    ) : (
                      <Text fontSize="2xl" fontWeight="bold">
                        {stats.broSellers}
                      </Text>
                    )}
                    <Text fontSize="sm" opacity={0.9}>Bro Satıcı</Text>
                  </CardBody>
                </Card>

                <Card 
                  bg="linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)" 
                  color="white" 
                  flex={1}
                  _hover={{ transform: 'translateY(-2px)', shadow: 'xl' }}
                  transition="all 0.2s"
                >
                  <CardBody textAlign="center">
                    <Icon as={FaGem} boxSize={8} mb={2} />
                    {statsLoading ? (
                      <Spinner size="sm" />
                    ) : (
                      <Text fontSize="2xl" fontWeight="bold">
                        {stats.broSubscribers}
                      </Text>
                    )}
                    <Text fontSize="sm" opacity={0.9}>Bro Abone</Text>
                  </CardBody>
                </Card>

                {/* Seçili Kullanıcı Kartı */}
                {targetAudience === 'individual' && (
                  <Card 
                    bg="linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)" 
                    color="gray.800" 
                    flex={1}
                    _hover={{ transform: 'translateY(-2px)', shadow: 'xl' }}
                    transition="all 0.2s"
                  >
                    <CardBody textAlign="center">
                      <Icon as={FaUser} boxSize={8} mb={2} />
                      <Text fontSize="2xl" fontWeight="bold">
                        {selectedUser ? 1 : 0}
                      </Text>
                      <Text fontSize="sm" opacity={0.9}>Seçili Kullanıcı</Text>
                    </CardBody>
                  </Card>
                )}
              </Flex>
            </CardBody>
          </Card>

          {/* Form */}
          <Card bg={cardBg} shadow="xl" borderRadius="2xl">
            <CardHeader>
              <Heading size="md" color={textColor}>
                📝 Duyuru Detayları
              </Heading>
            </CardHeader>
            <CardBody>
              <form onSubmit={handleSubmit(onSubmit)}>
                <VStack spacing={6} align="stretch">
                  {/* Başlık */}
                  <FormControl isInvalid={!!errors.title}>
                    <FormLabel color={textColor} fontWeight="semibold">
                      📌 Duyuru Başlığı
                    </FormLabel>
                    <Input
                      {...register('title', { required: 'Başlık zorunludur' })}
                      placeholder="Duyuru başlığını girin..."
                      bg={useColorModeValue('gray.50', 'gray.700')}
                      border="2px solid"
                      borderColor={borderColor}
                      _focus={{
                        borderColor: 'blue.400',
                        boxShadow: '0 0 0 1px #3182ce'
                      }}
                    />
                    {errors.title && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {errors.title.message}
                      </Text>
                    )}
                  </FormControl>

                  {/* Mesaj */}
                  <FormControl isInvalid={!!errors.message}>
                    <FormLabel color={textColor} fontWeight="semibold">
                      💬 Duyuru Mesajı
                    </FormLabel>
                    <Textarea
                      {...register('message', { required: 'Mesaj zorunludur' })}
                      placeholder="Duyuru mesajınızı girin..."
                      rows={6}
                      bg={useColorModeValue('gray.50', 'gray.700')}
                      border="2px solid"
                      borderColor={borderColor}
                      _focus={{
                        borderColor: 'blue.400',
                        boxShadow: '0 0 0 1px #3182ce'
                      }}
                    />
                    {errors.message && (
                      <Text color="red.500" fontSize="sm" mt={1}>
                        {errors.message.message}
                      </Text>
                    )}
                  </FormControl>

                  {/* Hedef Kitle */}
                  <FormControl>
                    <FormLabel color={textColor} fontWeight="semibold">
                      🎯 Hedef Kitle
                    </FormLabel>
                    <Select
                      {...register('target_audience')}
                      bg={useColorModeValue('gray.50', 'gray.700')}
                      border="2px solid"
                      borderColor={borderColor}
                      _focus={{
                        borderColor: 'blue.400',
                        boxShadow: '0 0 0 1px #3182ce'
                      }}
                    >
                      <option value="all">👥 Tüm Kullanıcılar ({stats.totalUsers})</option>
                      <option value="solo_users">🤖 Solo Robot Kullanıcıları ({stats.soloUsers})</option>
                      <option value="bro_sellers">👑 Bro Robot Satıcıları ({stats.broSellers})</option>
                      <option value="bro_subscribers">💎 Bro Robot Aboneleri ({stats.broSubscribers})</option>
                      <option value="individual">🎯 Bireysel Kullanıcı Seç</option>
                    </Select>
                  </FormControl>

                  {/* Bireysel Kullanıcı Seçimi */}
                  {targetAudience === 'individual' && (
                    <Card 
                      bg="linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)" 
                      borderRadius="xl"
                      p={4}
                    >
                      <VStack spacing={4} align="stretch">
                        <HStack>
                          <Icon as={FaUser} color="green.600" />
                          <Heading size="sm" color="gray.800">
                            Kullanıcı Seçimi
                          </Heading>
                        </HStack>

                        {/* Seçili Kullanıcı Alert */}
                        {selectedUser && (
                          <Alert status="success" borderRadius="lg">
                            <AlertIcon />
                            <HStack flex={1}>
                              <Avatar size="sm" src={selectedUser.avatar_url} name={selectedUser.full_name} />
                              <VStack align="start" spacing={0}>
                                <AlertTitle fontSize="sm">
                                  {selectedUser.full_name}
                                </AlertTitle>
                                <AlertDescription fontSize="xs">
                                  @{selectedUser.username}
                                </AlertDescription>
                              </VStack>
                            </HStack>
                          </Alert>
                        )}

                        {/* Arama Kutusu */}
                        <InputGroup>
                          <InputLeftElement>
                            <Icon as={FaSearch} color="gray.500" />
                          </InputLeftElement>
                          <Input
                            placeholder="Kullanıcı adı veya isim ile ara..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            bg="white"
                            border="2px solid"
                            borderColor="green.200"
                            _focus={{
                              borderColor: 'green.400',
                              boxShadow: '0 0 0 1px #48bb78'
                            }}
                          />
                        </InputGroup>

                        {/* Kullanıcı Listesi */}
                        <Box
                          maxH="300px"
                          overflowY="auto"
                          border="2px solid"
                          borderColor="green.200"
                          borderRadius="lg"
                          bg="white"
                        >
                          {usersLoading ? (
                            <Flex justify="center" p={4}>
                              <Spinner color="green.500" />
                            </Flex>
                          ) : filteredUsers.length > 0 ? (
                            <VStack spacing={0} align="stretch">
                              {filteredUsers.map((user) => (
                                <Box
                                  key={user.id}
                                  p={3}
                                  cursor="pointer"
                                  _hover={{ bg: 'green.50' }}
                                  onClick={() => {
                                    setSelectedUser(user);
                                    setValue('individual_user_id', user.id);
                                  }}
                                  borderBottom="1px solid"
                                  borderColor="green.100"
                                  position="relative"
                                >
                                  <HStack justify="space-between">
                                    <HStack>
                                      <Avatar size="sm" src={user.avatar_url} name={user.full_name} />
                                      <VStack align="start" spacing={0}>
                                        <Text fontWeight="medium" color="gray.800" fontSize="sm">
                                          {user.full_name}
                                        </Text>
                                        <Text color="gray.600" fontSize="xs">
                                          @{user.username}
                                        </Text>
                                      </VStack>
                                    </HStack>
                                    {selectedUser?.id === user.id && (
                                      <Badge colorScheme="green" variant="solid">
                                        Seçili
                                      </Badge>
                                    )}
                                  </HStack>
                                </Box>
                              ))}
                            </VStack>
                          ) : (
                            <Box p={4} textAlign="center">
                              <Text color="gray.600" fontSize="sm">
                                Arama kriterinize uygun kullanıcı bulunamadı
                              </Text>
                            </Box>
                          )}
                        </Box>
                      </VStack>
                    </Card>
                  )}

                  {/* Önem Düzeyi */}
                  <FormControl>
                    <FormLabel color={textColor} fontWeight="semibold">
                      ⚠️ Önem Düzeyi
                    </FormLabel>
                    <Select
                      {...register('severity')}
                      bg={useColorModeValue('gray.50', 'gray.700')}
                      border="2px solid"
                      borderColor={borderColor}
                      _focus={{
                        borderColor: 'blue.400',
                        boxShadow: '0 0 0 1px #3182ce'
                      }}
                    >
                      <option value="info">ℹ️ Bilgi</option>
                      <option value="success">✅ Başarı</option>
                      <option value="warning">⚠️ Uyarı</option>
                      <option value="error">❌ Hata</option>
                    </Select>
                  </FormControl>

                  {/* İsteğe Bağlı Alanlar */}
                  <Divider />
                  
                  <Text color={textColor} fontWeight="semibold" fontSize="lg">
                    📋 İsteğe Bağlı Ayarlar
                  </Text>

                  {/* Son Kullanma Tarihi */}
                  <FormControl>
                    <FormLabel color={textColor} fontWeight="semibold">
                      📅 Son Kullanma Tarihi (İsteğe Bağlı)
                    </FormLabel>
                    <Input
                      {...register('expires_at')}
                      type="datetime-local"
                      bg={useColorModeValue('gray.50', 'gray.700')}
                      border="2px solid"
                      borderColor={borderColor}
                      _focus={{
                        borderColor: 'blue.400',
                        boxShadow: '0 0 0 1px #3182ce'
                      }}
                    />
                    <FormHelperText>
                      Belirtilen tarihten sonra bildirim otomatik olarak gizlenecek
                    </FormHelperText>
                  </FormControl>

                  {/* Aksiyon URL */}
                  <FormControl>
                    <FormLabel color={textColor} fontWeight="semibold">
                      🔗 Aksiyon URL (İsteğe Bağlı)
                    </FormLabel>
                    <Input
                      {...register('action_url')}
                      placeholder="https://example.com/action"
                      bg={useColorModeValue('gray.50', 'gray.700')}
                      border="2px solid"
                      borderColor={borderColor}
                      _focus={{
                        borderColor: 'blue.400',
                        boxShadow: '0 0 0 1px #3182ce'
                      }}
                    />
                    <FormHelperText>
                      Kullanıcının tıklayabileceği bir bağlantı
                    </FormHelperText>
                  </FormControl>

                  {/* Aksiyon Etiketi */}
                  <FormControl>
                    <FormLabel color={textColor} fontWeight="semibold">
                      🏷️ Aksiyon Etiketi (İsteğe Bağlı)
                    </FormLabel>
                    <Input
                      {...register('action_label')}
                      placeholder="Detayları Gör"
                      bg={useColorModeValue('gray.50', 'gray.700')}
                      border="2px solid"
                      borderColor={borderColor}
                      _focus={{
                        borderColor: 'blue.400',
                        boxShadow: '0 0 0 1px #3182ce'
                      }}
                    />
                    <FormHelperText>
                      Aksiyon butonunda görünecek metin
                    </FormHelperText>
                  </FormControl>

                  {/* Butonlar */}
                  <Divider />
                  
                  <HStack spacing={4} justify="space-between">
                    <Button
                      variant="outline"
                      colorScheme="gray"
                      leftIcon={<Icon as={FaTimes} />}
                      onClick={() => navigate('/admin/notifications')}
                      size="lg"
                    >
                      İptal
                    </Button>

                    <Button
                      type="submit"
                      colorScheme="blue"
                      leftIcon={<Icon as={FaPaperPlane} />}
                      isLoading={isSubmitting}
                      loadingText="Hazırlanıyor..."
                      size="lg"
                      isDisabled={
                        targetAudience === 'individual' && !selectedUser
                      }
                      bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                      _hover={{
                        bg: "linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%)",
                        transform: 'translateY(-2px)',
                        boxShadow: 'xl'
                      }}
                      _active={{
                        transform: 'translateY(0)',
                      }}
                      transition="all 0.2s"
                    >
                      Önizleme & Gönder
                    </Button>
                  </HStack>
                </VStack>
              </form>
            </CardBody>
          </Card>
        </VStack>

        {/* Önizleme Modal */}
        <NotificationPreviewModal
          isOpen={showPreview}
          onClose={() => setShowPreview(false)}
          onConfirm={handleConfirmSend}
          previewData={{
            title: title || '',
            message: message || '',
            target_audience: targetAudience,
            severity: severity,
            action_url: watch('action_url'),
            action_label: watch('action_label'),
            expires_at: watch('expires_at'),
            targetCount: getTargetCount(),
            recipientName: selectedUser ? `${selectedUser.full_name} (@${selectedUser.username})` : undefined
          }}
          isLoading={loading}
        />
      </Container>
    </Box>
  );
};

export default CreateAnnouncement; 