import React from 'react';
import {
  HStack,
  IconButton,
  Icon,
  useColorModeValue,
  useColorMode,
} from '@chakra-ui/react';
import { FiSearch, FiMoon, FiSun } from 'react-icons/fi';
import NotificationDropdown from '../NotificationDropdown';

interface NavbarActionsProps {
  onSearchClick: () => void;
}

/**
 * NavbarActions - <PERSON>les action buttons (search, notifications, theme toggle)
 * Separated for better maintainability and single responsibility
 */
const NavbarActions: React.FC<NavbarActionsProps> = ({ onSearchClick }) => {
  const { colorMode, toggleColorMode } = useColorMode();
  
  // Color values
  const iconColor = useColorModeValue('gray.600', 'gray.300');
  const buttonHover = useColorModeValue('gray.100', 'gray.700');
  const buttonActive = useColorModeValue('gray.200', 'gray.600');

  return (
    <HStack 
      spacing={{ base: '1', sm: '2', md: '3' }} 
      alignItems="center"
      flexShrink="0"
    >
      {/* Arama butonu - mobile/tablet için */}
      <IconButton
        aria-label="Arama yap"
        icon={<Icon as={FiSearch} />}
        variant="ghost"
        size={{ base: 'sm', md: 'md' }}
        borderRadius="full"
        color={iconColor}
        display={{ base: 'flex', lg: 'none' }}
        onClick={onSearchClick}
        _hover={{ bg: buttonHover }}
        _active={{ bg: buttonActive }}
        _focus={{
          boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
          outline: 'none',
        }}
      />

      {/* Bildirimler Dropdown */}
      <NotificationDropdown />

      {/* Dark mode toggle */}
      <IconButton
        aria-label={
          colorMode === 'light' 
            ? 'Karanlık temaya geç' 
            : 'Açık temaya geç'
        }
        icon={<Icon as={colorMode === 'light' ? FiMoon : FiSun} />}
        variant="ghost"
        size={{ base: 'sm', md: 'md' }}
        borderRadius="full"
        color={iconColor}
        onClick={toggleColorMode}
        _hover={{ bg: buttonHover }}
        _active={{ bg: buttonActive }}
        _focus={{
          boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
          outline: 'none',
        }}
      />
    </HStack>
  );
};

export default NavbarActions;
