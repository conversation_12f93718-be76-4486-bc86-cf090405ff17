/**
 * Data Validation & Sanitization Utility
 * Comprehensive input validation and sanitization for security hardening
 * Phase 2.3: Data Validation & Sanitization Hardening
 */

import DOMPurify from 'dompurify';

// Validation result interface
interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedValue?: any;
}

// Validation rules interface
interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: any) => boolean | string;
  sanitizer?: (value: any) => any;
}

// Common validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  username: /^[a-zA-Z0-9_-]{3,30}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  url: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  phoneNumber: /^\+?[1-9]\d{1,14}$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  numeric: /^\d+$/,
  decimal: /^\d+(\.\d{1,8})?$/,
  symbol: /^[A-Z]{1,10}$/,
  webhookId: /^[a-zA-Z0-9-_]{10,50}$/,
  robotName: /^[a-zA-Z0-9\s\-_]{3,50}$/,
  description: /^[\w\s\-.,!?()]{0,500}$/,
  ipAddress: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
} as const;

// Sanitization functions
export const SANITIZERS = {
  html: (value: string): string => {
    return DOMPurify.sanitize(value, { 
      ALLOWED_TAGS: [], 
      ALLOWED_ATTR: [] 
    });
  },
  
  richText: (value: string): string => {
    return DOMPurify.sanitize(value, {
      ALLOWED_TAGS: ['b', 'i', 'u', 'strong', 'em', 'p', 'br', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: []
    });
  },
  
  alphanumeric: (value: string): string => {
    return value.replace(/[^a-zA-Z0-9]/g, '');
  },
  
  numeric: (value: string): string => {
    return value.replace(/[^0-9]/g, '');
  },
  
  decimal: (value: string): string => {
    return value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');
  },
  
  username: (value: string): string => {
    return value.toLowerCase().replace(/[^a-z0-9_-]/g, '').substring(0, 30);
  },
  
  symbol: (value: string): string => {
    return value.toUpperCase().replace(/[^A-Z]/g, '').substring(0, 10);
  },
  
  url: (value: string): string => {
    try {
      const url = new URL(value);
      return url.toString();
    } catch {
      return '';
    }
  },
  
  trim: (value: string): string => {
    return value.trim();
  },
  
  removeExtraSpaces: (value: string): string => {
    return value.replace(/\s+/g, ' ').trim();
  }
} as const;

/**
 * Data Validator Class
 * Provides comprehensive validation and sanitization capabilities
 */
export class DataValidator {
  /**
   * Validate a single field
   */
  static validateField(
    value: any,
    rules: ValidationRule,
    fieldName: string = 'field'
  ): ValidationResult {
    const errors: string[] = [];
    let sanitizedValue = value;

    // Apply sanitizer first if provided
    if (rules.sanitizer && typeof value === 'string') {
      sanitizedValue = rules.sanitizer(value);
    }

    // Required validation
    if (rules.required && (sanitizedValue === null || sanitizedValue === undefined || sanitizedValue === '')) {
      errors.push(`${fieldName} is required`);
      return { isValid: false, errors, sanitizedValue };
    }

    // Skip other validations if value is empty and not required
    if (!rules.required && (sanitizedValue === null || sanitizedValue === undefined || sanitizedValue === '')) {
      return { isValid: true, errors: [], sanitizedValue };
    }

    // String validations
    if (typeof sanitizedValue === 'string') {
      // Min length validation
      if (rules.minLength && sanitizedValue.length < rules.minLength) {
        errors.push(`${fieldName} must be at least ${rules.minLength} characters long`);
      }

      // Max length validation
      if (rules.maxLength && sanitizedValue.length > rules.maxLength) {
        errors.push(`${fieldName} must be no more than ${rules.maxLength} characters long`);
      }

      // Pattern validation
      if (rules.pattern && !rules.pattern.test(sanitizedValue)) {
        errors.push(`${fieldName} format is invalid`);
      }
    }

    // Custom validator
    if (rules.customValidator) {
      const customResult = rules.customValidator(sanitizedValue);
      if (typeof customResult === 'string') {
        errors.push(customResult);
      } else if (!customResult) {
        errors.push(`${fieldName} is invalid`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue
    };
  }

  /**
   * Validate multiple fields
   */
  static validateFields(
    data: Record<string, any>,
    schema: Record<string, ValidationRule>
  ): { isValid: boolean; errors: Record<string, string[]>; sanitizedData: Record<string, any> } {
    const errors: Record<string, string[]> = {};
    const sanitizedData: Record<string, any> = {};
    let isValid = true;

    Object.entries(schema).forEach(([fieldName, rules]) => {
      const result = this.validateField(data[fieldName], rules, fieldName);
      
      if (!result.isValid) {
        errors[fieldName] = result.errors;
        isValid = false;
      }
      
      sanitizedData[fieldName] = result.sanitizedValue;
    });

    return { isValid, errors, sanitizedData };
  }

  /**
   * Trading-specific validations
   */
  static validateTradeData(data: any): ValidationResult {
    const schema = {
      symbol: {
        required: true,
        pattern: VALIDATION_PATTERNS.symbol,
        sanitizer: SANITIZERS.symbol
      },
      quantity: {
        required: true,
        customValidator: (value: any) => {
          const num = parseFloat(value);
          return !isNaN(num) && num > 0 && num <= 1000000;
        }
      },
      price: {
        required: true,
        customValidator: (value: any) => {
          const num = parseFloat(value);
          return !isNaN(num) && num > 0;
        }
      },
      orderSide: {
        required: true,
        customValidator: (value: any) => ['BUY', 'SELL'].includes(value)
      }
    };

    const result = this.validateFields(data, schema);
    
    return {
      isValid: result.isValid,
      errors: Object.values(result.errors).flat(),
      sanitizedValue: result.sanitizedData
    };
  }

  /**
   * User profile validations
   */
  static validateUserProfile(data: any): ValidationResult {
    const schema = {
      username: {
        required: true,
        minLength: 3,
        maxLength: 30,
        pattern: VALIDATION_PATTERNS.username,
        sanitizer: SANITIZERS.username
      },
      fullName: {
        required: false,
        maxLength: 100,
        sanitizer: SANITIZERS.removeExtraSpaces
      },
      bio: {
        required: false,
        maxLength: 500,
        sanitizer: SANITIZERS.richText
      },
      email: {
        required: true,
        pattern: VALIDATION_PATTERNS.email,
        sanitizer: SANITIZERS.trim
      }
    };

    const result = this.validateFields(data, schema);
    
    return {
      isValid: result.isValid,
      errors: Object.values(result.errors).flat(),
      sanitizedValue: result.sanitizedData
    };
  }

  /**
   * Robot data validations
   */
  static validateRobotData(data: any): ValidationResult {
    const schema = {
      name: {
        required: true,
        minLength: 3,
        maxLength: 50,
        pattern: VALIDATION_PATTERNS.robotName,
        sanitizer: SANITIZERS.removeExtraSpaces
      },
      description: {
        required: false,
        maxLength: 500,
        sanitizer: SANITIZERS.richText
      },
      price: {
        required: true,
        customValidator: (value: any) => {
          const num = parseFloat(value);
          return !isNaN(num) && num >= 0 && num <= 10000;
        }
      },
      subscriptionPeriod: {
        required: true,
        customValidator: (value: any) => {
          const num = parseInt(value);
          return !isNaN(num) && num >= 1 && num <= 365;
        }
      }
    };

    const result = this.validateFields(data, schema);
    
    return {
      isValid: result.isValid,
      errors: Object.values(result.errors).flat(),
      sanitizedValue: result.sanitizedData
    };
  }

  /**
   * API key validation
   */
  static validateApiKey(apiKey: string): ValidationResult {
    const errors: string[] = [];

    if (!apiKey || apiKey.length < 10) {
      errors.push('API key must be at least 10 characters long');
    }

    if (apiKey.length > 200) {
      errors.push('API key is too long');
    }

    // Check for suspicious patterns
    if (/[<>'"&]/.test(apiKey)) {
      errors.push('API key contains invalid characters');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: apiKey.trim()
    };
  }

  /**
   * Webhook URL validation
   */
  static validateWebhookUrl(url: string): ValidationResult {
    const errors: string[] = [];

    if (!url) {
      return { isValid: true, errors: [], sanitizedValue: '' };
    }

    try {
      const parsedUrl = new URL(url);
      
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        errors.push('Webhook URL must use HTTP or HTTPS protocol');
      }

      if (parsedUrl.hostname === 'localhost' || parsedUrl.hostname === '127.0.0.1') {
        errors.push('Localhost URLs are not allowed for webhooks');
      }

    } catch {
      errors.push('Invalid webhook URL format');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: SANITIZERS.url(url)
    };
  }
}

// Export convenience functions
export const validateField = DataValidator.validateField.bind(DataValidator);
export const validateFields = DataValidator.validateFields.bind(DataValidator);
export const validateTradeData = DataValidator.validateTradeData.bind(DataValidator);
export const validateUserProfile = DataValidator.validateUserProfile.bind(DataValidator);
export const validateRobotData = DataValidator.validateRobotData.bind(DataValidator);
export const validateApiKey = DataValidator.validateApiKey.bind(DataValidator);
export const validateWebhookUrl = DataValidator.validateWebhookUrl.bind(DataValidator);
