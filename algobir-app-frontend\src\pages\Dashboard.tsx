import React from 'react';
import { Box, SimpleGrid, VStack } from '@chakra-ui/react';
import {
  Md<PERSON><PERSON><PERSON><PERSON><PERSON>,
  MdShowChart,
  MdCheckCircle,
  MdHourglassEmpty,
  MdToday,
  MdSecurity
} from 'react-icons/md';
import { FiClock, FiTrendingUp, FiActivity, FiTarget, FiBarChart } from 'react-icons/fi';
import { useDashboardData } from '../hooks/useDashboardData';
import RecentTradesTable from '../components/dashboard/RecentTradesTable';
import TradingStatCard from '../components/dashboard/TradingStatCard';
import ApiExpiryCard from '../components/dashboard/ApiExpiryCard';
import LoadingState from '../components/LoadingState';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const {
    loading,
    error,
    totalInvestment,
    totalPnL,
    openPositionsCount,
    totalTradesCount,
    winRate,
    last10ClosedTrades,
    todayTradesCount,
    lastTradeTime,
    apiCredentialsExpiryDate
  } = useDashboardData();



  if (loading) {
    return <LoadingState />;
  }

  // Format currency
  const formatCurrency = (value: number) => {
    return `₺${value.toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // Format last trade time
  const formatLastTradeTime = (dateString: string | null) => {
    if (!dateString) return 'Henüz işlem yok';
    try {
      return format(new Date(dateString), 'd MMMM yyyy, HH:mm', { locale: tr });
    } catch {
      return 'Geçersiz tarih';
    }
  };



  return (
    <Box>
      {/* Dashboard Cards - Fixed 4-4-4 Layout */}
      <VStack spacing="24px" mb="24px" data-testid="dashboard-cards">
        {/* First Row - 4 Cards */}
        <SimpleGrid
          columns={{ base: 1, sm: 2, md: 4 }}
          gap={{ base: '16px', md: '24px' }}
          w="100%"
          maxW="1400px"
          mx="auto"
        >
        {/* 1. Total Investment Card */}
        <TradingStatCard
          title="Toplam Yatırım"
          value={formatCurrency(totalInvestment)}
          icon={MdAttachMoney}
          colorScheme="blue"
          isLoading={loading}
          isError={!!error}
          errorMessage="Yatırım verisi yüklenemedi"
          helpText="Toplam yatırım miktarınız"
        />

        {/* 2. Total Profit/Loss Card */}
        <TradingStatCard
          title="Toplam Kâr/Zarar"
          value={formatCurrency(totalPnL)}
          icon={MdShowChart}
          colorScheme={totalPnL >= 0 ? 'green' : 'red'}
          isLoading={loading}
          isError={!!error}
          errorMessage="K/Z verisi yüklenemedi"
          helpText="Genel kar/zarar durumunuz"
          badge={!error && totalPnL >= 0 ? { text: 'KAR', colorScheme: 'green' } : !error ? { text: 'ZARAR', colorScheme: 'red' } : undefined}
        />

        {/* 3. Today's Trade Count Card */}
        <TradingStatCard
          title="Bugünkü İşlemler"
          value={todayTradesCount.toString()}
          icon={MdToday}
          colorScheme="purple"
          isLoading={loading}
          isError={!!error}
          errorMessage="Günlük veri yüklenemedi"
          helpText="Bugün gerçekleştirilen işlem sayısı"
        />

        {/* 4. Open Positions Card */}
        <TradingStatCard
          title="Açık Pozisyonlar"
          value={openPositionsCount.toString()}
          icon={MdHourglassEmpty}
          colorScheme="orange"
          isLoading={loading}
          isError={!!error}
          errorMessage="Pozisyon verisi yüklenemedi"
          helpText="Şu anda açık olan pozisyon sayısı"
          isClickable={!error && openPositionsCount > 0}
          onClick={() => !error && openPositionsCount > 0 && navigate('/open-positions')}
        />
        </SimpleGrid>

        {/* Second Row - 4 Cards */}
        <SimpleGrid
          columns={{ base: 1, sm: 2, md: 4 }}
          gap={{ base: '16px', md: '24px' }}
          w="100%"
          maxW="1400px"
          mx="auto"
        >
        {/* 5. Overall Win Rate Card */}
        <TradingStatCard
          title="Kazanma Oranı"
          value={`${winRate.toFixed(1)}%`}
          icon={MdCheckCircle}
          colorScheme={winRate >= 60 ? 'green' : winRate >= 40 ? 'yellow' : 'red'}
          isLoading={loading}
          isError={!!error}
          errorMessage="Oran verisi yüklenemedi"
          helpText="Genel başarı oranınız"
        />

        {/* 6. Last Trade Time Card */}
        <TradingStatCard
          title="Son İşlem Zamanı"
          value={formatLastTradeTime(lastTradeTime)}
          icon={FiClock}
          colorScheme="teal"
          isLoading={loading}
          isError={!!error}
          errorMessage="Zaman verisi yüklenemedi"
          helpText="En son gerçekleştirilen işlem zamanı"
        />

        {/* 7. API Credentials Expiry Card */}
        <ApiExpiryCard
          apiCredentialsExpiryDate={apiCredentialsExpiryDate}
          isLoading={loading}
          onConfigureClick={() => navigate('/management')}
        />

        {/* 8. Portfolio Performance Card */}
        <TradingStatCard
          title="Portföy Performansı"
          value={totalInvestment > 0 ? `${((totalPnL / totalInvestment) * 100).toFixed(2)}%` : '0%'}
          icon={FiTrendingUp}
          colorScheme={totalPnL >= 0 ? 'green' : 'red'}
          isLoading={loading}
          isError={!!error}
          errorMessage="Performans verisi yüklenemedi"
          helpText="Yatırım getiri oranınız"
        />
        </SimpleGrid>

        {/* Third Row - 4 Cards */}
        <SimpleGrid
          columns={{ base: 1, sm: 2, md: 4 }}
          gap={{ base: '16px', md: '24px' }}
          w="100%"
          maxW="1400px"
          mx="auto"
        >
        {/* 9. Average Trade Performance */}
        <TradingStatCard
          title="Ortalama İşlem"
          value={totalTradesCount > 0 ? formatCurrency(totalPnL / totalTradesCount) : formatCurrency(0)}
          icon={FiTarget}
          colorScheme="cyan"
          isLoading={loading}
          isError={!!error}
          errorMessage="Ortalama verisi yüklenemedi"
          helpText="İşlem başına ortalama kar/zarar"
        />

        {/* 10. Trading Activity */}
        <TradingStatCard
          title="İşlem Aktivitesi"
          value={totalTradesCount.toString()}
          icon={FiActivity}
          colorScheme="pink"
          isLoading={loading}
          isError={!!error}
          errorMessage="Aktivite verisi yüklenemedi"
          helpText="Toplam işlem sayısı"
          isClickable={!error}
          onClick={() => !error && navigate('/trades')}
        />

        {/* 11. Risk Level Indicator */}
        <TradingStatCard
          title="Risk Seviyesi"
          value={openPositionsCount > 5 ? 'Yüksek' : openPositionsCount > 2 ? 'Orta' : 'Düşük'}
          icon={MdSecurity}
          colorScheme={openPositionsCount > 5 ? 'red' : openPositionsCount > 2 ? 'yellow' : 'green'}
          isLoading={loading}
          isError={!!error}
          errorMessage="Risk verisi yüklenemedi"
          helpText="Mevcut risk seviyeniz"
        />

        {/* 12. Total Trades Count */}
        <TradingStatCard
          title="Toplam İşlem Sayısı"
          value={totalTradesCount.toString()}
          icon={FiBarChart}
          colorScheme="indigo"
          isLoading={loading}
          isError={!!error}
          errorMessage="İşlem sayısı yüklenemedi"
          helpText="Şimdiye kadar yapılan toplam işlem"
        />
        </SimpleGrid>
      </VStack>

      {/* Recent Trades Table */}
      <Box mt={8}>
        <RecentTradesTable trades={last10ClosedTrades} />
      </Box>
    </Box>
  );
};

export default Dashboard; 