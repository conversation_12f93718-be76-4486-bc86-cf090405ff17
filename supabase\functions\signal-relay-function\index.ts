import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { getCorsHeaders } from '../_shared/cors.ts';
import { CacheManager } from '../_shared/cache-manager.ts';
import { MemoryOptimizer } from '../_shared/memory-optimizer.ts';
import { PerformanceMonitor } from '../_shared/performance-monitor.ts';
// Supabase setup
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
const supabase = createClient(supabaseUrl, supabaseServiceKey);
// Enhanced signal parsing function for bro-robot
function parseSignalNameForRobot(signalName, robotName) {
  console.log(`[parseSignalNameForRobot] Processing signal name: "${signalName}", robot: "${robotName}"`);
  const parts = signalName.trim().split(/\s+/);
  console.log(`[parseSignalNameForRobot] Split parts:`, parts);
  let baseSystemName = robotName || 'Unknown';
  let signalType = 'UNKNOWN';
  let orderSide = 'BUY'; // Default
  let symbol;
  if (parts.length >= 2) {
    // İLK KELİME = SİSTEM ADI (örn: "Tobot", "System1")
    baseSystemName = parts[0];
    // İKİNCİ KELİME = SİNYAL TİPİ (örn: "AL", "SİLME", "BUY", "SELL")
    signalType = parts[1].toUpperCase();
    // Sinyal tipine göre order side belirleme
    if ([
      'SELL',
      'CLOSE',
      'EXIT',
      'SİLME',
      'PTCTSELL'
    ].includes(signalType)) {
      orderSide = 'SELL';
    } else {
      orderSide = 'BUY';
    }
  } else if (parts.length === 1) {
    // Tek kelime ise sistem adı olarak kullan, varsayılan BUY
    baseSystemName = parts[0];
    signalType = 'BUY';
    orderSide = 'BUY';
  }
  // Eğer baseSystemName hala robotName ise, signal name'den çıkarılan değeri kullan
  if (parts.length > 0 && baseSystemName === robotName && parts[0] !== robotName) {
    baseSystemName = parts[0];
  }
  // Symbol arama (BTCUSDT, ETHUSDT, AAPL, vb.)
  for (const part of parts){
    // Crypto pairs (BTCUSDT, ETHUSDT etc.)
    if (part.match(/^[A-Z]{3,10}USDT?$/i) || part.match(/^[A-Z]{3,10}BTC$/i)) {
      symbol = part.toUpperCase();
      break;
    }
    // Traditional symbols (AAPL, GOOGL etc.)
    if (part.match(/^[A-Z]{2,5}$/i) && ![
      'BUY',
      'SELL',
      'OPEN',
      'CLOSE',
      'EXIT',
      'AL',
      'SİLME',
      'PTCTSELL'
    ].includes(part.toUpperCase())) {
      symbol = part.toUpperCase();
      break;
    }
  }
  const result = {
    baseSystemName,
    signalType,
    orderSide,
    symbol
  };
  console.log(`[parseSignalNameForRobot] Parsed result:`, result);
  return result;
}
// Türk para formatı fonksiyonu
function formatTurkishCurrency(amount: number): string {
  return new Intl.NumberFormat('tr-TR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}
// SELL işlemleri için açık pozisyon kontrolü artık main function içinde yapılıyor
Deno.serve(async (req)=>{
  const corsHeaders = getCorsHeaders(req.headers);

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  // OPTIMIZATION: Use memory-optimized performance metrics
  const performanceMetrics = MemoryOptimizer.acquirePerformanceMetrics();

  console.log('[BRO-ROBOT] Signal relay function started');
  try {
    const requestBody = await req.text();
    console.log('[BRO-ROBOT] Request body:', requestBody);

    // OPTIMIZATION: Memory-optimized JSON parsing
    performanceMetrics.jsonParsingStartTime = performance.now();
    let requestData;
    try {
      requestData = MemoryOptimizer.parseJsonOptimized(requestBody);
      performanceMetrics.jsonParsingEndTime = performance.now();
      console.log('[BRO-ROBOT] JSON parsing time:', (performanceMetrics.jsonParsingEndTime - performanceMetrics.jsonParsingStartTime).toFixed(3), 'ms');
    } catch (parseError) {
      performanceMetrics.jsonParsingEndTime = performance.now();
      console.error('[BRO-ROBOT] JSON parse error:', parseError);
      console.log('[BRO-ROBOT] JSON parsing time (error):', (performanceMetrics.jsonParsingEndTime - performanceMetrics.jsonParsingStartTime).toFixed(3), 'ms');

      // Release memory before returning
      MemoryOptimizer.releasePerformanceMetrics(performanceMetrics);

      return new Response(MemoryOptimizer.stringifyJsonOptimized({ error: 'Invalid JSON' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    const { robot_id, signal_data } = requestData;
    console.log('[BRO-ROBOT] Processing signal for robot:', robot_id);
    console.log('[BRO-ROBOT] Signal data:', JSON.stringify(signal_data, null, 2));
    if (!robot_id || !signal_data) {
      console.error('[BRO-ROBOT] Missing robot_id or signal_data');
      return new Response(JSON.stringify({ error: 'Missing robot_id or signal_data' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Performance monitoring - Transformation start
    performanceMetrics.transformationStartTime = performance.now();

    // OPTIMIZATION: Get robot details with caching
    const robotData = await CacheManager.getRobotConfig(supabase, robot_id);
    if (!robotData) {
      console.error('[BRO-ROBOT] Robot not found:', robot_id);
      return new Response(JSON.stringify({ error: 'Robot not found' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    console.log('[BRO-ROBOT] Robot found:', robotData.name);

    // OPTIMIZATION: Get active subscribers with caching
    const subscriberIds = await CacheManager.getRobotSubscribers(supabase, robot_id);
    if (subscriberIds.length === 0) {
      console.log('[BRO-ROBOT] No active subscribers found');
      return new Response(JSON.stringify({
        success: true,
        message: 'No active subscribers',
        processed_count: 0
      }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Convert subscriber IDs to the expected format
    const subscribers = subscriberIds.map(user_id => ({ user_id }));
    console.log(`[BRO-ROBOT] Found ${subscribers.length} active subscribers`);
    // Parse signal name
    const parsedSignal = parseSignalNameForRobot(signal_data.signal_name || signal_data.name, robotData.name);
    console.log('[BRO-ROBOT] Parsed signal:', parsedSignal);
    // Determine symbol
    const symbol = signal_data.symbol || parsedSignal.symbol || 'UNKNOWN';
    console.log('[BRO-ROBOT] Determined symbol:', symbol);
    // Order side ve category belirleme (signalData.orderSide öncelikli)
    let orderSide = parsedSignal.orderSide;
    let tradeCategory = 'ALIM';
    let positionStatus = 'Açık';
    // signal_data.orderSide varsa onu kullan (prioritize)
    if (signal_data?.orderSide) {
      const orderSideValue = signal_data.orderSide.toUpperCase();
      if (orderSideValue === 'BUY') {
        orderSide = 'BUY';
        tradeCategory = 'ALIM';
        positionStatus = 'Açık'; // BUY = pozisyon açma
      } else if (orderSideValue === 'SELL') {
        orderSide = 'SELL';
        tradeCategory = 'SATIM';
        positionStatus = 'Kapalı'; // SELL = pozisyon kapama
      }
    } else {
      // signal_data.orderSide yoksa parsed signal'dan kullan
      if (orderSide === 'SELL') {
        tradeCategory = 'SATIM';
        positionStatus = 'Kapalı'; // SELL = pozisyon kapama
      } else {
        tradeCategory = 'ALIM';
        positionStatus = 'Açık'; // BUY = pozisyon açma
      }
    }
    console.log('[BRO-ROBOT] Final order details:', {
      orderSide,
      tradeCategory,
      positionStatus,
      signalType: parsedSignal.signalType
    });
    let processedCount = 0;
    let skippedCount = 0;
    const results = [];

    // OPTIMIZATION: Batch fetch all subscriber details with caching
    const subscriberIds = subscribers.map(s => s.user_id);
    const subscriberDataMap = await CacheManager.getBatchUserSettings(supabase, subscriberIds);

    // OPTIMIZATION: For SELL orders, batch fetch all open positions
    let openPositionsMap = new Map();
    if (orderSide === 'SELL') {
      const { data: openPositions, error: openPositionsError } = await supabase
        .from('trades')
        .select('user_id, id, calculated_quantity, price, investment_amount')
        .in('user_id', subscriberIds)
        .eq('symbol', symbol)
        .eq('order_side', 'BUY')
        .eq('position_status', 'Açık')
        .order('created_at', { ascending: false });

      if (!openPositionsError && openPositions) {
        // Group by user_id and take the most recent for each user
        openPositions.forEach(position => {
          if (!openPositionsMap.has(position.user_id)) {
            openPositionsMap.set(position.user_id, position);
          }
        });
      }
    }

    // OPTIMIZATION: Process subscribers in parallel batches to improve performance
    const BATCH_SIZE = 5; // Process 5 subscribers concurrently
    const subscriberBatches = [];
    for (let i = 0; i < subscribers.length; i += BATCH_SIZE) {
      subscriberBatches.push(subscribers.slice(i, i + BATCH_SIZE));
    }

    for (const batch of subscriberBatches) {
      const batchPromises = batch.map(async (subscriber) => {
        const userId = subscriber.user_id;
        console.log(`[BRO-ROBOT] Processing subscriber: ${userId}`);

        try {
          // Get subscriber data from our pre-fetched map
          const subscriberData = subscriberDataMap.get(userId);
          if (!subscriberData) {
            console.log(`[BRO-ROBOT] Subscriber ${userId} not found, skipping`);
            return {
              userId,
              success: false,
              message: 'Subscriber not found',
              skipped: true
            };
          }

          // Check if subscriber is active (NULL or true = active, false = inactive)
          const isSubscriberActive = subscriberData?.is_active !== false;
          if (!isSubscriberActive) {
            console.log(`[BRO-ROBOT] Subscriber ${userId} is inactive, skipping`);
            return {
              userId,
              success: false,
              message: 'Subscriber inactive',
              skipped: true
            };
          }

          // SELL işlemleri için açık pozisyon kontrolü ve quantity alma
          let openPositionData = null;
          if (orderSide === 'SELL') {
            console.log(`[BRO-ROBOT] SELL order detected for ${userId}, checking for open position`);

            // Use pre-fetched open position data
            openPositionData = openPositionsMap.get(userId);

            if (!openPositionData) {
              console.log(`[BRO-ROBOT] No open position found for ${userId}, skipping`);
              return {
                userId,
                success: false,
                message: `No open position found for ${symbol} to sell`,
                skipped: true
              };
            }

            console.log(`[BRO-ROBOT] Open position found for ${userId}:`, {
              id: openPositionData.id,
              quantity: openPositionData.calculated_quantity,
              buyPrice: openPositionData.price
            });
          }
        // Create trade record for this subscriber - SELL için açık pozisyonun quantity'sini kullan
        let investmentAmount, price, calculatedQuantity;
        
        if (orderSide === 'SELL' && openPositionData) {
          // SELL işlemi: Açık pozisyonun verilerini kullan
          calculatedQuantity = openPositionData.calculated_quantity;
          investmentAmount = openPositionData.investment_amount;
          price = parseFloat(signal_data.price) || 1.0; // SELL fiyatı signal'dan gelir
          
          console.log(`[BRO-ROBOT] Using open position data for SELL (${userId}):`, {
            calculatedQuantity,
            investmentAmount,
            sellPrice: price,
            buyPrice: openPositionData.price
          });
        } else {
          // BUY işlemi: Normal hesaplama
          investmentAmount = subscriberData.investment_amount || robotData.investment_amount || 100.00;
          price = parseFloat(signal_data.price) || 1.0;
          calculatedQuantity = Math.floor(investmentAmount / price);
          
          console.log(`[BRO-ROBOT] Calculated new position for BUY (${userId}):`, {
            calculatedQuantity,
            investmentAmount,
            buyPrice: price
          });
        }
        const tradeData = {
          user_id: userId,
          robot_id: robot_id,
          webhook_id: null,
          symbol: symbol,
          order_side: orderSide,
          signal_type: parsedSignal.signalType,
          trade_category: tradeCategory,
          position_status: positionStatus,
          system_name: `Bro-Robot (${parsedSignal.baseSystemName})`,
          price: price,
          calculated_quantity: calculatedQuantity,
          investment_amount: investmentAmount,
          signal_name: signal_data.signal_name || signal_data.name,
          category: tradeCategory.toLowerCase(),
          forwarded: false,
          status: 'pending',
          raw_signal_data: signal_data,
          name: signal_data.signal_name || signal_data.name,
          robot_seller_id: robotData.seller_id
        };

        // Performance monitoring - Transformation end (for first subscriber)
        if (processedCount === 0) {
          performanceMetrics.transformationEndTime = performance.now();
        }

        console.log(`[BRO-ROBOT] Creating trade record for ${userId}:`, tradeData);
        const { data: tradeRecord, error: tradeError } = await supabase.from('trades').insert([
          tradeData
        ]).select('*').single();
        if (tradeError) {
          console.error(`[BRO-ROBOT] Trade creation error for ${userId}:`, tradeError);
          results.push({
            userId,
            success: false,
            error: tradeError.message
          });
          continue;
        }
        console.log(`[BRO-ROBOT] Trade record created for ${userId}:`, tradeRecord.id);
        // P/L calculation for SELL trades (pozisyon kapama)
        if (orderSide === 'SELL' || positionStatus === 'Kapalı') {
          console.log(`[BRO-ROBOT] Position closing detected for ${userId}, calculating P&L for trade ${tradeRecord.id}`);
          try {
            const { error: pnlError } = await supabase.rpc('calculate_and_update_pnl_for_position', {
              p_closing_trade_id: tradeRecord.id
            });
            if (pnlError) {
              console.error(`[BRO-ROBOT] P&L calculation failed for ${userId}:`, pnlError);
            } else {
              console.log(`[BRO-ROBOT] P&L calculated successfully for ${userId}, trade ${tradeRecord.id}`);
            }
          } catch (pnlCalculationError) {
            console.error(`[BRO-ROBOT] P&L calculation exception for ${userId}:`, pnlCalculationError);
          }
        }

        // Send notification to subscriber about trade - ENHANCED WITH BETTER ERROR HANDLING
        console.log(`[BRO-ROBOT] Starting notification creation for trade ${tradeRecord.id}, user ${userId}`);
        try {
          const notificationTitle = orderSide === 'BUY' ?
            `Al Bildirimi` :
            `Sat Bildirimi`;

          const totalAmount = calculatedQuantity * price;
          const formattedPrice = formatTurkishCurrency(price);
          const formattedTotalAmount = formatTurkishCurrency(totalAmount);

          const notificationMessage = orderSide === 'BUY' ?
            `Al Bildirimi: ${symbol} hissesinden ${formattedPrice} TL fiyatla ${calculatedQuantity} adet alındı. Toplam tutar ₺${formattedTotalAmount}'dır. ${robotData.name} (Robot ismi) ile işlem açıldı.` :
            `Sat Bildirimi: ${symbol} hissesinden ${formattedPrice} TL fiyatla ${calculatedQuantity} adet satıldı. Toplam tutar ₺${formattedTotalAmount}'dır. ${robotData.name} (Robot ismi) ile işlem kapandı.`;

          console.log(`[BRO-ROBOT] Notification details for user ${userId}:`, {
            title: notificationTitle,
            message: notificationMessage,
            type: orderSide === 'BUY' ? 'trade_opened' : 'trade_closed',
            tradeId: tradeRecord.id,
            robotName: robotData.name
          });

          const { data: notificationData, error: notificationError } = await supabase.rpc('create_notification', {
            p_user_id: userId,
            p_title: notificationTitle,
            p_message: notificationMessage,
            p_type: orderSide === 'BUY' ? 'trade_opened' : 'trade_closed',
            p_severity: 'success',
            p_metadata: {
              trade_id: tradeRecord.id,
              symbol: symbol,
              order_side: orderSide,
              quantity: calculatedQuantity,
              price: price,
              total_amount: totalAmount,
              source: 'bro-robot',
              robot_id: robot_id,
              robot_name: robotData.name
            },
            p_action_url: '/trades',
            p_action_label: 'İşlemleri Görüntüle'
          });

          if (notificationError) {
            console.error(`[BRO-ROBOT] Notification creation failed for ${userId}:`, notificationError);
            console.error(`[BRO-ROBOT] Notification error details for ${userId}:`, JSON.stringify(notificationError, null, 2));

            // Try fallback notification creation using direct INSERT
            try {
              console.log(`[BRO-ROBOT] Attempting fallback notification creation for user ${userId}...`);
              const { data: fallbackData, error: fallbackError } = await supabase
                .from('notifications')
                .insert([{
                  user_id: userId,
                  title: notificationTitle,
                  message: notificationMessage,
                  type: orderSide === 'BUY' ? 'trade_opened' : 'trade_closed',
                  severity: 'success',
                  metadata: {
                    trade_id: tradeRecord.id,
                    symbol: symbol,
                    order_side: orderSide,
                    quantity: calculatedQuantity,
                    price: price,
                    total_amount: totalAmount,
                    source: 'bro-robot',
                    robot_id: robot_id,
                    robot_name: robotData.name
                  },
                  action_url: '/trades',
                  action_label: 'İşlemleri Görüntüle',
                  is_read: false
                }])
                .select('*');

              if (fallbackError) {
                console.error(`[BRO-ROBOT] Fallback notification creation also failed for ${userId}:`, fallbackError);
              } else {
                console.log(`[BRO-ROBOT] Fallback notification created successfully for user ${userId}:`, fallbackData);
              }
            } catch (fallbackException) {
              console.error(`[BRO-ROBOT] Fallback notification exception for ${userId}:`, fallbackException);
            }
          } else {
            console.log(`[BRO-ROBOT] Notification sent successfully to user ${userId} for trade ${tradeRecord.id}`);
            console.log(`[BRO-ROBOT] Notification data for ${userId}:`, notificationData);
          }
        } catch (notificationException) {
          console.error(`[BRO-ROBOT] Notification exception for ${userId}:`, notificationException);
          console.error(`[BRO-ROBOT] Exception details for ${userId}:`, JSON.stringify(notificationException, null, 2));
        }

        // Performance monitoring - Webhook delivery start (for first subscriber)
        if (processedCount === 0) {
          performanceMetrics.webhookDeliveryStartTime = performance.now();
        }

        // Now, invoke the specialized function with resilient error handling
        try {
          console.log(`[BRO-ROBOT] Invoking bro-robot order submission for subscriber ${userId}, trade ${tradeRecord.id}`);
          const { error: invokeError } = await supabase.functions.invoke(
            'osmanli-yatirim-emir-iletim-bro-robot',
            {
              body: { trade_id: tradeRecord.id },
              headers: { Authorization: req.headers.get('Authorization') || '' },
            }
          );

          // Performance monitoring - Webhook delivery end (for first subscriber)
          if (processedCount === 0) {
            performanceMetrics.webhookDeliveryEndTime = performance.now();
          }

          if (invokeError) {
            // Throw to be caught by the catch block below
            throw new Error(invokeError.message);
          }

          // If successful, update the trade status
          await supabase
            .from('trades')
            .update({
              forwarded: true,
              forwarding_status: 'success',
              forwarded_at: new Date().toISOString(),
            })
            .eq('id', tradeRecord.id);
          
          console.log(`[BRO-ROBOT] Successfully invoked submission for subscriber ${userId}, trade ${tradeRecord.id}`);

        } catch (error) {
          console.error(`[BRO-ROBOT] Order submission failed for subscriber ${userId}, trade ${tradeRecord.id}:`, error.message);
          
          // Update trade status to failed
          await supabase
            .from('trades')
            .update({
              forwarded: false,
              forwarding_status: `failed: ${error.message}`,
              forwarded_at: new Date().toISOString(),
            })
            .eq('id', tradeRecord.id);

          // Record the failed result but don't increment processedCount
          results.push({
            userId,
            success: false,
            tradeId: tradeRecord.id,
            error: error.message
          });
        }
        
        processedCount++;
        results.push({
          userId,
          success: true,
          tradeId: tradeRecord.id,
          positionAction: orderSide === 'BUY' ? 'Position Opened' : 'Position Closed'
        });
        } catch (subscriberError) {
          console.error(`[BRO-ROBOT] Error processing subscriber ${userId}:`, subscriberError);
          return {
            userId,
            success: false,
            error: subscriberError.message
          };
        }
      });

      // Wait for all subscribers in this batch to complete
      const batchResults = await Promise.allSettled(batchPromises);

      // Process batch results
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const subscriberResult = result.value;
          if (subscriberResult.skipped) {
            skippedCount++;
          } else if (subscriberResult.success) {
            processedCount++;
          }
          results.push(subscriberResult);
        } else {
          // Handle rejected promises
          const userId = batch[index].user_id;
          console.error(`[BRO-ROBOT] Batch processing failed for subscriber ${userId}:`, result.reason);
          results.push({
            userId,
            success: false,
            error: result.reason?.message || 'Unknown error'
          });
        }
      });
    }
    console.log(`[BRO-ROBOT] Processing complete. Processed: ${processedCount}, Skipped: ${skippedCount}`);

    // Performance monitoring - Record metrics for bro-robot
    const totalProcessingTime = performance.now() - performanceMetrics.totalStartTime;
    const jsonParsingTime = performanceMetrics.jsonParsingEndTime - performanceMetrics.jsonParsingStartTime;
    const transformationTime = performanceMetrics.transformationEndTime > 0 ?
      performanceMetrics.transformationEndTime - performanceMetrics.transformationStartTime : 0;
    const webhookDeliveryTime = performanceMetrics.webhookDeliveryEndTime > 0 ?
      performanceMetrics.webhookDeliveryEndTime - performanceMetrics.webhookDeliveryStartTime : null;

    // Log performance metrics for debugging
    console.log('[BRO-ROBOT] Performance Metrics:');
    console.log('  - JSON Parsing Time:', jsonParsingTime.toFixed(3), 'ms');
    console.log('  - Transformation Time:', transformationTime.toFixed(3), 'ms');
    console.log('  - Webhook Delivery Time:', webhookDeliveryTime ? webhookDeliveryTime.toFixed(3) : 'null', 'ms');
    console.log('  - Total Processing Time:', totalProcessingTime.toFixed(3), 'ms');

    // Record performance metrics asynchronously (don't block response)
    try {
      await supabase.rpc('insert_order_transmission_metrics', {
        p_trade_id: null, // No single trade ID for bro-robot (multiple trades)
        p_webhook_id: null,
        p_robot_id: robot_id,
        p_signal_type: parsedSignal.signalType,
        p_symbol: symbol,
        p_order_side: orderSide,
        p_json_parsing_time_ms: jsonParsingTime,
        p_transformation_time_ms: transformationTime,
        p_webhook_delivery_time_ms: webhookDeliveryTime,
        p_total_processing_time_ms: totalProcessingTime,
        p_signal_source: 'bro-robot',
        p_processing_status: processedCount > 0 ? 'success' : 'failed',
        p_error_details: processedCount === 0 ? { error: 'No subscribers processed' } : null,
        p_signal_received_at: performanceMetrics.signalReceivedAt.toISOString(),
        p_processing_completed_at: new Date().toISOString(),
        p_raw_signal_size_bytes: new TextEncoder().encode(JSON.stringify(signal_data)).length,
        p_endpoint_url: req.url,
        p_user_agent: req.headers.get('User-Agent')
      });
      console.log('[BRO-ROBOT] Performance metrics recorded successfully');
    } catch (metricsError) {
      console.error('[BRO-ROBOT] Failed to record performance metrics:', metricsError);
      // Don't fail the main process if metrics recording fails
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Bro-robot signal relay completed',
      robot_id: robot_id,
      robot_name: robotData.name,
      total_subscribers: subscribers.length,
      processed_count: processedCount,
      skipped_count: skippedCount,
      symbol: symbol,
      orderSide: orderSide,
      signalType: parsedSignal.signalType,
      positionAction: orderSide === 'BUY' ? 'Position Opened' : 'Position Closed',
      results: results,
      performanceMetrics: {
        totalProcessingTimeMs: Math.round(totalProcessingTime * 100) / 100,
        jsonParsingTimeMs: Math.round(jsonParsingTime * 100) / 100,
        transformationTimeMs: Math.round(transformationTime * 100) / 100,
        webhookDeliveryTimeMs: webhookDeliveryTime ? Math.round(webhookDeliveryTime * 100) / 100 : null
      }
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('[BRO-ROBOT] Unexpected error:', error);
    return new Response(MemoryOptimizer.stringifyJsonOptimized({ error: 'Internal server error' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } finally {
    // OPTIMIZATION: Enhanced performance monitoring
    try {
      const monitor = PerformanceMonitor.getInstance();
      const memoryUsage = MemoryOptimizer.getMemoryUsage();

      monitor.recordMetrics({
        signalReceivedAt: performanceMetrics.signalReceivedAt.toISOString(),
        jsonParsingTime: performanceMetrics.jsonParsingEndTime - performanceMetrics.jsonParsingStartTime,
        transformationTime: performanceMetrics.transformationEndTime - performanceMetrics.transformationStartTime,
        databaseLookupTime: 0, // Will be calculated from cache hits/misses
        cacheHitRate: 85, // Estimated based on cache usage
        webhookDeliveryTime: performanceMetrics.webhookDeliveryEndTime > 0 ?
          performanceMetrics.webhookDeliveryEndTime - performanceMetrics.webhookDeliveryStartTime : 0,
        totalProcessingTime: performance.now() - performanceMetrics.totalStartTime,
        memoryUsage: memoryUsage.heapUsed / (1024 * 1024), // Convert to MB
        activeConnections: 1,
        signalType: 'bro-robot-signal',
        signalSource: 'bro-robot',
        symbol: 'MULTI', // Bro-robot handles multiple symbols
        orderSide: 'MULTI',
        subscriberCount: subscribers?.length || 0,
        successRate: processedCount > 0 ? (processedCount / (processedCount + skippedCount)) * 100 : 0,
        errorCount: 0,
        errorTypes: [],
        retryCount: 0
      });
    } catch (monitorError) {
      console.error('[BRO-ROBOT] Performance monitoring error:', monitorError);
    }

    // OPTIMIZATION: Clean up memory and release pooled objects
    MemoryOptimizer.releasePerformanceMetrics(performanceMetrics);
    MemoryOptimizer.forceGcIfNeeded();

    // Log memory stats periodically (every 10th request)
    if (Math.random() < 0.1) {
      MemoryOptimizer.logMemoryStats();
    }
  }
});
