// supabase/functions/seller-signal-endpoint/index.ts
// BRO-ROBOT FLOW (Part 1): Satıcı robot sinyallerini alır ve aboneler için signal-relay-function'ı tetikler
// NOT: Bu endpoint webhook endpoint'i olduğu için JWT doğrulaması YAPMAZ - verify_jwt: false olarak deploy edilmelidir

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.8'
import { getCorsHeaders } from '../_shared/cors.ts'

console.log('--- seller-signal-endpoint: BRO-ROBOT Flow (Part 1) Handler starting ---')

// Rate limiting map - Basit in-memory rate limiting
const rateLimitMap = new Map()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 dakika
const RATE_LIMIT_MAX_REQUESTS = 30 // Dakikada maksimum 30 istek

// Rate limiting kontrolü
function checkRateLimit(identifier: string): boolean {
  const now = Date.now()
  const current = rateLimitMap.get(identifier) || {
    count: 0,
    lastReset: now
  }
  
  // Zaman penceresi geçmişse sıfırla
  if (now - current.lastReset > RATE_LIMIT_WINDOW) {
    current.count = 1
    current.lastReset = now
  } else {
    current.count += 1
  }
  
  rateLimitMap.set(identifier, current)
  return current.count <= RATE_LIMIT_MAX_REQUESTS
}

// Robot validation based solely on robot_id
async function validateRobotById(supabaseAdmin: any, robotId: string) {
  console.log(`[seller-signal-endpoint] Validating robot ${robotId}`)
  
  // Robot ID formatını kontrol et
  if (!robotId || !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(robotId)) {
    console.error(`[seller-signal-endpoint] Invalid Robot ID format: ${robotId}`)
    return {
      valid: false,
      sellerId: null,
      error: 'Invalid Robot ID format'
    }
  }
  
  try {
    // Robot bilgilerini al
    const { data: robot, error: robotError } = await supabaseAdmin
      .from('robots')
      .select('id, name, seller_id, is_public, is_emergency_stopped, deleted_at')
      .eq('id', robotId)
      .is('deleted_at', null)
      .single()
    
    if (robotError || !robot) {
      console.error(`[seller-signal-endpoint] Robot not found: ${robotId}`, robotError?.message)
      return {
        valid: false,
        sellerId: null,
        error: 'Robot not found or inactive'
      }
    }
    
    // Robot durumu kontrolü
    if (robot.is_emergency_stopped) {
      console.warn(`[seller-signal-endpoint] Robot ${robotId} is emergency stopped`)
      return {
        valid: false,
        sellerId: robot.seller_id,
        error: 'Robot is currently in emergency stop mode'
      }
    }
    
    if (!robot.is_public) {
      console.warn(`[seller-signal-endpoint] Robot ${robotId} is not public`)
      return {
        valid: false,
        sellerId: robot.seller_id,
        error: 'Robot is not publicly available'
      }
    }
    
    console.log(`[seller-signal-endpoint] Robot ${robotId} successfully validated`)
    return {
      valid: true,
      sellerId: robot.seller_id,
      robot: robot
    }
  } catch (error) {
    console.error(`[seller-signal-endpoint] Validation error for robot ${robotId}:`, error)
    return {
      valid: false,
      sellerId: null,
      error: `Validation error: ${error.message}`
    }
  }
}

serve(async (req) => {
  const functionStartTime = Date.now()
  console.log(`--- seller-signal-endpoint: REQUEST RECEIVED ${req.method} ${req.url} ---`)
  
  // İstek kaynağını al
  const clientIP = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'
  
  // Dinamik CORS başlıklarını oluştur
  const corsHeaders = getCorsHeaders(req.headers)
  const responseHeaders = new Headers(corsHeaders)
  responseHeaders.set('Content-Type', 'application/json')
  
  // CORS OPTIONS isteğini işle
  if (req.method === 'OPTIONS') {
    console.log('[seller-signal-endpoint] Handling OPTIONS request.')
    return new Response('ok', {
      headers: corsHeaders
    })
  }
  
  // Sadece POST isteklerini kabul et
  if (req.method !== 'POST') {
    console.warn(`[seller-signal-endpoint] Invalid method: ${req.method}.`)
    const errorHeaders = new Headers(corsHeaders)
    errorHeaders.set('Content-Type', 'application/json')
    return new Response(JSON.stringify({
      error: 'Method Not Allowed. Only POST is accepted.'
    }), {
      status: 405,
      headers: errorHeaders
    })
  }
  try {
    console.log('[seller-signal-endpoint] Initializing Supabase admin client...')
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
    
    console.log('[seller-signal-endpoint] Environment check:', {
      supabaseUrl: supabaseUrl ? 'present' : 'missing',
      serviceRoleKey: serviceRoleKey ? 'present' : 'missing'
    })
    
    if (!supabaseUrl || !serviceRoleKey) {
      const missing = [
        !supabaseUrl && 'SUPABASE_URL',
        !serviceRoleKey && 'SUPABASE_SERVICE_ROLE_KEY'
      ].filter(Boolean).join(', ')
      console.error(`[seller-signal-endpoint] Missing environment variables: ${missing}`)
      throw new Error(`Missing Supabase environment variables: ${missing}`)
    }
    
    const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey)
    console.log('[seller-signal-endpoint] Supabase admin client initialized.')
    
    // 1. Correctly parse robot_id from the URL
    const url = new URL(req.url)
    console.log(`[seller-signal-endpoint] Full URL: ${req.url}`)
    console.log(`[seller-signal-endpoint] URL pathname: ${url.pathname}`)
    
    const pathParts = url.pathname.split('/').filter((part) => part.length > 0)
    console.log(`[seller-signal-endpoint] Path parts:`, pathParts)
    
    // Robot ID'yi çıkar - path'in sonundaki UUID'yi ara
    let robotIdFromPath = null
    for (let i = pathParts.length - 1; i >= 0; i--) {
      const part = pathParts[i]
      if (/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(part)) {
        robotIdFromPath = part
        console.log(`[seller-signal-endpoint] Found robot_id in path at position ${i}: ${robotIdFromPath}`)
        break
      }
    }
    
    // Alternatif olarak query parameter'dan da kontrol et
    if (!robotIdFromPath) {
      const robotIdFromQuery = url.searchParams.get('robot_id')
      if (robotIdFromQuery && /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(robotIdFromQuery)) {
        robotIdFromPath = robotIdFromQuery
        console.log(`[seller-signal-endpoint] Found robot_id in query params: ${robotIdFromPath}`)
      }
    }
    
    console.log(`[seller-signal-endpoint] Extracted robot_id: ${robotIdFromPath}`)
    
    if (!robotIdFromPath) {
      throw new Error('robot_id not found in URL path or query params. Expected format: .../seller-signal-endpoint/{robot_id} or ?robot_id={robot_id}')
    }
    
    // Rate limiting kontrolü - IP ve robot ID kombinasyonu ile
    const rateLimitId = `${clientIP}:${robotIdFromPath}`
    if (!checkRateLimit(rateLimitId)) {
      console.warn(`[seller-signal-endpoint] Rate limit exceeded for ${rateLimitId}`)
      const rateLimitHeaders = new Headers(corsHeaders)
      rateLimitHeaders.set('Content-Type', 'application/json')
      rateLimitHeaders.set('Retry-After', '60')
      return new Response(JSON.stringify({
        error: 'Rate limit exceeded. Maximum 30 requests per minute per IP/robot combination.',
        retry_after: 60
      }), {
        status: 429,
        headers: rateLimitHeaders
      })
    }
    
    // Robot doğrulaması
    const robotValidation = await validateRobotById(supabaseAdmin, robotIdFromPath)
    if (!robotValidation.valid) {
      console.error(`[seller-signal-endpoint] Robot validation failed: ${robotValidation.error}`)
      const validationErrorHeaders = new Headers(corsHeaders)
      validationErrorHeaders.set('Content-Type', 'application/json')
      return new Response(JSON.stringify({
        error: robotValidation.error,
        robot_id: robotIdFromPath
      }), {
        status: 403,
        headers: validationErrorHeaders
      })
    }
    
    console.log(`[seller-signal-endpoint] Robot ${robotIdFromPath} validated successfully.`)
    
    // Satıcının aktif durumunu kontrol et
    console.log(`[seller-signal-endpoint] Checking active status for seller ID: ${robotValidation.sellerId}`)
    const { data: userSettings, error: settingsError } = await supabaseAdmin
      .from('user_settings')
      .select('is_active')
      .eq('id', robotValidation.sellerId)
      .single()
    
    if (settingsError) {
      console.error(`[seller-signal-endpoint] Error fetching user_settings for seller ${robotValidation.sellerId}:`, settingsError.message)
      throw new Error(`Error fetching user settings: ${settingsError.message}`)
    }
    
    if (!userSettings || userSettings.is_active === false) {
      const message = `Signal for robot ${robotIdFromPath} ignored. Seller ${robotValidation.sellerId} is inactive (emergency stop activated or settings not found).`
      console.warn(`[seller-signal-endpoint] ${message}`)
      const inactiveHeaders = new Headers(corsHeaders)
      inactiveHeaders.set('Content-Type', 'application/json')
      return new Response(JSON.stringify({
        success: false,
        message: message
      }), {
        status: 200,
        headers: inactiveHeaders
      })
    }
    
    console.log(`[seller-signal-endpoint] Seller ${robotValidation.sellerId} is active. Proceeding with signal processing.`)
    
    // 2. Get signal data from the request body
    const signal_data = await req.json()
    if (!signal_data) {
      throw new Error('Signal data is missing from the request body.')
    }
    
    console.log(`[seller-signal-endpoint] Received signal for robot_id: ${robotIdFromPath}`)
    console.log(`[seller-signal-endpoint] Signal data:`, JSON.stringify(signal_data, null, 2))
    
    // 3. THE FIX: Pass both robot_id and signal_data in the body of the invoke call.
    console.log('[seller-signal-endpoint] Invoking signal-relay-function...')
    const { data, error } = await supabaseAdmin.functions.invoke(
      'signal-relay-function',
      {
        body: { robot_id: robotIdFromPath, signal_data }, // Correctly structured payload
      }
    )
    
    if (error) {
      console.error(`[seller-signal-endpoint] Error invoking signal-relay-function:`, error.message)
      throw new Error(`Failed to relay signal to subscribers: ${error.message}`)
    }
    
    console.log(`[seller-signal-endpoint] Successfully invoked signal-relay-function for robot_id: ${robotIdFromPath}`)
    console.log(`[seller-signal-endpoint] Relay result:`, data)
    
    const processingTime = Date.now() - functionStartTime
    console.log(`[seller-signal-endpoint] SUCCESS - Processing completed in ${processingTime}ms`)
    
    const successHeaders = new Headers(corsHeaders)
    successHeaders.set('Content-Type', 'application/json')
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Signal relay invoked successfully.', 
      robot_id: robotIdFromPath,
      processing_time_ms: processingTime,
      response: data 
    }), {
      headers: successHeaders,
      status: 200,
    })
    
  } catch (error) {
    const processingTime = Date.now() - functionStartTime
    console.error(`[seller-signal-endpoint] Critical processing error after ${processingTime}ms:`, error.message)
    console.error('[seller-signal-endpoint] Error stack:', error.stack)
    
    // Hata tipine göre HTTP durum kodunu belirle
    let statusCode = 500
    if (error.message?.includes('robot_id not found') || error.message?.includes('Signal data is missing')) {
      statusCode = 400 // Bad Request
    } else if (error.message?.includes('not found')) {
      statusCode = 404 // Not Found
    } else if (error.message?.includes('Validation') || error.message?.includes('unauthorized')) {
      statusCode = 403 // Forbidden
    } else if (error.message?.includes('Rate limit')) {
      statusCode = 429 // Too Many Requests
    }
    
    const errorHeaders = new Headers(corsHeaders)
    errorHeaders.set('Content-Type', 'application/json')
    return new Response(JSON.stringify({ 
      error: `Failed to process signal: ${error.message}`,
      robot_id: robotIdFromPath || 'unknown',
      processing_time_ms: Date.now() - functionStartTime
    }), {
      headers: errorHeaders,
      status: statusCode,
    })
  }
})

console.log('--- seller-signal-endpoint: BRO-ROBOT Flow (Part 1) handler setup complete. ---')
