import { test, expect } from '@playwright/test';

test.describe('ROI Analysis Filtering System', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/statistics');
    await page.waitForLoadState('networkidle');
    await page.click('text=ROI Analizi');
    await page.waitForSelector('[data-testid="roi-dashboard"]', { timeout: 10000 });
  });

  test('should apply robot type filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Test Solo robot filter
    await page.click('text=Solo-Robot');
    await page.click('button:has-text("Filtrele")');
    
    // Verify only solo robot data is shown
    await expect(page.locator('text=Solo-Robot')).toBeVisible();
    
    // Test Bro robot filter
    await page.click('text=Bro-Robot');
    await page.click('button:has-text("Filtrele")');
    
    // Verify bro robot data is shown
    const robotNames = await page.locator('[data-testid="robot-name"]').allTextContents();
    robotNames.forEach(name => {
      expect(name).not.toBe('Solo-Robot');
    });
  });

  test('should apply date range filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Test 7 days filter
    await page.selectOption('select[data-testid="date-range"]', '7d');
    await page.click('button:has-text("Filtrele")');
    
    // Verify data is filtered (should show recent data only)
    await expect(page.locator('[data-testid="roi-dashboard"]')).toBeVisible();
    
    // Test 30 days filter
    await page.selectOption('select[data-testid="date-range"]', '30d');
    await page.click('button:has-text("Filtrele")');
    
    // Test 1 year filter
    await page.selectOption('select[data-testid="date-range"]', '1y');
    await page.click('button:has-text("Filtrele")');
  });

  test('should apply ROI range filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Set minimum ROI
    await page.fill('input[placeholder="0"]', '10');
    await page.click('button:has-text("Filtrele")');
    
    // Verify all displayed ROI values are >= 10%
    const roiValues = await page.locator('[data-testid="roi-value"]').allTextContents();
    roiValues.forEach(value => {
      const numericValue = parseFloat(value.replace('%', ''));
      expect(numericValue).toBeGreaterThanOrEqual(10);
    });
    
    // Set maximum ROI
    await page.fill('input[placeholder="∞"]', '50');
    await page.click('button:has-text("Filtrele")');
    
    // Verify all displayed ROI values are <= 50%
    const filteredRoiValues = await page.locator('[data-testid="roi-value"]').allTextContents();
    filteredRoiValues.forEach(value => {
      const numericValue = parseFloat(value.replace('%', ''));
      expect(numericValue).toBeLessThanOrEqual(50);
    });
  });

  test('should apply performance level filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Test excellent performance filter
    await page.selectOption('select[data-testid="performance-level"]', 'excellent');
    await page.click('button:has-text("Filtrele")');
    
    // Verify only excellent performers are shown
    const roiValues = await page.locator('[data-testid="roi-value"]').allTextContents();
    roiValues.forEach(value => {
      const numericValue = parseFloat(value.replace('%', ''));
      expect(numericValue).toBeGreaterThan(20); // Excellent is 20%+
    });
    
    // Test loss filter
    await page.selectOption('select[data-testid="performance-level"]', 'loss');
    await page.click('button:has-text("Filtrele")');
    
    // Verify only loss-making entries are shown
    const lossRoiValues = await page.locator('[data-testid="roi-value"]').allTextContents();
    lossRoiValues.forEach(value => {
      const numericValue = parseFloat(value.replace('%', ''));
      expect(numericValue).toBeLessThan(0);
    });
  });

  test('should apply ROI range button filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Test positive ROI filter
    await page.click('button:has-text("Pozitif")');
    await page.click('button:has-text("Filtrele")');
    
    // Verify only positive ROI values are shown
    const positiveRoiValues = await page.locator('[data-testid="roi-value"]').allTextContents();
    positiveRoiValues.forEach(value => {
      const numericValue = parseFloat(value.replace('%', ''));
      expect(numericValue).toBeGreaterThan(0);
    });
    
    // Test negative ROI filter
    await page.click('button:has-text("Negatif")');
    await page.click('button:has-text("Filtrele")');
    
    // Verify only negative ROI values are shown
    const negativeRoiValues = await page.locator('[data-testid="roi-value"]').allTextContents();
    negativeRoiValues.forEach(value => {
      const numericValue = parseFloat(value.replace('%', ''));
      expect(numericValue).toBeLessThan(0);
    });
  });

  test('should apply symbol filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Select a specific symbol
    await page.selectOption('select[data-testid="symbol-filter"]', 'AAPL');
    await page.click('button:has-text("Filtrele")');
    
    // Verify only AAPL trades are shown
    await expect(page.locator('text=AAPL')).toBeVisible();
    
    // Navigate to symbol comparison to verify
    await page.click('text=Karşılaştırma');
    await page.click('text=Sembol Bazlı');
    
    // Should show AAPL in the analysis
    await expect(page.locator('text=AAPL')).toBeVisible();
  });

  test('should apply trade size filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Set minimum trade size
    await page.fill('input[data-testid="min-trade-size"]', '1000');
    await page.click('button:has-text("Filtrele")');
    
    // Verify filtering is applied (check if data changes)
    await expect(page.locator('[data-testid="roi-dashboard"]')).toBeVisible();
    
    // Set maximum trade size
    await page.fill('input[data-testid="max-trade-size"]', '10000');
    await page.click('button:has-text("Filtrele")');
    
    // Verify filtering is applied
    await expect(page.locator('[data-testid="roi-dashboard"]')).toBeVisible();
  });

  test('should apply profit/loss filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Test profit only filter
    await page.check('input[data-testid="profit-only"]');
    await page.click('button:has-text("Filtrele")');
    
    // Verify only profitable trades are included
    const roiValues = await page.locator('[data-testid="roi-value"]').allTextContents();
    const hasPositiveValues = roiValues.some(value => {
      const numericValue = parseFloat(value.replace('%', ''));
      return numericValue > 0;
    });
    expect(hasPositiveValues).toBeTruthy();
    
    // Test loss only filter
    await page.uncheck('input[data-testid="profit-only"]');
    await page.check('input[data-testid="loss-only"]');
    await page.click('button:has-text("Filtrele")');
    
    // Verify only loss-making trades are included
    const lossRoiValues = await page.locator('[data-testid="roi-value"]').allTextContents();
    const hasNegativeValues = lossRoiValues.some(value => {
      const numericValue = parseFloat(value.replace('%', ''));
      return numericValue < 0;
    });
    expect(hasNegativeValues).toBeTruthy();
  });

  test('should combine multiple filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Apply multiple filters
    await page.selectOption('select[data-testid="date-range"]', '30d');
    await page.fill('input[placeholder="0"]', '5'); // Min ROI 5%
    await page.selectOption('select[data-testid="performance-level"]', 'good');
    await page.click('button:has-text("Pozitif")'); // Positive ROI only
    
    await page.click('button:has-text("Filtrele")');
    
    // Verify all filters are applied
    const roiValues = await page.locator('[data-testid="roi-value"]').allTextContents();
    roiValues.forEach(value => {
      const numericValue = parseFloat(value.replace('%', ''));
      expect(numericValue).toBeGreaterThanOrEqual(5); // Min ROI
      expect(numericValue).toBeGreaterThan(0); // Positive only
      expect(numericValue).toBeGreaterThan(10); // Good performance (10-20%)
    });
  });

  test('should clear filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Apply some filters
    await page.fill('input[placeholder="0"]', '10');
    await page.selectOption('select[data-testid="performance-level"]', 'excellent');
    await page.click('button:has-text("Filtrele")');
    
    // Clear filters
    await page.click('button:has-text("Filtreleri Temizle")');
    
    // Verify filters are cleared
    await expect(page.locator('input[placeholder="0"]')).toHaveValue('');
    await expect(page.locator('select[data-testid="performance-level"]')).toHaveValue('all');
    
    // Verify data is reset
    await expect(page.locator('[data-testid="roi-dashboard"]')).toBeVisible();
  });

  test('should display active filters correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Apply filters
    await page.fill('input[placeholder="0"]', '15');
    await page.selectOption('select[data-testid="performance-level"]', 'good');
    await page.click('button:has-text("Pozitif")');
    await page.click('button:has-text("Filtrele")');
    
    // Check for active filter badges
    await expect(page.locator('text*="ROI: 15%"')).toBeVisible();
    await expect(page.locator('text*="İyi Performans"')).toBeVisible();
    await expect(page.locator('text*="Pozitif ROI"')).toBeVisible();
  });

  test('should persist filters across tab navigation', async ({ page }) => {
    // Open filter panel and apply filters
    await page.click('[data-testid="filter-toggle"]');
    await page.fill('input[placeholder="0"]', '10');
    await page.click('button:has-text("Filtrele")');
    
    // Navigate to different tabs
    await page.click('text=Karşılaştırma');
    await page.click('text=Dağılım');
    await page.click('text=Trend Analizi');
    
    // Verify filters are still active
    await expect(page.locator('text*="ROI: 10%"')).toBeVisible();
  });

  test('should handle filter validation correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Test invalid ROI range (min > max)
    await page.fill('input[placeholder="0"]', '50');
    await page.fill('input[placeholder="∞"]', '20');
    await page.click('button:has-text("Filtrele")');
    
    // Should show validation error
    await expect(page.locator('text*="Geçersiz ROI aralığı"')).toBeVisible();
    
    // Test negative minimum ROI
    await page.fill('input[placeholder="0"]', '-10');
    await page.click('button:has-text("Filtrele")');
    
    // Should handle negative values appropriately
    await expect(page.locator('[data-testid="roi-dashboard"]')).toBeVisible();
  });

  test('should update filter counts correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Check initial counts
    const initialCount = await page.locator('[data-testid="total-robots"]').textContent();
    
    // Apply filter
    await page.selectOption('select[data-testid="performance-level"]', 'excellent');
    await page.click('button:has-text("Filtrele")');
    
    // Check updated counts
    const filteredCount = await page.locator('[data-testid="total-robots"]').textContent();
    
    if (initialCount && filteredCount) {
      const initial = parseInt(initialCount.replace(/\D/g, ''));
      const filtered = parseInt(filteredCount.replace(/\D/g, ''));
      expect(filtered).toBeLessThanOrEqual(initial);
    }
  });

  test('should handle empty filter results correctly', async ({ page }) => {
    // Open filter panel
    await page.click('[data-testid="filter-toggle"]');
    
    // Apply very restrictive filters
    await page.fill('input[placeholder="0"]', '1000'); // Very high min ROI
    await page.selectOption('select[data-testid="performance-level"]', 'excellent');
    await page.click('button:has-text("Filtrele")');
    
    // Should show no results message
    await expect(page.locator('text*="Filtre kriterlerine uygun veri bulunamadı"')).toBeVisible();
    
    // Should suggest clearing filters
    await expect(page.locator('button:has-text("Filtreleri Temizle")')).toBeVisible();
  });

  test('should export filtered data correctly', async ({ page }) => {
    // Apply filters
    await page.click('[data-testid="filter-toggle"]');
    await page.fill('input[placeholder="0"]', '10');
    await page.click('button:has-text("Filtrele")');
    
    // Export data
    const exportButton = page.locator('button:has-text("Dışa Aktar")');
    if (await exportButton.isVisible()) {
      const downloadPromise = page.waitForEvent('download');
      await exportButton.click();
      const download = await downloadPromise;
      
      // Verify download includes filter information
      expect(download.suggestedFilename()).toContain('filtered');
    }
  });
});
