import { test, expect } from '@playwright/test';

test.describe('ROI Calculations and Data Integrity', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/statistics');
    await page.waitForLoadState('networkidle');
    await page.click('text=ROI <PERSON>zi');
    await page.waitForSelector('[data-testid="roi-dashboard"]', { timeout: 10000 });
  });

  test('should calculate ROI percentages correctly', async ({ page }) => {
    // Get investment and return values
    const investmentText = await page.locator('text=Toplam Yatırım').locator('..').locator('[data-testid="metric-value"]').textContent();
    const returnText = await page.locator('text=Toplam Getiri').locator('..').locator('[data-testid="metric-value"]').textContent();
    const roiText = await page.locator('text=Toplam ROI').locator('..').locator('[data-testid="metric-value"]').textContent();
    
    if (investmentText && returnText && roiText) {
      // Parse values
      const investment = parseFloat(investmentText.replace(/[₺,]/g, ''));
      const returns = parseFloat(returnText.replace(/[₺,]/g, ''));
      const displayedROI = parseFloat(roiText.replace('%', ''));
      
      // Calculate expected ROI
      const expectedROI = investment > 0 ? (returns / investment) * 100 : 0;
      
      // Allow for small rounding differences
      expect(Math.abs(displayedROI - expectedROI)).toBeLessThan(0.01);
    }
  });

  test('should display consistent data across different views', async ({ page }) => {
    // Get ROI value from main metrics
    const mainROI = await page.locator('text=Toplam ROI').locator('..').locator('[data-testid="metric-value"]').textContent();
    
    // Navigate to comparison tab
    await page.click('text=Karşılaştırma');
    
    // Check if the same ROI appears in comparison tables
    if (mainROI) {
      const roiValue = mainROI.replace('%', '');
      // Look for the same value in comparison tables (allowing for formatting differences)
      const comparisonElements = await page.locator(`text*="${roiValue}"`).count();
      expect(comparisonElements).toBeGreaterThan(0);
    }
  });

  test('should handle negative ROI correctly', async ({ page }) => {
    // Mock data with negative ROI
    await page.route('**/api/statistics/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          roiAnalysis: {
            totalInvestment: 10000,
            totalReturns: -2000,
            totalROI: -20,
            annualizedROI: -25,
            roiByRobot: [
              { robotId: 'test', robotName: 'Test Robot', roi: -20, investment: 10000 }
            ]
          }
        })
      });
    });
    
    await page.reload();
    
    // Check for negative ROI display
    await expect(page.locator('text=-20%')).toBeVisible();
    
    // Check for red color coding
    const roiElement = page.locator('text=-20%').first();
    await expect(roiElement).toHaveCSS('color', /rgb\(220, 38, 38\)|rgb\(239, 68, 68\)|red/);
  });

  test('should validate compound growth rate calculations', async ({ page }) => {
    // Enable advanced metrics to see CAGR
    await page.locator('input[id="advanced-metrics"]').click();
    
    // Check if CAGR is displayed
    await expect(page.locator('text=Bileşik Büyüme (CAGR)')).toBeVisible();
    
    // Get CAGR value
    const cagrText = await page.locator('text=Bileşik Büyüme (CAGR)').locator('..').locator('[data-testid="metric-value"]').textContent();
    
    if (cagrText) {
      const cagrValue = parseFloat(cagrText.replace('%', ''));
      // CAGR should be a reasonable value (between -100% and 1000% for most cases)
      expect(cagrValue).toBeGreaterThan(-100);
      expect(cagrValue).toBeLessThan(1000);
    }
  });

  test('should validate risk-adjusted return calculations', async ({ page }) => {
    // Enable advanced metrics
    await page.locator('input[id="advanced-metrics"]').click();
    
    // Check for risk-adjusted return
    await expect(page.locator('text=Risk Ayarlı Getiri')).toBeVisible();
    
    // Navigate to Risk Analysis tab
    await page.click('text=Risk Analizi');
    
    // Check for Sharpe ratio
    await expect(page.locator('text=Sharpe Oranı')).toBeVisible();
    
    // Validate that Sharpe ratio is within reasonable bounds
    const sharpeText = await page.locator('text=Sharpe Oranı').locator('..').locator('[data-testid="metric-value"]').textContent();
    
    if (sharpeText) {
      const sharpeValue = parseFloat(sharpeText);
      // Sharpe ratio should typically be between -5 and 5
      expect(sharpeValue).toBeGreaterThan(-5);
      expect(sharpeValue).toBeLessThan(5);
    }
  });

  test('should validate time-based ROI calculations', async ({ page }) => {
    // Test monthly ROI calculations
    await page.selectOption('select', 'monthly');
    await expect(page.locator('text=Aylık ROI Trendi')).toBeVisible();
    
    // Test quarterly ROI calculations
    await page.selectOption('select', 'quarterly');
    await expect(page.locator('text=Çeyreklik ROI Trendi')).toBeVisible();
    
    // Test yearly ROI calculations
    await page.selectOption('select', 'yearly');
    await expect(page.locator('text=Yıllık ROI Trendi')).toBeVisible();
    
    // Navigate to comparison tab to see time-based data
    await page.click('text=Karşılaştırma');
    await page.click('text=Zaman Bazlı');
    
    // Check for time-based comparison chart
    await expect(page.locator('text=Zaman Bazlı ROI Karşılaştırması')).toBeVisible();
  });

  test('should handle zero investment scenarios', async ({ page }) => {
    // Mock data with zero investment
    await page.route('**/api/statistics/**', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          roiAnalysis: {
            totalInvestment: 0,
            totalReturns: 0,
            totalROI: 0,
            roiByRobot: []
          }
        })
      });
    });
    
    await page.reload();
    
    // Should show no data message
    await expect(page.locator('text=ROI Analizi Yapılamıyor')).toBeVisible();
    await expect(page.locator('text=yeterli yatırım verisi bulunmuyor')).toBeVisible();
  });

  test('should validate robot-specific ROI calculations', async ({ page }) => {
    // Navigate to comparison tab
    await page.click('text=Karşılaştırma');
    
    // Ensure robot comparison is selected
    await page.click('text=Robot Bazlı');
    
    // Check for robot ROI chart
    await expect(page.locator('text=Robot ROI Karşılaştırması')).toBeVisible();
    
    // Navigate to performance ranking
    await page.click('text=Zaman Bazlı');
    await expect(page.locator('text=Performans Sıralaması')).toBeVisible();
    
    // Check that robots are ranked by ROI
    const robotRows = await page.locator('table tbody tr').count();
    if (robotRows > 1) {
      // Get ROI values from first two rows
      const firstROI = await page.locator('table tbody tr:first-child td:nth-child(3)').textContent();
      const secondROI = await page.locator('table tbody tr:nth-child(2) td:nth-child(3)').textContent();
      
      if (firstROI && secondROI) {
        const firstValue = parseFloat(firstROI.replace('%', ''));
        const secondValue = parseFloat(secondROI.replace('%', ''));
        
        // First robot should have higher or equal ROI than second
        expect(firstValue).toBeGreaterThanOrEqual(secondValue);
      }
    }
  });

  test('should validate symbol-specific ROI calculations', async ({ page }) => {
    // Navigate to comparison tab
    await page.click('text=Karşılaştırma');
    await page.click('text=Sembol Bazlı');
    
    // Check for symbol-based analysis
    await expect(page.locator('text=Sembol Bazlı ROI Analizi')).toBeVisible();
    
    // Navigate to distribution tab
    await page.click('text=Dağılım');
    
    // Check for symbol performance distribution
    await expect(page.locator('text=Sembol Performans Dağılımı')).toBeVisible();
  });

  test('should validate currency formatting', async ({ page }) => {
    // Check for Turkish Lira symbol
    await expect(page.locator('text=₺')).toBeVisible();
    
    // Check for proper number formatting (thousands separator)
    const currencyElements = await page.locator('text*="₺"').allTextContents();
    
    currencyElements.forEach(text => {
      // Should contain Turkish Lira symbol
      expect(text).toContain('₺');
      
      // Should have proper decimal formatting
      const numberPart = text.replace('₺', '').trim();
      if (parseFloat(numberPart) >= 1000) {
        // Should have thousands separator for large numbers
        expect(numberPart).toMatch(/\d{1,3}(,\d{3})*/);
      }
    });
  });

  test('should validate percentage formatting', async ({ page }) => {
    // Get all percentage values
    const percentageElements = await page.locator('text*="%"').allTextContents();
    
    percentageElements.forEach(text => {
      // Should contain percentage symbol
      expect(text).toContain('%');
      
      // Should have proper decimal places (typically 2)
      const numberPart = text.replace('%', '').trim();
      const decimalPlaces = numberPart.split('.')[1]?.length || 0;
      expect(decimalPlaces).toBeLessThanOrEqual(2);
    });
  });

  test('should validate data consistency across tabs', async ({ page }) => {
    // Get total investment from main view
    const totalInvestment = await page.locator('text=Toplam Yatırım').locator('..').locator('[data-testid="metric-value"]').textContent();
    
    // Navigate to distribution tab
    await page.click('text=Dağılım');
    
    // Check if the same total appears in the pie chart center
    if (totalInvestment) {
      const investmentValue = totalInvestment.replace(/[₺,]/g, '');
      await expect(page.locator(`text*="${investmentValue}"`)).toBeVisible();
    }
  });

  test('should handle real-time data updates', async ({ page }) => {
    // Check for real-time indicators
    const liveIndicator = page.locator('text=CANLI');
    
    if (await liveIndicator.isVisible()) {
      // Check for live update timestamp
      await expect(page.locator('text*="Son:"')).toBeVisible();
      
      // Check for today's P&L if available
      const todaysPnL = page.locator('text*="Bugün:"');
      if (await todaysPnL.isVisible()) {
        const pnlText = await todaysPnL.textContent();
        expect(pnlText).toMatch(/Bugün:\s*₺[\d,.-]+/);
      }
    }
  });

  test('should validate benchmark performance calculations', async ({ page }) => {
    // Check for performance benchmark section
    await expect(page.locator('text=Performans Kıyaslaması')).toBeVisible();
    
    // Check for performance level badges
    const performanceBadges = ['Mükemmel', 'İyi', 'Orta', 'Zayıf', 'Kayıp'];
    let foundBadge = false;
    
    for (const badge of performanceBadges) {
      if (await page.locator(`text=${badge}`).isVisible()) {
        foundBadge = true;
        break;
      }
    }
    
    expect(foundBadge).toBeTruthy();
  });
});
