import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  ModalBody,
  <PERSON>dal<PERSON>lose<PERSON>utton,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Switch,
  useToast,
  Spinner,
  Center,
  Alert,
  AlertIcon,
  Text,
  Avatar,
  Box,
  HStack,
  Tooltip,
  VStack
} from '@chakra-ui/react';
import { supabase } from '../../supabaseClient';
import { RobotSubscriber } from '../../types/robot';

interface RobotSubscribersModalProps {
  isOpen: boolean;
  onClose: () => void;
  robotId: string;
  robotName: string;
}

const RobotSubscribersModal = ({ 
  isOpen, 
  onClose, 
  robotId, 
  robotName 
}: RobotSubscribersModalProps) => {
  const [subscribers, setSubscribers] = useState<RobotSubscriber[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [togglingSubscriptions, setTogglingSubscriptions] = useState<Record<string, boolean>>({});
  const toast = useToast();

  // Aboneleri getir
  const fetchSubscribers = async () => {
    if (!robotId || !isOpen) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Ensure robotId is valid before calling
      if (!robotId) {
        throw new Error("Robot ID'si bulunamadı.");
      }

      console.log(`[RobotSubscribersModal] "${robotName}" (ID: ${robotId}) için aboneler getiriliyor...`);
      
      const { data, error: rpcError } = await supabase.rpc('get_robot_subscribers_for_seller', {
        p_robot_id_param: robotId
      });
      
      if (rpcError) {
        console.error('RPC çağrısı get_robot_subscribers_for_seller başarısız oldu:', JSON.stringify(rpcError, null, 2));
        
        if (rpcError.code === 'PGRST116' || (rpcError.message && rpcError.message.includes('404')) || 
            rpcError.code === 'PGRST202' || (rpcError.message && rpcError.message.includes('Could not find the function'))) {
          setError('Abonelikleri getirme servisi (get_robot_subscribers_for_seller) bulunamadı. Lütfen SQL fonksiyonunun veritabanında oluşturulduğundan ve API şemasının Supabase Dashboard üzerinden yenilendiğinden emin olun.');
          toast({
            title: "Servis Erişim Hatası",
            description: "Abonelikleri getirme servisi (get_robot_subscribers_for_seller) bulunamadı. Lütfen SQL fonksiyonunun veritabanında oluşturulduğundan ve API şemasının Supabase Dashboard üzerinden yenilendiğinden emin olun (API -> Reload Schema).",
            status: "error",
            duration: 9000, // Daha uzun süre için önemli mesajlar
            isClosable: true,
          });
        } else if (rpcError.code === 'PGRST301') {
          setError('Abonelikleri getirme servisi için yetkiniz bulunmuyor.');
          toast({
            title: "Yetki Hatası",
            description: "Bu robot için aboneleri görüntüleme yetkiniz bulunmuyor.",
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        } else {
          setError(`Aboneler yüklenirken bir hata oluştu: ${rpcError.message || 'Bilinmeyen bir hata'}`);
          toast({
            title: "Veri Hatası",
            description: rpcError.message || "Aboneler alınamadı. Lütfen daha sonra tekrar deneyin.",
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        }
        
        setSubscribers([]);
      } else {
        console.log(`[RobotSubscribersModal] ${data?.length || 0} abone başarıyla yüklendi.`);
        setSubscribers(data || []);
      }
    } catch (e: any) {
      console.error('Genel hata - fetchSubscribers:', e);
      setError(e.message || 'Beklenmedik bir hata oluştu.');
      setSubscribers([]);
      toast({
        title: "Beklenmedik Hata",
        description: e.message || "Aboneler yüklenirken bir sorun oluştu.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Abonelik durumunu değiştir
  const handleToggleSubscriptionStatus = async (subscriptionId: string, newStatus: boolean) => {
    // Eğer abonelik zaten toggle işlemi yapılıyorsa, çık
    if (togglingSubscriptions[subscriptionId]) return;
    
    console.log(`[RobotSubscribersModal] Abonelik durumu değiştiriliyor: ID ${subscriptionId}, Yeni durum: ${newStatus ? 'Aktif' : 'Pasif'}`);
    
    // Toggle durumunu güncelle
    setTogglingSubscriptions(prev => ({ ...prev, [subscriptionId]: true }));
    
    // Optimistic UI güncellemesi
    setSubscribers(prev => 
      prev.map(s => s.subscription_id === subscriptionId 
        ? { ...s, is_subscription_active: newStatus } 
        : s
      )
    );
    
    try {
      const { error: rpcError } = await supabase.rpc('set_subscription_active_status', {
        p_subscription_id: subscriptionId,
        p_new_is_active: newStatus
      });
      
      if (rpcError) {
        console.error('RPC çağrısı set_subscription_active_status başarısız oldu:', JSON.stringify(rpcError, null, 2));
        
        // Hata türüne göre farklı mesajlar
        if (rpcError.code === 'PGRST116' || (rpcError.message && rpcError.message.includes('404'))) {
          toast({
            title: "Servis Hatası",
            description: "Abonelik durumu değiştirme servisi bulunamadı. Sistem yöneticisi RPC fonksiyonlarını kontrol etmeli.",
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        } else if (rpcError.code === 'PGRST301') {
          toast({
            title: "Yetki Hatası",
            description: "Bu aboneliğin durumunu değiştirme yetkiniz bulunmuyor.",
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        } else {
          toast({
            title: "İşlem Hatası",
            description: rpcError.message || "Abonelik durumu değiştirilemedi. Lütfen daha sonra tekrar deneyin.",
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        }
        
        // Hata durumunda UI'ı geri al
        setSubscribers(prev => 
          prev.map(s => s.subscription_id === subscriptionId 
            ? { ...s, is_subscription_active: !newStatus } 
            : s
          )
        );
      } else {
        console.log(`[RobotSubscribersModal] Abonelik durumu başarıyla güncellendi: ${newStatus ? 'Aktif' : 'Pasif'}`);
        
        toast({
          title: 'Başarılı!',
          description: `Abonelik durumu ${newStatus ? 'aktif' : 'pasif'} olarak güncellendi.`,
          status: 'success',
          duration: 3000,
          isClosable: true
        });
        
        // Başarılı olduğunda da verileri tekrar çekerek en güncel durumu göster
        if (isOpen && robotId) {
          // Durumu yenilemek için hafif bir gecikme ekle, kullanıcı için daha iyi bir deneyim olacaktır
          setTimeout(() => {
            fetchSubscribers();
          }, 1000);
        }
      }
    } catch (error: any) {
      console.error('Abonelik durumu güncellenirken genel hata:', error);
      
      // Hata durumunda UI'ı geri al
      setSubscribers(prev => 
        prev.map(s => s.subscription_id === subscriptionId 
          ? { ...s, is_subscription_active: !newStatus } 
          : s
        )
      );
      
      toast({
        title: 'Beklenmedik Hata!',
        description: `Abonelik durumu güncellenemedi: ${error.message || 'Bilinmeyen bir hata oluştu'}`,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      // Toggle durumunu sıfırla (hafif bir gecikme ile - kullanıcı deneyimi için)
      setTimeout(() => {
        setTogglingSubscriptions(prev => ({ ...prev, [subscriptionId]: false }));
      }, 500);
    }
  };

  // Modal açıldığında aboneleri getir
  useEffect(() => {
    if (isOpen && robotId) {
      fetchSubscribers();
    }
  }, [isOpen, robotId]);

  // Tarih formatlama fonksiyonu
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Belirtilmemiş';
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" scrollBehavior="inside">
      <ModalOverlay />
      <ModalContent borderRadius="xl">
        <ModalHeader>{robotName} - Aboneler</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          {isLoading ? (
            <Center py={10}>
              <VStack spacing={3}>
                <Spinner size="xl" color="brand.500" thickness="4px" />
                <Text color="gray.500">Aboneler yükleniyor...</Text>
              </VStack>
            </Center>
          ) : error ? (
            <Alert status="error" borderRadius="md" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" p={4}>
              <AlertIcon boxSize="40px" mr={0} />
              <Text mt={4} mb={2} fontSize="lg" fontWeight="bold">
                Aboneler Yüklenemedi
              </Text>
              <Text mb={4}>{error}</Text>
              <Button 
                colorScheme="red" 
                onClick={() => fetchSubscribers()}
                size="sm"
              >
                Yeniden Dene
              </Button>
            </Alert>
          ) : subscribers.length === 0 ? (
            <Alert status="info" borderRadius="md" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" p={6}>
              <AlertIcon boxSize="40px" mr={0} />
              <Text mt={4} fontSize="lg" fontWeight="medium">
                Bu robota henüz abone olan kullanıcı bulunmamaktadır.
              </Text>
            </Alert>
          ) : (
            <Box overflowX="auto">
              <Table variant="simple" size="sm">
                <Thead>
                  <Tr>
                    <Th>Kullanıcı</Th>
                    <Th>Abonelik Başlangıcı</Th>
                    <Th>Abonelik Bitişi</Th>
                    <Th>Durum</Th>
                    <Th>Aktif/Pasif</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {subscribers.map((subscriber) => (
                    <Tr key={subscriber.subscription_id}>
                      <Td>
                        <HStack spacing={2}>
                          <Avatar 
                            size="sm" 
                            name={subscriber.username || subscriber.full_name || 'Kullanıcı'} 
                            src={subscriber.avatar_url || undefined} 
                          />
                          <Text>{subscriber.username || subscriber.full_name || 'Anonim Kullanıcı'}</Text>
                        </HStack>
                      </Td>
                      <Td>{formatDate(subscriber.subscribed_at)}</Td>
                      <Td>{formatDate(subscriber.expires_at)}</Td>
                      <Td>
                        <Badge colorScheme={subscriber.is_subscription_active ? 'green' : 'red'}>
                          {subscriber.is_subscription_active ? 'Aktif' : 'Pasif'}
                        </Badge>
                      </Td>
                      <Td>
                        <Tooltip 
                          label={subscriber.is_subscription_active 
                            ? 'Aboneyi devre dışı bırakmak için tıklayın. Aboneye sinyal gönderilmeyecek.' 
                            : 'Aboneyi aktif etmek için tıklayın. Aboneye sinyal gönderilecek.'} 
                          placement="top"
                        >
                          <Switch 
                            isChecked={subscriber.is_subscription_active} 
                            isDisabled={togglingSubscriptions[subscriber.subscription_id]}
                            onChange={() => handleToggleSubscriptionStatus(
                              subscriber.subscription_id, 
                              !subscriber.is_subscription_active
                            )}
                            colorScheme="green"
                            size="md"
                          />
                        </Tooltip>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </Box>
          )}
        </ModalBody>
        <ModalFooter>
          {!isLoading && !error && subscribers.length > 0 && (
            <Text mr="auto" fontSize="sm" color="gray.500">
              Toplam {subscribers.length} abone bulundu
            </Text>
          )}
          <Button colorScheme="blue" onClick={onClose}>
            Kapat
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default RobotSubscribersModal; 