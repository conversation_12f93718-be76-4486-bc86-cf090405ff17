import React from 'react';
import {
  Box,
  <PERSON>lex,
  Text,
  Icon,
  VStack,
  HStack,
  Badge,
  Button,
  useColorModeValue,
  Skeleton,
  Alert,
  AlertIcon
} from '@chakra-ui/react';
import { keyframes } from '@emotion/react';

// Animation keyframes
const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const urgentPulse = keyframes`
  0%, 100% {
    border-color: #E53E3E;
    box-shadow: 0 0 0 0 rgba(229, 62, 62, 0.7);
  }
  50% {
    border-color: #FC8181;
    box-shadow: 0 0 0 4px rgba(229, 62, 62, 0.3);
  }
`;
import { <PERSON><PERSON>ey, FiAlertTriangle, FiSettings } from 'react-icons/fi';
import { format, differenceInHours, differenceInDays, isPast } from 'date-fns';
import { tr } from 'date-fns/locale';
import Card from '../card/Card';

interface ApiExpiryCardProps {
  apiCredentialsExpiryDate?: string | null | undefined;
  isLoading?: boolean;
  onConfigureClick?: () => void;
}

const ApiExpiryCard: React.FC<ApiExpiryCardProps> = ({
  apiCredentialsExpiryDate,
  isLoading = false,
  onConfigureClick
}) => {
  // Theme colors
  const cardBg = useColorModeValue('white', 'navy.800');
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  const textColorSecondary = useColorModeValue('secondaryGray.600', 'secondaryGray.300');
  const borderColor = useColorModeValue('gray.200', 'whiteAlpha.100');
  const hoverBg = useColorModeValue('gray.50', 'whiteAlpha.50');

  // Calculate expiry status
  const getExpiryStatus = (expiryDate: string | null | undefined) => {
    if (!expiryDate) return null;

    const expiry = new Date(expiryDate);
    const now = new Date();
    const hoursUntilExpiry = differenceInHours(expiry, now);
    const daysUntilExpiry = differenceInDays(expiry, now);

    if (isPast(expiry)) {
      return {
        status: 'expired',
        message: 'Süresi dolmuş',
        colorScheme: 'red',
        isUrgent: true
      };
    } else if (hoursUntilExpiry <= 48) {
      return {
        status: 'critical',
        message: `${hoursUntilExpiry} saat kaldı`,
        colorScheme: 'red',
        isUrgent: true
      };
    } else if (daysUntilExpiry <= 7) {
      return {
        status: 'warning',
        message: `${daysUntilExpiry} gün kaldı`,
        colorScheme: 'orange',
        isUrgent: false
      };
    } else {
      return {
        status: 'good',
        message: `${daysUntilExpiry} gün kaldı`,
        colorScheme: 'green',
        isUrgent: false
      };
    }
  };

  const credentialsStatus = getExpiryStatus(apiCredentialsExpiryDate);

  // Determine urgency
  const isUrgent = credentialsStatus?.isUrgent || false;
  const hasExpiry = !!apiCredentialsExpiryDate;

  // Format date for display
  const formatExpiryDate = (dateString: string | null) => {
    if (!dateString) return 'Ayarlanmamış';
    try {
      return format(new Date(dateString), 'd MMMM yyyy, HH:mm', { locale: tr });
    } catch {
      return 'Geçersiz tarih';
    }
  };

  return (
    <Card
      bg={cardBg}
      border="2px solid"
      borderColor={isUrgent ? 'red.300' : borderColor}
      borderRadius="20px"
      p={{ base: '16px', md: '20px' }}
      h="100%"
      minH={{ base: '160px', md: '180px' }}
      position="relative"
      animation={`${fadeInUp} 0.6s ease-out`}
      transition="all 0.3s cubic-bezier(0.4, 0, 0.2, 1)"
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: 'lg',
        bg: hoverBg
      }}
      sx={isUrgent ? {
        animation: `${urgentPulse} 2s infinite`,
      } : {}}
    >
      <VStack align="stretch" spacing={4} h="100%">
        {/* Header */}
        <Flex justify="space-between" align="center">
          <HStack spacing={3}>
            <Icon
              as={FiKey}
              w={{ base: '24px', md: '28px' }}
              h={{ base: '24px', md: '28px' }}
              color={isUrgent ? 'red.500' : 'brand.500'}
            />
            <Text
              fontSize={{ base: 'sm', md: 'md' }}
              color={textColorSecondary}
              fontWeight="600"
            >
              API Kimlik Bilgileri Durumu
            </Text>
          </HStack>
          
          {isUrgent && (
            <Icon
              as={FiAlertTriangle}
              w="20px"
              h="20px"
              color="red.500"
            />
          )}
        </Flex>

        {/* Content */}
        <VStack align="stretch" spacing={3} flex={1}>
          {isLoading ? (
            <>
              <Skeleton height="20px" borderRadius="md" />
              <Skeleton height="20px" borderRadius="md" />
              <Skeleton height="32px" borderRadius="md" />
            </>
          ) : !hasExpiry ? (
            <Alert status="info" borderRadius="md" size="sm">
              <AlertIcon />
              <Text fontSize="sm">
                API kimlik bilgileri süresi ayarlanmamış
              </Text>
            </Alert>
          ) : (
            <Box>
              <HStack justify="space-between" align="center" mb={2}>
                <Text fontSize="xs" color={textColorSecondary} fontWeight="500">
                  Son Kullanma Tarihi
                </Text>
                {credentialsStatus && (
                  <Badge
                    colorScheme={credentialsStatus.colorScheme}
                    variant="subtle"
                    fontSize="xs"
                  >
                    {credentialsStatus.message}
                  </Badge>
                )}
              </HStack>
              <Text fontSize="sm" color={textColor} fontWeight="500">
                {formatExpiryDate(apiCredentialsExpiryDate)}
              </Text>
            </Box>
          )}
        </VStack>

        {/* Footer */}
        <Button
          leftIcon={<Icon as={FiSettings} />}
          size="sm"
          variant="outline"
          colorScheme={isUrgent ? 'red' : 'brand'}
          onClick={onConfigureClick}
          fontSize="xs"
          fontWeight="600"
          borderRadius="12px"
          _hover={{
            transform: 'translateY(-1px)',
            boxShadow: 'md'
          }}
        >
          Ayarları Düzenle
        </Button>
      </VStack>
    </Card>
  );
};

export default ApiExpiryCard;
