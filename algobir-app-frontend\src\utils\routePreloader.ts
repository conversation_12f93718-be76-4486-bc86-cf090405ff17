/**
 * Enhanced Route Preloader Utility
 *
 * Intelligently preloads route chunks based on user behavior and navigation patterns
 * Improves perceived performance by reducing loading times for likely next routes
 */

import { BundleOptimizer } from './performanceOptimizer';

/**
 * Route chunk mapping for dynamic imports
 */
const ROUTE_CHUNKS = {
  // Main application routes
  '/dashboard': () => import(/* webpackChunkName: "dashboard" */ '../pages/Dashboard'),
  '/trades': () => import(/* webpackChunkName: "trades" */ '../pages/Trades'),
  '/statistics': () => import(/* webpackChunkName: "statistics" */ '../pages/Statistics'),
  '/management': () => import(/* webpackChunkName: "management" */ '../pages/Management'),
  '/profile': () => import(/* webpackChunkName: "profile" */ '../pages/Profile'),
  '/open-positions': () => import(/* webpackChunkName: "open-positions" */ '../pages/OpenPositions'),

  // Marketplace routes
  '/marketplace': () => import(/* webpackChunkName: "marketplace" */ '../pages/marketplace/MarketplacePage'),
  '/robot-details': () => import(/* webpackChunkName: "robot-details" */ '../pages/RobotDetails'),
  '/robot-management': () => import(/* webpackChunkName: "robot-management" */ '../pages/seller/RobotManagementPage'),

  // Guide routes
  '/guide': () => import(/* webpackChunkName: "guide" */ '../pages/guide/GuidePage'),
  '/guide/solo-robot': () => import(/* webpackChunkName: "solo-guide" */ '../pages/guide/SoloRobotGuide'),
  '/guide/bro-robot': () => import(/* webpackChunkName: "bro-guide" */ '../pages/guide/BroRobotGuide'),
  '/guide/faq': () => import(/* webpackChunkName: "faq" */ '../pages/guide/FAQPage'),

  // Statistics sub-routes
  '/statistics/overview': () => import(/* webpackChunkName: "statistics-overview" */ '../pages/statistics/OverviewPage'),
  '/statistics/roi-analysis': () => import(/* webpackChunkName: "statistics-roi" */ '../pages/statistics/ROIAnalysisPage'),
  '/statistics/performance': () => import(/* webpackChunkName: "statistics-performance" */ '../pages/statistics/PerformancePage'),
  '/statistics/solo-robot': () => import(/* webpackChunkName: "statistics-solo" */ '../pages/statistics/SoloRobotPage'),
  '/statistics/bro-robots': () => import(/* webpackChunkName: "statistics-bro" */ '../pages/statistics/BroRobotsPage'),

  // Admin routes (lower priority)
  '/admin/dashboard': () => import(/* webpackChunkName: "admin-dashboard" */ '../pages/admin/AdminDashboard'),
  '/admin/users': () => import(/* webpackChunkName: "admin-users" */ '../pages/admin/UserManagement'),
  '/admin/trades': () => import(/* webpackChunkName: "admin-trades" */ '../pages/admin/AllTradesView'),
  '/admin/notifications': () => import(/* webpackChunkName: "admin-notifications" */ '../pages/admin/NotificationManagement'),
  '/admin/statistics': () => import(/* webpackChunkName: "admin-statistics" */ '../pages/admin/AdminStatistics'),
  '/admin/status': () => import(/* webpackChunkName: "admin-status" */ '../pages/admin/AdminStatus'),

  // Notification routes
  '/notifications': () => import(/* webpackChunkName: "notifications" */ '../pages/Notifications'),
} as const;

/**
 * Common navigation patterns for intelligent preloading
 */
const NAVIGATION_PATTERNS = {
  '/dashboard': ['/trades', '/statistics', '/management'],
  '/trades': ['/open-positions', '/statistics'],
  '/statistics': ['/statistics/overview', '/statistics/performance'],
  '/management': ['/profile', '/marketplace'],
  '/marketplace': ['/robot-details'],
  '/guide': ['/guide/solo-robot', '/guide/bro-robot', '/guide/faq'],
} as const;

/**
 * Priority levels for route preloading
 */
enum PreloadPriority {
  HIGH = 1,    // Critical routes (dashboard, trades)
  MEDIUM = 2,  // Important routes (statistics, management)
  LOW = 3,     // Optional routes (guides, admin)
}

/**
 * Route priority mapping
 */
const ROUTE_PRIORITIES: Record<string, PreloadPriority> = {
  '/dashboard': PreloadPriority.HIGH,
  '/trades': PreloadPriority.HIGH,
  '/statistics': PreloadPriority.MEDIUM,
  '/management': PreloadPriority.MEDIUM,
  '/marketplace': PreloadPriority.MEDIUM,
  '/profile': PreloadPriority.MEDIUM,
  '/open-positions': PreloadPriority.MEDIUM,
  '/guide': PreloadPriority.LOW,
  '/notifications': PreloadPriority.LOW,
};

/**
 * Route preloader class
 */
class RoutePreloader {
  private preloadedRoutes = new Set<string>();
  private preloadQueue: Array<{ route: string; priority: PreloadPriority }> = [];
  private isPreloading = false;
  private preloadTimeout: NodeJS.Timeout | null = null;

  /**
   * Preload a specific route
   */
  async preloadRoute(route: string): Promise<void> {
    if (this.preloadedRoutes.has(route)) {
      return; // Already preloaded
    }

    const importFn = ROUTE_CHUNKS[route as keyof typeof ROUTE_CHUNKS];
    if (!importFn) {
      console.warn(`No chunk mapping found for route: ${route}`);
      return;
    }

    try {
      await BundleOptimizer.dynamicImport(importFn);
      this.preloadedRoutes.add(route);
      console.log(`✅ Preloaded route: ${route}`);
    } catch (error) {
      console.warn(`Failed to preload route ${route}:`, error);
    }
  }

  /**
   * Add route to preload queue
   */
  queuePreload(route: string, priority: PreloadPriority = PreloadPriority.MEDIUM): void {
    if (this.preloadedRoutes.has(route)) {
      return;
    }

    // Remove existing entry if present
    this.preloadQueue = this.preloadQueue.filter(item => item.route !== route);

    // Add to queue with priority
    this.preloadQueue.push({ route, priority });

    // Sort by priority (lower number = higher priority)
    this.preloadQueue.sort((a, b) => a.priority - b.priority);

    // Start processing queue
    this.processQueue();
  }

  /**
   * Process the preload queue
   */
  private async processQueue(): Promise<void> {
    if (this.isPreloading || this.preloadQueue.length === 0) {
      return;
    }

    this.isPreloading = true;

    while (this.preloadQueue.length > 0) {
      const { route } = this.preloadQueue.shift()!;

      // Add delay between preloads to avoid blocking main thread
      await new Promise(resolve => setTimeout(resolve, 100));

      // Check if browser is idle (if supported)
      if ('requestIdleCallback' in window) {
        await new Promise(resolve => {
          requestIdleCallback(resolve as IdleRequestCallback, { timeout: 1000 });
        });
      }

      await this.preloadRoute(route);
    }

    this.isPreloading = false;
  }

  /**
   * Preload routes based on current route and navigation patterns
   */
  preloadRelatedRoutes(currentRoute: string): void {
    const relatedRoutes = NAVIGATION_PATTERNS[currentRoute as keyof typeof NAVIGATION_PATTERNS];

    if (relatedRoutes) {
      relatedRoutes.forEach(route => {
        const priority = ROUTE_PRIORITIES[route] || PreloadPriority.MEDIUM;
        this.queuePreload(route, priority);
      });
    }
  }

  /**
   * Preload common routes on app initialization
   */
  preloadCommonRoutes(): void {
    const commonRoutes = ['/dashboard', '/trades', '/statistics'];

    commonRoutes.forEach(route => {
      const priority = ROUTE_PRIORITIES[route] || PreloadPriority.HIGH;
      this.queuePreload(route, priority);
    });
  }

  /**
   * Preload route on hover (for navigation links)
   */
  preloadOnHover(route: string): void {
    // Clear any existing timeout
    if (this.preloadTimeout) {
      clearTimeout(this.preloadTimeout);
    }

    // Preload after a short delay to avoid unnecessary preloads
    this.preloadTimeout = setTimeout(() => {
      const priority = ROUTE_PRIORITIES[route] || PreloadPriority.LOW;
      this.queuePreload(route, priority);
    }, 200);
  }

  /**
   * Cancel hover preload
   */
  cancelHoverPreload(): void {
    if (this.preloadTimeout) {
      clearTimeout(this.preloadTimeout);
      this.preloadTimeout = null;
    }
  }

  /**
   * Get preload statistics
   */
  getStats(): { preloaded: number; queued: number; routes: string[] } {
    return {
      preloaded: this.preloadedRoutes.size,
      queued: this.preloadQueue.length,
      routes: Array.from(this.preloadedRoutes)
    };
  }
}

// Global route preloader instance
const routePreloader = new RoutePreloader();

/**
 * Public API functions
 */

/**
 * Preload common routes on app initialization
 */
export function preloadCommonRoutes(): void {
  routePreloader.preloadCommonRoutes();
}

/**
 * Preload routes related to current route
 */
export function preloadRelatedRoutes(currentRoute: string): void {
  routePreloader.preloadRelatedRoutes(currentRoute);
}

/**
 * Preload specific route
 */
export function preloadRoute(route: string): Promise<void> {
  return routePreloader.preloadRoute(route);
}

/**
 * Queue route for preloading
 */
export function queueRoutePreload(route: string, priority?: PreloadPriority): void {
  routePreloader.queuePreload(route, priority);
}

/**
 * Preload route on hover
 */
export function preloadOnHover(route: string): void {
  routePreloader.preloadOnHover(route);
}

/**
 * Cancel hover preload
 */
export function cancelHoverPreload(): void {
  routePreloader.cancelHoverPreload();
}

/**
 * Get preloader statistics
 */
export function getPreloaderStats(): { preloaded: number; queued: number; routes: string[] } {
  return routePreloader.getStats();
}

/**
 * Hook for React components to use route preloading
 */
export function useRoutePreloader() {
  return {
    preloadRoute,
    preloadOnHover,
    cancelHoverPreload,
    preloadRelatedRoutes,
    getStats: getPreloaderStats
  };
}

// Export priority enum for external use
export { PreloadPriority };