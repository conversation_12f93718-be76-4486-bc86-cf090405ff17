import { useState } from 'react';
import {
  Box,
  Flex,
  useColorModeValue,
  FlexProps,
  BoxProps,
  useBreakpointValue,
} from '@chakra-ui/react';
import { TRANSITIONS, PERFORMANCE } from '../sidebar/animations';
import SearchBox from './SearchBox';
import MobileSearchModal from './MobileSearchModal';
import NavbarBrand from './components/NavbarBrand';
import NavbarActions from './components/NavbarActions';
import NavbarUserMenu from './components/NavbarUserMenu';


// Responsive navbar yüksekliği
export const NAVBAR_HEIGHT = {
  base: '64px',  // Mobile'da daha kompakt
  md: '72px',    // Tablet'te orta boyut
  lg: '80px',    // Desktop'ta tam boyut
};

export interface AdminNavbarProps extends FlexProps {
  brandText: string;
  variant?: string;
  secondary?: boolean;
}

export default function AdminNavbar(props: AdminNavbarProps) {
  const [isMobileSearchOpen, setIsMobileSearchOpen] = useState(false);
  const { brandText } = props;

  // Navbar padding should match main content padding for perfect alignment
  const navbarPadding = useBreakpointValue({
    base: '4',    // 16px - matches main content
    sm: '5',      // 20px - matches main content
    md: '6',      // 24px - matches main content
    lg: '8',      // 32px - matches main content
  });



  // Semantic color tokens with better contrast
  const navbarBg = useColorModeValue(
    'rgba(255, 255, 255, 0.8)',
    'rgba(17, 24, 39, 0.8)'
  );
  const navbarShadow = useColorModeValue(
    '0px 4px 20px rgba(0, 0, 0, 0.1)',
    '0px 4px 20px rgba(0, 0, 0, 0.3)'
  );
  const navbarBorder = useColorModeValue('gray.200', 'gray.700');

  // Precise navbar positioning for perfect sidebar alignment
  const boxStyle: BoxProps = {
    position: 'fixed',
    top: '0',
    // Navbar starts exactly where sidebar ends for seamless integration
    // Since navbar is inside a container with marginLeft, we don't need additional left offset
    left: {
      base: '0',
      md: '0',
      lg: '0'  // No left offset needed since parent container handles positioning
    },
    // Remove right property to avoid positioning conflicts
    // Width calculation ensures no gaps or overlaps
    // Since parent container handles width, navbar uses full width of its container
    width: {
      base: '100%',
      md: '100%',
      lg: '100%'  // Full width of parent container (which is already sized correctly)
    },
    zIndex: 1200, // Higher than sidebar (1050) for proper layering
    boxShadow: navbarShadow,
    bg: navbarBg,
    borderColor: navbarBorder,
    borderRadius: '0',
    // Only bottom border for visual separation - no side borders
    borderWidth: '0',
    borderBottomWidth: '1px',
    borderStyle: 'solid',
    // Smooth 300ms transitions for sidebar state changes
    transition: `${TRANSITIONS.hover}, left 300ms cubic-bezier(0.4, 0, 0.2, 1), width 300ms cubic-bezier(0.4, 0, 0.2, 1)`,
    minH: NAVBAR_HEIGHT,
    h: NAVBAR_HEIGHT,
    backdropFilter: 'blur(20px)',
    mx: '0',
    // No horizontal padding at box level - handled by inner content
    px: '0',
    py: { base: '2', md: '3' },
    sx: {
      // Performance optimizations
      willChange: PERFORMANCE.willChange,
      // Ensure proper layering and eliminate gaps
      isolation: 'isolate',
      // Ensure no margin/padding interference
      margin: 0,
      padding: 0,
      // Prevent any potential gaps from box-sizing
      boxSizing: 'border-box',
      // Ensure seamless connection with sidebar
      borderLeft: 'none',
      borderRight: 'none',
      borderTop: 'none',
      // Only keep bottom border for visual separation
      borderBottomWidth: '1px',
      borderBottomStyle: 'solid',
      // Prevent any visual artifacts
      outline: 'none',
      // Ensure no background bleeding
      backgroundClip: 'padding-box',
    }
  };

  return (
    <Box
      {...boxStyle}
      alignItems="center"
      display="flex"
      justifyContent="center"
      role="banner"
      aria-label="Site navigation"
      data-testid="navbar"
    >
      <Flex
        w="100%"
        alignItems="center"
        justifyContent="space-between"
        flexDirection="row"
        gap={{ base: '2', md: '4' }}
        // Apply padding to match main content area for perfect alignment
        px={navbarPadding}
      >
        {/* Brand Section - Mobile menu + Breadcrumb + Title */}
        <NavbarBrand brandText={brandText} />

        {/* Search Box - Desktop only */}
        <Box
          display={{ base: 'none', lg: 'block' }}
          flex="0 0 auto"
          minW="300px"
          maxW="500px"
        >
          <SearchBox size="md" placeholder="Ara... (Ctrl+K)" />
        </Box>

        {/* Actions Section - Search, Notifications, Theme Toggle */}
        <NavbarActions onSearchClick={() => setIsMobileSearchOpen(true)} />

        {/* User Menu */}
        <NavbarUserMenu />
      </Flex>

      {/* Mobile Search Modal */}
      <MobileSearchModal
        isOpen={isMobileSearchOpen}
        onClose={() => setIsMobileSearchOpen(false)}
      />
    </Box>
  );
}