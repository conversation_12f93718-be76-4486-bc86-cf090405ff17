import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Button, 
  FormControl, 
  FormLabel, 
  Input, 
  VStack, 
  Checkbox, 
  Text, 
  InputGroup, 
  InputRightElement, 
  IconButton,
  Alert, 
  AlertIcon, 
  useToast 
} from '@chakra-ui/react';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { supabase } from '../../supabaseClient';

const Signup = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const navigate = useNavigate();
  const toast = useToast();

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!termsAccepted) {
      setError('Kayıt olmak için kullanım koşullarını kabul etmelisiniz.');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) {
        setError(error.message);
        toast({
          title: 'Kayıt başarısız',
          description: error.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } else {
        setSuccess(true);
        toast({
          title: 'Hesap başarıyla oluşturuldu!',
          description: 'Hesabınızı doğrulamak için lütfen e-postanızı kontrol edin.',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        // Bazı Supabase ayarlarında e-posta onayı olmadan kayıt olunabilir
        if (data.session) {
          navigate('/');
        }
      }
    } catch (err) {
      const errorMessage = (err as Error).message;
      setError(errorMessage);
      toast({
        title: 'Bir hata oluştu',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box as="form" onSubmit={onSubmit}>
      {error && (
        <Alert status="error" mb={5} borderRadius="md">
          <AlertIcon />
          {error}
        </Alert>
      )}

      {success && (
        <Alert status="success" mb={5} borderRadius="md">
          <AlertIcon />
          E-posta adresinize gönderilen onay bağlantısını tıklayarak hesabınızı aktifleştirebilirsiniz.
        </Alert>
      )}

      <VStack spacing={5}>
        <FormControl isRequired>
          <FormLabel fontWeight="medium">Ad Soyad</FormLabel>
          <Input 
            placeholder="Ad Soyadınız" 
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            size="lg"
            borderRadius="md"
            autoComplete="name"
            _focus={{ borderColor: "brand.500", boxShadow: "0 0 0 1px var(--chakra-colors-brand-500)" }}
          />
        </FormControl>
        
        <FormControl isRequired>
          <FormLabel fontWeight="medium">E-posta</FormLabel>
          <Input 
            type="email" 
            placeholder="E-posta adresiniz" 
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            size="lg"
            borderRadius="md"
            autoComplete="email"
            _focus={{ borderColor: "brand.500", boxShadow: "0 0 0 1px var(--chakra-colors-brand-500)" }}
          />
        </FormControl>
        
        <FormControl isRequired>
          <FormLabel fontWeight="medium">Şifre</FormLabel>
          <InputGroup size="lg">
            <Input 
              type={showPassword ? "text" : "password"}
              placeholder="Şifreniz"
              value={password} 
              onChange={(e) => setPassword(e.target.value)}
              borderRadius="md"
              autoComplete="new-password"
              _focus={{ borderColor: "brand.500", boxShadow: "0 0 0 1px var(--chakra-colors-brand-500)" }}
            />
            <InputRightElement>
              <IconButton
                bg="transparent"
                variant="ghost"
                aria-label={showPassword ? "Şifreyi gizle" : "Şifreyi göster"}
                icon={showPassword ? <FaEyeSlash /> : <FaEye />}
                onClick={toggleShowPassword}
                size="sm"
                _focus={{ boxShadow: "none" }}
              />
            </InputRightElement>
          </InputGroup>
        </FormControl>
        
        <FormControl mt={2}>
          <Checkbox 
            colorScheme="brand"
            isChecked={termsAccepted}
            onChange={(e) => setTermsAccepted(e.target.checked)}
          >
            <Text fontSize="sm">
              Kullanım koşullarını ve gizlilik politikasını kabul ediyorum.
            </Text>
          </Checkbox>
        </FormControl>
        
        <Button 
          colorScheme="brand" 
          width="100%"
          size="lg"
          mt={2}
          type="submit"
          isLoading={isLoading}
          loadingText="Kayıt yapılıyor..."
          isDisabled={!termsAccepted || isLoading}
          boxShadow="md"
          _hover={{
            transform: "translateY(-1px)",
            boxShadow: "lg",
          }}
        >
          Hesap Oluştur
        </Button>
      </VStack>
    </Box>
  );
};

export default Signup; 