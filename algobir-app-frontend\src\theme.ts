import { extendTheme } from '@chakra-ui/react';
import { mode } from '@chakra-ui/theme-tools';

// Horizon UI renk paleti - güncellenmiş ve genişletilmiş
const colors = {
  brand: {
    50: '#F7FAFC',
    100: '#E9E3FF',
    200: '#D6BCFA',
    300: '#B794F6',
    400: '#9F7AEA',
    500: '#422AFB', // Ana marka rengi
    600: '#3311DB',
    700: '#2D3748',
    800: '#190793',
    900: '#11047A'
  },
  brandScheme: {
    50: '#F7FAFC',
    100: '#E9E3FF',
    200: '#7551FF',
    300: '#7551FF',
    400: '#7551FF',
    500: '#422AFB',
    600: '#3311DB',
    700: '#02044A',
    800: '#190793',
    900: '#02044A'
  },
  brandTabs: {
    50: '#F7FAFC',
    100: '#E9E3FF',
    200: '#422AFB',
    300: '#422AFB',
    400: '#422AFB',
    500: '#422AFB',
    600: '#3311DB',
    700: '#02044A',
    800: '#190793',
    900: '#02044A'
  },
  secondaryGray: {
    50: '#F7FAFC',
    100: '#E0E5F2',
    200: '#E1E9F8',
    300: '#F4F7FE',
    400: '#E9EDF7',
    500: '#8F9BBA',
    600: '#A3AED0',
    700: '#707EAE',
    800: '#707EAE',
    900: '#1B2559'
  },
  red: {
    50: '#FED7D7',
    100: '#FEEFEE',
    200: '#FEB2B2',
    300: '#FC8181',
    400: '#F56565',
    500: '#EE5D50',
    600: '#E31A1A',
    700: '#C53030',
    800: '#9B2C2C',
    900: '#742A2A'
  },
  blue: {
    50: '#EFF4FB',
    100: '#DBEAFE',
    200: '#BFDBFE',
    300: '#93C5FD',
    400: '#60A5FA',
    500: '#3965FF',
    600: '#2563EB',
    700: '#1D4ED8',
    800: '#1E40AF',
    900: '#1E3A8A'
  },
  orange: {
    50: '#FFFBEB',
    100: '#FFF6DA',
    200: '#FED7AA',
    300: '#FDBA74',
    400: '#FB923C',
    500: '#FFB547',
    600: '#EA580C',
    700: '#C2410C',
    800: '#9A3412',
    900: '#7C2D12'
  },
  green: {
    50: '#ECFDF5',
    100: '#E6FAF5',
    200: '#A7F3D0',
    300: '#6EE7B7',
    400: '#34D399',
    500: '#01B574',
    600: '#059669',
    700: '#047857',
    800: '#065F46',
    900: '#064E3B'
  },
  navy: {
    50: '#d0dcfb',
    100: '#aac0fe',
    200: '#a3b9f8',
    300: '#728fea',
    400: '#3652ba',
    500: '#1b3bbb',
    600: '#24388a',
    700: '#1B254B',
    800: '#111c44',
    900: '#0b1437'
  },
  gray: {
    50: '#F9FAFB',
    100: '#FAFCFE',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827'
  }
};

// Horizon UI tipografi sistemi
const fontSizes = {
  xs: '0.75rem',    // 12px
  sm: '0.875rem',   // 14px
  md: '1rem',       // 16px
  lg: '1.125rem',   // 18px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '1.875rem', // 30px
  '4xl': '2.25rem', // 36px
  '5xl': '3rem',    // 48px
  '6xl': '3.75rem', // 60px
  '7xl': '4.5rem',  // 72px
  '8xl': '6rem',    // 96px
  '9xl': '8rem',    // 128px
};

// Horizon UI spacing sistemi
const space = {
  px: '1px',
  0.5: '0.125rem',
  1: '0.25rem',
  1.5: '0.375rem',
  2: '0.5rem',
  2.5: '0.625rem',
  3: '0.75rem',
  3.5: '0.875rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  7: '1.75rem',
  8: '2rem',
  9: '2.25rem',
  10: '2.5rem',
  12: '3rem',
  14: '3.5rem',
  16: '4rem',
  20: '5rem',
  24: '6rem',
  28: '7rem',
  32: '8rem',
  36: '9rem',
  40: '10rem',
  44: '11rem',
  48: '12rem',
  52: '13rem',
  56: '14rem',
  60: '15rem',
  64: '16rem',
  72: '18rem',
  80: '20rem',
  96: '24rem',
};

// Responsive breakpoints
const breakpoints = {
  base: '0em',
  sm: '30em',    // 480px
  md: '48em',    // 768px
  lg: '62em',    // 992px
  xl: '80em',    // 1280px
  '2xl': '96em', // 1536px
};

// Font yapılandırması - Horizon UI'ya uygun
const fonts = {
  heading: `'DM Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"`,
  body: `'DM Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"`,
  mono: `SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace`,
};

// Horizon UI gölge sistemi
const shadows = {
  xs: '0 0 0 1px rgba(0, 0, 0, 0.05)',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  outline: '0 0 0 3px rgba(66, 153, 225, 0.6)',
  inner: 'inset 0 2px 4px 0 rgba(0,0,0,0.06)',
  none: 'none',
  // Horizon UI özel gölgeleri
  'card': '0px 3.5px 5.5px rgba(0, 0, 0, 0.02)',
  'navbar': '0px 7px 23px rgba(0, 0, 0, 0.05)',
  'button': '45px 76px 113px 7px rgba(112, 144, 176, 0.08)',
  'dark-lg': 'rgba(0, 0, 0, 0.1) 0px 0px 0px 1px, rgba(0, 0, 0, 0.2) 0px 5px 10px, rgba(0, 0, 0, 0.4) 0px 15px 40px'
};

// Border radius sistemi
const radii = {
  none: '0',
  sm: '0.125rem',
  base: '0.25rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  '3xl': '1.5rem',
  full: '9999px',
  // Horizon UI özel radius değerleri
  card: '20px',
  button: '16px',
  input: '16px',
};

// Card bileşeni - Horizon UI stilinde
const Card = {
  baseStyle: (props: any) => ({
    p: { base: '16px', md: '20px' },
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    position: 'relative',
    borderRadius: 'card',
    minWidth: '0px',
    wordWrap: 'break-word',
    bg: mode('white', 'navy.800')(props),
    backgroundClip: 'border-box',
    boxShadow: mode('card', 'none')(props),
    border: '0px solid transparent',
    overflow: 'hidden',
    transition: 'all 0.2s ease'
  }),
  variants: {
    elevated: (props: any) => ({
      boxShadow: mode('lg', 'dark-lg')(props),
    }),
    outline: (props: any) => ({
      border: '1px solid',
      borderColor: mode('gray.200', 'navy.600')(props),
      boxShadow: 'none',
    }),
  },
  defaultProps: {
    variant: 'elevated',
  },
};

// Tema bileşenlerini özelleştirme - Horizon UI stilinde
const components = {
  Button: {
    baseStyle: {
      borderRadius: 'button',
      fontWeight: '500',
      transition: 'all 0.2s ease',
      boxSizing: 'border-box',
      _focus: {
        boxShadow: 'none'
      },
      _active: {
        boxShadow: 'none'
      }
    },
    variants: {
      brand: (props: any) => ({
        bg: mode('brand.500', 'brand.400')(props),
        color: 'white',
        boxShadow: mode('button', 'none')(props),
        _focus: {
          bg: mode('brand.500', 'brand.400')(props)
        },
        _active: {
          bg: mode('brand.600', 'brand.500')(props)
        },
        _hover: {
          bg: mode('brand.600', 'brand.500')(props),
          _disabled: {
            bg: mode('brand.500', 'brand.400')(props),
          }
        }
      }),
      outline: (props: any) => ({
        borderRadius: 'button',
        border: '1px solid',
        borderColor: mode('gray.200', 'navy.600')(props),
        color: mode('gray.700', 'white')(props),
        _hover: {
          bg: mode('gray.50', 'whiteAlpha.100')(props),
        }
      }),
      ghost: (props: any) => ({
        color: mode('gray.700', 'white')(props),
        _hover: {
          bg: mode('gray.100', 'whiteAlpha.100')(props),
        },
        _active: {
          bg: mode('gray.200', 'whiteAlpha.200')(props),
        }
      }),
      darkBrand: (props: any) => ({
        bg: mode('brand.900', 'brand.400')(props),
        color: 'white',
        _focus: {
          bg: mode('brand.900', 'brand.400')(props)
        },
        _active: {
          bg: mode('brand.900', 'brand.400')(props)
        },
        _hover: {
          bg: mode('brand.800', 'brand.400')(props)
        }
      }),
      lightBrand: (props: any) => ({
        bg: mode('brand.50', 'whiteAlpha.100')(props),
        color: mode('brand.500', 'white')(props),
        _focus: {
          bg: mode('brand.50', 'whiteAlpha.100')(props)
        },
        _active: {
          bg: mode('brand.100', 'whiteAlpha.200')(props)
        },
        _hover: {
          bg: mode('brand.100', 'whiteAlpha.200')(props)
        }
      }),
      light: (props: any) => ({
        bg: mode('secondaryGray.300', 'whiteAlpha.100')(props),
        color: mode('secondaryGray.900', 'white')(props),
        _focus: {
          bg: mode('secondaryGray.300', 'whiteAlpha.100')(props)
        },
        _active: {
          bg: mode('secondaryGray.400', 'whiteAlpha.200')(props)
        },
        _hover: {
          bg: mode('secondaryGray.400', 'whiteAlpha.200')(props)
        }
      }),
    },
    sizes: {
      sm: {
        h: '32px',
        minW: '32px',
        fontSize: 'sm',
        px: '12px',
      },
      md: {
        h: '40px',
        minW: '40px',
        fontSize: 'md',
        px: '16px',
      },
      lg: {
        h: '48px',
        minW: '48px',
        fontSize: 'lg',
        px: '24px',
      },
    },
    defaultProps: {
      variant: 'brand',
      size: 'md',
    },
  },
  Card: Card,
  Heading: {
    baseStyle: {
      fontWeight: '700',
      letterSpacing: '-0.025em',
      lineHeight: '1.2',
    },
    sizes: {
      xs: { fontSize: 'md' },
      sm: { fontSize: 'lg' },
      md: { fontSize: 'xl' },
      lg: { fontSize: '2xl' },
      xl: { fontSize: '3xl' },
      '2xl': { fontSize: '4xl' },
    },
  },
  Input: {
    baseStyle: {
      field: {
        borderRadius: 'input',
        border: '1px solid',
        borderColor: 'gray.200',
        _focus: {
          borderColor: 'brand.500',
          boxShadow: '0 0 0 1px var(--chakra-colors-brand-500)',
        },
        _hover: {
          borderColor: 'gray.300',
        },
        transition: 'all 0.2s',
        fontSize: 'md',
      },
    },
    variants: {
      outline: (props: any) => ({
        field: {
          bg: mode('white', 'navy.800')(props),
          borderColor: mode('gray.200', 'navy.600')(props),
          color: mode('gray.700', 'white')(props),
          _placeholder: {
            color: mode('gray.400', 'gray.400')(props),
          },
        },
      }),
    },
    defaultProps: {
      variant: 'outline',
    },
  },
  Table: {
    baseStyle: {
      table: {
        borderCollapse: 'separate',
        borderSpacing: 0,
      },
      thead: {
        tr: {
          th: {
            borderBottom: '1px solid',
            borderColor: 'gray.200',
            fontSize: 'xs',
            fontWeight: '700',
            letterSpacing: '0.05em',
            textTransform: 'uppercase',
            color: 'gray.600',
            py: '12px',
            px: '16px',
          },
        },
      },
      tbody: {
        tr: {
          td: {
            borderBottom: '1px solid',
            borderColor: 'gray.100',
            py: '12px',
            px: '16px',
          },
          _hover: {
            bg: 'gray.50',
          },
        },
      },
    },
    variants: {
      simple: (props: any) => ({
        thead: {
          tr: {
            th: {
              borderColor: mode('gray.200', 'navy.600')(props),
              color: mode('gray.600', 'gray.400')(props),
            },
          },
        },
        tbody: {
          tr: {
            td: {
              borderColor: mode('gray.100', 'navy.700')(props),
            },
            _hover: {
              bg: mode('gray.50', 'whiteAlpha.50')(props),
            },
          },
        },
      }),
    },
  },
  Modal: {
    baseStyle: (props: any) => ({
      dialog: {
        borderRadius: 'card',
        bg: mode('white', 'navy.800')(props),
        boxShadow: mode('xl', 'dark-lg')(props),
      },
      header: {
        fontSize: 'lg',
        fontWeight: '700',
        color: mode('gray.700', 'white')(props),
      },
      body: {
        color: mode('gray.600', 'gray.300')(props),
      },
    }),
  },
  Menu: {
    baseStyle: (props: any) => ({
      list: {
        bg: mode('white', 'navy.800')(props),
        borderRadius: 'card',
        border: 'none',
        boxShadow: mode('xl', 'dark-lg')(props),
        py: '8px',
      },
      item: {
        bg: 'transparent',
        color: mode('gray.700', 'white')(props),
        _hover: {
          bg: mode('gray.100', 'whiteAlpha.100')(props),
        },
        _focus: {
          bg: mode('gray.100', 'whiteAlpha.100')(props),
        },
        borderRadius: 'md',
        mx: '8px',
        my: '2px',
      },
    }),
  },
  Link: {
    baseStyle: {
      transition: 'all 0.2s',
      _hover: {
        textDecoration: 'none',
      },
    },
  },
  Text: {
    baseStyle: {
      lineHeight: '1.6',
    },
    variants: {
      secondary: (props: any) => ({
        color: mode('gray.600', 'gray.400')(props),
      }),
    },
  },
};

// Global stiller - Horizon UI temasına uygun
const styles = {
  global: (props: { colorMode: string }) => ({
    body: {
      overflowX: 'hidden',
      bg: mode('secondaryGray.300', 'navy.900')(props),
      fontFamily: 'body',
      letterSpacing: '-0.025em',
      color: mode('gray.700', 'white')(props),
      lineHeight: '1.6',
    },
    '*': {
      borderColor: mode('gray.200', 'navy.600')(props),
    },
    '*::placeholder': {
      color: mode('gray.400', 'gray.400')(props),
    },
    html: {
      fontFamily: 'body',
      scrollBehavior: 'smooth',
    },
    // Chakra UI reset
    'html, body': {
      WebkitFontSmoothing: 'antialiased',
      MozOsxFontSmoothing: 'grayscale',
    },
    // Horizon UI scrollbar stilleri
    '::-webkit-scrollbar': {
      width: '8px',
      height: '8px',
    },
    '::-webkit-scrollbar-thumb': {
      background: props.colorMode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
      borderRadius: '8px',
    },
    '::-webkit-scrollbar-track': {
      background: props.colorMode === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.05)',
    },
    // Focus stilleri
    '*:focus': {
      outline: 'none',
    },
    // Selection stilleri
    '::selection': {
      bg: mode('brand.100', 'brand.700')(props),
      color: mode('brand.700', 'brand.100')(props),
    },
  }),
};

// Tema yapılandırması
const theme = extendTheme({
  colors,
  components,
  styles,
  fonts,
  fontSizes,
  space,
  shadows,
  radii,
  breakpoints,
  config: {
    initialColorMode: 'light',
    useSystemColorMode: false,
  },
  // Horizon UI özel token'ları
  semanticTokens: {
    colors: {
      'chakra-body-text': {
        _light: 'gray.700',
        _dark: 'white',
      },
      'chakra-body-bg': {
        _light: 'secondaryGray.300',
        _dark: 'navy.900',
      },
      'chakra-border-color': {
        _light: 'gray.200',
        _dark: 'navy.600',
      },
      'chakra-subtle-bg': {
        _light: 'gray.50',
        _dark: 'navy.800',
      },
      'chakra-subtle-text': {
        _light: 'gray.600',
        _dark: 'gray.400',
      },
    },
  },
});

export default theme; 