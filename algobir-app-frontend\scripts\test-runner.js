#!/usr/bin/env node

/**
 * Comprehensive Test Runner Script
 * Phase 3.1: Automated Testing Pipeline Setup
 * Orchestrates all testing phases with detailed reporting
 */

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// Test configuration
const TEST_CONFIG = {
  unit: {
    command: 'npm',
    args: ['run', 'test:unit:coverage'],
    timeout: 120000, // 2 minutes
    required: true
  },
  e2e: {
    command: 'npm',
    args: ['run', 'test:e2e'],
    timeout: 300000, // 5 minutes
    required: false // Optional for development
  },
  edgeFunctions: {
    command: 'deno',
    args: ['test', '--allow-all', 'supabase/functions/__tests__/'],
    timeout: 60000, // 1 minute
    required: true
  },
  lint: {
    command: 'npm',
    args: ['run', 'lint'],
    timeout: 30000, // 30 seconds
    required: true
  },
  build: {
    command: 'npm',
    args: ['run', 'build'],
    timeout: 180000, // 3 minutes
    required: true
  }
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Utility functions
const log = (message, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`);
};

const logSection = (title) => {
  const separator = '='.repeat(60);
  log(`\n${separator}`, colors.cyan);
  log(`${title}`, colors.cyan + colors.bright);
  log(`${separator}`, colors.cyan);
};

const logStep = (step, status = 'info') => {
  const statusColors = {
    info: colors.blue,
    success: colors.green,
    warning: colors.yellow,
    error: colors.red
  };
  log(`${statusColors[status]}▶ ${step}${colors.reset}`);
};

// Test execution function
const runTest = (testName, config) => {
  return new Promise((resolve, reject) => {
    logStep(`Starting ${testName} tests...`);
    
    const startTime = Date.now();
    const process = spawn(config.command, config.args, {
      cwd: projectRoot,
      stdio: 'pipe',
      shell: true
    });

    let stdout = '';
    let stderr = '';

    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    // Set timeout
    const timeout = setTimeout(() => {
      process.kill('SIGTERM');
      reject(new Error(`${testName} tests timed out after ${config.timeout}ms`));
    }, config.timeout);

    process.on('close', (code) => {
      clearTimeout(timeout);
      const duration = Date.now() - startTime;
      
      const result = {
        testName,
        code,
        duration,
        stdout,
        stderr,
        success: code === 0
      };

      if (code === 0) {
        logStep(`${testName} tests completed successfully (${duration}ms)`, 'success');
        resolve(result);
      } else {
        logStep(`${testName} tests failed with code ${code} (${duration}ms)`, 'error');
        if (config.required) {
          reject(new Error(`Required test ${testName} failed`));
        } else {
          logStep(`${testName} tests failed but are not required, continuing...`, 'warning');
          resolve(result);
        }
      }
    });

    process.on('error', (error) => {
      clearTimeout(timeout);
      logStep(`${testName} tests encountered an error: ${error.message}`, 'error');
      if (config.required) {
        reject(error);
      } else {
        resolve({
          testName,
          code: -1,
          duration: Date.now() - startTime,
          stdout: '',
          stderr: error.message,
          success: false
        });
      }
    });
  });
};

// Generate test report
const generateReport = async (results) => {
  const reportDir = path.join(projectRoot, 'test-results');
  await fs.mkdir(reportDir, { recursive: true });

  const totalTests = results.length;
  const passedTests = results.filter(r => r.success).length;
  const failedTests = totalTests - passedTests;
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      total: totalTests,
      passed: passedTests,
      failed: failedTests,
      duration: totalDuration,
      success: failedTests === 0
    },
    results: results.map(r => ({
      testName: r.testName,
      success: r.success,
      duration: r.duration,
      code: r.code,
      hasOutput: r.stdout.length > 0,
      hasErrors: r.stderr.length > 0
    })),
    details: results
  };

  // Write JSON report
  await fs.writeFile(
    path.join(reportDir, 'test-report.json'),
    JSON.stringify(report, null, 2)
  );

  // Write HTML report
  const htmlReport = generateHtmlReport(report);
  await fs.writeFile(
    path.join(reportDir, 'test-report.html'),
    htmlReport
  );

  return report;
};

// Generate HTML report
const generateHtmlReport = (report) => {
  const { summary, results } = report;
  const statusColor = summary.success ? '#28a745' : '#dc3545';
  
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Algobir Test Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: ${statusColor}; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
        .content { padding: 20px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #495057; }
        .stat-label { color: #6c757d; margin-top: 5px; }
        .test-results { margin-top: 20px; }
        .test-item { display: flex; align-items: center; padding: 10px; border-bottom: 1px solid #e9ecef; }
        .test-status { width: 20px; height: 20px; border-radius: 50%; margin-right: 15px; }
        .success { background: #28a745; }
        .failure { background: #dc3545; }
        .test-name { flex: 1; font-weight: 500; }
        .test-duration { color: #6c757d; font-size: 0.9em; }
        .footer { text-align: center; padding: 20px; color: #6c757d; border-top: 1px solid #e9ecef; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Algobir Test Report</h1>
            <p>Generated on ${new Date(report.timestamp).toLocaleString()}</p>
        </div>
        <div class="content">
            <div class="summary">
                <div class="stat-card">
                    <div class="stat-number">${summary.total}</div>
                    <div class="stat-label">Total Tests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #28a745">${summary.passed}</div>
                    <div class="stat-label">Passed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" style="color: #dc3545">${summary.failed}</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${(summary.duration / 1000).toFixed(1)}s</div>
                    <div class="stat-label">Duration</div>
                </div>
            </div>
            <div class="test-results">
                <h3>Test Results</h3>
                ${results.map(result => `
                    <div class="test-item">
                        <div class="test-status ${result.success ? 'success' : 'failure'}"></div>
                        <div class="test-name">${result.testName}</div>
                        <div class="test-duration">${(result.duration / 1000).toFixed(1)}s</div>
                    </div>
                `).join('')}
            </div>
        </div>
        <div class="footer">
            <p>Algobir Unicorn Standards Testing Pipeline - Phase 3.1</p>
        </div>
    </div>
</body>
</html>`;
};

// Main execution function
const main = async () => {
  const startTime = Date.now();
  
  logSection('🚀 ALGOBIR UNICORN STANDARDS TESTING PIPELINE');
  log('Phase 3.1: Automated Testing Pipeline Setup\n');

  const args = process.argv.slice(2);
  const skipE2E = args.includes('--skip-e2e');
  const onlyUnit = args.includes('--unit-only');
  const verbose = args.includes('--verbose');

  if (skipE2E) {
    TEST_CONFIG.e2e.required = false;
    logStep('E2E tests will be skipped', 'warning');
  }

  const results = [];
  let hasFailures = false;

  try {
    // Run tests in sequence
    const testsToRun = onlyUnit 
      ? ['lint', 'unit'] 
      : ['lint', 'unit', 'edgeFunctions', 'build', ...(skipE2E ? [] : ['e2e'])];

    for (const testName of testsToRun) {
      if (TEST_CONFIG[testName]) {
        try {
          const result = await runTest(testName, TEST_CONFIG[testName]);
          results.push(result);
          
          if (verbose && result.stdout) {
            log('\n--- Test Output ---', colors.blue);
            console.log(result.stdout);
          }
          
          if (!result.success) {
            hasFailures = true;
            if (result.stderr) {
              log('\n--- Error Output ---', colors.red);
              console.log(result.stderr);
            }
          }
        } catch (error) {
          hasFailures = true;
          results.push({
            testName,
            success: false,
            duration: 0,
            code: -1,
            stdout: '',
            stderr: error.message
          });
          logStep(`${testName} failed: ${error.message}`, 'error');
        }
      }
    }

    // Generate report
    logSection('📊 GENERATING TEST REPORT');
    const report = await generateReport(results);
    
    const totalDuration = Date.now() - startTime;
    
    logSection('✅ TEST PIPELINE COMPLETE');
    log(`Total Duration: ${(totalDuration / 1000).toFixed(1)}s`);
    log(`Tests Run: ${results.length}`);
    log(`Passed: ${results.filter(r => r.success).length}`, colors.green);
    log(`Failed: ${results.filter(r => !r.success).length}`, colors.red);
    log(`Report: test-results/test-report.html`, colors.blue);

    if (hasFailures) {
      log('\n❌ Some tests failed. Check the report for details.', colors.red);
      process.exit(1);
    } else {
      log('\n🎉 All tests passed successfully!', colors.green);
      process.exit(0);
    }

  } catch (error) {
    logStep(`Pipeline failed: ${error.message}`, 'error');
    process.exit(1);
  }
};

// Handle process signals
process.on('SIGINT', () => {
  log('\n\n⚠️  Test pipeline interrupted by user', colors.yellow);
  process.exit(130);
});

process.on('SIGTERM', () => {
  log('\n\n⚠️  Test pipeline terminated', colors.yellow);
  process.exit(143);
});

// Run the main function
main().catch((error) => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
