import { useState, useEffect, useCallback } from "react";
import {
  Box, Heading, Text, SimpleGrid, Card, CardBody, CardHeader,
  Stat, StatNumber, Spinner, Button, Progress, Badge,
  HStack, Icon, Alert, AlertIcon, VStack, Flex, Container,
  useColorModeValue, Grid, GridItem, StatHelpText, chakra,
  useBreakpointValue
} from "@chakra-ui/react";
import { keyframes } from "@emotion/react";
import {
  FaUsers, FaExchangeAlt, FaCalendarDay, FaCalendarWeek,
  FaBell, FaChartLine, FaFire, FaBolt, FaDatabase,
  FaCloud, FaShieldAlt, FaTrophy, FaSync
} from "react-icons/fa";
import { 
  MdDashboard, MdSpeed, MdSecurity
} from "react-icons/md";
import { supabase } from "../../supabaseClient";
import { Link } from "react-router-dom";

// Animations
const pulse = keyframes`
  0% { box-shadow: 0 0 0 0 rgba(124, 58, 237, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(124, 58, 237, 0); }
  100% { box-shadow: 0 0 0 0 rgba(124, 58, 237, 0); }
`;

const float = keyframes`
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
`;

const gradient = keyframes`
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
`;

// İstatistik tipi
interface AdminStats {
  totalUsers: number;
  totalTrades: number;
  tradesToday: number;
  tradesThisWeek: number;
  activeUsers: number;
}

const AdminDashboard = () => {
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalTrades: 0,
    tradesToday: 0,
    tradesThisWeek: 0,
    activeUsers: 0
  });
  
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Color mode values
  const bgGradient = useColorModeValue(
    'linear(to-br, gray.50, blue.50, purple.50)',
    'linear(to-br, gray.900, blue.900, purple.900)'
  );
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');
  const accentColor = useColorModeValue('purple.600', 'purple.300');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  
  // Responsive values
  const headerSize = useBreakpointValue({ base: '2xl', md: '3xl', lg: '4xl' });
  const containerMaxW = useBreakpointValue({ base: 'container.sm', md: 'container.md', lg: 'container.xl' });

  // Fetch admin statistics function
  const fetchAdminStats = useCallback(async () => {
    try {
      setLoading(true);

      // Toplam kullanıcı sayısı
      const { count: userCount, error: userError } = await supabase
        .from('user_settings')
        .select('*', { count: 'exact', head: true });

      if (userError) throw userError;

      // Toplam işlem sayısı
      const { count: totalTradesCount, error: totalTradesError } = await supabase
        .from('trades')
        .select('*', { count: 'exact', head: true });

      if (totalTradesError) throw totalTradesError;

      // Bugünün ve bu haftanın başlangıç tarihlerini hesapla
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      // Haftanın başlangıcı (Pazartesi olarak)
      const dayOfWeek = now.getDay(); // 0 = Pazar, 1 = Pazartesi
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
      weekStart.setHours(0, 0, 0, 0);

      // Bugünkü işlemler sayısı
      const { count: todayTradesCount, error: todayError } = await supabase
        .from('trades')
        .select('*', { count: 'exact', head: true })
        .gte('received_at', today.toISOString());

      if (todayError) throw todayError;

      // Bu haftaki işlemler sayısı
      const { count: weekTradesCount, error: weekError } = await supabase
        .from('trades')
        .select('*', { count: 'exact', head: true })
        .gte('received_at', weekStart.toISOString());

      if (weekError) throw weekError;

      // Aktif kullanıcıları hesapla (son 30 gündeki işlem yapan benzersiz kullanıcı sayısı)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data: activeUsersData, error: activeError } = await supabase
        .from('trades')
        .select('user_id')
        .gte('received_at', thirtyDaysAgo.toISOString())
        .limit(10000); // Üst limit belirt

      if (activeError) throw activeError;

      // Benzersiz kullanıcı ID'lerini say
      const activeUserIds = new Set();
      if (activeUsersData) {
        activeUsersData.forEach(trade => {
          if (trade.user_id) {
            activeUserIds.add(trade.user_id);
          }
        });
      }

      setStats({
        totalUsers: userCount || 0,
        totalTrades: totalTradesCount || 0,
        tradesToday: todayTradesCount || 0,
        tradesThisWeek: weekTradesCount || 0,
        activeUsers: activeUserIds.size
      });
    } catch (err: any) {
      console.error('Admin istatistikleri yüklenirken hata oluştu:', err);
      setError(err.message || 'İstatistikler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, []);

  // Page visibility state
  const [isPageVisible, setIsPageVisible] = useState(!document.hidden);

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsPageVisible(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Initial data fetch and real-time updates
  useEffect(() => {
    fetchAdminStats();

    // Set up real-time subscription for trades table
    const tradesChannel = supabase
      .channel('admin_trades_updates')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'trades'
      }, () => {
        // Only update if page is visible to avoid unnecessary queries
        if (!document.hidden) {
          fetchAdminStats();
        }
      })
      .subscribe();

    // Set up real-time subscription for user_settings table
    const usersChannel = supabase
      .channel('admin_users_updates')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'user_settings'
      }, () => {
        // Only update if page is visible to avoid unnecessary queries
        if (!document.hidden) {
          fetchAdminStats();
        }
      })
      .subscribe();

    // Cleanup subscriptions
    return () => {
      supabase.removeChannel(tradesChannel);
      supabase.removeChannel(usersChannel);
    };
  }, [fetchAdminStats]);

  // Refresh data when page becomes visible again
  useEffect(() => {
    if (isPageVisible) {
      fetchAdminStats();
    }
  }, [isPageVisible, fetchAdminStats]);

  if (loading) {
    return (
      <Box 
        minH="100vh" 
        bgGradient={bgGradient}
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <VStack spacing={8}>
          <Box
            animation={`${pulse} 2s infinite`}
            p={8}
            borderRadius="full"
            bg={cardBg}
            boxShadow="2xl"
          >
            <Spinner 
              size="xl" 
              thickness="6px"
              speed="0.8s"
              color="purple.500"
            />
          </Box>
          <VStack spacing={4}>
            <Heading size="lg" color={textColor} fontWeight="bold">
              Admin Paneli Yükleniyor...
            </Heading>
            <Text color={mutedColor} fontSize="lg">
              Sistem verileri analiz ediliyor
            </Text>
            <Progress 
              size="lg" 
              isIndeterminate 
              colorScheme="purple" 
              w="300px"
              borderRadius="full"
            />
          </VStack>
        </VStack>
      </Box>
    );
  }

  if (error) {
    return (
      <Box minH="100vh" bgGradient={bgGradient}>
        <Container maxW={containerMaxW}>
          <Alert 
            status="error" 
            borderRadius="2xl" 
            boxShadow="2xl"
            border="2px solid"
            borderColor="red.200"
            bg={cardBg}
          >
            <AlertIcon />
            <VStack align="start" spacing={2}>
              <Heading size="md" color="red.500">Veri Yükleme Hatası</Heading>
              <Text color={textColor}>{error}</Text>
            </VStack>
          </Alert>
        </Container>
      </Box>
    );
  }

  return (
    <Box
      minH="100vh"
      bgGradient={bgGradient}
      position="relative"
      overflow="hidden"
    >
      {/* Background decorative elements */}
      <Box
        position="absolute"
        top="10%"
        right="5%"
        w="400px"
        h="400px"
        bgGradient="radial(circle, purple.100, transparent)"
        borderRadius="full"
        filter="blur(3px)"
        opacity="0.3"
        animation={`${float} 6s ease-in-out infinite`}
      />
      <Box
        position="absolute"
        bottom="10%"
        left="5%"
        w="300px"
        h="300px"
        bgGradient="radial(circle, blue.100, transparent)"
        borderRadius="full"
        filter="blur(2px)"
        opacity="0.4"
        animation={`${float} 4s ease-in-out infinite reverse`}
      />

      <Container maxW={containerMaxW} position="relative" zIndex={1}>
        <VStack spacing={12} align="stretch">
          {/* Hero Header */}
          <Box textAlign="center" py={8}>
            <Badge 
              size="lg"
              px={8} 
              py={4} 
              borderRadius="full"
              bgGradient="linear(to-r, purple.400, pink.400, blue.500)"
              color="white"
              fontWeight="bold"
              fontSize="xl"
              textTransform="none"
              boxShadow="2xl"
              animation={`${pulse} 3s infinite`}
              mb={6}
            >
              👑 ADMIN CONTROL CENTER
            </Badge>
            
            <Heading 
              as="h1" 
              size={headerSize}
              bgGradient="linear(to-r, purple.400, blue.400, pink.400)"
              bgClip="text"
              fontWeight="black"
              letterSpacing="tight"
              lineHeight="1.1"
              mb={4}
            >
              Algobir Yönetim Paneli
            </Heading>
            
            <Text 
              fontSize={{ base: 'lg', md: 'xl' }}
              color={mutedColor}
              maxW="800px"
              mx="auto"
              lineHeight="1.6"
              fontWeight="medium"
            >
              Sistem performansını izleyin, kullanıcıları yönetin ve platform sağlığını kontrol edin.
              <br />
              <chakra.span color={accentColor} fontWeight="bold">
                Tüm sistem kontrolleri tek yerden
              </chakra.span>
            </Text>

            {/* Refresh Button */}
            <Flex justify="center" mt={6}>
              <Button
                onClick={fetchAdminStats}
                size="lg"
                bgGradient="linear(to-r, blue.400, purple.500)"
                color="white"
                _hover={{
                  bgGradient: "linear(to-r, blue.500, purple.600)",
                  transform: "scale(1.05)"
                }}
                borderRadius="2xl"
                px={8}
                py={6}
                fontSize="lg"
                fontWeight="bold"
                boxShadow="xl"
                transition="all 0.3s"
                isLoading={loading}
                loadingText="Güncelleniyor..."
                leftIcon={<Icon as={FaSync} />}
              >
                Verileri Yenile
              </Button>
            </Flex>
          </Box>

          {/* Quick Action Buttons - Premium Design */}
          <Card
            bg={cardBg}
            borderRadius="3xl"
            boxShadow="2xl"
            border="1px solid"
            borderColor={borderColor}
            overflow="hidden"
            position="relative"
            _hover={{ transform: 'translateY(-2px)', boxShadow: '3xl' }}
            transition="all 0.3s"
          >
            <Box
              position="absolute"
              top="0"
              left="0"
              right="0"
              h="6px"
              bgGradient="linear(to-r, purple.400, pink.400, blue.400, green.400)"
              animation={`${gradient} 3s ease infinite`}
              backgroundSize="200% 200%"
            />
            
            <CardHeader pb={4}>
              <HStack spacing={4} justify="center">
                <Icon as={MdDashboard} color={accentColor} boxSize={8} />
                <Heading size="xl" color={textColor} fontWeight="bold">
                  Hızlı Erişim Menüsü
                </Heading>
              </HStack>
            </CardHeader>
            
            <CardBody pt={0}>
              <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)', lg: 'repeat(3, 1fr)', xl: 'repeat(5, 1fr)' }} gap={6}>
                {/* Kullanıcı Yönetimi */}
                <Button
                  as={Link}
                  to="/admin/users"
                  size="xl"
                  h="120px"
                  bgGradient="linear(to-br, blue.400, blue.600)"
                  color="white"
                  _hover={{
                    bgGradient: "linear(to-br, blue.500, blue.700)",
                    transform: "translateY(-4px)",
                    boxShadow: "xl"
                  }}
                  boxShadow="lg"
                  borderRadius="2xl"
                  border="3px solid"
                  borderColor="blue.200"
                  transition="all 0.3s"
                  position="relative"
                  overflow="hidden"
                >
                  <VStack spacing={3}>
                    <Box
                      p={3}
                      borderRadius="full"
                      bg="whiteAlpha.200"
                      animation={`${float} 2s ease-in-out infinite`}
                    >
                      <Icon as={FaUsers} fontSize="2xl" />
                    </Box>
                    <VStack spacing={1}>
                      <Text fontWeight="bold" fontSize="lg">Kullanıcı Yönetimi</Text>
                      <Text fontSize="sm" opacity="0.9">Tüm kullanıcıları yönet</Text>
                    </VStack>
                  </VStack>
                  <Box
                    position="absolute"
                    top="10px"
                    right="10px"
                    w="20px"
                    h="20px"
                    borderRadius="full"
                    bg="whiteAlpha.300"
                    animation={`${pulse} 1s infinite`}
                  />
                </Button>

                {/* İşlem Yönetimi */}
                <Button
                  as={Link}
                  to="/admin/trades"
                  size="xl"
                  h="120px"
                  bgGradient="linear(to-br, green.400, emerald.600)"
                  color="white"
                  _hover={{
                    bgGradient: "linear(to-br, green.500, emerald.700)",
                    transform: "translateY(-4px)",
                    boxShadow: "xl"
                  }}
                  boxShadow="lg"
                  borderRadius="2xl"
                  border="3px solid"
                  borderColor="green.200"
                  transition="all 0.3s"
                  position="relative"
                  overflow="hidden"
                >
                  <VStack spacing={3}>
                    <Box
                      p={3}
                      borderRadius="full"
                      bg="whiteAlpha.200"
                      animation={`${float} 2s ease-in-out infinite 0.5s`}
                    >
                      <Icon as={FaExchangeAlt} fontSize="2xl" />
                    </Box>
                    <VStack spacing={1}>
                      <Text fontWeight="bold" fontSize="lg">İşlem Yönetimi</Text>
                      <Text fontSize="sm" opacity="0.9">Tüm işlemleri izle</Text>
                    </VStack>
                  </VStack>
                  <Box
                    position="absolute"
                    top="10px"
                    right="10px"
                    w="20px"
                    h="20px"
                    borderRadius="full"
                    bg="whiteAlpha.300"
                    animation={`${pulse} 1s infinite 0.5s`}
                  />
                </Button>

                {/* Bildirim Yönetimi */}
                <Button
                  as={Link}
                  to="/admin/notifications"
                  size="xl"
                  h="120px"
                  bgGradient="linear(to-br, purple.400, pink.600)"
                  color="white"
                  _hover={{
                    bgGradient: "linear(to-br, purple.500, pink.700)",
                    transform: "translateY(-4px)",
                    boxShadow: "xl"
                  }}
                  boxShadow="lg"
                  borderRadius="2xl"
                  border="3px solid"
                  borderColor="purple.200"
                  transition="all 0.3s"
                  position="relative"
                  overflow="hidden"
                >
                  <VStack spacing={3}>
                    <Box
                      p={3}
                      borderRadius="full"
                      bg="whiteAlpha.200"
                      animation={`${float} 2s ease-in-out infinite 1s`}
                    >
                      <Icon as={FaBell} fontSize="2xl" />
                    </Box>
                    <VStack spacing={1}>
                      <Text fontWeight="bold" fontSize="lg">Bildirim Yönetimi</Text>
                      <Text fontSize="sm" opacity="0.9">Duyuru gönder</Text>
                    </VStack>
                  </VStack>
                  <Box
                    position="absolute"
                    top="10px"
                    right="10px"
                    w="20px"
                    h="20px"
                    borderRadius="full"
                    bg="whiteAlpha.300"
                    animation={`${pulse} 1s infinite 1s`}
                  />
                </Button>

                {/* İstatistikler */}
                <Button
                  as={Link}
                  to="/admin/statistics"
                  size="xl"
                  h="120px"
                  bgGradient="linear(to-br, orange.400, yellow.600)"
                  color="white"
                  _hover={{
                    bgGradient: "linear(to-br, orange.500, yellow.700)",
                    transform: "translateY(-4px)",
                    boxShadow: "xl"
                  }}
                  boxShadow="lg"
                  borderRadius="2xl"
                  border="3px solid"
                  borderColor="orange.200"
                  transition="all 0.3s"
                  position="relative"
                  overflow="hidden"
                >
                  <VStack spacing={3}>
                    <Box
                      p={3}
                      borderRadius="full"
                      bg="whiteAlpha.200"
                      animation={`${float} 2s ease-in-out infinite 1.5s`}
                    >
                      <Icon as={FaChartLine} fontSize="2xl" />
                    </Box>
                    <VStack spacing={1}>
                      <Text fontWeight="bold" fontSize="lg">İstatistikler</Text>
                      <Text fontSize="sm" opacity="0.9">Detaylı analiz</Text>
                    </VStack>
                  </VStack>
                  <Box
                    position="absolute"
                    top="10px"
                    right="10px"
                    w="20px"
                    h="20px"
                    borderRadius="full"
                    bg="whiteAlpha.300"
                    animation={`${pulse} 1s infinite 1.5s`}
                  />
                </Button>

                {/* Sistem Durumu */}
                <Button
                  as={Link}
                  to="/admin/status"
                  size="xl"
                  h="120px"
                  bgGradient="linear(to-br, teal.400, cyan.600)"
                  color="white"
                  _hover={{
                    bgGradient: "linear(to-br, teal.500, cyan.700)",
                    transform: "translateY(-4px)",
                    boxShadow: "xl"
                  }}
                  boxShadow="lg"
                  borderRadius="2xl"
                  border="3px solid"
                  borderColor="teal.200"
                  transition="all 0.3s"
                  position="relative"
                  overflow="hidden"
                >
                  <VStack spacing={3}>
                    <Box
                      p={3}
                      borderRadius="full"
                      bg="whiteAlpha.200"
                      animation={`${float} 2s ease-in-out infinite 2s`}
                    >
                      <Icon as={FaDatabase} fontSize="2xl" />
                    </Box>
                    <VStack spacing={1}>
                      <Text fontWeight="bold" fontSize="lg">Sistem Durumu</Text>
                      <Text fontSize="sm" opacity="0.9">Uptime izleme</Text>
                    </VStack>
                  </VStack>
                  <Box
                    position="absolute"
                    top="10px"
                    right="10px"
                    w="20px"
                    h="20px"
                    borderRadius="full"
                    bg="whiteAlpha.300"
                    animation={`${pulse} 1s infinite 2s`}
                  />
                </Button>
              </Grid>
            </CardBody>
          </Card>

          {/* Premium Statistics Grid */}
          <Grid templateColumns={{ base: '1fr', lg: 'repeat(3, 1fr)' }} gap={6}>
            {/* Premium Stat Card 1 - Total Users */}
            <GridItem>
              <Card
                bg={cardBg}
                borderRadius="3xl"
                boxShadow="2xl"
                border="1px solid"
                borderColor={borderColor}
                overflow="hidden"
                position="relative"
                h="200px"
                _hover={{ 
                  transform: 'translateY(-8px) scale(1.02)', 
                  boxShadow: '3xl',
                  borderColor: 'blue.400'
                }}
                transition="all 0.4s"
              >
                <Box
                  position="absolute"
                  top="0"
                  left="0"
                  right="0"
                  h="4px"
                  bgGradient="linear(to-r, blue.400, cyan.400)"
                />
                
                <CardBody p={8} h="full">
                  <Flex justify="space-between" align="center" h="full">
                    <VStack align="start" spacing={4} flex={1}>
                      <HStack spacing={3}>
                        <Box
                          p={3}
                          borderRadius="xl"
                          bgGradient="linear(to-br, blue.100, cyan.100)"
                        >
                          <Icon as={FaUsers} color="blue.500" boxSize={6} />
                        </Box>
                        <VStack align="start" spacing={0}>
                          <Heading size="sm" color={mutedColor} fontWeight="medium">
                            Toplam Kullanıcı
                          </Heading>
                          <Badge colorScheme="blue" variant="subtle" borderRadius="full" px={3}>
                            Kayıtlı
                          </Badge>
                        </VStack>
                      </HStack>
                      
                      <Stat>
                        <StatNumber
                          fontSize="4xl"
                          fontWeight="black"
                          color="blue.500"
                        >
                          {stats.totalUsers.toLocaleString()}
                        </StatNumber>
                        <StatHelpText fontSize="md" color={mutedColor} fontWeight="medium">
                          Platform geneli
                        </StatHelpText>
                      </Stat>
                    </VStack>
                    
                    <Box
                      animation={`${float} 3s ease-in-out infinite`}
                    >
                      <Icon as={FaTrophy} color="blue.300" boxSize={12} opacity="0.3" />
                    </Box>
                  </Flex>
                </CardBody>
              </Card>
            </GridItem>

            {/* Premium Stat Card 2 - Total Trades */}
            <GridItem>
              <Card
                bg={cardBg}
                borderRadius="3xl"
                boxShadow="2xl"
                border="1px solid"
                borderColor={borderColor}
                overflow="hidden"
                position="relative"
                h="200px"
                _hover={{ 
                  transform: 'translateY(-8px) scale(1.02)', 
                  boxShadow: '3xl',
                  borderColor: 'purple.400'
                }}
                transition="all 0.4s"
              >
                <Box
                  position="absolute"
                  top="0"
                  left="0"
                  right="0"
                  h="4px"
                  bgGradient="linear(to-r, purple.400, pink.400)"
                />
                
                <CardBody p={8} h="full">
                  <Flex justify="space-between" align="center" h="full">
                    <VStack align="start" spacing={4} flex={1}>
                      <HStack spacing={3}>
                        <Box
                          p={3}
                          borderRadius="xl"
                          bgGradient="linear(to-br, purple.100, pink.100)"
                        >
                          <Icon as={FaExchangeAlt} color="purple.500" boxSize={6} />
                        </Box>
                        <VStack align="start" spacing={0}>
                          <Heading size="sm" color={mutedColor} fontWeight="medium">
                            Toplam İşlem
                          </Heading>
                          <Badge colorScheme="purple" variant="subtle" borderRadius="full" px={3}>
                            Tamamlanan
                          </Badge>
                        </VStack>
                      </HStack>
                      
                      <Stat>
                        <StatNumber
                          fontSize="4xl"
                          fontWeight="black"
                          color="purple.500"
                        >
                          {stats.totalTrades.toLocaleString()}
                        </StatNumber>
                        <StatHelpText fontSize="md" color={mutedColor} fontWeight="medium">
                          Başlangıçtan beri
                        </StatHelpText>
                      </Stat>
                    </VStack>
                    
                    <Box
                      animation={`${float} 3s ease-in-out infinite 0.5s`}
                    >
                      <Icon as={FaChartLine} color="purple.300" boxSize={12} opacity="0.3" />
                    </Box>
                  </Flex>
                </CardBody>
              </Card>
            </GridItem>

            {/* Premium Stat Card 3 - Active Users */}
            <GridItem>
              <Card
                bg={cardBg}
                borderRadius="3xl"
                boxShadow="2xl"
                border="1px solid"
                borderColor={borderColor}
                overflow="hidden"
                position="relative"
                h="200px"
                _hover={{ 
                  transform: 'translateY(-8px) scale(1.02)', 
                  boxShadow: '3xl',
                  borderColor: 'green.400'
                }}
                transition="all 0.4s"
              >
                <Box
                  position="absolute"
                  top="0"
                  left="0"
                  right="0"
                  h="4px"
                  bgGradient="linear(to-r, green.400, emerald.400)"
                />
                
                <CardBody p={8} h="full">
                  <Flex justify="space-between" align="center" h="full">
                    <VStack align="start" spacing={4} flex={1}>
                      <HStack spacing={3}>
                        <Box
                          p={3}
                          borderRadius="xl"
                          bgGradient="linear(to-br, green.100, emerald.100)"
                        >
                          <Icon as={FaFire} color="green.500" boxSize={6} />
                        </Box>
                        <VStack align="start" spacing={0}>
                          <Heading size="sm" color={mutedColor} fontWeight="medium">
                            Aktif Kullanıcı
                          </Heading>
                          <Badge colorScheme="green" variant="subtle" borderRadius="full" px={3}>
                            Son 30 gün
                          </Badge>
                        </VStack>
                      </HStack>
                      
                      <Stat>
                        <StatNumber
                          fontSize="4xl"
                          fontWeight="black"
                          color="green.500"
                        >
                          {stats.activeUsers.toLocaleString()}
                        </StatNumber>
                        <StatHelpText fontSize="md" color={mutedColor} fontWeight="medium">
                          İşlem yapan
                        </StatHelpText>
                      </Stat>
                    </VStack>
                    
                    <Box
                      animation={`${float} 3s ease-in-out infinite 1s`}
                    >
                      <Icon as={FaBolt} color="green.300" boxSize={12} opacity="0.3" />
                    </Box>
                  </Flex>
                </CardBody>
              </Card>
            </GridItem>
          </Grid>

          {/* Today & Week Stats - Side by Side Premium Cards */}
          <Grid templateColumns={{ base: '1fr', md: 'repeat(2, 1fr)' }} gap={6}>
            {/* Today's Trades */}
            <Card
              bg={cardBg}
              borderRadius="3xl"
              boxShadow="2xl"
              border="1px solid"
              borderColor={borderColor}
              overflow="hidden"
              position="relative"
              _hover={{ 
                transform: 'translateY(-6px)', 
                boxShadow: '3xl',
                borderColor: 'orange.400'
              }}
              transition="all 0.4s"
            >
              <Box
                position="absolute"
                top="0"
                left="0"
                right="0"
                h="4px"
                bgGradient="linear(to-r, orange.400, yellow.400)"
              />
              
              <CardBody p={8}>
                <HStack justify="space-between" mb={6}>
                  <HStack spacing={4}>
                    <Box
                      p={4}
                      borderRadius="2xl"
                      bgGradient="linear(to-br, orange.100, yellow.100)"
                    >
                      <Icon as={FaCalendarDay} color="orange.500" boxSize={8} />
                    </Box>
                    <VStack align="start" spacing={1}>
                      <Heading size="lg" color={textColor} fontWeight="bold">
                        Bugünkü İşlemler
                      </Heading>
                      <Text color={mutedColor} fontSize="md">
                        24 saat içindeki aktivite
                      </Text>
                    </VStack>
                  </HStack>
                  
                  <Badge 
                    colorScheme="orange" 
                    variant="solid" 
                    borderRadius="full" 
                    px={4} 
                    py={2}
                    fontSize="md"
                  >
                    GÜNLÜK
                  </Badge>
                </HStack>
                
                <Stat textAlign="center">
                  <StatNumber 
                    fontSize="5xl" 
                    fontWeight="black"
                    bgGradient="linear(to-r, orange.400, yellow.600)" 
                    bgClip="text"
                    mb={2}
                  >
                    {stats.tradesToday.toLocaleString()}
                  </StatNumber>
                  
                  <Progress 
                    value={(stats.tradesToday / Math.max(stats.totalTrades, 1)) * 100} 
                    colorScheme="orange" 
                    size="lg" 
                    borderRadius="full"
                    bg="orange.50"
                  />
                  
                  <StatHelpText fontSize="lg" color={mutedColor} fontWeight="medium" mt={3}>
                    {((stats.tradesToday / Math.max(stats.totalTrades, 1)) * 100).toFixed(1)}% toplam işlemlerden
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>

            {/* This Week's Trades */}
            <Card
              bg={cardBg}
              borderRadius="3xl"
              boxShadow="2xl"
              border="1px solid"
              borderColor={borderColor}
              overflow="hidden"
              position="relative"
              _hover={{ 
                transform: 'translateY(-6px)', 
                boxShadow: '3xl',
                borderColor: 'teal.400'
              }}
              transition="all 0.4s"
            >
              <Box
                position="absolute"
                top="0"
                left="0"
                right="0"
                h="4px"
                bgGradient="linear(to-r, teal.400, blue.400)"
              />
              
              <CardBody p={8}>
                <HStack justify="space-between" mb={6}>
                  <HStack spacing={4}>
                    <Box
                      p={4}
                      borderRadius="2xl"
                      bgGradient="linear(to-br, teal.100, blue.100)"
                    >
                      <Icon as={FaCalendarWeek} color="teal.500" boxSize={8} />
                    </Box>
                    <VStack align="start" spacing={1}>
                      <Heading size="lg" color={textColor} fontWeight="bold">
                        Bu Haftaki İşlemler
                      </Heading>
                      <Text color={mutedColor} fontSize="md">
                        7 günlük aktivite özeti
                      </Text>
                    </VStack>
                  </HStack>
                  
                  <Badge 
                    colorScheme="teal" 
                    variant="solid" 
                    borderRadius="full" 
                    px={4} 
                    py={2}
                    fontSize="md"
                  >
                    HAFTALİK
                  </Badge>
                </HStack>
                
                <Stat textAlign="center">
                  <StatNumber 
                    fontSize="5xl" 
                    fontWeight="black"
                    bgGradient="linear(to-r, teal.400, blue.600)" 
                    bgClip="text"
                    mb={2}
                  >
                    {stats.tradesThisWeek.toLocaleString()}
                  </StatNumber>
                  
                  <Progress 
                    value={(stats.tradesThisWeek / Math.max(stats.totalTrades, 1)) * 100} 
                    colorScheme="teal" 
                    size="lg" 
                    borderRadius="full"
                    bg="teal.50"
                  />
                  
                  <StatHelpText fontSize="lg" color={mutedColor} fontWeight="medium" mt={3}>
                    {((stats.tradesThisWeek / Math.max(stats.totalTrades, 1)) * 100).toFixed(1)}% toplam işlemlerden
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </Grid>

          {/* System Health Premium Card */}
          <Card
            bg={cardBg}
            borderRadius="3xl"
            boxShadow="2xl"
            border="1px solid"
            borderColor={borderColor}
            overflow="hidden"
            position="relative"
            _hover={{ transform: 'translateY(-4px)', boxShadow: '3xl' }}
            transition="all 0.3s"
          >
            <Box
              position="absolute"
              top="0"
              left="0"
              right="0"
              h="6px"
              bgGradient="linear(to-r, green.400, blue.400, purple.400, pink.400)"
              animation={`${gradient} 4s ease infinite`}
              backgroundSize="200% 200%"
            />
            
            <CardHeader pb={4}>
              <HStack spacing={4} justify="center">
                <Icon as={FaShieldAlt} color="green.500" boxSize={8} />
                <Heading size="xl" color={textColor} fontWeight="bold">
                  Sistem Sağlık Durumu
                </Heading>
                <Badge 
                  colorScheme="green" 
                  variant="solid" 
                  borderRadius="full" 
                  px={4} 
                  py={2}
                  fontSize="lg"
                  animation={`${pulse} 2s infinite`}
                >
                  ✅ SAĞLIKLI
                </Badge>
              </HStack>
            </CardHeader>
            
            <CardBody pt={0}>
              <SimpleGrid columns={{ base: 2, md: 4 }} spacing={8}>
                <VStack spacing={4}>
                  <Box
                    p={4}
                    borderRadius="2xl"
                    bg="green.50"
                    border="2px solid"
                    borderColor="green.200"
                  >
                    <Icon as={MdSpeed} color="green.500" boxSize={8} />
                  </Box>
                  <VStack spacing={2}>
                    <Text fontWeight="bold" color={textColor} fontSize="lg">
                      Platform Hızı
                    </Text>
                    <Badge colorScheme="green" variant="solid" borderRadius="full" px={4} py={1}>
                      ⚡ HIZLI
                    </Badge>
                  </VStack>
                </VStack>

                <VStack spacing={4}>
                  <Box
                    p={4}
                    borderRadius="2xl"
                    bg="blue.50"
                    border="2px solid"
                    borderColor="blue.200"
                  >
                    <Icon as={MdSecurity} color="blue.500" boxSize={8} />
                  </Box>
                  <VStack spacing={2}>
                    <Text fontWeight="bold" color={textColor} fontSize="lg">
                      Güvenlik
                    </Text>
                    <Badge colorScheme="blue" variant="solid" borderRadius="full" px={4} py={1}>
                      🛡️ GÜVENLİ
                    </Badge>
                  </VStack>
                </VStack>

                <VStack spacing={4}>
                  <Box
                    p={4}
                    borderRadius="2xl"
                    bg="purple.50"
                    border="2px solid"
                    borderColor="purple.200"
                  >
                    <Icon as={FaDatabase} color="purple.500" boxSize={8} />
                  </Box>
                  <VStack spacing={2}>
                    <Text fontWeight="bold" color={textColor} fontSize="lg">
                      Veritabanı
                    </Text>
                    <Badge colorScheme="purple" variant="solid" borderRadius="full" px={4} py={1}>
                      📊 AKTİF
                    </Badge>
                  </VStack>
                </VStack>

                <VStack spacing={4}>
                  <Box
                    p={4}
                    borderRadius="2xl"
                    bg="orange.50"
                    border="2px solid"
                    borderColor="orange.200"
                  >
                    <Icon as={FaCloud} color="orange.500" boxSize={8} />
                  </Box>
                  <VStack spacing={2}>
                    <Text fontWeight="bold" color={textColor} fontSize="lg">
                      API Durumu
                    </Text>
                    <Badge colorScheme="orange" variant="solid" borderRadius="full" px={4} py={1}>
                      🚀 HAZIR
                    </Badge>
                  </VStack>
                </VStack>
              </SimpleGrid>
            </CardBody>
          </Card>
        </VStack>
      </Container>
    </Box>
  );
};

export default AdminDashboard;