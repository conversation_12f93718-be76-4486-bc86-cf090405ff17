# Algobir Technical Architecture Documentation

## 🏗️ System Overview

Algobir is a comprehensive algorithmic trading platform built with modern web technologies, designed to handle high-frequency trading signals, user management, and real-time data processing at scale.

### Core Business Model
- **Solo-Robot**: Individual users create personal trading algorithms
- **Bro-Robot**: Signal providers share trading signals with subscribers
- **Marketplace**: Users discover and subscribe to profitable robots
- **Real-time Trading**: Sub-second order transmission optimization

## 🛠️ Technology Stack

### Frontend Architecture
```typescript
// Core Technologies
React 18.3.1          // UI Framework with Concurrent Features
TypeScript 5.8.3      // Type Safety and Developer Experience
Vite 5.4.18           // Build Tool with HMR and Optimization
Chakra UI 2.10.7      // Component Library with Horizon UI Theme

// State Management
React Context API     // Global State Management
Custom Hooks Pattern  // Business Logic Encapsulation
React Hook Form 7.56.1 // Form State Management

// Routing & Navigation
React Router DOM v6   // Client-side Routing
React Helmet Async    // Document Head Management

// Data Visualization
Recharts 2.15.4      // Charts and Analytics
ApexCharts 4.7.0     // Advanced Charting
Chart.js 4.5.0       // Interactive Charts
D3.js 7.9.0          // Custom Data Visualizations

// Performance & Monitoring
Framer Motion 11.11.18 // Animations and Micro-interactions
React Error Boundary   // Error Handling
Custom Performance Monitoring // Web Vitals and Metrics
```

### Backend Architecture
```yaml
# Supabase Platform
Database: PostgreSQL 15
  - Row Level Security (RLS) enabled
  - Real-time subscriptions
  - Connection pooling (25 default, 150 max)
  - Automated backups and point-in-time recovery

Authentication: Supabase Auth
  - JWT-based authentication
  - Social login providers
  - Role-based access control
  - Session management

Real-time: Supabase Realtime
  - WebSocket connections
  - Database change subscriptions
  - Presence tracking
  - Broadcast messaging

Storage: Supabase Storage
  - 50MiB file size limit
  - Image optimization
  - CDN integration
  - Access control policies

Edge Functions: Deno Runtime
  - TypeScript support
  - V8 isolates for security
  - Global deployment
  - Auto-scaling
```

## 📁 Project Structure

```
tobot-v2-app/
├── algobir-app-frontend/          # React Application
│   ├── src/
│   │   ├── components/            # Reusable UI Components
│   │   │   ├── admin/            # Admin Panel Components
│   │   │   ├── marketplace/      # Trading Robot Marketplace
│   │   │   ├── navbar/           # Navigation Components
│   │   │   ├── sidebar/          # Sidebar Navigation
│   │   │   ├── statistics/       # Analytics Dashboards
│   │   │   ├── trades/           # Trading Interface
│   │   │   └── monitoring/       # System Monitoring
│   │   ├── context/              # React Context Providers
│   │   ├── hooks/                # Custom Business Logic Hooks
│   │   ├── pages/                # Page Components
│   │   ├── services/             # API Service Functions
│   │   ├── utils/                # Utility Functions
│   │   └── theme/                # Chakra UI Theme
│   ├── tests/                    # Test Suites
│   │   ├── unit/                 # Unit Tests (Vitest)
│   │   ├── integration/          # Integration Tests
│   │   └── e2e/                  # End-to-End Tests (Playwright)
│   ├── scripts/                  # Build and Deployment Scripts
│   └── dist/                     # Build Output
├── supabase/                     # Backend Configuration
│   ├── functions/                # Edge Functions
│   │   ├── algobir-webhook-listener/    # Solo-robot signals
│   │   ├── seller-signal-endpoint/      # Bro-robot signals
│   │   ├── signal-relay-function/       # Signal distribution
│   │   ├── performance-dashboard/       # Metrics API
│   │   ├── monitoring-collector/        # Monitoring data
│   │   ├── monitoring-dashboard/        # Dashboard API
│   │   └── alert-notifications/         # Alert system
│   ├── migrations/               # Database Migrations
│   └── config.toml              # Supabase Configuration
└── horizon-ui-chakra/           # UI Theme Library
```

## 🔄 Data Flow Architecture

### Trading Signal Processing
```mermaid
graph TD
    A[TradingView Signal] --> B{Signal Type}
    B -->|Solo-Robot| C[algobir-webhook-listener]
    B -->|Bro-Robot| D[seller-signal-endpoint]
    
    C --> E[Database Storage]
    D --> F[signal-relay-function]
    F --> G[Subscriber Notification]
    
    E --> H[Order Execution]
    G --> I[Subscriber Order Execution]
    
    H --> J[Trade Recording]
    I --> J
    J --> K[Performance Analytics]
```

### Real-time Data Synchronization
```typescript
// Supabase Realtime Integration
const subscription = supabase
  .channel('trades')
  .on('postgres_changes', 
    { event: 'INSERT', schema: 'public', table: 'trades' },
    (payload) => {
      // Update UI with new trade data
      updateTradesList(payload.new);
    }
  )
  .subscribe();
```

## 🗄️ Database Schema

### Core Tables
```sql
-- User Management
profiles (
  id UUID PRIMARY KEY,
  username TEXT UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
);

user_settings (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  webhook_id TEXT,
  api_keys JSONB,
  permissions JSONB,
  created_at TIMESTAMPTZ
);

-- Trading System
robots (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  seller_id UUID REFERENCES profiles(id),
  robot_type TEXT CHECK (robot_type IN ('solo', 'bro')),
  performance_metrics JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ
);

trades (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  robot_id UUID REFERENCES robots(id),
  symbol TEXT NOT NULL,
  order_side TEXT CHECK (order_side IN ('buy', 'sell')),
  quantity DECIMAL NOT NULL,
  price DECIMAL NOT NULL,
  executed_at TIMESTAMPTZ,
  status TEXT DEFAULT 'pending'
);

subscriptions (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  robot_id UUID REFERENCES robots(id),
  started_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT true
);

-- Platform Features
notifications (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES profiles(id),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL,
  metadata JSONB,
  read_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Performance Monitoring
performance_metrics (
  id TEXT PRIMARY KEY,
  session_id TEXT NOT NULL,
  metric_name TEXT NOT NULL,
  metric_value NUMERIC NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL,
  url TEXT NOT NULL,
  tags JSONB DEFAULT '{}',
  metadata JSONB DEFAULT '{}'
);

error_reports (
  id TEXT PRIMARY KEY,
  session_id TEXT NOT NULL,
  error_message TEXT NOT NULL,
  stack_trace TEXT,
  severity TEXT CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  context JSONB DEFAULT '{}',
  timestamp TIMESTAMPTZ NOT NULL,
  url TEXT NOT NULL,
  user_agent TEXT
);
```

### Row Level Security (RLS) Policies
```sql
-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Trading data access control
CREATE POLICY "Users can view own trades" ON trades
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view subscribed robot trades" ON trades
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM subscriptions 
      WHERE user_id = auth.uid() 
      AND robot_id = trades.robot_id 
      AND is_active = true
    )
  );

-- Admin access for monitoring data
CREATE POLICY "Service role can insert monitoring data" ON performance_metrics
  FOR INSERT WITH CHECK (auth.role() = 'service_role');

CREATE POLICY "Users can view aggregated monitoring data" ON performance_metrics
  FOR SELECT USING (auth.role() = 'authenticated');
```

## 🔐 Security Architecture

### Authentication & Authorization
```typescript
// JWT-based Authentication
interface AuthContext {
  user: User | null;
  session: Session | null;
  signIn: (email: string, password: string) => Promise<AuthResponse>;
  signUp: (email: string, password: string) => Promise<AuthResponse>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

// Role-based Access Control
enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  SUPERUSER = 'superuser'
}

// Permission System
interface UserPermissions {
  canCreateRobots: boolean;
  canManageSubscriptions: boolean;
  canAccessAdminPanel: boolean;
  canViewAnalytics: boolean;
  canManageUsers: boolean;
}
```

### Data Protection
```typescript
// Input Sanitization
const sanitizeInput = (input: string): string => {
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');
};

// API Key Encryption
const encryptApiKey = async (apiKey: string): Promise<string> => {
  const encrypted = await supabase.rpc('encrypt_api_key', { 
    api_key: apiKey 
  });
  return encrypted.data;
};

// Rate Limiting
const rateLimiter = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
};
```

## ⚡ Performance Optimization

### Frontend Optimizations
```typescript
// Code Splitting
const LazyMarketplace = lazy(() => import('./pages/Marketplace'));
const LazyStatistics = lazy(() => import('./pages/Statistics'));
const LazyAdmin = lazy(() => import('./pages/Admin'));

// Memoization
const MemoizedTradesList = memo(TradesList);
const MemoizedChart = memo(TradingChart);

// Virtual Scrolling for Large Lists
const VirtualizedTradesList = ({ trades }: { trades: Trade[] }) => (
  <FixedSizeList
    height={600}
    itemCount={trades.length}
    itemSize={60}
    itemData={trades}
  >
    {TradeRow}
  </FixedSizeList>
);

// Performance Monitoring
const usePerformanceMonitoring = () => {
  useEffect(() => {
    // Web Vitals tracking
    getCLS(onCLS);
    getFID(onFID);
    getFCP(onFCP);
    getLCP(onLCP);
    getTTFB(onTTFB);
  }, []);
};
```

### Backend Optimizations
```typescript
// Connection Pooling
const supabaseConfig = {
  db: {
    poolSize: 25,
    maxPoolSize: 150,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000
  }
};

// Query Optimization
const getTradesWithPagination = async (
  userId: string, 
  page: number, 
  limit: number
) => {
  const { data, error } = await supabase
    .from('trades')
    .select(`
      *,
      robots!inner(name, robot_type),
      profiles!inner(username)
    `)
    .eq('user_id', userId)
    .order('executed_at', { ascending: false })
    .range(page * limit, (page + 1) * limit - 1);
    
  return { data, error };
};

// Caching Strategy
const cacheConfig = {
  performance_metrics: '5 minutes',
  robot_performance: '15 minutes',
  user_statistics: '1 hour',
  market_data: '30 seconds'
};
```

## 🔄 Real-time Features

### WebSocket Integration
```typescript
// Real-time Trade Updates
const useRealTimeTrades = (userId: string) => {
  const [trades, setTrades] = useState<Trade[]>([]);
  
  useEffect(() => {
    const subscription = supabase
      .channel(`trades:user_id=eq.${userId}`)
      .on('postgres_changes', 
        { event: 'INSERT', schema: 'public', table: 'trades' },
        (payload) => {
          setTrades(prev => [payload.new as Trade, ...prev]);
        }
      )
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'trades' },
        (payload) => {
          setTrades(prev => prev.map(trade => 
            trade.id === payload.new.id ? payload.new as Trade : trade
          ));
        }
      )
      .subscribe();
      
    return () => {
      subscription.unsubscribe();
    };
  }, [userId]);
  
  return trades;
};

// Real-time Notifications
const useRealTimeNotifications = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  
  useEffect(() => {
    if (!user) return;
    
    const subscription = supabase
      .channel(`notifications:user_id=eq.${user.id}`)
      .on('postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'notifications' },
        (payload) => {
          setNotifications(prev => [payload.new as Notification, ...prev]);
          
          // Show toast notification
          toast({
            title: payload.new.title,
            description: payload.new.message,
            status: payload.new.type === 'error' ? 'error' : 'info',
            duration: 5000,
            isClosable: true
          });
        }
      )
      .subscribe();
      
    return () => {
      subscription.unsubscribe();
    };
  }, [user]);
  
  return notifications;
};
```

## 📊 Monitoring & Analytics

### Performance Monitoring
```typescript
// Web Vitals Tracking
interface WebVitalsMetrics {
  LCP: number; // Largest Contentful Paint
  FID: number; // First Input Delay
  CLS: number; // Cumulative Layout Shift
  FCP: number; // First Contentful Paint
  TTFB: number; // Time to First Byte
}

// Error Tracking
interface ErrorReport {
  id: string;
  message: string;
  stack: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context: Record<string, any>;
  timestamp: string;
  url: string;
  userAgent: string;
}

// Business Metrics
interface BusinessMetrics {
  activeUsers: number;
  robotCreations: number;
  subscriptions: number;
  tradeVolume: number;
  revenue: number;
  conversionRate: number;
}
```

### Alert System
```typescript
// Alert Rules Configuration
interface AlertRule {
  id: string;
  name: string;
  metric: string;
  operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  timeWindow: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  channels: ('email' | 'slack' | 'webhook' | 'sms')[];
  enabled: boolean;
}

// Multi-channel Notifications
const sendAlert = async (alert: Alert) => {
  const channels = alert.rule.channels;
  
  if (channels.includes('email')) {
    await sendEmailNotification(alert);
  }
  
  if (channels.includes('slack')) {
    await sendSlackNotification(alert);
  }
  
  if (channels.includes('webhook')) {
    await sendWebhookNotification(alert);
  }
  
  if (channels.includes('sms')) {
    await sendSMSNotification(alert);
  }
};
```

## 🚀 Deployment Architecture

### Environment Configuration
```typescript
// Environment-specific Settings
interface EnvironmentConfig {
  staging: {
    supabaseUrl: string;
    supabaseAnonKey: string;
    enableDebugMode: true;
    performanceSampleRate: 0.1;
    errorSampleRate: 1.0;
  };
  production: {
    supabaseUrl: string;
    supabaseAnonKey: string;
    enableDebugMode: false;
    performanceSampleRate: 0.05;
    errorSampleRate: 1.0;
  };
}

// Build Optimization
const buildConfig = {
  target: 'es2020',
  minify: 'terser',
  sourcemap: false, // Disabled in production
  chunkSizeWarningLimit: 1000,
  rollupOptions: {
    output: {
      manualChunks: {
        'react-vendor': ['react', 'react-dom'],
        'ui-vendor': ['@chakra-ui/react', '@emotion/react'],
        'charts-vendor': ['recharts', 'apexcharts'],
        'api-vendor': ['@supabase/supabase-js']
      }
    }
  }
};
```

### CDN & Caching Strategy
```typescript
// Vercel Configuration
const vercelConfig = {
  headers: [
    {
      source: '/assets/(.*)',
      headers: [
        { key: 'Cache-Control', value: 'public, max-age=31536000, immutable' },
        { key: 'X-Content-Type-Options', value: 'nosniff' }
      ]
    }
  ],
  rewrites: [
    { source: '/((?!api/).*)', destination: '/index.html' }
  ]
};

// Service Worker Caching
const cacheStrategy = {
  fonts: 'CacheFirst', // 1 year cache
  images: 'CacheFirst', // 30 days cache
  api: 'NetworkFirst', // Fresh data preferred
  static: 'StaleWhileRevalidate' // Background updates
};
```

## 📋 API Endpoints

### Edge Functions
```typescript
// Trading Signal Processing
POST /functions/v1/algobir-webhook-listener
POST /functions/v1/seller-signal-endpoint
POST /functions/v1/signal-relay-function

// Performance & Monitoring
GET  /functions/v1/performance-dashboard
POST /functions/v1/monitoring-collector
GET  /functions/v1/monitoring-dashboard
POST /functions/v1/alert-notifications

// Utility Functions
POST /functions/v1/secure-save-api-key
```

### Database API (Supabase)
```typescript
// User Management
GET    /rest/v1/profiles
POST   /rest/v1/profiles
PATCH  /rest/v1/profiles?id=eq.{id}

// Trading System
GET    /rest/v1/robots
POST   /rest/v1/robots
GET    /rest/v1/trades
POST   /rest/v1/trades
GET    /rest/v1/subscriptions
POST   /rest/v1/subscriptions

// Monitoring
GET    /rest/v1/performance_metrics
POST   /rest/v1/performance_metrics
GET    /rest/v1/error_reports
POST   /rest/v1/error_reports
```

This technical architecture provides a comprehensive foundation for understanding the Algobir platform's design, implementation, and operational characteristics. The system is built to handle high-frequency trading scenarios while maintaining excellent user experience and operational reliability.
