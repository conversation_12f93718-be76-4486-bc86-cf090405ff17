import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
);

// Signal name parsing function for robots
function parseSignalNameForRobot(signalName, robotName) {
  console.log(`[parseSignalNameForRobot] Input: ${signalName}, Robot: ${robotName}`);
  
  // Default values
  let baseSystemName = robotName || 'Unknown';
  let signalType = 'Unknown';
  let orderSide = 'BUY'; // Default to BUY
  let symbol = 'UNKNOWN';
  
  if (!signalName || typeof signalName !== 'string') {
    console.log(`[parseSignalNameForRobot] Invalid signal name, using defaults`);
    return { baseSystemName, signalType, orderSide, symbol };
  }
  
  // Convert to uppercase for consistent parsing
  const upperSignalName = signalName.toUpperCase();
  
  // Use robot name as base system name if available
  if (robotName) {
    baseSystemName = robotName;
  } else {
    // Extract base system name (before first space or dash)
    const systemMatch = upperSignalName.match(/^([A-Z0-9]+)/);
    if (systemMatch) {
      baseSystemName = systemMatch[1];
    }
  }
  
  // Determine order side (BUY/SELL)
  if (upperSignalName.includes('SELL') || upperSignalName.includes('SATIM') || upperSignalName.includes('SHORT')) {
    orderSide = 'SELL';
  } else if (upperSignalName.includes('BUY') || upperSignalName.includes('ALIM') || upperSignalName.includes('LONG')) {
    orderSide = 'BUY';
  }
  
  // Extract signal type (the part after system name or the whole signal)
  if (robotName) {
    // If robot name is provided, use the signal name as signal type
    signalType = signalName.trim();
  } else {
    // Extract signal type (the part after system name)
    const typeMatch = upperSignalName.match(/^[A-Z0-9]+[\s\-_]?(.+)$/);
    if (typeMatch) {
      signalType = typeMatch[1].trim();
    }
  }
  
  // Try to extract symbol from signal name
  const symbolPatterns = [
    /([A-Z]{3,6}USD[T]?)/,  // BTCUSDT, ETHUSDT, etc.
    /([A-Z]{2,4}[\/\_][A-Z]{2,4})/, // BTC/USD, ETH_USD, etc.
    /([A-Z]{3,6})\s/,       // Symbol followed by space
  ];
  
  for (const pattern of symbolPatterns) {
    const match = upperSignalName.match(pattern);
    if (match) {
      symbol = match[1].replace(/[\/\_]/, '');
      break;
    }
  }
  
  const result = {
    baseSystemName,
    signalType,
    orderSide,
    symbol
  };
  
  console.log(`[parseSignalNameForRobot] Parsed result:`, result);
  return result;
}

Deno.serve(async (req) => {
  console.log('[BRO-ROBOT-DEBUG] Signal relay function started');
  
  try {
    const requestBody = await req.text();
    console.log('[BRO-ROBOT-DEBUG] Request body:', requestBody);
    
    let requestData;
    try {
      requestData = JSON.parse(requestBody);
    } catch (parseError) {
      console.error('[BRO-ROBOT-DEBUG] JSON parse error:', parseError);
      return new Response('Invalid JSON', { status: 400 });
    }
    
    const { robot_id, signal_data } = requestData;
    console.log('[BRO-ROBOT-DEBUG] Processing signal for robot:', robot_id);
    console.log('[BRO-ROBOT-DEBUG] Signal data:', JSON.stringify(signal_data, null, 2));
    
    if (!robot_id || !signal_data) {
      console.error('[BRO-ROBOT-DEBUG] Missing robot_id or signal_data');
      return new Response('Missing robot_id or signal_data', { status: 400 });
    }
    
    // Get robot details
    const { data: robotData, error: robotError } = await supabase
      .from('robots')
      .select(`
        id,
        name,
        seller_id,
        investment_amount
      `)
      .eq('id', robot_id)
      .single();
    
    if (robotError || !robotData) {
      console.error('[BRO-ROBOT-DEBUG] Robot not found:', robotError);
      return new Response('Robot not found', { status: 404 });
    }
    
    console.log('[BRO-ROBOT-DEBUG] Robot found:', robotData.name);
    
    // Debug: First let's see what's in the subscriptions table for this robot
    console.log('[BRO-ROBOT-DEBUG] ==== DEBUGGING SUBSCRIPTIONS QUERY ====');
    console.log('[BRO-ROBOT-DEBUG] Robot ID being searched:', robot_id);
    
    // Test 1: Get all subscriptions for this robot (no filters)
    const { data: allSubs, error: allSubsError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('robot_id', robot_id);
    
    console.log('[BRO-ROBOT-DEBUG] All subscriptions for robot (no filters):');
    console.log('[BRO-ROBOT-DEBUG] Count:', allSubs?.length || 0);
    console.log('[BRO-ROBOT-DEBUG] Data:', JSON.stringify(allSubs, null, 2));
    console.log('[BRO-ROBOT-DEBUG] Error:', allSubsError);
    
    // Test 2: Check is_active = true filter
    const { data: activeSubs, error: activeSubsError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('robot_id', robot_id)
      .eq('is_active', true);
    
    console.log('[BRO-ROBOT-DEBUG] Active subscriptions (is_active = true):');
    console.log('[BRO-ROBOT-DEBUG] Count:', activeSubs?.length || 0);
    console.log('[BRO-ROBOT-DEBUG] Data:', JSON.stringify(activeSubs, null, 2));
    console.log('[BRO-ROBOT-DEBUG] Error:', activeSubsError);
    
    // Test 3: Check is_deleted = false filter
    const { data: notDeletedSubs, error: notDeletedSubsError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('robot_id', robot_id)
      .eq('is_deleted', false);
    
    console.log('[BRO-ROBOT-DEBUG] Not deleted subscriptions (is_deleted = false):');
    console.log('[BRO-ROBOT-DEBUG] Count:', notDeletedSubs?.length || 0);
    console.log('[BRO-ROBOT-DEBUG] Data:', JSON.stringify(notDeletedSubs, null, 2));
    console.log('[BRO-ROBOT-DEBUG] Error:', notDeletedSubsError);
    
    // Test 4: The actual query used in the original code
    const { data: subscribers, error: subError } = await supabase
      .from('subscriptions')
      .select('user_id')
      .eq('robot_id', robot_id)
      .eq('is_active', true)
      .eq('is_deleted', false);
    
    console.log('[BRO-ROBOT-DEBUG] Final query result (original logic):');
    console.log('[BRO-ROBOT-DEBUG] Count:', subscribers?.length || 0);
    console.log('[BRO-ROBOT-DEBUG] Data:', JSON.stringify(subscribers, null, 2));
    console.log('[BRO-ROBOT-DEBUG] Error:', subError);
    
    console.log('[BRO-ROBOT-DEBUG] ==== END DEBUGGING ====');
    
    if (subError) {
      console.error('[BRO-ROBOT-DEBUG] Subscribers lookup error:', subError);
      return new Response('Failed to get subscribers', { status: 500 });
    }
    
    if (!subscribers || subscribers.length === 0) {
      console.log('[BRO-ROBOT-DEBUG] No active subscribers found - returning early');
      return new Response(JSON.stringify({
        success: true,
        message: 'No active subscribers found',
        robot_id: robot_id,
        subscribers_count: 0,
        debug_info: {
          total_subscriptions: allSubs?.length || 0,
          active_subscriptions: activeSubs?.length || 0,
          not_deleted_subscriptions: notDeletedSubs?.length || 0,
          final_result_count: subscribers?.length || 0
        }
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Rest of the function would continue here...
    console.log(`[BRO-ROBOT-DEBUG] Found ${subscribers.length} active subscribers - continuing...`);
    
    return new Response(JSON.stringify({
      success: true,
      message: 'Debug completed',
      robot_id: robot_id,
      subscribers_count: subscribers?.length || 0
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    console.error('[BRO-ROBOT-DEBUG] Unexpected error:', error);
    return new Response('Internal server error', { status: 500 });
  }
}); 