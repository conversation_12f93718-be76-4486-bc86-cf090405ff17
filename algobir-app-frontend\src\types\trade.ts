export type Trade = {
  id: number;
  user_id: string;
  symbol: string;
  signal_type: string;
  position_id: string;
  price: number;
  calculated_quantity: number;
  received_at: string;
  pnl?: number | null;
  pnl_percentage?: number | null;
  pnl_calculated_at?: string | null;
  exit_price?: number | null;
  exit_date?: string | null;
  is_closed?: boolean;
  is_deleted?: boolean; // Soft delete için kullanılır
  created_at: string; // Artık tabloda varsayılan olarak bulunuyor
  updated_at?: string;
  status?: 'buy' | 'tp' | 'sl' | 'unknown' | 'cancelled_by_user' | 'filled';
  
  // Genişletilmiş özellikler
  system_name?: string | null;
  trade_category?: string | null;
  position_status?: 'Açık' | 'Kapalı' | 'İptal Edildi' | 'Hata' | 'Gönderiliyor' | 'Beklemede' | string | null;
  signal_name?: string;
  category?: string;
  forwarded?: boolean;
  order_side?: 'BUY' | 'SELL' | string | null;
  name?: string;
  closing_trade_id?: string | null;
  robot_id?: string | null;  // Bro-robot için robot ID
  webhook_id?: string | null; // Solo-robot için webhook ID
  forwarding_status?: string | null; // İletim durumu
  forwarded_at?: string | null; // İletim zamanı
  robot_seller_id?: string | null; // Bro-robot satıcısının ID'si
  
  // Satıcı İptal Özellikleri
  is_cancelled_by_seller?: boolean; // Satıcı tarafından iptal edildi mi
  cancelled_by_seller_at?: string | null; // Satıcı tarafından ne zaman iptal edildi
  signal_batch_id?: string | null; // Sinyal grup ID'si - aynı satıcı sinyalinden gelen tüm işlemleri tanımlar
  
  // Robot detayları (join ile alındığında)
  robot?: {
    id: string;
    name: string;
    seller_id: string;
  };
};

export type TradeError = {
  message: string;
};

// Sinyal kaynağı tipi için enum
export enum SignalSourceType {
  SOLO_ROBOT = "solo_robot",
  BRO_ROBOT = "bro_robot"
} 