import React, { useState, useMemo } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardBody,
  Heading,
  Text,
  HStack,
  VStack,
  Badge,
  Icon,
  useColorModeValue,

  Button,
  ButtonGroup,
  Select,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel
} from '@chakra-ui/react';
import {
  ComposedChart,
  Line,
  Bar,

  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  ReferenceLine,
  Brush,
  Legend
} from 'recharts';
import {
  FiTrendingUp,
  FiActivity,
  FiBarChart,
  FiZoomIn,
  FiClock
} from 'react-icons/fi';

interface InteractiveTimeAnalysisChartProps {
  timeData: Array<{
    hour: number;
    pnl: number;
    trades: number;
  }>;
  monthlyData: Array<{
    month: string;
    pnl: number;
    trades: number;
    roi: number;
  }>;
  height?: string;
  title?: string;
}

const InteractiveTimeAnalysisChart: React.FC<InteractiveTimeAnalysisChartProps> = ({
  timeData,
  monthlyData,
  height = '500px',
  title = 'İnteraktif <PERSON>'
}) => {
  const [activeView, setActiveView] = useState<'hourly' | 'monthly'>('hourly');
  const [chartType, setChartType] = useState<'composed' | 'line' | 'bar'>('composed');

  const [drillDownData, setDrillDownData] = useState<any>(null);

  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'white');
  const gridColor = useColorModeValue('#f0f0f0', '#2d3748');
  const bgColor = useColorModeValue('gray.50', 'gray.800');
  
  // Chart colors
  const primaryColor = useColorModeValue('#3182CE', '#63B3ED');

  const infoColor = useColorModeValue('#00B5D8', '#0BC5EA');

  // Enhanced formatting functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(value / 100);
  };

  // Process hourly data with enhanced metrics
  const processedHourlyData = useMemo(() => {
    return timeData.map(item => ({
      ...item,
      hourLabel: `${item.hour.toString().padStart(2, '0')}:00`,
      avgPnLPerTrade: item.trades > 0 ? item.pnl / item.trades : 0,
      efficiency: item.trades > 0 ? (item.pnl / item.trades) * 100 : 0,
      isProfit: item.pnl > 0,
      period: item.hour < 12 ? 'Sabah' : item.hour < 18 ? 'Öğleden Sonra' : 'Akşam'
    }));
  }, [timeData]);

  // Process monthly data with enhanced metrics
  const processedMonthlyData = useMemo(() => {
    let cumulativePnL = 0;
    return monthlyData.map((item, index) => {
      cumulativePnL += item.pnl;
      return {
        ...item,
        cumulativePnL,
        avgPnLPerTrade: item.trades > 0 ? item.pnl / item.trades : 0,
        efficiency: item.trades > 0 ? (item.pnl / item.trades) * 100 : 0,
        isProfit: item.pnl > 0,
        monthIndex: index + 1,
        growth: index > 0 ? ((item.pnl - monthlyData[index - 1].pnl) / Math.abs(monthlyData[index - 1].pnl)) * 100 : 0
      };
    });
  }, [monthlyData]);

  // Get current data based on active view
  const getCurrentData = () => {
    return activeView === 'hourly' ? processedHourlyData : processedMonthlyData;
  };

  // Calculate statistics for current view
  const stats = useMemo(() => {
    const data = getCurrentData();
    if (!data.length) return null;

    const totalPnL = data.reduce((sum, item) => sum + item.pnl, 0);
    const totalTrades = data.reduce((sum, item) => sum + item.trades, 0);
    const profitablePeriods = data.filter(item => item.pnl > 0).length;
    const avgPnL = totalPnL / data.length;
    const avgTrades = totalTrades / data.length;
    
    const bestPeriod = data.reduce((best, item) => item.pnl > best.pnl ? item : best, data[0]);
    const worstPeriod = data.reduce((worst, item) => item.pnl < worst.pnl ? item : worst, data[0]);
    
    // Calculate volatility
    const returns = data.map(d => d.pnl);
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
    const volatility = Math.sqrt(variance);

    return {
      totalPnL,
      totalTrades,
      profitablePeriods,
      successRate: (profitablePeriods / data.length) * 100,
      avgPnL,
      avgTrades,
      bestPeriod,
      worstPeriod,
      volatility,
      sharpeRatio: volatility > 0 ? avgReturn / volatility : 0
    };
  }, [activeView, processedHourlyData, processedMonthlyData]);

  // Handle drill-down functionality
  const handleDataPointClick = (data: any) => {
    setDrillDownData(data);
  };

  // Custom tooltip with drill-down info
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          bg={cardBg}
          p={4}
          borderRadius="lg"
          shadow="lg"
          border="1px"
          borderColor={borderColor}
          cursor="pointer"
          onClick={() => handleDataPointClick(data)}
        >
          <VStack spacing={2} align="start">
            <HStack justify="space-between" w="full">
              <Text fontWeight="bold" color={textColor}>
                {activeView === 'hourly' ? data.hourLabel : data.month}
              </Text>
              <Icon as={FiZoomIn} color="blue.500" boxSize={4} />
            </HStack>
            <HStack spacing={4}>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">P&L</Text>
                <Text 
                  fontWeight="bold" 
                  color={data.pnl > 0 ? 'green.500' : 'red.500'}
                >
                  {formatCurrency(data.pnl)}
                </Text>
              </VStack>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">İşlemler</Text>
                <Text fontWeight="bold" color={primaryColor}>
                  {data.trades}
                </Text>
              </VStack>
            </HStack>
            <HStack spacing={4}>
              <VStack spacing={1} align="start">
                <Text fontSize="sm" color="gray.500">İşlem Başı Ort.</Text>
                <Text 
                  fontWeight="bold" 
                  color={data.avgPnLPerTrade > 0 ? 'green.500' : 'red.500'}
                >
                  {formatCurrency(data.avgPnLPerTrade)}
                </Text>
              </VStack>
              {activeView === 'monthly' && (
                <VStack spacing={1} align="start">
                  <Text fontSize="sm" color="gray.500">ROI</Text>
                  <Text 
                    fontWeight="bold" 
                    color={data.roi > 0 ? 'green.500' : 'red.500'}
                  >
                    {formatPercent(data.roi)}
                  </Text>
                </VStack>
              )}
            </HStack>
            <Text fontSize="xs" color="blue.500">
              Detaylar için tıklayın
            </Text>
          </VStack>
        </Box>
      );
    }
    return null;
  };

  return (
    <Card bg={cardBg} borderRadius="xl" shadow="sm">
      <CardHeader pb={2}>
        <HStack justify="space-between" flexWrap="wrap">
          <VStack align="start" spacing={1}>
            <HStack spacing={3}>
              <Icon as={FiClock} color="blue.500" boxSize={6} />
              <Heading size="md" color={textColor}>
                {title}
              </Heading>
            </HStack>
            {stats && (
              <HStack spacing={4} flexWrap="wrap">
                <Badge 
                  colorScheme={stats.totalPnL > 0 ? 'green' : 'red'} 
                  variant="subtle"
                  px={3}
                  py={1}
                >
                  <Icon as={FiTrendingUp} mr={1} />
                  {formatCurrency(stats.totalPnL)}
                </Badge>
                <Badge colorScheme="blue" variant="subtle" px={3} py={1}>
                  <Icon as={FiActivity} mr={1} />
                  {stats.successRate.toFixed(1)}% Başarı
                </Badge>
                <Badge colorScheme="purple" variant="subtle" px={3} py={1}>
                  Sharpe: {stats.sharpeRatio.toFixed(2)}
                </Badge>
              </HStack>
            )}
          </VStack>

          <VStack align="end" spacing={2}>
            <HStack spacing={2}>
              <Select 
                size="sm" 
                value={activeView} 
                onChange={(e) => setActiveView(e.target.value as any)}
                w="auto"
              >
                <option value="hourly">Saatlik</option>
                <option value="monthly">Aylık</option>
              </Select>
              <ButtonGroup size="sm" isAttached variant="outline">
                <Button
                  isActive={chartType === 'composed'}
                  onClick={() => setChartType('composed')}
                >
                  <Icon as={FiBarChart} />
                </Button>
                <Button 
                  isActive={chartType === 'line'}
                  onClick={() => setChartType('line')}
                >
                  <Icon as={FiTrendingUp} />
                </Button>
                <Button 
                  isActive={chartType === 'bar'}
                  onClick={() => setChartType('bar')}
                >
                  <Icon as={FiActivity} />
                </Button>
              </ButtonGroup>
            </HStack>
          </VStack>
        </HStack>
      </CardHeader>

      <CardBody pt={0}>
        <Tabs variant="enclosed" colorScheme="blue">
          <TabList>
            <Tab>Ana Görünüm</Tab>
            <Tab>Performans Analizi</Tab>
            {drillDownData && <Tab>Detay Analizi</Tab>}
          </TabList>

          <TabPanels>
            {/* Main Chart View */}
            <TabPanel px={0}>
              <Box h={height}>
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart 
                    data={getCurrentData()} 
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
                    <XAxis 
                      dataKey={activeView === 'hourly' ? 'hourLabel' : 'month'}
                      tick={{ fontSize: 12, fill: textColor }}
                      axisLine={{ stroke: borderColor }}
                    />
                    <YAxis 
                      yAxisId="pnl"
                      orientation="left"
                      tick={{ fontSize: 12, fill: textColor }}
                      axisLine={{ stroke: borderColor }}
                      tickFormatter={formatCurrency}
                    />
                    <YAxis 
                      yAxisId="trades"
                      orientation="right"
                      tick={{ fontSize: 12, fill: textColor }}
                      axisLine={{ stroke: borderColor }}
                    />
                    <RechartsTooltip content={<CustomTooltip />} />
                    <Legend />
                    
                    <ReferenceLine 
                      yAxisId="pnl"
                      y={0} 
                      stroke={borderColor} 
                      strokeDasharray="5 5"
                    />
                    
                    {chartType === 'composed' && (
                      <>
                        <Bar 
                          yAxisId="trades"
                          dataKey="trades" 
                          fill={infoColor}
                          fillOpacity={0.3}
                          name="İşlem Sayısı"
                        />
                        <Line 
                          yAxisId="pnl"
                          type="monotone" 
                          dataKey="pnl" 
                          stroke={primaryColor}
                          strokeWidth={3}
                          dot={{ fill: primaryColor, strokeWidth: 2, r: 5 }}
                          name="P&L"
                        />
                      </>
                    )}
                    
                    <Brush 
                      dataKey={activeView === 'hourly' ? 'hourLabel' : 'month'}
                      height={30} 
                      stroke={primaryColor}
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </Box>
            </TabPanel>

            {/* Performance Analysis */}
            <TabPanel px={0}>
              <VStack spacing={6} align="stretch">
                {stats && (
                  <HStack justify="space-around" p={4} bg={bgColor} borderRadius="lg">
                    <VStack spacing={1}>
                      <Text fontSize="sm" color="gray.500">En İyi {activeView === 'hourly' ? 'Saat' : 'Ay'}</Text>
                      <Text fontWeight="bold" color="green.500">
                        {activeView === 'hourly' ? (stats.bestPeriod as any).hourLabel : (stats.bestPeriod as any).month}
                      </Text>
                      <Text fontSize="sm" color="green.500">
                        {formatCurrency(stats.bestPeriod.pnl)}
                      </Text>
                    </VStack>
                    <VStack spacing={1}>
                      <Text fontSize="sm" color="gray.500">En Kötü {activeView === 'hourly' ? 'Saat' : 'Ay'}</Text>
                      <Text fontWeight="bold" color="red.500">
                        {activeView === 'hourly' ? (stats.worstPeriod as any).hourLabel : (stats.worstPeriod as any).month}
                      </Text>
                      <Text fontSize="sm" color="red.500">
                        {formatCurrency(stats.worstPeriod.pnl)}
                      </Text>
                    </VStack>
                    <VStack spacing={1}>
                      <Text fontSize="sm" color="gray.500">Volatilite</Text>
                      <Text fontWeight="bold" color="orange.500">
                        {formatCurrency(stats.volatility)}
                      </Text>
                    </VStack>
                    <VStack spacing={1}>
                      <Text fontSize="sm" color="gray.500">Ortalama</Text>
                      <Text 
                        fontWeight="bold" 
                        color={stats.avgPnL > 0 ? 'green.500' : 'red.500'}
                      >
                        {formatCurrency(stats.avgPnL)}
                      </Text>
                    </VStack>
                  </HStack>
                )}
              </VStack>
            </TabPanel>

            {/* Drill-down Detail */}
            {drillDownData && (
              <TabPanel px={0}>
                <VStack spacing={4} align="stretch">
                  <Text fontSize="lg" fontWeight="semibold" color={textColor}>
                    {activeView === 'hourly' ? drillDownData.hourLabel : drillDownData.month} - Detaylı Analiz
                  </Text>
                  <Box p={4} bg={bgColor} borderRadius="lg">
                    <Text fontSize="sm" color="gray.500" mb={2}>
                      Bu {activeView === 'hourly' ? 'saat' : 'ay'} için detaylı performans metrikleri
                    </Text>
                    {/* Add more detailed analysis here */}
                  </Box>
                </VStack>
              </TabPanel>
            )}
          </TabPanels>
        </Tabs>
      </CardBody>
    </Card>
  );
};

export default InteractiveTimeAnalysisChart;
