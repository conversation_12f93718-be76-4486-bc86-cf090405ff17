import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Card,
  CardBody,
  CardHeader,
  Heading,
  useColorModeValue,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,

  Badge,
  Icon,
  Progress,
  Divider,
  Tooltip,
  CircularProgress,
  CircularProgressLabel
} from '@chakra-ui/react';
import {
  FiZap,
  FiActivity,
  FiTarget,
  FiCheckCircle,
  FiAlertTriangle,
  FiDatabase,
  FiSend
} from 'react-icons/fi';
import { motion } from 'framer-motion';
import useOrderTransmissionMetrics from '../../hooks/useOrderTransmissionMetrics';

const MotionBox = motion(Box);

interface OrderTransmissionMetricsDashboardProps {
  timeRangeHours?: number;
}

const OrderTransmissionMetricsDashboard: React.FC<OrderTransmissionMetricsDashboardProps> = ({
  timeRangeHours = 24
}) => {
  // Color mode values
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  // Fetch metrics data
  const { stats, loading, error } = useOrderTransmissionMetrics({
    timeRangeHours,
    enabled: true,
    autoRefresh: true,
    refreshInterval: 30000
  });

  if (loading) {
    return (
      <Card bg={cardBg} borderRadius="xl" boxShadow="lg">
        <CardBody>
          <VStack spacing={4} py={8}>
            <CircularProgress isIndeterminate color="blue.400" />
            <Text color={mutedColor}>Performans metrikleri yükleniyor...</Text>
          </VStack>
        </CardBody>
      </Card>
    );
  }

  if (error || !stats) {
    return (
      <Card bg={cardBg} borderRadius="xl" boxShadow="lg">
        <CardBody>
          <VStack spacing={4} py={8}>
            <Icon as={FiAlertTriangle} color="red.500" boxSize={8} />
            <Text color="red.500">Performans metrikleri yüklenemedi</Text>
            {error && (
              <Text fontSize="sm" color={mutedColor}>
                {error}
              </Text>
            )}
          </VStack>
        </CardBody>
      </Card>
    );
  }

  // Helper function to get performance color
  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'green';
    if (value <= thresholds.warning) return 'yellow';
    return 'red';
  };

  // Performance thresholds (in milliseconds)
  const thresholds = {
    total: { good: 100, warning: 500 },
    parsing: { good: 10, warning: 50 },
    transformation: { good: 50, warning: 200 },
    webhook: { good: 200, warning: 1000 }
  };

  return (
    <VStack spacing={6} align="stretch">
      {/* Overview Stats */}
      <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
        <MotionBox
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
            <CardBody>
              <Stat>
                <StatLabel fontSize="sm" color={mutedColor}>
                  Toplam Sinyal
                </StatLabel>
                <StatNumber fontSize="2xl" color="blue.500">
                  {stats.total_signals.toLocaleString('tr-TR')}
                </StatNumber>
                <StatHelpText>
                  <HStack spacing={2}>
                    <Badge colorScheme="blue" variant="subtle">
                      Solo: {stats.solo_robot_count}
                    </Badge>
                    <Badge colorScheme="purple" variant="subtle">
                      Bro: {stats.bro_robot_count}
                    </Badge>
                  </HStack>
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>
        </MotionBox>

        <MotionBox
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
            <CardBody>
              <Stat>
                <StatLabel fontSize="sm" color={mutedColor}>
                  Başarı Oranı
                </StatLabel>
                <StatNumber fontSize="2xl" color="green.500">
                  %{stats.success_rate.toFixed(1)}
                </StatNumber>
                <StatHelpText>
                  <Icon as={FiCheckCircle} color="green.500" mr={1} />
                  Sistem güvenilirliği
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>
        </MotionBox>

        <MotionBox
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
            <CardBody>
              <Stat>
                <StatLabel fontSize="sm" color={mutedColor}>
                  Ortalama Hız
                </StatLabel>
                <StatNumber 
                  fontSize="2xl" 
                  color={getPerformanceColor(stats.avg_total_processing_time_ms, thresholds.total) + '.500'}
                >
                  {stats.avg_total_processing_time_ms.toFixed(1)}ms
                </StatNumber>
                <StatHelpText>
                  <Icon as={FiZap} mr={1} />
                  İşlem süresi
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>
        </MotionBox>

        <MotionBox
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card bg={cardBg} borderRadius="xl" boxShadow="md" border="1px solid" borderColor={borderColor}>
            <CardBody>
              <Stat>
                <StatLabel fontSize="sm" color={mutedColor}>
                  Sinyal/Dakika
                </StatLabel>
                <StatNumber fontSize="2xl" color="purple.500">
                  {stats.signals_per_minute.toFixed(1)}
                </StatNumber>
                <StatHelpText>
                  <Icon as={FiActivity} mr={1} />
                  İşlem yoğunluğu
                </StatHelpText>
              </Stat>
            </CardBody>
          </Card>
        </MotionBox>
      </SimpleGrid>

      {/* Detailed Performance Metrics */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        {/* Processing Time Breakdown */}
        <MotionBox
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Card bg={cardBg} borderRadius="xl" boxShadow="lg" border="1px solid" borderColor={borderColor}>
            <CardHeader>
              <Heading size="md" color={textColor}>
                İşlem Süresi Dağılımı
              </Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <Box>
                  <HStack justify="space-between" mb={2}>
                    <HStack>
                      <Icon as={FiDatabase} color="blue.500" />
                      <Text fontSize="sm" fontWeight="medium">JSON Ayrıştırma</Text>
                    </HStack>
                    <Text fontSize="sm" color={mutedColor}>
                      {stats.avg_json_parsing_time_ms.toFixed(1)}ms
                    </Text>
                  </HStack>
                  <Progress
                    value={(stats.avg_json_parsing_time_ms / stats.avg_total_processing_time_ms) * 100}
                    colorScheme={getPerformanceColor(stats.avg_json_parsing_time_ms, thresholds.parsing)}
                    size="sm"
                    borderRadius="full"
                  />
                </Box>

                <Box>
                  <HStack justify="space-between" mb={2}>
                    <HStack>
                      <Icon as={FiTarget} color="green.500" />
                      <Text fontSize="sm" fontWeight="medium">Veri Dönüştürme</Text>
                    </HStack>
                    <Text fontSize="sm" color={mutedColor}>
                      {stats.avg_transformation_time_ms.toFixed(1)}ms
                    </Text>
                  </HStack>
                  <Progress
                    value={(stats.avg_transformation_time_ms / stats.avg_total_processing_time_ms) * 100}
                    colorScheme={getPerformanceColor(stats.avg_transformation_time_ms, thresholds.transformation)}
                    size="sm"
                    borderRadius="full"
                  />
                </Box>

                <Box>
                  <HStack justify="space-between" mb={2}>
                    <HStack>
                      <Icon as={FiSend} color="orange.500" />
                      <Text fontSize="sm" fontWeight="medium">Webhook İletimi</Text>
                    </HStack>
                    <Text fontSize="sm" color={mutedColor}>
                      {stats.avg_webhook_delivery_time_ms.toFixed(1)}ms
                    </Text>
                  </HStack>
                  <Progress
                    value={(stats.avg_webhook_delivery_time_ms / stats.avg_total_processing_time_ms) * 100}
                    colorScheme={getPerformanceColor(stats.avg_webhook_delivery_time_ms, thresholds.webhook)}
                    size="sm"
                    borderRadius="full"
                  />
                </Box>
              </VStack>
            </CardBody>
          </Card>
        </MotionBox>

        {/* Performance Percentiles */}
        <MotionBox
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Card bg={cardBg} borderRadius="xl" boxShadow="lg" border="1px solid" borderColor={borderColor}>
            <CardHeader>
              <Heading size="md" color={textColor}>
                Performans Dağılımı
              </Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={6}>
                <SimpleGrid columns={2} spacing={4} w="full">
                  <VStack>
                    <CircularProgress
                      value={Math.min((stats.fastest_processing_time_ms / 100) * 100, 100)}
                      color="green.400"
                      size="80px"
                      thickness="8px"
                    >
                      <CircularProgressLabel fontSize="xs" fontWeight="bold">
                        En Hızlı
                      </CircularProgressLabel>
                    </CircularProgress>
                    <Text fontSize="sm" textAlign="center">
                      {stats.fastest_processing_time_ms.toFixed(1)}ms
                    </Text>
                  </VStack>

                  <VStack>
                    <CircularProgress
                      value={Math.min((stats.slowest_processing_time_ms / 1000) * 100, 100)}
                      color="red.400"
                      size="80px"
                      thickness="8px"
                    >
                      <CircularProgressLabel fontSize="xs" fontWeight="bold">
                        En Yavaş
                      </CircularProgressLabel>
                    </CircularProgress>
                    <Text fontSize="sm" textAlign="center">
                      {stats.slowest_processing_time_ms.toFixed(1)}ms
                    </Text>
                  </VStack>
                </SimpleGrid>

                <Divider />

                <VStack spacing={3} w="full">
                  <HStack justify="space-between" w="full">
                    <Text fontSize="sm" fontWeight="medium">95. Yüzdelik</Text>
                    <Badge 
                      colorScheme={getPerformanceColor(stats.p95_total_processing_time_ms, thresholds.total)}
                      variant="subtle"
                    >
                      {stats.p95_total_processing_time_ms.toFixed(1)}ms
                    </Badge>
                  </HStack>

                  <HStack justify="space-between" w="full">
                    <Text fontSize="sm" fontWeight="medium">99. Yüzdelik</Text>
                    <Badge 
                      colorScheme={getPerformanceColor(stats.p99_total_processing_time_ms, thresholds.total)}
                      variant="subtle"
                    >
                      {stats.p99_total_processing_time_ms.toFixed(1)}ms
                    </Badge>
                  </HStack>

                  <Tooltip label="Sinyallerin %95'i bu sürede işleniyor">
                    <Text fontSize="xs" color={mutedColor} textAlign="center">
                      Performans hedefleri için referans değerler
                    </Text>
                  </Tooltip>
                </VStack>
              </VStack>
            </CardBody>
          </Card>
        </MotionBox>
      </SimpleGrid>
    </VStack>
  );
};

export default OrderTransmissionMetricsDashboard;
