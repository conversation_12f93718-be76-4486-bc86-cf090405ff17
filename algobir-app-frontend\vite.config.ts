import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const isProduction = mode === 'production'
  const isDevelopment = mode === 'development'

  return {
    plugins: [
      react()
    ],

    // Development server configuration
    server: {
      port: 3000,
      open: true,
      host: true,
      cors: true,
      // HMR optimizations
      hmr: {
        overlay: true
      }
    },

    // Build optimizations
    build: {
      target: 'es2020',
      outDir: 'dist',
      assetsDir: 'assets',

      rollupOptions: {
        input: {
          main: resolve(__dirname, 'index.html')
        },
        output: {
          // Advanced chunk splitting strategy
          manualChunks: (id) => {
            // React core
            if (id.includes('react') || id.includes('react-dom')) {
              return 'vendor-react';
            }

            // UI Framework
            if (id.includes('@chakra-ui') || id.includes('@emotion')) {
              return 'vendor-ui';
            }

            // Charts and visualization
            if (id.includes('recharts') || id.includes('chart.js') || id.includes('d3')) {
              return 'vendor-charts';
            }

            // Data processing
            if (id.includes('@tanstack/react-table') || id.includes('xlsx') || id.includes('jspdf')) {
              return 'vendor-data';
            }

            // Supabase and API
            if (id.includes('@supabase') || id.includes('supabase')) {
              return 'vendor-supabase';
            }

            // Utilities
            if (id.includes('date-fns') || id.includes('lodash') || id.includes('react-helmet-async')) {
              return 'vendor-utils';
            }

            // Analytics
            if (id.includes('@vercel/analytics') || id.includes('@vercel/speed-insights')) {
              return 'vendor-analytics';
            }

            // Animation
            if (id.includes('framer-motion')) {
              return 'vendor-animation';
            }

            // Icons
            if (id.includes('react-icons') || id.includes('@chakra-ui/icons')) {
              return 'vendor-icons';
            }

            // Other node_modules
            if (id.includes('node_modules')) {
              return 'vendor-misc';
            }
          },

          // Optimize chunk names and hashing
          chunkFileNames: (chunkInfo) => {
            const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
            return `js/[name]-[hash].js`;
          },
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
              return `images/[name]-[hash][extname]`;
            }
            if (/woff2?|eot|ttf|otf/i.test(ext)) {
              return `fonts/[name]-[hash][extname]`;
            }
            return `assets/[name]-[hash][extname]`;
          }
        },

        // External dependencies (CDN)
        external: isProduction ? [] : [],

        // Tree shaking optimizations
        treeshake: {
          moduleSideEffects: false,
          propertyReadSideEffects: false,
          unknownGlobalSideEffects: false
        }
      },

      // Chunk size optimization
      chunkSizeWarningLimit: 1000,

      // Advanced minification
      minify: isProduction ? 'terser' : false,
      terserOptions: isProduction ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.info', 'console.debug'],
          passes: 2
        },
        mangle: {
          safari10: true
        },
        format: {
          comments: false
        }
      } : {},

      // Source maps
      sourcemap: isDevelopment,

      // Asset optimization
      assetsInlineLimit: 4096,

      // CSS optimization
      cssCodeSplit: true,
      cssMinify: isProduction,

      // Report bundle size
      reportCompressedSize: true,

      // Emit manifest
      manifest: isProduction
    },

    // Dependency optimization
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react/jsx-runtime',
        '@chakra-ui/react',
        '@chakra-ui/icons',
        '@emotion/react',
        '@emotion/styled',
        'framer-motion',
        '@supabase/supabase-js',
        'react-router-dom',
        'react-hook-form'
      ],
      exclude: [
        '@vercel/analytics',
        '@vercel/speed-insights'
      ],
      // Force optimization
      force: isDevelopment
    },

    // Global constants
    define: {
      'process.env.NODE_ENV': JSON.stringify(mode),
      '__DEV__': JSON.stringify(isDevelopment),
      '__PROD__': JSON.stringify(isProduction),
      'import.meta.env.VITE_WEBHOOK_BASE_URL': JSON.stringify(
        env.VITE_WEBHOOK_BASE_URL || 'https://hook.algobir.com'
      ),
    },

    // Path resolution
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'src/components'),
        '@pages': resolve(__dirname, 'src/pages'),
        '@hooks': resolve(__dirname, 'src/hooks'),
        '@utils': resolve(__dirname, 'src/utils'),
        '@services': resolve(__dirname, 'src/services'),
        '@types': resolve(__dirname, 'src/types'),
        '@theme': resolve(__dirname, 'src/theme')
      }
    },

    // CSS preprocessing
    css: {
      devSourcemap: isDevelopment,
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/theme/variables.scss";`
        }
      }
    },

    // Preview server (for production builds)
    preview: {
      port: 4173,
      host: true
    },

    // Worker optimization
    worker: {
      format: 'es'
    }
  }
})