import React, { Suspense } from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON><PERSON><PERSON> } from 'react-router-dom'
import { ChakraProvider, extendTheme, Flex, Spinner } from '@chakra-ui/react'
import App from './App'
import './index.css'
import { AuthProvider } from './context/AuthContext'
import { SidebarProvider } from './context/SidebarContext'
import horizonTheme from './theme/horizonTheme'
import { ToastHelperProvider } from './components/ToastHelper'
import { HelmetProvider } from 'react-helmet-async'
import { initializeGlobalErrorHandlers } from './utils/globalErrorHandlers'
import ErrorBoundary from './components/ErrorBoundary'
import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/react'

// Initialize global error handlers as early as possible
initializeGlobalErrorHandlers()

// Safe theme extension to prevent icon loading issues
const safeTheme = extendTheme(horizonTheme, {
  components: {
    Icon: {
      baseStyle: {
        display: 'inline-block',
        lineHeight: '1em',
        flexShrink: 0,
      },
    },
  },
})

// Loading component for Suspense fallbacks
const PageLoader = () => (
  <Flex justify="center" align="center" minH="60vh">
    <Spinner size="lg" color="brand.500" thickness="4px" />
  </Flex>
)

const root = document.getElementById('root') as HTMLElement
if (!root) throw new Error('Root element not found')

ReactDOM.createRoot(root).render(
  <React.StrictMode>
    <HelmetProvider>
      <ChakraProvider theme={safeTheme}>
        <BrowserRouter 
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true
          }}
        >
          <AuthProvider>
            <ToastHelperProvider>
              <SidebarProvider>
                <ErrorBoundary>
                  <Suspense fallback={<PageLoader />}>
                    <App />
                  </Suspense>
                  <Analytics />
                  <SpeedInsights />
                </ErrorBoundary>
              </SidebarProvider>
            </ToastHelperProvider>
          </AuthProvider>
        </BrowserRouter>
      </ChakraProvider>
    </HelmetProvider>
  </React.StrictMode>
)
