import { useState, useCallback, useRef, useEffect } from 'react';
import { useToast } from '@chakra-ui/react';

/**
 * Standard async operation states
 */
export interface AsyncOperationState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastFetch: number | null;
}

/**
 * Configuration for async operations
 */
export interface AsyncOperationConfig {
  showToastOnError?: boolean;
  errorToastTitle?: string;
  retryAttempts?: number;
  retryDelay?: number;
  cacheTimeout?: number;
  enableCache?: boolean;
}

/**
 * Return type for useAsyncOperation hook
 */
export interface AsyncOperationReturn<T> extends AsyncOperationState<T> {
  execute: () => Promise<void>;
  reset: () => void;
  retry: () => Promise<void>;
  isStale: boolean;
}

/**
 * Standardized async operation hook
 * Provides consistent loading, error, and data states across all hooks
 * 
 * @param operation - Async function that returns data
 * @param config - Configuration options
 * @returns Standardized async operation state and controls
 */
export function useAsyncOperation<T>(
  operation: () => Promise<T>,
  config: AsyncOperationConfig = {}
): AsyncOperationReturn<T> {
  const {
    showToastOnError = true,
    errorToastTitle = 'Hata',
    retryAttempts = 2,
    retryDelay = 1000,
    cacheTimeout = 300000, // 5 minutes default
    enableCache = true
  } = config;

  const toast = useToast();
  const retryCount = useRef(0);
  const abortController = useRef<AbortController | null>(null);

  const [state, setState] = useState<AsyncOperationState<T>>({
    data: null,
    loading: false,
    error: null,
    lastFetch: null
  });

  // Check if data is stale
  const isStale = enableCache && state.lastFetch 
    ? Date.now() - state.lastFetch > cacheTimeout
    : true;

  // Execute the async operation
  const execute = useCallback(async () => {
    // Cancel any ongoing operation
    if (abortController.current) {
      abortController.current.abort();
    }

    abortController.current = new AbortController();
    
    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    try {
      const result = await operation();
      
      // Check if operation was aborted
      if (abortController.current?.signal.aborted) {
        return;
      }

      setState({
        data: result,
        loading: false,
        error: null,
        lastFetch: Date.now()
      });

      retryCount.current = 0;
    } catch (error) {
      // Check if operation was aborted
      if (abortController.current?.signal.aborted) {
        return;
      }

      const errorMessage = error instanceof Error ? error.message : 'Bilinmeyen hata oluştu';
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }));

      // Show toast notification if enabled
      if (showToastOnError) {
        toast({
          title: errorToastTitle,
          description: errorMessage,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }

      console.error('Async operation failed:', error);
    }
  }, [operation, showToastOnError, errorToastTitle, toast]);

  // Retry with exponential backoff
  const retry = useCallback(async () => {
    if (retryCount.current < retryAttempts) {
      retryCount.current++;
      const delay = retryDelay * Math.pow(2, retryCount.current - 1);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      await execute();
    }
  }, [execute, retryAttempts, retryDelay]);

  // Reset state
  const reset = useCallback(() => {
    if (abortController.current) {
      abortController.current.abort();
    }
    
    setState({
      data: null,
      loading: false,
      error: null,
      lastFetch: null
    });
    
    retryCount.current = 0;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortController.current) {
        abortController.current.abort();
      }
    };
  }, []);

  return {
    ...state,
    execute,
    reset,
    retry,
    isStale
  };
}

/**
 * Hook for operations that need automatic execution
 */
export function useAutoAsyncOperation<T>(
  operation: () => Promise<T>,
  dependencies: any[] = [],
  config: AsyncOperationConfig = {}
): AsyncOperationReturn<T> {
  const asyncOp = useAsyncOperation(operation, config);

  useEffect(() => {
    // Only execute if data is stale or not cached
    if (asyncOp.isStale || !config.enableCache) {
      asyncOp.execute();
    }
  }, dependencies); // eslint-disable-line react-hooks/exhaustive-deps

  return asyncOp;
}
