import React from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Flex,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Step,
  StepIndicator,
  StepStatus,
  StepIcon,
  StepNumber,
  StepTitle,
  StepDescription,
  useSteps,
  useToast,
} from '@chakra-ui/react';
import { useForm, FormProvider } from 'react-hook-form';
import { supabase } from '../../supabaseClient';
import { useAuth } from '../../context/AuthContext';
import { Robot } from '../../types/robot';

// Step components
import { Step1BasicInfo } from './wizard/Step1BasicInfo';
import { Step2TradingSettings } from './wizard/Step2TradingSettings';
import { Step3MarketplaceSettings } from './wizard/Step3MarketplaceSettings';
import { Step4BuyJson } from './wizard/Step4BuyJson';
import { Step5SellJson } from './wizard/Step5SellJson';
import { Step6Summary } from './wizard/Step6Summary';

const steps = [
  { title: '<PERSON><PERSON> Bilgiler', description: '<PERSON>sim ve Açıklama' },
  { title: 'T<PERSON><PERSON>', description: 'Yatırım detayları' },
  { title: 'Pazar Yeri', description: 'Abonelik detayları' },
  { title: 'Alım JSON', description: 'TradingView alım sinyali' },
  { title: 'Satım JSON', description: 'TradingView satım sinyali' },
  { title: 'Özet ve Oluştur', description: 'İnceleme ve Oluştur' },
];

interface RobotSetupWizardProps {
  editingRobot?: Robot | null;
  onCancel: () => void;
  onSuccess: () => void; // To refresh the robot list
}

export interface RobotFormData {
  name: string;
  description: string;
  strategy_type: string;
  image_url: string;
  version: string;
  is_public: boolean;
  is_free: boolean;
  investment_amount_per_position: number;
  price: number;
  subscription_period: number;
}

export const RobotSetupWizard: React.FC<RobotSetupWizardProps> = ({ editingRobot, onCancel, onSuccess }) => {
  const { user } = useAuth();
  const toast = useToast();
  const methods = useForm<RobotFormData>({
    defaultValues: {
      name: '',
      description: '',
      strategy_type: '',
      image_url: '',
      version: '1.0',
      is_public: true,
      is_free: false,
      investment_amount_per_position: 100,
      price: 10,
      subscription_period: 30,
    },
  });
  const { activeStep, goToNext, goToPrevious } = useSteps({
    index: 0,
    count: steps.length,
  });

  const [isLoading, setIsLoading] = React.useState(false);

  // Pre-fill form data when editing a robot
  React.useEffect(() => {
    if (editingRobot) {
      methods.reset({
        name: editingRobot.name || '',
        description: editingRobot.description || '',
        strategy_type: editingRobot.strategy_type || '',
        image_url: editingRobot.image_url || '',
        version: editingRobot.version || '1.0',
        is_public: editingRobot.is_public ?? true,
        is_free: (editingRobot.price === 0) || false,
        investment_amount_per_position: editingRobot.investment_amount_per_position || 100,
        price: editingRobot.price || 10,
        subscription_period: editingRobot.subscription_period || 30,
      });
    }
  }, [editingRobot, methods]);

  const validateStep = async (stepIndex: number): Promise<boolean> => {
    let fieldsToValidate: (keyof RobotFormData)[] = [];
    
    switch (stepIndex) {
      case 0: // Basic Info
        fieldsToValidate = ['name', 'description'];
        break;
      case 1: // Trading Settings
        fieldsToValidate = ['strategy_type', 'investment_amount_per_position'];
        break;
      case 2: // Marketplace Settings
        fieldsToValidate = ['is_public'];
        // Only validate price and subscription_period if not free
        const isFreeMerch = methods.watch('is_free');
        if (!isFreeMerch) {
          fieldsToValidate.push('price', 'subscription_period');
        }
        break;
      case 3: // Buy JSON
      case 4: // Sell JSON
        // JSON steps might not need specific validation here
        return true;
      case 5: // Summary - Final validation before submit
        fieldsToValidate = ['name', 'description', 'strategy_type', 'investment_amount_per_position'];
        const isFree = methods.watch('is_free');
        if (!isFree) {
          fieldsToValidate.push('price', 'subscription_period');
        }
        break;
      default:
        return true;
    }

    const result = await methods.trigger(fieldsToValidate);
    return result;
  };

  const handleNext = async () => {
    const isValid = await validateStep(activeStep);
    if (isValid) {
      goToNext();
    } else {
      toast({ 
        title: "Lütfen tüm gerekli alanları doğru şekilde doldurun.", 
        status: 'warning', 
        isClosable: true 
      });
    }
  };

  const onSubmit = async (data: RobotFormData) => {
    if (!user) {
      toast({ title: 'Giriş yapmanız gerekiyor.', status: 'error' });
      return;
    }
    setIsLoading(true);
    
    try {
      const dataToSubmit = {
        name: data.name.trim(),
        description: data.description ? data.description.trim() : null,
        strategy_type: data.strategy_type || null,
        image_url: data.image_url ? data.image_url.trim() : null,
        version: data.version ? data.version.trim() : '1.0',
        is_public: Boolean(data.is_public),
        investment_amount_per_position: data.investment_amount_per_position ? Number(data.investment_amount_per_position) : 100.00,
        price: data.is_free ? 0.00 : (data.price ? Number(data.price) : 10.00),
        subscription_period: data.subscription_period ? Number(data.subscription_period) : 30,
        seller_id: user.id,
      };

      if (editingRobot) {
        // Update existing robot
        const { error } = await supabase
          .from('robots')
          .update(dataToSubmit)
          .eq('id', editingRobot.id);
        
        if (error) {
          throw error;
        }

        toast({ 
          title: 'Robot başarıyla güncellendi!', 
          status: 'success', 
          isClosable: true 
        });
      } else {
        // Create new robot
        const { error } = await supabase.from('robots').insert([dataToSubmit]);
        
        if (error) {
          throw error;
        }

        toast({ 
          title: 'Robot başarıyla oluşturuldu!', 
          status: 'success', 
          isClosable: true 
        });
      }

      onSuccess(); // Refresh list and exit wizard
    } catch (error: any) {
      console.error('Robot işlemi hatası:', error);
      const action = editingRobot ? 'güncelleme' : 'oluşturma';
      toast({ 
        title: `Robot ${action} hatası`, 
        description: error.message, 
        status: 'error', 
        isClosable: true 
      });
    } finally {
      setIsLoading(false);
    }
  };
      
  const renderStepContent = () => {
    switch (activeStep) {
      case 0: return <Step1BasicInfo />;
      case 1: return <Step2TradingSettings />;
      case 2: return <Step3MarketplaceSettings />;
      case 3: return <Step4BuyJson />;
      case 4: return <Step5SellJson />;
      case 5: return <Step6Summary />;
      default: return null;
    }
  };

  return (
    <FormProvider {...methods}>
      <Box as="form" onSubmit={methods.handleSubmit(onSubmit)}>
        <Stepper index={activeStep} mb={8} colorScheme="brand">
          {steps.map((step, index) => (
            <Step key={index}>
              <StepIndicator>
                <StepStatus 
                  complete={<StepIcon />} 
                  incomplete={<StepNumber />} 
                  active={<StepNumber />} 
                />
              </StepIndicator>
              <Box flexShrink="0">
                <StepTitle>{step.title}</StepTitle>
                <StepDescription>{step.description}</StepDescription>
              </Box>
            </Step>
          ))}
        </Stepper>

        <Box minH="400px" p={6} borderWidth={1} borderRadius="md" mb={8} bg="gray.50">
          {renderStepContent()}
        </Box>

        <Flex width="100%" justify="space-between" mt={8}>
          {/* The Cancel button should always be visible to allow exiting the wizard */}
          <Button variant="ghost" onClick={onCancel}>
            İptal
          </Button>

          <HStack>
            <Button onClick={goToPrevious} isDisabled={activeStep === 0}>
              Geri
            </Button>

            {/* --- CORRECTED LOGIC --- */}
            {activeStep < steps.length - 1 ? (
              // Show "Next" button on all steps EXCEPT the last one.
              // This button should NOT be type="submit".
              <Button colorScheme="brand" onClick={handleNext}>
                İleri
              </Button>
            ) : (
              // Show the final confirmation button ONLY on the last step.
              // This button MUST be type="submit" to trigger the form's onSubmit.
              <Button
                colorScheme="brand"
                type="submit"
                isLoading={isLoading}
                loadingText={editingRobot ? "Robot Güncelleniyor..." : "Robot Oluşturuluyor..."}
              >
                {editingRobot ? 'Onayla & Güncelle' : 'Onayla & Oluştur'}
              </Button>
            )}
          </HStack>
        </Flex>
      </Box>
    </FormProvider>
  );
}; 