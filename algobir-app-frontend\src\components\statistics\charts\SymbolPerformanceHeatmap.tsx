import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  ButtonGroup,
  Select,
  useColorModeValue,

  Badge,
  Flex,
  Spacer,
  Grid,
  GridItem,

  Switch,
  FormControl,
  FormLabel,
  Tooltip
} from '@chakra-ui/react';

import { motion } from 'framer-motion';
import { SymbolTimePerformance } from '../../../types/symbolAnalysis';

const MotionBox = motion(Box);

interface SymbolPerformanceHeatmapProps {
  data: SymbolTimePerformance[];
  height?: number;
  selectedSymbols?: string[];
  onSymbolClick?: (symbol: string) => void;
  maxSymbols?: number;
}

type HeatmapType = 'hourly' | 'daily' | 'monthly';
type MetricType = 'pnl' | 'winRate' | 'trades' | 'avgTradeSize';

interface HeatmapCell {
  symbol: string;
  period: string | number;
  value: number;
  intensity: number; // 0-1 for color intensity
  trades: number;
  winRate: number;
  label: string;
}

const SymbolPerformanceHeatmap: React.FC<SymbolPerformanceHeatmapProps> = ({
  data,
  height = 600,
  selectedSymbols = [],
  onSymbolClick,
  maxSymbols = 10
}) => {
  const [heatmapType, setHeatmapType] = useState<HeatmapType>('hourly');
  const [selectedMetric, setSelectedMetric] = useState<MetricType>('pnl');
  const [showValues, setShowValues] = useState(true);
  const [showTooltips, setShowTooltips] = useState(true);

  // Theme colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'white');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Metric configurations
  const metricConfigs = {
    pnl: {
      label: 'Kar/Zarar',
      formatValue: (value: number) => `₺${value.toFixed(0)}`,
      positiveColor: '#48BB78',
      negativeColor: '#F56565',
      neutralColor: '#E2E8F0'
    },
    winRate: {
      label: 'Kazanma Oranı',
      formatValue: (value: number) => `${value.toFixed(1)}%`,
      positiveColor: '#38A169',
      negativeColor: '#FED7D7',
      neutralColor: '#E2E8F0'
    },
    trades: {
      label: 'İşlem Sayısı',
      formatValue: (value: number) => value.toString(),
      positiveColor: '#3182CE',
      negativeColor: '#E2E8F0',
      neutralColor: '#E2E8F0'
    },
    avgTradeSize: {
      label: 'Ortalama İşlem Boyutu',
      formatValue: (value: number) => `₺${value.toFixed(0)}`,
      positiveColor: '#9F7AEA',
      negativeColor: '#E2E8F0',
      neutralColor: '#E2E8F0'
    }
  };

  // Process heatmap data
  const heatmapData = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Filter symbols
    const symbolsToShow = selectedSymbols.length > 0 
      ? data.filter(item => selectedSymbols.includes(item.symbol))
      : data.slice(0, maxSymbols);

    const cells: HeatmapCell[] = [];

    symbolsToShow.forEach(symbolData => {
      let performanceData: any[] = [];
      let periods: (string | number)[] = [];

      // Get appropriate data based on heatmap type
      switch (heatmapType) {
        case 'hourly':
          performanceData = symbolData.hourlyPerformance;
          periods = Array.from({ length: 24 }, (_, i) => i);
          break;
        case 'daily':
          performanceData = symbolData.dailyPerformance;
          periods = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];
          break;
        case 'monthly':
          performanceData = symbolData.monthlyPerformance;
          periods = performanceData.map(item => item.month);
          break;
      }

      periods.forEach((period, index) => {
        const periodData = heatmapType === 'monthly' 
          ? performanceData.find(item => item.month === period)
          : performanceData.find(item => 
              heatmapType === 'hourly' ? item.hour === period : item.dayOfWeek === index
            );

        if (periodData) {
          const value = periodData[selectedMetric] || 0;
          
          cells.push({
            symbol: symbolData.symbol,
            period,
            value,
            intensity: 0, // Will be calculated
            trades: periodData.trades || 0,
            winRate: periodData.winRate || 0,
            label: heatmapType === 'hourly' ? `${period}:00` : 
                   heatmapType === 'daily' ? period as string :
                   period as string
          });
        }
      });
    });

    // Calculate intensity values (normalize to 0-1)
    if (cells.length > 0) {
      const values = cells.map(cell => Math.abs(cell.value));
      const maxValue = Math.max(...values);
      const minValue = Math.min(...values);
      
      cells.forEach(cell => {
        if (maxValue === minValue) {
          cell.intensity = 0.5;
        } else {
          cell.intensity = Math.abs(cell.value - minValue) / (maxValue - minValue);
        }
      });
    }

    return cells;
  }, [data, selectedSymbols, maxSymbols, heatmapType, selectedMetric]);

  // Get unique symbols and periods
  const symbols = [...new Set(heatmapData.map(cell => cell.symbol))];
  const periods = [...new Set(heatmapData.map(cell => cell.period))];

  // Get cell color
  const getCellColor = (cell: HeatmapCell) => {
    const config = metricConfigs[selectedMetric];
    const { positiveColor, negativeColor, neutralColor } = config;

    if (cell.value === 0) return neutralColor;

    const baseColor = cell.value > 0 ? positiveColor : negativeColor;
    const opacity = Math.max(0.1, cell.intensity);
    
    // Convert hex to rgba
    const hex = baseColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  };

  // Get cell data
  const getCellData = (symbol: string, period: string | number) => {
    return heatmapData.find(cell => cell.symbol === symbol && cell.period === period);
  };

  // Handle cell click
  const handleCellClick = (symbol: string) => {
    onSymbolClick?.(symbol);
  };

  return (
    <VStack spacing={4} align="stretch">
      {/* Controls */}
      <Flex wrap="wrap" gap={4} align="center">
        {/* Heatmap Type */}
        <ButtonGroup size="sm" isAttached>
          <Button
            variant={heatmapType === 'hourly' ? 'solid' : 'outline'}
            onClick={() => setHeatmapType('hourly')}
          >
            Saatlik
          </Button>
          <Button
            variant={heatmapType === 'daily' ? 'solid' : 'outline'}
            onClick={() => setHeatmapType('daily')}
          >
            Günlük
          </Button>
          <Button
            variant={heatmapType === 'monthly' ? 'solid' : 'outline'}
            onClick={() => setHeatmapType('monthly')}
          >
            Aylık
          </Button>
        </ButtonGroup>

        {/* Metric Selection */}
        <HStack>
          <Text fontSize="sm" fontWeight="medium" color={textColor}>
            Metrik:
          </Text>
          <Select
            value={selectedMetric}
            onChange={(e) => setSelectedMetric(e.target.value as MetricType)}
            size="sm"
            w="180px"
          >
            {Object.entries(metricConfigs).map(([key, config]) => (
              <option key={key} value={key}>
                {config.label}
              </option>
            ))}
          </Select>
        </HStack>

        <Spacer />

        {/* Options */}
        <HStack>
          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="show-values" mb="0" fontSize="sm">
              Değerleri Göster
            </FormLabel>
            <Switch
              id="show-values"
              isChecked={showValues}
              onChange={(e) => setShowValues(e.target.checked)}
              size="sm"
            />
          </FormControl>

          <FormControl display="flex" alignItems="center">
            <FormLabel htmlFor="show-tooltips" mb="0" fontSize="sm">
              Tooltip'ler
            </FormLabel>
            <Switch
              id="show-tooltips"
              isChecked={showTooltips}
              onChange={(e) => setShowTooltips(e.target.checked)}
              size="sm"
            />
          </FormControl>
        </HStack>
      </Flex>

      {/* Current Settings Info */}
      <HStack>
        <Badge colorScheme="blue" variant="subtle">
          {metricConfigs[selectedMetric].label}
        </Badge>
        <Badge colorScheme="green" variant="subtle">
          {heatmapType === 'hourly' ? 'Saatlik' : heatmapType === 'daily' ? 'Günlük' : 'Aylık'}
        </Badge>
        <Badge colorScheme="purple" variant="subtle">
          {symbols.length} sembol
        </Badge>
      </HStack>

      {/* Heatmap */}
      <Box 
        overflowX="auto" 
        overflowY="auto" 
        maxH={`${height}px`}
        border="1px solid"
        borderColor={borderColor}
        borderRadius="md"
        bg={bgColor}
      >
        <Grid
          templateColumns={`120px repeat(${periods.length}, 1fr)`}
          gap={1}
          p={2}
          minW={`${120 + periods.length * 60}px`}
        >
          {/* Header row */}
          <GridItem />
          {periods.map((period, index) => (
            <GridItem key={index}>
              <Box
                p={2}
                textAlign="center"
                fontSize="xs"
                fontWeight="bold"
                color={textColor}
                transform="rotate(-45deg)"
                transformOrigin="center"
                h="60px"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                {heatmapType === 'hourly' ? `${period}:00` : period}
              </Box>
            </GridItem>
          ))}

          {/* Data rows */}
          {symbols.map(symbol => (
            <React.Fragment key={symbol}>
              {/* Symbol label */}
              <GridItem>
                <Box
                  p={2}
                  fontSize="sm"
                  fontWeight="bold"
                  color={textColor}
                  cursor="pointer"
                  onClick={() => handleCellClick(symbol)}
                  _hover={{ bg: 'blue.50' }}
                  borderRadius="md"
                  textAlign="right"
                  border={selectedSymbols.includes(symbol) ? '2px solid' : '1px solid transparent'}
                  borderColor={selectedSymbols.includes(symbol) ? 'blue.500' : 'transparent'}
                >
                  {symbol}
                </Box>
              </GridItem>

              {/* Data cells */}
              {periods.map((period, periodIndex) => {
                const cellData = getCellData(symbol, period);
                
                return (
                  <GridItem key={`${symbol}-${periodIndex}`}>
                    <Tooltip
                      label={
                        cellData ? (
                          <VStack align="start" spacing={1}>
                            <Text fontWeight="bold">{symbol} - {cellData.label}</Text>
                            <Text>{metricConfigs[selectedMetric].label}: {metricConfigs[selectedMetric].formatValue(cellData.value)}</Text>
                            <Text>İşlem Sayısı: {cellData.trades}</Text>
                            <Text>Kazanma Oranı: {cellData.winRate.toFixed(1)}%</Text>
                          </VStack>
                        ) : 'Veri yok'
                      }
                      isDisabled={!showTooltips}
                    >
                      <MotionBox
                        w="50px"
                        h="40px"
                        bg={cellData ? getCellColor(cellData) : 'gray.100'}
                        borderRadius="md"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                        cursor="pointer"
                        onClick={() => handleCellClick(symbol)}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        border={selectedSymbols.includes(symbol) ? '2px solid' : '1px solid transparent'}
                        borderColor={selectedSymbols.includes(symbol) ? 'blue.500' : 'transparent'}
                      >
                        {showValues && cellData && (
                          <Text fontSize="xs" fontWeight="bold" color={cellData.value > 0 ? 'white' : 'black'}>
                            {selectedMetric === 'pnl' ? (cellData.value > 0 ? '+' : '') + cellData.value.toFixed(0) :
                             selectedMetric === 'winRate' ? cellData.value.toFixed(0) + '%' :
                             selectedMetric === 'trades' ? cellData.value.toString() :
                             cellData.value.toFixed(0)}
                          </Text>
                        )}
                      </MotionBox>
                    </Tooltip>
                  </GridItem>
                );
              })}
            </React.Fragment>
          ))}
        </Grid>
      </Box>

      {/* Legend */}
      <HStack justify="space-between" fontSize="sm" color="gray.500">
        <Text>
          Toplam Hücre: {symbols.length * periods.length}
        </Text>
        <HStack>
          <Box w={4} h={4} bg={metricConfigs[selectedMetric].positiveColor} borderRadius="sm" />
          <Text>Yüksek</Text>
          <Box w={4} h={4} bg={metricConfigs[selectedMetric].neutralColor} borderRadius="sm" />
          <Text>Orta</Text>
          <Box w={4} h={4} bg={metricConfigs[selectedMetric].negativeColor} borderRadius="sm" />
          <Text>Düşük</Text>
        </HStack>
      </HStack>
    </VStack>
  );
};

export default SymbolPerformanceHeatmap;
