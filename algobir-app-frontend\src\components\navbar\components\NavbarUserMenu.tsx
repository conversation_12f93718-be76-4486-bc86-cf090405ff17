import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Avatar,
  Icon,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  MenuDivider,
  Text,
  Button,
  HStack,
  Portal,
  useColorModeValue,
} from '@chakra-ui/react';
import {
  FiUser,
  FiLogOut,
  FiChevronDown,
  FiSettings,
} from 'react-icons/fi';
import { useAuth } from '../../../context/AuthContext';
import { supabase } from '../../../supabaseClient';

/**
 * NavbarUserMenu - Handles user menu and authentication actions
 * Separated for better maintainability and single responsibility
 */
const NavbarUserMenu: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  // Color values
  const mainText = useColorModeValue('text-primary', 'white');
  const secondaryText = useColorModeValue('text-secondary', 'gray.400');
  const iconColor = useColorModeValue('gray.600', 'gray.300');
  const buttonHover = useColorModeValue('gray.100', 'gray.700');
  const buttonActive = useColorModeValue('gray.200', 'gray.600');
  const menuBg = useColorModeValue('white', 'gray.800');
  const navbarBorder = useColorModeValue('gray.200', 'gray.700');
  const shadow = useColorModeValue(
    '0px 8px 32px rgba(0, 0, 0, 0.12)',
    '0px 8px 32px rgba(0, 0, 0, 0.4)'
  );

  // Event handlers
  const handleProfileClick = () => {
    navigate('/profile');
  };

  const handleSettingsClick = () => {
    navigate('/management');
  };

  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Logout error:', error);
      } else {
        navigate('/login');
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <Menu placement="bottom-end">
      <MenuButton
        as={Button}
        variant="ghost"
        p={{ base: '1', md: '2' }}
        borderRadius="full"
        _hover={{ bg: buttonHover }}
        _active={{ bg: buttonActive }}
        _expanded={{ bg: buttonActive }}
        _focus={{
          boxShadow: '0 0 0 2px rgba(14, 165, 233, 0.6)',
          outline: 'none',
        }}
        aria-label="Kullanıcı menüsü"
      >
        <HStack spacing={{ base: '1', md: '2' }}>
          <Avatar
            size={{ base: 'xs', md: 'sm' }}
            src={user?.user_metadata?.avatar_url}
            name={user?.user_metadata?.full_name || user?.email}
            bg="brand.500"
            color="white"
          />
          <Box 
            display={{ base: 'none', sm: 'block' }}
            textAlign="left"
            maxW="120px"
          >
            <Text 
              fontSize="sm" 
              fontWeight="medium" 
              color={mainText}
              noOfLines={1}
            >
              {user?.user_metadata?.full_name || 'Kullanıcı'}
            </Text>
            <Text 
              fontSize="xs" 
              color={secondaryText}
              noOfLines={1}
            >
              {user?.email}
            </Text>
          </Box>
          <Icon 
            as={FiChevronDown} 
            fontSize="sm" 
            color={iconColor}
            display={{ base: 'none', md: 'block' }}
          />
        </HStack>
      </MenuButton>
      
      <Portal>
        <MenuList
          bg={menuBg}
          borderColor={navbarBorder}
          boxShadow={shadow}
          borderRadius="xl"
          py="2"
          minW="200px"
          zIndex={9999}
          position="relative"
        >
          <MenuItem
            icon={<Icon as={FiUser} />}
            onClick={handleProfileClick}
            _hover={{ bg: buttonHover }}
            _focus={{
              bg: buttonHover,
              outline: 'none',
            }}
            color={mainText}
            fontWeight="medium"
          >
            Profil
          </MenuItem>
          <MenuItem
            icon={<Icon as={FiSettings} />}
            onClick={handleSettingsClick}
            _hover={{ bg: buttonHover }}
            _focus={{
              bg: buttonHover,
              outline: 'none',
            }}
            color={mainText}
            fontWeight="medium"
          >
            Ayarlar
          </MenuItem>
          <MenuDivider borderColor={navbarBorder} />
          <MenuItem
            icon={<Icon as={FiLogOut} />}
            onClick={handleLogout}
            _hover={{ bg: 'error.50' }}
            _focus={{
              bg: 'error.50',
              outline: 'none',
            }}
            color="error.600"
            fontWeight="medium"
          >
            Çıkış Yap
          </MenuItem>
        </MenuList>
      </Portal>
    </Menu>
  );
};

export default NavbarUserMenu;
