import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../supabaseClient';
import { useAuth } from '../context/AuthContext';
import { 
  Notification, 
  NotificationPreferences, 
  CreateNotificationParams,
  CreateAnnouncementParams
} from '../types/notification';

export const useNotifications = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State değişiklik izleyicisi
  useEffect(() => {
    console.log('🔄 NOTIFICATIONS STATE DEĞİŞTİ:', {
      count: notifications.length,
      loading,
      error,
      firstNotificationTitle: notifications[0]?.title || 'YOK',
      allIds: notifications.map(n => n.id)
    });
  }, [notifications, loading, error]);

  // Bildirim sayısını al
  const fetchUnreadCount = useCallback(async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase.rpc('get_unread_notification_count');
      
      if (error) throw error;
      setUnreadCount(data || 0);
    } catch (err) {
      console.error('Error fetching unread count:', err);
      setError(err instanceof Error ? err.message : 'Bildirim sayısı alınamadı');
    }
  }, [user]);

  // Bildirimleri al
  const fetchNotifications = useCallback(async (
    limit: number = 20, 
    offset: number = 0, 
    unreadOnly: boolean = false
  ) => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      console.log('🔍 Kullanıcı bildirimleri çekiliyor...', { 
        userId: user.id, 
        limit, 
        offset, 
        unreadOnly 
      });

      const { data, error } = await supabase.rpc('get_user_notifications', {
        p_user_id: user.id,
        p_limit: limit,
        p_offset: offset,
        p_unread_only: unreadOnly
      });

      console.log('📊 RPC sonucu RAW:', { 
        data: data,
        dataType: typeof data,
        dataArray: Array.isArray(data),
        dataCount: data?.length || 0, 
        error: error?.message || 'Yok',
        hasData: !!data && data.length > 0
      });

      if (error) {
        console.error('❌ RPC hatası:', error);
        throw error;
      }
      
      // Veri formatını kontrol et ve güçlü parse işlemi
      let processedData = data;
      
      console.log('🧪 RPC veri formatı analizi:', {
        dataType: typeof data,
        isArray: Array.isArray(data),
        isString: typeof data === 'string',
        isNull: data === null,
        rawData: data
      });

      // Eğer veri string formatında gelirse JSON parse et
      if (typeof data === 'string') {
        console.log('📄 String formatında veri geldi, JSON parse edilecek...', data);
        try {
          processedData = JSON.parse(data);
          console.log('✅ JSON parse başarılı:', processedData);
        } catch (parseError) {
          console.error('❌ JSON parse hatası:', parseError, 'Raw data:', data);
          // Eğer string düzgün JSON değilse, direkt bir array'e çevir
          processedData = [];
        }
      }

      // Eğer null gelirse boş array yap
      if (!processedData) {
        console.log('⚠️ Null/undefined veri geldi, boş array ayarlanıyor');
        processedData = [];
      }

      // Array değilse array'e çevir
      if (!Array.isArray(processedData)) {
        console.log('⚠️ Array olmayan veri formatı, array\'e çevriliyor:', processedData);
        processedData = processedData ? [processedData] : [];
      }

      console.log('📋 İşlenmiş veri:', { 
        count: processedData.length, 
        items: processedData.map((item: any) => ({ id: item?.id, title: item?.title, type: item?.type }))
      });

      const formattedNotifications: Notification[] = processedData.map((item: any, index: number) => {
        console.log(`🔍 Bildirim #${index} formatlanıyor:`, item);
        
        // Güvenli alan erişimi
        const formatted = {
          id: item?.id || `temp_id_${Date.now()}_${index}`,
          title: item?.title || 'Başlık yok',
          message: item?.message || 'Mesaj yok',
          type: item?.type || 'unknown',
          severity: item?.severity || 'info',
          is_read: Boolean(item?.is_read),
          metadata: item?.metadata || {},
          action_url: item?.action_url || null,
          action_label: item?.action_label || null,
          created_at: item?.created_at || new Date().toISOString(),
          expires_at: item?.expires_at || null
        };

        console.log(`✅ Bildirim #${index} formatlandı:`, formatted);
        return formatted;
      });

      console.log(`✅ ${formattedNotifications.length} adet bildirim formatlandı`);

      // Trade bildirimlerini özellikle kontrol et
      const tradeNotifications = formattedNotifications.filter(n => 
        n.type === 'trade_opened' || n.type === 'trade_closed'
      );
      console.log(`📈 ${tradeNotifications.length} adet trade bildirimi bulundu:`, tradeNotifications);

      if (offset === 0) {
        setNotifications(formattedNotifications);
        console.log('📝 Bildirimler state\'e set edildi (yeni liste)');
        console.log('🔍 State güncelleme sonrası kontrol:', {
          arrayLength: formattedNotifications.length,
          firstNotification: formattedNotifications[0]?.title || 'YOK',
          allNotificationIds: formattedNotifications.map(n => n.id)
        });
      } else {
        setNotifications(prev => [...prev, ...formattedNotifications]);
        console.log('📝 Bildirimler state\'e eklendi (mevcut listeye)');
      }

      return formattedNotifications;
    } catch (err) {
      console.error('❌ fetchNotifications hatası:', err);
      setError(err instanceof Error ? err.message : 'Bildirimler alınamadı');
      return [];
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Bildirimi okundu olarak işaretle
  const markAsRead = useCallback(async (notificationId: string) => {
    if (!user) return false;

    try {
      const { data, error } = await supabase.rpc('mark_notification_as_read', {
        p_notification_id: notificationId
      });

      if (error) throw error;

      // Yerel state'i güncelle
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === notificationId 
            ? { ...notif, is_read: true }
            : notif
        )
      );

      // Okunmamış sayısını güncelle
      await fetchUnreadCount();

      return data || false;
    } catch (err) {
      console.error('Error marking notification as read:', err);
      setError(err instanceof Error ? err.message : 'Bildirim güncellenemedi');
      return false;
    }
  }, [user, fetchUnreadCount]);

  // Tüm bildirimleri okundu olarak işaretle
  const markAllAsRead = useCallback(async () => {
    if (!user) return 0;

    try {
      const { data, error } = await supabase.rpc('mark_all_notifications_as_read');

      if (error) throw error;

      // Yerel state'i güncelle
      setNotifications(prev => 
        prev.map(notif => ({ ...notif, is_read: true }))
      );
      setUnreadCount(0);

      return data || 0;
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      setError(err instanceof Error ? err.message : 'Bildirimler güncellenemedi');
      return 0;
    }
  }, [user]);

  // Yeni bildirim oluştur (admin için)
  const createNotification = useCallback(async (params: CreateNotificationParams) => {
    if (!user) return null;

    try {
      const { data, error } = await supabase.rpc('create_notification', {
        p_user_id: params.user_id,
        p_title: params.title,
        p_message: params.message,
        p_type: params.type || 'system_alert',
        p_severity: params.severity || 'info',
        p_metadata: params.metadata || null,
        p_action_url: params.action_url || null,
        p_action_label: params.action_label || null,
        p_expires_at: params.expires_at || null
      });

      if (error) throw error;
      return data;
    } catch (err) {
      console.error('Error creating notification:', err);
      setError(err instanceof Error ? err.message : 'Bildirim oluşturulamadı');
      return null;
    }
  }, [user]);

  // Admin duyuru oluştur
  const createAnnouncement = useCallback(async (params: CreateAnnouncementParams) => {
    if (!user) return null;

    try {
      const { data, error } = await supabase.rpc('create_admin_announcement', {
        p_title: params.title,
        p_message: params.message,
        p_target_audience: params.target_audience || 'all',
        p_severity: params.severity || 'info',
        p_expires_at: params.expires_at || null,
        p_action_url: params.action_url || null,
        p_action_label: params.action_label || null,
        p_metadata: params.metadata || null
      });

      if (error) throw error;
      return data;
    } catch (err) {
      console.error('Error creating announcement:', err);
      setError(err instanceof Error ? err.message : 'Duyuru oluşturulamadı');
      return null;
    }
  }, [user]);

  // Bildirimi sil (soft delete)
  const deleteNotification = useCallback(async (notificationId: string) => {
    if (!user) return false;

    try {
      const { data, error } = await supabase.rpc('delete_notification', {
        p_notification_id: notificationId
      });

      if (error) throw error;

      // Yerel state'den bildirimi kaldır
      setNotifications(prev => 
        prev.filter(notif => notif.id !== notificationId)
      );

      // Okunmamış sayısını güncelle
      await fetchUnreadCount();

      return data || false;
    } catch (err) {
      console.error('Error deleting notification:', err);
      setError(err instanceof Error ? err.message : 'Bildirim silinemedi');
      return false;
    }
  }, [user, fetchUnreadCount]);

  // Real-time dinleyici kurulumu
  useEffect(() => {
    if (!user) return;

    // İlk yüklemede verileri al
    fetchUnreadCount();
    fetchNotifications();

    // Real-time subscription
    const subscription = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${user.id}`,
        },
        (payload) => {
          console.log('Notification change received:', payload);
          
          // Bildirimleri ve sayıyı yenile
          fetchUnreadCount();
          fetchNotifications();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [user, fetchUnreadCount, fetchNotifications]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    createNotification,
    createAnnouncement,
    refetch: () => {
      fetchUnreadCount();
      fetchNotifications();
    }
  };
};

// Bildirim tercihleri için ayrı hook
export const useNotificationPreferences = () => {
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Tercihleri al/oluştur
  const fetchPreferences = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.rpc('get_or_create_notification_preferences');

      if (error) throw error;

      if (data && data.length > 0) {
        setPreferences(data[0]);
      }
    } catch (err) {
      console.error('Error fetching notification preferences:', err);
      setError(err instanceof Error ? err.message : 'Tercihler alınamadı');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Tercihleri güncelle
  const updatePreferences = useCallback(async (updates: Partial<NotificationPreferences>) => {
    if (!user || !preferences) return false;

    try {
      const { error } = await supabase
        .from('notification_preferences')
        .update(updates)
        .eq('user_id', user.id);

      if (error) throw error;

      setPreferences(prev => prev ? { ...prev, ...updates } : null);
      return true;
    } catch (err) {
      console.error('Error updating notification preferences:', err);
      setError(err instanceof Error ? err.message : 'Tercihler güncellenemedi');
      return false;
    }
  }, [user, preferences]);

  useEffect(() => {
    if (user) {
      fetchPreferences();
    }
  }, [user, fetchPreferences]);

  return {
    preferences,
    loading,
    error,
    updatePreferences,
    refetch: fetchPreferences
  };
}; 