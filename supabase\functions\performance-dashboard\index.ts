import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { getCorsHeaders } from '../_shared/cors.ts';
import { PerformanceMonitor } from '../_shared/performance-monitor.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
const supabase = createClient(supabaseUrl, supabaseServiceKey);

Deno.serve(async (req) => {
  const corsHeaders = getCorsHeaders(req.headers);

  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const url = new URL(req.url);
    const endpoint = url.pathname.split('/').pop();
    const monitor = PerformanceMonitor.getInstance();

    switch (endpoint) {
      case 'dashboard':
        return handleDashboard(monitor, corsHeaders);
      
      case 'realtime':
        return handleRealTime(monitor, corsHeaders);
      
      case 'alerts':
        return handleAlerts(monitor, corsHeaders);
      
      case 'metrics':
        return handleMetrics(supabase, corsHeaders, url.searchParams);
      
      case 'health':
        return handleHealth(monitor, corsHeaders);
      
      default:
        return new Response(JSON.stringify({ error: 'Invalid endpoint' }), {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
    }
  } catch (error) {
    console.error('[PERF-DASHBOARD] Error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

/**
 * Handle dashboard data request
 */
async function handleDashboard(monitor: PerformanceMonitor, corsHeaders: Record<string, string>) {
  const dashboardData = monitor.getDashboardData();
  
  return new Response(JSON.stringify({
    success: true,
    data: dashboardData,
    timestamp: new Date().toISOString()
  }), {
    status: 200,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

/**
 * Handle real-time stats request
 */
async function handleRealTime(monitor: PerformanceMonitor, corsHeaders: Record<string, string>) {
  const realTimeStats = monitor.getRealTimeStats();
  
  return new Response(JSON.stringify({
    success: true,
    data: realTimeStats,
    timestamp: new Date().toISOString()
  }), {
    status: 200,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

/**
 * Handle alerts request
 */
async function handleAlerts(monitor: PerformanceMonitor, corsHeaders: Record<string, string>) {
  const dashboardData = monitor.getDashboardData();
  
  return new Response(JSON.stringify({
    success: true,
    data: {
      activeAlerts: dashboardData.activeAlerts,
      alertsSummary: {
        total: dashboardData.activeAlerts.length,
        critical: dashboardData.activeAlerts.filter((a: any) => a.severity === 'critical').length,
        high: dashboardData.activeAlerts.filter((a: any) => a.severity === 'high').length,
        medium: dashboardData.activeAlerts.filter((a: any) => a.severity === 'medium').length
      }
    },
    timestamp: new Date().toISOString()
  }), {
    status: 200,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

/**
 * Handle historical metrics request
 */
async function handleMetrics(supabase: any, corsHeaders: Record<string, string>, searchParams: URLSearchParams) {
  const timeRangeHours = parseInt(searchParams.get('hours') || '24');
  const signalSource = searchParams.get('source') as 'solo-robot' | 'bro-robot' | null;
  const limit = parseInt(searchParams.get('limit') || '100');

  try {
    // Get historical metrics from database
    const { data: metrics, error } = await supabase.rpc('get_order_transmission_metrics', {
      p_time_range_hours: timeRangeHours,
      p_signal_source: signalSource,
      p_limit: limit
    });

    if (error) {
      throw error;
    }

    // Get aggregated stats
    const { data: stats, error: statsError } = await supabase.rpc('get_order_transmission_stats', {
      p_time_range_hours: timeRangeHours
    });

    if (statsError) {
      throw statsError;
    }

    // Calculate performance trends
    const trends = calculatePerformanceTrends(metrics || []);

    return new Response(JSON.stringify({
      success: true,
      data: {
        metrics: metrics || [],
        stats: stats?.[0] || null,
        trends,
        timeRange: `${timeRangeHours} hours`,
        totalRecords: metrics?.length || 0
      },
      timestamp: new Date().toISOString()
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('[PERF-DASHBOARD] Metrics error:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Failed to fetch metrics',
      details: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Handle system health request
 */
async function handleHealth(monitor: PerformanceMonitor, corsHeaders: Record<string, string>) {
  const dashboardData = monitor.getDashboardData();
  const systemHealth = dashboardData.systemHealth;
  
  // Add additional health checks
  const healthChecks = {
    database: await checkDatabaseHealth(),
    memory: checkMemoryHealth(),
    performance: systemHealth
  };

  const overallHealth = determineOverallHealth(healthChecks);

  return new Response(JSON.stringify({
    success: true,
    data: {
      status: overallHealth.status,
      score: overallHealth.score,
      checks: healthChecks,
      timestamp: new Date().toISOString(),
      uptime: getUptime()
    }
  }), {
    status: 200,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

/**
 * Calculate performance trends
 */
function calculatePerformanceTrends(metrics: any[]): any {
  if (metrics.length < 2) {
    return { trend: 'insufficient_data' };
  }

  const sorted = metrics.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
  const half = Math.floor(sorted.length / 2);
  const firstHalf = sorted.slice(0, half);
  const secondHalf = sorted.slice(half);

  const firstAvg = firstHalf.reduce((sum, m) => sum + m.total_processing_time_ms, 0) / firstHalf.length;
  const secondAvg = secondHalf.reduce((sum, m) => sum + m.total_processing_time_ms, 0) / secondHalf.length;

  const change = ((secondAvg - firstAvg) / firstAvg) * 100;

  return {
    trend: change > 10 ? 'degrading' : change < -10 ? 'improving' : 'stable',
    changePercent: change.toFixed(2),
    firstHalfAvg: firstAvg.toFixed(2),
    secondHalfAvg: secondAvg.toFixed(2)
  };
}

/**
 * Check database health
 */
async function checkDatabaseHealth(): Promise<any> {
  try {
    const start = performance.now();
    const { data, error } = await supabase.from('user_settings').select('id').limit(1);
    const duration = performance.now() - start;

    if (error) {
      return { status: 'unhealthy', error: error.message, responseTime: duration };
    }

    return {
      status: duration < 100 ? 'healthy' : duration < 500 ? 'degraded' : 'unhealthy',
      responseTime: duration.toFixed(2)
    };
  } catch (error) {
    return { status: 'unhealthy', error: error.message };
  }
}

/**
 * Check memory health
 */
function checkMemoryHealth(): any {
  try {
    if (typeof Deno !== 'undefined' && Deno.memoryUsage) {
      const usage = Deno.memoryUsage();
      const heapUsedMB = usage.heapUsed / (1024 * 1024);
      const heapTotalMB = usage.heapTotal / (1024 * 1024);
      const utilization = (heapUsedMB / heapTotalMB) * 100;

      return {
        status: heapUsedMB < 100 ? 'healthy' : heapUsedMB < 200 ? 'degraded' : 'unhealthy',
        heapUsedMB: heapUsedMB.toFixed(2),
        heapTotalMB: heapTotalMB.toFixed(2),
        utilization: utilization.toFixed(2)
      };
    }

    return { status: 'unknown', message: 'Memory usage not available' };
  } catch (error) {
    return { status: 'error', error: error.message };
  }
}

/**
 * Determine overall health status
 */
function determineOverallHealth(checks: any): any {
  const statuses = Object.values(checks).map((check: any) => check.status);
  
  if (statuses.includes('unhealthy')) {
    return { status: 'unhealthy', score: 25 };
  } else if (statuses.includes('degraded')) {
    return { status: 'degraded', score: 60 };
  } else if (statuses.every(status => status === 'healthy')) {
    return { status: 'healthy', score: 95 };
  } else {
    return { status: 'unknown', score: 50 };
  }
}

/**
 * Get system uptime
 */
function getUptime(): string {
  // This is a simplified uptime - in production you'd track actual service start time
  return 'Runtime uptime not available in Edge Functions';
}
