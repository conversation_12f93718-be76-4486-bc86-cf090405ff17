import { mode } from '@chakra-ui/theme-tools';

export const ProgressComponent = {
  components: {
    Progress: {
      baseStyle: (props: any) => ({
        track: {
          bg: mode('secondaryGray.100', 'navy.700')(props),
        },
        filledTrack: {
          bg: mode('brand.500', 'brand.400')(props),
        },
      }),
      variants: {
        main: (props: any) => ({
          track: {
            bg: mode('secondaryGray.100', 'navy.700')(props),
          },
          filledTrack: {
            bg: mode('brand.500', 'brand.400')(props),
          },
        }),
        table: (props: any) => ({
          track: {
            bg: mode('secondaryGray.100', 'navy.700')(props),
          },
          filledTrack: {
            bg: mode('brand.500', 'brand.400')(props),
          },
        }),
      },
    },
  },
}; 